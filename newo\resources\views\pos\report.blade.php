@extends('layouts.admin')
@section('page-title')
    {{__('POS Summary')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('POS Summary')}}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
@endpush

@section('content')
    <div id="printableArea">
        <!-- معلومات الوردية -->
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-1">{{ __('معلومات الوردية الحالية') }}</h6>
                            @if(isset($openShift) && $openShift)
                                <p class="mb-0">
                                    <strong>{{ __('الوردية') }}:</strong> #{{ $openShift->id }} |
                                    <strong>{{ __('بدأت في') }}:</strong> {{ $openShift->opened_at ? $openShift->opened_at->format('Y-m-d H:i') : __('غير محدد') }} |
                                    <strong>{{ __('المستودع') }}:</strong> {{ $openShift->warehouse ? $openShift->warehouse->name : __('غير محدد') }}
                                </p>
                            @else
                                <p class="mb-0 text-warning">
                                    <i class="fas fa-exclamation-triangle"></i> {{ __('لا توجد وردية مفتوحة حالياً - لن تظهر أي فواتير') }}
                                </p>
                            @endif
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-primary">{{ __('عدد الفواتير') }}: {{ count($posPayments) }}</span>
                            @php
                                $deliveryCount = collect($posPayments)->where('delivery_status', 'delivery_pending')->count();
                            @endphp
                            @if($deliveryCount > 0)
                                <span class="badge bg-warning">{{ __('طلبات التوصيل') }}: {{ $deliveryCount }}</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if(!isset($openShift) || !$openShift)
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="alert alert-warning text-center">
                        <h5><i class="fas fa-exclamation-triangle"></i> {{ __('لا توجد وردية مفتوحة') }}</h5>
                        <p>{{ __('يجب فتح وردية أولاً لعرض الفواتير. اذهب إلى إدارة الورديات لفتح وردية جديدة.') }}</p>
                        <a href="{{ route('shift.index') }}" class="btn btn-primary">
                            <i class="fas fa-clock"></i> {{ __('إدارة الورديات') }}
                        </a>
                    </div>
                </div>
            </div>
        @else
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body table-border-style">
                            <div class="table-responsive">
                                <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('POS ID')}}</th>
                                    <th>{{ __('Date') }}</th>
                                    <th>{{ __('Customer') }}</th>
                                    <th>{{ __('Warehouse') }}</th>
                                    <th>{{ __('Sub Total') }}</th>
                                    <th>{{ __('Discount') }}</th>
                                    <th>{{ __('Total') }}</th>
                                    <th>{{ __('Payment Method') }}</th>
                                    <th>{{ __('منشئ الفاتورة') }} ({{ __('Invoice Creator') }})</th>
                                    <th>{{ __('الإجراءات') }} ({{ __('Actions') }})</th>
                                </tr>
                                </thead>

                                <tbody>
                                    {{-- @dd($posPayments); --}}
                                @forelse ($posPayments as $posPayment)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('pos.show',\Crypt::encrypt($posPayment->id)) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->posNumberFormat($posPayment->id) }}
                                            </a>
                                        </td>

                                        <td>{{ Auth::user()->dateFormat($posPayment->created_at)}}</td>
                                        @if($posPayment->customer_id == 0)
                                            <td class="">{{__('Walk-in Customer')}}</td>
                                        @else
                                            <td>{{ !empty($posPayment->customer) ? $posPayment->customer->name : '' }} </td>
                                        @endif
                                        <td>{{ !empty($posPayment->warehouse) ? $posPayment->warehouse->name : '' }} </td>
                                        <td>{{!empty($posPayment->posPayment)? \Auth::user()->priceFormat ($posPayment->posPayment->amount) :0}}</td>
                                        <td>{{!empty($posPayment->posPayment)? \Auth::user()->priceFormat($posPayment->posPayment->discount) :0}}</td>
                                        <td>{{!empty($posPayment->posPayment)? \Auth::user()->priceFormat($posPayment->posPayment->discount_amount) :0}}</td>
                                        <td>
                                            @if($posPayment->status_type == 'returned')
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('مرتجع بضاعة') }} ↩️</span>
                                            @elseif($posPayment->status_type == 'cancelled')
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('ملغية') }} ❌</span>
                                            @elseif($posPayment->delivery_status == 'delivery_pending')
                                                <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري توصيل الطلب') }} 🚚</span>
                                            @elseif($posPayment->delivery_status == 'delivery_completed')
                                                <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم توصيل الطلب') }} ✅</span>
                                            @elseif(!empty($posPayment->customer) && strpos(strtolower($posPayment->customer->name), 'delivery') !== false)
                                                @if($posPayment->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري تحصيلها من مندوب التوصيل') }} 🚚</span>
                                                @endif
                                            @elseif(!empty($posPayment->posPayment) && !empty($posPayment->posPayment->payment_type))
                                                @if($posPayment->posPayment->payment_type == 'cash')
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Cash') }} 💵</span>
                                                @elseif($posPayment->posPayment->payment_type == 'network')
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Network') }} 💳</span>
                                                @elseif($posPayment->posPayment->payment_type == 'split')
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Split Payment') }} 💳 💵</span>
                                                @else
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Unpaid') }}</span>
                                                @endif
                                            @else
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('Unpaid') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            {{ !empty($posPayment->createdBy) ? $posPayment->createdBy->name : __('غير معروف') . ' (' . __('Unknown') . ')' }}
                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-info ms-2">
                                                <a href="{{ route('pos.show', \Crypt::encrypt($posPayment->id)) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('عرض التفاصيل') }}">
                                                    <i class="ti ti-eye text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-success ms-2">
                                                <a href="{{ route('pos.thermal.print', $posPayment->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" target="_blank" data-bs-toggle="tooltip" title="{{ __('طباعة الفاتورة الحرارية') }}">
                                                    <i class="ti ti-printer text-white"></i>
                                                </a>
                                            </div>
                                            @if($posPayment->delivery_status == 'delivery_pending')
                                                <div class="action-btn bg-warning ms-2">
                                                    <button class="mx-3 btn btn-sm d-inline-flex align-items-center pay-delivery-btn"
                                                            data-id="{{ $posPayment->id }}"
                                                            data-bs-toggle="tooltip"
                                                            title="{{ __('تحصيل الدفع') }}">
                                                        <i class="ti ti-cash text-white"></i>
                                                    </button>
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center text-dark"><p>{{__('No Data Found')}}</p></td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).on('click', '.pay-button', function(e) {
        e.preventDefault();
        var id = $(this).data('id');

        if (confirm('{{ __("هل أنت متأكد من تحصيل هذه الفاتورة؟") }}')) {
            $.ajax({
                url: '{{ route("pos.collect.payment") }}',
                type: 'POST',
                data: {
                    id: id,
                    _token: '{{ csrf_token() }}'
                },
                success: function(data) {
                    if (data.success) {
                        show_toastr('Success', data.success, 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        show_toastr('Error', data.error, 'error');
                    }
                },
                error: function(data) {
                    show_toastr('Error', '{{ __("حدث خطأ أثناء معالجة الطلب") }}', 'error');
                }
            });
        }
    });

    // معالج زر تحصيل دفع طلبات التوصيل
    $(document).on('click', '.pay-delivery-btn', function(e) {
        e.preventDefault();
        var id = $(this).data('id');
        var button = $(this);

        if (confirm('{{ __("هل أنت متأكد من تحصيل دفع هذا الطلب؟") }}')) {
            // تعطيل الزر وإضافة مؤشر تحميل
            button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span>');

            $.ajax({
                url: '{{ route("pos.process.delivery.payment") }}',
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                data: {
                    pos_id: id
                },
                success: function(data) {
                    if (data.success) {
                        show_toastr('Success', data.message || '{{ __("تم تحصيل الدفع بنجاح") }}', 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        show_toastr('Error', data.error || '{{ __("فشل في تحصيل الدفع") }}', 'error');
                        // إعادة تفعيل الزر
                        button.prop('disabled', false).html('<i class="ti ti-cash text-white"></i>');
                    }
                },
                error: function(xhr) {
                    console.log('خطأ AJAX:', xhr.responseText);
                    var errorMessage = '{{ __("حدث خطأ أثناء معالجة الطلب") }}';

                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }

                    show_toastr('Error', errorMessage, 'error');
                    // إعادة تفعيل الزر
                    button.prop('disabled', false).html('<i class="ti ti-cash text-white"></i>');
                }
            });
        }
    });
</script>
@endpush