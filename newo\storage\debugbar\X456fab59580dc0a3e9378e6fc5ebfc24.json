{"__meta": {"id": "X456fab59580dc0a3e9378e6fc5ebfc24", "datetime": "2025-06-08 15:09:13", "utime": **********.153814, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749395352.507395, "end": **********.153841, "duration": 0.6464459896087646, "duration_str": "646ms", "measures": [{"label": "Booting", "start": 1749395352.507395, "relative_start": 0, "end": 1749395352.991173, "relative_end": 1749395352.991173, "duration": 0.4837779998779297, "duration_str": "484ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749395352.991191, "relative_start": 0.4837958812713623, "end": **********.153846, "relative_end": 5.0067901611328125e-06, "duration": 0.16265511512756348, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00986, "accumulated_duration_str": "9.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.062295, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 42.292}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.084969, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 42.292, "width_percent": 11.156}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1148648, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 53.448, "width_percent": 9.635}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.11975, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 63.083, "width_percent": 9.939}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1302419, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 73.022, "width_percent": 18.154}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.137855, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 91.176, "width_percent": 8.824}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1358316645 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358316645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.128193, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1339268228 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1339268228\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-459345841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-459345841\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-760368884 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-760368884\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAxaWZlUEptNHhIWmZtdnkvZ2tOb0E9PSIsInZhbHVlIjoiUjY1aEJjNllNQUNESTR3VjdPSmpYZU5iWFJpNW9xYXk4SXFhbG43WXhIVGZ4U0YrTzB2REtqZEJUaTY2YkhwZFhRVzJ6N3RaTm9BcDh4Ui9PSzlTWkpTYklmdys2dGFRTW1uNmdsakNCWG1mL01CMU1wenpPcEpUeGRjRTVWQWNOY2szc3V3MTZqYTJ2K3BzdHpBOWRNR1MrR1pPa0p3N3d6UFFXL3RiNDZsZ0U3bUVJMm5nM0Z2WFZqSEk5TU1abUYweG1xOFlHNy9mcFIraFBaVE9NVEFxTFg5WjRmUmwwWjdPQ1pJeDQ5VHRxYm9pZUlKcC8waDhxd205d3FvdGhMMXFjUDVOb3B2OVNMSVgzOS8vZFFxTzZQNGFlckpBN2JtbFVsdkQwbm82bXNxQUhKck5yek5tQ3JxNVc0MjQwcFRMNmVVSDMrVjFJdFh1Z2E5VnlJTFpiMXh6MEJiQTEwaHFMNm5wdDJWN1FhMHczTENQY2NNTk83VDZkbDFkcUJSbEZlNm56RE85Q2RKTmlNSUx2R1dzcDRIU21mczRuajJsYnFlY25vd2dJK1JzZG13RTZrRmcxakdtZmZSdXdjdzgvSTlScTZvcDZtV1VRb1hKMmI0MDVkM3lSd2czTW5ha0xFNE1DK0VrbXFvRUZXSXpERlpFUzkwU2J1cG4iLCJtYWMiOiIxM2RkYjBkM2Y5NGQ2ZTdiYWI5OGRkMGM4NWUzYjgwMTgwMjU4NzNiNjk3YjUzNzA2MWVlNjRiZjQ0YTVjMTNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijc5RWpmdExzbGlSTHhpa0p6ckljSWc9PSIsInZhbHVlIjoiaXVIOTZ6VXp5dzJWeHVyWkt5T01MdVdqQ0ZxcVpIZzdNSzZKOWtZdExMbWUxRXhPaHB4a1dJOEZPQThNZHYrME5sOGNrdkllSGJwNy9FaHJ0Mkx4aTNhMjE3bTZhUEduT2pOaTlTTkJOOGZJWHBqRU1LMnVNK2JySktNS1RPcy94MmttZmpMQVlIQlRxTkFUL3I1SXRVQW42K09pWTlWZXUzMXRhSFBscEVwejRnczNFU2NHYkEyb3gydEtRWFRKRW5GS0MyQTBhTlRvd3N3R1M3WCtxS284SWtDNTViQ0NwY1NUdWxKUVM5NFZzNVNIY2IxaXRmSllPdElKZ2t2NW1IWFJnYmk4dXNwREt3d3pQS0F5ZGJJa2c1ekpmYktTV0NYRm51Z0ppN2liRWk1dmFyUThoUE1lNkh2U1ZKdldEeWUzRnhSRW1kUDNGUkZXSTJ2UnBwVGc0blRrYlREc0JwTzdQTnBEdFUzM1FrMklkUmJEd1ppT1drZ3c5RVZsVUhTOXhBMnoyQlRzUjFMT0JFTHYvSlpRbUhoa0ZiYUQ0Q1B1aXRueXU0elhXeTNFT1JmSjRlL3Yyb3ZtTHdqVVBDSnVmRHg4MzNnZSsrSWtOQmgycjJtN0U4OUVMNEdqZ2lvK0N0M1hQb256a0dDOFJVcHFVU1JPd1EvNCs2eWIiLCJtYWMiOiJiNmRiMzIxZjI0YTZhNjMxY2RjMWViMzJiNThmNGE4YTEwMjI1MjcyMDA0ZWZiMDAzNjkyY2M1ZTgxNDU5ZDg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-850352640 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850352640\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-102049437 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFmYVltYnZQL3k0ZFNtSHZoQkg3Znc9PSIsInZhbHVlIjoiVkdPdnhDMTVzZ1VNRFJYZUdORFM0eVFMUnBYUVBmdlBNVDd6YmZadjhldUtzSmg2STlsRXdJVlRVY3U5QjVuckUxQ2dUalc3L1JsMW9FMnBhL004RGgyQWN4ZmVlUG1TZ0h3SktvSytrVnZ6eEpkTDVUcWtyRDFmangreCtRT01mZncxUU1XQUtXUjdqUjJ6M0V3cmh1T1BjZHpsdVFRZHJjV2MyVnFuMTV5Y2pOL1RpbFUxekRZdTJXaDl2S3BNTVErbEZDLzdXbkN5emxPanFyVGFHVmk3aVROTlgzMkF1dHZUaVp6N2R1UEFsV3FIcXJ6Qzl2RnBsVVNZbFJlcjQ4TUlZNElZOUtmMGFOODZMRFpJckF1TnUxZS85UjhnUXk1cjI3N2tSMXh4WWtyMFN6cDhLdjJPemFPTkdodHZKNEJoVkJ0dlBOSWFxSjA1Y0IvQXgydENNcjZyaFZTYitBNFBIYXUzNkpZSEwrZlczc05Gd0owTkZyUVJCMjRLcnBwNzlKTXYvVUQ0WmVvQzFJc2VyUi9QdXE3WE5xbDVpQzEwR2xxR3o5czE2MkFCeC9SbE9HSlVxR0MzWktVbG9GdG1zR1lBS3czbmpTVUNQcU5tT2tEdjY1elR3ZEZhYnMwYmdMQXNLaWtaWUFrekhwTmE0ZGRwdlgrZ2N3bmoiLCJtYWMiOiJiZTBlYzQxNDRmMGI4MzRkMjU3YWFiNmRhY2ZmNTMzNGFhNzk3MzM0NGUyNDVmYmRjNTQyYjAxOTZhZjE5NDA2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFFc0NpK29oUlU2bUxzZWRBY1FkUWc9PSIsInZhbHVlIjoiV0REYjdWWXFKMENrWnVlc3d6WGhnem1OVGlrSTl2d1BNZm9jQ3lrMVQ0eE9JU1RXaVU0OTZtVnVhY0ZWRHZ6YlhHUVpHRmVGVUZyVlRna094NVdSWDZEakZCVjQ4TGtRNStNd3Rtc081K0h4Y0pqUWcrUXZIeCtDaHFTU1BxcjRtVzVJNSsxZnVHZ2pXVWtBdkZTR0NLWE5qYmJFMVc0R3RhWWs2VHo4aEMzTWNKdG9OajFldFVDWVYwVVVjbWFwL280OUtOdUUybHE3OWtKWFkyZHh5TmsvUlZlZW9PV09tK0xPRUR5RzhlNVVWTUhXR2hsTkNqWkxQdkRKTGtWNkxMSkR5YWNkVDdTek9ieHZjUnFEVDhaUkZQaVFpSzRESG0zVFpEb0VKaHAyQzhmbHRIOVBLTHZMRTltb1lWRzB4YlA4SzlqQTRQaWEvQ1E3ZUF4QW1EbkhBZkx5UkM5NVRNamJrZlo2WDBzZjN6R2s5aml5ZStQbFhoWnpldnpZSi9KWEx1UlRVZGRWbTl2QkYzN0oxWHo0L1V4MVl6TEpiZ3krQXRsdEFEYTJNdCtNVjU4THJTYVQ4aUhpWGd2eGk1UmR0ZHdNRzRYZzNsS3RtYVc2QXh5bms5L1dJYTF0ckNJaWFDYWw5bUx1TlFUa3RVMmh0RkN2UXhkaTAwS3AiLCJtYWMiOiIwZGU5ZjFmNTc1ZmI3YzQ4ZTJlZGFmYTE0OWNmMWNmNzQyMzBhOWY0OGM4ZDMyZjZlOGY4ZDJjNGM3OTlmYWQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFmYVltYnZQL3k0ZFNtSHZoQkg3Znc9PSIsInZhbHVlIjoiVkdPdnhDMTVzZ1VNRFJYZUdORFM0eVFMUnBYUVBmdlBNVDd6YmZadjhldUtzSmg2STlsRXdJVlRVY3U5QjVuckUxQ2dUalc3L1JsMW9FMnBhL004RGgyQWN4ZmVlUG1TZ0h3SktvSytrVnZ6eEpkTDVUcWtyRDFmangreCtRT01mZncxUU1XQUtXUjdqUjJ6M0V3cmh1T1BjZHpsdVFRZHJjV2MyVnFuMTV5Y2pOL1RpbFUxekRZdTJXaDl2S3BNTVErbEZDLzdXbkN5emxPanFyVGFHVmk3aVROTlgzMkF1dHZUaVp6N2R1UEFsV3FIcXJ6Qzl2RnBsVVNZbFJlcjQ4TUlZNElZOUtmMGFOODZMRFpJckF1TnUxZS85UjhnUXk1cjI3N2tSMXh4WWtyMFN6cDhLdjJPemFPTkdodHZKNEJoVkJ0dlBOSWFxSjA1Y0IvQXgydENNcjZyaFZTYitBNFBIYXUzNkpZSEwrZlczc05Gd0owTkZyUVJCMjRLcnBwNzlKTXYvVUQ0WmVvQzFJc2VyUi9QdXE3WE5xbDVpQzEwR2xxR3o5czE2MkFCeC9SbE9HSlVxR0MzWktVbG9GdG1zR1lBS3czbmpTVUNQcU5tT2tEdjY1elR3ZEZhYnMwYmdMQXNLaWtaWUFrekhwTmE0ZGRwdlgrZ2N3bmoiLCJtYWMiOiJiZTBlYzQxNDRmMGI4MzRkMjU3YWFiNmRhY2ZmNTMzNGFhNzk3MzM0NGUyNDVmYmRjNTQyYjAxOTZhZjE5NDA2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFFc0NpK29oUlU2bUxzZWRBY1FkUWc9PSIsInZhbHVlIjoiV0REYjdWWXFKMENrWnVlc3d6WGhnem1OVGlrSTl2d1BNZm9jQ3lrMVQ0eE9JU1RXaVU0OTZtVnVhY0ZWRHZ6YlhHUVpHRmVGVUZyVlRna094NVdSWDZEakZCVjQ4TGtRNStNd3Rtc081K0h4Y0pqUWcrUXZIeCtDaHFTU1BxcjRtVzVJNSsxZnVHZ2pXVWtBdkZTR0NLWE5qYmJFMVc0R3RhWWs2VHo4aEMzTWNKdG9OajFldFVDWVYwVVVjbWFwL280OUtOdUUybHE3OWtKWFkyZHh5TmsvUlZlZW9PV09tK0xPRUR5RzhlNVVWTUhXR2hsTkNqWkxQdkRKTGtWNkxMSkR5YWNkVDdTek9ieHZjUnFEVDhaUkZQaVFpSzRESG0zVFpEb0VKaHAyQzhmbHRIOVBLTHZMRTltb1lWRzB4YlA4SzlqQTRQaWEvQ1E3ZUF4QW1EbkhBZkx5UkM5NVRNamJrZlo2WDBzZjN6R2s5aml5ZStQbFhoWnpldnpZSi9KWEx1UlRVZGRWbTl2QkYzN0oxWHo0L1V4MVl6TEpiZ3krQXRsdEFEYTJNdCtNVjU4THJTYVQ4aUhpWGd2eGk1UmR0ZHdNRzRYZzNsS3RtYVc2QXh5bms5L1dJYTF0ckNJaWFDYWw5bUx1TlFUa3RVMmh0RkN2UXhkaTAwS3AiLCJtYWMiOiIwZGU5ZjFmNTc1ZmI3YzQ4ZTJlZGFmYTE0OWNmMWNmNzQyMzBhOWY0OGM4ZDMyZjZlOGY4ZDJjNGM3OTlmYWQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102049437\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1900989589 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900989589\", {\"maxDepth\":0})</script>\n"}}