# ملخص عملية إنشاء فاتورة التوصيل وربطها بالجداول

## 🎯 **العملية المطلوبة:**

عند اختيار عميل لديه صلاحية التوصيل والنقر على زر "توصيل طلب":
1. **إنشاء فاتورة** برقم ضمن تسلسل الفواتير
2. **ربط العملية بالجداول الثلاث:** `pos`, `pos_payments`, `pos_products`
3. **تسجيل المبلغ** في عمود `delivery_cash` في جدول `financial_records`
4. **عرض الفاتورة** في POS Summary بحالة "جاري توصيل الطلب"

## ✅ **الجداول المرتبطة:**

### 📊 **1. جدول `pos` (الفاتورة الرئيسية):**
```sql
- id: معرف الفاتورة
- pos_id: رقم الفاتورة (تسلسلي)
- customer_id: معرف العميل
- warehouse_id: معرف المستودع
- delivery_status: 'delivery_pending' (جاري توصيل الطلب)
- cashier_id: معرف الكاشير المسؤول
- shift_id: معرف الوردية
- is_payment_set: 0 (لم يتم الدفع بعد)
- created_by: منشئ الفاتورة
- pos_date: تاريخ الفاتورة
```

### 💰 **2. جدول `pos_payments` (مدفوعات الفاتورة):**
```sql
- id: معرف سجل الدفع
- pos_id: معرف الفاتورة (مرتبط بجدول pos)
- amount: المبلغ قبل الخصم
- discount: قيمة الخصم
- discount_amount: المبلغ النهائي بعد الخصم
- payment_type: 'pending' (قيد الانتظار للتوصيل)
- cash_amount: NULL (لم يتم الدفع بعد)
- network_amount: NULL (لم يتم الدفع بعد)
- date: تاريخ إنشاء السجل
- created_by: منشئ السجل
```

### 📦 **3. جدول `pos_products` (منتجات الفاتورة):**
```sql
- id: معرف سجل المنتج
- pos_id: معرف الفاتورة (مرتبط بجدول pos)
- product_id: معرف المنتج
- quantity: الكمية المباعة
- price: سعر الوحدة
- discount: خصم المنتج
- tax: معرف الضريبة
- total: المجموع الفرعي (شامل الضريبة)
```

### 💳 **4. جدول `financial_records` (السجلات المالية):**
```sql
- id: معرف السجل المالي
- shift_id: معرف الوردية
- delivery_cash: المبلغ المسجل كعجز للتوصيل
- current_cash: النقد الحالي
- overnetwork_cash: مبالغ الشبكة
- total_cash: إجمالي النقد
- created_by: منشئ السجل
- updated_by: محدث السجل
```

## 🔄 **تدفق العملية في الكود:**

### 📝 **1. دالة `storeDeliveryOrder` في PosController:**

```php
public function storeDeliveryOrder(Request $request)
{
    // 1. التحقق من الصلاحيات والبيانات
    // 2. التحقق من وجود وردية مفتوحة
    // 3. التحقق من صلاحية التوصيل للعميل
    // 4. إنشاء رقم فاتورة فريد
    
    // 5. إنشاء الفاتورة في جدول pos
    $pos = new Pos();
    $pos->pos_id = $pos_id;
    $pos->customer_id = $customer_id->id;
    $pos->warehouse_id = $request->warehouse_name;
    $pos->delivery_status = 'delivery_pending';
    $pos->shift_id = $openShift->id;
    $pos->is_payment_set = 0;
    $pos->save();
    
    // 6. حفظ منتجات الفاتورة في pos_products
    foreach ($pos_data as $product_id => $item) {
        $posProduct = new PosProduct();
        $posProduct->pos_id = $pos->id;
        $posProduct->product_id = $product_id;
        $posProduct->quantity = $item['quantity'];
        $posProduct->price = $item['price'];
        $posProduct->total = $subtotal + $tax;
        $posProduct->save();
        
        // خصم الكمية من المخزون
        Utility::warehouse_quantity('minus', $quantity, $product_id, $warehouse);
    }
    
    // 7. إنشاء سجل دفع في pos_payments
    $posPayment = new PosPayment();
    $posPayment->pos_id = $pos->id;
    $posPayment->amount = $mainsubtotal + $totalTax;
    $posPayment->discount = $discount;
    $posPayment->discount_amount = $total;
    $posPayment->payment_type = 'pending';
    $posPayment->save();
    
    // 8. تسجيل العجز في financial_records
    $this->recordDeliveryDeficit($pos, $total);
}
```

### 💰 **2. دالة `recordDeliveryDeficit`:**

```php
private function recordDeliveryDeficit($pos, $amount)
{
    // البحث عن السجل المالي للوردية
    $financialRecord = FinancialRecord::where('shift_id', $pos->shift_id)->first();
    
    if ($financialRecord) {
        // إضافة المبلغ إلى delivery_cash كعجز
        $financialRecord->delivery_cash += $amount;
        $financialRecord->updated_by = Auth::id();
        $financialRecord->save();
    } else {
        // إنشاء سجل مالي جديد
        $financialRecord = new FinancialRecord();
        $financialRecord->shift_id = $pos->shift_id;
        $financialRecord->delivery_cash = $amount;
        $financialRecord->current_cash = 0;
        $financialRecord->total_cash = 0;
        $financialRecord->save();
    }
}
```

## 🔗 **العلاقات بين الجداول:**

### 🔄 **العلاقات المباشرة:**
```
pos (1) ←→ (1) pos_payments
pos (1) ←→ (*) pos_products  
pos (*) ←→ (1) financial_records (عبر shift_id)
```

### 📊 **مثال على البيانات المترابطة:**

#### **جدول pos:**
```
id: 1
pos_id: 2024001
customer_id: 5
warehouse_id: 2
delivery_status: 'delivery_pending'
shift_id: 10
```

#### **جدول pos_payments:**
```
id: 1
pos_id: 1 (مرتبط بالفاتورة أعلاه)
amount: 100.00
discount: 5.00
discount_amount: 95.00
payment_type: 'pending'
```

#### **جدول pos_products:**
```
id: 1, pos_id: 1, product_id: 15, quantity: 2, price: 25.00, total: 50.00
id: 2, pos_id: 1, product_id: 20, quantity: 1, price: 45.00, total: 45.00
```

#### **جدول financial_records:**
```
id: 1
shift_id: 10 (نفس الوردية)
delivery_cash: 95.00 (نفس المبلغ النهائي)
current_cash: 500.00
total_cash: 595.00
```

## 🎯 **النتيجة في POS Summary:**

```
رقم الفاتورة: 2024001
العميل: اسم العميل
المبلغ: 95.00 ريال
الحالة: جاري توصيل الطلب
التاريخ: 2024-01-15
الإجراءات: [تحصيل الدفع] [عرض الفاتورة]
```

## ✅ **التحقق من صحة العملية:**

### 🔍 **نقاط الفحص:**
1. ✅ **الفاتورة في pos:** `delivery_status = 'delivery_pending'`
2. ✅ **الدفع في pos_payments:** `payment_type = 'pending'`
3. ✅ **المنتجات في pos_products:** جميع المنتجات مسجلة
4. ✅ **العجز في financial_records:** `delivery_cash` محدث
5. ✅ **المخزون:** الكميات مخصومة
6. ✅ **POS Summary:** الفاتورة تظهر بالحالة الصحيحة

### 🧪 **اختبار العملية:**
```sql
-- فحص الفاتورة
SELECT * FROM pos WHERE delivery_status = 'delivery_pending';

-- فحص الدفع
SELECT * FROM pos_payments WHERE payment_type = 'pending';

-- فحص المنتجات
SELECT * FROM pos_products WHERE pos_id = [رقم_الفاتورة];

-- فحص السجل المالي
SELECT delivery_cash FROM financial_records WHERE shift_id = [رقم_الوردية];
```

## 🎉 **الخلاصة:**

العملية تعمل بالتسلسل التالي:
1. **إنشاء فاتورة** في `pos` بحالة `delivery_pending`
2. **ربط المنتجات** في `pos_products`
3. **إنشاء سجل دفع** في `pos_payments` بنوع `pending`
4. **تسجيل العجز** في `delivery_cash` في `financial_records`
5. **عرض النتيجة** في POS Summary

جميع الجداول مترابطة بشكل صحيح والعملية تحافظ على تكامل البيانات! 🚀
