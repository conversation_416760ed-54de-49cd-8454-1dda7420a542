{"__meta": {"id": "Xe142289a56800999758ae42a11502f3a", "datetime": "2025-06-08 13:32:29", "utime": **********.516435, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389547.972324, "end": **********.51647, "duration": 1.5441460609436035, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749389547.972324, "relative_start": 0, "end": **********.356982, "relative_end": **********.356982, "duration": 1.3846580982208252, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.357004, "relative_start": 1.3846800327301025, "end": **********.516474, "relative_end": 4.0531158447265625e-06, "duration": 0.1594700813293457, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00652, "accumulated_duration_str": "6.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4607701, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.362}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4932492, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.362, "width_percent": 17.638}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1679586655 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1679586655\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1892075767 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892075767\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2106637084 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106637084\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhVakRGVXZ5YVNDcll0Tjk2SkFyVmc9PSIsInZhbHVlIjoiUy9GY0xFTzUxdDc2S2FrOC90cldYUzJvbTNtaDRINUNmaWZKWkhZV01Jb3V2bW9rK2xKT3FPK1lmYm80cGJFV2lRRVJXUmdWT2prOHloUkl3VFhxa0dXaVg4WlY5N2d0aGVzWDl6aXJOWkxKclhkbUVzUGZBZVFzT01CQ3dKZ3hNK0podmRTbzBjeVRBQi83QWxyNGo3ZHNkNVlEYUM4eGdBdTFqeGNnd0t2Nk5mNUpIaVA0OUc4RFB3WHVOOXJ0NXh4T3ZSYmNTeDNGWU5jdjB5aThQNzVrSXQxYkdpR21WTmwvRXlWaWJPQVpyK013UVVwZ2NkNkdoZ28wL2lwTndINm5rSWx5RXR0MWU4bzlRb0l1bVgrTHdWZmhYOGRuZzRrSXhlQU8zRlBGRnJndWFzRzdzZ1dtQkhBU0ZKcUVPOCtuLy8vMy9lVXdvM2NYZ2xvQ2ppTzFlTW9WMTJmcEJWVDVENThnQkFmRnAyMkVBNzVUUzNEVWE2WXJkU29rTXV2MVZqSFFZMGtPekMydjFuSms0aXNuMVFRS3E1OFNrSVVla3JFNFBNc3VETG5lUGxXcFdlT0VXaWVabU1pQyt3UmVmZzJYc2dQcGhsUkhJUEg4NVBwd0oyNnBvOU1Dd1FkUUdKd2hMczR2UDc1YlF4MjJ6RlE3VUNaQ1BaR2MiLCJtYWMiOiIxMjE2ZGQwYjA2MThiNDY3Nzc2NDZmZDdjMjJmZDk4YTIxZmI3NWU0ZmM2OTA4NzM5N2ViMGZkNGZmNTRhNDU3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRCVSt0WTR3dzdSZEFpZ1Zsd01ac3c9PSIsInZhbHVlIjoiRmVoWnZmS3U4MlllbE1oTnZ4RTh1bEljTVhjdzNhZmsxbmhZeFI3enFTRlpsbENuZkZxc1BuRTdUZExaSE9ZRDhYQW5tSDF1bWJQZU96YTZ4QmhCOEkvY21uK3FpWVVPa29kQjVqT3B6UXRHNmdzRjNNMVFlTGxEQU5pZk51N3VrTTQ3T1U5S0Y3cEZpQW1xclovOExhOUZqcnJ5RDI3OHl3NndvRlpmREMzMExqdHpOVzVBb1MzUXJuVm5ZR0xCSXlibWs5V212UzVtWlU1aXdjUWE5ZmF5L2NLd2JNLzJ6a3pBcjNwekpYc1VLdGdFSUZ1dDI0Z1FJdUJrRTlVV3pEVHZNWUVlamY3MG5icXIzc0tVOWd5bWhUY2ptR3dIbEZqQnljZlBwT2dwM2pVVDRsNE40RExpamlNSjk0UWwybXl1QTNUVElHNnQxQmJiN045L3ZrZm1odDlwbExVeVZYUmZobTVoVHY0YlNGdjA5YmR0bCtOUTczZWIyRHhoZG5aN1ZieVI3cFByYWJycFdCZ0cyZjlJYW45V0NDNG9OMlpENUM2cUMrb2t0QlNTUGpzdk5pOHRaVkFsUzAxWGU1NVdWbDBqc3ptTGhManRhWFpWY3ppdWYxeTJjTHVLN05aS0JTN1NqeHR4c3RkR1lxV3VtVVZKT21BTk9vcVciLCJtYWMiOiJmNjAzNzZmMTNjMDQ4NjM4ZDRiNTM4MTI5OTZhNTUwNjJjMTU4MGU0Nzc0NDlmZWVlMDM2MjFlYjExNmYxOGU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1011693732 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:32:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVLdi9XNnhVVVA2RWhlNk4rWXpWdXc9PSIsInZhbHVlIjoiVjhCdk9CUmt5Vk5sa0dCM25IcVpKN3BRK2MvdlFNLzFESCsySnNScnRMbzdTMEFJUmRLOWt0Nk83VnJ6Z0J5c1dJNW9Ma0NGUzhkVXQxV3lIUjdlWkJuYXIxaGJiR3pxZmFINkM0R05NWGVXRENPdE85TFJnWWMrcTcxdVdqSzIyNXZ6UlUxak81WmZuQnlnSmxLM2phSmx0QkR1Yk5CM2E3VEduUDllQnZNQUV6cG13bGlQemlUbFhWTTF6MEw2ZkJ6Q3ZOOUlQdFZ5Z0FTUzVjWnVqMEtxaVdpQkl6MzhFWmxENTVROTFKSTQ0bmRLMyt4bzFqaWx3OTY3RmJJZjVYNVVLVWgxWWp4eW5RVWpjU2IwbkVYL1d6T2R6ajZ2V0gzelhkY0lxNFdFZDVxYjNPb0RSYlgxWEVjVFROQmwyZFlyMVRscDhMemViZEJ4VkNhNTN1YlVxMmVVRmlkRkJXU0h3MnI4MTFHQ2Yvdjc1OXdiT2J5TjQ5YUw4NFl0ZE5HdWZhM2w1amtUb2NNbEs3NDJsaHpoNFA4WGJBVm5nVGZVb2RzdkFhbnVuRFova0tNTWM2eURmV0lYTkNMeGJ2Ym01Y29GWWN4aldKTG5GeW1HYVVzd0RhK1pJVHVvY2JiMS9HVmtvaWpCY0hzNDVVUm0weXh2L2VXOTJpcmUiLCJtYWMiOiI4NTk0OTBlZDJkOTViMjdkYWM2MzMzOThlM2Q5OTAyYjY2MGI2YTRiNmFmYzQwNjc5OGRlYTE5NjVkYjNiNmNkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxNQk0xaVZwaFFxTE1FbWZ0dkE1Z1E9PSIsInZhbHVlIjoiRDAvMllCY2F6VjNSSUltVXBDTE15dU9pMU8xMHFHZmEvQTBMcHJsNDgvRkJBRmZYYzFnZDdES0NBcFRDWjZ1aFhJQ1NiZ2E5ZG9yS3ErRXBlZm1KMDNkaHlPdCtDRk9VeHVXditEanE3WVVuczB0RVE5OUppNk12a0lZV0VIM3IyRDZYZUhMSTJVZ2tzSllXeVJ1YkFKTlZkWE9mYWNDWE5wbFErZGJhWXk3SlRaSW41dXMwY2ZQYzc1SWV0YlFyeUhmV3JXQlNMbUlabGJuSloxV0FyR1kwS2NWbjU5WHI4U2xkTUg5QmNEZzJNYVdSSzRKNjk4QTJvbGlyenM0RWtMUmZSWFludFJyd3dNR3RidlRQZkMvTFk4aGxKUUVXOC83TlcwaVVBdzBmYVFDa0o0Vk43c3h2QVZqZVJGenNabnRmNFU0UDk0RkQ2Q1RSYU5vSG0zWGpCWDNyZGNUazJyS3lxZVppVlB5WDNlZHBuZmZEZEJxcFdnUnRwMGdkZStWWWpNdTY1SlV4KzVqWEE0VEFwM1JUMXV6Rmp4OUFtWjRTdzdGeGlLZDk2eDZFUUlKWFpQaGVWcVNrVDJETXJiR240cXBrbmlDREpTN01aS3FsWFBCL3hqYzFEU3pQNkh3TU05RkxpeFNOZS9XRTIxUll1VzIxOHB0Nlo4WFQiLCJtYWMiOiI3MmYxMGU2NjU4YTkzNzhiYTdmOGJiMmIzNTI0NjMwNTEzZmMxOTYzZmM2NTA2ZmNhYWIwNjkyYjE2NjVhY2VkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVLdi9XNnhVVVA2RWhlNk4rWXpWdXc9PSIsInZhbHVlIjoiVjhCdk9CUmt5Vk5sa0dCM25IcVpKN3BRK2MvdlFNLzFESCsySnNScnRMbzdTMEFJUmRLOWt0Nk83VnJ6Z0J5c1dJNW9Ma0NGUzhkVXQxV3lIUjdlWkJuYXIxaGJiR3pxZmFINkM0R05NWGVXRENPdE85TFJnWWMrcTcxdVdqSzIyNXZ6UlUxak81WmZuQnlnSmxLM2phSmx0QkR1Yk5CM2E3VEduUDllQnZNQUV6cG13bGlQemlUbFhWTTF6MEw2ZkJ6Q3ZOOUlQdFZ5Z0FTUzVjWnVqMEtxaVdpQkl6MzhFWmxENTVROTFKSTQ0bmRLMyt4bzFqaWx3OTY3RmJJZjVYNVVLVWgxWWp4eW5RVWpjU2IwbkVYL1d6T2R6ajZ2V0gzelhkY0lxNFdFZDVxYjNPb0RSYlgxWEVjVFROQmwyZFlyMVRscDhMemViZEJ4VkNhNTN1YlVxMmVVRmlkRkJXU0h3MnI4MTFHQ2Yvdjc1OXdiT2J5TjQ5YUw4NFl0ZE5HdWZhM2w1amtUb2NNbEs3NDJsaHpoNFA4WGJBVm5nVGZVb2RzdkFhbnVuRFova0tNTWM2eURmV0lYTkNMeGJ2Ym01Y29GWWN4aldKTG5GeW1HYVVzd0RhK1pJVHVvY2JiMS9HVmtvaWpCY0hzNDVVUm0weXh2L2VXOTJpcmUiLCJtYWMiOiI4NTk0OTBlZDJkOTViMjdkYWM2MzMzOThlM2Q5OTAyYjY2MGI2YTRiNmFmYzQwNjc5OGRlYTE5NjVkYjNiNmNkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxNQk0xaVZwaFFxTE1FbWZ0dkE1Z1E9PSIsInZhbHVlIjoiRDAvMllCY2F6VjNSSUltVXBDTE15dU9pMU8xMHFHZmEvQTBMcHJsNDgvRkJBRmZYYzFnZDdES0NBcFRDWjZ1aFhJQ1NiZ2E5ZG9yS3ErRXBlZm1KMDNkaHlPdCtDRk9VeHVXditEanE3WVVuczB0RVE5OUppNk12a0lZV0VIM3IyRDZYZUhMSTJVZ2tzSllXeVJ1YkFKTlZkWE9mYWNDWE5wbFErZGJhWXk3SlRaSW41dXMwY2ZQYzc1SWV0YlFyeUhmV3JXQlNMbUlabGJuSloxV0FyR1kwS2NWbjU5WHI4U2xkTUg5QmNEZzJNYVdSSzRKNjk4QTJvbGlyenM0RWtMUmZSWFludFJyd3dNR3RidlRQZkMvTFk4aGxKUUVXOC83TlcwaVVBdzBmYVFDa0o0Vk43c3h2QVZqZVJGenNabnRmNFU0UDk0RkQ2Q1RSYU5vSG0zWGpCWDNyZGNUazJyS3lxZVppVlB5WDNlZHBuZmZEZEJxcFdnUnRwMGdkZStWWWpNdTY1SlV4KzVqWEE0VEFwM1JUMXV6Rmp4OUFtWjRTdzdGeGlLZDk2eDZFUUlKWFpQaGVWcVNrVDJETXJiR240cXBrbmlDREpTN01aS3FsWFBCL3hqYzFEU3pQNkh3TU05RkxpeFNOZS9XRTIxUll1VzIxOHB0Nlo4WFQiLCJtYWMiOiI3MmYxMGU2NjU4YTkzNzhiYTdmOGJiMmIzNTI0NjMwNTEzZmMxOTYzZmM2NTA2ZmNhYWIwNjkyYjE2NjVhY2VkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011693732\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1315109458 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315109458\", {\"maxDepth\":0})</script>\n"}}