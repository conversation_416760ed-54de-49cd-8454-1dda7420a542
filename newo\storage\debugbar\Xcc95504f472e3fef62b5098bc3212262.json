{"__meta": {"id": "Xcc95504f472e3fef62b5098bc3212262", "datetime": "2025-06-08 12:54:29", "utime": **********.283283, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387267.613705, "end": **********.283329, "duration": 1.6696240901947021, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": 1749387267.613705, "relative_start": 0, "end": **********.068562, "relative_end": **********.068562, "duration": 1.4548571109771729, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.068585, "relative_start": 1.4548799991607666, "end": **********.283334, "relative_end": 5.0067901611328125e-06, "duration": 0.21474909782409668, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45101632, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00972, "accumulated_duration_str": "9.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.175211, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 55.556}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.212701, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 55.556, "width_percent": 12.551}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2409658, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 68.107, "width_percent": 18.621}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.259992, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.728, "width_percent": 13.272}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1018375161 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1018375161\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-539196731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-539196731\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-144338255 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144338255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1224550841 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387248352%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcrMWRnNHlGRFd5Vk1oa2ZWNlhvNGc9PSIsInZhbHVlIjoiNFNreWpTbUNZVWRuZTgwc0NhUlo2NWViNEtxaE92Nm1GZzBJbStucWZtRHZYajhPTnJVQnBCT25TV1puTTB4QzFRZ1FseENwdFVGRk5RYlc4N3BUNk00WmxvUmw4WHNVRzlIQk0wYzBXNHZiUkNNSXhxZmJrQWhIVkt0QmQyMUE2WHBDdlgxalFadXNlSmwyU2dwZFV0a2pKTytoZ25CQ2svYkt2TWNTbVlsdU5uQmkwQ2dKalBJRFhiTnVZd2wxQllSK1RNUDlsQWpCVmRMSFpDV0w1TFNRT0hjVmtackpncEgyMk53alpCWDBsRXdkb3lEZm5DRnBNeXpTVXJtZlh3MlVzcFZraklOeEw4UjI4MFJKd0xJbTFDZnBKNjB0YXVMNSthK0YzRkVSVFhCbnVsZ08rREI1cFdwTGc5T2ppYkoxZ0FuNkNoOGozVGYzTjVqZTVGekswRm1KRTd0YWpkSmRHM3VmK1N1a3pBci9CejVlOHo2VEVhZEhwOXFQUC9OYm9PSnJZcGU0V0p4emNKNmh5emdVclV2cWQ3NlhMUENqMCszM2NsY1ZzVHpxWFNGa0pyWG9tblZWWFJqNHlaVStNQVM4b094dC9tb0RDSDZ3WHNMNTFhZDNmUXorMjJNQTF5U1Q2MGFibFdDMkhPZ2l0RThvRWU3blpxbGkiLCJtYWMiOiJmZGNlZDA0NzZjZDQ3MGIwNThhZDc0NmM3MzZlMzFkOTRlYzBlZGU2ZDk5ZGRlNjlhYzAzMjIyNDFlZjMxYmZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpCMjZzNFg4NHF3OUJHMFdEZTJYV3c9PSIsInZhbHVlIjoiSHB3UEdtUXdqdkVKR3YxMlNFcFVwM1owQm8xQWw4bng4L0tDdndvRnNxTVF0NlZzYVBkbW92QnIzVk5qU053bS83Q290K2ViVk9tMEJjM0RCNVR3Wi8wU0tPYzcveXFFcitXSjdPSDBNdEwwcWEvNHQzZ1FUeFRjTUpWWENxdDdzQTlTVzE5ekhjWGF3aVVjZjJyZkFsRHBLQm1yMjhMblBTU3RMTW1GTDhpZEZwN3NwQVdZa3VZUVJYZnR5UVQvZHM0eEd1RU9hOTNQSDJhbWtCMjI2eXZscnVpbzJXMFpBRnhqUFRWRnZNUDc3amZNUnFmTWtEWmtqLzM3R0pCRzkrd2dGeXRZcnJtZzY1a2lzM2toK3o0TXRMVXB6ZWt6c2xNbFNpRGJVOVU4Q1B3ci83enZEZng5eGpXQ25EZjJHVGZJY0c1eVNiNW5yZDloSDhrVEVEYTc5V0pMczFUdHJJZWgwa3pqU1hmRy9JNGtpRVJtMzFWTXFGN0FJNHQvUldsYW5pZ1pmRU9NQ2ZoNW43NmVLUmJsdUNSZ3VVSFE0R1BmYTRTcEpBTXZ0NE0xdWxDWW9KczdmblJobUhBeVd2RDBHT3plZ1N5aFlydU1rc1o5aDRiYTM0TmZRZ1Y4Y2d6OGRvd0J3Mk9OTFJWNTA1bkFZQWE0V0hLSzJtYzUiLCJtYWMiOiI3ZGNkZTI5MzI0NzA0ZTU3ZjlmMDBjOTlmMTNhYzEwZjJkMTU0NTBjNzRjM2U0MDYzODg5MjUwNDE2NzRkN2JjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224550841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2109185175 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109185175\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1650606388 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRJVEpQanBsb1Z1ajBOcEwzOU1wRFE9PSIsInZhbHVlIjoiNlJCY0hHc0ppVm4wOUREaVVlNy9DVHkweTVJRTVYZmJHVmYvY2NXcjNxV0N4YUN3Z3JXWUwvR1lJcDM0RDB0TnJERW9ZZ2xRQlNMMUdMaXhFVTc1Rm5oVHBEVllQeU5YVzhYc1J5empyZmtORGlweWY5dWNzVjZZSVErRzJKNVBDRUVYY080WWVJcDExZ1BucForUXJVaFNNdDRIdHR5cHFKVGZiRkxDQjZhUFZwZmdIMUgzRWxQbDhGZDg1YXZYOWhrSHU3eENOMXZIc1lKWlJpRDM3V3lsdjNpcDRmcXl2S2ZYTys1NklXQkFHbDR4a3AvRzhJVXE4ZmdpTDJzOE5kcWhpSW8xOS9nTHNmc0RoR1p6YnRWTS9VaENpNG8rWWpySG44czJyZWpEUmJFRWI5RHRKaFpHdzhTVHBLOHJWUDlXL0l4OFNTYW5uOHgrdHJkbk55cjh3MThQcUJZczhyVmhKQjRmTDFkYWIxRkdCaURlYnVLU295ZTZaK09vbG82SzMva2RiNHpGRnZocXkyRGc4WVlsQVdwWlpQeGxEdGV6SDFDTzhjdlFBQi9xZFBpbUZ0akdPaVh5aGdxQ3ZHRnBWbFlOWVFIdUlHREpVVUJ5cjZXOGpHRjdyZU5DSGt5QUZtN0NjUHpXVkRzdzFiS3cvbllCUmFDM3Nab2wiLCJtYWMiOiIyOTljMDc1MGE0MzNjYWM4NzA1YTU0NzliYmYxNzI4YjdjOTMyNTA5Yzg2YTEzOWFkM2E2YmRiOTBjZDgzM2MxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhBYmFwRGQ1aUExSUlWUGdCblZjckE9PSIsInZhbHVlIjoiQVhpNzNOeGtlQ3B1bFVTazRKR2tPQWNSWngxa0M2ZWtMNkM2ZkJQSnR1SmY0WkpDbWY5M1kyS0xHdURZRkVmenNVeWF0Ti9QUElmMzJTVXNjMnBCd1dYTjdLQUlPODFtV09FaFdGU1dFOWZCYUc0SlhENmZYNEZScmo2OFlrUm1jYTdOUzlaVEpZdVIwMzhJWkNaSkkzenhEZVR2MC9zTmpIUFlIQlYxNDdMWm1sVlMxRjRxb1pzTG16T21UQTlrU01lS3U0aTF6dHhRQTBjOUZzdHZiOERCYTFhZkg0ZkhMNUk0YXZxc2RGcjFzTzJrZ3BFRTVMWm1wVDFOZmNVenlkOTlQbDZjZTVXNnJaY0JkNXNudXBJQUJTenZ2Z2dnZ3VVZmt3eERFK0pIdWxMbmp6OEI0YXJtVHhzb3EwN2NxaGtTSVdLcjZxTVNna0lvaHp6NGpUZmZkT0Y1ZUE4aTROaUh0YXc2N2ppcTJ0Q2ZLZ0VlZXZna3FmNzVGK3pLTEJaTUo3N20rTzIxTno5ZU43cUt2ZWsxeUxGUUd4L2oyaFJybHZ1OEw0UVM3djl6R2FFbGhianNMUEF2MGdCYTIySVpzT2NKb0J5SlRaUEw3MUJJalBuQnFzRG5uQ0RzU0dXVmxBR3lvSVFIRjc0YmhsQ2VEb3RhSXNJMmJ6UlMiLCJtYWMiOiI4YTJkOWU5NmFmN2IwNDk4MTcwZGRkMjQzMGUyODk3NGEzNjhmOTQ3ZWE3ZjI5ZmVlZjYxZGNkNjNlM2ZkMDY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRJVEpQanBsb1Z1ajBOcEwzOU1wRFE9PSIsInZhbHVlIjoiNlJCY0hHc0ppVm4wOUREaVVlNy9DVHkweTVJRTVYZmJHVmYvY2NXcjNxV0N4YUN3Z3JXWUwvR1lJcDM0RDB0TnJERW9ZZ2xRQlNMMUdMaXhFVTc1Rm5oVHBEVllQeU5YVzhYc1J5empyZmtORGlweWY5dWNzVjZZSVErRzJKNVBDRUVYY080WWVJcDExZ1BucForUXJVaFNNdDRIdHR5cHFKVGZiRkxDQjZhUFZwZmdIMUgzRWxQbDhGZDg1YXZYOWhrSHU3eENOMXZIc1lKWlJpRDM3V3lsdjNpcDRmcXl2S2ZYTys1NklXQkFHbDR4a3AvRzhJVXE4ZmdpTDJzOE5kcWhpSW8xOS9nTHNmc0RoR1p6YnRWTS9VaENpNG8rWWpySG44czJyZWpEUmJFRWI5RHRKaFpHdzhTVHBLOHJWUDlXL0l4OFNTYW5uOHgrdHJkbk55cjh3MThQcUJZczhyVmhKQjRmTDFkYWIxRkdCaURlYnVLU295ZTZaK09vbG82SzMva2RiNHpGRnZocXkyRGc4WVlsQVdwWlpQeGxEdGV6SDFDTzhjdlFBQi9xZFBpbUZ0akdPaVh5aGdxQ3ZHRnBWbFlOWVFIdUlHREpVVUJ5cjZXOGpHRjdyZU5DSGt5QUZtN0NjUHpXVkRzdzFiS3cvbllCUmFDM3Nab2wiLCJtYWMiOiIyOTljMDc1MGE0MzNjYWM4NzA1YTU0NzliYmYxNzI4YjdjOTMyNTA5Yzg2YTEzOWFkM2E2YmRiOTBjZDgzM2MxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhBYmFwRGQ1aUExSUlWUGdCblZjckE9PSIsInZhbHVlIjoiQVhpNzNOeGtlQ3B1bFVTazRKR2tPQWNSWngxa0M2ZWtMNkM2ZkJQSnR1SmY0WkpDbWY5M1kyS0xHdURZRkVmenNVeWF0Ti9QUElmMzJTVXNjMnBCd1dYTjdLQUlPODFtV09FaFdGU1dFOWZCYUc0SlhENmZYNEZScmo2OFlrUm1jYTdOUzlaVEpZdVIwMzhJWkNaSkkzenhEZVR2MC9zTmpIUFlIQlYxNDdMWm1sVlMxRjRxb1pzTG16T21UQTlrU01lS3U0aTF6dHhRQTBjOUZzdHZiOERCYTFhZkg0ZkhMNUk0YXZxc2RGcjFzTzJrZ3BFRTVMWm1wVDFOZmNVenlkOTlQbDZjZTVXNnJaY0JkNXNudXBJQUJTenZ2Z2dnZ3VVZmt3eERFK0pIdWxMbmp6OEI0YXJtVHhzb3EwN2NxaGtTSVdLcjZxTVNna0lvaHp6NGpUZmZkT0Y1ZUE4aTROaUh0YXc2N2ppcTJ0Q2ZLZ0VlZXZna3FmNzVGK3pLTEJaTUo3N20rTzIxTno5ZU43cUt2ZWsxeUxGUUd4L2oyaFJybHZ1OEw0UVM3djl6R2FFbGhianNMUEF2MGdCYTIySVpzT2NKb0J5SlRaUEw3MUJJalBuQnFzRG5uQ0RzU0dXVmxBR3lvSVFIRjc0YmhsQ2VEb3RhSXNJMmJ6UlMiLCJtYWMiOiI4YTJkOWU5NmFmN2IwNDk4MTcwZGRkMjQzMGUyODk3NGEzNjhmOTQ3ZWE3ZjI5ZmVlZjYxZGNkNjNlM2ZkMDY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650606388\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1254957764 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254957764\", {\"maxDepth\":0})</script>\n"}}