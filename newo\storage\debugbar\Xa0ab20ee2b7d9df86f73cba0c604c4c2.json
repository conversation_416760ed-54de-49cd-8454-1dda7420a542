{"__meta": {"id": "Xa0ab20ee2b7d9df86f73cba0c604c4c2", "datetime": "2025-06-08 13:05:37", "utime": **********.680482, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387936.354376, "end": **********.680514, "duration": 1.3261380195617676, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749387936.354376, "relative_start": 0, "end": **********.514597, "relative_end": **********.514597, "duration": 1.1602208614349365, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.514622, "relative_start": 1.1602458953857422, "end": **********.680517, "relative_end": 2.86102294921875e-06, "duration": 0.1658949851989746, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45285096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0054, "accumulated_duration_str": "5.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.621402, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.519}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6538532, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.519, "width_percent": 21.481}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-591613968 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-591613968\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1714777348 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1714777348\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-762412697 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762412697\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1320379360 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpmaDRBcTRJUXNEeVg4N1dQNFJiRmc9PSIsInZhbHVlIjoiYW1RNmJ1NFZ6cUhnelhHRFQyK3FKOFRXb3EvV2QyeVMxV3JJSTNRZHBmYStyOFZZbDIzZ3BTazB4VitPbitOYWZIQUhtTzlWZ254TVJDY1JKMjFMQis3UUJVZTZ0L3lrUkJUM2lCcWg1TUp0bFpUOHlnZmFNRWw0dUtCV2NVZkQ1Q1NBTnhQV1poRnFIWVVtbFBBLytpcFJiVG5sa2hrNlZpbW9VNmE1Q2sxVXpPL2czRWlRdGtJU2NMQlduUmpKOHdPMW9wNVpOVjVlc2M4WnNvTGpZTk5OZC9tWktOWnJ1TS81aXVWZU1obmVVc3lxd3pYWG5EMS9PQlFuallpRzlPaUh5TUtDVFRTeFAxMVQyVVBZaGZtVzdiei96ZnRzWmovSHZIY1ZXdVdoZm5wVDYxekQvQlFuNHZmQ1o0MkZTL1hybXNIdmNzU2J4Zk1oL0tMUk1DOHAzTlF4dzJiWHo0N2RkaXBOSDIvN3B6d2dUYmVtU0x6dkE1UHd5MG9ob2lzQXF2K3pza2hSUjZVRURCTy9GWnZUblFVbld2aG9DeDg5NjdVanBPbWE3dlJlQ3VpNUlmNXVrc1ptenAwK0RMV0RmVHo2cGloUXRGUE9sdHFQT1BpaDJmR25SczBMb1RLc2l6TlNRZnNpbGdVRGlWUm5ndmVKSHp1eFFyY0QiLCJtYWMiOiI5NTQzNmM2YTI0MzI1YTRjZDcwNmNhODBiMGY3YzNjYzgzOWQxMzhhN2M4ODNmMGZiMDZiYmViMTUwNDI2ZjgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iko4NnlRelJFeTVTU1ZZRTJvRzZDYkE9PSIsInZhbHVlIjoicGUrMGZETlRMYy81M0FKdkIvbE5reEdpTytjRlVvbFJmVlZ5M2FJa1ExeXB0WWhjZEhMQVpFZmt5b0xIQWRvVFBzZTJFYmNVNHp4OTR6Mk5VNHFML3hSRjhJWFhxQThjMzBzTFdnT3N1djZwNzdNVkJwL2xnM0t3em9ESDdWQ2JLSlRzNWQwNG9DQXliNFVKa1YzK0w2MmNKWWgzTFJxZVFKTmtSUTI4enJ1UXFzanNjTnlnQzE2WTdvb21ZQTZuTUdwU2swOHYvOWdicHFNS2VUeTZubVJSR0tXYmFGaHUzNzhucXhIbFgzcE42OWpqTGY2MWNDTjZGSXBjeEdzSlZQdFhMMWUySEExK1dVTFNMaFJ3bkFIN3JxYUJvbEJsN0pBZSt6TVBtRklwcHdiNzR1RlpNZGFzZnpHMjE2UnJvanpGcjIxd1doYU54OVkzUkNZQlVZblM2MEVERVVUa0I3UDAzazRKdEk2VkY5Z3ZZc3d3cjFiTnVTUEdydHhtTFRTL3NCUzBXcDhicGZOcS9BaWttM20zdXFvSXZjTTljdDJ3dDZGS2grclVMRyt2ejVLTHgzYTA0bjZZRUowazdWbE5wZGxpd25KSlVFNkRtaVI1WkgwRHdOZ0xjbEtYZ3lGK3NVWC9yTm5iRndtczIwUUYzNDhscFRMbHQxNmwiLCJtYWMiOiJhYjc0NjExMzgzZWYwMTE4MWIxMGEwOWRiOWJjZjcyMTE3ZDY4MGEzNTcxMzE5ZTE4NGMwZWE5ZmI5ZDk1ZmY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320379360\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRNand5cVNzRFpWdlJndU1kRGRPdkE9PSIsInZhbHVlIjoiK00rbEdOdkEvK2VkTzFGS2NIQW54VU01MUtSSXhsb0ZpWDBhUURxb0txc3JmQlhFL3VnMnc3OG8wNHViOU9lYTFXTEVETnNCQitPSnEwdi9kbXp5Y0lrY1RSOVlFUWtyZWFQMGdWV1F3R2NGSklhTzhhcEVLLzY2V0lxT1ViZWZTQnFBVG5NVys5azBTTStOQXNHUXRZMitDMXFpR3pEdmNPR1JWaUxjZjBzS2lRellxOVgxSWg5VGozZVhRS1dWUTBOK2ZLMDJMaEdlYnMrNnFkTGp4N2FuSGdqUVF4cnNudUtNOGtUdDBQeS9FTDVBSlB6SnVZQWE4UGNkN29sSUdZcVlzb29WbjFHN0V1Zm5xaUt6bGVucFBnMFJRVy93b1F3U2ljbktQd3VmMHhDeUNNazd1VGhhTE5vU2ZMWkZyZktWekhlM0lxLzg1UGh1WlhNNng0RURla3VQOEw3VTgvdFgyUkEwaWdwaGxnQXJZanhWeGNhQll2T0pPa2NXTW9IMUtTRW93UkFxR0FBRVNENzlLYmk2Q2plalprUENxZDFvLzNDdXRLaGlydldmU2FOQ0tLckxUaXFIaE9kSmpyWHBHWkdudEhTakFOSUhuemdYYXRJYmNuNGhiZUo5cUlGN3hMMlNsYi9ZckpENTlsRktqZ1UyWGlKbkM2ZnMiLCJtYWMiOiI3NDA1ODMxYjUzZTJmOTg4NzQ3YjI2OTdiYmY5OTEzY2M3NjgxNDRhMmI5NDIyMDhiMjM1M2FmOWI4YmFkN2Y0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNnS1prdmNWR2Q1Ni9wS2R3NmZYQUE9PSIsInZhbHVlIjoiN214Kzl3MUFiN2hER293VFdiWXRndG80a2dvWmVRcmtVZ2hmbnREYmk5d1NjaU9WNjh3VXk5eUNrcm5DL1Z6TUYwRUdZTU1XTENWNEFJT3VKSC94TnE2bGhPRm9DMlR4WXRuejBod0dyeUN1bjZLYXo4ckdwTFcxS01LVG5HVzhzRlRYNnJKSzlodWxid2pxRUZFQnNhbStjWitWZ0JGWGVGVTZHN3dROWxZZjVsSU4yVXd3eXVMTTl1ZFBVRWVaZnF6UW4wQVFSRkFvZ0Jya3RNRnVyR3c1OUpmM0pOMUpNMnhMZnlDZHl5QjFndm9wVjROeW1EVkFaa3F0bmhTUHNFcENtTHN4N3hNWXF5VDlxeGVZa0hUaU9QWDJmMVFOSVp0Z2FhcTNhRTQvOGxQb2FJVy9BT1BGMEh3WXVqdVZnK2NaQk83cjRtdnF4cEZSeGtadGtoWEJMenJwSjgycVZ0UVBMdjltNG9IVUNYSURLblVtdWwzcnVyd21rcGlNeGdFMmtrcGpxcitROUxPblpsQVV0N2xsVWFPYU14NUNNQU51dlRKTmNPRUdWY1pXa0szMUtwbVZRMUhISm8rVUJaY0FkRlo3SEJ4MS8vNU9TVmRMeXNBbkVuYUFpUXdhbVlpcGZBUlVxOCt6cFpLak5uc0hRUTJyd0tQcUdWYkEiLCJtYWMiOiJjMDk5MTA2MTRkNDcyOGMzYzA2ODAwOTAxN2RjZmZkMjI2NzJlNjQxNTgzNGQ4YjVkMDU4MTA1MzRkMjkxNjZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRNand5cVNzRFpWdlJndU1kRGRPdkE9PSIsInZhbHVlIjoiK00rbEdOdkEvK2VkTzFGS2NIQW54VU01MUtSSXhsb0ZpWDBhUURxb0txc3JmQlhFL3VnMnc3OG8wNHViOU9lYTFXTEVETnNCQitPSnEwdi9kbXp5Y0lrY1RSOVlFUWtyZWFQMGdWV1F3R2NGSklhTzhhcEVLLzY2V0lxT1ViZWZTQnFBVG5NVys5azBTTStOQXNHUXRZMitDMXFpR3pEdmNPR1JWaUxjZjBzS2lRellxOVgxSWg5VGozZVhRS1dWUTBOK2ZLMDJMaEdlYnMrNnFkTGp4N2FuSGdqUVF4cnNudUtNOGtUdDBQeS9FTDVBSlB6SnVZQWE4UGNkN29sSUdZcVlzb29WbjFHN0V1Zm5xaUt6bGVucFBnMFJRVy93b1F3U2ljbktQd3VmMHhDeUNNazd1VGhhTE5vU2ZMWkZyZktWekhlM0lxLzg1UGh1WlhNNng0RURla3VQOEw3VTgvdFgyUkEwaWdwaGxnQXJZanhWeGNhQll2T0pPa2NXTW9IMUtTRW93UkFxR0FBRVNENzlLYmk2Q2plalprUENxZDFvLzNDdXRLaGlydldmU2FOQ0tLckxUaXFIaE9kSmpyWHBHWkdudEhTakFOSUhuemdYYXRJYmNuNGhiZUo5cUlGN3hMMlNsYi9ZckpENTlsRktqZ1UyWGlKbkM2ZnMiLCJtYWMiOiI3NDA1ODMxYjUzZTJmOTg4NzQ3YjI2OTdiYmY5OTEzY2M3NjgxNDRhMmI5NDIyMDhiMjM1M2FmOWI4YmFkN2Y0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNnS1prdmNWR2Q1Ni9wS2R3NmZYQUE9PSIsInZhbHVlIjoiN214Kzl3MUFiN2hER293VFdiWXRndG80a2dvWmVRcmtVZ2hmbnREYmk5d1NjaU9WNjh3VXk5eUNrcm5DL1Z6TUYwRUdZTU1XTENWNEFJT3VKSC94TnE2bGhPRm9DMlR4WXRuejBod0dyeUN1bjZLYXo4ckdwTFcxS01LVG5HVzhzRlRYNnJKSzlodWxid2pxRUZFQnNhbStjWitWZ0JGWGVGVTZHN3dROWxZZjVsSU4yVXd3eXVMTTl1ZFBVRWVaZnF6UW4wQVFSRkFvZ0Jya3RNRnVyR3c1OUpmM0pOMUpNMnhMZnlDZHl5QjFndm9wVjROeW1EVkFaa3F0bmhTUHNFcENtTHN4N3hNWXF5VDlxeGVZa0hUaU9QWDJmMVFOSVp0Z2FhcTNhRTQvOGxQb2FJVy9BT1BGMEh3WXVqdVZnK2NaQk83cjRtdnF4cEZSeGtadGtoWEJMenJwSjgycVZ0UVBMdjltNG9IVUNYSURLblVtdWwzcnVyd21rcGlNeGdFMmtrcGpxcitROUxPblpsQVV0N2xsVWFPYU14NUNNQU51dlRKTmNPRUdWY1pXa0szMUtwbVZRMUhISm8rVUJaY0FkRlo3SEJ4MS8vNU9TVmRMeXNBbkVuYUFpUXdhbVlpcGZBUlVxOCt6cFpLak5uc0hRUTJyd0tQcUdWYkEiLCJtYWMiOiJjMDk5MTA2MTRkNDcyOGMzYzA2ODAwOTAxN2RjZmZkMjI2NzJlNjQxNTgzNGQ4YjVkMDU4MTA1MzRkMjkxNjZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}