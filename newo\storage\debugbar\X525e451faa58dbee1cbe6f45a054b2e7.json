{"__meta": {"id": "X525e451faa58dbee1cbe6f45a054b2e7", "datetime": "2025-06-08 13:27:11", "utime": **********.508914, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389230.104918, "end": **********.508947, "duration": 1.4040288925170898, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749389230.104918, "relative_start": 0, "end": **********.349456, "relative_end": **********.349456, "duration": 1.2445380687713623, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.349477, "relative_start": 1.2445590496063232, "end": **********.50895, "relative_end": 3.0994415283203125e-06, "duration": 0.15947294235229492, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45602312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0092, "accumulated_duration_str": "9.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.422181, "duration": 0.00658, "duration_str": "6.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.522}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.458597, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.522, "width_percent": 13.261}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.48042, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.783, "width_percent": 15.217}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1651827072 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1651827072\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1794166590 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjgvRHdWWG5XRHR5SUR4YWNOWE84Tmc9PSIsInZhbHVlIjoieElNdDFMOVlrTFhja3U0Ynd5cWpXNHpXNHUrUUdtdmwvVWpOT1NOQmp5Wlh4clAxU2FiUDFzMkIxYkt0MjBsOFJGZGdGMnhza2tUM1VkRStpM1RsMHdpM0pyenByRnZCQndHSGRyNEhncmFhVzhWK3pabTdZUHA2QWxwSnVqQW5udVJuSThGZDRpMkV1WmlvV0hieXJFUzVBMjdVWFBScFMybUsvZ2dBaE1jcWFmK0JWTFE0Z1VIa3E2NzJVR0xaWmJPZ1IwWDhwM05kc2NvSDh3czdWcGpUUjRoaXQxUmw2RFQrZzdyTGlsaGVieEdJaXp1L0NTaWFTSlVJSWJzVlU1SzdEZWh2b3pEaWxsWTI5ekVSdFV6MDRZK3dJV1J4bC9OM0VEZ0tiZVc0Vlcrbjk1WlNFK0NFZmY5c01YWGFDd0UvdDdJamhWS3A3Tk9kVEZDYi9hZWFqMUZGNk9mQ2ZGTVNMY2VUcDR3QkpGc2Y5cEFBeFZlWkl5TnNrSlErQktVS3RHaFNuYXN2SkdZOStwMmN5dlFhWEJLQ3pReU5lTHFKb0pFU1E5ZHJhR2ZNVzFoeDBHekIxcEw4SGpjUkQwR1RyenI0S29qS1ZUSVlmRlFBVU9taFNLWWhrbFRIYThCUjFteU5zUzNyMyt3d2RVazloeklaZk10aWdSaUciLCJtYWMiOiJiMWM0YzdiM2YxNjk3MjZkYmJiNDUzYzA5MjUxNTFkZmVjZWQ1NTdhYjM0NzRhMmIyM2YwMDk3NmFjMGQ2MWQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik95eXgxdWdVYzB5UVJ0VWNKOG03ZGc9PSIsInZhbHVlIjoiVlppczl2WTJKT1NOTXg1emRjaHpQVXNCdi92K2VyRlo1RkRKQmxrZVNGTW5pMG1qS3VuNFJDbEN2M08ycUxxZ0xNMkU2dEdNMm9KYTg2dnVsZTZXRlpycld2UWRkTGpJcTNkNXNLUGZiaFUvYlVzMXdMVEM0QnN0Y2VHRmVNZVF6Z2YraEVBaUNmYlZwYk9wbnJFcE1GalpaaTVBVWovcmtPdEd0SVJYM2gzUXhrNkRTSEpGWjFuVzF3ZmtacE0xRVI5VTVGb0xWY0NjdTBiTTNXV0FsT08zSGdlVFdibmhjWXU5VHlleTJnYmp5MEdwOWxFSC8zUmNYOXZKeWQ4K0VUeVB6dUJqS05RY3hKZkxXWGRmL2g0eElsdXg2aVpYb0ZHWHpZMjBhUXVmek1KZkJlSW1VclZRVVBKQkY4NzBlYmtVdE5NYU0reVlQWGZWVC9WNXJRMjhaem9iN1JGYlJvMDJMTlBqYzFFeC9XQ1lOaUgwUzMrSnhpYmhHaU1USW8ySGhET3BLd0ZZTVJXbmx4TlZjcHBDMUhEbThtcUR3c0VhQmluNlZhOWhWUmtqYjNVcDJCREJKakZ0QlFUUFVkWm5aOGV1VEZHK2lxcjVrdXVhVytZSVVIaVhmMHZXY2RxOU41dlNkN0FyMDlHZ05NYzN5cHlab21SSWo5S00iLCJtYWMiOiI1MDZjMGU2OGI4MDJlMjRjZWVhM2I1ODk2NjFmNzkzYWU0ZmQ4ODE2ZmE4NjBlMDA2MDI2Zjk1Y2YwZWYzOTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794166590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-815259111 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815259111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikw5TE5tQzBGMHBNWWFhUHVaNXFVRmc9PSIsInZhbHVlIjoiK3MyUklRanZBUUl2RzNENHlhUWMvVDZWYjViR3B0NUVpYkZXU01aNE1GOU5XK0J3bWNvNnBmTERpK0c3V0xSTE5DVjBETllRR1RZUEloejByMWwvVmdBd1FsYklSNm01aXF3aW5BZ3lpbUM3VHovQkxlRU1WSjYvNW5OdnkrQWF0N3R2T09NQ3U2NGZ6VzhmK2dKS1N5NDJiR2VIZVNGSGJpam1QdGtVQU5zbVRUOUcranJzNEpFdGNPQzRTSys3end6Y3FTZzBhQWY2a0tNOE9wZ0VlSjNydCs1aXVIWmVFNUE4TU1jbVhRc0R3MEFtOXBrY3VlNFZsVkh3MElPRmFzdmJQV0FLTmxSZHcyVU5qWEFqWk5weXA2enh0Z3ZsN2o5YW9pc2JyZXlSWVUrVjVnN3k2RGN4dUo3ODFaRUpFYStEQmt6VHdXVkZNOUpNTGRINE5BTFcxNklvT3JFVjV4MHJ2L0hDclF4QmV2bS8zVGRjbmJWY2VCekhKcCs0cTIzcGQ2ekhydnRGdUlqRTBXLzZ5clFrMVlGbFZSOXZjUjBUWnNmdXllUDBHb1NWVmYyVmFjRFkyV2VVeWd0TkpPenlXUVovdmFaS3VwWlZ5YXFJWmR2MG1adktoTFVrVDN1N0tQYUczOWkrc1REdEZ3RkdvK0h0VDNxUDBHaXAiLCJtYWMiOiI1NDZiYTMzNTQ3YzBkNjQ2NWViM2RjNWQxN2QzZTBiNmEwNmRhZDgxNDdlZDkwNjNmZGY0MzM5OTU5NDY4NmVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJiWHJrd3FxVkpybDdkazZ0M2VwQWc9PSIsInZhbHVlIjoiT0hWekFpK0kvRVl6Yk5IMVhuRGhLZjRWU21HejgxNkVncVNMZnl5SExOUVlUNFl4aTF3YmY4Tld5UnFFcjFPNnhsQkt1NitlSDBONzUraVhhSFNqVlV6ZDM2dkszczhSQVFNM2YveFJXcFdPWGEyVEF3ZHVJOEo4eEZwVFFhMjcxYiswZ0lvUFRNZTZwYmw1dkR4aUU4am9Pd0UxTDQzQ0VTc3o4Y3pPbUZ4bXVmMWhDcW1iQWJxakxhYSsya1A5ZmF0V2xwTjFhWHdIcklKQmhvSzluYjExQjltUWllbGlUY2hEVSsxWGtEOWVHY1ZOV3pZS3ZMS1hRc1BoWUNIaHR3MDZ4RkVWMlJwQi8ydmYxZEErTC9xa0lJMnRqblZjeFJYWlhHa2xNRE9kWGgzVXlrVWYwSTBnb3VFcFdFcFd0TFY4dytZN3RMK3VDdWdvSG16ZUZtazZUb0ZwaS8xWHBBb2xFc0dycnZSRWx4UWt1K1JuZmRzNFY0VzFDa0s3eGNhdTI2aUtiaEFpamZqaDVtajhxdFFUNGtCMjYxZVZ1ekJGRXRUZ3J4QjV3WkttVzh3dHBvbFBBeHNKMit4K2prdFJLN3E5VG1hdm1nYldJTytlby9RUzUvVDN6bjI3Rk9IbDdSVVhEek1wcG1jY0hBSXVUbnNJRWpneVQycjQiLCJtYWMiOiJmMzc2ZmE3ZmQ5MWJhMWExNTc5YTQ5ZWM2YjljYmE5ZWE1YmYzZDU3YTE3M2ZkNTc5Yjg1M2Q0NWZjNjFiZTNlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikw5TE5tQzBGMHBNWWFhUHVaNXFVRmc9PSIsInZhbHVlIjoiK3MyUklRanZBUUl2RzNENHlhUWMvVDZWYjViR3B0NUVpYkZXU01aNE1GOU5XK0J3bWNvNnBmTERpK0c3V0xSTE5DVjBETllRR1RZUEloejByMWwvVmdBd1FsYklSNm01aXF3aW5BZ3lpbUM3VHovQkxlRU1WSjYvNW5OdnkrQWF0N3R2T09NQ3U2NGZ6VzhmK2dKS1N5NDJiR2VIZVNGSGJpam1QdGtVQU5zbVRUOUcranJzNEpFdGNPQzRTSys3end6Y3FTZzBhQWY2a0tNOE9wZ0VlSjNydCs1aXVIWmVFNUE4TU1jbVhRc0R3MEFtOXBrY3VlNFZsVkh3MElPRmFzdmJQV0FLTmxSZHcyVU5qWEFqWk5weXA2enh0Z3ZsN2o5YW9pc2JyZXlSWVUrVjVnN3k2RGN4dUo3ODFaRUpFYStEQmt6VHdXVkZNOUpNTGRINE5BTFcxNklvT3JFVjV4MHJ2L0hDclF4QmV2bS8zVGRjbmJWY2VCekhKcCs0cTIzcGQ2ekhydnRGdUlqRTBXLzZ5clFrMVlGbFZSOXZjUjBUWnNmdXllUDBHb1NWVmYyVmFjRFkyV2VVeWd0TkpPenlXUVovdmFaS3VwWlZ5YXFJWmR2MG1adktoTFVrVDN1N0tQYUczOWkrc1REdEZ3RkdvK0h0VDNxUDBHaXAiLCJtYWMiOiI1NDZiYTMzNTQ3YzBkNjQ2NWViM2RjNWQxN2QzZTBiNmEwNmRhZDgxNDdlZDkwNjNmZGY0MzM5OTU5NDY4NmVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJiWHJrd3FxVkpybDdkazZ0M2VwQWc9PSIsInZhbHVlIjoiT0hWekFpK0kvRVl6Yk5IMVhuRGhLZjRWU21HejgxNkVncVNMZnl5SExOUVlUNFl4aTF3YmY4Tld5UnFFcjFPNnhsQkt1NitlSDBONzUraVhhSFNqVlV6ZDM2dkszczhSQVFNM2YveFJXcFdPWGEyVEF3ZHVJOEo4eEZwVFFhMjcxYiswZ0lvUFRNZTZwYmw1dkR4aUU4am9Pd0UxTDQzQ0VTc3o4Y3pPbUZ4bXVmMWhDcW1iQWJxakxhYSsya1A5ZmF0V2xwTjFhWHdIcklKQmhvSzluYjExQjltUWllbGlUY2hEVSsxWGtEOWVHY1ZOV3pZS3ZMS1hRc1BoWUNIaHR3MDZ4RkVWMlJwQi8ydmYxZEErTC9xa0lJMnRqblZjeFJYWlhHa2xNRE9kWGgzVXlrVWYwSTBnb3VFcFdFcFd0TFY4dytZN3RMK3VDdWdvSG16ZUZtazZUb0ZwaS8xWHBBb2xFc0dycnZSRWx4UWt1K1JuZmRzNFY0VzFDa0s3eGNhdTI2aUtiaEFpamZqaDVtajhxdFFUNGtCMjYxZVZ1ekJGRXRUZ3J4QjV3WkttVzh3dHBvbFBBeHNKMit4K2prdFJLN3E5VG1hdm1nYldJTytlby9RUzUvVDN6bjI3Rk9IbDdSVVhEek1wcG1jY0hBSXVUbnNJRWpneVQycjQiLCJtYWMiOiJmMzc2ZmE3ZmQ5MWJhMWExNTc5YTQ5ZWM2YjljYmE5ZWE1YmYzZDU3YTE3M2ZkNTc5Yjg1M2Q0NWZjNjFiZTNlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-16******** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16********\", {\"maxDepth\":0})</script>\n"}}