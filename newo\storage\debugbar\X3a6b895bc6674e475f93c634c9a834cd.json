{"__meta": {"id": "X3a6b895bc6674e475f93c634c9a834cd", "datetime": "2025-06-08 13:05:37", "utime": **********.903809, "method": "GET", "uri": "/pos?warehouse_id=8&ajax=1", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387936.369316, "end": **********.90385, "duration": 1.534533977508545, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1749387936.369316, "relative_start": 0, "end": **********.561924, "relative_end": **********.561924, "duration": 1.1926078796386719, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.561951, "relative_start": 1.1926348209381104, "end": **********.903853, "relative_end": 2.86102294921875e-06, "duration": 0.3419020175933838, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52911864, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.index", "controller": "App\\Http\\Controllers\\PosController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=37\" onclick=\"\">app/Http/Controllers/PosController.php:37-123</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01662, "accumulated_duration_str": "16.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.685521, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 23.466}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7191029, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 23.466, "width_percent": 5.776}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7689798, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 29.242, "width_percent": 4.753}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7753708, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 33.995, "width_percent": 5.716}, {"sql": "select *, CONCAT(name) AS name from `warehouses` where `created_by` = 15 and `id` = 8", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.792129, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "PosController.php:50", "source": "app/Http/Controllers/PosController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=50", "ajax": false, "filename": "PosController.php", "line": "50"}, "connection": "ty", "start_percent": 39.711, "width_percent": 7.521}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'ty' and table_name = 'customers' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8022418, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "PosController.php:57", "source": "app/Http/Controllers/PosController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=57", "ajax": false, "filename": "PosController.php", "line": "57"}, "connection": "ty", "start_percent": 47.232, "width_percent": 22.623}, {"sql": "select * from `customers` where `created_by` = 15 and (`warehouse_id` = '8' or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["15", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.813989, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "PosController.php:69", "source": "app/Http/Controllers/PosController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=69", "ajax": false, "filename": "PosController.php", "line": "69"}, "connection": "ty", "start_percent": 69.856, "width_percent": 8.965}, {"sql": "select * from `users` where (exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\Models\\User' and `name` = 'delivery') or exists (select * from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `users`.`id` = `model_has_permissions`.`model_id` and `model_has_permissions`.`model_type` = 'App\\Models\\User' and `name` = 'manage delevery')) and (`warehouse_id` = 8 or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["App\\Models\\User", "delivery", "App\\Models\\User", "manage delevery", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 85}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.823427, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "PosController.php:85", "source": "app/Http/Controllers/PosController.php:85", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=85", "ajax": false, "filename": "PosController.php", "line": "85"}, "connection": "ty", "start_percent": 78.821, "width_percent": 12.455}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 531}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 88}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8374798, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "PosController.php:531", "source": "app/Http/Controllers/PosController.php:531", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=531", "ajax": false, "filename": "PosController.php", "line": "531"}, "connection": "ty", "start_percent": 91.276, "width_percent": 8.724}]}, "models": {"data": {"App\\Models\\Customer": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-876845537 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876845537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.78902, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1742874714 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742874714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.835683, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/pos", "status_code": "<pre class=sf-dump id=sf-dump-1498717393 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1498717393\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1297533682 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>ajax</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297533682\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1531966114 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1531966114\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1005116214 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpmaDRBcTRJUXNEeVg4N1dQNFJiRmc9PSIsInZhbHVlIjoiYW1RNmJ1NFZ6cUhnelhHRFQyK3FKOFRXb3EvV2QyeVMxV3JJSTNRZHBmYStyOFZZbDIzZ3BTazB4VitPbitOYWZIQUhtTzlWZ254TVJDY1JKMjFMQis3UUJVZTZ0L3lrUkJUM2lCcWg1TUp0bFpUOHlnZmFNRWw0dUtCV2NVZkQ1Q1NBTnhQV1poRnFIWVVtbFBBLytpcFJiVG5sa2hrNlZpbW9VNmE1Q2sxVXpPL2czRWlRdGtJU2NMQlduUmpKOHdPMW9wNVpOVjVlc2M4WnNvTGpZTk5OZC9tWktOWnJ1TS81aXVWZU1obmVVc3lxd3pYWG5EMS9PQlFuallpRzlPaUh5TUtDVFRTeFAxMVQyVVBZaGZtVzdiei96ZnRzWmovSHZIY1ZXdVdoZm5wVDYxekQvQlFuNHZmQ1o0MkZTL1hybXNIdmNzU2J4Zk1oL0tMUk1DOHAzTlF4dzJiWHo0N2RkaXBOSDIvN3B6d2dUYmVtU0x6dkE1UHd5MG9ob2lzQXF2K3pza2hSUjZVRURCTy9GWnZUblFVbld2aG9DeDg5NjdVanBPbWE3dlJlQ3VpNUlmNXVrc1ptenAwK0RMV0RmVHo2cGloUXRGUE9sdHFQT1BpaDJmR25SczBMb1RLc2l6TlNRZnNpbGdVRGlWUm5ndmVKSHp1eFFyY0QiLCJtYWMiOiI5NTQzNmM2YTI0MzI1YTRjZDcwNmNhODBiMGY3YzNjYzgzOWQxMzhhN2M4ODNmMGZiMDZiYmViMTUwNDI2ZjgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iko4NnlRelJFeTVTU1ZZRTJvRzZDYkE9PSIsInZhbHVlIjoicGUrMGZETlRMYy81M0FKdkIvbE5reEdpTytjRlVvbFJmVlZ5M2FJa1ExeXB0WWhjZEhMQVpFZmt5b0xIQWRvVFBzZTJFYmNVNHp4OTR6Mk5VNHFML3hSRjhJWFhxQThjMzBzTFdnT3N1djZwNzdNVkJwL2xnM0t3em9ESDdWQ2JLSlRzNWQwNG9DQXliNFVKa1YzK0w2MmNKWWgzTFJxZVFKTmtSUTI4enJ1UXFzanNjTnlnQzE2WTdvb21ZQTZuTUdwU2swOHYvOWdicHFNS2VUeTZubVJSR0tXYmFGaHUzNzhucXhIbFgzcE42OWpqTGY2MWNDTjZGSXBjeEdzSlZQdFhMMWUySEExK1dVTFNMaFJ3bkFIN3JxYUJvbEJsN0pBZSt6TVBtRklwcHdiNzR1RlpNZGFzZnpHMjE2UnJvanpGcjIxd1doYU54OVkzUkNZQlVZblM2MEVERVVUa0I3UDAzazRKdEk2VkY5Z3ZZc3d3cjFiTnVTUEdydHhtTFRTL3NCUzBXcDhicGZOcS9BaWttM20zdXFvSXZjTTljdDJ3dDZGS2grclVMRyt2ejVLTHgzYTA0bjZZRUowazdWbE5wZGxpd25KSlVFNkRtaVI1WkgwRHdOZ0xjbEtYZ3lGK3NVWC9yTm5iRndtczIwUUYzNDhscFRMbHQxNmwiLCJtYWMiOiJhYjc0NjExMzgzZWYwMTE4MWIxMGEwOWRiOWJjZjcyMTE3ZDY4MGEzNTcxMzE5ZTE4NGMwZWE5ZmI5ZDk1ZmY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005116214\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-305270892 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVDelRzK1FOWnIzaXV1NW1CdDM5Znc9PSIsInZhbHVlIjoiaTZGQUJCSytJdlAyNWtOZVlnSEVzQ0RETEJpNzZhb1UyUjJ6UXhVWHRWNlJLSG9MTzBaNmJReVZWQTNZeFUzbVJiNHNFSFhqTS82dWF5UVBzS0tHME5rc3FkWnpyS1pMNkxrNk0xOEpWajczZmxjZlBpQ2VFeTltQTV5Q3h2aFY3alhNSmtnZHd2M2RSSDZGMW92ZjFRUUpUZ2NnckVFbjBEc0QyZzJGKzE4SHlXU0FOUGlhNFU3Zm1WTU9ITVVJcW9BN0pueWROWmlZb25XMHhLMGJDemVBVzhSZEFKNnJBbllmZHBIcVF5SnJhQlhidzMvSitDcTdwVzUxSEt2QVptSXZLQUt5Y0RseWRBWFhnNnZXSzF3aUdFYUl6Z2o4UWhPU3orYTR3dkZ3NGJsSFJ1K0VvODFnWnNadko5WGxRYUVDZE5PQ3Rodmcra0xnVEFLVkdNcVhXSGVkVkpUcmwyaGlhYVBiM3Z1ckdqUjVzU0xtNjlKOUJlS3J4eDV6SWdZbG9mQTRRMWcyMk1Lb3hYK3RxS3JKcm01Y1MwVnV6UGY5YURYdHJaR0tDbEM5KzQyTEcwM0ZINHoydFY1Snl5T21pVWxKblVFQ3hleExTUjBMUUZTOUVvYmEvUGREdjk3SkE2QUdEa2oxSnUrRXA4amlMU1BWeVNMOWVSWE8iLCJtYWMiOiIwNDM3MjJlNDdhYmU0NWFkOGRkNzUxYTVjYjUxOTVhMGNiODM0ZGUwYzExMDIwMjUxMjdmNGU3NzhmZmMwODhmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJUMExOWkNpZTF3MWdjQ09RN3ZYQXc9PSIsInZhbHVlIjoiS3JaRzZ3Z2V0M25UQWdxRmgvTTdlamFYY0srQ3ZGQUJ6YkUyTkxNU2FrYkYxOXE3cSs0Vm9seDlDQUI0OFFKeWcwSDlqdmc4T3pSbUxZUFUrcXFoR2lFYlYzbHJ3UXNURzBzY3VVczFoOU8rNjdkSElWMDR1S3JoOEp5dXU5ZEc3amdUNW8zcnhxTjJlaVNKUWwxQmtKZmJBRE9QRmc1RmM5TFgyNVM2dXlTUG9uaEE3cEFaeUZYVEZRc2UrWm8xUmFtMWZGeFprczJ0SUVSMy9CclJOK2lMbGNoR1JUSkpGMGtmVnE5SzJGb0NOS2hmZWlrRmV6NXZLNjh0Rys5Y3pjem9wNWNnVDU3V0E2amI4clVuQ0hiRVplS1NVeENmVTBITEI2dnpTZ2F5SlJObG1VOVprdHNDZEZGMzdXWHVGcjh2UURlVC9jcWQwZUphTExYZ1ZMUDFQalk0SWFEVlFOZGZXaFdGL0N0dnF2Q0hKL3hVTGZnMXVJVFIzNW5Hd1NSNUZLYTNDbVBuUDZuR1prdC9mUko5MXptYnBLQTduTkx4K3pIblhZcVhtbjB1Rkp2d1FzdmxVNXRmM24xNFJHVFROWUhPZ1RyVmRnMkwwMlRUMGtaTXVKUkpRM2hqR1JVYVBrNkU3ZFkyQitWR1JGdVV2ZHQ5N2wyaXlXOGciLCJtYWMiOiI3OGQxNGJkZTMxZmNlZjcwYzMwNTUxNzc5Mjc3NzJlMmM5ZjYzZWE0NzI0YTcyMTljNmQ2Y2FjNDM3Y2U3YTEzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVDelRzK1FOWnIzaXV1NW1CdDM5Znc9PSIsInZhbHVlIjoiaTZGQUJCSytJdlAyNWtOZVlnSEVzQ0RETEJpNzZhb1UyUjJ6UXhVWHRWNlJLSG9MTzBaNmJReVZWQTNZeFUzbVJiNHNFSFhqTS82dWF5UVBzS0tHME5rc3FkWnpyS1pMNkxrNk0xOEpWajczZmxjZlBpQ2VFeTltQTV5Q3h2aFY3alhNSmtnZHd2M2RSSDZGMW92ZjFRUUpUZ2NnckVFbjBEc0QyZzJGKzE4SHlXU0FOUGlhNFU3Zm1WTU9ITVVJcW9BN0pueWROWmlZb25XMHhLMGJDemVBVzhSZEFKNnJBbllmZHBIcVF5SnJhQlhidzMvSitDcTdwVzUxSEt2QVptSXZLQUt5Y0RseWRBWFhnNnZXSzF3aUdFYUl6Z2o4UWhPU3orYTR3dkZ3NGJsSFJ1K0VvODFnWnNadko5WGxRYUVDZE5PQ3Rodmcra0xnVEFLVkdNcVhXSGVkVkpUcmwyaGlhYVBiM3Z1ckdqUjVzU0xtNjlKOUJlS3J4eDV6SWdZbG9mQTRRMWcyMk1Lb3hYK3RxS3JKcm01Y1MwVnV6UGY5YURYdHJaR0tDbEM5KzQyTEcwM0ZINHoydFY1Snl5T21pVWxKblVFQ3hleExTUjBMUUZTOUVvYmEvUGREdjk3SkE2QUdEa2oxSnUrRXA4amlMU1BWeVNMOWVSWE8iLCJtYWMiOiIwNDM3MjJlNDdhYmU0NWFkOGRkNzUxYTVjYjUxOTVhMGNiODM0ZGUwYzExMDIwMjUxMjdmNGU3NzhmZmMwODhmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJUMExOWkNpZTF3MWdjQ09RN3ZYQXc9PSIsInZhbHVlIjoiS3JaRzZ3Z2V0M25UQWdxRmgvTTdlamFYY0srQ3ZGQUJ6YkUyTkxNU2FrYkYxOXE3cSs0Vm9seDlDQUI0OFFKeWcwSDlqdmc4T3pSbUxZUFUrcXFoR2lFYlYzbHJ3UXNURzBzY3VVczFoOU8rNjdkSElWMDR1S3JoOEp5dXU5ZEc3amdUNW8zcnhxTjJlaVNKUWwxQmtKZmJBRE9QRmc1RmM5TFgyNVM2dXlTUG9uaEE3cEFaeUZYVEZRc2UrWm8xUmFtMWZGeFprczJ0SUVSMy9CclJOK2lMbGNoR1JUSkpGMGtmVnE5SzJGb0NOS2hmZWlrRmV6NXZLNjh0Rys5Y3pjem9wNWNnVDU3V0E2amI4clVuQ0hiRVplS1NVeENmVTBITEI2dnpTZ2F5SlJObG1VOVprdHNDZEZGMzdXWHVGcjh2UURlVC9jcWQwZUphTExYZ1ZMUDFQalk0SWFEVlFOZGZXaFdGL0N0dnF2Q0hKL3hVTGZnMXVJVFIzNW5Hd1NSNUZLYTNDbVBuUDZuR1prdC9mUko5MXptYnBLQTduTkx4K3pIblhZcVhtbjB1Rkp2d1FzdmxVNXRmM24xNFJHVFROWUhPZ1RyVmRnMkwwMlRUMGtaTXVKUkpRM2hqR1JVYVBrNkU3ZFkyQitWR1JGdVV2ZHQ5N2wyaXlXOGciLCJtYWMiOiI3OGQxNGJkZTMxZmNlZjcwYzMwNTUxNzc5Mjc3NzJlMmM5ZjYzZWE0NzI0YTcyMTljNmQ2Y2FjNDM3Y2U3YTEzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305270892\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1131000737 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131000737\", {\"maxDepth\":0})</script>\n"}}