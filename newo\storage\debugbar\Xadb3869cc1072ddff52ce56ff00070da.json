{"__meta": {"id": "Xadb3869cc1072ddff52ce56ff00070da", "datetime": "2025-06-08 14:57:05", "utime": **********.369237, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394624.819581, "end": **********.369259, "duration": 0.5496780872344971, "duration_str": "550ms", "measures": [{"label": "Booting", "start": 1749394624.819581, "relative_start": 0, "end": **********.288725, "relative_end": **********.288725, "duration": 0.4691438674926758, "duration_str": "469ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.288737, "relative_start": 0.46915602684020996, "end": **********.369262, "relative_end": 2.86102294921875e-06, "duration": 0.08052492141723633, "duration_str": "80.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45185160, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017669999999999998, "accumulated_duration_str": "17.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.326859, "duration": 0.016489999999999998, "duration_str": "16.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.322}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3548288, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.322, "width_percent": 3.452}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.359518, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 96.774, "width_percent": 3.226}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1623994848 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1623994848\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-365149867 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365149867\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-536737340 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-536737340\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-428607100 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394379618%7C66%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldJdzlzdEtVdUxlKzlNMFBMYkY5cUE9PSIsInZhbHVlIjoiamdUWVpxenRkQlJPc1YxOVJad2lpUm5mM2YwbzIyUVZzWC95VWk0dUdsQk5PRlVvd0gxMjFxWlFSSm5HcDhQOEVWdjR5UExhNCtoOW8zZ1RiNVc5QmZ0endFZVNobWxIQ1R5UGszVHhjMG9QUVRsKzR0N0kyVk9HUG45cHBJOUZ5ZjZyamNhSWszY09aNHNUK0ZrOTRZeGVWSTVZeWwveXdlTTZzUFZhNFhFd3Y4NDVLSHV3cDQ1UHVpeFRoZHJDSnlML2Fna2lPY2lmYUhLUlk3TU9mQkpybTkvZ0MrMUFSUmZKTTR3TEk2ZzFMdkVubFh1TmpPWGNtbUdvMlZuMHQwdDFYbFp5dTQ5UGx2cU5tK2tyVGZDOENEcWVKODJreVZJU1IvZDU5b3FBejAyZ2piWmhTR01IWXpjV3cxcUZUNnZSOFNEbXRNRExwS3dHM2U0amxWeXVtODVwcE1oQ3BmOCt4UkxOa1EwWTlMazdNWjVDTjFMVDFCQThFdVFxaTRFQkdRU0NhUGFQZ3lQVk56dVVoZ2ZxWkczeC9GTG9ySG1OM2FFM1FxNVcrV0lVS2pNVjN5KzNsZGdSUkxPc0NCRGYwbWlIaUdUTGFhMkhCMTlnbWpRRE9ucDNvekYwMkdpSTZ6bkVoczltNTFrbExYSDJNZ1kyMVU5MndmUXEiLCJtYWMiOiJkYzc5YTUzMTIxYjkxNWM0MWM3MzExYTUwMWVmOGIzNzY2YzQzOWY2YmExZGY5Y2JkOWYyMDg1OTEzYjg3MDJlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldpK3FqdWVGZStmS1UxYkNrbFphMkE9PSIsInZhbHVlIjoiMXFaTUpod1FMYUF0bjlTb2VLR01kVGlVMERVR3p6NjhFVzUyalNjWVRKalI3Nitad29rU1poV0kzM2tleldkODQvTXhzZmFUZmtnVWkwYWZiWW42TEFpb01WeWgrd0dYcUU1OFFaa0ppcUJkODF3YW0yalE1SW5MMzVXcGc2dk1DUnlURjVRdjVrc09oRG83TEU0TFVkcEF2b3ZNNDhxSisyeE1LT04xMUZjODhFWEtIZSs4L1JIeUg3Unl2UFZVc0g4R0JqVUUvOGRzcFdxM2tZdzJVSTFMNWlXNUFJVk9vOHBhdHBXc01uNkdUcW5tYmoxUnNReVNPN1JxSGZxZERRczliUTRUUGdVS1hOWlpTd1VkdmNSY1BlTnhhVU1pWWh6SzJKS1JQMXI3eHQ4MDFXbU9tTmdvQnFkQUxleVBaS3BKdkVlVzhyeGV1TzhlUmRBdi90aW5FQkhVTGxhMERwTm9FMkYva1gxT2FQdVhKMWpaWUpwVWZXUkVmSnNrSENFKzdJeFhETEQ3VGpxWENzUHhjeVBRRG1xaGppQXlRcElkUWk4Z1ZCS1lPVWRLV01xTHU2TXF0YnNSUmZwbGVMcUdYcWoyUnF1TzgwbzRlUkRPRXF2Wm95YlNHUDhRVjNNdzlWWU1iS3paSnF6amlsWTlmc244aGVQVEcrT1YiLCJtYWMiOiI0NGM0YWI3Y2QxMWE0MTgzNzQ2YmQ0MDg0YmFiMTRlMmNmMDc1ZmIzMDY3ODk5OTFlZmZiNGY3N2MxMzc3OTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428607100\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2042609358 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042609358\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1790344674 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtmaFlFQTc1MmtTam12VmxZZjk5ZFE9PSIsInZhbHVlIjoiSnExK0JrcWJDMDJsMnQ0SlN3R0VHdFJRK2pEV1k5MlM4TGZYeUdUOVVUem9NVUpSZVN4R1BzUzFIVXVUUitjMGZQZHh5TE9zQWNrc29IMHRzZHI3NmdkMUpVWnBGRGUyL1ppN3U0S2wrcCswRytyVEtGTVBaZXRWeG9FTnRXRnpiTmdHZ1JFVUdaRHk4dGdMdjl6MmlBOGlvQ2dGNThyTW1idnpENytxd2hPM2M1a0FLbDVQVndnYUlhUmVoVUxTT2hFR1NqRk55TlRSa3VhSFhSRUFMSlh5MzJwQ2IrMVU0ellCRXVZcU1UY3h0aXZsSlAySVd0cG1Xa0p3NDJ2dlFrRzRRbEM5TXF4dGN6QVJiRGNSRTlRUmwyeUZ1dFYxU0V4aXY0V240aEZSKzlSd0hrSXdPelFTajB0WElBWWpjYzhWTHJqWGtERzdXT2JPd3h4QUMxQjBROXpRK0hpdHhSVzZsNDdaSVFsUStmQjlGVzFHVzQvcG1JbExtM0VwR05nTmd4bU1BYVM4ZmZKMEY5MG5waGVCYjRqbFBRZFdBc01kdzhzNng5K1hNQ3R2VnZzYzRFSFpEakRBU2lFVFRSNytJNUFQTWwybVRHWGFaTzV4WGhJZ3pIb3N0aE1URGZLRWd3QnoyMTA4ZDN3TksvZUM0bTVXZGVyZ1R4dDEiLCJtYWMiOiI5MmE0OTA0MzEyN2IzODkxNTE2ZTMzYzVjOGE0Y2RiODg1YTU1MWY3ZjBiZWMxZDdkNjYwODJkZTczNWYzNjM5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRJMm85OTdjckVnN01DeWFjRHR2blE9PSIsInZhbHVlIjoiWWliNG9MMjg2aVJJL2NPSkt0Sk1EUER6T0Y0dDR4NjVHWUNSNlE3eGhPUktzK2sreTZ1Sms4MDlpdzdMOE5qNk5aTE40RlNhczJYRXVkOUlWVm1FOXhBQ2UyTW1ORjI3Y1VsNHFaaGFFaTVKY2Yxbnh0Y0hPVFQ0YjNNMG1aM0ZXbzlkTUM1OThSR0VObkNsTmQxK2g1SVBIZTREZ3BvSklsOFB1RWc1djlBbVNGZWgzejVUdXlJcmZaUENJc0plbVRVVEpaMXphWkRwUHpNREhEOVRGcmJleDZnTTBvKzRNM01yT3gxb3RYTEJNb3JJMXRIUkJuVjFrQnErcit2OFlibnNiRHBzWFlNMDNaMTJPK1RXQjJOSis4d1Z1MDk5M0JCN2RkOGhOSHBDT1o1Y2RrdkxBc3VHbVp2ek4rWHMzK05KT2ZVKzF5dzh0OXlBa1hXanRISlFSZjZaNjgxaW4rdklGL1FQVDZrNFBHZFZvd1U5bUFwTGxqUytlaE1GdlhrY3NZUXV2V1IvRGVsVVZxeTVNVlJEVkx3dGl3YzhWUVhiSEVabWhCNTg1anJreWEvNDkreW5SM21YNTRGdUpVeE5ubHdqMzlGMEN0NWFQV3JVeS9xWHFvbDRISHpLYjF2MGx4dE1SZHlZKzZVRmlGQlRYMFZmRXRBOFBZWEIiLCJtYWMiOiJiM2VkNDMzMjk4NjYwNDJmYjRjNGFiMWY3MmY4ZjUyMjBmOGI4NWQ2MDBiNDVmMzQ2OTAxMGI3ZDAxYzdiNDk4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtmaFlFQTc1MmtTam12VmxZZjk5ZFE9PSIsInZhbHVlIjoiSnExK0JrcWJDMDJsMnQ0SlN3R0VHdFJRK2pEV1k5MlM4TGZYeUdUOVVUem9NVUpSZVN4R1BzUzFIVXVUUitjMGZQZHh5TE9zQWNrc29IMHRzZHI3NmdkMUpVWnBGRGUyL1ppN3U0S2wrcCswRytyVEtGTVBaZXRWeG9FTnRXRnpiTmdHZ1JFVUdaRHk4dGdMdjl6MmlBOGlvQ2dGNThyTW1idnpENytxd2hPM2M1a0FLbDVQVndnYUlhUmVoVUxTT2hFR1NqRk55TlRSa3VhSFhSRUFMSlh5MzJwQ2IrMVU0ellCRXVZcU1UY3h0aXZsSlAySVd0cG1Xa0p3NDJ2dlFrRzRRbEM5TXF4dGN6QVJiRGNSRTlRUmwyeUZ1dFYxU0V4aXY0V240aEZSKzlSd0hrSXdPelFTajB0WElBWWpjYzhWTHJqWGtERzdXT2JPd3h4QUMxQjBROXpRK0hpdHhSVzZsNDdaSVFsUStmQjlGVzFHVzQvcG1JbExtM0VwR05nTmd4bU1BYVM4ZmZKMEY5MG5waGVCYjRqbFBRZFdBc01kdzhzNng5K1hNQ3R2VnZzYzRFSFpEakRBU2lFVFRSNytJNUFQTWwybVRHWGFaTzV4WGhJZ3pIb3N0aE1URGZLRWd3QnoyMTA4ZDN3TksvZUM0bTVXZGVyZ1R4dDEiLCJtYWMiOiI5MmE0OTA0MzEyN2IzODkxNTE2ZTMzYzVjOGE0Y2RiODg1YTU1MWY3ZjBiZWMxZDdkNjYwODJkZTczNWYzNjM5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRJMm85OTdjckVnN01DeWFjRHR2blE9PSIsInZhbHVlIjoiWWliNG9MMjg2aVJJL2NPSkt0Sk1EUER6T0Y0dDR4NjVHWUNSNlE3eGhPUktzK2sreTZ1Sms4MDlpdzdMOE5qNk5aTE40RlNhczJYRXVkOUlWVm1FOXhBQ2UyTW1ORjI3Y1VsNHFaaGFFaTVKY2Yxbnh0Y0hPVFQ0YjNNMG1aM0ZXbzlkTUM1OThSR0VObkNsTmQxK2g1SVBIZTREZ3BvSklsOFB1RWc1djlBbVNGZWgzejVUdXlJcmZaUENJc0plbVRVVEpaMXphWkRwUHpNREhEOVRGcmJleDZnTTBvKzRNM01yT3gxb3RYTEJNb3JJMXRIUkJuVjFrQnErcit2OFlibnNiRHBzWFlNMDNaMTJPK1RXQjJOSis4d1Z1MDk5M0JCN2RkOGhOSHBDT1o1Y2RrdkxBc3VHbVp2ek4rWHMzK05KT2ZVKzF5dzh0OXlBa1hXanRISlFSZjZaNjgxaW4rdklGL1FQVDZrNFBHZFZvd1U5bUFwTGxqUytlaE1GdlhrY3NZUXV2V1IvRGVsVVZxeTVNVlJEVkx3dGl3YzhWUVhiSEVabWhCNTg1anJreWEvNDkreW5SM21YNTRGdUpVeE5ubHdqMzlGMEN0NWFQV3JVeS9xWHFvbDRISHpLYjF2MGx4dE1SZHlZKzZVRmlGQlRYMFZmRXRBOFBZWEIiLCJtYWMiOiJiM2VkNDMzMjk4NjYwNDJmYjRjNGFiMWY3MmY4ZjUyMjBmOGI4NWQ2MDBiNDVmMzQ2OTAxMGI3ZDAxYzdiNDk4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790344674\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1269951585 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269951585\", {\"maxDepth\":0})</script>\n"}}