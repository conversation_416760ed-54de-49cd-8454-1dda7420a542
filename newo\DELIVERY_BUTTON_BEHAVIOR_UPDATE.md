# تحديث سلوك زر التوصيل - إخفاء زر الدفع وإظهار زر التوصيل

## 🎯 **التحديث المطلوب:**
بناءً على طلب المستخدم، تم تعديل السلوك ليكون:
- **عند اختيار عميل لديه صلاحية التوصيل:** يختفي زر "دفع" ويظهر زر "توصيل طلب" فقط
- **عند اختيار عميل عادي أو عدم اختيار عميل:** يظهر زر "دفع" ويختفي زر "توصيل طلب"

## ✅ **التحديثات المنجزة:**

### 1. **تعديل منطق إظهار/إخفاء الأزرار:**

#### 🔄 **للعملاء ذوي صلاحية التوصيل:**
```javascript
if (data.is_delivery && (userCanDelivery || userIsCashier)) {
    // إخفاء زر الدفع
    $('#payment').hide();
    
    // إظهار زر التوصيل
    if ($('#delivery-order-btn').length === 0) {
        $('#btn-pur').append(
            '<button type="button" id="delivery-order-btn" class="btn btn-warning btn-lg btn-primary" style="min-width: 150px;">' +
                '<i class="fas fa-truck me-2"></i>توصيل طلب' +
            '</button>'
        );
    } else {
        $('#delivery-order-btn').show();
    }
}
```

#### 🔄 **للعملاء العاديين:**
```javascript
else {
    // إظهار زر الدفع
    $('#payment').show();
    $('#payment').attr({
        'data-ajax-popup': 'true',
        'data-size': 'xl',
        'data-align': 'centered',
        'data-url': billTypeUrl,
        'data-title': 'POS Invoice'
    });
    
    // إخفاء زر التوصيل
    $('#delivery-order-btn').hide();
}
```

### 2. **تحديث التنبيه:**
```javascript
// تنبيه محدث للعملاء ذوي صلاحية التوصيل
'<div id="delivery-notice" class="alert alert-warning mt-2">' +
    '<i class="fas fa-truck me-2"></i>' +
    'هذا العميل لديه صلاحية التوصيل - سيتم حفظ الطلب للتوصيل' +
'</div>'
```

### 3. **إخفاء زر التوصيل في البداية:**
```javascript
$(function () {
    getProductCategories();
    checkPaymentButtonStatus();
    
    // إخفاء زر التوصيل في البداية
    $('#delivery-order-btn').hide();
});
```

### 4. **معالجة حالة عدم اختيار عميل:**
```javascript
} else {
    // لا يوجد عميل محدد - إظهار زر الدفع وإخفاء زر التوصيل
    $('#payment').show();
    $('#payment').attr({
        'data-ajax-popup': 'true',
        'data-size': 'xl', 
        'data-align': 'centered',
        'data-url': billTypeUrl,
        'data-title': 'POS Invoice'
    });
    
    // إخفاء زر التوصيل والتنبيه
    $('#delivery-order-btn').hide();
    $('#delivery-notice').remove();
}
```

## 🎨 **تصميم زر التوصيل:**
```html
<button type="button" id="delivery-order-btn" class="btn btn-warning btn-lg btn-primary" style="min-width: 150px;">
    <i class="fas fa-truck me-2"></i>توصيل طلب
</button>
```

**خصائص التصميم:**
- ✅ لون أصفر تحذيري (`btn-warning`) مع إضافة (`btn-primary`)
- ✅ حجم كبير (`btn-lg`)
- ✅ عرض ثابت (`min-width: 150px`)
- ✅ أيقونة شاحنة (`fas fa-truck`)

## 🔄 **السلوك الجديد:**

### 📋 **سيناريو 1: عميل لديه صلاحية التوصيل**
1. المستخدم يختار عميل لديه `is_delivery = 1`
2. **يختفي زر "دفع"** تماماً
3. **يظهر زر "توصيل طلب"** مكانه
4. يظهر تنبيه أصفر: "هذا العميل لديه صلاحية التوصيل"

### 📋 **سيناريو 2: عميل عادي**
1. المستخدم يختار عميل عادي `is_delivery = 0`
2. **يظهر زر "دفع"** العادي
3. **يختفي زر "توصيل طلب"** تماماً
4. لا يظهر أي تنبيه

### 📋 **سيناريو 3: عدم اختيار عميل**
1. لا يوجد عميل محدد
2. **يظهر زر "دفع"** العادي
3. **يختفي زر "توصيل طلب"** تماماً
4. لا يظهر أي تنبيه

## 🧪 **اختبار السلوك الجديد:**

### ✅ **خطوات الاختبار:**
1. **افتح شاشة POS**
2. **تأكد من عدم ظهور زر التوصيل في البداية**
3. **اختر عميل لديه صلاحية التوصيل:**
   - ✅ يجب أن يختفي زر "دفع"
   - ✅ يجب أن يظهر زر "توصيل طلب" أصفر اللون
   - ✅ يجب أن يظهر تنبيه أصفر
4. **اختر عميل عادي:**
   - ✅ يجب أن يظهر زر "دفع"
   - ✅ يجب أن يختفي زر "توصيل طلب"
   - ✅ يجب أن يختفي التنبيه
5. **امسح اختيار العميل:**
   - ✅ يجب أن يظهر زر "دفع"
   - ✅ يجب أن يختفي زر "توصيل طلب"

### 🔍 **نقاط الفحص:**
- ✅ لا يظهر زر التوصيل والدفع معاً في نفس الوقت
- ✅ زر التوصيل يظهر فقط للعملاء ذوي صلاحية التوصيل
- ✅ زر الدفع يظهر للعملاء العاديين وعند عدم اختيار عميل
- ✅ التنبيه يظهر فقط مع زر التوصيل
- ✅ التصميم والألوان صحيحة

## 📱 **الواجهة النهائية:**

### 🟡 **مع عميل توصيل:**
```
[مستودع] [عميل توصيل] 
⚠️ هذا العميل لديه صلاحية التوصيل - سيتم حفظ الطلب للتوصيل

الإجمالي: 22.00

[🚛 توصيل طلب] [👤 عميل جديد] [🗑️ إفراغ السلة]
```

### 🔵 **مع عميل عادي:**
```
[مستودع] [عميل عادي]

الإجمالي: 22.00

[💳 دفع] [👤 عميل جديد] [🗑️ إفراغ السلة]
```

## 🎯 **النتيجة:**
الآن السلوك يطابق تماماً ما طلبه المستخدم:
- **زر واحد فقط يظهر في كل مرة**
- **زر التوصيل للعملاء ذوي صلاحية التوصيل**
- **زر الدفع للعملاء العاديين**
- **تصميم واضح ومميز لكل زر**

النظام جاهز للاستخدام! 🚀
