{"__meta": {"id": "Xed2a6398c54694112551c393c05af383", "datetime": "2025-06-08 13:19:53", "utime": **********.298779, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388791.996965, "end": **********.298811, "duration": 1.3018460273742676, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1749388791.996965, "relative_start": 0, "end": **********.136659, "relative_end": **********.136659, "duration": 1.1396939754486084, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.136686, "relative_start": 1.139721155166626, "end": **********.298814, "relative_end": 3.0994415283203125e-06, "duration": 0.16212797164916992, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43927472, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.03229, "accumulated_duration_str": "32.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.237776, "duration": 0.03111, "duration_str": "31.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.346}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.279205, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 96.346, "width_percent": 3.654}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-375947538 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-375947538\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-605291754 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605291754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1784372001 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1784372001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZDTGo3dzZGUWJNb0E0NzhrSWRtZXc9PSIsInZhbHVlIjoiR2tQTFpzQ2s3NVdjOEdmYU53b2F5dnhJaDBlcDBMQ0Z6WTE4ZVZTMktsZFNVZlAxR0FVQ0ZhcWRsSjhSYVFrY1pFTjdFeVV0bDFJa1BOSjRRb1BETDJ2MXQxclBrbWcwK2tTT0FhSXI5MnlnUEhxOFZBMlBZN2U5TEUwY1U2ODB5SUhLQUxxNTgycWtVK1N0N3NKQnByRXoyUXhCaWM4Rkx5L0FSOU5wSHRxQnBKTC9Jc2FlNHAySHo2Qkc0TjA0WjdoNmEwaENDemVsWVMwb0pSaU1seVR6bXFMTXdWdlpJQjhvYjVQTUNQMXpFR3N4RVJnQlBuVmszSm1PY2NWQW56N2RaREhpNVRQSHdoYVVEakVlMGFOUnR6Q0FQOUQ4Ykw4SHZqa2JqT25EQVNXNU1lWUdrOGw2UDJHUHpyazZvWklpMkNsTDRDYXNtYlpmSW1VOXZtZkVUa1NRZFlaRWF0TFQ2ckJZYzI5WURpaVRmcmVTZks4Y3pJWDgyNDBsa1lmWWpyRDNmTzh5STZ0bHUwWUNVOC9QeU9Oc3hoTjJSYlNKeXl2Uk5NMmlncUJsa0hVSXozVUowTnZJdlU3K3loMzFNZG5WTnM3K0xuRlREYjdZY01rbDVYblM3dFVIVEtuWmFKN0pWS2RzZTNBNWltN1FmSjJORTVrYlFJdzYiLCJtYWMiOiJjOTFlYTkyZDhlYTYyM2Q5OTI5OGRmNjljMzk4YjI1NGY4NmJjMzI1Zjc2ZjcxMTQ5NjFmNTk1NTQ2ZGY2ZGU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFXYldsckY2VXVlUytlT0syWkRaaWc9PSIsInZhbHVlIjoiMm4waFBLbzRZaU5Cdmt5TSs0OFdVVE1VNWFwMS9lTFZYdDNzdTV3dkR4ZGJXbmozcGRBU1NqNXd2NEtqOTNxaVZOcWJhUk1HR0NETWh5aVNIcGEzUURodGY5Q3FTdGtVUnpiOXdKRUFhNWszZzFjUVp1TnlGcGVmQ1NlNGc5NnBYemRBeVNDOU5nMTR1a0pYWDJBaUJKTzkwQ2x1WDJLRFZVS1l0TWI0amxpb1VEb3lNOU5HR1FZQmRhb1NnUWxuRTU4VE5aTURpU3JZQUh3WUovN1JSa044S09KNnJ1RkkwSHdCK2x1dnBYdVVhcHZLeVZyUDBrSi9QaTBhS3hIelN0TlozS2h0dnhUMHYwQk13M1E0eWdabGYzcjZybVJMREM5a2dLUnlydHV4cDNlVjhIUURQVmprZnBwV21wZkdRQWFpbElMbTdqbzRETW9UaGFOYVZWdU5DVjkzSGhaUmJ2THBNclRrS1FQdVI1Q0gyZmFNWmpLMVllYUtpVTF6em5KZlBoaXBaTklXcWFSZnRvbUhKeFQvUmlnUHk2Q2EwcjJiUTJVa3ZiUXpZSXU4ODVaaHJwc2p2MGppS3FoQ3EyRm5MS216aFZpaUJmVGw5cDJ2ekMvL3Z6MitCUjdNSW9kQjltYlBGNUZDNy8yakxqRytLOGN2aTliUFRrOFgiLCJtYWMiOiI1MTRhNjlkYTc3ZmFmNTAxNDljYzQwMTJiOWE2MGVjMzI4NzliZTk1OTRmMzIzODkyZTc1ZDI1YjAwYzdkYTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-35159182 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklnZXkvWi8wdm5IYnEvT0ZiazNsVGc9PSIsInZhbHVlIjoiNnVacTQ4elV3WEZqNmg4T2FhN2RBVlZkU25jRll6TnJUWDd6S0xOYXZjbENXTXlGVnJzVVlJaWVvQmlTK29EcTJIVUNNU3ZLWmxjT0FYbC9BYWlmTERJejhPTXhhOVhZMjdIYTlzS1BBcVgzNHlXUWEvODNhNkVCbUo0dk1HeWxqK25sUDlwZFMrbTdnUGF4K2d0VkZ3bXh1ZWYxcm5kK2I0allYWU9abVN6bjBaQ05VaUkxQXhtK3J2TUg2bUhlWXl0WnExa3QvV01kVW13UHd0M2dSZTdMaXdzNHJvTkZDOHdYblNXQVdRQUMza2xGUXZJb3pGbnVTd3JtK3dpT3NYVWkzamNKQkd2SE92MzEyckNqSklDeGFXVkcwdC92Nmp6VGxlMDVSMitQbDNoL2E4MUswOHE0bWVCblduZW0yWGNlMDJHRlFRUXp5akZBVEFYdldVN2FMVGluRk95Zlp5dUFFSG1mWHNkWCtLRE1xbWNyQ2JyangwTXQ4clQ3Z2Ivd3NZT1pISTRjSWtYamhzVllOSWRnMkg5ZVJEYWtOaythZ3ozcU15TlBaZXdZZTJVVEdOeEZycEhUMktXMlJaT3M1akxUdVZpQS9CcFdkNVR6Z1lHR3EvNXJUZHp6YlpndE41ZmhoVDZ2Nys1VlBBTXhJUU1LZkhWK054M0YiLCJtYWMiOiIxM2ZlYjNlMDQ4YWMxNDE5NTFiNGRhNGZhMThmM2I1ODM0NTUxZGMyNDMxNTk5OGIyZGI0M2IyYjYyYjkxYmZiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZ2eVZpbjhYVVRaTjI1cTBPbHlXTEE9PSIsInZhbHVlIjoiUFFWdFdzNFlldTBxSEswUDhVYk5mUGNhTHMwY0lJL0FIU3JYMThEWEQ3czJFV1FVRklUL2J6TjJpaHRaL3cxbnZZUjlwNHZPYW1BdEhMY2hIRHRHVWN5TjZCL1pyRU5hVHBVcGV3QUxGRkhKU3VqTEpGT2ZhTFhrVFVOeFFwVGdmMFFRejZYanhuaTJSU3RZU2Zla01XNDJ1SUlHUURUNlFSaDNhMWRsK3JxaEY2VVE3ckxuQUZ6a1NrL3R4dFdNMXNXNTMyUXZPbGlBTUQ2T3BhMVFKcGlBM1I0YkZwQnprYkt3eTJyM2tEbXdSbHlDM0UwY0JkM1EzellXcFUvZ1ZTVVNoSk5TNEVRMXdUSk16QWEvanZuWnVNWDJ4TWl1eDl3cGJpaG9HYkRVd3lndVBuSEFXbWhMNTVXbG9DdlA0NGp3Zml6NklYN09WUDA1MUlxeFBQYWQwT3FscDN6RUVocU41RmNLNG0wcnZYbTVIR2dYTmNXbGZlT0FhYXhxOUVBcXdzcFpvd05TbVJ6Rmpmb3IycWtlNU8yQXIxVitwYVdHc2dndzJQbWNXa0dqYlgrUHFnNEdyRllsR1ZyMWJFN25rMWZjblBsS2VPb0QzL3E5YStOamJQQkkwNEZZMzlIR3g3WXBqM2RZQ1orb0J4dTlIYkdLenRDb1dlKzgiLCJtYWMiOiJkY2I0NjI4ODc3YzU5NTIyNWZmZGU3NDQ1ZjU4N2RjODUwNmM5NjFmY2QyOWJhNjczNmFmYmZmZjMzMzc4Yzk5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklnZXkvWi8wdm5IYnEvT0ZiazNsVGc9PSIsInZhbHVlIjoiNnVacTQ4elV3WEZqNmg4T2FhN2RBVlZkU25jRll6TnJUWDd6S0xOYXZjbENXTXlGVnJzVVlJaWVvQmlTK29EcTJIVUNNU3ZLWmxjT0FYbC9BYWlmTERJejhPTXhhOVhZMjdIYTlzS1BBcVgzNHlXUWEvODNhNkVCbUo0dk1HeWxqK25sUDlwZFMrbTdnUGF4K2d0VkZ3bXh1ZWYxcm5kK2I0allYWU9abVN6bjBaQ05VaUkxQXhtK3J2TUg2bUhlWXl0WnExa3QvV01kVW13UHd0M2dSZTdMaXdzNHJvTkZDOHdYblNXQVdRQUMza2xGUXZJb3pGbnVTd3JtK3dpT3NYVWkzamNKQkd2SE92MzEyckNqSklDeGFXVkcwdC92Nmp6VGxlMDVSMitQbDNoL2E4MUswOHE0bWVCblduZW0yWGNlMDJHRlFRUXp5akZBVEFYdldVN2FMVGluRk95Zlp5dUFFSG1mWHNkWCtLRE1xbWNyQ2JyangwTXQ4clQ3Z2Ivd3NZT1pISTRjSWtYamhzVllOSWRnMkg5ZVJEYWtOaythZ3ozcU15TlBaZXdZZTJVVEdOeEZycEhUMktXMlJaT3M1akxUdVZpQS9CcFdkNVR6Z1lHR3EvNXJUZHp6YlpndE41ZmhoVDZ2Nys1VlBBTXhJUU1LZkhWK054M0YiLCJtYWMiOiIxM2ZlYjNlMDQ4YWMxNDE5NTFiNGRhNGZhMThmM2I1ODM0NTUxZGMyNDMxNTk5OGIyZGI0M2IyYjYyYjkxYmZiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZ2eVZpbjhYVVRaTjI1cTBPbHlXTEE9PSIsInZhbHVlIjoiUFFWdFdzNFlldTBxSEswUDhVYk5mUGNhTHMwY0lJL0FIU3JYMThEWEQ3czJFV1FVRklUL2J6TjJpaHRaL3cxbnZZUjlwNHZPYW1BdEhMY2hIRHRHVWN5TjZCL1pyRU5hVHBVcGV3QUxGRkhKU3VqTEpGT2ZhTFhrVFVOeFFwVGdmMFFRejZYanhuaTJSU3RZU2Zla01XNDJ1SUlHUURUNlFSaDNhMWRsK3JxaEY2VVE3ckxuQUZ6a1NrL3R4dFdNMXNXNTMyUXZPbGlBTUQ2T3BhMVFKcGlBM1I0YkZwQnprYkt3eTJyM2tEbXdSbHlDM0UwY0JkM1EzellXcFUvZ1ZTVVNoSk5TNEVRMXdUSk16QWEvanZuWnVNWDJ4TWl1eDl3cGJpaG9HYkRVd3lndVBuSEFXbWhMNTVXbG9DdlA0NGp3Zml6NklYN09WUDA1MUlxeFBQYWQwT3FscDN6RUVocU41RmNLNG0wcnZYbTVIR2dYTmNXbGZlT0FhYXhxOUVBcXdzcFpvd05TbVJ6Rmpmb3IycWtlNU8yQXIxVitwYVdHc2dndzJQbWNXa0dqYlgrUHFnNEdyRllsR1ZyMWJFN25rMWZjblBsS2VPb0QzL3E5YStOamJQQkkwNEZZMzlIR3g3WXBqM2RZQ1orb0J4dTlIYkdLenRDb1dlKzgiLCJtYWMiOiJkY2I0NjI4ODc3YzU5NTIyNWZmZGU3NDQ1ZjU4N2RjODUwNmM5NjFmY2QyOWJhNjczNmFmYmZmZjMzMzc4Yzk5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35159182\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1323566999 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323566999\", {\"maxDepth\":0})</script>\n"}}