{"__meta": {"id": "Xe7b4d80a843fb18c095d88b65130359e", "datetime": "2025-06-08 13:02:35", "utime": **********.46994, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387754.098141, "end": **********.469989, "duration": 1.3718481063842773, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749387754.098141, "relative_start": 0, "end": **********.286703, "relative_end": **********.286703, "duration": 1.1885621547698975, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.286723, "relative_start": 1.188581943511963, "end": **********.469994, "relative_end": 5.0067901611328125e-06, "duration": 0.18327116966247559, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45594712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02042, "accumulated_duration_str": "20.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3631668, "duration": 0.01728, "duration_str": "17.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.623}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.410443, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.623, "width_percent": 6.709}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.435548, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.332, "width_percent": 8.668}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1573341020 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1573341020\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-278608455 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-278608455\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-645708736 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645708736\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-156626319 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387735347%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9Xb2RKV2pDN2JKVVNycVlVSVhlQmc9PSIsInZhbHVlIjoiOStXU0JoTno3aXZ1TjNES2RMcDdqRlJCUHVic2V1YlVMY2FmSzVNa0ErK2NFaHFVUHp2dnR1cFROamFTVEQzRVQ2WlRpMHhBcWhaY0JRblVHWldDbTdVNHFmYnhHaVY2MXZORXlxK2xPVXhVUVhuUTJRSUpIMHR1dkFQc3cyOU13cTEwNFlUcEF2YjRoOHBwalVhT1hRVUk1bTl0L1k5cjVBRU1kSXYxQVFzZ0dtZlVXRzNTS2M3V2Z3eWZMM0JZMXhVMUlDdFRSZE5ibkNha3c3emlkSU0ycGptUU8zRFc0dkU3d293S1JWbDNiSkFxOWY2dnB2WjlqT3J4SU1BcEYvdVFuQno3NnRLclgyY2pkSDIxZnNtbnN6S2lDRUVDUE9jQkg2N0tQMDBtZXhETDFRY0FaWDIrNE5RTzBoR0ZFOU1JK2pubGIvYVZEa0hYOEYySUNOTHZ5cWc4UmkzdU5QQnlIVndiRjM3eElNZWNqbHY5dTI3bjdKTDZlS2dyb3c4Q3B3Vytob2RjR3hjNFZNamgxKzEySDkxUmttRlc1eE5JTDROdHMyUU10ZC81MTBjQ3c4bUE2VG01NkZ3Wmp3NXAwY0JSSWljYm1LZnA5TDAyS09HbkRLa0VHNDRyREJSWnU2ZFlPWlBuM2lwbFRacWxBakxkSjdCRDlyMlkiLCJtYWMiOiIwM2YzYzlmODM0YWQwZGNlN2QyNGMwYTZiOTI2ZTNiNjNhOGEwNmEzM2QzNzUyMjRiNTc4ZWUxNjE5ZDNmMjk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkY3S2phUlAwV0ZCNVFVM3JKekFSUVE9PSIsInZhbHVlIjoicE1QZjFSaVJrNTFWZm10Z2xRRCt0WG40N2creHVBcVJiM0V1OWQ5bjdEWnVMMHpmMUlGUWZTZUlEcGZjTnNzeWszVVNiSXhtVXJPeTR1SHlIeDYydGpjVXkzNS9XMkZFaTQ2em9sMjJCWXlxTDVzWUMybDBlR0hlY05HMjR6T3AralNyT2ROU2RTdWRESUtGZUI3eTczTjdpRlJ3VGNNRERJOGVGalgzVFJ1eUNnRnNHMWtHYXJBeGlUZ0V3cnRoYXVWY2k5SnJaM2RGTWl0SzhnWUgzblM2R1lCZjRzQStNbnRPZThaaGZhc2dJcjdFUCtheVB1YnQzZjhKazc3ZTdzaVF4amkxM2V2Y2NIdVppTXB4bXhnSG00MngzcWJWSmw4VFovQ2JpL0dOb3lxZGxIa0hDN25XZFE0SlY3dDcxcjJqZkR5OUNXTThTM2dKUlo5Q1lDY2NMeDJDL0R5Mm5ybHNvU3pwSDdaZ1FOeEd1Ujc1TGdzVk5WdDdJQ2VJT2RVWlRNdDF1OE0yam5tWmFLWGMyWHhwdFBnaC9xU0VvcDBTZ3BrUnJHQTJHTGUyWHhYRmgxaUR0eUFzY1ExMVE4LzA3Z1o1azQ2akRKYWVFM0NzMzlicDNYd1pJUGdOUEEzam5ZVEVEakVkRVJhQTlrSU1zSGZrSlZMVlNyKzkiLCJtYWMiOiIzNTExYWY5MTY1NWQwNGIwMTZkNGJjMDIwMjZlMTI0MDJhZjE1Y2VhODYxYWEwYmYzOTk2NTMyZTZhODI5OTZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156626319\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1116279071 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116279071\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1229017387 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:02:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBYemZPMTdFYldSZkNrVTdYYXJ6Z1E9PSIsInZhbHVlIjoiWnFOT0NIOGlaYlhlOUNZaXQrRkZSeDZKbG5aUG5WVy90RS9xWE5yaWw1T1UzYnlVeVo5cjJHR0hZeUVJdHZ4NisyOTVITXl5d21XOGxad21xcmxpbFE2THlWcXlUMlNpd0JYM1pWYWt6L3h4WGVSZGlKQllrYmpoVXFLYUxlTWtuWXJCWExMQmNiS1ZBY0tKYXpPNDJnVWpqTzd0R1JxbXlSWWFyU0N4djVLQlkvZEdoNXUvNUh4d3dDM1VvUHNMV3lzNjNZalpjTWdpTnF2Nlg4VXEycWlidlB0dW5vaElSQjYzZ3FDKzI2ZU5xdVZlWXVJV2Y2Zll6ZlV3cEk5YVNnUDJWZHlldFEvZ3ZzbjNpcW9Gdm15TE5VSThnbWN2UWs2Ujl1T1ZYczQwWHZ1b0hUT0RnemZyT05ZZnBDNEtaNyt5RlF2N2ltdlhNRkZBcWR1Zi9XTlk3eWZQU3Y0M0lHakZVUTk2Q0YvRzdHcm93TU9JdXFDakFYZkg0VlQyYW9JTVp3eU5LYmFFZlVNZjgwUld5UFdMNnRDZFRDdCtEaSsrMU9hY3pweVViK0h4T2NCVHJqSjZPS1ZUMEo5YW45MVpFdUkvZ3BaaXMyaytrZHhYYktrRzEzWnltN2pmQ3lvSVdPZTNtVlI5U1pyT1g3dTFBZy9oblY4eXI5MDkiLCJtYWMiOiI3ZGNmOWRhMzQ5NjU0YWJmNTg1YjNiZjc3YWYwOGU2NTE3N2QxMjY1MjkzNTcxMzVjODc0NDU4ODA4ZGJjNzRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxKaDhvNXJrYitHUk5jZHdyNkpaN2c9PSIsInZhbHVlIjoiNUNRYUJROHBrYXJnOC9ZMllscHQ2RTNjdXcwbGcyYmNPbjRFT1RCSWtJQ2RQeThlSWVORTErc2JDbXlpZUFDN2MveVF2ZVBqUXpiVUhhRDRBUE5EdW9VbnR4OHVuaTBhcVlOL1Z3SWhCRkQzZkdKT2c5cUZWeWJ3K3VvbmF1REJobFBWck9lWnJtN0kyVW1yR2xnNHNoMENNYTdmR3NwVGF0a01odFdkVHp5QW5pSHpuaVVxTXdnU1pkaVdXb0VmN2JTTlhhbjRxdzlXbkpJM3dGVStqKzZpQkhxYXNPSUlTM1YyRm9paTBiYUM1VFNMb2FLb0VncTZTaktkNER0QWpZYzE5OUYrL1JvbC9WbW93N3VmYzFLalVJL1RMa2RQcVlrbEYyelZiQXJNK3FvTnJQM2daVHVHc1BQY1ZKZEFOUG5IUFpBUkFXWk5DaldVY2ZDdUVTUmgvbHc5UFN4SFlBNHU4NzlLU2hVV3FBN0N5ZEcyT3pEdlpwbE51U3ZDbVJhRFBUN3hxL05qdk9YNHB6MEVCamh3WVRZOEFrK2gvQnpNdUtVejMvYjFmRmVpRjlTNXhDbXNqWXoyR1RTS1IzeUxHQ3IwRnRvV0UwN1doY3Y0VzhCU2hsQzNSNktRdDVvT3dMSHArdWIwWG9iTFdGK0Ftb1B5SmQrd2lSTG0iLCJtYWMiOiIxNjJjYjM0NTIzMjUzYTc0ODhkYTQ4NDgzYjgwNDRmZDRkMzdjNmM0NzIzYTcxZThmNWNmNzFhNDY3Y2Q4M2M0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBYemZPMTdFYldSZkNrVTdYYXJ6Z1E9PSIsInZhbHVlIjoiWnFOT0NIOGlaYlhlOUNZaXQrRkZSeDZKbG5aUG5WVy90RS9xWE5yaWw1T1UzYnlVeVo5cjJHR0hZeUVJdHZ4NisyOTVITXl5d21XOGxad21xcmxpbFE2THlWcXlUMlNpd0JYM1pWYWt6L3h4WGVSZGlKQllrYmpoVXFLYUxlTWtuWXJCWExMQmNiS1ZBY0tKYXpPNDJnVWpqTzd0R1JxbXlSWWFyU0N4djVLQlkvZEdoNXUvNUh4d3dDM1VvUHNMV3lzNjNZalpjTWdpTnF2Nlg4VXEycWlidlB0dW5vaElSQjYzZ3FDKzI2ZU5xdVZlWXVJV2Y2Zll6ZlV3cEk5YVNnUDJWZHlldFEvZ3ZzbjNpcW9Gdm15TE5VSThnbWN2UWs2Ujl1T1ZYczQwWHZ1b0hUT0RnemZyT05ZZnBDNEtaNyt5RlF2N2ltdlhNRkZBcWR1Zi9XTlk3eWZQU3Y0M0lHakZVUTk2Q0YvRzdHcm93TU9JdXFDakFYZkg0VlQyYW9JTVp3eU5LYmFFZlVNZjgwUld5UFdMNnRDZFRDdCtEaSsrMU9hY3pweVViK0h4T2NCVHJqSjZPS1ZUMEo5YW45MVpFdUkvZ3BaaXMyaytrZHhYYktrRzEzWnltN2pmQ3lvSVdPZTNtVlI5U1pyT1g3dTFBZy9oblY4eXI5MDkiLCJtYWMiOiI3ZGNmOWRhMzQ5NjU0YWJmNTg1YjNiZjc3YWYwOGU2NTE3N2QxMjY1MjkzNTcxMzVjODc0NDU4ODA4ZGJjNzRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxKaDhvNXJrYitHUk5jZHdyNkpaN2c9PSIsInZhbHVlIjoiNUNRYUJROHBrYXJnOC9ZMllscHQ2RTNjdXcwbGcyYmNPbjRFT1RCSWtJQ2RQeThlSWVORTErc2JDbXlpZUFDN2MveVF2ZVBqUXpiVUhhRDRBUE5EdW9VbnR4OHVuaTBhcVlOL1Z3SWhCRkQzZkdKT2c5cUZWeWJ3K3VvbmF1REJobFBWck9lWnJtN0kyVW1yR2xnNHNoMENNYTdmR3NwVGF0a01odFdkVHp5QW5pSHpuaVVxTXdnU1pkaVdXb0VmN2JTTlhhbjRxdzlXbkpJM3dGVStqKzZpQkhxYXNPSUlTM1YyRm9paTBiYUM1VFNMb2FLb0VncTZTaktkNER0QWpZYzE5OUYrL1JvbC9WbW93N3VmYzFLalVJL1RMa2RQcVlrbEYyelZiQXJNK3FvTnJQM2daVHVHc1BQY1ZKZEFOUG5IUFpBUkFXWk5DaldVY2ZDdUVTUmgvbHc5UFN4SFlBNHU4NzlLU2hVV3FBN0N5ZEcyT3pEdlpwbE51U3ZDbVJhRFBUN3hxL05qdk9YNHB6MEVCamh3WVRZOEFrK2gvQnpNdUtVejMvYjFmRmVpRjlTNXhDbXNqWXoyR1RTS1IzeUxHQ3IwRnRvV0UwN1doY3Y0VzhCU2hsQzNSNktRdDVvT3dMSHArdWIwWG9iTFdGK0Ftb1B5SmQrd2lSTG0iLCJtYWMiOiIxNjJjYjM0NTIzMjUzYTc0ODhkYTQ4NDgzYjgwNDRmZDRkMzdjNmM0NzIzYTcxZThmNWNmNzFhNDY3Y2Q4M2M0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229017387\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1418384184 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418384184\", {\"maxDepth\":0})</script>\n"}}