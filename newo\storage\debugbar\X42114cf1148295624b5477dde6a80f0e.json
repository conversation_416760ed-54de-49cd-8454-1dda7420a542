{"__meta": {"id": "X42114cf1148295624b5477dde6a80f0e", "datetime": "2025-06-08 14:46:33", "utime": **********.04829, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749393992.406627, "end": **********.048317, "duration": 0.6416900157928467, "duration_str": "642ms", "measures": [{"label": "Booting", "start": 1749393992.406627, "relative_start": 0, "end": 1749393992.959881, "relative_end": 1749393992.959881, "duration": 0.5532541275024414, "duration_str": "553ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749393992.959895, "relative_start": 0.5532679557800293, "end": **********.04832, "relative_end": 3.0994415283203125e-06, "duration": 0.0884251594543457, "duration_str": "88.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43933616, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.027589999999999996, "accumulated_duration_str": "27.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.006613, "duration": 0.027039999999999998, "duration_str": "27.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 98.007}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0387719, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 98.007, "width_percent": 1.993}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-170228316 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-170228316\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1069790957 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069790957\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1744038969 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744038969\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-142632078 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749393346722%7C61%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRYemhLQlk4UFdrNm51VnlLSGZRaVE9PSIsInZhbHVlIjoiMHY1MGd3cVlMT09yRnlqaXNHNDJvU2xYT0NvLzdJeEZYdi9hVnRQSVNPVWNQMUdrSDNCYUdvdVFiN2dqY3FVSnBaYjlTUzI4Z2NtblVsWTRVUnpHLzJuN1lMVlU5Qnk2MWpiSFBIcjZiazYva2FUcXFXOEhySkdyRU5salM5QXpZMHVkZ1k0b0NoK1E3NEN3K2VaeXlYdmYvbENla29DeWRqbWFXS1BEODJzaUpnZm9zbS8wL3RTY0VlWlJJV3VrRHBzakFQY2YzeWFRcGlsczZzaXI3Tkd1eHREWG1vZFdycVoxbTZuSHV5dWptWHRmQnhPS2xNRjczSXVkakwxd2s1OVptT3FqWU1VL3EzeDI3SEJqbEhkcFA0UTJKUDF4VGplRjZhVk5NaTU2dm1xSlZkdkN1SjdzUGpjUldYbVFrTW9TNis5VVRQbWR3QWMwb2RtVEFjeWVCUVZ5Tmcxa1hLb1l3RDlvWEdKaUJmY0RVN3NpUjIrd25mRlFBSE1TK3U2K011REVQSitGS2NlcmY3VnI5RjM4aGhlZjY2SHZCdk5xYi8rKzY2cUlTVE02MUU0eEZYOVVSVzA4dERaUTV5bmhpU0M0RTFSUjg2UncwS1F5QnVpdThXQkNjV0Q2NjB0b0hMVlJXZFlPaElOamhhT1Z0NkN3Wmx3eU1CeGQiLCJtYWMiOiJkNTZkZDJjMDY0ZmQ5ZTA2MTZmNTM3Njc4MGNkMWNmZmYyYTQyYWMxYzQyYTI2MjYzOTQ2ZGVlNDAyMzIyZDdhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjltQy9NREtCU0ora29OZURVVi8vN0E9PSIsInZhbHVlIjoieGNVOUdZaHc0b3g1YVRvZ0F5YVpGOUVBTEtHQ2NRTFdkU3RaRnppZW1HbGtFU2V3RU1pK1E5WXlINlZESitBQ0JoNENPWW9pK1FJUzZROHQrNEEwbVM2QXljTjVoSkcwRnBRMXRMb0Z5enkwYjg1ZC92QXVVMHgrczBFNFBuS1dXV1dHS0NhbnFiMUxxaUJMakt2MGNUdTFza1E2b2EvMEpaYjkxc2RrWjZDemNGZ0JpamtrRWg0MWJST2IySUh5N2hLdXNPemtaWENMUWRpTTVaU3d5S1VqMGVQc3YzNnB5MTBpSXpWdWduZ0FvWHhLWGtwNElRSFdzamIzcTl6a1BadlA1L2tCZ0dkSXJTSWc2ZUw5N0dIa09Gdi8yRWdZaVRHZitwbjhyckh0OFpJb2xuYk1xZjRObmpvK1c0bXRlR3k0UWxmdkp3UVE2TlFFN0ZOQ0pTcVBLaVJqUnZPd2NMcnNWZHlmNWowYnE1L2tIUGpRQlE3aWhCY3l0UW5TdGh0RHFGTm5ZNnpkekV1RXNlWDZZaFB5SVVsWXdMOURYWkUySk5sTXpNV2xQZDZ1L1dYQlNMNTlteHB1bHRZcHJZMS80ZmlvV3NGT2M1cUVvWHFFQi9DNlBrLzRUYk43VHp0MXY5SW04RUszWkM1OXFueEVsZ0FKcXRhMUZqeHciLCJtYWMiOiI5ZTU3ZjA0M2JkNGI5MjE3MWRlMzMwMDFlN2IzYzJhYmUxZDY2NGJmMDk5NTE3ZGZiYzQ4MDliZThmZTY2ZDBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142632078\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-652250882 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-652250882\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1588799545 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:46:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNIVkNNNFl6NnJuYzlEYVpWWERKVmc9PSIsInZhbHVlIjoiUWc3OW9FWlVZekFOV0JkRERGQm13YjErcXgvT3lhNHRFeHBtN3NuN0JaWk4yS2syMTNBOTBndG4wUGhkRkU0Y1Ywd0VKN1BLdlVySjBITWpjbkNvVGpyU3I4T2RVVmNUeTJoNjNMYjdDQkIyZnJ6VnpUVTRHMkRlQmFzN01JanJ0Y0dNdDlRNHJmRXNKNlZhT1dwRUI3MTZ4QkxJQ3IxSTJqS094NzJ5dDlCVkhudVdWL3BtZXpsWCtRNWFrVFc3a29lUG9NajVPZnJvbWNaUHVxOE9ZaytMU21mZ3NyZTB3d1l3MitiaW5LbndBV1ZzTXRYR2luV0krSlJDRnRIQVQ4OWRYZmRtK0J2M1Z4ZG9kNGVnSk44eWRBR3RiK3d5VDFFQzBFRkFXdCtRQnJ3RTdGYkc4SVozZWR6SG4xaUtDbG03VUk5MXBia3h0QjkrRG53aTk0Zzl2WGRIOS9VdFdSVWRCWGNhbEhWYmlzVUxvZkdDTCtKZjVvZWd3eisydmRoV3VZSDZIQzZwUThmL25EQWo3N0FpbnFSd2w4UDlsVGx1bnNkV21ZRlYzeTNVQXcwU0NGdElidUQzVERSOTRCUXNTemdLaGUwUHRsVGJSaUtMOFRvZmxYaVdvSFZiM1lQTkNVY3R5VmZyS0F6YUVjWFR2c3pKZWJVWXRjelMiLCJtYWMiOiI1N2Q3NWM5YzM5NTgxMmNlMTIwZTFlZWExODQ0NmMwYTA2YjAzYTA4MTllNmJmYTc4OTkyZGVhMTFjNjYwOTRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik82aWVkOFFhZ0FXVll5aFMyQkRGWnc9PSIsInZhbHVlIjoiWk80aE5SUStOTDc4Mm91akhqUUMycWREdDREU1dzdHZHYVowd2JPSUNyc1hEYXA0b2pMTWdSZnpNUkwrU2tUSWpUT2U0d2xhVkdDa0I0VmF0bXF3bzM1V3Fra2pGaFBXeDZOanVpMTNuRWFWTEtoTS9sK1ZEclQrRjJqRnJ2UW1wdjZXY0V5MzgyUkV6SERHR1JxdTh0bGFpaVBGWWtpQi9MVUVaYWV5UGJOZFBPRG1Yb3VYOUgvbGVDS29nSmoxRkJ0MEJWV2Vtd1NSS0VONlllN2MvbkF5M2Znb0FSOC8xZ01GVmovN01qTFhUako4c1hJWHRiYXpqVFVPRjZGRE50WW9hanlReElxUkJEMUdtaXdVWnRraFJuTUJ2eGY2eVBvLzRpc09jQkNWMkRVZkRZcEx1TW5iT3RMWm8vZlZtSTVFNzZVM0JPRVhLZ0FNaEl2SXo4NzJ3VmxCeWlTV2xBdjNZR3dqQXl6TGkwMldpdVFFQUh1aVZJdTBQMjREZDZidnpsV1ZsRDlCL3BIVGV0S2tjUU16MjlKOFFkOTlDOFhvSmd6TzZxQ1lHMXMyVXY2enppYXBCeXRRSXN6TWhZSTMreDVRUnJwMnNha3VSNmh1YlJjcGJOSFY3TUNsQitOSmgxMi9nQ3QwRHd4NW4wOThET3pnbWVWRDZaUEsiLCJtYWMiOiJiMTlhYWRhZTlmNzViYjM4NmQyNTBjMWIwZjdkZGYyMGYyZTdiZGExMGNjZjYyNTZhNGYzZWJiMGQxOGUzNjU4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNIVkNNNFl6NnJuYzlEYVpWWERKVmc9PSIsInZhbHVlIjoiUWc3OW9FWlVZekFOV0JkRERGQm13YjErcXgvT3lhNHRFeHBtN3NuN0JaWk4yS2syMTNBOTBndG4wUGhkRkU0Y1Ywd0VKN1BLdlVySjBITWpjbkNvVGpyU3I4T2RVVmNUeTJoNjNMYjdDQkIyZnJ6VnpUVTRHMkRlQmFzN01JanJ0Y0dNdDlRNHJmRXNKNlZhT1dwRUI3MTZ4QkxJQ3IxSTJqS094NzJ5dDlCVkhudVdWL3BtZXpsWCtRNWFrVFc3a29lUG9NajVPZnJvbWNaUHVxOE9ZaytMU21mZ3NyZTB3d1l3MitiaW5LbndBV1ZzTXRYR2luV0krSlJDRnRIQVQ4OWRYZmRtK0J2M1Z4ZG9kNGVnSk44eWRBR3RiK3d5VDFFQzBFRkFXdCtRQnJ3RTdGYkc4SVozZWR6SG4xaUtDbG03VUk5MXBia3h0QjkrRG53aTk0Zzl2WGRIOS9VdFdSVWRCWGNhbEhWYmlzVUxvZkdDTCtKZjVvZWd3eisydmRoV3VZSDZIQzZwUThmL25EQWo3N0FpbnFSd2w4UDlsVGx1bnNkV21ZRlYzeTNVQXcwU0NGdElidUQzVERSOTRCUXNTemdLaGUwUHRsVGJSaUtMOFRvZmxYaVdvSFZiM1lQTkNVY3R5VmZyS0F6YUVjWFR2c3pKZWJVWXRjelMiLCJtYWMiOiI1N2Q3NWM5YzM5NTgxMmNlMTIwZTFlZWExODQ0NmMwYTA2YjAzYTA4MTllNmJmYTc4OTkyZGVhMTFjNjYwOTRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik82aWVkOFFhZ0FXVll5aFMyQkRGWnc9PSIsInZhbHVlIjoiWk80aE5SUStOTDc4Mm91akhqUUMycWREdDREU1dzdHZHYVowd2JPSUNyc1hEYXA0b2pMTWdSZnpNUkwrU2tUSWpUT2U0d2xhVkdDa0I0VmF0bXF3bzM1V3Fra2pGaFBXeDZOanVpMTNuRWFWTEtoTS9sK1ZEclQrRjJqRnJ2UW1wdjZXY0V5MzgyUkV6SERHR1JxdTh0bGFpaVBGWWtpQi9MVUVaYWV5UGJOZFBPRG1Yb3VYOUgvbGVDS29nSmoxRkJ0MEJWV2Vtd1NSS0VONlllN2MvbkF5M2Znb0FSOC8xZ01GVmovN01qTFhUako4c1hJWHRiYXpqVFVPRjZGRE50WW9hanlReElxUkJEMUdtaXdVWnRraFJuTUJ2eGY2eVBvLzRpc09jQkNWMkRVZkRZcEx1TW5iT3RMWm8vZlZtSTVFNzZVM0JPRVhLZ0FNaEl2SXo4NzJ3VmxCeWlTV2xBdjNZR3dqQXl6TGkwMldpdVFFQUh1aVZJdTBQMjREZDZidnpsV1ZsRDlCL3BIVGV0S2tjUU16MjlKOFFkOTlDOFhvSmd6TzZxQ1lHMXMyVXY2enppYXBCeXRRSXN6TWhZSTMreDVRUnJwMnNha3VSNmh1YlJjcGJOSFY3TUNsQitOSmgxMi9nQ3QwRHd4NW4wOThET3pnbWVWRDZaUEsiLCJtYWMiOiJiMTlhYWRhZTlmNzViYjM4NmQyNTBjMWIwZjdkZGYyMGYyZTdiZGExMGNjZjYyNTZhNGYzZWJiMGQxOGUzNjU4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588799545\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}