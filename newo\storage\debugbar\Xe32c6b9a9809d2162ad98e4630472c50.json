{"__meta": {"id": "Xe32c6b9a9809d2162ad98e4630472c50", "datetime": "2025-06-08 12:54:42", "utime": **********.047568, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.714262, "end": **********.047604, "duration": 1.****************, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": **********.714262, "relative_start": 0, "end": **********.878203, "relative_end": **********.878203, "duration": 1.****************, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.878243, "relative_start": 1.****************, "end": **********.047608, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00836, "accumulated_duration_str": "8.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.964149, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.928}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9943979, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.928, "width_percent": 13.397}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.023319, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 73.325, "width_percent": 26.675}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387279672%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkF5QW1rc1VEQlFQaG1YQk81VEFyY0E9PSIsInZhbHVlIjoiS3k4a2hGcmZzUGJHNXBnZGhKRHU5MXZ5bVhadURzUWRuL3RoU2VuWGJPVm5SeUlKa1F2VUhYQkpoNlZoRjdFUzJNbWhReHNXd1gzSDJhNTJMRFVLUkhXMnFDUmJyWEw1YjAvRzdLRm9Rc0l2SjM0UHJNclpFWGRLa0xsM2VPcGw2R1lhOFRQd0UzbnM2NHJpQS82ZUZ0OTNpcFd4aDB4Yk1jU1YxVk9WaGpoeGdyL0ZGUnd1bkYxZ0JlUkFlaE1CYy9NamFpNXZYZlh4NXg0RzNLS01kbTlRT0VMT0Q2dkV2Z0NaR1VUQTc5RmVuajdTTEVRMCtrenBxYmRzTVhqWFdpYUNhSk1iaVA1QjZnclVYck0xYTBGTzNaQWpORk1vYVp2RDNRTWNPTmZ2Q1UvNXR6aFZ0T05LWDhwWkx0NGFVWUxtbjk5NzFHcjBnRHZFd0VYSVFGOU5MNzNjWWV6TFJwK2s1bWNOS2VuOWk4K05VR0Irc0c2dTlTdE5iZlFJclFFdHFHZWZuY0syd3huRnZxRUV3aDVSSGFDV3hBQWIrSjRBamJjd0Via0ljYUlMRjVsenpDUE5JeEwxbkZmYWUxeWV0blhtUEczeG4xMjJsUlQ0Rmc1OWxYWDJ4cGhBVTh6Wm96c3JkUXRJU2FrdlB0YkdDbko1VlhJWERKWnkiLCJtYWMiOiIwZjlkNzVhMmY5NTVkNzc2ODQ0NDFmYTU4YjE1ZjJmOGZkYzk4ZGU5NzhjOGEwNjVlMzkxYTZjZmJhZDRjZmZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVVS1hSbkhyNG51bCtVMVMwbkNjUkE9PSIsInZhbHVlIjoiNVlXOURjWjVpVi9UQVNsSHR1KytVSUdaZGhnSzYvQ0lDa3B4dnYzVWpIdFlDMVhKQ1NKWlBjM1hCQ3pCVDBqRkt0eEdxdnVJVW92YUdMcHhtOE1WalFGeG5KbVFQaGpIMm1aN1NkWjVvTkExc1ZEUnNXaFhQbzdUU05HbkpxNElNMjl3cXVTenVFeDY3ZmxhRlE3bmk5L292aXhvUkhCYzlKUkpyVksyMnE4cWkzRE5ta3FQMTBna0g0aERoTVdMWFB2Rjl1SEtjTGpYWnB6cU4razd1enA1eVRDa1JIZ2RISWxEMko2b1RLUTdOYmdYNnNBR2RnUVVGVFFrajBNRngvUlBqaUxSZFRQQWpmVEZGdlM1Y29yY0ZaOFBDbWdRRjU1NVdMRkdTUVNzTXRpZFJjSUFVQVE3RWN2T1phQjc2ZUFqT0UvWXVUZWR0MXhMV0psWGVjRzZMckRUamJYSTZ0VFE1ZzgyZ0ErZVJaUm8vRVdqdGZuclRMQkJIYU5GS0xKS1FnU2ZMT0pNZ1Q5bmNpZXlVQ0tOSmROMzVQZnY0UUFOcndHaG9lTnc0RHQrUlZEQXd3TTFrUzF4UVdhMlFQdkdGRnB6a2tad0JCTFMycGt0cEJQeTdHM0lnTVBUNTY1ZEdZM3hTUU05ZnV3SFc3djVLVjdWV0lmQjR2MEciLCJtYWMiOiJhMTNiZmJhMmExMjIxMTgwODMyY2M4MjRhZDk0MTU4MDMxYWIxMWJhYjdlZmFmNzkxMTcxY2UwYmE1MmQ4ODU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1642486880 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642486880\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1290001953 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imc5Mlp4YldxNllVUmF4WTAvT2xuVnc9PSIsInZhbHVlIjoiVVZQczVkeUpHRVU4K0MwNXBFdWI1TVpBbFJvaXZoQ0FvWUtPdExBayt5amRSejRwL2NCaDFyTVllWjRUT3Y5TWZ4dllmemx0VzFUbTdjY0JDNUhYd0h4aHIzSE9RWVNMQ1VPM3ZyWnRtKzUwbDhOdVVFNTdlaUwyNlg3MWx1Yi9EQ3RTamZqUWsxc095YzB6VGt5WUtCcS84UmtWVE91R1pTWXFEOFBmOWZMeDg2aHViWjFYTE15VXl0SHloQmFSaHNGbStPblNMRkEyUVNveVhzNFpIWlpGSDFKTzdMTUhQaFIxb2JqZVFMdlEzVTVJWkUwM3Iwa0VqU2ppa3lCSUlYRkRDMlRTWlovRHJvMFVsMlNoN1N4M3Z3VlEvU2NyWDRQT2RWemwyUXV3YUhBYzI2S2R2ejduTmF5dXBPc0ZrRHZ4b0lkVm5hSUw3Z2JYb3JDSmptR3ZRaGJyc252WGtyOEl3QVIzeE9uSDlPQVFJTEpYYnh4aTl3ZmhOK0FvL05aM1dLWm5rWFZoZEtnaG5aclVaZ05GVHpJczRxVmFhZmlmNzJObU0yRC82a1VKRS9qc3Ywck1CeDJjQStMNDlGaG12Z2sva2U0VmRncklJbWRlWmRRaDRZZXFWekdVVkJoZFFFRnBXUFIwRng5UXV1R3lUc252TG1sanQxUHoiLCJtYWMiOiJmYWI4NTRmMTMxNjM1NzFjOWIxZGQyNGQwOGMzZTc3MzE0NDkxOTI1M2M0YjliZjZkYTNiYjJkYjhkZjNhMDA5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNSRExKcUI5eTBsRWpabFRtRUhBUlE9PSIsInZhbHVlIjoiRWRKdVFZdGliMTRPZG1MVlFIV2dGcHY0WFRwN2V0bUh6ZEFOa3RXblNSQmszMURiS0wrVlhxUnNqZlBtVFpHS1JkdWU1SmZ4WmtlMDN2YW0xb3B3YlorNWV5ZHAvZ2oyWVo4U1hBZ1NrTXNtZldYRnpkcXNxTU40Q2QzQWxmL29zanJWT0htWmFqeXM5RXQrZHRzdTl2dDhzNExJMjU2QjJtL1NWQ3BtRjZpT3lQV013MHlNcDh0aWlLS3pkVnJlUERpdkhjb3R3QXVNUCtqdUNCcDJ1a0hmWTA3ZUFmSWRaYzFUeXllNXRjR25PdmVqbnV0UGQ4a2JJc3lNOVBFZzMrMjA5N0h4eW8wa1BaVjRQLzhWTm9hY3R3aHB4RWZKRUVpUVNYV216RlVNNTdLQUt1NVZjeVNHNkhrWUZZZ0JaeFNrSXo5MmR5UTh3dEVmUlFaeXJVVnNXYmw5T0FBZTFvZjZEcmRiYXRCQ2pIbEpNM0hyQUdlOGJHWFkxWkZEVmNSYzZ4V05GL3l3LzNmUXdSdUZFc2d4czYvYXVmK2R1QUFxdEo2a3lheFVBK3M0MHFYVmtkY3ZuWWtubUlYdy85R2NwYWFCR2RyL3V4OEUxdTVQRWQ3djF3ckJGcG0vcVR6RU1YWUxjNTgvSnJDSnd5UUZ1Z0xHZ3VDZjNEMlUiLCJtYWMiOiI2YjViMTZkNzBlZDRiYWE2ZTUxMGQ2M2IzOTU2MDRkYjEwNjMyMjA1ZGJlNTlhNzEwOTdiYzg0NjQ3ZGU0OGUwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imc5Mlp4YldxNllVUmF4WTAvT2xuVnc9PSIsInZhbHVlIjoiVVZQczVkeUpHRVU4K0MwNXBFdWI1TVpBbFJvaXZoQ0FvWUtPdExBayt5amRSejRwL2NCaDFyTVllWjRUT3Y5TWZ4dllmemx0VzFUbTdjY0JDNUhYd0h4aHIzSE9RWVNMQ1VPM3ZyWnRtKzUwbDhOdVVFNTdlaUwyNlg3MWx1Yi9EQ3RTamZqUWsxc095YzB6VGt5WUtCcS84UmtWVE91R1pTWXFEOFBmOWZMeDg2aHViWjFYTE15VXl0SHloQmFSaHNGbStPblNMRkEyUVNveVhzNFpIWlpGSDFKTzdMTUhQaFIxb2JqZVFMdlEzVTVJWkUwM3Iwa0VqU2ppa3lCSUlYRkRDMlRTWlovRHJvMFVsMlNoN1N4M3Z3VlEvU2NyWDRQT2RWemwyUXV3YUhBYzI2S2R2ejduTmF5dXBPc0ZrRHZ4b0lkVm5hSUw3Z2JYb3JDSmptR3ZRaGJyc252WGtyOEl3QVIzeE9uSDlPQVFJTEpYYnh4aTl3ZmhOK0FvL05aM1dLWm5rWFZoZEtnaG5aclVaZ05GVHpJczRxVmFhZmlmNzJObU0yRC82a1VKRS9qc3Ywck1CeDJjQStMNDlGaG12Z2sva2U0VmRncklJbWRlWmRRaDRZZXFWekdVVkJoZFFFRnBXUFIwRng5UXV1R3lUc252TG1sanQxUHoiLCJtYWMiOiJmYWI4NTRmMTMxNjM1NzFjOWIxZGQyNGQwOGMzZTc3MzE0NDkxOTI1M2M0YjliZjZkYTNiYjJkYjhkZjNhMDA5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNSRExKcUI5eTBsRWpabFRtRUhBUlE9PSIsInZhbHVlIjoiRWRKdVFZdGliMTRPZG1MVlFIV2dGcHY0WFRwN2V0bUh6ZEFOa3RXblNSQmszMURiS0wrVlhxUnNqZlBtVFpHS1JkdWU1SmZ4WmtlMDN2YW0xb3B3YlorNWV5ZHAvZ2oyWVo4U1hBZ1NrTXNtZldYRnpkcXNxTU40Q2QzQWxmL29zanJWT0htWmFqeXM5RXQrZHRzdTl2dDhzNExJMjU2QjJtL1NWQ3BtRjZpT3lQV013MHlNcDh0aWlLS3pkVnJlUERpdkhjb3R3QXVNUCtqdUNCcDJ1a0hmWTA3ZUFmSWRaYzFUeXllNXRjR25PdmVqbnV0UGQ4a2JJc3lNOVBFZzMrMjA5N0h4eW8wa1BaVjRQLzhWTm9hY3R3aHB4RWZKRUVpUVNYV216RlVNNTdLQUt1NVZjeVNHNkhrWUZZZ0JaeFNrSXo5MmR5UTh3dEVmUlFaeXJVVnNXYmw5T0FBZTFvZjZEcmRiYXRCQ2pIbEpNM0hyQUdlOGJHWFkxWkZEVmNSYzZ4V05GL3l3LzNmUXdSdUZFc2d4czYvYXVmK2R1QUFxdEo2a3lheFVBK3M0MHFYVmtkY3ZuWWtubUlYdy85R2NwYWFCR2RyL3V4OEUxdTVQRWQ3djF3ckJGcG0vcVR6RU1YWUxjNTgvSnJDSnd5UUZ1Z0xHZ3VDZjNEMlUiLCJtYWMiOiI2YjViMTZkNzBlZDRiYWE2ZTUxMGQ2M2IzOTU2MDRkYjEwNjMyMjA1ZGJlNTlhNzEwOTdiYzg0NjQ3ZGU0OGUwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290001953\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1959284965 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959284965\", {\"maxDepth\":0})</script>\n"}}