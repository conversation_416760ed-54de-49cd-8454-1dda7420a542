{"__meta": {"id": "Xb296344767ef7d53006385c528c4e732", "datetime": "2025-06-08 14:52:59", "utime": **********.848021, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.148503, "end": **********.848046, "duration": 0.6995429992675781, "duration_str": "700ms", "measures": [{"label": "Booting", "start": **********.148503, "relative_start": 0, "end": **********.750327, "relative_end": **********.750327, "duration": 0.6018240451812744, "duration_str": "602ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.750339, "relative_start": 0.6018359661102295, "end": **********.848048, "relative_end": 1.9073486328125e-06, "duration": 0.09770894050598145, "duration_str": "97.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45602888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01727, "accumulated_duration_str": "17.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.790621, "duration": 0.01504, "duration_str": "15.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.087}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8200002, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.087, "width_percent": 4.748}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.830233, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.836, "width_percent": 8.164}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1143810816 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1143810816\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2066059704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2066059704\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1843328901 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394374407%7C65%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZySStLMXFoNmxsenBDZkNoNzBaU3c9PSIsInZhbHVlIjoiRTVKNTNPZnUrVXFSN1dXRFVjM05YcFM2WFNlSVNuWVpnR2IwZmxNUEo5aUNJT3Y2Y1BHRVdOdElRU1pIcHNRKzRuNjVJUHRmd2ZuTDdrbXk0S2x2dDJTb2RTaDBZUjdReERVMldDQ3FPUFRKRHRXTEdUTzZuT3JIZjVjMksxS0JNdnJkUjVacWNkU1Z5aW9reUs3SkE2di9CMWswbWlTejkwTEhKdCtXdndja2ZEWmQrLzZiZzYvSmhmVXdySmhzUmRTWi91VGhpRkZSbjhSckRSTEVDaHQxVkV6QTZwVUNXM0g5N1pUMFhWT0UvSXlJbFBVUGw3YnUxbmxtSHJ0b3VSblM1QzJ3RHh5WWs0eWQ2TzBkNWRldWFWUlUvaWVmSVZQTjRrMzVmcS9IQUZUWFNyRWQ5c1Z1ZG9JV1FDc3BWdFp6MWMxQXo1OHBPU0ZVWHRyblRxcHl6aWFONjlPelgrNThvd1E2VHZ0RnBvR3RUcmxXZzVJbElCYlp0bG1ZT1Y5MlVwTEoraXhnWm5KZDRXdXFVWld3KzRSZlhCd0pmdWxsaVN5REZzN2dVQnV6MWhydzMwZGJMYWRQaVlCTk02RjdvbEZkTnJiWEZvVzlTdUU2OEV4OXkvN0lUZ1FWdEEzWmdNZWpadXhqZ05HaE5XaDNyeFJvSHhZZUxScDgiLCJtYWMiOiIyNjc4Y2I5ZGY0Y2ZmMGYyMWEyNzQ5OTQ2NDBmMDM4NTg0MTU3YTIzZjRhMTJlNDRlYTNhZTFlYmZiZGYxNzM2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpjdDc3cEhYbnB0SCtOZnpNR29zUlE9PSIsInZhbHVlIjoiSmZ3OGhSSlZwRkxTdGxDTjlFY1Z3eGVDTkRyUE0rNWY5R3pnSWluWWZkUUNYZ284bjV3OTIrVWFhS1hCV3laekZvc1JOTC9zK0h1eTdhdnVZUVJiL2ZRcGJQUDJBQ3ZuZENSMkpuM3lQTUhnODZzVVJxNnIyTFdaOXI2Qmt0ZU85TEQxbENpbTJZU1ViUUczTHM2ZlpCRkE5bDRaSDN6bUZhUmRQMEdjbE05K2hRbGxVdWlrUFdhYWQxNkFqcGxjTHdGV005K2IxOUdEM1V2SnJxSkhJQVB4YXYwVS9qOXVJWmwyRWZOci9EdlpsUk9LYnhIL2lqTzJIemZaOGxrS1VZY2RNMU1OQ0s2R25UZlNtZWY1WHh1OVJCZ2FURDZkekk0NHNlL2UvK2pNa1ZJVFdpcy8vSnVHUHBNSGp3c2Z4TzBDdnRRR1NtR29aTklEZGN2RHptMGJxZmwrcVR3aE9yRFo4WmF0T3FGbXkzemhWYk5kR1VaWERNNDRGT3pMWjQxeGxsU2NNTzZiTGlpeFZzY0JwK1dmKzFIcTliNFViWGM2Ti9RNnJzSzUxUzhsOUpNV3BtWmxOcTB6VnpCN1VLUWN0UkovekVzSkgzaWsvSE1lMEE2eHF4TndqRlhFRVZSQVh5N1pCY3FJazBFdVEzRFBsS2xjRzNCZ3puQmwiLCJtYWMiOiI4Y2IzOTQ2Y2U4Nzk5NjVhMjhmZmYwNGIyZjIwYzg0MTgxYzczNDc4MWFlNDExNmY3NGZkOWNjMjE2MjBlOTUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843328901\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-38477300 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38477300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-484414062 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFUOHRwSWRieGpMRWcxRE9SRkUxZWc9PSIsInZhbHVlIjoiankxSFVaYVFUdFhIYjk1TDF0YUU2cEdzM2k1elJ6OEdQcWhaUGN4OW1EdTNDRUtiVDgwTlRoM1lSN043VWlYZisyY0oyazJRd253QlR2eFpOSVQ5NFZPQ3ZuNDBzTlRoV0ZudkpSWFNEL01LTzhlU2lpV3d6YXQ5VmVhUTNOUzFuTHl4UTVFWlZia1U2a1pKVXRYVHIvWk9yS3kwL1N4Tm84NnZLS3JuZGJBdkdFRlpsOVRsa3dWcjN3VHUxVm95azN0bU9pclgvQU1aeDAxWVFHVjJudDRTOVRvMm5OV0ZxSCt6MUFBMmVpS2lER3BJcG5VN01aZGtQRW5lYTQzMUpqcG9welBLbzI1Wm9SaGFaM296bSt1NDl2ek16NmhCRnROZlJlTi9JLzJDNzcvemFxT3owbjN4WDd2SUFIKzlHYXJCc0N2UnphMlJ3NzN5NGdJcXQ2YVN4NDJBb2NTemUzOFcyZnZKd1JaOHg4RXZsUGV0ZGEzRk8zM1UySHFqVit3bjdjV1JNWEZ5S1dkMVVWcXNnc09neXhYcnR6dGRMR3RhK3BBRHhYM1orcCtwT0IraS9SRldZUVp6Sk5Pb2JtbkhZRWNJOEQvaEFtM0pVUkFVSG4zb3d0K0kwQmc2MzJnQjBWcGNZU2NJRVRhNlROQ0tXbzFMQnk0bmtkU2kiLCJtYWMiOiJlMDE2MjYwMThlMWVlMWI1NTE1YTJmNThhYzUxODM2ZWM4MjFkODQzZTUwNTFmM2NlY2I3MjBkYjk3ZDA0ODg1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlU3ajVtUHZpV1ZzRWNTYkpqUlJSdXc9PSIsInZhbHVlIjoiWmJUYTFXNlFmbkhJMlpabERCWU5Jdk9DOU1iWVYxQzBXVEdxMUNSWWV5bFJ3RGhhdkx3eTYvZVBycTNpclMxeEtSUXVCZ1lYRGh2alowOTBhdnYwVEhKNEhucUQ0MXkrR3N2RjJJVVhvT1RvSk1HSnYxREV1R2QzL2NXbEdCMUhRSmszL2h0dlRIcTBCMFRZTjlZWGVnNlgrSllwSE9sR1RqOThBK25Zd3pmck5pdEdFdVM3cDFqYk5XMWZ6QkYvT3ZHOThFU0dxUitjcUtCaFJibUJZS2RzZzFRK3JaZFlJQ2d4VjltTG95STBiNEYycW9nNXNGSUtyZStBVUluM0prRkZjNWRmV3NKTGdzWnJ0SWZKVmlTdlRnYWE2aGZBMnpxeE1Md1B4Z2YyZkxiMXkxNU8rZ05hTXF4ZXUvd0Q3WVgzdmt1WEYxQWJVQW4rWUN0V0d2VFdoWlV2Unk0WG52SDRyeGx0ZktXYld3dDFKWEU3bFRKdDF5SHFzOURXRjdzOFdlMlZuUXh4M2tCSmMwSW9MMlJ4d0lhMkNzUmVSTGpmR1c4T2xjZkRtRnZobHZvMFE5R0FNRFdCSDhrR2VwYjJuSFpJU1kra2daNzZibU1QVStJNXdieVdqZEMyRW9aT2E2eVN0azNIcENGU0V1akV2cmJuVVBWd0U4dTIiLCJtYWMiOiJlODU2YjcyNmMxYjU5YWQ4ZDc3ZWNmMWUwN2U3NDQ0ZDA3M2YwYTY5NWZmZmM3ZDU4OWY1YmE3ZGU3OTg2NjhlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFUOHRwSWRieGpMRWcxRE9SRkUxZWc9PSIsInZhbHVlIjoiankxSFVaYVFUdFhIYjk1TDF0YUU2cEdzM2k1elJ6OEdQcWhaUGN4OW1EdTNDRUtiVDgwTlRoM1lSN043VWlYZisyY0oyazJRd253QlR2eFpOSVQ5NFZPQ3ZuNDBzTlRoV0ZudkpSWFNEL01LTzhlU2lpV3d6YXQ5VmVhUTNOUzFuTHl4UTVFWlZia1U2a1pKVXRYVHIvWk9yS3kwL1N4Tm84NnZLS3JuZGJBdkdFRlpsOVRsa3dWcjN3VHUxVm95azN0bU9pclgvQU1aeDAxWVFHVjJudDRTOVRvMm5OV0ZxSCt6MUFBMmVpS2lER3BJcG5VN01aZGtQRW5lYTQzMUpqcG9welBLbzI1Wm9SaGFaM296bSt1NDl2ek16NmhCRnROZlJlTi9JLzJDNzcvemFxT3owbjN4WDd2SUFIKzlHYXJCc0N2UnphMlJ3NzN5NGdJcXQ2YVN4NDJBb2NTemUzOFcyZnZKd1JaOHg4RXZsUGV0ZGEzRk8zM1UySHFqVit3bjdjV1JNWEZ5S1dkMVVWcXNnc09neXhYcnR6dGRMR3RhK3BBRHhYM1orcCtwT0IraS9SRldZUVp6Sk5Pb2JtbkhZRWNJOEQvaEFtM0pVUkFVSG4zb3d0K0kwQmc2MzJnQjBWcGNZU2NJRVRhNlROQ0tXbzFMQnk0bmtkU2kiLCJtYWMiOiJlMDE2MjYwMThlMWVlMWI1NTE1YTJmNThhYzUxODM2ZWM4MjFkODQzZTUwNTFmM2NlY2I3MjBkYjk3ZDA0ODg1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlU3ajVtUHZpV1ZzRWNTYkpqUlJSdXc9PSIsInZhbHVlIjoiWmJUYTFXNlFmbkhJMlpabERCWU5Jdk9DOU1iWVYxQzBXVEdxMUNSWWV5bFJ3RGhhdkx3eTYvZVBycTNpclMxeEtSUXVCZ1lYRGh2alowOTBhdnYwVEhKNEhucUQ0MXkrR3N2RjJJVVhvT1RvSk1HSnYxREV1R2QzL2NXbEdCMUhRSmszL2h0dlRIcTBCMFRZTjlZWGVnNlgrSllwSE9sR1RqOThBK25Zd3pmck5pdEdFdVM3cDFqYk5XMWZ6QkYvT3ZHOThFU0dxUitjcUtCaFJibUJZS2RzZzFRK3JaZFlJQ2d4VjltTG95STBiNEYycW9nNXNGSUtyZStBVUluM0prRkZjNWRmV3NKTGdzWnJ0SWZKVmlTdlRnYWE2aGZBMnpxeE1Md1B4Z2YyZkxiMXkxNU8rZ05hTXF4ZXUvd0Q3WVgzdmt1WEYxQWJVQW4rWUN0V0d2VFdoWlV2Unk0WG52SDRyeGx0ZktXYld3dDFKWEU3bFRKdDF5SHFzOURXRjdzOFdlMlZuUXh4M2tCSmMwSW9MMlJ4d0lhMkNzUmVSTGpmR1c4T2xjZkRtRnZobHZvMFE5R0FNRFdCSDhrR2VwYjJuSFpJU1kra2daNzZibU1QVStJNXdieVdqZEMyRW9aT2E2eVN0azNIcENGU0V1akV2cmJuVVBWd0U4dTIiLCJtYWMiOiJlODU2YjcyNmMxYjU5YWQ4ZDc3ZWNmMWUwN2U3NDQ0ZDA3M2YwYTY5NWZmZmM3ZDU4OWY1YmE3ZGU3OTg2NjhlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484414062\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}