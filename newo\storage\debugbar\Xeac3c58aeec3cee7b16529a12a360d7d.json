{"__meta": {"id": "Xeac3c58aeec3cee7b16529a12a360d7d", "datetime": "2025-06-08 13:26:51", "utime": **********.806765, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389210.457428, "end": **********.806796, "duration": 1.3493680953979492, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1749389210.457428, "relative_start": 0, "end": **********.659308, "relative_end": **********.659308, "duration": 1.2018799781799316, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.65933, "relative_start": 1.201901912689209, "end": **********.8068, "relative_end": 3.814697265625e-06, "duration": 0.14746999740600586, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00733, "accumulated_duration_str": "7.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.749332, "duration": 0.0063, "duration_str": "6.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.948}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.782757, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.948, "width_percent": 14.052}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-274774266 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-274774266\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1348697347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1348697347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1572419633 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572419633\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1578742634 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImthQk9KV3lTbE00QkFCMFdFWDdZMUE9PSIsInZhbHVlIjoia1IxblZCVVo0ZytYRGs3c3pVSEtmUHJOTEhHanNIZ0s1VERWWWZBeEpkQjM1NmdVM0QrTU5BVG9Ic0tpSklYWU1xVlFnRGpqMkZZeVowS2wxc0pwWWsrWEp4TjVPSUx6R1ZaSGFQclpFcWswTjdTZEtJSi9nd2duUEpLTWgvYXZoRUpzeXdZM0QzZmc4WlE5Nis3UmNOd2NaR0MwUnBQTzNQSmM2cnFkNmUvYXk5ODZWb0ZUYkJUQ3Nva0RFQjRyT2szWk5jZVpKV2p5R0hOcld4c1VyNEpyb2NWeWR6TFc1WWszN2RmYTY5dTZwdVpqbjNvU0kxNGp3N1gxMndHMEFlNUdUTkMwZjBCY3FPQ3hxNHRMbmVWVmFYT0xOK0VVUzYzNHprZ3NCLzBXU3dTMXRCRlhTbXpYL1kzQVRuQWZpd2ROL3cvNVhxUWI1ZG9UNkp1TDRnTmlVQjFoODV5Sk1oVFdpekY4cW1WeEFHb0dvcGVBQU1uRFcraTduVGdjbGlNeDduZ0UxM2NndjVOeHh0MERKdGU4VjhFUERZWEJGTWhwUEkyR1Ayd3dmWUg1U2tnR0kreXVPWU5jcUhsczZzb1ZSWVJLcWhPK1pSNkN6WXh2RW1ZVzZCa2ZIcWZ6UU1EaGhlVzU1aHR2UGZ3aFNMSU9CbDljTEZmZzBmS00iLCJtYWMiOiIzYjZkZDY0OWE3OTg4NjEzZTI5MTdhNDRiYTQ3ZTRiNWZlZDU3ZjBiMTY4NGIxOTdhOGI2N2RhNTZlNWU3NWVhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikp2VDA2U3FYUVNKSjJGQUpxbERURHc9PSIsInZhbHVlIjoiWnlzeGNydkZpL0ZxQ2czQktPUGxqc21rVE82TkJNbGNSVFdhbDFKSmd4U29jMjVHU3JlaUVBTHlPREJ4ZGRIY1VMbmxJVEw3OWk4K0dtcjVISGtWeHpLK09rdWVEQ084eHZ3aFFlbFZ2QnlBM3pRbThGd0RsVlVmMGxjMFlzOGVUMCtyTlUvTC9WNkkrb2wvRFR0Q0dzeVJaR01KL0FXWjVYcDlqcTVRdUhHQ3FqOUcvbXZWQnNCZkVoWEsreE94d2tIYWdMbFFnZzhXMHJ5NDBoaVFEUUZiRUtoSndRSFN3UmhwUlcxMTkwbmY2dFU3VXhVKzF4MUZNQ0t1eHlubnFiM0pEakJRc2RXaERYRE5FdHdDTld3SDY2UFZxREtPK0xMMTF2UXR2b3VOcVVYYXZyL0hiZ1NzRTNidDdDZllmSVF1ZVFXNU9GWXFwdmZhS0FXQ293NUF6Tm5TWUtDdXhtR3NnS25lbEwwaVM5U0JGT25NSVF6WGZrUkdZcklURVg0VXBiYjMxYU00bEMzUUxxS2NSdkl4OU1vNWJNMURFSCs3OFVmNzFSSHU3amVRYUUvK21ocmovNDcxTStCeHBXd1BjNHRLbnZKVVlQSlcrOWxBeThsVzZtdXA3NC9ZTm1KMEdQcEYvUmppMHg1eEJJN3BLQnVyWERTVy8wTHoiLCJtYWMiOiIzMmJhZjNlNGY1MjNmY2RmYzVjNGQzMTQ0OTkyM2ZjOTVkNTQ5MjU2ZTQ4YjZhMmY2NzA4MDM2MjhmYjRhMDQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578742634\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1347581779 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347581779\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-465526003 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:26:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJnUFpmM1FaSVl4KzYrR25kNEJyNnc9PSIsInZhbHVlIjoia2g4KzQzQlVaNFc4WjNURzRHZWkrNDluZXgrS3c5VmVDUmM2dzhmKzFUUlNJNzBpM1BQU3h5dWgrTGRodmpyZGVzZUsxSjBEREltbERPb2xOZEJhWEVkM05iT2JKZ09LYXh0TXVseTI0ZU8zRFdHU0tsY3pVdEhPMEtMVFBBOXhDR0ZWZ2Z3U0hvQ1c0WHQrS25IL2IyOU1HTkNNUGY3bkh0cFlTMHA4NEJ4Z2o2MmdzM0FNR1dxTkFLVzFjNHhSY1NteVdob1o1RGFCVjFZTHRLZjZ4d29mcWNYa0pMTWthek9URjNBd2hrK2RMWjJDMyt5NllseGo5TWpFdm84K0VwL094RHJnRmhCdkY3eEwrekJDZjM2N29oUjBNRFlmQVNUS3YrS2lWcXlab3pRR0VKVmVPQzIwME5vNEdKSUZQVnNqOTZuR1g2Qzl3ajN6V0F6aTF6Uk5DYWRuZXN5dmdnWmZiR1RjcTRsK2NPZ1p2NHJZZkZucWZWTzNlM1BGeHBGMlg3b1RYbTVUUjVOQlpGRkpiQ2d6TGVmRzNZQ012Z2h0WEtnemFRQXRWZjgrMXY0SEI5UXduRFU1Rzlkb2FjWFdmM09iTElraU1ZRkMrTDlsK3prWWpGMW14S3pxZ1QrWno2dFZwTnd3RGVJemE2b3llVHhkWkVhTC9jS0IiLCJtYWMiOiJkNDczOGIyNWE5NjcyNzgxMWFiOWU5NGI1ZDFhNTNlNjI3M2U2NzRkZjk1ZmI4ZWUzZDNhZmEyMTJmMGZhZjBiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjcwK3hvYWt0Z3pKYXIzNFJqcGt3bGc9PSIsInZhbHVlIjoibTliTDdISDlRRlpDTXpxRzgxbkNjL3BicDJRYzc4UFY2K2tMZE9JYTNET1RudmRGK09MaDRCajFwNUtkNDd2RytsRGdNeFlxeStjSEJkZ3E2ODhpc3hOR0dBdEtQZlpyeE5SbFlkd1pDNmlYMDVkQ2xIM0lmNnNkMno0MVhwelJrMVFMTmlHanhpUmUwU1JtaDRiTlpjYjBxZFo2VEkwYkhnS1ZWMXlFMFR3Rk1WOUtCODM4WVk3dk0xWVZwdEpUTldKeWFRbnFRZVNiN2M2Mm1YOVgyQWdQeXFqRkdydXZsK1VHVWVaU09UN284Z3FmR0c1TkFEMnRocG41MXA0cmNVYVpldWFBS29SWk5UMkZnNWhCVWpDUzMvcDVTL1ljaEp5eGYxMlFCYjh4M2ppNS9maTVEWHEwTlc2SkJaOHRUVy9jSElXV2R2WE93TjZGRlQ4a1EzaUp6OXFtdUQ1bHBndk43ZjBwZzZJTExRQkM5aE9nV2l2eVBUeGRHNGswTkNWQ0tIR1BxYVBlY1ZWSEFLRnNFMnNSQ01FeEtNbFFBWkMyWG9PREMyNENFNmFETHNmb1RJTmh5WS9uSWtwT0kwUUlSMXVnaE5KKzczaXE2UTJyUFI3NjZsUnFXK0wwaFVMeWQxM01xcmJQdklPVFVid29TTHMvVkRYcGZrM1AiLCJtYWMiOiI2YTU5YWRjOGIzNjE5Nzg2NzYxMmU2MDQ1MTI4ZmQyZmU2N2Y2YTFmODFkOWEwYWI0NTM4NjBjNmRkNjljNjk5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJnUFpmM1FaSVl4KzYrR25kNEJyNnc9PSIsInZhbHVlIjoia2g4KzQzQlVaNFc4WjNURzRHZWkrNDluZXgrS3c5VmVDUmM2dzhmKzFUUlNJNzBpM1BQU3h5dWgrTGRodmpyZGVzZUsxSjBEREltbERPb2xOZEJhWEVkM05iT2JKZ09LYXh0TXVseTI0ZU8zRFdHU0tsY3pVdEhPMEtMVFBBOXhDR0ZWZ2Z3U0hvQ1c0WHQrS25IL2IyOU1HTkNNUGY3bkh0cFlTMHA4NEJ4Z2o2MmdzM0FNR1dxTkFLVzFjNHhSY1NteVdob1o1RGFCVjFZTHRLZjZ4d29mcWNYa0pMTWthek9URjNBd2hrK2RMWjJDMyt5NllseGo5TWpFdm84K0VwL094RHJnRmhCdkY3eEwrekJDZjM2N29oUjBNRFlmQVNUS3YrS2lWcXlab3pRR0VKVmVPQzIwME5vNEdKSUZQVnNqOTZuR1g2Qzl3ajN6V0F6aTF6Uk5DYWRuZXN5dmdnWmZiR1RjcTRsK2NPZ1p2NHJZZkZucWZWTzNlM1BGeHBGMlg3b1RYbTVUUjVOQlpGRkpiQ2d6TGVmRzNZQ012Z2h0WEtnemFRQXRWZjgrMXY0SEI5UXduRFU1Rzlkb2FjWFdmM09iTElraU1ZRkMrTDlsK3prWWpGMW14S3pxZ1QrWno2dFZwTnd3RGVJemE2b3llVHhkWkVhTC9jS0IiLCJtYWMiOiJkNDczOGIyNWE5NjcyNzgxMWFiOWU5NGI1ZDFhNTNlNjI3M2U2NzRkZjk1ZmI4ZWUzZDNhZmEyMTJmMGZhZjBiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjcwK3hvYWt0Z3pKYXIzNFJqcGt3bGc9PSIsInZhbHVlIjoibTliTDdISDlRRlpDTXpxRzgxbkNjL3BicDJRYzc4UFY2K2tMZE9JYTNET1RudmRGK09MaDRCajFwNUtkNDd2RytsRGdNeFlxeStjSEJkZ3E2ODhpc3hOR0dBdEtQZlpyeE5SbFlkd1pDNmlYMDVkQ2xIM0lmNnNkMno0MVhwelJrMVFMTmlHanhpUmUwU1JtaDRiTlpjYjBxZFo2VEkwYkhnS1ZWMXlFMFR3Rk1WOUtCODM4WVk3dk0xWVZwdEpUTldKeWFRbnFRZVNiN2M2Mm1YOVgyQWdQeXFqRkdydXZsK1VHVWVaU09UN284Z3FmR0c1TkFEMnRocG41MXA0cmNVYVpldWFBS29SWk5UMkZnNWhCVWpDUzMvcDVTL1ljaEp5eGYxMlFCYjh4M2ppNS9maTVEWHEwTlc2SkJaOHRUVy9jSElXV2R2WE93TjZGRlQ4a1EzaUp6OXFtdUQ1bHBndk43ZjBwZzZJTExRQkM5aE9nV2l2eVBUeGRHNGswTkNWQ0tIR1BxYVBlY1ZWSEFLRnNFMnNSQ01FeEtNbFFBWkMyWG9PREMyNENFNmFETHNmb1RJTmh5WS9uSWtwT0kwUUlSMXVnaE5KKzczaXE2UTJyUFI3NjZsUnFXK0wwaFVMeWQxM01xcmJQdklPVFVid29TTHMvVkRYcGZrM1AiLCJtYWMiOiI2YTU5YWRjOGIzNjE5Nzg2NzYxMmU2MDQ1MTI4ZmQyZmU2N2Y2YTFmODFkOWEwYWI0NTM4NjBjNmRkNjljNjk5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465526003\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1263626684 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263626684\", {\"maxDepth\":0})</script>\n"}}