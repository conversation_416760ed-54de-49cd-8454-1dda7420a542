{"__meta": {"id": "Xe25e3d94e31d88b10a89c147aa75d6c3", "datetime": "2025-06-08 12:54:29", "utime": **********.30859, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387267.611695, "end": **********.308631, "duration": 1.6969358921051025, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1749387267.611695, "relative_start": 0, "end": **********.081447, "relative_end": **********.081447, "duration": 1.4697518348693848, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.08147, "relative_start": 1.4697749614715576, "end": **********.308636, "relative_end": 5.0067901611328125e-06, "duration": 0.22716593742370605, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45084608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.010220000000000002, "accumulated_duration_str": "10.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1977382, "duration": 0.005730000000000001, "duration_str": "5.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 56.067}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.230812, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 56.067, "width_percent": 10.274}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2598062, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 66.341, "width_percent": 16.732}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.281076, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.072, "width_percent": 16.928}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1594909357 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1594909357\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-960655828 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-960655828\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1580521184 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580521184\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1162878713 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387248352%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcrMWRnNHlGRFd5Vk1oa2ZWNlhvNGc9PSIsInZhbHVlIjoiNFNreWpTbUNZVWRuZTgwc0NhUlo2NWViNEtxaE92Nm1GZzBJbStucWZtRHZYajhPTnJVQnBCT25TV1puTTB4QzFRZ1FseENwdFVGRk5RYlc4N3BUNk00WmxvUmw4WHNVRzlIQk0wYzBXNHZiUkNNSXhxZmJrQWhIVkt0QmQyMUE2WHBDdlgxalFadXNlSmwyU2dwZFV0a2pKTytoZ25CQ2svYkt2TWNTbVlsdU5uQmkwQ2dKalBJRFhiTnVZd2wxQllSK1RNUDlsQWpCVmRMSFpDV0w1TFNRT0hjVmtackpncEgyMk53alpCWDBsRXdkb3lEZm5DRnBNeXpTVXJtZlh3MlVzcFZraklOeEw4UjI4MFJKd0xJbTFDZnBKNjB0YXVMNSthK0YzRkVSVFhCbnVsZ08rREI1cFdwTGc5T2ppYkoxZ0FuNkNoOGozVGYzTjVqZTVGekswRm1KRTd0YWpkSmRHM3VmK1N1a3pBci9CejVlOHo2VEVhZEhwOXFQUC9OYm9PSnJZcGU0V0p4emNKNmh5emdVclV2cWQ3NlhMUENqMCszM2NsY1ZzVHpxWFNGa0pyWG9tblZWWFJqNHlaVStNQVM4b094dC9tb0RDSDZ3WHNMNTFhZDNmUXorMjJNQTF5U1Q2MGFibFdDMkhPZ2l0RThvRWU3blpxbGkiLCJtYWMiOiJmZGNlZDA0NzZjZDQ3MGIwNThhZDc0NmM3MzZlMzFkOTRlYzBlZGU2ZDk5ZGRlNjlhYzAzMjIyNDFlZjMxYmZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpCMjZzNFg4NHF3OUJHMFdEZTJYV3c9PSIsInZhbHVlIjoiSHB3UEdtUXdqdkVKR3YxMlNFcFVwM1owQm8xQWw4bng4L0tDdndvRnNxTVF0NlZzYVBkbW92QnIzVk5qU053bS83Q290K2ViVk9tMEJjM0RCNVR3Wi8wU0tPYzcveXFFcitXSjdPSDBNdEwwcWEvNHQzZ1FUeFRjTUpWWENxdDdzQTlTVzE5ekhjWGF3aVVjZjJyZkFsRHBLQm1yMjhMblBTU3RMTW1GTDhpZEZwN3NwQVdZa3VZUVJYZnR5UVQvZHM0eEd1RU9hOTNQSDJhbWtCMjI2eXZscnVpbzJXMFpBRnhqUFRWRnZNUDc3amZNUnFmTWtEWmtqLzM3R0pCRzkrd2dGeXRZcnJtZzY1a2lzM2toK3o0TXRMVXB6ZWt6c2xNbFNpRGJVOVU4Q1B3ci83enZEZng5eGpXQ25EZjJHVGZJY0c1eVNiNW5yZDloSDhrVEVEYTc5V0pMczFUdHJJZWgwa3pqU1hmRy9JNGtpRVJtMzFWTXFGN0FJNHQvUldsYW5pZ1pmRU9NQ2ZoNW43NmVLUmJsdUNSZ3VVSFE0R1BmYTRTcEpBTXZ0NE0xdWxDWW9KczdmblJobUhBeVd2RDBHT3plZ1N5aFlydU1rc1o5aDRiYTM0TmZRZ1Y4Y2d6OGRvd0J3Mk9OTFJWNTA1bkFZQWE0V0hLSzJtYzUiLCJtYWMiOiI3ZGNkZTI5MzI0NzA0ZTU3ZjlmMDBjOTlmMTNhYzEwZjJkMTU0NTBjNzRjM2U0MDYzODg5MjUwNDE2NzRkN2JjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162878713\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-582720751 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582720751\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1148195529 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InptSDVWa0RxU3orQVFBUmtPQ0tGZFE9PSIsInZhbHVlIjoiZWJMUlk4RWdMakppcjQzekZLRDR1UHdkSjgwditwbzhjQXM2Z3Zucy91VXRiN3FDaU9CWVZ2U280TnRmbDU5Y0tqZDlvcnVsYUE5WmRkc2ZpK0E5NUgzMEExUXhxR1lsNjlVR0VSRVlyVHRyUU9IaWtwT0JvSlJLQ2hkN3JKYVA3NTZJeTFReEkyZjBFUU5mZFdGYnE5OU42aUpKdEYxVVNSanRDV3AzK3NkaVkyOCtneWUyaUN6alA3TWtZR0tyVzZtVWlnTXVGcGh4b0hwWk1iSU9qRTVSSnlHZzArNXgyZVFOV1RncG1Id3FldTZ6cllFSEJKRkt1WU5mTUZ4VmlPWEQySncvOVFONlY3WWNZak4xK2l0ZVd5dWFaTXZJc3Q1MDdhZmpwZDE4ZXQ5NlpLdTA1aHJibWhIdXg0WUFhNVFDMy9mcVluZDBvdDd0US8yaVJ2cTZueDhYUkRsSlBPNms2NHB3ZUhhRUZacHVPMllyaitMTTlHSk54OHF4VnpFYUxOSnVOclB0bjBnMlhxYnhoZUlLY2lyZUh3SUU1NDFnZmNHUkFzc3lTMjZqZXgrV3c4UHBqc25wSmxoQzRMQ1QreURTTFY5RVNxV2V4dTQ0S0FDanJhNUxkdVNEd3g5dXV5WTk2L2VmbDU4UTlHQzIvQkswMzlnYXFpZFMiLCJtYWMiOiI2MWU4NzBkNDFmZTQ0ZTAzZDA4YzI0MzJhNjVkODEwYzdiMGYwOTg0ZTNiM2MxN2Q4ZjVhYTIyYjM1NmFlZTU1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImhrSEM0NTNYMzcvYzNmTm9hcHhWZmc9PSIsInZhbHVlIjoiUFBIUlhYcWdqYTVZUUt4RncvY3RSZ1FYWG8zL3N3RHI2cWdjd04zbVgzTnl3eTlnY2NLajFkMU9wazMxVWNJTVlQRFgzNUNTNkQxQ3lPWlBWZ1BnRWdndmlDZ0JUQ0pQUGx6VkNYR0RueEF5TWI5ZFZKM0lWTlNKOENvMG9Ta1VkY2w1NGhTOEdQVloxVXRhMHhlU3pBbWNrOWFWQTBmbDNaajFKbW4ranFkc3JoczhzSFdjb0YvNEZMN0hHNHRZalhieW1mTW9BU3kwSjNhTFZGY3FzVWVDSVUxbjhvUVlJL1g1d1p4T2ZxUGpySDJWMjg3Y2pGUkNCK0RRakM3RHZpSTRFQmxVTW5kYTd1UUF0cXUxc1UyL2x3SEo4TEZCa2dIc08raVZjSTc4MHlINWh5VEtZRHIxemErbHZZY3M1djFIeDFhUGRHOEt0alhmREQ1eGJNL3FNRTZQMUZ0QXhNWjdwVE5lSG8xWU1QR2MzVXNWWHRJU1I2QTZObzRIZkp4NWtsRkhoR3owaUNlZ0lHOThQeTdoZDRvK0RBOTl6K0ljeVZFNXJiU0ltWEpqUElzOS9zWkF5dU95eWJyeWhEcjZMem9iM3dLMm1uTzA0eGVHTDlEUzFDME1tUHAzQUtmdjhxWTVzcVI4WGpoVjlPWTh5K2FHQ3NIQnhaNlYiLCJtYWMiOiI3YzhmNTBmOTUzNzRiMjU5OWYxOWI3NGIyMTE0Y2I5YTQyOTFmZDUyOTYxNTMwOGI5MTY4NmFkNWE2MzM5ZWIyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InptSDVWa0RxU3orQVFBUmtPQ0tGZFE9PSIsInZhbHVlIjoiZWJMUlk4RWdMakppcjQzekZLRDR1UHdkSjgwditwbzhjQXM2Z3Zucy91VXRiN3FDaU9CWVZ2U280TnRmbDU5Y0tqZDlvcnVsYUE5WmRkc2ZpK0E5NUgzMEExUXhxR1lsNjlVR0VSRVlyVHRyUU9IaWtwT0JvSlJLQ2hkN3JKYVA3NTZJeTFReEkyZjBFUU5mZFdGYnE5OU42aUpKdEYxVVNSanRDV3AzK3NkaVkyOCtneWUyaUN6alA3TWtZR0tyVzZtVWlnTXVGcGh4b0hwWk1iSU9qRTVSSnlHZzArNXgyZVFOV1RncG1Id3FldTZ6cllFSEJKRkt1WU5mTUZ4VmlPWEQySncvOVFONlY3WWNZak4xK2l0ZVd5dWFaTXZJc3Q1MDdhZmpwZDE4ZXQ5NlpLdTA1aHJibWhIdXg0WUFhNVFDMy9mcVluZDBvdDd0US8yaVJ2cTZueDhYUkRsSlBPNms2NHB3ZUhhRUZacHVPMllyaitMTTlHSk54OHF4VnpFYUxOSnVOclB0bjBnMlhxYnhoZUlLY2lyZUh3SUU1NDFnZmNHUkFzc3lTMjZqZXgrV3c4UHBqc25wSmxoQzRMQ1QreURTTFY5RVNxV2V4dTQ0S0FDanJhNUxkdVNEd3g5dXV5WTk2L2VmbDU4UTlHQzIvQkswMzlnYXFpZFMiLCJtYWMiOiI2MWU4NzBkNDFmZTQ0ZTAzZDA4YzI0MzJhNjVkODEwYzdiMGYwOTg0ZTNiM2MxN2Q4ZjVhYTIyYjM1NmFlZTU1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImhrSEM0NTNYMzcvYzNmTm9hcHhWZmc9PSIsInZhbHVlIjoiUFBIUlhYcWdqYTVZUUt4RncvY3RSZ1FYWG8zL3N3RHI2cWdjd04zbVgzTnl3eTlnY2NLajFkMU9wazMxVWNJTVlQRFgzNUNTNkQxQ3lPWlBWZ1BnRWdndmlDZ0JUQ0pQUGx6VkNYR0RueEF5TWI5ZFZKM0lWTlNKOENvMG9Ta1VkY2w1NGhTOEdQVloxVXRhMHhlU3pBbWNrOWFWQTBmbDNaajFKbW4ranFkc3JoczhzSFdjb0YvNEZMN0hHNHRZalhieW1mTW9BU3kwSjNhTFZGY3FzVWVDSVUxbjhvUVlJL1g1d1p4T2ZxUGpySDJWMjg3Y2pGUkNCK0RRakM3RHZpSTRFQmxVTW5kYTd1UUF0cXUxc1UyL2x3SEo4TEZCa2dIc08raVZjSTc4MHlINWh5VEtZRHIxemErbHZZY3M1djFIeDFhUGRHOEt0alhmREQ1eGJNL3FNRTZQMUZ0QXhNWjdwVE5lSG8xWU1QR2MzVXNWWHRJU1I2QTZObzRIZkp4NWtsRkhoR3owaUNlZ0lHOThQeTdoZDRvK0RBOTl6K0ljeVZFNXJiU0ltWEpqUElzOS9zWkF5dU95eWJyeWhEcjZMem9iM3dLMm1uTzA0eGVHTDlEUzFDME1tUHAzQUtmdjhxWTVzcVI4WGpoVjlPWTh5K2FHQ3NIQnhaNlYiLCJtYWMiOiI3YzhmNTBmOTUzNzRiMjU5OWYxOWI3NGIyMTE0Y2I5YTQyOTFmZDUyOTYxNTMwOGI5MTY4NmFkNWE2MzM5ZWIyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148195529\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}