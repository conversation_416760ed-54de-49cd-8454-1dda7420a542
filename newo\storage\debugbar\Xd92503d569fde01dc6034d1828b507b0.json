{"__meta": {"id": "Xd92503d569fde01dc6034d1828b507b0", "datetime": "2025-06-08 14:52:18", "utime": **********.346324, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394337.532326, "end": **********.346358, "duration": 0.8140320777893066, "duration_str": "814ms", "measures": [{"label": "Booting", "start": 1749394337.532326, "relative_start": 0, "end": **********.22009, "relative_end": **********.22009, "duration": 0.6877639293670654, "duration_str": "688ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.220109, "relative_start": 0.6877830028533936, "end": **********.346362, "relative_end": 4.0531158447265625e-06, "duration": 0.1262531280517578, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015809999999999998, "accumulated_duration_str": "15.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.294645, "duration": 0.01509, "duration_str": "15.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.446}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.329843, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.446, "width_percent": 4.554}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-905472014 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-905472014\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-147323157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-147323157\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-829914539 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829914539\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-8126385 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394122823%7C63%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InB2TDlhT0IyTnhyMDNOenlNVnA3dlE9PSIsInZhbHVlIjoiK2JqNTB3c2JtWk5Xak1TdXorSSszWTR4VEVoM1NObFhwNG5ZTTFOcHk1bjRCWklKeDhvdGxhN2UxbHA5MDJZYU9vTUtqdTI4U3NEZzQ0UHRaOTJDaytFMmpMVG44SWs1RlBhc0xPY3hjVUEySEhKakJxaUxWNi9OWXpEbXVQbmV6b1ZSdWYvejU4Z2tNNmJLcDV5TllqUFJ3WDVIeDJBVmpxWHBHZXJKK3BpRlJFQTdFNFR6dGhLaytqWU56N3dkVHBMWmh3NzVmSGxWY0V2WEx0NVlyMlAyVlMxeXh0Y096d1ZlbkppVjBxOUJCTVFUSTBIVGx3YW15Q040OWRJOEdnZFdUTkFTMFlQK2VzQXRxc1RCb3RUQW02ekp4WnV2OUF1YUY4LzJKMTRHN2VSMzZVb2xjM0ZrZkhkUXo0MWQ3QTl0SG1pNHVaejBwWEJhUkF6OUZGYWkzOURFRzdPNjh0ekVlL1k5cEpsemNuTG5LR2NGVnFoZDZkc0d1eWdZeXJSeHVVdHp0ckFBL2VSMm5UZE1UVGFnaXVzRHd3T1R6ek5ocXBmSnVLUTV6MXdLQlkrSFVTRFQ5T3NZeHV5aEJpNzZGdm9icUw5NGdYWkdPalFBNzBDamdZMlp6SkQybTJJclpnanR1eGVCdytxRENxQlhpL010eGJpY3pSam4iLCJtYWMiOiJmM2U2ZTcyYzcyOTU4MWQyMmI4NGRhMWU2ZjRhOTg4Y2VhOTQyNDk0YzJjZjJjMDBlOTQ4YTZjNjliNDRkODA0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9pMzlHaDdndWlab0R1Tm5FNW90eEE9PSIsInZhbHVlIjoiOFlsOHJnUHJiWWRsS3ZxeWs1WEMrTFk3VTVGT21Db2pFRXV4UEQwUGdGRlYvLzcvd2NvZFk5aWswa0M1Zk5aZUl6eGpUa05yU3Y1d3AwNW1mWG1PZlE3byswZUpsaWx0RDc4UWM2L0xYcE44UjVWSFE0eXBaQzRvVGVFa1FOUTJTQ2k3M3hxYjl6bmZFK281Vm9sNTNDSVcya1Z0RVdXSmJCNlZmRmhaVUpqbW85RTRHenJJazhlSmExaFpjVTRHQ1JjR0tveDV0L3RCSWhaOVhHWHRYMWEvTWkvS0JrUGo3T0FVWk12bGRlb1BIRThyeHBxMlUwNm1DKzFrRFhyUTB6OHZ3aXFvVFRQdHQrZFpuaHR3Y0huUEVReCtiSzRTRUhkV1luZGJTSWp6NkxuVDhKcDdLelhISiszemM1RmI2U0hZU3ZzZUZsUDR6VkkvOTB2QkxiUzhCRERkaFRqeEZKck9XbzJrVmEzcngveHB2UWhFOGp4Nm5lY3YzZVh2RkxobFhKNUNGMFdPajY2aVB4K0RNQmtIQjhNRlhORVVRWFVVZWFzUy8xcEVzTCtEUjAxZytSUjhhcTNaY3B4V25VdStwSmFpUU1sK1Q1QXkyZjNQV1B1aEpPM1B1ell2RHFTWWJPVEN2ekZwQ3VXVmZWMVpRcm51aVJUM0JUcVkiLCJtYWMiOiJhMDczM2YyM2YzN2ZkNTZlMmZiNWNlNDk3MmY5NDcwNDVjMTllYjBkZmYyNmU5MDYxZDcwMjBlZGM0MmI0ZGNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8126385\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-15904425 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15904425\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1287264662 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI0bkdBSUhmZ2ljclRlZkRzZGhGZlE9PSIsInZhbHVlIjoiQXNsMmJPcEpPVUtUSnF1ZnF5UmxiTG40NWYxakZqK1ZCTWR6UEpwZ3RGSWJZZnFVUHRDdDExa1dyd0hXUENxZmMyVGJMWjdIeHpZQ2N2dDVVVHF6OGdBckVkd1dTV1Z4dWtwdHp6OEdiWDV6NTNocUloQnpSaDFZaVFadS96NzlaR0dDNXRYQ3Uwa2ZhTksveFREdWM1TjhKQ1pqVzVqOWhtNW45N2k2cnlSZXhMMUNkbUg0ZFk5WGE4NC81eno3RkRtMHJoQnpsR25RT3RUUUFyaHkwWlJQL08zenVDRno3NlVGOFk4UWVpVTkrc096T0pkdFdqQWRvSzVrUW5oeDRtRHg2WG01d2xILzhMNW5FUnpySS9MaDJsOHhPWXNWa3l3dFJlZGNwcHlyUVZ5RDdZdUYzd25JaXlnQjdmVk9CYmZXZytuaGIveFlVNW5OL3ViN2xNckF6TUwrV3NBVUI5azBDUFpKdWxFL0oxc2x0alQ5b3creG9oQzBJLzd2emVxVUxxamx0Y2crRnJXTzFQNGkzZ3JQSHljTWF6dG5IbGxta0xHaEI0VGVKaVo4bVFSL3kzMkhJaEFrbWlKdmZhQU5oVDkyeHBDWWRjRVRCUWVhdHl4SmZuWnl3ZCszZS9ualhjc0RUR3lDTmE2RWk1MUNzVlZMYUpNak0wVzkiLCJtYWMiOiIwOWFhMDYwZmNlODZhNDZmMmYyOWNlMWZhYjJiNGQxMmIyOTU4MzAxODAzYmVmMjM5ZjYwMDExY2Y5NmVmNmRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxrOW81eTdQQ20rTHB1YnlxN21QcVE9PSIsInZhbHVlIjoiU2VJamY4SlN4TFcrTjcwZmlIeFloWkxMcDJtZiswU0hvNnphemZjRDgzSkdDUy92cXgyY2pJd1h3N3JGN3UxWUpjU1kvcTJaU0Nwd2xEWk5UeEt1SmV6VHBmU3picmc5Zm9qNlVVa2NRNHJEMDFaSlVMYXFKOUdaRnZ5RUpRdllyZHMzTVVKbEZyd3NVWm9JZmdYeDNOTy9vWnZqTVNmdjBlMjc5MGlvU1M1Um12NjhRUmVJZzdUQ2dUd2xmZWNnNjNkRlRrSXRTSUJWbUt1Q0dkMWNYSENpV3JoZlVEaitmYXhtSzAveTlkMzB2eG1HQVQ0WVZFUHQrVnZDdjkvK3NWRmJMZVp4WlJmN3pXNVdZdjdxRk5Idi9rVmNGREFVb1kzMnZZWUt1ZVNOQm9yWkV0eE1RSTRSWklUV0JWeC9zWThLUmF6TFZNa3E5NSs2M2kxMytsRmNkUnZQaTAwRmhYWTlGTkJBNDBUY3NqeE5ORXFtT0dINk5KVWRaTGxCcVkzV0FwanByeXRLRnAwOXhZeExMMlZ2MWFGN3BTdzkzbFM0ZGVCTG9WUFJkRFhZdEFMeElrQlNKTS9UU2xsTnJVREt4WmRHcVR0cjZOcTNiUXZpQ2M0ZVR3aG9yd3E5M3FSN2pDbUd2cmlWU1g1dlBZVk1ROThvM0g1TzZxZDEiLCJtYWMiOiIxZWFjNzJhMjFjNWFjYWQzZTNjMmI4YjczMzFmNTExYTc3YzUxZTA3M2FkMGM3MDI1MjRhYjk4OWE5YjU1YzVkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI0bkdBSUhmZ2ljclRlZkRzZGhGZlE9PSIsInZhbHVlIjoiQXNsMmJPcEpPVUtUSnF1ZnF5UmxiTG40NWYxakZqK1ZCTWR6UEpwZ3RGSWJZZnFVUHRDdDExa1dyd0hXUENxZmMyVGJMWjdIeHpZQ2N2dDVVVHF6OGdBckVkd1dTV1Z4dWtwdHp6OEdiWDV6NTNocUloQnpSaDFZaVFadS96NzlaR0dDNXRYQ3Uwa2ZhTksveFREdWM1TjhKQ1pqVzVqOWhtNW45N2k2cnlSZXhMMUNkbUg0ZFk5WGE4NC81eno3RkRtMHJoQnpsR25RT3RUUUFyaHkwWlJQL08zenVDRno3NlVGOFk4UWVpVTkrc096T0pkdFdqQWRvSzVrUW5oeDRtRHg2WG01d2xILzhMNW5FUnpySS9MaDJsOHhPWXNWa3l3dFJlZGNwcHlyUVZ5RDdZdUYzd25JaXlnQjdmVk9CYmZXZytuaGIveFlVNW5OL3ViN2xNckF6TUwrV3NBVUI5azBDUFpKdWxFL0oxc2x0alQ5b3creG9oQzBJLzd2emVxVUxxamx0Y2crRnJXTzFQNGkzZ3JQSHljTWF6dG5IbGxta0xHaEI0VGVKaVo4bVFSL3kzMkhJaEFrbWlKdmZhQU5oVDkyeHBDWWRjRVRCUWVhdHl4SmZuWnl3ZCszZS9ualhjc0RUR3lDTmE2RWk1MUNzVlZMYUpNak0wVzkiLCJtYWMiOiIwOWFhMDYwZmNlODZhNDZmMmYyOWNlMWZhYjJiNGQxMmIyOTU4MzAxODAzYmVmMjM5ZjYwMDExY2Y5NmVmNmRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxrOW81eTdQQ20rTHB1YnlxN21QcVE9PSIsInZhbHVlIjoiU2VJamY4SlN4TFcrTjcwZmlIeFloWkxMcDJtZiswU0hvNnphemZjRDgzSkdDUy92cXgyY2pJd1h3N3JGN3UxWUpjU1kvcTJaU0Nwd2xEWk5UeEt1SmV6VHBmU3picmc5Zm9qNlVVa2NRNHJEMDFaSlVMYXFKOUdaRnZ5RUpRdllyZHMzTVVKbEZyd3NVWm9JZmdYeDNOTy9vWnZqTVNmdjBlMjc5MGlvU1M1Um12NjhRUmVJZzdUQ2dUd2xmZWNnNjNkRlRrSXRTSUJWbUt1Q0dkMWNYSENpV3JoZlVEaitmYXhtSzAveTlkMzB2eG1HQVQ0WVZFUHQrVnZDdjkvK3NWRmJMZVp4WlJmN3pXNVdZdjdxRk5Idi9rVmNGREFVb1kzMnZZWUt1ZVNOQm9yWkV0eE1RSTRSWklUV0JWeC9zWThLUmF6TFZNa3E5NSs2M2kxMytsRmNkUnZQaTAwRmhYWTlGTkJBNDBUY3NqeE5ORXFtT0dINk5KVWRaTGxCcVkzV0FwanByeXRLRnAwOXhZeExMMlZ2MWFGN3BTdzkzbFM0ZGVCTG9WUFJkRFhZdEFMeElrQlNKTS9UU2xsTnJVREt4WmRHcVR0cjZOcTNiUXZpQ2M0ZVR3aG9yd3E5M3FSN2pDbUd2cmlWU1g1dlBZVk1ROThvM0g1TzZxZDEiLCJtYWMiOiIxZWFjNzJhMjFjNWFjYWQzZTNjMmI4YjczMzFmNTExYTc3YzUxZTA3M2FkMGM3MDI1MjRhYjk4OWE5YjU1YzVkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287264662\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1519396633 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519396633\", {\"maxDepth\":0})</script>\n"}}