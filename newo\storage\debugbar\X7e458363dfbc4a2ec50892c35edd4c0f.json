{"__meta": {"id": "X7e458363dfbc4a2ec50892c35edd4c0f", "datetime": "2025-06-08 13:27:21", "utime": **********.467985, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389240.16061, "end": **********.468013, "duration": 1.3074030876159668, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749389240.16061, "relative_start": 0, "end": **********.322151, "relative_end": **********.322151, "duration": 1.1615409851074219, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.322172, "relative_start": 1.1615619659423828, "end": **********.468017, "relative_end": 4.0531158447265625e-06, "duration": 0.1458451747894287, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45396096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0073, "accumulated_duration_str": "7.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.413439, "duration": 0.00512, "duration_str": "5.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.137}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4419959, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.137, "width_percent": 14.932}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4498339, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 85.068, "width_percent": 14.932}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1136340293 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1136340293\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2146995120 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146995120\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1039424472 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389236177%7C21%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFCN0RxY0U1UzJQU2d3L243bDBhOUE9PSIsInZhbHVlIjoiT3lsRHlpYzFKamxjeFl6QnZQbDRXUVhPc09vcDJ6RFpwNUVVUloyTitONkd2ekl5R0NadnVHYzNWSXFCemplWGFEWTc0UWw0eUNFbS9CNlNpd1pjeHp4WEhaUFZRMUsvRkhaalZmTnNVN0dFQUs4ekpUVWE3RUZjYVF3RzAvS0NHYXovVXVvZmRocDVXcmcyTkhhMFZOelRyYkw0aXpGd0owZU5EUUEvVDVqd0pJUTQ5T0VxRjRNMEtWVFdQZlFZYmFQWnRJNG1nK09qNWxHdnhZQ2hmY3VIclJpS2NuT1VmeERseGs4aTFkMC85bVZyRVBqNWd2RHFuNVhyN3JjdzAzNzZSNUthVkg1WGpZdkV0SXhFbE5Vd0VobDgyT0w1aFN0VG4yZW92R2tRUE1KRCs5QUhuVWRDbjd3TXVycTU2YWh6NUtidlNCTVhQWFRrWWl4L0tLVHZLMmVXSlVFNEEvRlJjR0FzYnpFNEQ2UkUyRTdjNVYrYnVvaHdRK2RLb25XdDdTMTFTVSs1Tjg1c3RkZGlIdnBUSTk0QlFDSGxHU0lkczJiSkdJTTEyczZ2TURnMXlSUDkzSURweFMzSjdqaDZLbHcybGIraU56TVpqRFdMMXBYN3VyRXArVHV0VWZBT1lwZnVqOUxYUlRRUUFsR0Zkc0VJQWtJaitOemgiLCJtYWMiOiIyNTUyZTBjNTNkMGQ1MjQ0YmIyYmQ3M2UwM2FjOWE1ZjgxNDBiNjNhNmNkODQ3MTY0YzYwODgzYjk3NTYyOGViIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iks3SkxUTjVRdkRlMEx3d09OdmxKZUE9PSIsInZhbHVlIjoiMGNpRXV0N1prU21BSnViOHZKOG5tdXR6ZjBVTjlYbHJpYnJYSUxhWHI5UmhZUVcvMzNqUEZuQ0Z6eEVobExEWFE3UHM1L2YrOTZjTUlUbld4L0oxWTJvS1BBUmcxemtmV1BjREUzdlJDcVFBNlJIR3p6N3VlSnZ0SEg1bkRCSndnQ0U4d3dlVWpYYXJPZ3FuM09aNHAzMTlHS3RPR21QV2N1a3pqdWw1NXJTNGxKUG5vYlEzMCtCVnVIVXNSeGNzWnNhakVwaEVtSVRiVWpTTlQwcERwUG5Dd3dKZGo1ZFQ1UzNtNHFTWUplMytYWlFBbDk3VW1adGVkV0FJdFFSMFNrd29aUVFqQzYyRFNLc0ZCM3NtVTN4MndkOGRKTytOVWpzYWoxKzBGYnpEb205UWNMYUNuODNiNzM2cmhZVDJDNDFzMmFZelpoSTVseGtrbStCSDdpKzlEQzBxUHdrSHFQazI2MFYvOFVRS0VmSG9JdkdGbGNIaVgwT2Q2bHIwVTI5VzhVR0dmU2hHaWJtb2xXS2s1MDBwUHNBbDBYMzFxOXFmQnFWc0RoMVpUZ0IvR0ZmZ2dDOHdQOU0vQ2ZsZ0VCVnNDN2R6bWR3UlgvUUhGNUxPc2l6Q0tONG92aDB2Q2dqM3h2b1lTby85Qk91VUgzMEM4Nm4rMWZBbE5oSnMiLCJtYWMiOiI3MjBjMmMyZDgxMWQ0NTJkZjRiZjY1OTg2Y2JjOTA3YWYzMmNjNGJkOTcwNWQ4YjM2ZjdiYjBmY2I2YzcxODc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039424472\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1829340567 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829340567\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2076560240 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitKZXdRdkdtOWZ1R0YzZ3ZGNUhTa2c9PSIsInZhbHVlIjoiM2NhZlRtQmdXdzF6ZC9qS3VJcUxXK045YlpxYXcxbzUyTVV2UzB6Z1NKZ0c3WXZCYzJqeFJOU28vY0l2L2ZUK1pDV1RRRlB0Y1RvYVVoUm1Sak5NZGIzNWg5ZWlTMDFENTZHcy84NjdLZVhxSmExcW0xL2UyWmc0dDBRMUVrOWNSL3h1Yko2TlIwMGNqWmIzMldPT3IvZzh2emRIN3RwdDJ1dFpTM3plaTNqNWY3OTRlR0ViN1h1cm81UWw1ZERRdURaMGhKNFMzeUFOUGFrMk5scHN2NnByY1lyN1NUN3VRMDhuL3crcWRRZ1EvTGJENU0xVEt6TmVPVXFNdzNjTDhlU3RCaXlQN0tXSHZpcmRkSDRqbDJuUlNuSTFnTytOZ001SVg0Mnc1ZCtxd0tvNmZvSHhoeGY0YzNweHBuNVVjY3F6MmVUVmRjU0oyZU4zN1FlVkhEaEtlaWJzbEYzZDJrV1d5MXV1Z29GTDVaYkViQjdLd0Q0QTdwc1R6Z25Na2dTNHpiUDdoNDkxL0FnanVTTHBuZWtMbmNhQTM0NXMzMUhkQldpNTd5VHZIRXNxS3dsZjd1cnNjRDBNVHpubkJUWWNRN2tMVlFnRGtXaU96ZWlvK0x6UytHT2FJaHBHWXBIdjJMUlZhYW9wcGg2V0d6S1kwRzFpR3l1WVQrTysiLCJtYWMiOiJmODAzMzhmYTliYTNhNGEzOTU0Nzk1OTY5ODdjNGU5MmRhYzM5OGM2ZDk0Mzg5YTU0MTZkNGJkMGZjZjM1YWEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZYeEdyNDVNVXdDQU5GaDFCR09nM0E9PSIsInZhbHVlIjoieDExWlpTQWFNQWttRXhjQk9INGJwaFI1WXl3NFh2RGJnVGpEaG82Sjh5b3JGbDRyK2VCRDhzMmlXOEJTMU9POWI0Z24yMHpzNEF0ekErZDFBdXloU2E2YTF5N0wwVWIzcUxJbGJZZnpHMDgrcHlSeDRpcHZuVkE0T1hDckNhRmxxWWFxRUZvSFpKRVVaM1ZaZFZ2K2tCUjkrMmVld0kyc1IzQTRNd0tCRGJBODNwSC9jL2hQMlJDR1kzdXJOMUlNR3hOUERlbnY2TkhPQzg5bEFxMDRKamdVOEZpaHFOdzRRQ0FRQjZTRitnUER2RU9BV1daRUp2NkdaZ1hOZVJBUGJmazFhbEd1T3ZZWEg0Qy9QQVhITU9wWjRBNDl1K2JIamRWckhTa3JjUjFSR0RmZFN4dVpDUUY2VkNRajQ2SlQyRkRTMUtBZVJOMmFMVkV0L3hIM3ZPRGpkWm5CbzBJbmtodmFPVlNGcm5LQ3h5NFdRd21ZVVBqSUlUYjN4NVhKTWdFVWNTYVR1bFgwdVRqa3dFamVBd2lVdzlZUlk4LzRyQUVNWTFEUXBpcnNVdmh0TjgzVFNjeXdVV2ttQ01ObHJOamRCbDZHNXppZk8zcnA4cGEvZEZwZ2dkaVhxMHNsZ2pnaEplOHVjSnhJb3JSYitGa3YvWkhid2hkNVBucE0iLCJtYWMiOiIxNjVjMzNlNjY5OThkOTFkYjg1ZDI4NGE3NTE3MTU2NmQ5NzJjMzdiYTU5ZDEyNWUzZjcxYjNkNTE1ZTdhMjJlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitKZXdRdkdtOWZ1R0YzZ3ZGNUhTa2c9PSIsInZhbHVlIjoiM2NhZlRtQmdXdzF6ZC9qS3VJcUxXK045YlpxYXcxbzUyTVV2UzB6Z1NKZ0c3WXZCYzJqeFJOU28vY0l2L2ZUK1pDV1RRRlB0Y1RvYVVoUm1Sak5NZGIzNWg5ZWlTMDFENTZHcy84NjdLZVhxSmExcW0xL2UyWmc0dDBRMUVrOWNSL3h1Yko2TlIwMGNqWmIzMldPT3IvZzh2emRIN3RwdDJ1dFpTM3plaTNqNWY3OTRlR0ViN1h1cm81UWw1ZERRdURaMGhKNFMzeUFOUGFrMk5scHN2NnByY1lyN1NUN3VRMDhuL3crcWRRZ1EvTGJENU0xVEt6TmVPVXFNdzNjTDhlU3RCaXlQN0tXSHZpcmRkSDRqbDJuUlNuSTFnTytOZ001SVg0Mnc1ZCtxd0tvNmZvSHhoeGY0YzNweHBuNVVjY3F6MmVUVmRjU0oyZU4zN1FlVkhEaEtlaWJzbEYzZDJrV1d5MXV1Z29GTDVaYkViQjdLd0Q0QTdwc1R6Z25Na2dTNHpiUDdoNDkxL0FnanVTTHBuZWtMbmNhQTM0NXMzMUhkQldpNTd5VHZIRXNxS3dsZjd1cnNjRDBNVHpubkJUWWNRN2tMVlFnRGtXaU96ZWlvK0x6UytHT2FJaHBHWXBIdjJMUlZhYW9wcGg2V0d6S1kwRzFpR3l1WVQrTysiLCJtYWMiOiJmODAzMzhmYTliYTNhNGEzOTU0Nzk1OTY5ODdjNGU5MmRhYzM5OGM2ZDk0Mzg5YTU0MTZkNGJkMGZjZjM1YWEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZYeEdyNDVNVXdDQU5GaDFCR09nM0E9PSIsInZhbHVlIjoieDExWlpTQWFNQWttRXhjQk9INGJwaFI1WXl3NFh2RGJnVGpEaG82Sjh5b3JGbDRyK2VCRDhzMmlXOEJTMU9POWI0Z24yMHpzNEF0ekErZDFBdXloU2E2YTF5N0wwVWIzcUxJbGJZZnpHMDgrcHlSeDRpcHZuVkE0T1hDckNhRmxxWWFxRUZvSFpKRVVaM1ZaZFZ2K2tCUjkrMmVld0kyc1IzQTRNd0tCRGJBODNwSC9jL2hQMlJDR1kzdXJOMUlNR3hOUERlbnY2TkhPQzg5bEFxMDRKamdVOEZpaHFOdzRRQ0FRQjZTRitnUER2RU9BV1daRUp2NkdaZ1hOZVJBUGJmazFhbEd1T3ZZWEg0Qy9QQVhITU9wWjRBNDl1K2JIamRWckhTa3JjUjFSR0RmZFN4dVpDUUY2VkNRajQ2SlQyRkRTMUtBZVJOMmFMVkV0L3hIM3ZPRGpkWm5CbzBJbmtodmFPVlNGcm5LQ3h5NFdRd21ZVVBqSUlUYjN4NVhKTWdFVWNTYVR1bFgwdVRqa3dFamVBd2lVdzlZUlk4LzRyQUVNWTFEUXBpcnNVdmh0TjgzVFNjeXdVV2ttQ01ObHJOamRCbDZHNXppZk8zcnA4cGEvZEZwZ2dkaVhxMHNsZ2pnaEplOHVjSnhJb3JSYitGa3YvWkhid2hkNVBucE0iLCJtYWMiOiIxNjVjMzNlNjY5OThkOTFkYjg1ZDI4NGE3NTE3MTU2NmQ5NzJjMzdiYTU5ZDEyNWUzZjcxYjNkNTE1ZTdhMjJlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076560240\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1731412406 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731412406\", {\"maxDepth\":0})</script>\n"}}