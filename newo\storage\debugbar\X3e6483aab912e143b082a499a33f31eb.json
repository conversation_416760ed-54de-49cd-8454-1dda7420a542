{"__meta": {"id": "X3e6483aab912e143b082a499a33f31eb", "datetime": "2025-06-08 14:14:57", "utime": **********.585897, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392096.254102, "end": **********.585938, "duration": 1.3318359851837158, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749392096.254102, "relative_start": 0, "end": **********.437854, "relative_end": **********.437854, "duration": 1.1837520599365234, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.437879, "relative_start": 1.183777093887329, "end": **********.585943, "relative_end": 5.0067901611328125e-06, "duration": 0.14806389808654785, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43918496, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00561, "accumulated_duration_str": "5.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5450191, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.036}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5607638, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 80.036, "width_percent": 19.964}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-371018140 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-371018140\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1459069464 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459069464\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1875410483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1875410483\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-362156601 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749391833855%7C52%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inl6RWl3U0MwMzVxcW8xbXBBeGZ0SlE9PSIsInZhbHVlIjoiSi9uTkJGZGNmYWpUaURvNUs5ajNBdmF1L01hSFp5UVcwS2lLMUN2RmhqZXdZRlljQkhRV29OOFErb0pUaUZYK3NSQ05OMllQTkhVbkF5RVVibjVjRjZ6Q2NrRU1JUFlqbWQ1dFBsNW11OHRIemY5ZWk1RlBBUkdBYWxXOVRMZUNrOUlXa1Y0MUV4bHJqTVl6ajB0NHNPMXM1TVRyUVJLT2xiRHlYUThCZlJ0UVZQQW1BbWNHbVIxWG0wSlFLZGhuTzZFVVdhNVJYV0swclZSSGwreHhKUStjQUM4clRNb1pLbGNDaWVPL2ZMK1hFSHRDWGZOdUVSSlZPNlFtclZVbGNKeEFXbDdDZnkvVE83cmJRUEk2RUp3SDlkWUZuQ2dGSDRrSS9rcFlsMyt5Q3EzbEozaDhuMnh2c1Y2RllUN283cmkrUDFERWU2NkhYT3lnbHFtSyt1ZURlbjZLaWs1YUczenZSakwwQ1JwMEZhc0hDNklOUHE1SjJNRnk2a25GcFZGZEVPNE1JRlNxYlRPMFVFZ2sveDdpY3ZJSjFlTWZHVmpMeTBVRlBpOGdoeXdQUFpHL21PWnVocGdFa1JsSThiTWtlaUNnL2EvUmtmZGdOcnhJc0lNblM3TklLY081ZVNURzVtS1hRaUs1aytGYXNlaUtramFjQ1ZNeHJtdEciLCJtYWMiOiIzZTJmYWMzMGM0NWY0NzRiNTI1NmJlNjdhNjFjMDZjNjE4MjExYWYxOGVmOTg4ZWFiODRmNTJmMDViMjZhYjdmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlhJb2NIMDgvek9GZVRKVVEzSkQyRVE9PSIsInZhbHVlIjoiU0JyVzk3bldNdXNocllZN1hNUk9hTUEzcXVKZmNWNjE0N3BQeThFdmt4Z1N0cEhOTTRyS1ExcXpLYTlwMWJDTUk2MEZiTzNUNmROeWF5SmZYNzM4Z2ZHcW1wNFVxM1plL01HVE9NMlZSazA1dkdCaitKVkVtdW9vRzFjNk82LzExSFZQZHBENVdmTnFlR1NFMWxmMmhHcysrcmFObHBFM2lsY3Y5elQ3OWhQTDJCUTU4eWl4dmNnWjdxTTNERmNhTExkS2svbTNabml4a3dEY2p1TUZob3VLbWc0dk1Xazl0NzlqLzhkQTFuQVhmeWcvZGladTBQZDBPQkVFcXZPZ1BMVlVMZVQxdEM4djd5akphOHp1ek5mNzQzZTR4RE1RV1dsaWFlU1VvZS9rQU0zL3JyaGVNbWNRbElFM2NmME0rbGhKbG9yM1VmVDJoc2xyejBKQi9EZG8xZjhrOFgwYnZhMlk4YytjSW1WakhjTDMxd3QvMUQra2Jnb0MwK0VsbHB0NlBRMlJTaFo1T09hM0NBcWhYZUtpSGVCMWg3dE9uWnc0SVZ5SklPck84b1l1djRIdWd3ejR3dFpBcVVFcVY0aHo5dkI4M1NCS1JsN0R5MkpXQlFQbnRHZXA0M2pmclN1MEpicDUrSDlabWkyMGNUa3ZWazZEY0NybWpVK2IiLCJtYWMiOiJlYjE5MTcxNzMxNjY4YzkxYjBkZDMxYjlmM2I4NDFlNGQxMzJlZmVmNThiOGUyZDdjNGE4MDEyNWQ3MDQwOWMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362156601\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-4656281 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4656281\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2024521113 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:14:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdzZXFtQ1dBcHlGQncvUFlVa3BkTEE9PSIsInZhbHVlIjoiY05sQkM1M3l3cXZha3l0WUlJZkthaDIvWDhNanFZZmRocFI3TUc0Sjl3MCtoL1hDb0NCUCt5Z1Y3dmxMQVBySmV3YmpSemllMmZKTk85Yk1udzdqd1hBTnk5MGhRbnVZQ2tmdFNaUjdVSll1aVlidW5uMnltbU9mcWgvZjJjaitjK2l4R3owQW80S3BteGhSbkNoVzRlWDRoR3oxTEFZZm84eFVkbk55QVp4dm1xZERQV2pKRmFoT3NkbjJadjJ3OUV6ZGJoQXhCYlFKZDhVT1pkdG4rTno4bTIyTDd6Zm83QWYyNGFOSW13N2JveW1kWWhTY2l5bUFPd240LzMvVXYvSVgzOE1TaEVsZGFWenJGRFliYXVmeHk2Zko4Y0dJVTRHSjQzV3l0UVNCRFF3allsNjVsYVluRDJSR3FjakRxNFovSVpJN3dKSVJDUEdMWU9Ec1h6aTNET05LTnU2Z245SXJodTRZT3ljRkplaFFHQTl5UmVrUndwdG5xSDhYbHowaHhsT3psZ2d1MEFvdjBGZFluODdCZm5CRVJTOWxSV3hGUnFzT2RYUm80MVVWYTdRbVBtNGlwNXNBWHBpUWV4YTJ2NG85bGcrRXZjQ2QzYkh3MnNUeVc2MUdWZVpZYjV5Nlc2ek9QQkZJWk9Uc3ZvcVhCRFJDZXgwNlhoTGUiLCJtYWMiOiIzN2E3YzQwNmUyYWFkZDhlYmE1YjVhMjcwMDFiYzE2N2NlMmQ4MDBmZGIwN2ZmOWMwZDk1ODRhZTY0ZDhlMGI5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRaODhYSGhtNjd3aVd2cDVIQ1lYY0E9PSIsInZhbHVlIjoidXEwdTFuUHluZ3JMT2h6NWpKUzE1b015Y2k4dnkrWVY3djl6bmlPeXRlaVVjT0FhODZWUmhKUnJ5NXV2c0FLOVhXOXkvTVNBNUF5VTk4OWZVYUlUVVltek5CZjA4U2JtbDdwY3orWmtWZWF2ekE1L1BaRktXdW9MZ3hVTHN3Y096U3JnWE9DWjRBbjN0aFlWQ0dGOE1KUUlleHhISWtOV1FhcUZvUnpjcTFDV2FJT2dTV0wvbGxyejA1RFg5V0xQQzlEeks4VG1lVTNwOStzbEdLN0xLR0Z2b3FPZGQwSDBscXZuNW5rVFpTVVVYMnZFNDRwMmRrSWZwaTBSLzJxM2RyNEtqa3dDQXltViszbUxYbzkyV0wwai9DRHhLRzJFMjdzSllndDl4b0ZrMzd5cWpNMXFOLzJ6eExyNGpyNVYvQnRCTzZMMkJwTy9FcjV2dkZ3R0s2NDR0OEhWQzlSNDNFQ1VaMytZNXMwQ0Fsb2pCZUp6VjFHMVAxS3VGYmJ1TXExck5GTmhna3FhKzBhMWMydmxNZCtsY25Hc1FJeEJ6VG9CTXZrK2tSSDdXU2J1NjBwSUd2djEvTFdqTXBKWk1QNkxESFZhN0NHMCtyK0o3UU5aWExMS0tWUCtiNjg4RjFvQzNpNzl2RlgwS1NCRGpiYWxHY004NkFvMGFFOE0iLCJtYWMiOiI5MDI3MWVmYjRkMGVhZDgzOThiZmU1NmY5N2E3YmJkODczNjk2MjNkNWY4NGE3ZDgwZTEyOTczYWE2MmM3YzA3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdzZXFtQ1dBcHlGQncvUFlVa3BkTEE9PSIsInZhbHVlIjoiY05sQkM1M3l3cXZha3l0WUlJZkthaDIvWDhNanFZZmRocFI3TUc0Sjl3MCtoL1hDb0NCUCt5Z1Y3dmxMQVBySmV3YmpSemllMmZKTk85Yk1udzdqd1hBTnk5MGhRbnVZQ2tmdFNaUjdVSll1aVlidW5uMnltbU9mcWgvZjJjaitjK2l4R3owQW80S3BteGhSbkNoVzRlWDRoR3oxTEFZZm84eFVkbk55QVp4dm1xZERQV2pKRmFoT3NkbjJadjJ3OUV6ZGJoQXhCYlFKZDhVT1pkdG4rTno4bTIyTDd6Zm83QWYyNGFOSW13N2JveW1kWWhTY2l5bUFPd240LzMvVXYvSVgzOE1TaEVsZGFWenJGRFliYXVmeHk2Zko4Y0dJVTRHSjQzV3l0UVNCRFF3allsNjVsYVluRDJSR3FjakRxNFovSVpJN3dKSVJDUEdMWU9Ec1h6aTNET05LTnU2Z245SXJodTRZT3ljRkplaFFHQTl5UmVrUndwdG5xSDhYbHowaHhsT3psZ2d1MEFvdjBGZFluODdCZm5CRVJTOWxSV3hGUnFzT2RYUm80MVVWYTdRbVBtNGlwNXNBWHBpUWV4YTJ2NG85bGcrRXZjQ2QzYkh3MnNUeVc2MUdWZVpZYjV5Nlc2ek9QQkZJWk9Uc3ZvcVhCRFJDZXgwNlhoTGUiLCJtYWMiOiIzN2E3YzQwNmUyYWFkZDhlYmE1YjVhMjcwMDFiYzE2N2NlMmQ4MDBmZGIwN2ZmOWMwZDk1ODRhZTY0ZDhlMGI5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRaODhYSGhtNjd3aVd2cDVIQ1lYY0E9PSIsInZhbHVlIjoidXEwdTFuUHluZ3JMT2h6NWpKUzE1b015Y2k4dnkrWVY3djl6bmlPeXRlaVVjT0FhODZWUmhKUnJ5NXV2c0FLOVhXOXkvTVNBNUF5VTk4OWZVYUlUVVltek5CZjA4U2JtbDdwY3orWmtWZWF2ekE1L1BaRktXdW9MZ3hVTHN3Y096U3JnWE9DWjRBbjN0aFlWQ0dGOE1KUUlleHhISWtOV1FhcUZvUnpjcTFDV2FJT2dTV0wvbGxyejA1RFg5V0xQQzlEeks4VG1lVTNwOStzbEdLN0xLR0Z2b3FPZGQwSDBscXZuNW5rVFpTVVVYMnZFNDRwMmRrSWZwaTBSLzJxM2RyNEtqa3dDQXltViszbUxYbzkyV0wwai9DRHhLRzJFMjdzSllndDl4b0ZrMzd5cWpNMXFOLzJ6eExyNGpyNVYvQnRCTzZMMkJwTy9FcjV2dkZ3R0s2NDR0OEhWQzlSNDNFQ1VaMytZNXMwQ0Fsb2pCZUp6VjFHMVAxS3VGYmJ1TXExck5GTmhna3FhKzBhMWMydmxNZCtsY25Hc1FJeEJ6VG9CTXZrK2tSSDdXU2J1NjBwSUd2djEvTFdqTXBKWk1QNkxESFZhN0NHMCtyK0o3UU5aWExMS0tWUCtiNjg4RjFvQzNpNzl2RlgwS1NCRGpiYWxHY004NkFvMGFFOE0iLCJtYWMiOiI5MDI3MWVmYjRkMGVhZDgzOThiZmU1NmY5N2E3YmJkODczNjk2MjNkNWY4NGE3ZDgwZTEyOTczYWE2MmM3YzA3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024521113\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1064912556 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064912556\", {\"maxDepth\":0})</script>\n"}}