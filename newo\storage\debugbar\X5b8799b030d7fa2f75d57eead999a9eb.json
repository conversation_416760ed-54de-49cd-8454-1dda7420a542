{"__meta": {"id": "X5b8799b030d7fa2f75d57eead999a9eb", "datetime": "2025-06-08 13:35:15", "utime": **********.284455, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389713.969116, "end": **********.284486, "duration": 1.3153700828552246, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749389713.969116, "relative_start": 0, "end": **********.115251, "relative_end": **********.115251, "duration": 1.1461350917816162, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.11528, "relative_start": 1.1461639404296875, "end": **********.284489, "relative_end": 2.86102294921875e-06, "duration": 0.16920900344848633, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45184904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027680000000000003, "accumulated_duration_str": "27.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2026231, "duration": 0.02509, "duration_str": "25.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.643}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.254912, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.643, "width_percent": 3.866}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.264378, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.509, "width_percent": 5.491}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1613378273 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1613378273\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-216827286 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-216827286\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1302441192 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1302441192\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-318236158 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjduK3JoMUlHK0VjV1pUNHlUU3Z2dnc9PSIsInZhbHVlIjoienYzSjdnUXY3NTl2aTdtOWRWTWcyYmFHSHlzMnFWbGZJRmI4Z1h6cHVJR21iVWxteVgyMWRHcnZJYkNYd3Nuci82am5oaDY5TXdlQUc5S1BMZk5YYk0wdG8rNFFCVWlkU3pvaVdoRTg3YWhTMURJS01CNU9Rd21kd1p0SDFmY1VxenNSRjVIdUNDZ21rZEl4Mld3c0VEdjJaWk1xdGVOT0oySFpiS2ttaWdqeGVUVEpNU1R0NVJ3cUhUWFAzQmxDL3lUUE1Ec3h1QnkrckZZR1JXMVNYVWZtdmN6L0JYdmdobk85NERQOCtZWXFmMEpqNXNZM3dOZU5FV2czbHQ1VnhyeGtzcDg4d3ZOeWN3ODdBUVR4bGd1VS92bzFHVlVLZGJKUDRVOHVLNDI2U3dhRGFlSnFnQzVyajhoRzlzbmNpRnErRmdzR0NtcGNWbHFYaDdiSE1od09hTTBLZ0k2NExLMWMwL00yK011SlU0MnFsSFZmc3l4dVNvNDJrd1lFa0hCenpaTmxTRjdGdFZhOWFtOWVKaG9IUkdFZ3pvWGkwRHhjbW83VWVGaXJEc0JjbnNXeXNDUlp2SmJLZUNTendEYjY5STNpYW1sK1BRaTJ6WmNzaTliM0dWRjhQSFNuWkt3Y3hTaXlDRmduTURtVHJ2MUNMWTdNUjdJM043ZWgiLCJtYWMiOiJiMmFjZTg5YTJmZWQ0NTdhZWExOWI4OGI5MTNiZTQ0NjgzOTc0ODM2MzdkMmFlMjQ2YWNiODEwYTM4YzQ1MTUzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlA2NEsrKzUzb1h1UTRjcWJjN2J6bVE9PSIsInZhbHVlIjoiSlI3MXZrc254TFhSZWs1aXkzQS95b3dwK3lBR3I0R1lMN0ZMNm52aXF4c2NsdDhBd05nZ0w2bHdXeEdVUDRaSVNZTWhsOFZCc1dZVDAxc1E3N0RoYU1KZE9wdGk2Q2FBTElSVmFGQVdMUXhaVS83U3J1aXhPM00xY3dyUTRjUjJIaERCTjBVSU91QzZBN3NSdEJtemphWnlwNWt3Tnp5SmlyRkF1bkd0WjlVdVp1K2tSNEpISzR1S2FUYnY3N1JjYk5OMFRUUWlnSXNVWHV0SHJud09DUHE0RDF2bk9FazVjTTB0VEFvMGVmbW9HZzIvdUYvN2o2TjlYTVk1VXM0MzlZQzJrWTJZZ3RtaVhNL1J1UGI0WkhRdVRKZGE0M09JYldURkpNc3prYVZzejRsTXFsWGMvWkkyb0xXVklMQkdsWGhkYmpKeDFnV2dDTlhXOFBTNG1mNXJQb3Q5c200TlRBZFFlNkEyU2hIcXM4L002cGJTUkdJMTJiYU9TWGloOEhhUXBDclYrU0NTQU5XWXJUdHJRbVhQVFltTGZuMVo1OTE2ZDVMRFYvS1FHL2hwdjIwS01OWmx5QTAvU3UvWTFGTVd0MVMzQUQvN2x2UUUrWUdWRVJXM2pncTA2SzJhblRwT0ZSKzRBU2daN2RJWlJaR1RHVE5LSTRKQlluc08iLCJtYWMiOiI3YWJlYjdmZjgzMTE1MGZjMTY1MmE5ZGE0MDU2NmViOTc1NDZkMjQyM2ZjOTEyZmM2ZDRmYWYwNWM4YmRhMDE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318236158\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-92897605 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92897605\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-918829960 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:35:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllqNjRVZmc5WlZPazI5SjkyVHNhdEE9PSIsInZhbHVlIjoibEhrblpaVDJqMjFwNmYyNzV0WlAzL0JPN2pIRy80R0FYMEtFajdHRHlvL1lEVlZaUHQ3bmYvaWcrM1VCYk1jSUN1ekFuZTV5ZlRlOWRvWER4NlJTSGl3ZUJZVktHRW53L1hOUmdiaUgxK2FBNkxyNldxUnlDN2pDRGRKcHhXcmE3ajVHWjlRYkVVSmRqeU4xL1hYRDJkdUphd09FRHRhMEJVRDVqYldROVc0ZnRwbDNoMWZXSzhBckVidnFDODNGeEgySVhHcmNlMjdJZkU1ZkZ1Y2JYYy9saFdZRUZ6SVlnL2pVSjc5VTZja0s0bHFqSjJTLzF0TEdVeDk1TTU3ZjRZZ09ONXgvSDNxZVN6V01OaS82dnYrMG9YRzRZMFNSUmF2a3AvQnZJbTlseEd1clZlZU9ZMUtuVjdXSUlBWSt2ZndQWGE1bVR6ZmRVSDBVUTJkN2h5VHpESVFIRHorbHIycEFDTVovUFZJL3RnTVJnbkZOd1B2cnhwK01GWUtLVnA1RjRuR1ZVbjNqNGJMM0lDdGx4VFY3NmFSUWdRSjNvVnhjRDIwSGptam5mbHphUmJZbmczd0pZd1h2enJqd2NvOENldVdTNktGS2VVM2h6M29aejdsVTdrOWtUSXBDT0ZLdHVrdkVORlFQaWJnaDVRcXplV2k4SkFETTIydHkiLCJtYWMiOiI2MmE4N2RmZDZiZTJkMjhlZjYwM2RiNWQ2NDkwNDgyNmM3NzY3NGQzODU1NGZkNWFiZjllMWNiYjQyNGQyYjA5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlI4YzlpaTRmQXJMS2syUzg2aUo5bnc9PSIsInZhbHVlIjoiaklibDFacEpnZ3F5ZXN4RzI2RDRzbDBqUnlGaENVUDFCd3RGZW9wZ0RqYm43ZnRlQnlSUUVIZHV2MDVhUHVueWV6ZDkzNGVqVHMycVAvR2Nmc2RiMXpnVlFHYjl5U0dkQzBrdDVqTHJMRHpIbnJjNlo1VDlwaEozWnBkK1kwVU1MbjFORWUrMHdpZFBENEVMSnhOUUE2SkJDNXNGeTVNOXl1TE1nMGN2SzlZektqMXZDMkVSUFQ4TS9NSXpjL1lqM0hoeDk2WUExampXRndrZzNwWFlQWUlSRFlBN0JFMXdjOGFnV1BsakJCWjJRNGJ2MEoyYndOdUJUZUNTZURuYVg5b1NWZ3ZvZlhNaGdOM2NmbzNxVEtFWVlHWW5YYi9YOXVXNDMzL28xeEU2Wm52U2VJWCtuOXZVTTNtdXhPUm1TRGhiNG05Z3RVd1k4cWUvR3J2LysyTEpwWS9yU2dxSVhCdm0vU055QnR5dmZvOHgvTFR6NmFNSG5GYkQ2NHJYZVA4RHpwSTlETjNqeGlPWWNzSnVIVmVndkd4TjJVVWExY0txMEtoVmw0b29WQWcvWktjSTBRYmxzOWt3dXQ4NWZjUndoWU5RQUt3ZStBT0F2WW5qTGxHZmxyemJGM2JnVjdMbU1pOUx4MXZWdEVSd3dPNlg5djd5MHNla3V5NzUiLCJtYWMiOiI2NWFkNDgzNTQ5NGE3NDk2MDNmNTkwMTgyNzVjODcxODIzMmQ2YWQ0NjY0ZmExZjAzNWZhM2Q4MmNmMDI5MzBhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllqNjRVZmc5WlZPazI5SjkyVHNhdEE9PSIsInZhbHVlIjoibEhrblpaVDJqMjFwNmYyNzV0WlAzL0JPN2pIRy80R0FYMEtFajdHRHlvL1lEVlZaUHQ3bmYvaWcrM1VCYk1jSUN1ekFuZTV5ZlRlOWRvWER4NlJTSGl3ZUJZVktHRW53L1hOUmdiaUgxK2FBNkxyNldxUnlDN2pDRGRKcHhXcmE3ajVHWjlRYkVVSmRqeU4xL1hYRDJkdUphd09FRHRhMEJVRDVqYldROVc0ZnRwbDNoMWZXSzhBckVidnFDODNGeEgySVhHcmNlMjdJZkU1ZkZ1Y2JYYy9saFdZRUZ6SVlnL2pVSjc5VTZja0s0bHFqSjJTLzF0TEdVeDk1TTU3ZjRZZ09ONXgvSDNxZVN6V01OaS82dnYrMG9YRzRZMFNSUmF2a3AvQnZJbTlseEd1clZlZU9ZMUtuVjdXSUlBWSt2ZndQWGE1bVR6ZmRVSDBVUTJkN2h5VHpESVFIRHorbHIycEFDTVovUFZJL3RnTVJnbkZOd1B2cnhwK01GWUtLVnA1RjRuR1ZVbjNqNGJMM0lDdGx4VFY3NmFSUWdRSjNvVnhjRDIwSGptam5mbHphUmJZbmczd0pZd1h2enJqd2NvOENldVdTNktGS2VVM2h6M29aejdsVTdrOWtUSXBDT0ZLdHVrdkVORlFQaWJnaDVRcXplV2k4SkFETTIydHkiLCJtYWMiOiI2MmE4N2RmZDZiZTJkMjhlZjYwM2RiNWQ2NDkwNDgyNmM3NzY3NGQzODU1NGZkNWFiZjllMWNiYjQyNGQyYjA5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlI4YzlpaTRmQXJMS2syUzg2aUo5bnc9PSIsInZhbHVlIjoiaklibDFacEpnZ3F5ZXN4RzI2RDRzbDBqUnlGaENVUDFCd3RGZW9wZ0RqYm43ZnRlQnlSUUVIZHV2MDVhUHVueWV6ZDkzNGVqVHMycVAvR2Nmc2RiMXpnVlFHYjl5U0dkQzBrdDVqTHJMRHpIbnJjNlo1VDlwaEozWnBkK1kwVU1MbjFORWUrMHdpZFBENEVMSnhOUUE2SkJDNXNGeTVNOXl1TE1nMGN2SzlZektqMXZDMkVSUFQ4TS9NSXpjL1lqM0hoeDk2WUExampXRndrZzNwWFlQWUlSRFlBN0JFMXdjOGFnV1BsakJCWjJRNGJ2MEoyYndOdUJUZUNTZURuYVg5b1NWZ3ZvZlhNaGdOM2NmbzNxVEtFWVlHWW5YYi9YOXVXNDMzL28xeEU2Wm52U2VJWCtuOXZVTTNtdXhPUm1TRGhiNG05Z3RVd1k4cWUvR3J2LysyTEpwWS9yU2dxSVhCdm0vU055QnR5dmZvOHgvTFR6NmFNSG5GYkQ2NHJYZVA4RHpwSTlETjNqeGlPWWNzSnVIVmVndkd4TjJVVWExY0txMEtoVmw0b29WQWcvWktjSTBRYmxzOWt3dXQ4NWZjUndoWU5RQUt3ZStBT0F2WW5qTGxHZmxyemJGM2JnVjdMbU1pOUx4MXZWdEVSd3dPNlg5djd5MHNla3V5NzUiLCJtYWMiOiI2NWFkNDgzNTQ5NGE3NDk2MDNmNTkwMTgyNzVjODcxODIzMmQ2YWQ0NjY0ZmExZjAzNWZhM2Q4MmNmMDI5MzBhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918829960\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1109403508 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109403508\", {\"maxDepth\":0})</script>\n"}}