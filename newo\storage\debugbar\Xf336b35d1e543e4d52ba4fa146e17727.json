{"__meta": {"id": "Xf336b35d1e543e4d52ba4fa146e17727", "datetime": "2025-06-08 14:19:38", "utime": **********.603079, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392377.291051, "end": **********.603109, "duration": 1.3120579719543457, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749392377.291051, "relative_start": 0, "end": **********.409124, "relative_end": **********.409124, "duration": 1.1180729866027832, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409145, "relative_start": 1.1180942058563232, "end": **********.603113, "relative_end": 4.0531158447265625e-06, "duration": 0.1939678192138672, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45602544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03093, "accumulated_duration_str": "30.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.502943, "duration": 0.02847, "duration_str": "28.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.047}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.558704, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.047, "width_percent": 4.203}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.576786, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.25, "width_percent": 3.75}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 18\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-98115460 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-98115460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749392124871%7C53%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndSSm1XLzZ6dlBpeXN4N01yaExvc2c9PSIsInZhbHVlIjoiZ3BiWVdjN2ZLM1RINzFVQzVWKzFPV1d6QW0xTm5UUmpKWlhwbjJpNHkzWlZFdjRpaEFHclVVU00yakRueTFOdzFVdFpKaWNSOXdxVHdnRkNiOXBVc1k4NFlpNitFUlVKRytyaXBNellWemsrNTZtVDBlMmRWOW1rby9QOUs5OENzUWdobzFScHhDcXJZUHJMdmFYOXo0M21ueDNMeUpKaE8wc29LcTdWWmZCZUhXYUlhamF0TUZqZnlCOWF2anRXOE4xalI5eW9mM1N4VlB2dkx6N3cveHRGZmRxVkFzT2Z3NnRWR2Vqb3JqVy9hSFVpQ0NYUVNSenBvdXREUmFWakV5N3lGa1NrRWZWZEhDQkpRcmkzMW5HdDdLVVpKd2FrM2VYcEZ0dXJzamtQS1RRaUlzcG9JVTNzS1dzNEdzZTRkMEEvRlBsWU9zYlRuZGk4TURzRXRpeXA4dTNUSGpEakNvUy9hVklYVk5rOWhDd0JUVHZaRFRMeXE4dTYyNU5nMnV0RGh4bFlyN2swbDExSHRwVkp6bnczcFhVTHFMUld5TWowMGp4empXMmVBN2w0SG5wVHpYTGtLRlkrWUpqQ3d0eml1TGlsMlB5bU8ranhCUlNWU1RscU82WFU1VDZwMDd1TzVJTVViSEVzMTVpc0w3ZnArY0FYY001cTY1dUciLCJtYWMiOiI1NDM3MWZiMzJhZmUzOTU1ZGQ4MjI4ZjE4MGRjZGE0YWIxMDhjNzM3NjJhNzA4NWM5M2U3NmI5NGFhMGU4MTQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndLZG1menhselIveXJWb25ka1BZRkE9PSIsInZhbHVlIjoic2U1Z2pYWWhmOS9icWVwL0VSdE5DcVErOE4vTDZvbmU3cGllOXJnbjBIS2daSzA1a2UxSjcxMzY3VVF4V0JnazIwSE50OWVCN05hYVpPR3pVd21GM0t1Rkd6TFVHMzBVQ01YaHJkTjhQSEVkZkM2NDR3NkhOOGZxRk05ZUJmN3BFSGIxNnFkVEZWeGc4VDdMTU5YOFFSdG5PSms3UTFXZGJoeWNPaGF4OEs3NU9TdjJiQjdjZzRVeUllOXRFeWNjTUpxVzFOOWpadm8yRk9wbE9TSnJYSGlIT3d0cEFSRUhzZFJZdkhvRWx0VXdHZUkyeFlBbGNGRm5pYzN3bnRYemRaTDQyeHpYd3BGMWhISEI3UXZ5R1B2S1dDSFFUTjI2T3R2cXVId3lqMkxieGErWitXQTZJUVlSS09NM0VmQjA1WlBFVDF6SG5BZlpQRDJUaTNiSXEvMGtKVnQyVmlpWU5kb3BNRWJod3puMTFkRGVmeWFDYWVVSmY5UlhFV2lOU2ZMWHdGVDNiMXVyUkpZTDQyQ1R6QWtOeFd1RjJDamhucE0yQUNnSjAyTDM0MWZ5SElmYnI2OXRBbmdxYVdVU2V5c0VXZzJrdzlRVkRSVDR6ZDFFS0pBY1ZMWWVqdDAwVVl4OFZJM1pOVHo1WUVRZEl5bDhLZExRc0QwVURWcVgiLCJtYWMiOiJiZGQ2MDgzYjAzOWYyY2YxNzAxMzlhOWVmYjEzMTAxYTA3ZTUyMzc3YjJmOWNiZmQ4N2EyN2I2ZjFhNjA3ZDE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1685236630 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685236630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2126762288 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:19:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZiK2lzQTM0K21yVXRKS2tKQ1YzQ3c9PSIsInZhbHVlIjoiS1g5aE9zbnpYMU5sRU5aSnZEVkQvNnhNSDlMaURUeG9PWTBVM1ZWYVJEWGdLd1IxWmF3VW12ak1acmJIMWg5enZHcmRDczZFeWVhK1E3ZlBnL3ZITFZBcndlWnF0dVVMY1htWFRYbkdGK3BrZzIwUnFVYklvNmJCNVRmTnBLakpQYzB4aW9QZVZkOVBZSmtoWlRGdjR1UlpsdkcwWDIyZDAvN3BXdHBweDFkaERGMFZpQUkzYUpjQWo3TjF2NHNVbElGUEYyNklBY0czMjZoMEhJR1NrbXFIeHZncjJpeSt4V01RUmluVTAzV0lnZEZPK2NMOXRZak1FMEtubkJ0OVp2OXVibDlZODRWWk5RYlRaMllSdlBQdTNicWh0SkFjTHl5bjFmbE9wY0IwWm0zN29vak1uZmc5UDl4cllRRk8zcVVWTmRLQ2gvUlY5TUhDVmJ0cExhNnpWWUIxQ3JwaUdnZTZOMnorcnFidWJhWXhhOTZPdzQyMDhaci9yTy8yVnpWNngwSjhneGxGdDJDM0I3WHUxZ2dXTFNjRCsxWXI4UDl0STVSOHJmS1R2c0NSbGNjdjZXay9qS1BELzk2Q0oxQjNCSlVQZ1d0SmdEV09wTkdhbzA4eWtwd24wVy9PaHplNlJjVmZUVlpOaWJsLy9oTVZ4VUJsTnR2Z2dxUisiLCJtYWMiOiIxMDJlZDY3NWQzMjBmNTFkMDM0M2IwMTBjNDQ3YzAzYjBmZjY4MGIzZGU2NTAzNTQ4ZjE5NWRmODgzNDI4YTM5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:19:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlsNC9jSEpyR3h0VldvM2J1MmNTclE9PSIsInZhbHVlIjoiVEc5ellrVlVTQWp4TWRBaEdmdXpQUk9FUHplbWVTM0daa0grQmJPbEI1emxVYzRBV3dUYmJYWit2TlhsUS90Wnl0b1doSnlsTXBMcUZiejdxZ21xVG5UVjFvcHU1TUxnVHExZGR1WVoyQUlMOGdGZzJxVVNMMHNjRTBmckFsOGVIUTJsWFJJa00xS3NtWkx0eThHTzBmY1ZRTHVkOVRNMjJ0ZDRrdmgvTTlTdzVrUFg4ZlpvOU5Qdyt0eVh6bjZTVldwQlRBYUZnUU9pR1BSY0FnNll5SDQ3UmZZeDEwRmFqc0hkcUJVMGoxYW4xWGFHV3FYSlJabFBpOEZUd3FuRXVIZzlIelZoNG9SNWF5OGlwQ1BsbmlZdFpBaWZCbU9BWHpyV01CWEoxSGtlZ0NYS3hLdzhSYVFQWk5KeXhkMTcycWtBN1ZzK0NtMzRQZWRlUVZUVUNQRGRpSWZMbnI0TzUvQk1abWZ0aFJqbVFPRnNTZHExVlFqQ2x2TnhTZWtWUDBuZlJZWk1kNXZrMW0xQ2xNZ242cFp3SzNzczdUdG1YNWVTZnRMUEJidmxVZURYNUw1MXRkS3BsZE5KdDBlKyswRFFQZWI2YmJpVkVJaFpFTUhtZ1ZRTmhPcGVwc3YvRWpDbnRPS2R6K0lKc1BQSEpVRWVPVVh6MlhEU0VJVkUiLCJtYWMiOiJmNzM1YmE4MmUwNDE1YjdjMzZlODQwY2FiYjNjMDViM2JjMWNjNzQxZTY3MzIzODRjNGMwY2M4NGU2M2Y5ZmQxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:19:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZiK2lzQTM0K21yVXRKS2tKQ1YzQ3c9PSIsInZhbHVlIjoiS1g5aE9zbnpYMU5sRU5aSnZEVkQvNnhNSDlMaURUeG9PWTBVM1ZWYVJEWGdLd1IxWmF3VW12ak1acmJIMWg5enZHcmRDczZFeWVhK1E3ZlBnL3ZITFZBcndlWnF0dVVMY1htWFRYbkdGK3BrZzIwUnFVYklvNmJCNVRmTnBLakpQYzB4aW9QZVZkOVBZSmtoWlRGdjR1UlpsdkcwWDIyZDAvN3BXdHBweDFkaERGMFZpQUkzYUpjQWo3TjF2NHNVbElGUEYyNklBY0czMjZoMEhJR1NrbXFIeHZncjJpeSt4V01RUmluVTAzV0lnZEZPK2NMOXRZak1FMEtubkJ0OVp2OXVibDlZODRWWk5RYlRaMllSdlBQdTNicWh0SkFjTHl5bjFmbE9wY0IwWm0zN29vak1uZmc5UDl4cllRRk8zcVVWTmRLQ2gvUlY5TUhDVmJ0cExhNnpWWUIxQ3JwaUdnZTZOMnorcnFidWJhWXhhOTZPdzQyMDhaci9yTy8yVnpWNngwSjhneGxGdDJDM0I3WHUxZ2dXTFNjRCsxWXI4UDl0STVSOHJmS1R2c0NSbGNjdjZXay9qS1BELzk2Q0oxQjNCSlVQZ1d0SmdEV09wTkdhbzA4eWtwd24wVy9PaHplNlJjVmZUVlpOaWJsLy9oTVZ4VUJsTnR2Z2dxUisiLCJtYWMiOiIxMDJlZDY3NWQzMjBmNTFkMDM0M2IwMTBjNDQ3YzAzYjBmZjY4MGIzZGU2NTAzNTQ4ZjE5NWRmODgzNDI4YTM5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:19:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlsNC9jSEpyR3h0VldvM2J1MmNTclE9PSIsInZhbHVlIjoiVEc5ellrVlVTQWp4TWRBaEdmdXpQUk9FUHplbWVTM0daa0grQmJPbEI1emxVYzRBV3dUYmJYWit2TlhsUS90Wnl0b1doSnlsTXBMcUZiejdxZ21xVG5UVjFvcHU1TUxnVHExZGR1WVoyQUlMOGdGZzJxVVNMMHNjRTBmckFsOGVIUTJsWFJJa00xS3NtWkx0eThHTzBmY1ZRTHVkOVRNMjJ0ZDRrdmgvTTlTdzVrUFg4ZlpvOU5Qdyt0eVh6bjZTVldwQlRBYUZnUU9pR1BSY0FnNll5SDQ3UmZZeDEwRmFqc0hkcUJVMGoxYW4xWGFHV3FYSlJabFBpOEZUd3FuRXVIZzlIelZoNG9SNWF5OGlwQ1BsbmlZdFpBaWZCbU9BWHpyV01CWEoxSGtlZ0NYS3hLdzhSYVFQWk5KeXhkMTcycWtBN1ZzK0NtMzRQZWRlUVZUVUNQRGRpSWZMbnI0TzUvQk1abWZ0aFJqbVFPRnNTZHExVlFqQ2x2TnhTZWtWUDBuZlJZWk1kNXZrMW0xQ2xNZ242cFp3SzNzczdUdG1YNWVTZnRMUEJidmxVZURYNUw1MXRkS3BsZE5KdDBlKyswRFFQZWI2YmJpVkVJaFpFTUhtZ1ZRTmhPcGVwc3YvRWpDbnRPS2R6K0lKc1BQSEpVRWVPVVh6MlhEU0VJVkUiLCJtYWMiOiJmNzM1YmE4MmUwNDE1YjdjMzZlODQwY2FiYjNjMDViM2JjMWNjNzQxZTY3MzIzODRjNGMwY2M4NGU2M2Y5ZmQxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:19:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126762288\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}