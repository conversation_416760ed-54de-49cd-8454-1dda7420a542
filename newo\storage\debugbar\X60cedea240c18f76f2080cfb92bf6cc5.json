{"__meta": {"id": "X60cedea240c18f76f2080cfb92bf6cc5", "datetime": "2025-06-08 13:05:55", "utime": **********.846231, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387954.577384, "end": **********.846261, "duration": 1.2688770294189453, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749387954.577384, "relative_start": 0, "end": **********.710203, "relative_end": **********.710203, "duration": 1.1328189373016357, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.710224, "relative_start": 1.1328399181365967, "end": **********.846265, "relative_end": 4.0531158447265625e-06, "duration": 0.13604116439819336, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43912680, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01442, "accumulated_duration_str": "14.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8055182, "duration": 0.01345, "duration_str": "13.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.273}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.827867, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 93.273, "width_percent": 6.727}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-74328618 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74328618\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1221952378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1221952378\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InU5TDBsMDZhT0ZsT1RoYm1BeWI2WXc9PSIsInZhbHVlIjoiRGlFT2VpejlqcEFvVGhmYjd3eDIxMlZOMVRIZk8rbzlMTlFKcTB2R1N5U0tMYW1IOC95NkJaTjhqODRYaWprTGJUNVlYamJpMWpMbUdZNE9iSk1udTYwYXFTVkhkQ0dIZGU3N3AyUzNERFUxTGZqdGo5UmdKNG9rY2pFUFh6WGVMVzU4b0NvbWNPSkdqQksraHpkTWxiakR2bHVKWG5MNVloajlZYzJTMGp5TzlqYkoyR1dzRGQyUW4zV2E3RHNuVnRJUkh0d29hRUFQTVlVQzJmcmlJamc3UFJwMjc5VkZtTzBNUVVxOEZ0c3UyZEdqY2xHK3liZ3dzUGQ1Zy81QnVkS1czQ2tiSGtIcFdqVG1BY0U3N0dzWnI0K1JFUWtLUzJWaFdXRTcxMTFwbnJBdDNNenpUYkU4L1FhVDk1OXB3UFZacko5Qmg2cWRUNFlFSnNtc1RwekNMbkxwa1NxUHVNdWFQdEx0L0tEczJiaVpjTHQwZzdjVWZPY2J0MkpXUW1zeVlPYmdLTTdiaXAzZkpIMmYrU3NYU3lucS91QkNpUUgzNEc5eXNSd2dvWFRFWUFJd0FxcGZQWGx6d3gvVzNrd21xejc3azY3NTFHakxTK0FITXhjWUMwc1NVaENoc3UzaWloNGZaeG53N2k4UDh0NHdIL00xOXJucTZYVkwiLCJtYWMiOiI1MDYzOTIwNDk3YmVhZWMzNTdlNjcxNjViYzEyYWVhYmIwMjNhOWJiN2ZiNmY1MGRkYjE0YmQ3MDI3ZGRiZTFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtGd1d2QWcvYmpGTHRmdllpSURvR0E9PSIsInZhbHVlIjoiZy9jdTgwcjhmckIvNm1kZTlOYWxhcXJrVXBxekN5ajU2cHhnRnN6bjNlUjBhVEdBZG9pWG1RLzRpT09MU1JZckkvWnlMSnRmV0F5NytpTXZZbDk1cUNvZlpENk9ucWdUSklXQTB1MUtTekxLL3NtTjBpUzFzSlBTUmlXV0g3RTliU2dPOWQ5L1pJRFZ5L0ZKK0o5KzNGM0R0QmFsTnBRNVB3NDJhRFdhaWtqNWJuRmREclViTVJRb1ZPRDc0K0M1VGhOeVNJeVBBOTU1QWpxTVp4MWY4Y001WG5sTVRFNVAralp1dEQ4ZWhYK3hGemlkbkUzY05zYVNvUU1hU1VFdXdDVFRna2xlVkFQdk5uc25ZWHNub1QwRnp1cnJ0RGdkSTdwU3RxUm95dW4xVWZKc1NIWVpGRjVETmRVOVUwNk9sV1MwK1k0bGpPU3RjMDIvR2E4RXZOR1ZSZ1kyVkxGQmt0YlViejRQY0NrZFExZ04vRGNlVHc0VlVLamZxM3Vrc3lZbHlERVVGUFpiV29FQXZuSFVzeHBDWjE1MTEzd3lJcXpEZzdiMHp1NEZ0ZWcva2dPZnNScEM3TDRpMmVNOFNNOHY2am9sTUx1OGxaRFU4NmptYU9QNHZEZVFDRmVKYmpRb2lQc0g1MjhNY0llckxrQ3N0QTM3RUFXMlhUd1EiLCJtYWMiOiJiOGQ1NGEwYmFiMDBmNGEwZWY1YWYyZTZmNDE2NzY3M2Q1ZDYzMjM0NDEwZWJjM2Q4MzhlMzEzNzdjYjU3Nzk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1342029370 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342029370\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1425363794 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRNNEN6K0ttYnhvVGpFRXQyWStkQ3c9PSIsInZhbHVlIjoibTBZL3lJWTd1cHZUYytTcjVLd2ZCaVVzQzcrdU5ZOFRVZmYzZGw2Y29KaWI5OElscmxLemFNdTN0d0V5bFBONkhPZnNZYSt6SkNWZytGM1VDeVhNdEpmUVE2ZzhGVUVrWjNGbDJNSlNSaTVMUmVjTG0yOGV2d0FyN3c3UWk3c0pycW15SC82MStqSmp6TEJWaEFVUGFtME1LTW9OeGxFQW1aeWNnOVhuMXRncHZVK1hIVVlOMk1nTDI5OU1pUU92WmJTTDEwSUt0VGJtd3l1U2NJK0JXY0d2TU1KczBmTnY1TXd4Q1VsdUVVTVhLYTZvRTIxWHFLZTJPaGRIaHkzbTNvdXdQU3YwRXNqdmttenp0R1hxenl6alB2YW50ZUNmWkxOdWVtekM0ZnpyWE80TC9peEQrVUY1TWlPQndnYmxHc1MrRTZla1lKcFZuNXNTRmF5TWRGQ1NSZXlYT1JkY1dNUCt0aVZFb1RERzZURjh5OVI1cE00Ymw2MVJPMjR1VnVNalFWdnFqRkxXekFTcFZRQ3dpR3VGOURBYU0xaEV5Tng2RmR5d2I0VExtN2U5cTlERFFjVEZzeUdCdTNiR1FxU2JzZHFGNkpQSlpQcHUxTjhTZlZaMXpEZEZybU42UUlYeGF6MHB4OVhwYlAzWVFxcWxNZEc2emtRaWVRQ04iLCJtYWMiOiI1YmY1YTE0OTFhNzYxMTE5MWJlYjcyMDA5YTAxYTc0OWJlOGNmYWNmNjE3NWZmOTc0MmRhMDlhOGU3MGFkZTQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFsVkFVZlpCbkVUVjd3aDlRMU9QU2c9PSIsInZhbHVlIjoiTVMvNFhPdGM4dGFwN0JtUVpqZjR1QnFFcXRrNzR6UURxTzNKTE5DSXpwSEh0TUloZjBPNnZQVlhJY3RnSU9jQWJCV3Y2RGQwNFJXYXhCdVlsSk1VN09xaVp6NU5kSkxudktvenNIZkVTRHFLN1A5Yi9QeW9pYU4vakRZQTRDV3k4WEQ1YkM4aEd2SGNFdkorcTREUlF6eUUwOHdaQlNOZ0d5OGZsbEdQRG9CdnVkSzdhMHV5a0Uvai9ubnJMWDBMdEh0RFAwTVd5aHFPcFlvQjNKQkFDbS9IQ0xWRmc3c1FNRGN2TWlqRmZBc3FTYzB6cGkvaVY4KzZXeGNNQXBjNk5tREU2SGQ4VHN4c3ZSOGFTWEh4MS9adDBiaGtCZFN2MHlXakZxMHBJYWRHdU11VG1yV2ZzdVg0anJCYlg3UUlvQ0lzL3JLZFdxWWNZVU1vTnEzYXpMTktBcndnYnlMa3M4MjlxZU9lbzBGakdXMmUzckhXVStQOFpyTXdScEYwd0ZnR2FBbzB2dnFyOEluRHVWczN1eEM5ZmhLbU9OL1lFSDhNQUUxVGRGWlRGdFN2S3dscHZNRmlBUWM2RVVuNXBIOFYwUXdNK2N1aU1iejd6TnVGZ2U5dTVxYzBodGZRcWpDWTFDTzZJVUs2MURSTTdtZ2ZhU1ljTkhJNTJvV0wiLCJtYWMiOiJiZTg5ODMzYTIxYmUyMzhkMmNlZGRjZTQzYjk1MWY2NWJiODc2MjYwMmI0YjVkYmE1ZmZhOTE4YmQxMzIwNWQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRNNEN6K0ttYnhvVGpFRXQyWStkQ3c9PSIsInZhbHVlIjoibTBZL3lJWTd1cHZUYytTcjVLd2ZCaVVzQzcrdU5ZOFRVZmYzZGw2Y29KaWI5OElscmxLemFNdTN0d0V5bFBONkhPZnNZYSt6SkNWZytGM1VDeVhNdEpmUVE2ZzhGVUVrWjNGbDJNSlNSaTVMUmVjTG0yOGV2d0FyN3c3UWk3c0pycW15SC82MStqSmp6TEJWaEFVUGFtME1LTW9OeGxFQW1aeWNnOVhuMXRncHZVK1hIVVlOMk1nTDI5OU1pUU92WmJTTDEwSUt0VGJtd3l1U2NJK0JXY0d2TU1KczBmTnY1TXd4Q1VsdUVVTVhLYTZvRTIxWHFLZTJPaGRIaHkzbTNvdXdQU3YwRXNqdmttenp0R1hxenl6alB2YW50ZUNmWkxOdWVtekM0ZnpyWE80TC9peEQrVUY1TWlPQndnYmxHc1MrRTZla1lKcFZuNXNTRmF5TWRGQ1NSZXlYT1JkY1dNUCt0aVZFb1RERzZURjh5OVI1cE00Ymw2MVJPMjR1VnVNalFWdnFqRkxXekFTcFZRQ3dpR3VGOURBYU0xaEV5Tng2RmR5d2I0VExtN2U5cTlERFFjVEZzeUdCdTNiR1FxU2JzZHFGNkpQSlpQcHUxTjhTZlZaMXpEZEZybU42UUlYeGF6MHB4OVhwYlAzWVFxcWxNZEc2emtRaWVRQ04iLCJtYWMiOiI1YmY1YTE0OTFhNzYxMTE5MWJlYjcyMDA5YTAxYTc0OWJlOGNmYWNmNjE3NWZmOTc0MmRhMDlhOGU3MGFkZTQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFsVkFVZlpCbkVUVjd3aDlRMU9QU2c9PSIsInZhbHVlIjoiTVMvNFhPdGM4dGFwN0JtUVpqZjR1QnFFcXRrNzR6UURxTzNKTE5DSXpwSEh0TUloZjBPNnZQVlhJY3RnSU9jQWJCV3Y2RGQwNFJXYXhCdVlsSk1VN09xaVp6NU5kSkxudktvenNIZkVTRHFLN1A5Yi9QeW9pYU4vakRZQTRDV3k4WEQ1YkM4aEd2SGNFdkorcTREUlF6eUUwOHdaQlNOZ0d5OGZsbEdQRG9CdnVkSzdhMHV5a0Uvai9ubnJMWDBMdEh0RFAwTVd5aHFPcFlvQjNKQkFDbS9IQ0xWRmc3c1FNRGN2TWlqRmZBc3FTYzB6cGkvaVY4KzZXeGNNQXBjNk5tREU2SGQ4VHN4c3ZSOGFTWEh4MS9adDBiaGtCZFN2MHlXakZxMHBJYWRHdU11VG1yV2ZzdVg0anJCYlg3UUlvQ0lzL3JLZFdxWWNZVU1vTnEzYXpMTktBcndnYnlMa3M4MjlxZU9lbzBGakdXMmUzckhXVStQOFpyTXdScEYwd0ZnR2FBbzB2dnFyOEluRHVWczN1eEM5ZmhLbU9OL1lFSDhNQUUxVGRGWlRGdFN2S3dscHZNRmlBUWM2RVVuNXBIOFYwUXdNK2N1aU1iejd6TnVGZ2U5dTVxYzBodGZRcWpDWTFDTzZJVUs2MURSTTdtZ2ZhU1ljTkhJNTJvV0wiLCJtYWMiOiJiZTg5ODMzYTIxYmUyMzhkMmNlZGRjZTQzYjk1MWY2NWJiODc2MjYwMmI0YjVkYmE1ZmZhOTE4YmQxMzIwNWQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425363794\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}