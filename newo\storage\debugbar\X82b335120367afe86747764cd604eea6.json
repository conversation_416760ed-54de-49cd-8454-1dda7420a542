{"__meta": {"id": "X82b335120367afe86747764cd604eea6", "datetime": "2025-06-08 14:15:25", "utime": **********.384499, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392124.056223, "end": **********.384535, "duration": 1.3283121585845947, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749392124.056223, "relative_start": 0, "end": **********.200458, "relative_end": **********.200458, "duration": 1.1442351341247559, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.200479, "relative_start": 1.1442561149597168, "end": **********.384539, "relative_end": 3.814697265625e-06, "duration": 0.18405985832214355, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45604704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00877, "accumulated_duration_str": "8.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3112838, "duration": 0.00587, "duration_str": "5.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.933}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.340398, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.933, "width_percent": 11.859}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3567111, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.791, "width_percent": 21.209}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 18\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1792811782 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1792811782\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1802668377 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1802668377\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1586293268 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749391833855%7C52%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlrUmk5MXhyMTJyTUphbnNYTUlZVUE9PSIsInZhbHVlIjoiTGhIcUE0L1IzZTJoSVcrM0svYTlKRWhER280Nm8vZTVmRGhmcGFXbWVCVzhrQjRBRXBQYllURWNmSFlHUUZCQW5oYllDUExCeU1WeTRHZ0hsYnpwK05FSDFLMithYnlNU1VWb3NscHpmRnpTWDVMaFhLUy9PMEpYSG0rRXFDZ3I1R1N1NVRCTlFWN3hkaVhRRHBhbGFUR1BOMWF0cjBNZGRFVy9ydCtqd3RwWHNJd0Zkdlo2bkd2UkMwMGxnVzFyOWJoK2tMZHZxeEM0eTBRUEpPbWRSa0hRampXQ1lJdlJudFFBR1dja0ZIQmtyd2hEeFVkNmNGYU1BTFJCeTlhc3R3TlhFcW9wcXo4NTZhYXRKRWNDUTd6T1E0dVVIS3hxVkhxekZJSExjRVBBWjRiZUYwS0JkLzBFUTdob1NzWkpOOFFXL2U2V2hoTGdzMlJ6ZXVwMDZyZlByOCtCNHgzNGtHOGo2ams3UFBOaHh5VHNlUEM5eFJiK2l1aVdPUUhiRDFlVXZnTFc4bnFzSGlYT2JJT05sdEVxWG0yMXBOTmtkS1o4MlYrSUZrbWJhV1p1b1o5dkZhVUZ4RzcwV3pmNUF1ZTYydVdCZHlDLzAyb2ttd3VXRTMvamcvSVdUbzNxaVVWbzkxd3NIQjcxWmVrdUF5aFVYdXhpQXV5QzY5cTEiLCJtYWMiOiI3NzI3M2VkMThkZmRhYjc3MTQyZTE2MTIwN2RmNzE0ZTgxNDYzMDJkYzI3NGI4MDQxZDM0ZjcyN2E3MTg5YjBjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNoUXo5UGszMUQzNEJJRHRORnlMTlE9PSIsInZhbHVlIjoiNGs2QUNCWXRjKzdMVXhhOVp4ZUlKVHZjNTlmQ011aXllOUYrZHVrbjd3OGxpUUQvK0VZRmtqSktaUG1udDRJeDZMWjIvM2xYTktiUGVEdjJvd1RoWGJ3ejNockhKTnNQRlROTEVMb1FxL1VyUWJaTUNRdGtYQ0VYcWtQQUx0THVGbjdNQXlGQ0xRbnN3S1dvSXR5bHJqbWU3MndxU0JtQnU2ZnR2cTJwdS9ZZ3MrVm1TWGpockpkQU01WE4xbW10UGpEak1HZlNLa1VMSHRpQ3lUY0xKQUVFNFlhUTQ4Q2JCRllWd0t4SWVFbnpWR2xUcHlTVFJWeU9IK1F3TWVwZzFwdHM1dVkweGVyK0FXSjRSYVpvc1FKY2VRQlF6cTRaYnJ3aEJSZ0xHbE5LQlVJWVREc3ZKR1pjbkhKcTU5Rmk5VER1clUweS9RRXByMjZVVXhDR3FaVWduSVpXalcwMi9remFUSHpJMDlXdG5WbTF2UEp2QUlLVEozd0lIdUVycjVST0RSSFhVaDdNYnRYTU1xWDZBV3YxRFFJWXp4Ry9jVko2bFFiSmx1bnlwTEY0SG9UL2diMXp3UFI5ZTlYTGg5MU51dnZOSVJ6ZE9lVkM0aVhuK2dtTjdNNlJVMU1KZFNYcy9RL09JaitzZ1E5KzdSL2VMeXBJNCtHdHJTU1ciLCJtYWMiOiI3ODQ0MjlmMThmMTc1YzgwMmNlNjUyMmJlYzBhNWNmZmMyMGIwNWNiMjJmNDQ5MGMxMTQ3YWMyM2RiMTlmMDY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1586293268\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-354093515 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354093515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:15:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBUc2FFYVplSU1EazMrRGlVeXA4VGc9PSIsInZhbHVlIjoiaGF5M3FzU3JpYWlwZHpvOWwyeUIvQkR0b2xQLzR4Q0IwT2I2dGNaZExNMlEyT0FHMDZwdjJwYlZPV1E5RDdrUkM0eHZFRVkwWWtNYlQ5akwvSmNkdDVTNHpJeU1uMWVXSTMvRWFuZFBjSTRBQy9mN2U4VTJoMHpjbGN4OXB1U29OaG5yQ2UzU3JqcGVOWndscm1kS2JqQllZeWtycXFGblY4MnB1RkEyWUtlYmpFL0sxMjlrNWZSTkhKd0w5Y21YZ1RlcXNUdldydFloK1hGMFFxQnR2Qm5oYk5JdlIxNUpnK0lrendHMUx2WmpjbkhPUERGaEN1dnNIOTZBNDdyUDBlNnFtWkxYYlFrS2lTeG1tZC9SU21ZbENDS3BPY3RFeXNrcmFhVVFIRWkwU1R5MFQvR1labGtIWm53WkNNS1c5cVZQOGUxK0lUMTY2dk1QNGh5d1hCcUI1TytFRmNzTmd3SzNNMWJPNTZSY0tyNnVNazRCOXExZkRhTnpHQTVaMVhYRGRqMTdmUFlQQVZFdWZNZjZUY0tza1QzWjRQd0krUmwxR08vMTVLOEgyN2hqdk5WWEhyOGVKTUM1RE1yTGNoRFNMRXRaSys3NHkzTlloUU03cytQTG5BWHBWcnJBMlBsblBWQ0FPKzZiME9JNkpCSFpKbEhaTmRzUGMxdGwiLCJtYWMiOiIwNmYwNDJmNTA0Zjc0ZTA3MzA3NDQwNTAwMjc0MDZhNTY5OTI0N2YwYmU5MjQ4ZTU4NWNjNGI3ODlkYzliZDdmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:15:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFkVmdPWG9aejh4eGRzd1ZjTTNkM3c9PSIsInZhbHVlIjoiY09WMHNyQjFFRXI1b1pCMzdoRFljUGFRTUhmMDVlZ0RRMjFSendDTHhIUHpKSmpTTGVoQ1htVkFzSUxEZzdtSjl2WlBDK2t6MldOZ3ZqWGxGbnpwanNTQW41SmxhU3JLQ3lUaFVLTGpFK3BwMURRcW5lWG10bXFnM2ZaUkVwTTBEcDJacmNhSGFqTlZpSVhzT043dnF4TmNQTW9lb2VlaGNaYkNqLzlSdk5zRUZGWEd3K0RuUk4rT2ZrblI1cFd0MDZKT1hFVXR3bjNGK3JaS1hHWi8wVVJ3d0J4Um9CdzJyOENlaUdUb1c0NW0zSDZPMStXcHNxM1F2N0piMVk4L1B3QldsZGF1cHg3V3ZmZjdYQlVQNmNrMWJ0R3RBM2ZGUExBSFRNV2c0Tmt3d3MwN3NlaTZ5SlNuWjFhMEhpdklnYlk2KzF0ZG50cDJ0QWFOdFhiMSt1cjZFUU1tNXptYmJnRi8zYm9ENEZGa29iWnZLR1k3Z1F2OWoreGlVam95L2l6LzhzUkFIMU53aWN4NndRWXJDV3VrRXlqQThwQzU1L0N0RHFwOGR0Z09xNG5HeXorVVRRWFp1dHpKNVpQQzVCVkRMR2JWVnFORUszWThkNmc3Q0JBdEhNc2FYMXJ6aVl3SVhRNldUNk54aDNCMldOUkErcGd4OE1DTmdNMHIiLCJtYWMiOiI5OWI4MmU5ZDhkYWVhZDMyYmRjNzk1YmNkM2UxNDk0OWMwNmJmNTBhODU1YmY2ZWE1NmYzMGE4ZGNlMDM4OWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:15:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBUc2FFYVplSU1EazMrRGlVeXA4VGc9PSIsInZhbHVlIjoiaGF5M3FzU3JpYWlwZHpvOWwyeUIvQkR0b2xQLzR4Q0IwT2I2dGNaZExNMlEyT0FHMDZwdjJwYlZPV1E5RDdrUkM0eHZFRVkwWWtNYlQ5akwvSmNkdDVTNHpJeU1uMWVXSTMvRWFuZFBjSTRBQy9mN2U4VTJoMHpjbGN4OXB1U29OaG5yQ2UzU3JqcGVOWndscm1kS2JqQllZeWtycXFGblY4MnB1RkEyWUtlYmpFL0sxMjlrNWZSTkhKd0w5Y21YZ1RlcXNUdldydFloK1hGMFFxQnR2Qm5oYk5JdlIxNUpnK0lrendHMUx2WmpjbkhPUERGaEN1dnNIOTZBNDdyUDBlNnFtWkxYYlFrS2lTeG1tZC9SU21ZbENDS3BPY3RFeXNrcmFhVVFIRWkwU1R5MFQvR1labGtIWm53WkNNS1c5cVZQOGUxK0lUMTY2dk1QNGh5d1hCcUI1TytFRmNzTmd3SzNNMWJPNTZSY0tyNnVNazRCOXExZkRhTnpHQTVaMVhYRGRqMTdmUFlQQVZFdWZNZjZUY0tza1QzWjRQd0krUmwxR08vMTVLOEgyN2hqdk5WWEhyOGVKTUM1RE1yTGNoRFNMRXRaSys3NHkzTlloUU03cytQTG5BWHBWcnJBMlBsblBWQ0FPKzZiME9JNkpCSFpKbEhaTmRzUGMxdGwiLCJtYWMiOiIwNmYwNDJmNTA0Zjc0ZTA3MzA3NDQwNTAwMjc0MDZhNTY5OTI0N2YwYmU5MjQ4ZTU4NWNjNGI3ODlkYzliZDdmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:15:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFkVmdPWG9aejh4eGRzd1ZjTTNkM3c9PSIsInZhbHVlIjoiY09WMHNyQjFFRXI1b1pCMzdoRFljUGFRTUhmMDVlZ0RRMjFSendDTHhIUHpKSmpTTGVoQ1htVkFzSUxEZzdtSjl2WlBDK2t6MldOZ3ZqWGxGbnpwanNTQW41SmxhU3JLQ3lUaFVLTGpFK3BwMURRcW5lWG10bXFnM2ZaUkVwTTBEcDJacmNhSGFqTlZpSVhzT043dnF4TmNQTW9lb2VlaGNaYkNqLzlSdk5zRUZGWEd3K0RuUk4rT2ZrblI1cFd0MDZKT1hFVXR3bjNGK3JaS1hHWi8wVVJ3d0J4Um9CdzJyOENlaUdUb1c0NW0zSDZPMStXcHNxM1F2N0piMVk4L1B3QldsZGF1cHg3V3ZmZjdYQlVQNmNrMWJ0R3RBM2ZGUExBSFRNV2c0Tmt3d3MwN3NlaTZ5SlNuWjFhMEhpdklnYlk2KzF0ZG50cDJ0QWFOdFhiMSt1cjZFUU1tNXptYmJnRi8zYm9ENEZGa29iWnZLR1k3Z1F2OWoreGlVam95L2l6LzhzUkFIMU53aWN4NndRWXJDV3VrRXlqQThwQzU1L0N0RHFwOGR0Z09xNG5HeXorVVRRWFp1dHpKNVpQQzVCVkRMR2JWVnFORUszWThkNmc3Q0JBdEhNc2FYMXJ6aVl3SVhRNldUNk54aDNCMldOUkErcGd4OE1DTmdNMHIiLCJtYWMiOiI5OWI4MmU5ZDhkYWVhZDMyYmRjNzk1YmNkM2UxNDk0OWMwNmJmNTBhODU1YmY2ZWE1NmYzMGE4ZGNlMDM4OWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:15:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}