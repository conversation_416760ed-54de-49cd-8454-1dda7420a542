{"__meta": {"id": "Xd1bfe98c74f6dd751595b1bf0080d8cf", "datetime": "2025-06-08 13:28:01", "utime": **********.403783, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389280.194999, "end": **********.403813, "duration": 1.2088139057159424, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1749389280.194999, "relative_start": 0, "end": **********.260352, "relative_end": **********.260352, "duration": 1.0653529167175293, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.260372, "relative_start": 1.0653729438781738, "end": **********.403816, "relative_end": 3.0994415283203125e-06, "duration": 0.14344406127929688, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43935448, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01986, "accumulated_duration_str": "19.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.357143, "duration": 0.01852, "duration_str": "18.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.253}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.384449, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 93.253, "width_percent": 6.747}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1503782956 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1503782956\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-16540794 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV0OEtFL3o1R1pPTFVTV0REU3F0L1E9PSIsInZhbHVlIjoiN0x5dGdMNkxmMUlzSUwrZk5XeEI4dlRHUDNBaEc5UEhROGQ3aExlUmVpenpTdFB4MENwMGh5WDJVR3IyQ1hGdzBvek1QamJyd2xuNk5tQVJBSUd1R2JheWdJZlVQMU1NRjkvZVlxZCtMQnROVXg5cjlVUW45SW5xM1RPR3dGRXRHQkVvdThsZ2x5YkJOUzdQcS9RUG9tMFByK3hBRnJCRW5Hc2FndmtSMWlNVnFpWjhoc1RFMy9VdkViYVZnNHlvQjNEVkZKSC9wVGNrckhIVklPNjdTd0V4VHdiUlJ4RjAxNTBtWnJ6K0l5NE9MNE1Sc3V5dlVQRTRndXFCMUxIdDNjRDBZL1AvczZqUHBXaHhuOWx4bVlBN1BSbTF6NUZmakE4blBmemtqTkZ5c0x6bC9DR1JiaUZmckZCUFo5bHB6RDhudHB1UEprQ1hadHQzUDJpWW5QbGg0SGI3ZkRUczdyZTRMM2lHOFpBWlZqL0s4YUVXVGk3dmF0dFB5bEp4bE1FK2Z2UXFLN01XZGQxOFV2RnBpaHFxNzZrdjZGMjMrckczSGNHd1NKL2FhR1I5Q3V3UmZ2Y3k5SjFVYStESjREaGtRUDErem1tVGNhQmsyKzQxMi8xUnZ2WUZybDNTc1c3ek1xeGhLQ2w4OUlEaEJSYnlCTTZUSkNKYndva0QiLCJtYWMiOiI3MDU3YTQyNDAxMzQ2ZmU2ZjU4YzU3OGM1YTM2NzAxNWRhYjQ2MjJlNDllN2U3NmIxODNmZWQzMWUzYWVhOWIyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IncxSXVBNjhNSG5JY1pORVVzTnV1aWc9PSIsInZhbHVlIjoiREJrTzhnMHUxQmdVNTB1TDN3aWRPeGhZRnp2WmRjbVNveXpKZWs5VnhpSHVJUlJyZTJxd0JxdG9JeU1BempwU0lTSDVpMFpIQW91bHM3L05jK1ZXTWU5bitRMVB0NUJnWW51Y0huVmxkZVUzWmdLU2pCeVpQRTduUHIxbUJYRVNoc3pvNkRmUndGZ0gya202dkdmM2twaEU1S2hRSUJjOEtEQU93S1o4MDJKd0pVbXhTd1NlNzJQZnUwRC9US3pUT05YVlhMSVBKancrZFREVHRSeThHbWRpb0ZEZ1VVZFNuZnB3TkMzWVBpcGdGQ1cyNUtZa2x1YzcwQ0tqT0dYNitkMlhURHcrQzdqWnlqcFU0bEhhUkNEeEFFWGlBZ0ZadDg5eWlncTRzZkJENEROZG1MS0NuaE94eWdZVFdUNkd0d2htVnJxc2JUa1llOW1BdU9XWDVnNGplcHdwZ0gwVXl3TmI3QktXUWZKTG1YbFNSUkdhWDlNbFk0UDBISVBWWUZPYlpJWXVjN09rVEJCejQ1aGhLQmV2bDVFdnhTV1pERUVQd2lRRWl0cklJazJSaUN3dzZKaCtpdGFLa3lVb0xQZitZUUlCYk5aNWpOQWJIbTdXYlRZaFJMNTdaTWtDUE1kbnRoSG0xazhGei9YQTVNOEFVZ1hsS2pneFJCb3MiLCJtYWMiOiJlMDI2MDQ2ODAwNGI0NDdhOThlM2FiMzM2Y2E5ODI1NjM4ZTViY2NjZjI3MGUxNzQyOWNkOWMxMmQxNGIxMDkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16540794\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-262693169 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262693169\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-274296457 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:28:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQybEVmdWZmNWNQbE5VendXZ0UyMFE9PSIsInZhbHVlIjoibnhLMmg2ZkVNR2NFaFJ6eEZRWmNvYmsvbFJqdUtyU3ZMdGIrVURGR0psbmVGTVpJNUxIVnY4RTYyMFNpWTFpbTgrN1l4Vk9NcnY3T2htRFZlVnNsSTNPN2tqd2JKWHBYTlZuTElhMU00ZWhCRFp2UUZ1UzY0TmRKVjZwZXR5V3NoVEdSSU5BdHowVmdJL09NZVlXeFA3QzNnWG0rVXgvdUNPNXNjMGhxbmRtbEY1UGh5YjhnSUc2UDZIc3FoMHhFc3dvOUtQdjArN25PWnhnbUZPMC9rcHdsWG5ucDhBdlFXdlNyZnJHS3FXR1ZQRFZQd2NiRWhPM05HdlFyZm9oT0V5V3RLcko5U21LeW44RDJyejUxbTNWcXRQUmxES0JNVmRnVjRFbUh4SncvaUlpeDFhODVkQUk0Vmx0S3lSVEJ3TWFZeVEyVDltSE5LSVVKejVFb2Qzbm8rT3FmSkdPWEJQR2txY0VOZkplMXZzWlhxVjFYaThkWTdPWkFCVFpUbjBORUxUbU1rRmxYTDA5clluM01nbVpkampoRjdlc0kydmIwaWNLWEVENWlIRkFWYlJ5b09jQ3U5Z1VLaHkwc2tiSmhyTGRTOUFHU0tKRFlTd2NzaGQyU2xpamhjU3NmSU96cExITHQyTndXVG90Rm1QNGt4QTdPUHJpMDlzdUkiLCJtYWMiOiJmYmU0MmE3ZGMyZDUxNzY3ZDRhY2I2NzdjZjUyYjZjNTk4YmQ4NGY5MzM2NWVhOTZhZGMyNzM1YjAzNzE5Y2I5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:28:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpPcTVBYkI4K0w1UW01V2ltUlorTmc9PSIsInZhbHVlIjoiSGNwMjJIU3JZU2xYTU9NWDNTd2hjT2RRY3pNUlducXEvMGxOb2JRSzhjWFdCdXBpdWsweit6ZkJXczF5RVRwaSt5cW5oOUpVOGV6ZGR1Yk9IVFY3ZVNUMkNFY0t1UTUyOHcvcU9md1pRY1dERDdSOUh3YTFxN0lBLzFaeC8yOXdJOE9mQXV1c3E3SXUzaDlkZmlpbys5Y00xQWJhVFBJRGJPSWlTOStINm9hbEtVbHQ4WVdBMkM5MDF6OVdMZEdHbCtyZVhDeU0rU1BQYmY5dXZBTGc2cjFkMGU5cXdzR0QrbVQ1Y2RwbkcwWUlUeWdpL1ZhdlUvanNBbzg3cktkVk1iNDBHY1dEV2lPbUdlQVhtUEtzeFJ1TGFVSTFvSEJKVWlVK2cyb1Jtbk15NXN4SWR5TVppZjlmbXZycE1qK29wbWxBUUJvRzU3TmVTUHQ4RnNMT1k1MUlFbUwrQmkxOXdzYnhtWE14bzE0SXgwRkNuSTZRa3E0cEIyWHhQQ09FLzRFMDBjQnJjTnZEc1hZbEZuT29EYUdEOWlYRGltVHlUaDJ6TUkybTROT2RPV3VmbDQxK2FYRXhtemgzeEIzTS9zb1VJdVVFdktPd1dFZ2EzM21RWVZqVEtUb3lKdVF6c2pqY2hVeFp3aVg3V2ZMQVJneFAxSnNPYUV3M2pBcFIiLCJtYWMiOiJlYWY4ODdkMDcwNjc2ZDQ3YzdjM2Y4YmJmODkzMDQ2M2MyOWVjYWQyMmJkMGM3ODRhMmUxZDIwNDdiOTAyZTc0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:28:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQybEVmdWZmNWNQbE5VendXZ0UyMFE9PSIsInZhbHVlIjoibnhLMmg2ZkVNR2NFaFJ6eEZRWmNvYmsvbFJqdUtyU3ZMdGIrVURGR0psbmVGTVpJNUxIVnY4RTYyMFNpWTFpbTgrN1l4Vk9NcnY3T2htRFZlVnNsSTNPN2tqd2JKWHBYTlZuTElhMU00ZWhCRFp2UUZ1UzY0TmRKVjZwZXR5V3NoVEdSSU5BdHowVmdJL09NZVlXeFA3QzNnWG0rVXgvdUNPNXNjMGhxbmRtbEY1UGh5YjhnSUc2UDZIc3FoMHhFc3dvOUtQdjArN25PWnhnbUZPMC9rcHdsWG5ucDhBdlFXdlNyZnJHS3FXR1ZQRFZQd2NiRWhPM05HdlFyZm9oT0V5V3RLcko5U21LeW44RDJyejUxbTNWcXRQUmxES0JNVmRnVjRFbUh4SncvaUlpeDFhODVkQUk0Vmx0S3lSVEJ3TWFZeVEyVDltSE5LSVVKejVFb2Qzbm8rT3FmSkdPWEJQR2txY0VOZkplMXZzWlhxVjFYaThkWTdPWkFCVFpUbjBORUxUbU1rRmxYTDA5clluM01nbVpkampoRjdlc0kydmIwaWNLWEVENWlIRkFWYlJ5b09jQ3U5Z1VLaHkwc2tiSmhyTGRTOUFHU0tKRFlTd2NzaGQyU2xpamhjU3NmSU96cExITHQyTndXVG90Rm1QNGt4QTdPUHJpMDlzdUkiLCJtYWMiOiJmYmU0MmE3ZGMyZDUxNzY3ZDRhY2I2NzdjZjUyYjZjNTk4YmQ4NGY5MzM2NWVhOTZhZGMyNzM1YjAzNzE5Y2I5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:28:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpPcTVBYkI4K0w1UW01V2ltUlorTmc9PSIsInZhbHVlIjoiSGNwMjJIU3JZU2xYTU9NWDNTd2hjT2RRY3pNUlducXEvMGxOb2JRSzhjWFdCdXBpdWsweit6ZkJXczF5RVRwaSt5cW5oOUpVOGV6ZGR1Yk9IVFY3ZVNUMkNFY0t1UTUyOHcvcU9md1pRY1dERDdSOUh3YTFxN0lBLzFaeC8yOXdJOE9mQXV1c3E3SXUzaDlkZmlpbys5Y00xQWJhVFBJRGJPSWlTOStINm9hbEtVbHQ4WVdBMkM5MDF6OVdMZEdHbCtyZVhDeU0rU1BQYmY5dXZBTGc2cjFkMGU5cXdzR0QrbVQ1Y2RwbkcwWUlUeWdpL1ZhdlUvanNBbzg3cktkVk1iNDBHY1dEV2lPbUdlQVhtUEtzeFJ1TGFVSTFvSEJKVWlVK2cyb1Jtbk15NXN4SWR5TVppZjlmbXZycE1qK29wbWxBUUJvRzU3TmVTUHQ4RnNMT1k1MUlFbUwrQmkxOXdzYnhtWE14bzE0SXgwRkNuSTZRa3E0cEIyWHhQQ09FLzRFMDBjQnJjTnZEc1hZbEZuT29EYUdEOWlYRGltVHlUaDJ6TUkybTROT2RPV3VmbDQxK2FYRXhtemgzeEIzTS9zb1VJdVVFdktPd1dFZ2EzM21RWVZqVEtUb3lKdVF6c2pqY2hVeFp3aVg3V2ZMQVJneFAxSnNPYUV3M2pBcFIiLCJtYWMiOiJlYWY4ODdkMDcwNjc2ZDQ3YzdjM2Y4YmJmODkzMDQ2M2MyOWVjYWQyMmJkMGM3ODRhMmUxZDIwNDdiOTAyZTc0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:28:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274296457\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-47339925 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47339925\", {\"maxDepth\":0})</script>\n"}}