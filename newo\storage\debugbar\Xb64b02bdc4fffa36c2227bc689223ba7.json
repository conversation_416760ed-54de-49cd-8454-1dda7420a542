{"__meta": {"id": "Xb64b02bdc4fffa36c2227bc689223ba7", "datetime": "2025-06-08 13:02:16", "utime": **********.36372, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387735.006062, "end": **********.363765, "duration": 1.3577029705047607, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749387735.006062, "relative_start": 0, "end": **********.15893, "relative_end": **********.15893, "duration": 1.1528680324554443, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.158951, "relative_start": 1.1528890132904053, "end": **********.36377, "relative_end": 5.0067901611328125e-06, "duration": 0.2048189640045166, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579848, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019780000000000002, "accumulated_duration_str": "19.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2555602, "duration": 0.01735, "duration_str": "17.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.715}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3031492, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.715, "width_percent": 5.814}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3238208, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.529, "width_percent": 6.471}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1950054684 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1950054684\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1171859738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1171859738\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-179444890 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179444890\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-320341571 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387646954%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAzVmpSdmpObksvQXUxb3hFVkd6OUE9PSIsInZhbHVlIjoiempLYlF1YVNCbzJWQ2hBcXV6SE1QZ0gvQk9IY0t5SnVjdGZWaU1FUG5uRGFzd1pYQWdKOUpEOURzZUxQcVY3V0pESHduRnVTMEtqbTlmR3dQc1dlcnAyUmZDMXBLNVE2bGpNS3pBeEZ1dzFiNFhrZSsxNUZYS0w0aVdlK3ZQRGpaQm9ZYTJ1bUcxRnVVcHhpQlg3Rk5vRnY5ZUVkazdTNjRTb3JWL2czd0h2anFRUEx3TDJlWENuQkczNlNtK2twNmxuT3Mwdk9aVkxBQ2FNVzJxakxVWEh5a0JXNFdEL2s2Uk1MVDUrb1FmbSs3YmdOaFlsTm0rc3YxQ2lMa2ZPT2ZEQXJmbFhBZkNBZTI1STBiVVpITXQ4N2loNUUzUDh5c242VDhuUzBqTGhESmY1Q0U1bUhad1FqeEdIRHJvQldXZHVrN1NaRWRnbEtsOW5ETHFkRUFrZVR3VDEyb3lsd3E5LzlOSlE0UXVnWW8wNGZkclBKYzRkZWxrRHhHdjNZdURpTnBxMFV1Y1pDT1pvWFZUUThjQTJWNFByZWt1L3gyRkZ2dDBTb3UvRFBmS3YwaUtZV200cmZpUWYyR3pIaEpFc0JiMkZsVCs3U2pjdzhQUElmZ2VjUVFXV3FnUDZ2dkdYZ3ZxNjFnTlcrMDdjcXhjMzliT2VzVVJ6MW5zQkciLCJtYWMiOiJhM2UwZDE2MjYwMDgxM2FkOTZkZjFhYjRhNjViZTkyY2M5MzY5YzhiZTU2MWI0YWJkODg2Y2NiMjYyY2MxNjUwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZQbXl5L1VhV3dZMHdRS3JKSjhFRlE9PSIsInZhbHVlIjoiN3k5UDB5WUxHSzJ2a01aQnErZW1DVmkwSUVGdnBHVDU0WDlFYjdGdHVwdHpnWDVZWCtmdkc1SG5KMkNjMWlsYk9HMnVFZ3hiZEZNamluT0VieGxDVnhmbTF2VlZYTzh3eERKV2JYMTdmOXpFeGFJRmFoQnhab0NzZ2ZDNWdYSng2ZVZKWkppR0JHUXgwRkV2L3NGWS82UHgrNWN5OXZFWXlPdDA3YmcvQ3E1ZlFRN1dWQnlGVGxFMnptWXk5TWdUUTBZS0ZIOWRYR1g2cVoycXQ3ek5IVXhlWk1WazIxakdnOUw4QmVPWlZXT1FmMGw4Ry9LOEt4SldzQkJrV2ZiM2V3c0RiZTJBVmFDZ1RldmZNNy85YjNuQ1Jod2x1QjF4NThSS25Ia2RlRk9HRy9WbWdscHh4NWhTYkI0SE5Xcm1jc1c5bW5QWWNsRVF0MURuWkc1LzlHVDl3RzIvL0pNK043VjJ1T003K3hrbnBlODNjWDVlaUt5ejBNRGRhU3duc21JMURIMDFHaVJ0bmdITXQvY0xPdkdvT21WdU9wMTEvbU03UVIvQkZZcUtXR1VnMTNtSm5kYWV0MjBUTzNFYmJSWldkZmtDcThpMGxBMU10bXkxTGZrT2lpNE83MDNhY0grN1JyNHA5bWQwVjU0ZUxueHI1Umx1NXJCTU1GMmIiLCJtYWMiOiJhNmUxZDNmMjlmYTI0MDY4NjNjMTYxODMzNGFiMDFiZTE2M2QxMTYxODA0ZjgyZDMxYzg0YWRlNThkZDJkMWEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-320341571\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1381930731 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381930731\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1178653344 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:02:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1rZ0lTZFBqeS94aHAyUHNYM2hVUVE9PSIsInZhbHVlIjoiaTZYQ1hDUEpoRnJaQXFNdTg0OVEvaWF5WHl3aHBidkRaUUJmMWo1dDhUR2pYZ1ZaZ1ZBTDFUQ2Y1MmVDNGl5cVczeDFmZWs4RFhlemRCQlcrcHpFVk1JK1hUTzlBbU5KVExCdnVMY0VOZ1p1WllWeVBUVEFvK2w3SS96bXhuMHRGV0dkOE5ZMmRxR1Q2MEhhQm1ISU1nTWNPNXpwWDhiRCs3Zm9ISWhSRHRWUkg3Z01wVEpWU3Jqa2hhN3g1MU1remQrU2N4N1VIQjVwN2Q4eXpBME51VUNjWVNkOFpHT2RsNU9XL0xiWjVUNFBPMGdOSWpHZll3K3hrNHlmNnNlL2lCMTlWa2FSSHFOdGY0QTRWN1NwVk1sUU9QT1VPZGJVRUNVd2gyMVdLa1hMN0VEZS9OVWpEY2FjSjJwUGRzODN0eG5EWHBtV1pzTjJXVThwU0h5WlBEMjRkS0crWEVIc2lBeUkxdkxSTUJTc1R6YjBraWRUY0tBZmhZSXVDbExQcUd2UXlreHQ5TjZLSHVFdjEwM0ZIb2VlVU1jWFN0R3lLNDU1R1BoRFFCVWhnN3l3dlpOM0xBOWF3MU00ZWRrSDBTYnBabDExL0tRNmNEcjU2MnlrUmk1NkV2Vm80SmJHUUJGNHZmcHNDVkgzL3NqR21oRVk0MTVCQzV3TWtCdFYiLCJtYWMiOiIyYTY1NzIzZDg1NzZkNGQ3NmRlYjU4ODEzZmUxOWRhYzA5NzYwNGU3OWZlZTdkYTVhNjZiMDNmODc4YWI2ODAzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhZeXMvQXJJc2ptb2YyWWViVGNNR0E9PSIsInZhbHVlIjoiWnZ0TUpaNU1HQjVDb2FCemVVK1l1Q2h5NHo1VW9ydDBRaGNLR0h4T1VwQkxJSm1xU1dLZG5ZOC94cG5GaStOSWg1V1kxYkNYQWtNMlRNbkplV0VWc2NLR210am5ZbGlmM01naXdlYW1aOEtXendjTms1ejZFNVRNVG05Y0dKZm9qOFptUDh4TjhBQWlTblNkeWo0aXFqMUFEYkRibVpYZjhUaHdjK0ppTG9pYzlSTGpZL0M4YkRCdElxWXd2WFowZThMTFFoVVEwMUtlemhUU2VBOTRzYVU1T0ltQUF1UmVVMWMxK2FTV21XT1NzUE9CdzlmcUpySHVPV093ek1HQzZBelRvaERNZVpGOU9neHVaVUV0bnN3VVF6Zm1xd3RyaWNXVG9SY002QXJsWDZKbmQ3SjZyQzZsV3RtZ1dhSHRyclpwc1NBaTZVaThBNVcvd05MZWw2QThkeFc5TFRadHlYbFkxTjFTQ3RFTDhWZklZTThqb1gxSnpoM21hZ2ljZEdCSG42MW01aSt4VTlXVnR3aGcvNDU5U2pEY1J1cEt0QzRDcUI4azJnMkZHRlZiSE0rNFdrQ1ZqZm55WnFVL3hlYUxMMXNNRldXS0NhRzJ5WXdZWXdqakY4a3ZJT2ozNmk0NzdxaTVGWlBkaUZtWlJyam9TMFZ1cjZFNGg3VTIiLCJtYWMiOiI1NjY1ZGRlNmI5ZWQ0ZWI0MjlmZjM4ZGY5M2ZjY2I4Yjc5NGM2NGU2NTRjZGQxMmFkN2VmMTRhMDk5ZDAxNWMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1rZ0lTZFBqeS94aHAyUHNYM2hVUVE9PSIsInZhbHVlIjoiaTZYQ1hDUEpoRnJaQXFNdTg0OVEvaWF5WHl3aHBidkRaUUJmMWo1dDhUR2pYZ1ZaZ1ZBTDFUQ2Y1MmVDNGl5cVczeDFmZWs4RFhlemRCQlcrcHpFVk1JK1hUTzlBbU5KVExCdnVMY0VOZ1p1WllWeVBUVEFvK2w3SS96bXhuMHRGV0dkOE5ZMmRxR1Q2MEhhQm1ISU1nTWNPNXpwWDhiRCs3Zm9ISWhSRHRWUkg3Z01wVEpWU3Jqa2hhN3g1MU1remQrU2N4N1VIQjVwN2Q4eXpBME51VUNjWVNkOFpHT2RsNU9XL0xiWjVUNFBPMGdOSWpHZll3K3hrNHlmNnNlL2lCMTlWa2FSSHFOdGY0QTRWN1NwVk1sUU9QT1VPZGJVRUNVd2gyMVdLa1hMN0VEZS9OVWpEY2FjSjJwUGRzODN0eG5EWHBtV1pzTjJXVThwU0h5WlBEMjRkS0crWEVIc2lBeUkxdkxSTUJTc1R6YjBraWRUY0tBZmhZSXVDbExQcUd2UXlreHQ5TjZLSHVFdjEwM0ZIb2VlVU1jWFN0R3lLNDU1R1BoRFFCVWhnN3l3dlpOM0xBOWF3MU00ZWRrSDBTYnBabDExL0tRNmNEcjU2MnlrUmk1NkV2Vm80SmJHUUJGNHZmcHNDVkgzL3NqR21oRVk0MTVCQzV3TWtCdFYiLCJtYWMiOiIyYTY1NzIzZDg1NzZkNGQ3NmRlYjU4ODEzZmUxOWRhYzA5NzYwNGU3OWZlZTdkYTVhNjZiMDNmODc4YWI2ODAzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhZeXMvQXJJc2ptb2YyWWViVGNNR0E9PSIsInZhbHVlIjoiWnZ0TUpaNU1HQjVDb2FCemVVK1l1Q2h5NHo1VW9ydDBRaGNLR0h4T1VwQkxJSm1xU1dLZG5ZOC94cG5GaStOSWg1V1kxYkNYQWtNMlRNbkplV0VWc2NLR210am5ZbGlmM01naXdlYW1aOEtXendjTms1ejZFNVRNVG05Y0dKZm9qOFptUDh4TjhBQWlTblNkeWo0aXFqMUFEYkRibVpYZjhUaHdjK0ppTG9pYzlSTGpZL0M4YkRCdElxWXd2WFowZThMTFFoVVEwMUtlemhUU2VBOTRzYVU1T0ltQUF1UmVVMWMxK2FTV21XT1NzUE9CdzlmcUpySHVPV093ek1HQzZBelRvaERNZVpGOU9neHVaVUV0bnN3VVF6Zm1xd3RyaWNXVG9SY002QXJsWDZKbmQ3SjZyQzZsV3RtZ1dhSHRyclpwc1NBaTZVaThBNVcvd05MZWw2QThkeFc5TFRadHlYbFkxTjFTQ3RFTDhWZklZTThqb1gxSnpoM21hZ2ljZEdCSG42MW01aSt4VTlXVnR3aGcvNDU5U2pEY1J1cEt0QzRDcUI4azJnMkZHRlZiSE0rNFdrQ1ZqZm55WnFVL3hlYUxMMXNNRldXS0NhRzJ5WXdZWXdqakY4a3ZJT2ozNmk0NzdxaTVGWlBkaUZtWlJyam9TMFZ1cjZFNGg3VTIiLCJtYWMiOiI1NjY1ZGRlNmI5ZWQ0ZWI0MjlmZjM4ZGY5M2ZjY2I4Yjc5NGM2NGU2NTRjZGQxMmFkN2VmMTRhMDk5ZDAxNWMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178653344\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-33130555 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-33130555\", {\"maxDepth\":0})</script>\n"}}