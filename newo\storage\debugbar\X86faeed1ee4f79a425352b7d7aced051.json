{"__meta": {"id": "X86faeed1ee4f79a425352b7d7aced051", "datetime": "2025-06-08 15:09:57", "utime": **********.961125, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.145339, "end": **********.961147, "duration": 0.8158080577850342, "duration_str": "816ms", "measures": [{"label": "Booting", "start": **********.145339, "relative_start": 0, "end": **********.847199, "relative_end": **********.847199, "duration": 0.7018599510192871, "duration_str": "702ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.847216, "relative_start": 0.7018768787384033, "end": **********.96115, "relative_end": 2.86102294921875e-06, "duration": 0.11393404006958008, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45587008, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.012550000000000002, "accumulated_duration_str": "12.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8922658, "duration": 0.01097, "duration_str": "10.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.41}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.922512, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.41, "width_percent": 7.331}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.938455, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.741, "width_percent": 5.259}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 33\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1496907366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1496907366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1199729637 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199729637\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1328472412 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749395395437%7C72%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcvVmZEeGNubVdieW9kcUlvbjhzNWc9PSIsInZhbHVlIjoiZjJ2WFhzMW5HQkVtcGUxUTVSeTZPZjBlU1VCUSs4TTF3d3BvQnliditjYzV2MG4xbzNrdm5mWkxyYkV4KzBRT215ZVJSNlhBR1JLZm9XNGg2ZVBrMEt6cnVad0Y3WUFyZEltekkwUGlXZmtHcS9wQzMvb3Q1VkcrUnhNQ1BwQ3Jpb3g3TGhYZE1lc1VOQjlzeXdVdnV6MTM4T3JtdHpOWmFycjd0OERkTmpoRzZhcnF3Skx2STVnTjlmY0pLT3dNNWMrL3IzblNma21qVUJ0alZDUHQ2NUV6b2dSZUFBbHlsdmZpWW9DTmNUUHZEVkdUZ1pFWndEbkJRUGI1QkhBZm1PMjFucFdnWmY4TmRvemxaNnVJYmt1Z3NteVpkeTlkOW9lWE1CQ0NjbWVka2dPcEV2Tk5TQWU1MW5POENQTzFXOGR0dlBPTmd5V204a3Y2V2xhM3hGVGVvK0RFQnJBZ0k3ZDBJRTlaVUU5ckdjK2RvYXc3N3FEZmdMcWYrdWRDRVBveWxZL2pDTjJnOThndlYrdFNuODFjanBpelh6eEVSODlrRGNWdDNaOEtnUk5VbkxkQ1lqblhwNit4Y0FxaGtIcXFhSHNVTUxxRS80YkloVWtPQ0NVQVJFcUFRbXBnUlVEUzB6T0FYZUxpd3NxNStRM0hGOC96aERNbkhqNm4iLCJtYWMiOiI5MGQzOGMyNjgxZWMyZDAxZGQ0Yjg4ZTBlMzZlZjc1YmVjYzFkMzM0NmE2YzA4NDI1MjE4OGM2MDQ4ZGY1YjNhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVQd1dRRlduYWpUMXdOS0Jkak11bXc9PSIsInZhbHVlIjoianB2aGJlMU5DQmNHYjFZM0dKV1JDUzVpc0s5N3hYK21XSlkrWlVJMXg0SElaOWYzK3l2OE16U2o3ajIvREZXbmNSVC9GRTJaMkZwU0dwNlY2aXZJaC82Wkc4WGRNMHl4NXp3Y1dQUlpEYkdtaFJxTkhkVlA3aE1wQXhkWEgyQ2R1U0NCWjFndzNQd0ppUTUya0k1ODhvSGprNmlFUlRFeXlmYXRPT3dsR1FIMTZ4OTRibSsvZUJLbDdseXBFTk9DOUwxWFQzWDNFbHFRdTA1aDJSYTdhTGhoS2hVLzU3KzYvWGJWajJUSFFvcjJaYUVDSFBiUzJVWTM1bFlrdHgyc3JiWDZOMnVoQzVVZTJ1VjY3Uk9CdkQ0SS9XeEdqWDhxQXZ5NnJPZUptQWQ5a2J6Sjd4eVVZeWhka1plZmlDYkdyTlJIVm9NQ01FdSsxTklLUkV2RGhzOFg2VUFURDV1MnR3azBnbGVsMmNabGVQb3FqZzNYYStpZnVxU0taUjl2QUhBdmJJUm1XVWNRUVpCaGdqRHArQW94MzZjcW9zckNIZndwL2xURnh5UlZhYVdTZkJjV0FHdVR1ZEtVTTNob0wwRHN0VDF4TE8vZWpKUlF6bEFBY3htbTErVXhlN0lTQUFvZVZKb2EwUjBxK2phOXF6MGZGNExtSFNNbzkyVG8iLCJtYWMiOiJmYTNhMTQwNjIyNDZhYjUwZTJjNGViYmJmNWFkOWZlM2FkODMyMWIyYmQ5YjQwN2RmMjc4NmNmN2RiY2RiN2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328472412\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1968829072 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968829072\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-138877116 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImwxVnFubmxSWDdZeHNrZnZrZCtTS0E9PSIsInZhbHVlIjoiMXYyL1JwbHpUZFZZN3Y5ZlFRQVJsOU5hRmVzM093cjZVT2pkOXB4UEdlalN1NkdRd3drS2E5Q1EwY3JGZ1RBU2gxOGxQQXdPM2NhcEhWMS9ZVmRSNTZ5UkNlYUZvblNZK0gzVGx2ZHE1ZjhhUTIzb0dQbm1vY0lnSjQwMVV5MWVJcVdoTGJPSldnTTloRlFMRzFwdUVQc096WW9HTTgrNjNBOXJ4cmVpVW1aZE5Ra3JmalhiRERWb2ozZEdxd05xdHpxSW54Y3dRekxNNkRyU0s2ZHNjaS90d2JqNVlRRE0vVnFHdlZ0VlFoaGNOdnFxZVI5M1dQOE5WUlRqZFlLazYveFFkS0l2ZGE1ZGpmTkxlYm8wSDZvL1JCS2VkYXhGclFnWkJ3UlVNTjhycWg3YmQwdisxdmtsZlgyZ3MzY0ZpQkdLNzZ6OFFPSk9FRVJETktPVzg2ZThyREFoOGhINHorTWcvMHd1c3NlVVRCK2QrenJ2cGR3dUduc2I4TTJ6UVJ2WFhxeGNDYy9ZV1VzazJxVzdyOWFmTDNhcjFuMFVPOW54ek9iRHhhL1k5b3lQckdJRGRzRkNnODZaODIwdE55b0EwclQ2MUZIZ3czdkxqb0RJdXlvOUh3cGx0eDluQWlUM2ZiVzcwdERzRTlMRUlKS2dJUFNxQWthZVRrU3kiLCJtYWMiOiI0ZmFiZTgwYmI3ZDc0MDVjMGYzOThhZDgwM2ZiZjg5ODlmNDdkZjBmOTIwNGYxZmU4ODIyZDUzYTliZDA5YzVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZJajlCQWd5ODBUYUdBMDZieGVkNmc9PSIsInZhbHVlIjoiNXNEbTd2ZG1RdUd3bGd0RmNRcTl2RVh0WlNDVkVLUk9qSHk2TzMwaEc1WUxmbVc3YTArZUllVVlGL2ZySWorTlFLd1VGVGdYSm8yM0lqQ2FQVUlmYThvUzZFSHJWdWt2TzVwTFdqNnZRRG9nZkR3c21iKytTcGdRVlI1T1hRZGs2NWprY1ZvRkNjNTlka0xWT1JGRmhWdDBJTVJzL3hiUFhpV2VkUnEybVJxUTZxRmh6Um93eER0OXpzN1dIOVptYUl1MWVKeGdjbzBVMjRUUnJ2ZXJQYkU2aERlejdaMEVkT2o4QytQVzRzNkwrM1lqRHNiSnNUMTBDK045RmoxeE9XNzdiSTZWWEN4KzdvZ2FyUjJUamw3VFBiTytMNm5xTCtrUm1vVzJHWkQ2L05FL1lRcU9URUtIcUZOMTFaSEM1aGFISHZJZ2dTYXRCbStmMHUyWFpua3pPQWNDTmgvRlVwZUl3Vm1QbXVpYkFDYVRTVGYxS0RCZFc0YkIxZ29SU1duRVd1dTh2VUsyYWxZRU92SUkwVUszUnNXL2IrNDh4TGdyc2FKbGIxK2E3ZEZmN1JqY3g5bldtZGxPanhWNG11Um8zWEQ3d1lOMlFQdW1YNlBJZnJzN2kzNzc1U0xsTFVpd3ZFc2MrMlFhN1ZUZXZiVmc3ZlY2RDRvejdYaGYiLCJtYWMiOiJjNWU5NmJhZmYyNmM4ODU5MTBhYmE0ZWM1MjNkNTZmNWY4N2M0OTVhNjNjNTE5YmNhYTY3ODEwNDM0ZGUxOTg5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImwxVnFubmxSWDdZeHNrZnZrZCtTS0E9PSIsInZhbHVlIjoiMXYyL1JwbHpUZFZZN3Y5ZlFRQVJsOU5hRmVzM093cjZVT2pkOXB4UEdlalN1NkdRd3drS2E5Q1EwY3JGZ1RBU2gxOGxQQXdPM2NhcEhWMS9ZVmRSNTZ5UkNlYUZvblNZK0gzVGx2ZHE1ZjhhUTIzb0dQbm1vY0lnSjQwMVV5MWVJcVdoTGJPSldnTTloRlFMRzFwdUVQc096WW9HTTgrNjNBOXJ4cmVpVW1aZE5Ra3JmalhiRERWb2ozZEdxd05xdHpxSW54Y3dRekxNNkRyU0s2ZHNjaS90d2JqNVlRRE0vVnFHdlZ0VlFoaGNOdnFxZVI5M1dQOE5WUlRqZFlLazYveFFkS0l2ZGE1ZGpmTkxlYm8wSDZvL1JCS2VkYXhGclFnWkJ3UlVNTjhycWg3YmQwdisxdmtsZlgyZ3MzY0ZpQkdLNzZ6OFFPSk9FRVJETktPVzg2ZThyREFoOGhINHorTWcvMHd1c3NlVVRCK2QrenJ2cGR3dUduc2I4TTJ6UVJ2WFhxeGNDYy9ZV1VzazJxVzdyOWFmTDNhcjFuMFVPOW54ek9iRHhhL1k5b3lQckdJRGRzRkNnODZaODIwdE55b0EwclQ2MUZIZ3czdkxqb0RJdXlvOUh3cGx0eDluQWlUM2ZiVzcwdERzRTlMRUlKS2dJUFNxQWthZVRrU3kiLCJtYWMiOiI0ZmFiZTgwYmI3ZDc0MDVjMGYzOThhZDgwM2ZiZjg5ODlmNDdkZjBmOTIwNGYxZmU4ODIyZDUzYTliZDA5YzVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZJajlCQWd5ODBUYUdBMDZieGVkNmc9PSIsInZhbHVlIjoiNXNEbTd2ZG1RdUd3bGd0RmNRcTl2RVh0WlNDVkVLUk9qSHk2TzMwaEc1WUxmbVc3YTArZUllVVlGL2ZySWorTlFLd1VGVGdYSm8yM0lqQ2FQVUlmYThvUzZFSHJWdWt2TzVwTFdqNnZRRG9nZkR3c21iKytTcGdRVlI1T1hRZGs2NWprY1ZvRkNjNTlka0xWT1JGRmhWdDBJTVJzL3hiUFhpV2VkUnEybVJxUTZxRmh6Um93eER0OXpzN1dIOVptYUl1MWVKeGdjbzBVMjRUUnJ2ZXJQYkU2aERlejdaMEVkT2o4QytQVzRzNkwrM1lqRHNiSnNUMTBDK045RmoxeE9XNzdiSTZWWEN4KzdvZ2FyUjJUamw3VFBiTytMNm5xTCtrUm1vVzJHWkQ2L05FL1lRcU9URUtIcUZOMTFaSEM1aGFISHZJZ2dTYXRCbStmMHUyWFpua3pPQWNDTmgvRlVwZUl3Vm1QbXVpYkFDYVRTVGYxS0RCZFc0YkIxZ29SU1duRVd1dTh2VUsyYWxZRU92SUkwVUszUnNXL2IrNDh4TGdyc2FKbGIxK2E3ZEZmN1JqY3g5bldtZGxPanhWNG11Um8zWEQ3d1lOMlFQdW1YNlBJZnJzN2kzNzc1U0xsTFVpd3ZFc2MrMlFhN1ZUZXZiVmc3ZlY2RDRvejdYaGYiLCJtYWMiOiJjNWU5NmJhZmYyNmM4ODU5MTBhYmE0ZWM1MjNkNTZmNWY4N2M0OTVhNjNjNTE5YmNhYTY3ODEwNDM0ZGUxOTg5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-138877116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}