{"__meta": {"id": "Xe5101ff88a47a85e2becd3f04fcaed19", "datetime": "2025-06-08 13:05:54", "utime": **********.54798, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387953.291155, "end": **********.548021, "duration": 1.2568659782409668, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1749387953.291155, "relative_start": 0, "end": **********.394194, "relative_end": **********.394194, "duration": 1.1030387878417969, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.394218, "relative_start": 1.1030628681182861, "end": **********.548026, "relative_end": 5.0067901611328125e-06, "duration": 0.1538081169128418, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45179088, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0082, "accumulated_duration_str": "8.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.485682, "duration": 0.00529, "duration_str": "5.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.512}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.517518, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.512, "width_percent": 16.585}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5276442, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 81.098, "width_percent": 18.902}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1221430290 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1221430290\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1632958777 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1632958777\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1483263912 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtMSzBKRGVjQ2tvVUVzRXk5UE5CaGc9PSIsInZhbHVlIjoiNTRIbk5za0xLaC9nbmlNVGVwb3VOU1JKMlJ1TW15azg3Sm11RkJxRldqNFpERWdBYXNBYjB0ZWdSNFNiTnJqVzNydUJPZTk1VHp1dWM3bGt6eXZnS1BJQ1FoZ29zYnc4WmNHWENIYTFyYWNmU2xzaEorUHdoOU9rREJpU2grR1kycUZhZXFwcE4rVmRyOW5HSHRsVitNOU1NRzU4M3JxMCtveWhOUFptd2JFV2lUS2V1SVVhT3NSMUwvYkJ6N09aa3MvMGIwaWFleVcrYm15TTZjWk5NWlBmeDZRend1TjdkaGIzak9RaDZGL2NtSnIrY05pSVcwTkNqcDRQQlFNQUluYzducmJFbS9oaHFqWVNPaldwaFo0T0lCbnVGWC96Z294WURMK0JjN2ZDejBqdEx1T2psOTFQSjd2TWxibTVuS0xGazdDR0VkYlRrRUh1WjJ6dE4vN3k1bXRhMXlBZFpZQVoyUWtBTzByb3VGSUFBZkM2N21YVDI4MU9WcklvSEFSTi9rL0ZpSS9ST2ZRT3dISFpPRU5CUG40OXByZDNJamV3c0xLdGhzRkp5M1FFVFBnMHQ1WDF6c0JXT2tPL1p2dmY5WkcxcW9YTHgxSWN0M01wTUkxTm5kYjhrclUveXlDcFJtZXBsL0poK2JZWWJ2dFFXVUN5Wi8yRWVaTDQiLCJtYWMiOiI5OGYxODRiZDBmNDk0OTczMzhjYTg2M2M4NDM0MTFmZjM3MjI5MTAxN2U1MjlhZmFjZTA4ZTljNjYzNzQwYjMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkVrQmlsT3FQeVAvMU55RUkvbVQwOFE9PSIsInZhbHVlIjoid3E3OW15eHhjYnp2aU9KZ3IrcGJUL2Rva0VQK1ljd04rTTNyUVVBU1RHWWh2aEZBaklCc3p4WTh5U2dITzN2M2JEaXBSS1FycTBGSVgvd2VSOENZSldvNkMyVTZ6S3JWb1M3NFFvZ0xjUjFISVFkMEZqOGozMWRUdnpQVGc4KzNrdGJiczdlMFdqQ1l6OHNTTUJIcDlTWnZNUW8xSEpqY2kzaW03blcyWEM1bGNrQVVQa004NEFiVWMrWlFYTzZaR3EzTENlUGpZUFVDaG00NjVyaG5BT1NhRHFsczZhWVl5SXJJdGFERHNPRDBmemtPZWZDaXFQWjBkVGJVYWJoVSsxK0c3TG8yWDRFRGlwZHE5VytHOEc4NTF5U3B4RGhzSUdVdmg5U2ZVZysrQkxGVmhGZmovYzdTNVV4MCt5dkZMZTF4cWFIM1FlWWNFQ2Q3VVBFaGRtVFpYcGowQlVnVE9COXBNMHQvM0tFUzdDZk4vRFpQMnVDcnVMYzVmejVlVHNHZUx6c0hUam9veXpHcndyQmJIb09jME5PZk16NWU2ZlUvRlg2T0xYWFUrVUtuM1dPU1EyZ1ZGV3FmL2Vhekl6T1hTR0ExQzIwVlFPd3FIOE8zcjdxeWx6eFdZSG5iNzRwOWRSU2g1VStFVlIzZm1yY3FZVVVCZElhRmFyN2UiLCJtYWMiOiIxYWE1MTFmMjBlNGY5ZDk5ZTBhZjI0NjVkOTAxMjRjM2ZjZjdlZTU4ODdlOGRjNzI4MDhkNjNmYzFjNmVlNGJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483263912\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-958049683 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InU5TDBsMDZhT0ZsT1RoYm1BeWI2WXc9PSIsInZhbHVlIjoiRGlFT2VpejlqcEFvVGhmYjd3eDIxMlZOMVRIZk8rbzlMTlFKcTB2R1N5U0tMYW1IOC95NkJaTjhqODRYaWprTGJUNVlYamJpMWpMbUdZNE9iSk1udTYwYXFTVkhkQ0dIZGU3N3AyUzNERFUxTGZqdGo5UmdKNG9rY2pFUFh6WGVMVzU4b0NvbWNPSkdqQksraHpkTWxiakR2bHVKWG5MNVloajlZYzJTMGp5TzlqYkoyR1dzRGQyUW4zV2E3RHNuVnRJUkh0d29hRUFQTVlVQzJmcmlJamc3UFJwMjc5VkZtTzBNUVVxOEZ0c3UyZEdqY2xHK3liZ3dzUGQ1Zy81QnVkS1czQ2tiSGtIcFdqVG1BY0U3N0dzWnI0K1JFUWtLUzJWaFdXRTcxMTFwbnJBdDNNenpUYkU4L1FhVDk1OXB3UFZacko5Qmg2cWRUNFlFSnNtc1RwekNMbkxwa1NxUHVNdWFQdEx0L0tEczJiaVpjTHQwZzdjVWZPY2J0MkpXUW1zeVlPYmdLTTdiaXAzZkpIMmYrU3NYU3lucS91QkNpUUgzNEc5eXNSd2dvWFRFWUFJd0FxcGZQWGx6d3gvVzNrd21xejc3azY3NTFHakxTK0FITXhjWUMwc1NVaENoc3UzaWloNGZaeG53N2k4UDh0NHdIL00xOXJucTZYVkwiLCJtYWMiOiI1MDYzOTIwNDk3YmVhZWMzNTdlNjcxNjViYzEyYWVhYmIwMjNhOWJiN2ZiNmY1MGRkYjE0YmQ3MDI3ZGRiZTFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtGd1d2QWcvYmpGTHRmdllpSURvR0E9PSIsInZhbHVlIjoiZy9jdTgwcjhmckIvNm1kZTlOYWxhcXJrVXBxekN5ajU2cHhnRnN6bjNlUjBhVEdBZG9pWG1RLzRpT09MU1JZckkvWnlMSnRmV0F5NytpTXZZbDk1cUNvZlpENk9ucWdUSklXQTB1MUtTekxLL3NtTjBpUzFzSlBTUmlXV0g3RTliU2dPOWQ5L1pJRFZ5L0ZKK0o5KzNGM0R0QmFsTnBRNVB3NDJhRFdhaWtqNWJuRmREclViTVJRb1ZPRDc0K0M1VGhOeVNJeVBBOTU1QWpxTVp4MWY4Y001WG5sTVRFNVAralp1dEQ4ZWhYK3hGemlkbkUzY05zYVNvUU1hU1VFdXdDVFRna2xlVkFQdk5uc25ZWHNub1QwRnp1cnJ0RGdkSTdwU3RxUm95dW4xVWZKc1NIWVpGRjVETmRVOVUwNk9sV1MwK1k0bGpPU3RjMDIvR2E4RXZOR1ZSZ1kyVkxGQmt0YlViejRQY0NrZFExZ04vRGNlVHc0VlVLamZxM3Vrc3lZbHlERVVGUFpiV29FQXZuSFVzeHBDWjE1MTEzd3lJcXpEZzdiMHp1NEZ0ZWcva2dPZnNScEM3TDRpMmVNOFNNOHY2am9sTUx1OGxaRFU4NmptYU9QNHZEZVFDRmVKYmpRb2lQc0g1MjhNY0llckxrQ3N0QTM3RUFXMlhUd1EiLCJtYWMiOiJiOGQ1NGEwYmFiMDBmNGEwZWY1YWYyZTZmNDE2NzY3M2Q1ZDYzMjM0NDEwZWJjM2Q4MzhlMzEzNzdjYjU3Nzk5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InU5TDBsMDZhT0ZsT1RoYm1BeWI2WXc9PSIsInZhbHVlIjoiRGlFT2VpejlqcEFvVGhmYjd3eDIxMlZOMVRIZk8rbzlMTlFKcTB2R1N5U0tMYW1IOC95NkJaTjhqODRYaWprTGJUNVlYamJpMWpMbUdZNE9iSk1udTYwYXFTVkhkQ0dIZGU3N3AyUzNERFUxTGZqdGo5UmdKNG9rY2pFUFh6WGVMVzU4b0NvbWNPSkdqQksraHpkTWxiakR2bHVKWG5MNVloajlZYzJTMGp5TzlqYkoyR1dzRGQyUW4zV2E3RHNuVnRJUkh0d29hRUFQTVlVQzJmcmlJamc3UFJwMjc5VkZtTzBNUVVxOEZ0c3UyZEdqY2xHK3liZ3dzUGQ1Zy81QnVkS1czQ2tiSGtIcFdqVG1BY0U3N0dzWnI0K1JFUWtLUzJWaFdXRTcxMTFwbnJBdDNNenpUYkU4L1FhVDk1OXB3UFZacko5Qmg2cWRUNFlFSnNtc1RwekNMbkxwa1NxUHVNdWFQdEx0L0tEczJiaVpjTHQwZzdjVWZPY2J0MkpXUW1zeVlPYmdLTTdiaXAzZkpIMmYrU3NYU3lucS91QkNpUUgzNEc5eXNSd2dvWFRFWUFJd0FxcGZQWGx6d3gvVzNrd21xejc3azY3NTFHakxTK0FITXhjWUMwc1NVaENoc3UzaWloNGZaeG53N2k4UDh0NHdIL00xOXJucTZYVkwiLCJtYWMiOiI1MDYzOTIwNDk3YmVhZWMzNTdlNjcxNjViYzEyYWVhYmIwMjNhOWJiN2ZiNmY1MGRkYjE0YmQ3MDI3ZGRiZTFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtGd1d2QWcvYmpGTHRmdllpSURvR0E9PSIsInZhbHVlIjoiZy9jdTgwcjhmckIvNm1kZTlOYWxhcXJrVXBxekN5ajU2cHhnRnN6bjNlUjBhVEdBZG9pWG1RLzRpT09MU1JZckkvWnlMSnRmV0F5NytpTXZZbDk1cUNvZlpENk9ucWdUSklXQTB1MUtTekxLL3NtTjBpUzFzSlBTUmlXV0g3RTliU2dPOWQ5L1pJRFZ5L0ZKK0o5KzNGM0R0QmFsTnBRNVB3NDJhRFdhaWtqNWJuRmREclViTVJRb1ZPRDc0K0M1VGhOeVNJeVBBOTU1QWpxTVp4MWY4Y001WG5sTVRFNVAralp1dEQ4ZWhYK3hGemlkbkUzY05zYVNvUU1hU1VFdXdDVFRna2xlVkFQdk5uc25ZWHNub1QwRnp1cnJ0RGdkSTdwU3RxUm95dW4xVWZKc1NIWVpGRjVETmRVOVUwNk9sV1MwK1k0bGpPU3RjMDIvR2E4RXZOR1ZSZ1kyVkxGQmt0YlViejRQY0NrZFExZ04vRGNlVHc0VlVLamZxM3Vrc3lZbHlERVVGUFpiV29FQXZuSFVzeHBDWjE1MTEzd3lJcXpEZzdiMHp1NEZ0ZWcva2dPZnNScEM3TDRpMmVNOFNNOHY2am9sTUx1OGxaRFU4NmptYU9QNHZEZVFDRmVKYmpRb2lQc0g1MjhNY0llckxrQ3N0QTM3RUFXMlhUd1EiLCJtYWMiOiJiOGQ1NGEwYmFiMDBmNGEwZWY1YWYyZTZmNDE2NzY3M2Q1ZDYzMjM0NDEwZWJjM2Q4MzhlMzEzNzdjYjU3Nzk5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958049683\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}