{"__meta": {"id": "X8e99102b3aea943a18566ee280dd6290", "datetime": "2025-06-08 14:52:16", "utime": **********.375879, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394335.776926, "end": **********.375898, "duration": 0.5989718437194824, "duration_str": "599ms", "measures": [{"label": "Booting", "start": 1749394335.776926, "relative_start": 0, "end": **********.257476, "relative_end": **********.257476, "duration": 0.48055005073547363, "duration_str": "481ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.257495, "relative_start": 0.48056888580322266, "end": **********.375901, "relative_end": 3.0994415283203125e-06, "duration": 0.11840605735778809, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01669, "accumulated_duration_str": "16.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3018692, "duration": 0.01349, "duration_str": "13.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.827}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3276968, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.827, "width_percent": 3.535}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.349828, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.362, "width_percent": 3.655}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.353467, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.017, "width_percent": 3.595}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.360205, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 91.612, "width_percent": 4.733}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3647158, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.345, "width_percent": 3.655}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1139031510 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139031510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.358817, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-494601949 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-494601949\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2129502450 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2129502450\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-245955362 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-245955362\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-698497265 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394122823%7C63%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxNbm9tWTRYWjNPT3ZlclhyVys2RWc9PSIsInZhbHVlIjoiVkpFVnpJUUt2KzZEYTFUMUxYU3FVcGxWQ2ZTRnRQY1BoYmtlblU5ajI3ZmxmWFlDcDB2MkNCNEJhNFJEcUFlWHJlZzIwTk5SSXd1Q0JCVlYzMWRyRUlQV1lURk9BUXNqMEdxbkx6eVNwZzRQaDVVb2Q0UW9DNXgyK2tFbTRGVGcvb3JjeDBLNzBiU0M2SURhNjczOCtCblhKSURjWTBhNEhLbkxEMWp5SVJ3YVVKYnVDNjBFSGJsRllrdVB6bG44YjFnZVdweUVCaDRJeGNEVklnYkd1ZDY4NjVDS2c1MmcyUldSQ013S21EcjUrVFhjZUhSZmR6dk9XcFlJUWgvOTBLQ3Qyb3dNRGtubXRETW8yVTlTK2hsbkpJTVdrTUdhOGRLQlc0RWQ3MUs3d0NGd093VWZwcU5SL2UwZkJURzg2K0ZNNE1ERFF5LzJBenFWdTJ2V0s4aUtpd0VleGRHZVpPWWgvclRZMHFYRmFYK2UxcnVtbnM1ZWdjVk1JaEFsRkhIRU9raDd1djM1elJ4NmJ0cXcydVJWbmdjKzVGOGtBMC9RVldrM0lLcEdtcC9Cd09ycjczK0dISUp0Z3o4WlVkcHZUYjl3VDZYZG85dzh5S0Y4YzM3OEt5L2h3YzJLRmZ4TnZMTVFuc2U1SXErKzRLQkFkK3IvOXhPWTFJV0ciLCJtYWMiOiI5YjI3MTY5NDg0MjZkOGJiMThhODZlMTU3ZDk4ZjFjOGU3YzhmZTM3MzBkZjAwZTI2NjVlNjQ1YTExZmQyMDk5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjEzMjI3QlRLZy9ieDNrOFZrYUpwamc9PSIsInZhbHVlIjoiTWM0aURQREt1MU11UTBVdzZ1Vkphb3ZWb2lmNFNQOW1kMHRwL3VEZDROQmF1QitSdVgvd3RoWnZ6TzVwUUJXdkRxdkZFakwraUVPV0pYdWQ1UDlPUUNpVkRWMUYvYjRMYWFSUHJudm1GUnVJZzB0VS8ydXdIOHpvdUZxM2NUQTRxMU91TGZ0Z1gvV0ZIQlQyellONnA3T0dlSHNaSDQwNmJKaHhyb3VsMTRGYU9HVkYxWFJjSFBnRm9wb3U3UlBzdjIxbHNQS0cvR0YwU05oQlV4Smh5VGU0aVpmR0VnRUk0VmVmbDRkRXY5c3FVcGpCK3JDMUdTOU5hTXAxUHJHcE55c09EWE56TXk4QlVCeVo0WWU0eGUyYUxCQlJwTWU3QkNyZXFCNTAyWExNbXVsbXg1dGhsMDV3Z3JMT0tsVktvQ3Zxb2d6MGZmZFpJU1lYUFV0UlFUem5sdkFrMnMzVUR5Mlh1bkRFYUZoRmJTRXJ2MTRWUTI5Q0hHVllGbWRSYjZqc240dG1Fa0JGQnE0KzBGMXIzTjR3d01tVjh2Mm1rWDl4Q0VYWTRTaUw0NUlZMERMZXNId3k0Nmh1K2hBN0QxVTVKbjZGdmp4MlhOR2R3RXlCVk1kUEhRaFZXOEFaTllaeFN1SEliZnY5Rk1WZk9yWUZ5dTdHOGUrNnFaMVkiLCJtYWMiOiIxNmMzNGFjNTBmYTYzMGJiZjY4ZjM0NWQ2ZDE4MThmYzFmNTRkN2RjNDYyZGQzYTA0NjhlYmRjYTMyZDY4MjRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-698497265\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-361014618 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361014618\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1141994438 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InB2TDlhT0IyTnhyMDNOenlNVnA3dlE9PSIsInZhbHVlIjoiK2JqNTB3c2JtWk5Xak1TdXorSSszWTR4VEVoM1NObFhwNG5ZTTFOcHk1bjRCWklKeDhvdGxhN2UxbHA5MDJZYU9vTUtqdTI4U3NEZzQ0UHRaOTJDaytFMmpMVG44SWs1RlBhc0xPY3hjVUEySEhKakJxaUxWNi9OWXpEbXVQbmV6b1ZSdWYvejU4Z2tNNmJLcDV5TllqUFJ3WDVIeDJBVmpxWHBHZXJKK3BpRlJFQTdFNFR6dGhLaytqWU56N3dkVHBMWmh3NzVmSGxWY0V2WEx0NVlyMlAyVlMxeXh0Y096d1ZlbkppVjBxOUJCTVFUSTBIVGx3YW15Q040OWRJOEdnZFdUTkFTMFlQK2VzQXRxc1RCb3RUQW02ekp4WnV2OUF1YUY4LzJKMTRHN2VSMzZVb2xjM0ZrZkhkUXo0MWQ3QTl0SG1pNHVaejBwWEJhUkF6OUZGYWkzOURFRzdPNjh0ekVlL1k5cEpsemNuTG5LR2NGVnFoZDZkc0d1eWdZeXJSeHVVdHp0ckFBL2VSMm5UZE1UVGFnaXVzRHd3T1R6ek5ocXBmSnVLUTV6MXdLQlkrSFVTRFQ5T3NZeHV5aEJpNzZGdm9icUw5NGdYWkdPalFBNzBDamdZMlp6SkQybTJJclpnanR1eGVCdytxRENxQlhpL010eGJpY3pSam4iLCJtYWMiOiJmM2U2ZTcyYzcyOTU4MWQyMmI4NGRhMWU2ZjRhOTg4Y2VhOTQyNDk0YzJjZjJjMDBlOTQ4YTZjNjliNDRkODA0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9pMzlHaDdndWlab0R1Tm5FNW90eEE9PSIsInZhbHVlIjoiOFlsOHJnUHJiWWRsS3ZxeWs1WEMrTFk3VTVGT21Db2pFRXV4UEQwUGdGRlYvLzcvd2NvZFk5aWswa0M1Zk5aZUl6eGpUa05yU3Y1d3AwNW1mWG1PZlE3byswZUpsaWx0RDc4UWM2L0xYcE44UjVWSFE0eXBaQzRvVGVFa1FOUTJTQ2k3M3hxYjl6bmZFK281Vm9sNTNDSVcya1Z0RVdXSmJCNlZmRmhaVUpqbW85RTRHenJJazhlSmExaFpjVTRHQ1JjR0tveDV0L3RCSWhaOVhHWHRYMWEvTWkvS0JrUGo3T0FVWk12bGRlb1BIRThyeHBxMlUwNm1DKzFrRFhyUTB6OHZ3aXFvVFRQdHQrZFpuaHR3Y0huUEVReCtiSzRTRUhkV1luZGJTSWp6NkxuVDhKcDdLelhISiszemM1RmI2U0hZU3ZzZUZsUDR6VkkvOTB2QkxiUzhCRERkaFRqeEZKck9XbzJrVmEzcngveHB2UWhFOGp4Nm5lY3YzZVh2RkxobFhKNUNGMFdPajY2aVB4K0RNQmtIQjhNRlhORVVRWFVVZWFzUy8xcEVzTCtEUjAxZytSUjhhcTNaY3B4V25VdStwSmFpUU1sK1Q1QXkyZjNQV1B1aEpPM1B1ell2RHFTWWJPVEN2ekZwQ3VXVmZWMVpRcm51aVJUM0JUcVkiLCJtYWMiOiJhMDczM2YyM2YzN2ZkNTZlMmZiNWNlNDk3MmY5NDcwNDVjMTllYjBkZmYyNmU5MDYxZDcwMjBlZGM0MmI0ZGNmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InB2TDlhT0IyTnhyMDNOenlNVnA3dlE9PSIsInZhbHVlIjoiK2JqNTB3c2JtWk5Xak1TdXorSSszWTR4VEVoM1NObFhwNG5ZTTFOcHk1bjRCWklKeDhvdGxhN2UxbHA5MDJZYU9vTUtqdTI4U3NEZzQ0UHRaOTJDaytFMmpMVG44SWs1RlBhc0xPY3hjVUEySEhKakJxaUxWNi9OWXpEbXVQbmV6b1ZSdWYvejU4Z2tNNmJLcDV5TllqUFJ3WDVIeDJBVmpxWHBHZXJKK3BpRlJFQTdFNFR6dGhLaytqWU56N3dkVHBMWmh3NzVmSGxWY0V2WEx0NVlyMlAyVlMxeXh0Y096d1ZlbkppVjBxOUJCTVFUSTBIVGx3YW15Q040OWRJOEdnZFdUTkFTMFlQK2VzQXRxc1RCb3RUQW02ekp4WnV2OUF1YUY4LzJKMTRHN2VSMzZVb2xjM0ZrZkhkUXo0MWQ3QTl0SG1pNHVaejBwWEJhUkF6OUZGYWkzOURFRzdPNjh0ekVlL1k5cEpsemNuTG5LR2NGVnFoZDZkc0d1eWdZeXJSeHVVdHp0ckFBL2VSMm5UZE1UVGFnaXVzRHd3T1R6ek5ocXBmSnVLUTV6MXdLQlkrSFVTRFQ5T3NZeHV5aEJpNzZGdm9icUw5NGdYWkdPalFBNzBDamdZMlp6SkQybTJJclpnanR1eGVCdytxRENxQlhpL010eGJpY3pSam4iLCJtYWMiOiJmM2U2ZTcyYzcyOTU4MWQyMmI4NGRhMWU2ZjRhOTg4Y2VhOTQyNDk0YzJjZjJjMDBlOTQ4YTZjNjliNDRkODA0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9pMzlHaDdndWlab0R1Tm5FNW90eEE9PSIsInZhbHVlIjoiOFlsOHJnUHJiWWRsS3ZxeWs1WEMrTFk3VTVGT21Db2pFRXV4UEQwUGdGRlYvLzcvd2NvZFk5aWswa0M1Zk5aZUl6eGpUa05yU3Y1d3AwNW1mWG1PZlE3byswZUpsaWx0RDc4UWM2L0xYcE44UjVWSFE0eXBaQzRvVGVFa1FOUTJTQ2k3M3hxYjl6bmZFK281Vm9sNTNDSVcya1Z0RVdXSmJCNlZmRmhaVUpqbW85RTRHenJJazhlSmExaFpjVTRHQ1JjR0tveDV0L3RCSWhaOVhHWHRYMWEvTWkvS0JrUGo3T0FVWk12bGRlb1BIRThyeHBxMlUwNm1DKzFrRFhyUTB6OHZ3aXFvVFRQdHQrZFpuaHR3Y0huUEVReCtiSzRTRUhkV1luZGJTSWp6NkxuVDhKcDdLelhISiszemM1RmI2U0hZU3ZzZUZsUDR6VkkvOTB2QkxiUzhCRERkaFRqeEZKck9XbzJrVmEzcngveHB2UWhFOGp4Nm5lY3YzZVh2RkxobFhKNUNGMFdPajY2aVB4K0RNQmtIQjhNRlhORVVRWFVVZWFzUy8xcEVzTCtEUjAxZytSUjhhcTNaY3B4V25VdStwSmFpUU1sK1Q1QXkyZjNQV1B1aEpPM1B1ell2RHFTWWJPVEN2ekZwQ3VXVmZWMVpRcm51aVJUM0JUcVkiLCJtYWMiOiJhMDczM2YyM2YzN2ZkNTZlMmZiNWNlNDk3MmY5NDcwNDVjMTllYjBkZmYyNmU5MDYxZDcwMjBlZGM0MmI0ZGNmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141994438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-113377672 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113377672\", {\"maxDepth\":0})</script>\n"}}