{"__meta": {"id": "X81a2c121ed621e826c0d8ec7382f0c70", "datetime": "2025-06-08 12:56:15", "utime": **********.243585, "method": "PUT", "uri": "/users/17", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387373.758473, "end": **********.243623, "duration": 1.4851500988006592, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1749387373.758473, "relative_start": 0, "end": 1749387374.904631, "relative_end": 1749387374.904631, "duration": 1.14615797996521, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749387374.90466, "relative_start": 1.1461870670318604, "end": **********.243627, "relative_end": 4.0531158447265625e-06, "duration": 0.33896708488464355, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51503416, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT users/{user}", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.update", "controller": "App\\Http\\Controllers\\UserController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=252\" onclick=\"\">app/Http/Controllers/UserController.php:252-315</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.032869999999999996, "accumulated_duration_str": "32.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.004217, "duration": 0.00535, "duration_str": "5.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 16.276}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0366771, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 16.276, "width_percent": 3.712}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.085869, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 19.988, "width_percent": 5.324}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.094045, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 25.312, "width_percent": 4.989}, {"sql": "select * from `users` where `users`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 284}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.107648, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "UserController.php:284", "source": "app/Http/Controllers/UserController.php:284", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=284", "ajax": false, "filename": "UserController.php", "line": "284"}, "connection": "ty", "start_percent": 30.301, "width_percent": 4.29}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>' and `id` <> '17'", "type": "query", "params": [], "bindings": ["<EMAIL>", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.154264, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 34.591, "width_percent": 2.768}, {"sql": "select * from `roles` where `id` = '17' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["17", "web"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 120}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 298}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1625679, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": "vendor/spatie/laravel-permission/src/Models/Role.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "ty", "start_percent": 37.359, "width_percent": 2.799}, {"sql": "update `users` set `type` = 'Cashier', `users`.`updated_at` = '2025-06-08 12:56:15' where `id` = 17", "type": "query", "params": [], "bindings": ["Cashier", "2025-06-08 12:56:15", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 301}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.169714, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "UserController.php:301", "source": "app/Http/Controllers/UserController.php:301", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=301", "ajax": false, "filename": "UserController.php", "line": "301"}, "connection": "ty", "start_percent": 40.158, "width_percent": 11.895}, {"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 3059}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.181177, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3059", "source": "app/Models/Utility.php:3059", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=3059", "ajax": false, "filename": "Utility.php", "line": "3059"}, "connection": "ty", "start_percent": 52.054, "width_percent": 3.438}, {"sql": "update `employees` set `name` = '<PERSON><PERSON><PERSON>', `email` = '<EMAIL>', `employees`.`updated_at` = '2025-06-08 12:56:15' where `user_id` = 17", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "<EMAIL>", "2025-06-08 12:56:15", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 3061}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.189086, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3061", "source": "app/Models/Utility.php:3061", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=3061", "ajax": false, "filename": "Utility.php", "line": "3061"}, "connection": "ty", "start_percent": 55.491, "width_percent": 14.846}, {"sql": "select * from `model_has_roles` where `model_has_roles`.`model_id` = 17 and `model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.201194, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 70.338, "width_percent": 3.894}, {"sql": "delete from `model_has_roles` where `model_has_roles`.`model_id` = 17 and `model_type` = 'App\\Models\\User' and `model_has_roles`.`role_id` in (19)", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User", "19"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.209476, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 74.232, "width_percent": 12.504}, {"sql": "insert into `model_has_roles` (`model_id`, `model_type`, `role_id`) values (17, 'App\\Models\\User', 17)", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.21901, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 86.736, "width_percent": 13.264}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-883089684 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883089684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106327, "xdebug_link": null}]}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/17", "status_code": "<pre class=sf-dump id=sf-dump-1584809735 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1584809735\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-771734984 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-771734984\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-476457452 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sagheer <PERSON></span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476457452\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2111646810 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">141</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387360042%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRXR0p5NnZpcUdzUXdYZWt6RWNvTEE9PSIsInZhbHVlIjoiZ1JYV2dvOHhnUTJlZVo4RkszR3JxVHVmVDIvWFB2OFRWYzY5dU1nQmx0NHJDUVV1VGNTKzhzNThOV1p4TFVtSGRLL1hndWpXNW5ZK3Y2VG9obWZHVk5qQ21tTFphaHZRMEVMaGNmUEd1OGpwOUl3OWRaTVA5c3hqZ01GeVRUOEt3RDdYUFBZODZlQkFETlVrcEJUWXpkSnpDUXFoUGxxUmxGNUppOFlnUG13MWF3VTQ4YTBNNHJqRlZoRGY4TWplN3FtWXY4L2xkWHl4ZzZkQ1BSWWV5RjhzRE9FTTZVeHJtVE8vUHl6dDlyUDdEZHVNT29pQmVJZmNaRUI5L3dhNHZKcWdpYXpYOHdvdjRHM1dpNTlHdlZxelRYNGNYNWFSN20wRFY0SmVMR3R2cHdONEo2bC9qM04xOVRVRXAwSkVrbktsWjlnZ0grRUhGR2pvSHNFNFpXbk1pYU94dW1wN2ptRFFBa2ZpTmdmVFcvK0xpK2RidWcwVng0YVhFMDB6MUdtQyt5MDB5UU5BQ1oyenM5RHVXQnd0RXVwZllUMHpxK2RDeVhremJJTnZCRU41RExzbGNlMCszejVMeHlDMXpteEMyV3RaNWhNWHM0NS9hZWFIdUlBK2pqY0FuNTRyNXVRKyszYXRmQUF1WkhwdkRuTElBKzA5RnUxVERMQTkiLCJtYWMiOiIzZjUwZTRiZGZkOWRlNTY5ZGIxYzY3MDg5ODEzYmRiM2RiMDYzYTVkMmVkMzllMDExOTgzMWViNWY0ZGI0MGUwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVjY09qcm01KzZkdHZnaFFPQ05Oa3c9PSIsInZhbHVlIjoiVHc3WTNkZ2U2UFZXdnU5TlFoZnNKU3JyRHpTRTNnNE0rOVpUODdQVFdOMW0vd0ZlalQwQjhUdG5RZGkxc0ZhWmRaN09vMmJMcXhmalNMRlFxVm1JUXVyRS8wN1hlNytqRnJLMDh1ck9sNXJ5UzlCbUdvOWl5WjdwZFJpcC9NSllGWW5NZDNDMGd6MzBORWpnMDg3ekdrMHVrN2F2eHAvQW1CWTlIVXBLQStnK1h3ZkQ0UEJ1bjZnUXZqZmVOQlBnNDNVTFJHbkZ1VEVFTUhvZjJRQkNXS0ZobzNlMG9QU3pFNE1RdU9rU2pIb3RlSXB0OEExSTYraUNXWHArSzJzZGVaaGFncm9Ddy9RWTRWb2hROFE1a1A3SmI1aktONmY0SC9MWDF5cEdRRkkzcmQ5aXNUUVkwaHJaMnFXU1d3WWYrd2YvaWtBdjhNUDc0dzEwR084dzdMYnBNL2hndTNVcVBKKzBFZC8yMGljbmwrZmVqWFNmRzA3dGtRQXB2OGdTSTNNTW5wSnF4dE12YjFQR1o0TDZmWXY5UHZKRmFhdHhsTlhjRC9hVFljV0VkNHpEQ0ZKeXA5djdCNFgxeUFTSSs1QmdKQ25wZHJiaE1TcFRPWkNOWHNUWmVJbHYzM09NK1NIRC9VdjM2K1I2clBYcEYxK3lFQjd5WUhxakdiRlUiLCJtYWMiOiJjMGY2NTBlZDU4Njc4OTljOTU2N2RlMTk2NWI4ZGY1YjIzZjRjYjQ5M2VmYTJmMWIyZWYyMDBlOWEyNTU5NGRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111646810\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-606846624 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606846624\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1198630795 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:56:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZQay9lRHhDeTA3RmZHSEViY0pJMXc9PSIsInZhbHVlIjoiNWZhaitlQWF5LzQ4Mnl2bzhHeUN0Y0pETFd1TGppT3JQYVZRRURzUUJIR21MM05EQWVmMmVRODg1bDF5QTZYUDNqandydXg3SlF2ZnRKOXFqM2ZPQXhVbVRqS3lDUlNPM2l1d095Y2c0TTRuc2dmc1J0TFQ5eEtZdDAxQkVmQ25NSmZ3WUlmU0NEZjhvYjFRVENFYk9QZDRPdlFHRGhRRU9rUkhGc0JhOUh6SVhHamtuZDVMV1RHNXhLRjZibWhicVZ3eEdrcUxtSTRrSXc5eEV2VlJXQk12U25RSmE5NTBOdnFQeStlc1dZYmRsY2pOQ1BaWGIreHJPNkRkVHFsWlNPdVVNcHNySk9hbTZpcVBJRFhLVjZJdkk4L1ZPcVpjTUdiVmlKL1Y2UHYzd0Y2dGhMNjk1Wm55NkNMT0xJbGdPaUtHMnROOGVGNmZ2MWhvbCt0bFUwWnJOaUE5NWo3bXJTSEl4VEdNdXI1alpYN2l4RVhvWHpHWUZMWVZna00wdWlIMFE0aFRVaFk3R3RWaGo5T0JDTG85eFFUY21vTC94cHFSbkwwaGdsL1dudjh1S3FGNk1rbXhIN29WUnp2WEVON3IwS0l3anFYdHpTZ1Z2ZEt1RlQzVFByTXZVOVdtVVNiUlo5RkNZbVRUdUxCNEVMT04yQVkxN3ZLTmVWZjkiLCJtYWMiOiJkNDQxMmM4ZjQyYThkZjlhNDhkYjM2NTgwNTA0ZWY3ZGE2YTQwMTYwYWI4Y2VhNzE5NzQxZDgwYzEwMjU5NmFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik55N2RuUkVxbm90TnlJam5ZdEJDK3c9PSIsInZhbHVlIjoibVVuOUJpNEJHSWxGY0ljT0dMMEMwcSszQUVlQUc0TzllN2RKQmhGTVVuUGpuUkplWVRaWmtkbWRHUXl3bVlveGVYbk1mMVNIMW00cDc1Yk9IdE5zQVJheEQwRDhRZ3lvcGVndW5JR0hrWnBZWDJ6Y0lxdVRtYVVDb1k1ejgzdmZmRHdiLzIvSWpnSVgzTEtYdE9jRnFMWGZLcmtBNzFoVjNWL0czMkdBQ3h2UXdjdU9LbGJGVDEvbmpLeEczMDRHSDk5K3MwVjJYa0J4NTRVdGxjcEo5NEJyVThOZ1ZtaU9TZ0UvWThpRW5WQVZFbjFZK3BJZkRBQUxOdTZVdVJCL1hBUEdLdTZ1Ui9QT0pUaERyNkg1YW1kcGhOQlVXeGptZTJNbTJla3pWY1NyWkNLTG50WGhORXI5YWVLQUVtWHpPUHVjZjdDbnE1cDI2elFZZ293UVpQUWsrMTkvRjVDWElyd3JORGNSZ1FEOGNpRDQ0dnp1SDIwUkRrMVBaQ0FCd3dkb3kwZlp2ZE92VnJZaHRGUGVZOTBYb1RBRjJOdEV3UE1ZNjNEKzZDcDh5R2FTVTgvdk9NMk9idWplMHp4NUFpR0QyNTIzVWRiVGZxNmh2L1UzcVdNclR3QlcvRnFtNzAvUmJ1ZU8xdjc1ZkFVd2haa0dqcTlhdEUwYjJQOXQiLCJtYWMiOiIxYjVhYmYzOTU0NjZmZjI0YTE5YTQ1NTM5ZDg4ZjRjMGJmMmQ3YmFjOGJiMTAxYWNhMTVmNjc4YjIzNTFiNWQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZQay9lRHhDeTA3RmZHSEViY0pJMXc9PSIsInZhbHVlIjoiNWZhaitlQWF5LzQ4Mnl2bzhHeUN0Y0pETFd1TGppT3JQYVZRRURzUUJIR21MM05EQWVmMmVRODg1bDF5QTZYUDNqandydXg3SlF2ZnRKOXFqM2ZPQXhVbVRqS3lDUlNPM2l1d095Y2c0TTRuc2dmc1J0TFQ5eEtZdDAxQkVmQ25NSmZ3WUlmU0NEZjhvYjFRVENFYk9QZDRPdlFHRGhRRU9rUkhGc0JhOUh6SVhHamtuZDVMV1RHNXhLRjZibWhicVZ3eEdrcUxtSTRrSXc5eEV2VlJXQk12U25RSmE5NTBOdnFQeStlc1dZYmRsY2pOQ1BaWGIreHJPNkRkVHFsWlNPdVVNcHNySk9hbTZpcVBJRFhLVjZJdkk4L1ZPcVpjTUdiVmlKL1Y2UHYzd0Y2dGhMNjk1Wm55NkNMT0xJbGdPaUtHMnROOGVGNmZ2MWhvbCt0bFUwWnJOaUE5NWo3bXJTSEl4VEdNdXI1alpYN2l4RVhvWHpHWUZMWVZna00wdWlIMFE0aFRVaFk3R3RWaGo5T0JDTG85eFFUY21vTC94cHFSbkwwaGdsL1dudjh1S3FGNk1rbXhIN29WUnp2WEVON3IwS0l3anFYdHpTZ1Z2ZEt1RlQzVFByTXZVOVdtVVNiUlo5RkNZbVRUdUxCNEVMT04yQVkxN3ZLTmVWZjkiLCJtYWMiOiJkNDQxMmM4ZjQyYThkZjlhNDhkYjM2NTgwNTA0ZWY3ZGE2YTQwMTYwYWI4Y2VhNzE5NzQxZDgwYzEwMjU5NmFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik55N2RuUkVxbm90TnlJam5ZdEJDK3c9PSIsInZhbHVlIjoibVVuOUJpNEJHSWxGY0ljT0dMMEMwcSszQUVlQUc0TzllN2RKQmhGTVVuUGpuUkplWVRaWmtkbWRHUXl3bVlveGVYbk1mMVNIMW00cDc1Yk9IdE5zQVJheEQwRDhRZ3lvcGVndW5JR0hrWnBZWDJ6Y0lxdVRtYVVDb1k1ejgzdmZmRHdiLzIvSWpnSVgzTEtYdE9jRnFMWGZLcmtBNzFoVjNWL0czMkdBQ3h2UXdjdU9LbGJGVDEvbmpLeEczMDRHSDk5K3MwVjJYa0J4NTRVdGxjcEo5NEJyVThOZ1ZtaU9TZ0UvWThpRW5WQVZFbjFZK3BJZkRBQUxOdTZVdVJCL1hBUEdLdTZ1Ui9QT0pUaERyNkg1YW1kcGhOQlVXeGptZTJNbTJla3pWY1NyWkNLTG50WGhORXI5YWVLQUVtWHpPUHVjZjdDbnE1cDI2elFZZ293UVpQUWsrMTkvRjVDWElyd3JORGNSZ1FEOGNpRDQ0dnp1SDIwUkRrMVBaQ0FCd3dkb3kwZlp2ZE92VnJZaHRGUGVZOTBYb1RBRjJOdEV3UE1ZNjNEKzZDcDh5R2FTVTgvdk9NMk9idWplMHp4NUFpR0QyNTIzVWRiVGZxNmh2L1UzcVdNclR3QlcvRnFtNzAvUmJ1ZU8xdjc1ZkFVd2haa0dqcTlhdEUwYjJQOXQiLCJtYWMiOiIxYjVhYmYzOTU0NjZmZjI0YTE5YTQ1NTM5ZDg4ZjRjMGJmMmQ3YmFjOGJiMTAxYWNhMTVmNjc4YjIzNTFiNWQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198630795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">User successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}