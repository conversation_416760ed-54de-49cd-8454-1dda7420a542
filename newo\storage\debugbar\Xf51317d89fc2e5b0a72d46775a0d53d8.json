{"__meta": {"id": "Xf51317d89fc2e5b0a72d46775a0d53d8", "datetime": "2025-06-08 13:44:57", "utime": **********.720587, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390296.443343, "end": **********.720618, "duration": 1.2772750854492188, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1749390296.443343, "relative_start": 0, "end": **********.588204, "relative_end": **********.588204, "duration": 1.1448609828948975, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.588228, "relative_start": 1.1448850631713867, "end": **********.720621, "relative_end": 3.0994415283203125e-06, "duration": 0.13239312171936035, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43932184, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01467, "accumulated_duration_str": "14.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.678072, "duration": 0.01357, "duration_str": "13.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.502}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.70082, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 92.502, "width_percent": 7.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-2055853494 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2055853494\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1625386963 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625386963\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-164416245 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-164416245\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1745342148 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZGbkVmVU5vdGlQTVlJcnRYd2t1N2c9PSIsInZhbHVlIjoiM1R1NlA4TUNZRTNmWnpCVk1WNkg3Ym5DbWg4QWQxd1dXNjhPL0VzU2M5WnV2RGxpdjM2R2wrWVN0Q2Q0bUdkTkhHUjVOSEJWOE1MbEJVWllEdWM5aWxGTFczSzB6aTFuL0dWais3ZnhiT296TGpHQjJGaURUTWltVGhqQTBwcXBlQXZTZGo3MlYxd2taRk5FVzVZUDBuZ0tEVzdHS2NwakZRdm5idHJtUGhsUjdKdUhhSE1oMFcydTBoc2M4KytNa1F2b0dxakFId1NQWUk2blZEV2JJVUFUYmRhN3pIZGM3dWN3Yk05WkV2Y3lleldiTkxvZkVkZW5scjRmYXExTjJWMXNNS3BvYmpIdWdVM0UwUmVITUhkcWVUMThzTHhNSm1BWXFUUWJNVUdieW1KR1pEaDdwTHl4akJjaXJpdGIxVjVMQnBEUXBXZE9vVXJzenhWODVLNUdDUCtHcFJZNGF6QXNxWHZBQndlWWJ2Sm02MlNsZ254cUpzM3ZZSXVUdjlXMjFXT2xMcFhsSUtHMjMyWDBwUDU5MHBjbnZsSjR2TUJyZGdaeURqS3hUSVpnZ1NGSG4zRFY5UnAzbkpGOVZMOWQ0cHNwTlFRZlF5TFRoOTA4dWVHM2hDYTUvQ3ZhMGlaTzZtdUFnbkJKUFJHd0R3MFZ3bUJxTnFJdTVxeWoiLCJtYWMiOiIzYTNmZjVjNjI2YjYwZWVjYzUxOGY3MDU4MmY4MDVmNzA3NDhiZWM3MTMwZjg1MWIyODAxMDJiZWQ0MGIxYTdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imt0aUdTRVRVMzhVTE5HK0VvRUpJaVE9PSIsInZhbHVlIjoiRi9BTTcrOGsxK3VnV2lZWmFndXpNUUU5dWowVDhNaWxOaXNNSmhXZWxxakUxdkkrYXZHVGNuMHpUNDBxWHlpZjRmc01CUkY0cDhmRlplaFFjaFVKNGNYUDVSaGpXUjNuMDQzaVR2aFRNMDE0S0svSTIyb1NtdHFqQk1ITklFRkt0YnRwZ3RjbFduaVFYZ1BIbHFkWCtEemJyMTEyUXhDbzdMYXNHRG9uTWZlbE14UTJ0UmR4ZXlwQXdHVU1sUTlFTStBTnRLem5QTERaMlJUVzBpZnBTbkFnRURhbkJhUS8wYU1kenZSRHM3eFNoUCtiS0ovOTNTN0EreU9nMGNBZHUyM1VWNlBVbzRBMHVhVWRlWUJrVzFGcERkQ1Fuc09vUzAyb3J3ZUMxSStEenF5bTFXYXg1cjRsUFRXNlNSeFRwdXRpM3ZsQnpMWTUrNEl3akp3dEgrL1RETkZPN0syeXVFYm5lbUl1TUJDU1NGZ1NNQnhwL3k1RVdIcjdtY29jZjNHN3N6bU0rOVRaMmZaWVhnOWhabFp1YWpkU29JYnlpMXd5MlBWUENsVEcxbmE1bHpXN0RaUHBORjlYTlZ1bUcwbzByRE5ldXBBVG1hN0cwZHNNWDBhYThNVkhGRDV3Q0JCdG5uMzl0czBVZ20vY1R4K0VadWNVbjNSN1ZyN0MiLCJtYWMiOiI4ZjQyYWE2NWFkYjcwZjM4NzBlY2IzYmE3OWY2MjVmMzQ0NWYyMGFmMDgxZDllNjY3MjYxZDQ0NDUwNmM2NDg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745342148\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1990220976 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1990220976\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1383588027 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh4cC9oOVd5K2FMRzBhTGtNVWdub2c9PSIsInZhbHVlIjoiN25QWm9ZQS9DWit5akVpTGxUYWtndkhIOXh5VlNwTGYzR3NkOXBuS2NaSUN4bWVyTUZuUkduRjVoa1M1T2NEbE02Qk1WbEEzcTZKL0NGNVBxcGRwTEFES2g5OWY3d0twMUhZMXllQ2R6My9NVGNYV0NoanAxRkhkRWhKdkNOZERJQmhCWk02MnJLZkVuTS9FL0xyK0hhVVg2dWFDRllmZXVtQnpVZEFYNDNuUkhMM0FTRUEzZkZuRkNUZUNmNG5OTDJ5Q3ZISG9wS0ZxUC9CNWI4U1VDcnBPQVlZTUVvQnBuSjFGUUJBemMxL0JYRlJHZ0VNYzhBMitBRWNiL1BrMVNyWUQ2aEU3aG9kNUk0bkpnYWExMUxuNk5BZFZWYkdHU0VBcmdYZERGZ0VXNC8wNnh6ZUo1WWUrc0RRd3RNaVNDNndvdGNxZjc4UDB2NXYwcWVXaUp6NFFwTEluZDdSdlZiNVM0aTlQL2ZWdUVLYWxXVDM2eUpWSHZaa3hrSFNWbzZQeW5nSk12V2RsaEhvL0JvWDg5ZUc1VmEwa2ZIdEVHbWJEK3BlU0hmdTBpWHNCV2VPUHdJSWpCcWF2ZVNxcE43dHBqampUUkoyczB3SWUwK2MrTG9LZVpwY21sNjB2QUc1UnVEcW1RekxDcXdpYlpNNjRKamlMVFhPaDFLcjAiLCJtYWMiOiJjNjVlOTNiMjY4ZWRkMjUyOWEwNGQyZDU5ZDM5MjYzYjY2ZTZhMTkwNDEwZmU4ZDA1MWRlMWM2ODU4MWRiZWVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJ5TnhFT2pjTnhwUVU3WnlFK2IwQmc9PSIsInZhbHVlIjoiUUFNYm96OS9FRTFGNVpxQXhlT09OK0NMRGovZlBzNjFnMU1uS3RYM1NBTnNJd0hlUmxDVlZ4WkZKWmgyeEtpK2o2M0tTSXJMRnMrTlZPaWFaZTJLalhPL3A0K2pzaDNjVlZ4WkVRTTZNTWh0QVZ0SEJGUFRhZy9uTi9jRjY5SFVjUEZHSW9EMm1iaTlObDBsODBXNlR1aUYxMFhLUXFKbjFOVUtyNTRrZTlwTnhVeSs5MXVMbndxZ0xOZUZkQ0U1Yy9mV3kvYWRhaDgwTXJqRHg1T090REVQTk9BSXBobGp6YWJjUlVYRXgzS0w1S3l2cG96TEt4SERENUR3Uld0RkJaeUtoYW01eDBjTHJtL0t1VG1QQkMxM1U5RFUxZDc1MVlQa2QvZnB4REdGbFVJckg1QXB2L0xTTDN1dzhUbWZjRnN4OWMyd3d6eEV2MjV4TzE5ZUMvRzVBVUU0ZmNjWnAxMFVTQnBqa1BMNjJoRlpiTVdjbjVXbzJXOGlEektQdHNDTGxwVTV5eTZ1NFJBRkpUWlVsZ2hYVW9rMzlyNWdqb1owcFUwOXl6aVVZZE5XTW8wL3dWRUU3L2kzREg2aGJmc1BaWll0ZEVCek1jaDFZTWVlcDl3aHlUOHBzbTZ0T00za21tNi9vQmkwd240MmxkT0dnY2xvU09KSE5KU04iLCJtYWMiOiJjMmEwM2Y1MjRkYjljMTU2MDU0NDdhMmNhZTBjYTFmOGMzNDZhZGZjNGNmYzk2N2FmMWY0Y2M3YzZiYjMwNzY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh4cC9oOVd5K2FMRzBhTGtNVWdub2c9PSIsInZhbHVlIjoiN25QWm9ZQS9DWit5akVpTGxUYWtndkhIOXh5VlNwTGYzR3NkOXBuS2NaSUN4bWVyTUZuUkduRjVoa1M1T2NEbE02Qk1WbEEzcTZKL0NGNVBxcGRwTEFES2g5OWY3d0twMUhZMXllQ2R6My9NVGNYV0NoanAxRkhkRWhKdkNOZERJQmhCWk02MnJLZkVuTS9FL0xyK0hhVVg2dWFDRllmZXVtQnpVZEFYNDNuUkhMM0FTRUEzZkZuRkNUZUNmNG5OTDJ5Q3ZISG9wS0ZxUC9CNWI4U1VDcnBPQVlZTUVvQnBuSjFGUUJBemMxL0JYRlJHZ0VNYzhBMitBRWNiL1BrMVNyWUQ2aEU3aG9kNUk0bkpnYWExMUxuNk5BZFZWYkdHU0VBcmdYZERGZ0VXNC8wNnh6ZUo1WWUrc0RRd3RNaVNDNndvdGNxZjc4UDB2NXYwcWVXaUp6NFFwTEluZDdSdlZiNVM0aTlQL2ZWdUVLYWxXVDM2eUpWSHZaa3hrSFNWbzZQeW5nSk12V2RsaEhvL0JvWDg5ZUc1VmEwa2ZIdEVHbWJEK3BlU0hmdTBpWHNCV2VPUHdJSWpCcWF2ZVNxcE43dHBqampUUkoyczB3SWUwK2MrTG9LZVpwY21sNjB2QUc1UnVEcW1RekxDcXdpYlpNNjRKamlMVFhPaDFLcjAiLCJtYWMiOiJjNjVlOTNiMjY4ZWRkMjUyOWEwNGQyZDU5ZDM5MjYzYjY2ZTZhMTkwNDEwZmU4ZDA1MWRlMWM2ODU4MWRiZWVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJ5TnhFT2pjTnhwUVU3WnlFK2IwQmc9PSIsInZhbHVlIjoiUUFNYm96OS9FRTFGNVpxQXhlT09OK0NMRGovZlBzNjFnMU1uS3RYM1NBTnNJd0hlUmxDVlZ4WkZKWmgyeEtpK2o2M0tTSXJMRnMrTlZPaWFaZTJLalhPL3A0K2pzaDNjVlZ4WkVRTTZNTWh0QVZ0SEJGUFRhZy9uTi9jRjY5SFVjUEZHSW9EMm1iaTlObDBsODBXNlR1aUYxMFhLUXFKbjFOVUtyNTRrZTlwTnhVeSs5MXVMbndxZ0xOZUZkQ0U1Yy9mV3kvYWRhaDgwTXJqRHg1T090REVQTk9BSXBobGp6YWJjUlVYRXgzS0w1S3l2cG96TEt4SERENUR3Uld0RkJaeUtoYW01eDBjTHJtL0t1VG1QQkMxM1U5RFUxZDc1MVlQa2QvZnB4REdGbFVJckg1QXB2L0xTTDN1dzhUbWZjRnN4OWMyd3d6eEV2MjV4TzE5ZUMvRzVBVUU0ZmNjWnAxMFVTQnBqa1BMNjJoRlpiTVdjbjVXbzJXOGlEektQdHNDTGxwVTV5eTZ1NFJBRkpUWlVsZ2hYVW9rMzlyNWdqb1owcFUwOXl6aVVZZE5XTW8wL3dWRUU3L2kzREg2aGJmc1BaWll0ZEVCek1jaDFZTWVlcDl3aHlUOHBzbTZ0T00za21tNi9vQmkwd240MmxkT0dnY2xvU09KSE5KU04iLCJtYWMiOiJjMmEwM2Y1MjRkYjljMTU2MDU0NDdhMmNhZTBjYTFmOGMzNDZhZGZjNGNmYzk2N2FmMWY0Y2M3YzZiYjMwNzY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383588027\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2119027387 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119027387\", {\"maxDepth\":0})</script>\n"}}