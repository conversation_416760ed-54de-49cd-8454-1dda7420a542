{"__meta": {"id": "Xe6ba25bc0d558180318950e3f79755a5", "datetime": "2025-06-08 13:34:13", "utime": **********.662991, "method": "GET", "uri": "/receipt-voucher/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389652.295813, "end": **********.663025, "duration": 1.3672118186950684, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749389652.295813, "relative_start": 0, "end": **********.429066, "relative_end": **********.429066, "duration": 1.1332528591156006, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.429089, "relative_start": 1.1332759857177734, "end": **********.663029, "relative_end": 4.0531158447265625e-06, "duration": 0.23393988609313965, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46771432, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.receipt.create", "param_count": null, "params": [], "start": **********.634645, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/voucher/receipt/create.blade.phpvoucher.receipt.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fvoucher%2Freceipt%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.receipt.create"}]}, "route": {"uri": "GET receipt-voucher/create", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptVoucherController@create", "namespace": null, "prefix": "", "where": [], "as": "receipt.voucher.create", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=54\" onclick=\"\">app/Http/Controllers/ReceiptVoucherController.php:54-59</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024480000000000002, "accumulated_duration_str": "24.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.549827, "duration": 0.0217, "duration_str": "21.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.644}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.597198, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.644, "width_percent": 5.188}, {"sql": "select * from `users` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.607373, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:57", "source": "app/Http/Controllers/ReceiptVoucherController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=57", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "57"}, "connection": "ty", "start_percent": 93.832, "width_percent": 6.168}]}, "models": {"data": {"App\\Models\\User": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/receipt-voucher/create", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1667572442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1667572442\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1397553979 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1397553979\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1235275505 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389647289%7C27%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJ5Vy9abUoxWnB4SXdpVmdxcFJPTFE9PSIsInZhbHVlIjoiSE10NDAvRjUxejlDZnZsVVZQdFo1TndxYzRoalB1bWVYWS96QTZ0UVF3S1ZSbzFJRXpXSzBSeGJzN09ta2lVVEkvZnRkUXJOcmt5dER0SlpDMmVJSnVidFZSeFlXK3NMRzJXcGpRT2ZrS1hWeVdmVWtTVUxDQVdRTUZFK3JZb0RaVEswcFU4czgyMFR6T0ovRkM4d3VIVWlyOHRiNHk0YmNQbTVXajRJTFpsTkl3ZEE1MlJWTFo0ZTJjRDMxeUFKdWRnbnQxVVNVVU8rRGpvRjFWdk5BV2pxNVBKNWk5dTBiNVJCTGdsbWxOUzNCcG82Z3hVODd2TjNlVkpFSERyc1BBTjk5SDd3cDFBUFlqTXh1K0VDRFVzSEVOSXViWThrd2xzNmxlRHAzekhBTzVKbmJVU3JaZjNTclBpMUludUdZellzZEtqUE9MNjlkS3gyaEs4NTduRWtqeWdrSmhEeUZwSmRONUZNQnRnRHhBQVBOZjlMTS80QjZPVGVjcHo4amdWSjdoNFZ0UWJDYWkzZFFwSnl6amQvc2hIOE1YYUEvbmZUV0RjaklmWSt6aUFURE9IQjk5cUtraEcvd2V3cyt4d2swVmdxdGlSdGxscUtyZTNsSUtnMmJWTlFKc2FnSENLTmwxbDQ5Q214Nlg5aXAvOGZVZkp0Y1lrOUllVk4iLCJtYWMiOiIwZjIzZmUxM2Q3OWVlNmUwYzA0NmQ0Nzg3YWM2ZTkxYmZkNzQ5M2Q3YTE0ZGQ1NjEyYzM0MzAzZGQ5OTBkZGZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjRaZHpYQy9IYTN2ZldOMjZvOVNQb1E9PSIsInZhbHVlIjoiVEkwK2lYZGExWVBmUWg5MHNrQVdJWlRra3dMeHcxWkxXV2l3N0kxK2wvdEwyYUcxL0E4c0pkbEUvUG4zTW1rOVJHZlZzTXlGT29ML0pGend2Z0w5RU5BTWZ0VFZyNWlpWTUydWgvdnFtVExld2VLQkV1UmEvblNsT1laMGo5S2NxWDZvNERNbHFlYmtnb2Rqdy9xYTc3SFo0c3BXSEZsOHI3NE9rRllaR0srZ3BvQU5selBYVlNHV1lnQWwzOVpsdFp5YUQwVGxQQXZTNWFGZjNucmczNFkyM0RjV2tGZHlyTnBITGFlL0ZBTGRzR09IV1N6RWs5eHY0L1ZhaHlBWGpCWERWMVp0SkFRNkpudURoR3JWVHcvWktybkRtWTYwNmo3WTl6QUhjMkVQWEtTV0p6NWFCOTNDQVJqa25mRzJXQXNERXZYRms4VzRpQ1hQNTkrdThSL1p1c2kvVHBVYlNtVzVCUWhRR0lvSGtwdkU3a29Mb0cybkpjRkI2YzJYbGlWNDNYZFZHVXRnYmVFN2RuRXNsbXBKK0hBU3NKM09FYlczSzVSZ1M4Tk5pZkhMMWs5WWk2UzNRQzRlQTB5QUFibFljdTNWdnFScmtTQ1ErT0o1RXRzeXNJOWxWNEY3UVFVMHM3eDRORHpsVG1PMGQ0YVM0TlcvdEZidzZORFMiLCJtYWMiOiJmOWM3MmQ2NWZjY2FjODM0NTcwNGIyMzYwNjBiNTFkMTA0YjQxZDFkZjc5MjFkNGJhNmJjMmI5MGRkY2RkMmY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235275505\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-22709826 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22709826\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-828083049 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNMVlJCK2FTSHltUnRIU2ViVC9acUE9PSIsInZhbHVlIjoiYUlpRENCYllOajdPK1BMR1dvT1ZCRUFzU2Vrc1JSQzcwN1BLSVRRK1dIalVRSERYUndoc3VrK1FVQ09Ybm5maFp5UytxVEZCRHhpeDVmS3BGNkpXTHNkNDBmaDVwbFZTWlpGa2VYQW03eWRtV2UwcmxweDRUSFYwKzN2UkYxNkE3VHJ3ZVhSU2p2bThqZ2tSekxHR1hWREovMjB1clozWURMZnN3bzRBTWN6ZElVV3R4cDRWU0RqOVBLUE9sK2JiQVc2RGRmTFdQblR3YzVqaU1KS3FjeVZqTlJrYlBsVlRLNnhObXlidFE0bFdKMXROZE5jV29pOHEvdzlLNFJRdUZaQjlHd2ludUdveHo1UnpqWGZWcTlUc1NyQ0hyMTA3U0dwYkd3bjVHbktBU0VqK2lxeUppdzZDWjJoellVQURHUlF5V284SHdEdWIrL0lTWnlSQWUzR0tsMTQ3SjRqTW4rSHN2Z1ljeU14ZTR5eWQwemx3VTRmeitUQTcrWS9ENmtVb01TVVk3aUZTQ083NmJPdnpLY0pnTnEzbXlCb3RLWHhXb0J0NG1EY21uNk4wUXFWL2NKMzVBTjBkdG9UUCtSTTAxZlF2V1cxVitOUTJ0WVIreGEreGpTb2kwSzhBU0tSL2R6UHJQODVSMHZHdDYvMENwcFFmdXNNMHdMdjYiLCJtYWMiOiJmYTc3NzBjYTA1NDEzOTE1Y2U4NDViYmMyODMwYWQ4NmZmYjUzYTVhZDg2ZDE4Y2ZhYTcwMTM1ZmNkOTVhNWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InBrRU05Z1IxcHBqZVc4anVZb0NHQ1E9PSIsInZhbHVlIjoicUJNdTljTjJES2FzQk9sOThrbm5jUVp2a3V2MDNTTGpRSEdGTi9HTjVieDkrRm5MSExFZzNOKzFmWHQwTWFKSVVJUjFIUEVMS1pVQ0RRM2VxamhnYzdZMU5SbWNweFM1VjYvRTd1cWhsY1lCeS9JSUcxaktCOGgrYzJOdjVPbXlreTFuakI3amVEL0VhQ3Rqb1M4clhVM3grRFZOWGRaRUtwWWROVzl4Qlg2TGVPdmg3eDNET0ZBUXdMQXRyTDRLRzkyVU5XNFkxcDRCZXJFNFhqcHd5ckwrM0ltK2RLMEFtWHF5aVNaTmNIWDE2SzdSQU9OUGpmTHZwNTBVVDUvS2lqT212WlhJNFJiKzJGWGJINXhsNzM0aExnMmh5UGIrTTZ2VVQ4NUxoSWhzbTl3WmQvbXRJaDRTcmlLVTljYkxvT3VYeURzUzB2aTNnSUFKZHVDT2JBQlVLcjZ1cUFMSnVWUGZnTWJCb1NEZkI5Z3hjZXNiNTQva3NNR3NRVnQrd1hSdGxONHQvVzBaalE5RTJFY2ZXamppTFBqQVdrZjZUMXdRaEVrbDc4M09xZCtQL3JKdVBhRHFIdllpRFJVUXBIV2dxaVR2dkVYL3Y5U2g5YWt2M09qUXBtdjhpYU9UKzBVam5GeFBXcVpKSkJ1eDQrN0NYVHhGNlF3KzJkdEYiLCJtYWMiOiIzNTU1YzQ2MDlhMGE4MjQxODZhZmMzNGVlNTZlNTA2Y2M1NjU2NDhmNmRkOTk4YTNmNzI2MjFhZDQwM2EzYjhkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNMVlJCK2FTSHltUnRIU2ViVC9acUE9PSIsInZhbHVlIjoiYUlpRENCYllOajdPK1BMR1dvT1ZCRUFzU2Vrc1JSQzcwN1BLSVRRK1dIalVRSERYUndoc3VrK1FVQ09Ybm5maFp5UytxVEZCRHhpeDVmS3BGNkpXTHNkNDBmaDVwbFZTWlpGa2VYQW03eWRtV2UwcmxweDRUSFYwKzN2UkYxNkE3VHJ3ZVhSU2p2bThqZ2tSekxHR1hWREovMjB1clozWURMZnN3bzRBTWN6ZElVV3R4cDRWU0RqOVBLUE9sK2JiQVc2RGRmTFdQblR3YzVqaU1KS3FjeVZqTlJrYlBsVlRLNnhObXlidFE0bFdKMXROZE5jV29pOHEvdzlLNFJRdUZaQjlHd2ludUdveHo1UnpqWGZWcTlUc1NyQ0hyMTA3U0dwYkd3bjVHbktBU0VqK2lxeUppdzZDWjJoellVQURHUlF5V284SHdEdWIrL0lTWnlSQWUzR0tsMTQ3SjRqTW4rSHN2Z1ljeU14ZTR5eWQwemx3VTRmeitUQTcrWS9ENmtVb01TVVk3aUZTQ083NmJPdnpLY0pnTnEzbXlCb3RLWHhXb0J0NG1EY21uNk4wUXFWL2NKMzVBTjBkdG9UUCtSTTAxZlF2V1cxVitOUTJ0WVIreGEreGpTb2kwSzhBU0tSL2R6UHJQODVSMHZHdDYvMENwcFFmdXNNMHdMdjYiLCJtYWMiOiJmYTc3NzBjYTA1NDEzOTE1Y2U4NDViYmMyODMwYWQ4NmZmYjUzYTVhZDg2ZDE4Y2ZhYTcwMTM1ZmNkOTVhNWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InBrRU05Z1IxcHBqZVc4anVZb0NHQ1E9PSIsInZhbHVlIjoicUJNdTljTjJES2FzQk9sOThrbm5jUVp2a3V2MDNTTGpRSEdGTi9HTjVieDkrRm5MSExFZzNOKzFmWHQwTWFKSVVJUjFIUEVMS1pVQ0RRM2VxamhnYzdZMU5SbWNweFM1VjYvRTd1cWhsY1lCeS9JSUcxaktCOGgrYzJOdjVPbXlreTFuakI3amVEL0VhQ3Rqb1M4clhVM3grRFZOWGRaRUtwWWROVzl4Qlg2TGVPdmg3eDNET0ZBUXdMQXRyTDRLRzkyVU5XNFkxcDRCZXJFNFhqcHd5ckwrM0ltK2RLMEFtWHF5aVNaTmNIWDE2SzdSQU9OUGpmTHZwNTBVVDUvS2lqT212WlhJNFJiKzJGWGJINXhsNzM0aExnMmh5UGIrTTZ2VVQ4NUxoSWhzbTl3WmQvbXRJaDRTcmlLVTljYkxvT3VYeURzUzB2aTNnSUFKZHVDT2JBQlVLcjZ1cUFMSnVWUGZnTWJCb1NEZkI5Z3hjZXNiNTQva3NNR3NRVnQrd1hSdGxONHQvVzBaalE5RTJFY2ZXamppTFBqQVdrZjZUMXdRaEVrbDc4M09xZCtQL3JKdVBhRHFIdllpRFJVUXBIV2dxaVR2dkVYL3Y5U2g5YWt2M09qUXBtdjhpYU9UKzBVam5GeFBXcVpKSkJ1eDQrN0NYVHhGNlF3KzJkdEYiLCJtYWMiOiIzNTU1YzQ2MDlhMGE4MjQxODZhZmMzNGVlNTZlNTA2Y2M1NjU2NDhmNmRkOTk4YTNmNzI2MjFhZDQwM2EzYjhkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828083049\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1267508430 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267508430\", {\"maxDepth\":0})</script>\n"}}