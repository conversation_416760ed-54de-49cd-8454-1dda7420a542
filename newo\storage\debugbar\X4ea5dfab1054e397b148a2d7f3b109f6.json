{"__meta": {"id": "X4ea5dfab1054e397b148a2d7f3b109f6", "datetime": "2025-06-08 15:09:50", "utime": **********.657671, "method": "POST", "uri": "/pos/store/delivery", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[15:09:50] LOG.info: بداية معالجة طلب التوصيل {\n    \"user_id\": 16,\n    \"request_data\": {\n        \"customer_id\": \"7\",\n        \"warehouse_name\": \"8\",\n        \"user_id\": null,\n        \"discount\": 0,\n        \"quotation_id\": \"0\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.582005, "xdebug_link": null, "collector": "log"}, {"message": "[15:09:50] LOG.info: إنشاء فاتورة توصيل جديدة {\n    \"pos_id\": 18,\n    \"customer_id\": 7,\n    \"warehouse_id\": \"8\",\n    \"attempts\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.627975, "xdebug_link": null, "collector": "log"}, {"message": "[15:09:50] LOG.error: خطأ في حفظ طلب التوصيل {\n    \"error\": \"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'total' in 'field list' (Connection: mysql, SQL: insert into `pos_products` (`pos_id`, `product_id`, `quantity`, `price`, `discount`, `tax`, `total`, `updated_at`, `created_at`) values (38, 5, 1, 12.00, 0, , 12, 2025-06-08 15:09:50, 2025-06-08 15:09:50))\",\n    \"trace\": \"#0 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(779): Illuminate\\\\Database\\\\Connection->runQueryCallback('insert into `po...', Array, Object(Closure))\\n#1 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\MySqlConnection.php(42): Illuminate\\\\Database\\\\Connection->run('insert into `po...', Array, Object(Closure))\\n#2 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Query\\\\Processors\\\\MySqlProcessor.php(35): Illuminate\\\\Database\\\\MySqlConnection->insert('insert into `po...', Array, 'id')\\n#3 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Query\\\\Builder.php(3754): Illuminate\\\\Database\\\\Query\\\\Processors\\\\MySqlProcessor->processInsertGetId(Object(Illuminate\\\\Database\\\\Query\\\\Builder), 'insert into `po...', Array, 'id')\\n#4 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder.php(2038): Illuminate\\\\Database\\\\Query\\\\Builder->insertGetId(Array, 'id')\\n#5 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(1358): Illuminate\\\\Database\\\\Eloquent\\\\Builder->__call('insertGetId', Array)\\n#6 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(1323): Illuminate\\\\Database\\\\Eloquent\\\\Model->insertAndSetId(Object(Illuminate\\\\Database\\\\Eloquent\\\\Builder), Array)\\n#7 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(1162): Illuminate\\\\Database\\\\Eloquent\\\\Model->performInsert(Object(Illuminate\\\\Database\\\\Eloquent\\\\Builder))\\n#8 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\app\\\\Http\\\\Controllers\\\\PosController.php(2110): Illuminate\\\\Database\\\\Eloquent\\\\Model->save()\\n#9 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Controller.php(54): App\\\\Http\\\\Controllers\\\\PosController->storeDeliveryOrder(Object(Illuminate\\\\Http\\\\Request))\\n#10 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\ControllerDispatcher.php(43): Illuminate\\\\Routing\\\\Controller->callAction('storeDeliveryOr...', Array)\\n#11 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(262): Illuminate\\\\Routing\\\\ControllerDispatcher->dispatch(Object(Illuminate\\\\Routing\\\\Route), Object(App\\\\Http\\\\Controllers\\\\PosController), 'storeDeliveryOr...')\\n#12 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(208): Illuminate\\\\Routing\\\\Route->runController()\\n#13 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(808): Illuminate\\\\Routing\\\\Route->run()\\n#14 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(144): Illuminate\\\\Routing\\\\Router->Illuminate\\\\Routing\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#15 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\app\\\\Http\\\\Middleware\\\\XSS.php(69): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#16 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): App\\\\Http\\\\Middleware\\\\XSS->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#17 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Auth\\\\Middleware\\\\EnsureEmailIsVerified.php(41): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#18 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Auth\\\\Middleware\\\\EnsureEmailIsVerified->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#19 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\app\\\\Http\\\\Middleware\\\\FilterRequest.php(26): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#20 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): App\\\\Http\\\\Middleware\\\\FilterRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#21 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings.php(51): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#22 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#23 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Auth\\\\Middleware\\\\Authenticate.php(64): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#24 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Auth\\\\Middleware\\\\Authenticate->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#25 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken.php(88): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#26 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#27 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#28 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#29 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#30 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#31 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#32 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse.php(37): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#33 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#34 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(75): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#35 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#36 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(75): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#37 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#38 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(119): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#39 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(807): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#40 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(786): Illuminate\\\\Routing\\\\Router->runRouteWithinStack(Object(Illuminate\\\\Routing\\\\Route), Object(Illuminate\\\\Http\\\\Request))\\n#41 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(750): Illuminate\\\\Routing\\\\Router->runRoute(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Routing\\\\Route))\\n#42 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(739): Illuminate\\\\Routing\\\\Router->dispatchToRoute(Object(Illuminate\\\\Http\\\\Request))\\n#43 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(201): Illuminate\\\\Routing\\\\Router->dispatch(Object(Illuminate\\\\Http\\\\Request))\\n#44 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(144): Illuminate\\\\Foundation\\\\Http\\\\Kernel->Illuminate\\\\Foundation\\\\Http\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#45 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php(66): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#46 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#47 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\app\\\\Http\\\\Middleware\\\\RemoveInjectedVerifyScript.php(18): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#48 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): App\\\\Http\\\\Middleware\\\\RemoveInjectedVerifyScript->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#49 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php(21): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#50 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(51): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#51 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#52 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(110): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#53 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#54 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\TrustProxies.php(58): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#55 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#56 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php(21): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#57 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull.php(31): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#58 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#59 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php(21): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#60 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(51): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#61 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#62 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\ValidatePostSize.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#63 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\ValidatePostSize->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#64 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(110): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#65 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#66 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\HandleCors.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#67 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\HandleCors->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#68 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\TrustProxies.php(58): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#69 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#70 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(119): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#71 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(176): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#72 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(145): Illuminate\\\\Foundation\\\\Http\\\\Kernel->sendRequestThroughRouter(Object(Illuminate\\\\Http\\\\Request))\\n#73 C:\\\\laragon\\\\www\\\\to\\\\newo\\\\public\\\\index.php(51): Illuminate\\\\Foundation\\\\Http\\\\Kernel->handle(Object(Illuminate\\\\Http\\\\Request))\\n#74 {main}\",\n    \"user_id\": 16,\n    \"request_data\": {\n        \"customer_id\": \"7\",\n        \"warehouse_name\": \"8\",\n        \"user_id\": null,\n        \"discount\": 0,\n        \"quotation_id\": \"0\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.649758, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.047419, "end": **********.657696, "duration": 0.6102769374847412, "duration_str": "610ms", "measures": [{"label": "Booting", "start": **********.047419, "relative_start": 0, "end": **********.519971, "relative_end": **********.519971, "duration": 0.4725518226623535, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.519982, "relative_start": 0.4725630283355713, "end": **********.657699, "relative_end": 3.0994415283203125e-06, "duration": 0.13771700859069824, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48301080, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos/store/delivery", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@storeDeliveryOrder", "namespace": null, "prefix": "", "where": [], "as": "pos.store.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1964\" onclick=\"\">app/Http/Controllers/PosController.php:1964-2170</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.01791, "accumulated_duration_str": "17.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.562042, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 21.887}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5771499, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 21.887, "width_percent": 3.35}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.600827, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 25.237, "width_percent": 3.406}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.604042, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 28.643, "width_percent": 3.015}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = '8' and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1994}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.611239, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1994", "source": "app/Http/Controllers/PosController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1994", "ajax": false, "filename": "PosController.php", "line": "1994"}, "connection": "ty", "start_percent": 31.658, "width_percent": 3.964}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2006}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6159859, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2006", "source": "app/Http/Controllers/PosController.php:2006", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2006", "ajax": false, "filename": "PosController.php", "line": "2006"}, "connection": "ty", "start_percent": 35.623, "width_percent": 3.35}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 577}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2022}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6211839, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PosController.php:577", "source": "app/Http/Controllers/PosController.php:577", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=577", "ajax": false, "filename": "PosController.php", "line": "577"}, "connection": "ty", "start_percent": 38.973, "width_percent": 3.238}, {"sql": "select exists(select * from `pos` where `pos_id` = 18 and `created_by` = 16) as `exists`", "type": "query", "params": [], "bindings": ["18", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2025}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.624476, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2025", "source": "app/Http/Controllers/PosController.php:2025", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2025", "ajax": false, "filename": "PosController.php", "line": "2025"}, "connection": "ty", "start_percent": 42.211, "width_percent": 3.071}, {"sql": "insert into `pos` (`pos_id`, `customer_id`, `warehouse_id`, `user_id`, `pos_date`, `created_by`, `shift_id`, `delivery_status`, `is_payment_set`, `cashier_id`, `updated_at`, `created_at`) values (18, 7, '8', 16, '2025-06-08', 16, 2, 'delivery_pending', 0, 16, '2025-06-08 15:09:50', '2025-06-08 15:09:50')", "type": "query", "params": [], "bindings": ["18", "7", "8", "16", "2025-06-08", "16", "2", "delivery_pending", "0", "16", "2025-06-08 15:09:50", "2025-06-08 15:09:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2084}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.628586, "duration": 0.0086, "duration_str": "8.6ms", "memory": 0, "memory_str": null, "filename": "PosController.php:2084", "source": "app/Http/Controllers/PosController.php:2084", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2084", "ajax": false, "filename": "PosController.php", "line": "2084"}, "connection": "ty", "start_percent": 45.282, "width_percent": 48.018}, {"sql": "select * from `product_services` where `product_services`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2092}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.640749, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2092", "source": "app/Http/Controllers/PosController.php:2092", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2092", "ajax": false, "filename": "PosController.php", "line": "2092"}, "connection": "ty", "start_percent": 93.3, "width_percent": 3.853}, {"sql": "select `tax_id` from `product_services` where `id` = 5 and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2103}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.644362, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:259", "source": "app/Models/ProductService.php:259", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=259", "ajax": false, "filename": "ProductService.php", "line": "259"}, "connection": "ty", "start_percent": 97.152, "width_percent": 2.848}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2004217397 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004217397\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.609353, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-406255210 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406255210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.62029, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 33\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos/store/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1598997685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1598997685\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1511023295 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>discount</span>\" => <span class=sf-dump-num>0</span>\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511023295\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-658614110 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">85</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpCV0hjNm00YlZrOFpkSnRnR0tuanc9PSIsInZhbHVlIjoiQUJWc0hVSnhmdkoxSE5BZVdubmRTc2Y0VWlURjdzRlRseG9QME0vcmVMeFZCbDEzSVhtbkVIMmZLL01DaytsajRCY0RpUW9qN1E0dm9ZRmFOK0h0RDZKNTZScUtwK1RhQXJZamx3MFd4MEpvQ1VaK1JmTG5ZSFBHcDZFbENNNDFtT3BVeEMzeTlHM010aWV3bGxOWW5xemptTFRtQ3dxMnNwTmdOUFpneTAzVEdFVm5MeXNKampaZFdJVjYvbURaZjFLaXYvbHpxMWFvd2gzK2J2RGkrSTIyejg0bDNvVE5waTZldktmUEloWlF1eldSZ1FqazdKMElHN2g5VzVEQ1R5dDdId0FzRkFJUEVZUkgwSlhOWlpscVhQMFZYRTZXRld0SEpxSGxjK1pYSTdRdTVrNXYvZkt5WnUzQ2NHVVFoQWhrbW5DSXZ2QjVnVWRIQkxUR1k5dHhic1JuVy8vQ3VYem9ZQ0NkUGl5N09zNFRGYVliSVFkT3oxditCUDhoMS93L25IaW5pREF0UndTMkZBUlVBWlR4YVFkaEZCaUh6K3RiQlhITGkyU0wrdThJb3NLQkNkT29VYjdYOFhVMklDNlFGd3RqQVpyd1FSMHdJcXIyY2J6bE40ZjBJbmh0eUpuZDNYSXN6emRDYU4rNUFUdmltbDR3RGRJRU83QUUiLCJtYWMiOiIxY2I3MmZkYzA1ZmJmMjk3MjU0OTkzMzRiZTk2YzkwZjNiYWUyMmI3MzAzMmI3NzQwNzRhZWNiNjBhZjEzZGFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhRNTBNcitVWWhZYWFaSFdXajRKanc9PSIsInZhbHVlIjoibENOVmhZQlA5eThOWE42M0c0VVYvNExlb0lqVStyWU05RGpxeVMzZXdPUE5sR1p2L1hvWlllUHppbmVRSlJyVFBnMy82a2VLWFp2MkRoSjhaTjAwVWlFa29oT09idTBXMWJMeTdCV3Z1QjNIbHlhdkQrMTdvbDNEOVZVd1crcS9XZkdUMzB0V3F5cHpiMWhkOWZpQWVkVGxEVkY5aWZJQWNvcms5Tzdwcllmblo2L29tL3BtblBNQkI5UzJ4ZnNVN0M5Uk9TaW9GUERyc09XNkl0RytPR055K3hmRXZGL2RMY2Vac1IwcWdIZUdpajR0Mnl0VzR1TzBQUGNJQ2NJaENBNEkydyttU3ZEUXZjSW5LVndNZ1IxQzBWd0d2dUhtRGRvVG44RUpRWWsxSU0vdXE3OGNNb0QxYlQzWVVGdFp0eVU3cUlQUXA2cXM4WndnVU5kaitvM1hQL0wvZWt5NTA0RVgxczFJMXBrUWhSZ2k5MUNmdUVpUDhXT1RPSnBwRGpVckRYQmdDUDJRQVM1MzFiMjNoYllLTW5KdnNFWnJpeXBHME1IOEdHeHBheDRXWTNLS2ZJK0RUa0U0Z3c3WEdwbk10L2lISENhdGpKYWNVL2hDdFNjN3IvbVVlbEdxYTJnbE5PM2I2NnF5STY0OUo3NjBIVkExaGZFNm5ndU8iLCJtYWMiOiI5ODk5ZTU0ODY0ZjBhMmY5ZjgwNDI1NjA4OWY4NzQ5NjIwYjAzY2RjNjIzMDQ4NThmM2RlZGQ2Y2FiNWJiZjZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658614110\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-468693510 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468693510\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-855995277 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlY1N1NraElGYUFUbWdDY3VNQUs2b1E9PSIsInZhbHVlIjoiMFNqaW9YRnhaVmhseEowUUtKWFVwTmVZd09hZkxrak05T3NvZ1VsaWxFVFdNdlFpbEloKzJlVkNNb3hSR1ErYWVmRStCTXJ5TkVINmNLempZRy9nTGxrcGxCdlRRSjdraVFqL1NlYmZqZUtXRmE2UVpWMGhIU0JRYkRESy9HOTc1dEdEdThlRkVXcENwczhVeVRLUmh4ZjRzTDZNVmxZUnNTdGNmMUY5OVl6ZTIxOVVra0Z4VXpJTjhzVFhGaHBscm5yZHZhMG1ScWtCbS85MWJNQVRIYlRCcFg1aWlrS1E4a2tGczFOZ3JFb3U0ZmUxWmZUYmwvOTlHa1pCNVJZbXNyVDFLQWtRaExRSVFZejJWRVN6U2htbHIvRzZCUjI2RDhWT2VFK2dlRDZvUDQ1R0NNdk91MjlBMFFHWDRyMENYN0NVMnFIQ0I2ejV1dG5pcUZnYStNRWUzRjlRUTRkbE5nNDREN0hmcllCbDRoZ005aUgxejZCbDU3Q0FrdHBXTE5taXM1TWhnMi9RZjNsUmxDYWt6UnZlNnRMM1c1NGxVSlFRbUVXUm91OXBDbHNrS3BNWWNiRWJ4NFdpb1ZVOXpSQXR2alNRZE9VNlBjQVlBNnJFYUlUZzlqZmFBSTdKZVlmZGN5ZmdCVzFBdndUZ2xwcGxIUVpVQWVxY3Fod1giLCJtYWMiOiJkMGViMTYxZmYzNDY3YzIxMDg1MzMyOTEwZjhhMjQxNjk4N2RlZTY5N2UwYWM5MWM2YTMzY2UxY2UxOTc1ZTc4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im0xQlVLMi8wSktrdWIxOURsM2VzL2c9PSIsInZhbHVlIjoiUklzaWhhTW8rN1JjcG1TUEpsTUlDM0Q0L0wvU3BheGZHdFBOY2FuUUFPTi9kNTBiQjVRdDRQMmpUQ1lsZXNrM0pPaCttL0RBTTA2NXMxK3M5OCsvYVIzdlcrbkFabkhLY3k1d093MDFqVHBobGhDb1duWWVjS0J1RjRGaWJRNU5mbE9oVzJsUXZQQjlJMklKOEk2MVIzRVVJNzB4dzk3bW5lZXhWQ2ZRbDBBNnFvSFVOaXRXT0xKdWU0cmsxVEtJb1d2emNGVVgzeU80QitLRGtHSlR4OEQrNkVaMEhTL1JDbkhYNlYrVTdPcjU3OEJzQ09PZU1yU3lmWG9rVzU1V0FSODlXV2JFN0pyT0pVSmZGQ3dvWjRRNnlGbnpNTHdzR05GRDJ3dytQZ3ZKNm5ERFY5KzBtQlg4amZtYjJvZldwY0trZ1N0ZGY5eGJHK2lSd0xWL1laOXFoWDNFdHBTd056UTAvLzdDaHpQUnAzckFDQXZuZXRnZHU0bGozZkl4TFd3WVlqOU1LSGFJTGxXQ1UzcjZsR2lCaURmeVpkdEhlMENCVmgwdU5ZdXhveVdTcjNVSGRvempMMVEwdnAyTnQvN3ZZeWFQLzZlRXMyZVJLNFJNV0ZVUnNIejE4K3Z3ZTZ0WFc5TmRNWUxhMldzOGhRVU5ESlM1TTZPekV6SkoiLCJtYWMiOiJiZjdhOTQ0MjM3YjkwZTc0NDJjZDliMmRmMjkxMTBjMzMyZjk0YTIyOWI1OTdiMWYzYjY2MTAwMTM4NWM1YWE2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlY1N1NraElGYUFUbWdDY3VNQUs2b1E9PSIsInZhbHVlIjoiMFNqaW9YRnhaVmhseEowUUtKWFVwTmVZd09hZkxrak05T3NvZ1VsaWxFVFdNdlFpbEloKzJlVkNNb3hSR1ErYWVmRStCTXJ5TkVINmNLempZRy9nTGxrcGxCdlRRSjdraVFqL1NlYmZqZUtXRmE2UVpWMGhIU0JRYkRESy9HOTc1dEdEdThlRkVXcENwczhVeVRLUmh4ZjRzTDZNVmxZUnNTdGNmMUY5OVl6ZTIxOVVra0Z4VXpJTjhzVFhGaHBscm5yZHZhMG1ScWtCbS85MWJNQVRIYlRCcFg1aWlrS1E4a2tGczFOZ3JFb3U0ZmUxWmZUYmwvOTlHa1pCNVJZbXNyVDFLQWtRaExRSVFZejJWRVN6U2htbHIvRzZCUjI2RDhWT2VFK2dlRDZvUDQ1R0NNdk91MjlBMFFHWDRyMENYN0NVMnFIQ0I2ejV1dG5pcUZnYStNRWUzRjlRUTRkbE5nNDREN0hmcllCbDRoZ005aUgxejZCbDU3Q0FrdHBXTE5taXM1TWhnMi9RZjNsUmxDYWt6UnZlNnRMM1c1NGxVSlFRbUVXUm91OXBDbHNrS3BNWWNiRWJ4NFdpb1ZVOXpSQXR2alNRZE9VNlBjQVlBNnJFYUlUZzlqZmFBSTdKZVlmZGN5ZmdCVzFBdndUZ2xwcGxIUVpVQWVxY3Fod1giLCJtYWMiOiJkMGViMTYxZmYzNDY3YzIxMDg1MzMyOTEwZjhhMjQxNjk4N2RlZTY5N2UwYWM5MWM2YTMzY2UxY2UxOTc1ZTc4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im0xQlVLMi8wSktrdWIxOURsM2VzL2c9PSIsInZhbHVlIjoiUklzaWhhTW8rN1JjcG1TUEpsTUlDM0Q0L0wvU3BheGZHdFBOY2FuUUFPTi9kNTBiQjVRdDRQMmpUQ1lsZXNrM0pPaCttL0RBTTA2NXMxK3M5OCsvYVIzdlcrbkFabkhLY3k1d093MDFqVHBobGhDb1duWWVjS0J1RjRGaWJRNU5mbE9oVzJsUXZQQjlJMklKOEk2MVIzRVVJNzB4dzk3bW5lZXhWQ2ZRbDBBNnFvSFVOaXRXT0xKdWU0cmsxVEtJb1d2emNGVVgzeU80QitLRGtHSlR4OEQrNkVaMEhTL1JDbkhYNlYrVTdPcjU3OEJzQ09PZU1yU3lmWG9rVzU1V0FSODlXV2JFN0pyT0pVSmZGQ3dvWjRRNnlGbnpNTHdzR05GRDJ3dytQZ3ZKNm5ERFY5KzBtQlg4amZtYjJvZldwY0trZ1N0ZGY5eGJHK2lSd0xWL1laOXFoWDNFdHBTd056UTAvLzdDaHpQUnAzckFDQXZuZXRnZHU0bGozZkl4TFd3WVlqOU1LSGFJTGxXQ1UzcjZsR2lCaURmeVpkdEhlMENCVmgwdU5ZdXhveVdTcjNVSGRvempMMVEwdnAyTnQvN3ZZeWFQLzZlRXMyZVJLNFJNV0ZVUnNIejE4K3Z3ZTZ0WFc5TmRNWUxhMldzOGhRVU5ESlM1TTZPekV6SkoiLCJtYWMiOiJiZjdhOTQ0MjM3YjkwZTc0NDJjZDliMmRmMjkxMTBjMzMyZjk0YTIyOWI1OTdiMWYzYjY2MTAwMTM4NWM1YWE2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855995277\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}