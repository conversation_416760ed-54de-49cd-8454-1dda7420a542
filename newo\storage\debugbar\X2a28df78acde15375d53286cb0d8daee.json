{"__meta": {"id": "X2a28df78acde15375d53286cb0d8daee", "datetime": "2025-06-08 13:04:30", "utime": **********.084513, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387868.829351, "end": **********.08455, "duration": 1.2551989555358887, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1749387868.829351, "relative_start": 0, "end": 1749387869.934171, "relative_end": 1749387869.934171, "duration": 1.1048200130462646, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749387869.93419, "relative_start": 1.1048390865325928, "end": **********.084555, "relative_end": 5.0067901611328125e-06, "duration": 0.15036487579345703, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43910848, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02592, "accumulated_duration_str": "25.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0287368, "duration": 0.02462, "duration_str": "24.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.985}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0627449, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.985, "width_percent": 5.015}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1917131188 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1917131188\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2015294809 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015294809\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1955772796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1955772796\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-21121549 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhKM2RCWHMxUzdJQ3lFZkF4S2ViSFE9PSIsInZhbHVlIjoidXpTSmd3NGtRZmo3ck9BTUtCSUllYlZ3TDRxRm05NVE0aXlCTUZUWUhhSUMrUy91RndyS29UYXlySjVxcVJSY0NpRVJZVDJhLzVwN3YyMEppb2NsTlk3b1ltQ3N5aldUNFpxRmlWL3pZa1V1eHAvYkRFcGNHeWNQUmhDNCtudUx6Vjl5QThEOUNoK00ySjloUXlObzNKZi9oWDFqZWpIeVQ4dE9CR1cveW9jcUtKWE42T0NhQXUyVmhaR1RCRTZtRmd4WVNwL1hVc3BGRDF1NjZ1dGZQQ1V0YjZjN0hGdXloV3ZlNXJCZ3Z6UVpMY1BFbWxOZFJuakUrbmZKZ21aZ094cVAwZVFqU05GR28wRkZzN1VPV0VhNU1ZeTVmVk9SZmxmWm5WWFVRdUVqU1QrK0ZMQ0cvVjUyZ3oyM3lSaXBwUUdHZmlBK2xvUzVrckRQZExYVThQaTdiejh2RVBadGVIS0JwTktCaUVSaGNneEFUT1RnMWRCWno3cG8zaEp4Sm9RSTlzOUNiSUlXMDV6YU9lZlVwSFZiZENodDdWVmJKNDRDQ2l1Vm1XRXh3aFFYbExTK01DdTNvVzcxcGxzTG9DKy9VbWpXUUtkVTJLeGhzVzEyVHZUS2NQT09jajFQYWkwaHdMZkxteThnOUxqNjU4d0NZdy9XSWRMK21IYTQiLCJtYWMiOiJlZDZiOWExZjAyZDZjZTNlZmRmNWE2MDFlZGM3ZjU5YmFhZGM4YmQzZDM0NWY1YjE2ZTU2ODE0MjRiODljNmY5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJDQmlrWVVnRHpJcmpvZjVYbjZ2T3c9PSIsInZhbHVlIjoiTER0V3o4MTNvajh1NWVUSmMzRzFtRld0MFBlK0ZTdkFhanlwL1VGTkVicW1jSy9OU2Vpc3JVQzJicGhjY0x3UEFVMmNMSVpxNEE5MkUrQUtvL3hlZUhtRzZtaG5IVEZCaDZSRFJiTGYyZWp0T1BKQldvSHdRdU1yUXFNN0xqazUySnZMZ08xQ1RvbEVsekNveDBYOWlVYi84Z21BMGZlSW1ubXcrbEd4L1pUZCs4TGpsLzRGSXRxemhKSUM0am5pNU5SOU80S0VOZjZBNWFFUDY5eEhIaHVtRmtQL2NpTkV3VGdwdzJmNUJhUk5Qa1VueHdSRytWUUx5OW5PWUQxcCtSUTF2OTBiYTlIOWFGVmJBU0RQQ2VHU3ZVM0hwZHlqWHNUZVZ5NUdpVlBKSHdyMG53b2E2NDd3U1RzbEsxcXdhNysyUWszb1EreUEwd0lLZG5TQ2NqeDY1djVWaGhucWppWWRRUm5oSTIxWndJZlpDb3lVL1JzVGxESXo1OVZEb1Q3eS9yU1IwUy9IT3hTTGdMRlRBcnJXV0RkRG5XU1BXdkRVQ2g3TkNFbzVINlZDU2pONmIvdHlmSE1WOE0rYUtFenJlSGhtYnBGQlRycXNWQ3ZFSGJiU1dKcFY4eDFhR2lGTklnSm1lL21LZGM0bEJpV3NPQ0ZtTUtBdFlRWFoiLCJtYWMiOiI4YTE0ZWJlOGIyODU5OWVlZmMzODY5MWM2OTVjM2ZmOWRjZDgwZTY3ZjM0NjA0MzhiMjZhNTcxYTEyYjg3M2YxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21121549\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1369375007 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369375007\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-378623537 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:04:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVISFBqYkV3dzZUSU5QUll3MlVkcHc9PSIsInZhbHVlIjoiUDVBdXFvTExJblZLS2lzMGM2QU9rRnVRS1dCVnhtTmZIVTBGVjRhaVI2M3A4S1dka3FBbEYrMWlDQStnNExCODd0VEgwS0pjbmh6akYraCt5WkJzZlM5ZmtKV0syamRoeVhaV2ozQ00vSEN0TGl2bzQ4WWhHVUNWcG9lcXJjcW5VUXpyWUVEWlQxaWd5Ylc0dGZoWGk4UFBFQUJFZ1EvbVFlV3BQT2c3UTJiakxtVThlcFpkQzg0dkpmWGtaNnNzUlhDdEZ0cEpDY2hCd3VpR01uek5rV1c4Z0t5QXd6Si9TTm94WU1xVUtzNk9LcWtSa3RPMkowT2tiRjdmS3FXYVhOeGsvSUNERXFQaXRseVNoeTEwRXI0QkpCSG5CekJBc2xhZDhHRkJuSHVjM0VibWpIVUdjWE5rbVJNOGp4eEdnTkNZQVdvWTNtQlNKdGR3NWt3TFVadXRDQW5CTzVRK05LV29DMFFyU010eVVWUmZaK1dLc0RIVFBpWk9yblIrMmt5YVpPdzF1NWs4em1TKzBqQ0tBTEFBKzBrM2RtbDVTcmFHZkRmNlROcllpeGVTcGU5WERMenh1cmJpaC9pODdFM0RjK3p0Sjk3ajIrc2oxUjhnVk5vUzV5NmN4NFhhUlZ0THlDZlBwYnByaFlmR2JRL1BJRmJVTHIwMGNpTmkiLCJtYWMiOiI4MmExYzA1MDYwMDE5MGJiZGQ4Yzc5NmY4MDI5Y2YwNGZkZDZhNjUyMGUwODQyYWRhYmVjZmJlZGIyMzAzMzk3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImF3LzJMWWxmWXBDRldOdlBSWExtUGc9PSIsInZhbHVlIjoidEh6anZCZVpzRFNHTTNzYW1TYmxKQVNnc3JnenljZXAzSDlIYWRSRDFVME45QU0zTGpFTllrWlh0V2tWaTQ4ejh4YTRFN29PT2s5VDN3WDV3YjIwakpqVXJSV2pRRXRicS91dmJITnBmNitDVnJ3OWhNcnMwdkxCMlhUK2tIbWc0ZUxQL1Z1cTIzUURnUkdYVG5hbmNxREYybm14ZUx0dkxoZ2lHemhOTUNSVW5UQU9PWE4rNDNsWFlvRTZRNlJqVWRPdmw2OGNZbTNXNCtRZ0RuWDR5T3MzMTdOUVRJVHdQWEw2b1RIczdGckI1dGM0bDdIdWc3aFh5ZHU2bXBvZk1ZcnBhSEhRQVdxTDhxSFd5MDZRZktLOHE4Skplc2xsYVd6NHhQTVk2OHVYa2xMWS9KTy9KQ2hpVHRoVE5vMm9wY1UzZGhPSXFsQ0JMV2VYWjcrZ1hHVGdUZGU3bGg2cEdIYW5FSkhRc0NUZU9WdUZRRGRoNVpmYlRNUysrRVE3YjdYS2FTc25JUlR3c3YwYmlUY1dWdkZZaFllTHBEcUFBeVFhYWVwa3JxT1VBZ2g4MDZEaG90U1Z3NnNkYVdMTXp2dkw3aXJjTDZCaElKZGtjeDlPRkRuSmVNcmNqcFJHbFp2TVB5NTZwbWpocFdCeEFBNUZidzFxa29HRytHdXUiLCJtYWMiOiIwOGVmOTQ0MWY3NjFhNTYxMTQwZTc1YWQzZWU5MDhkZTY3MjBmMjViYTdjNDkwYzY1MDY4ODg5MWQ1YTAwMWY1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVISFBqYkV3dzZUSU5QUll3MlVkcHc9PSIsInZhbHVlIjoiUDVBdXFvTExJblZLS2lzMGM2QU9rRnVRS1dCVnhtTmZIVTBGVjRhaVI2M3A4S1dka3FBbEYrMWlDQStnNExCODd0VEgwS0pjbmh6akYraCt5WkJzZlM5ZmtKV0syamRoeVhaV2ozQ00vSEN0TGl2bzQ4WWhHVUNWcG9lcXJjcW5VUXpyWUVEWlQxaWd5Ylc0dGZoWGk4UFBFQUJFZ1EvbVFlV3BQT2c3UTJiakxtVThlcFpkQzg0dkpmWGtaNnNzUlhDdEZ0cEpDY2hCd3VpR01uek5rV1c4Z0t5QXd6Si9TTm94WU1xVUtzNk9LcWtSa3RPMkowT2tiRjdmS3FXYVhOeGsvSUNERXFQaXRseVNoeTEwRXI0QkpCSG5CekJBc2xhZDhHRkJuSHVjM0VibWpIVUdjWE5rbVJNOGp4eEdnTkNZQVdvWTNtQlNKdGR3NWt3TFVadXRDQW5CTzVRK05LV29DMFFyU010eVVWUmZaK1dLc0RIVFBpWk9yblIrMmt5YVpPdzF1NWs4em1TKzBqQ0tBTEFBKzBrM2RtbDVTcmFHZkRmNlROcllpeGVTcGU5WERMenh1cmJpaC9pODdFM0RjK3p0Sjk3ajIrc2oxUjhnVk5vUzV5NmN4NFhhUlZ0THlDZlBwYnByaFlmR2JRL1BJRmJVTHIwMGNpTmkiLCJtYWMiOiI4MmExYzA1MDYwMDE5MGJiZGQ4Yzc5NmY4MDI5Y2YwNGZkZDZhNjUyMGUwODQyYWRhYmVjZmJlZGIyMzAzMzk3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImF3LzJMWWxmWXBDRldOdlBSWExtUGc9PSIsInZhbHVlIjoidEh6anZCZVpzRFNHTTNzYW1TYmxKQVNnc3JnenljZXAzSDlIYWRSRDFVME45QU0zTGpFTllrWlh0V2tWaTQ4ejh4YTRFN29PT2s5VDN3WDV3YjIwakpqVXJSV2pRRXRicS91dmJITnBmNitDVnJ3OWhNcnMwdkxCMlhUK2tIbWc0ZUxQL1Z1cTIzUURnUkdYVG5hbmNxREYybm14ZUx0dkxoZ2lHemhOTUNSVW5UQU9PWE4rNDNsWFlvRTZRNlJqVWRPdmw2OGNZbTNXNCtRZ0RuWDR5T3MzMTdOUVRJVHdQWEw2b1RIczdGckI1dGM0bDdIdWc3aFh5ZHU2bXBvZk1ZcnBhSEhRQVdxTDhxSFd5MDZRZktLOHE4Skplc2xsYVd6NHhQTVk2OHVYa2xMWS9KTy9KQ2hpVHRoVE5vMm9wY1UzZGhPSXFsQ0JMV2VYWjcrZ1hHVGdUZGU3bGg2cEdIYW5FSkhRc0NUZU9WdUZRRGRoNVpmYlRNUysrRVE3YjdYS2FTc25JUlR3c3YwYmlUY1dWdkZZaFllTHBEcUFBeVFhYWVwa3JxT1VBZ2g4MDZEaG90U1Z3NnNkYVdMTXp2dkw3aXJjTDZCaElKZGtjeDlPRkRuSmVNcmNqcFJHbFp2TVB5NTZwbWpocFdCeEFBNUZidzFxa29HRytHdXUiLCJtYWMiOiIwOGVmOTQ0MWY3NjFhNTYxMTQwZTc1YWQzZWU5MDhkZTY3MjBmMjViYTdjNDkwYzY1MDY4ODg5MWQ1YTAwMWY1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378623537\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-948042293 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948042293\", {\"maxDepth\":0})</script>\n"}}