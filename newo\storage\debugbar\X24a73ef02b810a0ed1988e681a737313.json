{"__meta": {"id": "X24a73ef02b810a0ed1988e681a737313", "datetime": "2025-06-08 13:32:57", "utime": **********.492463, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389576.093359, "end": **********.492494, "duration": 1.3991351127624512, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749389576.093359, "relative_start": 0, "end": **********.285731, "relative_end": **********.285731, "duration": 1.1923720836639404, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.285755, "relative_start": 1.1923959255218506, "end": **********.492498, "relative_end": 3.814697265625e-06, "duration": 0.2067430019378662, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45587448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006540000000000001, "accumulated_duration_str": "6.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4168599, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.584}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4472191, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.584, "width_percent": 15.291}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.464069, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.875, "width_percent": 17.125}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2116892382 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2116892382\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-265333949 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFlcG5laDBnYlVZRlhVU0w0TS82Mnc9PSIsInZhbHVlIjoiRVlCMGZwcGIwQll5MzhqR3BZN1hkVWMvS3V1TEZpRkkzUmZrZmJPVzM1VWdrNHI2YW83ampkR3R2NHFUdjd4TlJ0d1ZUYXlLZDZiZ0duOHFmS0l1eXVXT0RTVlRXaWdWVDlQZ2l5aHdkOXRtQlFLbzVUNmN0Qnp2ZlJVUXo1dkhZZUtxcCtVeTJ0N283SXBhSTlXcXRvTjVOaUdvYWhlQlkvRUcwWmtTQ3N0a0FkOVUrR0NrbVp6OTVObUVQNVFHQkhyQytaYW81RXFERVFQZzRwcm1YMTNyNi9vazduTTZLK2xSUHUyM0YxdUM5V2NOWDhwVVNZY1NnbHdBTksyWi8wdnBNalp0a0RXamlTMnBBRjZjTTh1UTZ4NWJ2cmozS1crMW4xblN5L3dLQ3JEa3JOZGhZdzU3K3AyYzlWaVRzSmNWT3hJTVNYR1ZpSHlGODdNYmRUTjErWElUYzl4SHNGNjNkOHhzd1FxMDhteXVrS1VibmQwME5wWklzckZGb2tROGRxQTcrLzJKY0tTaTFYTmloYXYrZUxBWVNVR2NSemVNaFBQN3lQd2tMM1kwYVhOdktOVnZ2d3pTaHAxYVYrQUV5SytsdTVWZGdCUzJHbDg1MXprRE9YM2wzRHlmRmdCWmVqOTkyNjdEOXVvZExTUnZ5TkZybEdIRmVEWTciLCJtYWMiOiJmODA5OTZlNDBmNTAyOWUxNTczMGM0M2IxOTczOGI1YzdmZDg4YjAyNGNjYjg2MWUxZGFmOTg1OGQ2ZDFhYzM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikl6NmdidktNSkRwbTJlYnBDTFdBRmc9PSIsInZhbHVlIjoiZXBUTmc4UUFvdlBPMHNBYXZBRm1KK1BCc1VkbTlaYklRbzh0TXpwbm9JcXFORUhUUlBiL1YrQ3RHdnlNZmdOcjY0MUVYaWZzdVQ0K1QweFo3dkkyUFc3T3RlRDduZjNBRmw3cjlhLzNwbUY1U0lNUGpxTnF4T2JrYVJFbVlaRFZzSlZiaFo2cWlvZlArdnVvMnVJS3dpR1hWMzFBK1FtK25mVGJucXJ3VVRDOG11YVI0WEE3SjBaaFBJdVB5WEhXd2FDa2l0MksvemRrMTZOSm9LVUlsL3lKME1ieDVDZ0F5SllhOEhqQVduUitYd0ZPekRHNjlxTzJndkhmcjVGdUNoS01SUFZYd1BVV2phWHoyZmlNV1hERC92ekR0TmFYVUtzQk9zaTdsL3VFNklCNXdzaWgxRWNuTWNYZXV1SVpKeUF4NFRscnFFN0pLTkZBakpMTnFDQTJVQjFHOU1zWGFSbjZIK2syT2IrTkx0N1FSOHhoVVUvYWYza3hwRkN0NUZSemZab0NpOS94SEFaaUxKSkNpTXVqdjhHdUxSVDkwa2dzZU04MmR3bGZUOTJJM0tEMk0zV2dwVENYVW5CZ0ZnZGpJTGdSeXJBb0VvY2dZTXJ3TmVJZ3BMVnQ4NkJ0andZZVU4a3pYNGhrL2lwWFFZMWJEelV0Rkk2YUlyRlEiLCJtYWMiOiI5MGVmMDA3MmZjN2UwZjg5MjQ1Yzc4MmY4NjA3ZmRmNWUzNmQ4MmU0Y2VhZjJlNzVmNWI4OWM0MWM5ZGY0NDRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265333949\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1665918328 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665918328\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:32:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inc0cjlMeUlHUFJ2b2h6R254eVRtN1E9PSIsInZhbHVlIjoiQVRHejZnL3Y4SittdTRSYkZIQXVqMkgxRjZVaWp2b0tNTDFaY2dIRC8xYzZDZHFEVlBxSHF4TTRLa3F4d2hRcGE1OHhEYkVPRFQzU1NMdVRkTGVQRk1KZmtndmRkR3pva1NFZ1A5YkNiMGdNakhZZWNhT0JvaDhOakFzN0puU2ZCVzJIbzFLcGsxZkFIVUp2YW1SRWxsblNzV09oYUdoY3E4VWY3cDljOGtYWHY0Mm9TWFZoSWRCRlZnU3pPOFRvM1N2UmJ1dGZycHpyWmxMaGttNTlzT1lGVDY0dFdPeXQyVDVqZjdZTFo4VkZBRVNoMXAva3BjTnhvY1E5YjcwR24ydzZydzZaWUY3THNtdGU4NUdnYnh6cGdxTmRtZjFlQ1RlMHMzUHFQek15UHp4N0UyNlJlV2N0OWN5UWR4azRCak1heWZ1TFZJdjBRWVBWbm50aHBJNllFNEIzOGdFSm1Tc3dkMkw2SjRiV2RiVFo2bEdqeUFxMDREWHAySElxVEUxZmlhdUxkaW9hVWRDTy9JTTJSZWJMYnhBRjJ4dkp4MkhBRktPOGlFcnRrWnVhcGxXQTZFN1lVUkVhVjlCWnV4L0dGNmp6cXlydUcrbzgzNFViWElBK1R4Z2NlMHE5b24xTERVVkRXNkpDTmpFS1hnb25RNTdpMi9UNmc2MzMiLCJtYWMiOiI2ZTIwNDMxMmVmMDJlZWI3NjY2YzMwOTZlNDk4NzVhNjJkOTgyNDQxZDQ2Y2VmNjExOTE0ZmMzMjIwMzY0ZDVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZoako2Q0p6WmFzMVpVQnhmMTdOTkE9PSIsInZhbHVlIjoibW5YVUFXMVg5WVZDRFU3ZkJib1JHVGFIQlV5aFNkZVFIWmNNK2h2RVNiTzJOejNrdUMwZ2FUSmt5UGM1MlpoVUcvM0xCR1lqYlNMQ3ZnVEVmMWhMSDN1UGZoOWJ4U1Mxb2lWc1NMZm1nV2ZFRDNUMEJZdTFZemljK29xSkxETDF4cUhCdzI3S3BuY1FtMmdlcVVVY0J3TWpmRFpNbzc0dmpoRXF3c1pBSU10VXU5MUxIT2JmR3Ywc3h1bHhEY1ZWZGhJcEw1eTVVUDdKa3FCdWVzZXFINWtNM1Rnck1HRklBdkcrclBnaTd1eXJDcFlWYVozVno1Y2JVUTd2dkZDVHJNV0lIQUs4RGlPUVRJN2ZBalVJemNFZnI3RHNBK1NCWCtsdkovMW5kMmxhZlFpS0E4R1Y5TUJTT05OejFmY0tDdlBFcTBINFJJelFUWUdaQ3ZWSkdObEZ6amdvTWJVZWg2dlJhOWpYUzNETmhjMDR1SjhMWEhUVy9pUGN2NjQzN25CWjlvMTNJWU9Sd3VMMFN1alhlNWtHMXFSR2tOcmNDTGZ1NngyUWcwcTJaa1Q4WVc5V1FLN0NuQW1GeXZxamJoNWs1QnQ5MHRFRXhEdlZsc1BoRFZDYVpIdEFWUEVsNjBIbFdBcS9FME5oc3VEY0ZGSUVWeWs5UE9QL3dNSXAiLCJtYWMiOiJmZjY5N2FlNWFhNTI4MjIwZWZiNTcyZmEyYTI1OTYyZThmOTg1YmQxMDJiZDBjZjJiYzNkYjJiNWU0YjgwNmU5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inc0cjlMeUlHUFJ2b2h6R254eVRtN1E9PSIsInZhbHVlIjoiQVRHejZnL3Y4SittdTRSYkZIQXVqMkgxRjZVaWp2b0tNTDFaY2dIRC8xYzZDZHFEVlBxSHF4TTRLa3F4d2hRcGE1OHhEYkVPRFQzU1NMdVRkTGVQRk1KZmtndmRkR3pva1NFZ1A5YkNiMGdNakhZZWNhT0JvaDhOakFzN0puU2ZCVzJIbzFLcGsxZkFIVUp2YW1SRWxsblNzV09oYUdoY3E4VWY3cDljOGtYWHY0Mm9TWFZoSWRCRlZnU3pPOFRvM1N2UmJ1dGZycHpyWmxMaGttNTlzT1lGVDY0dFdPeXQyVDVqZjdZTFo4VkZBRVNoMXAva3BjTnhvY1E5YjcwR24ydzZydzZaWUY3THNtdGU4NUdnYnh6cGdxTmRtZjFlQ1RlMHMzUHFQek15UHp4N0UyNlJlV2N0OWN5UWR4azRCak1heWZ1TFZJdjBRWVBWbm50aHBJNllFNEIzOGdFSm1Tc3dkMkw2SjRiV2RiVFo2bEdqeUFxMDREWHAySElxVEUxZmlhdUxkaW9hVWRDTy9JTTJSZWJMYnhBRjJ4dkp4MkhBRktPOGlFcnRrWnVhcGxXQTZFN1lVUkVhVjlCWnV4L0dGNmp6cXlydUcrbzgzNFViWElBK1R4Z2NlMHE5b24xTERVVkRXNkpDTmpFS1hnb25RNTdpMi9UNmc2MzMiLCJtYWMiOiI2ZTIwNDMxMmVmMDJlZWI3NjY2YzMwOTZlNDk4NzVhNjJkOTgyNDQxZDQ2Y2VmNjExOTE0ZmMzMjIwMzY0ZDVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZoako2Q0p6WmFzMVpVQnhmMTdOTkE9PSIsInZhbHVlIjoibW5YVUFXMVg5WVZDRFU3ZkJib1JHVGFIQlV5aFNkZVFIWmNNK2h2RVNiTzJOejNrdUMwZ2FUSmt5UGM1MlpoVUcvM0xCR1lqYlNMQ3ZnVEVmMWhMSDN1UGZoOWJ4U1Mxb2lWc1NMZm1nV2ZFRDNUMEJZdTFZemljK29xSkxETDF4cUhCdzI3S3BuY1FtMmdlcVVVY0J3TWpmRFpNbzc0dmpoRXF3c1pBSU10VXU5MUxIT2JmR3Ywc3h1bHhEY1ZWZGhJcEw1eTVVUDdKa3FCdWVzZXFINWtNM1Rnck1HRklBdkcrclBnaTd1eXJDcFlWYVozVno1Y2JVUTd2dkZDVHJNV0lIQUs4RGlPUVRJN2ZBalVJemNFZnI3RHNBK1NCWCtsdkovMW5kMmxhZlFpS0E4R1Y5TUJTT05OejFmY0tDdlBFcTBINFJJelFUWUdaQ3ZWSkdObEZ6amdvTWJVZWg2dlJhOWpYUzNETmhjMDR1SjhMWEhUVy9pUGN2NjQzN25CWjlvMTNJWU9Sd3VMMFN1alhlNWtHMXFSR2tOcmNDTGZ1NngyUWcwcTJaa1Q4WVc5V1FLN0NuQW1GeXZxamJoNWs1QnQ5MHRFRXhEdlZsc1BoRFZDYVpIdEFWUEVsNjBIbFdBcS9FME5oc3VEY0ZGSUVWeWs5UE9QL3dNSXAiLCJtYWMiOiJmZjY5N2FlNWFhNTI4MjIwZWZiNTcyZmEyYTI1OTYyZThmOTg1YmQxMDJiZDBjZjJiYzNkYjJiNWU0YjgwNmU5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}