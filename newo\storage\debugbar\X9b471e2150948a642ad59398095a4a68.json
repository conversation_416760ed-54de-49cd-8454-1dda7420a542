{"__meta": {"id": "X9b471e2150948a642ad59398095a4a68", "datetime": "2025-06-08 13:05:40", "utime": **********.611843, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387939.286081, "end": **********.611877, "duration": 1.3257958889007568, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749387939.286081, "relative_start": 0, "end": **********.470396, "relative_end": **********.470396, "duration": 1.1843149662017822, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.470423, "relative_start": 1.1843419075012207, "end": **********.61188, "relative_end": 3.0994415283203125e-06, "duration": 0.14145708084106445, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43910592, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0055, "accumulated_duration_str": "5.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.570982, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.727}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.588052, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 78.727, "width_percent": 21.273}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1718613598 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1718613598\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-679110268 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679110268\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-891380934 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891380934\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1815925061 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVxNlBhUkJoQ2pwaTZOVmNoa3l1MlE9PSIsInZhbHVlIjoiOFVIYzFTb3hJTzFWVm8yNjN4L0xvTkhDWHJLL25NWmoxRS9iRXo3WnMzVXg3T1ZlZlhiL2ZoNU9QdTVxZEpXMjhidkZPNm16WVJpcCtON1hBWktoOWZZWWNuUGJpUUc2UnlhVGwrMVJsRHA0TmtCTm5aMVVMNUpJOURiUXNWcDYxamlHWUhFQk94Rk9DcjZkdy85L09QYk1VYnVyaXBtV0dKYzVzd3pBUHJ3c1VuQTZhMmw0YUVxd3VMa0xYQkhCNE9BV2xtNGdUK1hsRlRmTTN0V3pPOFRkeHU2NnhCdUQrMVFqbGpRYjkrWllmZ2RVKzhIY3hNcXdWcHljbUtmWFVsbzFDczk5Ky9tSWFXNWhGODh0RnJ1Qml4dldGSnU4QU9UMFBJVFgxVXVCa2JwZkpEcWo2ZTdHbEV6aGJMZGhpQWtQR00xUm9vQjdXVlp5T0xuTVFoOU1Gb2xreDdCYTJEcHlPaFgwWmJZWHRSNnBTRlBGVDNlY2VQN1phNGdZNnZQbktiZVN0MXhmNG1TSWlFMHhySVk4czQ2Vnc0dTZnNlFnZGViNHA2NVlURVpOZ3FFZ3IyMHRONk4yc1hMckg1ZXZ4UlhIKzVoK0VKQUVFYTNoT1F0dU1NWTVXZDZVQ2VlZ29uaVJ2TWM2Q2JHOG9EN3FINTkzZkowMkNKWGciLCJtYWMiOiJhZDY0MjM2NTgxODM0YWIwNGJiNGM2ZTk2NDk5NWZiZWY5MjljOGQzNzIzNGM0MDE3NTdjMmI4MzE2YmFlMDNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBYRlZCTG12VHpYa3FHeXdkanNrU2c9PSIsInZhbHVlIjoiY1NCOWlsc0hrbE9YNmxUYkNmcE9GazIvQUpPQ0tXMXdQZnZ0ekJNOHJuMjF1bHNzdGdnS0V5VUtyOW96N2lqOWRJcm5iQWdZNUt3UFJKY1F4YU5oVW8xUHNkZVhKWGx5VGlRT05kbGNMWURvSFpQM3pJSFFudnd4U01uU0ZGaEZoZUV3N3UvQzMrdHFDY3lLc3FNc0U4NnU3Skc0ZVBpZ1pzZ2lNVGFzekM1SlFneE1mTEtTRFV2ektCcU9qYlpLZTVNQy9DMGlSY0xyTThySTloZC8zaFFWUmk1SE5UV1RpdS82ckFobDczT1BDSkNRTHRmK3l1WjdGYjNmaXRXRUc2Tkl4Nk5qOXFzRjFRSWx0VXBrc1h4Ym1LK3dndnhXNFNBamNsTjN4MGpOQ0RRdk5FMUNUQXB2NGtpOVFQNGlBVUxONzRBR2d3TlRCTDZmSjE0TTNOWTEwUVY0NWpraEhabzZndFJERHZVK3RPY2dHWHl6Zjlab3lNQW44U2l4TWNKdUpDVWtWUGZlbWJIdUhYNEZkTWJCSDByaFVaY1NveEJXS2RqM291Y3hmby9iMDBFUFdpbTZZUXo0a0l6UEFlem1MTVVBdzFWNnZuMFMwTFUwV3UvM1lPTC93cXkyaG1pMlR0Y3FyYjgxV25TM1lNNUpwU1V1S3BqbG16THIiLCJtYWMiOiJhZmE2Mjc5MTlhZjQzNWU1MTY2ZDNjODQwZDFiZjlkOGFmYWIzYzYyZGY0OTFkYjZiZTk5MjM5NGI5NmYyODUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1815925061\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-469290787 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-469290787\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1215136136 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklpV0s4TUQxbnVIUm9mN0VqVFRFNkE9PSIsInZhbHVlIjoiY1FaU05udnNkaVNnT3d4bGlQbGQ5Z2hxU0twdldpaVh3eFVJdFJnMlVpYktZY2hjRFBWdmdnQ3VNb3FFLzZjUlFCYS9rNjh6emxRbEhFTStMc0dtN0t5TndWdXVuYWphUUdtVC9ZRS81dlNQWFU2cXd0alpwbGlSVXFRZTdORzBBMUVYYVJNSnlpMTluMDJuSTJZc3YxV1VDOGI0K0Y0RGoycVNmTjBEVW82Q1VKdnAvVzh5WDVEUVdUUDl4eDJJZmVuZTJrd1QzdzlSUGVrV2FJNUdYWTgvdW5kSkk1bk9vN2lHd3RnbENBZnd6ZmJUSkIxdTRFeStJV042aUpjYkdGY1F1TlVVOU9mcjhLaVR3T1djVjFJQ0NrQWVuMjR2MU44YTk5Ni91ckNaYkVhQWtRSHU4MTlQYUw1WXBIT0t5bTFUeHJ3TTVRNjBzYnd0TFRYVDNvcVJ0bU9acTdpbzBQSGZ3a1I4TmtKOTY4VU1JN1JXYUg2ZmgyaFA3M3JjUlp6U3ZTSTZEbVhpWWxMcXFMMGk1aWlGdGFFNTVXdWYyVHFzVllFOHU3c2JhczlaSFdXaTlNMlpkU1JWRG4va05sb0o2M2xRVW9iZVVmeHFWanFYU0NnZ29hWnhrNFcwR0hmQzNBOGswRmJiU1pIeFp5REZxMDUwWkhkRTkxWGIiLCJtYWMiOiIyZjU1NTEzZGM1YWRiMTMxYzc2MGEwNjFiMWJjZTEwZDYyYzY3MTJhZTBkZDA3NTQ2NTAyYjc3MGE1YjFmNTYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklHOUZUSnJWVHFJc0M5bmVuc2V0bXc9PSIsInZhbHVlIjoib3RWdmFML0piYXcwRTBGeWQ5ZjRvcVp6bHNOb3FRZC9FeDBGRzdkWjhHdGF0Vi83WVZUZHRtcW8wTEtpR0FiVU9WODZ2WDltMkNoR1kydXdkMy94Q1JuZUR5bkRwelZDQjFlazRtZ1MyY0h4bFJxZVplak5SWWVRSXh5WFpSczRoZHpHN0pEL1hCQXM0RWJTUkhrV0h2djF4VVVpU3BZUUwxTjFxTzNxYXJoK0JpQzR3UVJKYTMvbHJSd2hycktkbTdnQ2tobVFwdmdBWkh4VG5DMGt1b2ZqR0tFbnJ5NHlKbUplSmJQZWllMFk4cGlLVERKZU9pbVhqY291QlRnNnNGTmsyZk9PdDB2Y3dnaTkzQ21yaUhhdC9VeVQyanBHRDAvYVIzVEJ2RGZTdEZMNHdIcWh3dTN2eEpHQ2pRVVZIZkl5L1EwYVNqMXp3dkhFaURjN3RVeGJNS1hvVUcyeGJtWW0yTXFmTHJ3L1N0V1hKV2N6MVFoMkVqTHo4MWdmZE1iVHRETlNPMUUwVmpmOFdDcjVrVHJML2xEcmNuTWNxa0hHQW5wR1RmTCs1VjVJK0hqbXNJRE9QcFg3am5NNVRmbkJabmMrNmpZaGJlQkdGNFEzYVhSTTJKcVhWc1FIU1d5WGEwUGtIU1dYNmcwWGhteTVIamNzZWpwNjd1dHUiLCJtYWMiOiI2ZmYwYWRkNDUxYTI2M2E5YTU0YzkyMTAyYzZmOTc4NTU1NGJjMTE1ODM4MDgzNGM3ZTUyOGE2OGYzMWI0NmQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklpV0s4TUQxbnVIUm9mN0VqVFRFNkE9PSIsInZhbHVlIjoiY1FaU05udnNkaVNnT3d4bGlQbGQ5Z2hxU0twdldpaVh3eFVJdFJnMlVpYktZY2hjRFBWdmdnQ3VNb3FFLzZjUlFCYS9rNjh6emxRbEhFTStMc0dtN0t5TndWdXVuYWphUUdtVC9ZRS81dlNQWFU2cXd0alpwbGlSVXFRZTdORzBBMUVYYVJNSnlpMTluMDJuSTJZc3YxV1VDOGI0K0Y0RGoycVNmTjBEVW82Q1VKdnAvVzh5WDVEUVdUUDl4eDJJZmVuZTJrd1QzdzlSUGVrV2FJNUdYWTgvdW5kSkk1bk9vN2lHd3RnbENBZnd6ZmJUSkIxdTRFeStJV042aUpjYkdGY1F1TlVVOU9mcjhLaVR3T1djVjFJQ0NrQWVuMjR2MU44YTk5Ni91ckNaYkVhQWtRSHU4MTlQYUw1WXBIT0t5bTFUeHJ3TTVRNjBzYnd0TFRYVDNvcVJ0bU9acTdpbzBQSGZ3a1I4TmtKOTY4VU1JN1JXYUg2ZmgyaFA3M3JjUlp6U3ZTSTZEbVhpWWxMcXFMMGk1aWlGdGFFNTVXdWYyVHFzVllFOHU3c2JhczlaSFdXaTlNMlpkU1JWRG4va05sb0o2M2xRVW9iZVVmeHFWanFYU0NnZ29hWnhrNFcwR0hmQzNBOGswRmJiU1pIeFp5REZxMDUwWkhkRTkxWGIiLCJtYWMiOiIyZjU1NTEzZGM1YWRiMTMxYzc2MGEwNjFiMWJjZTEwZDYyYzY3MTJhZTBkZDA3NTQ2NTAyYjc3MGE1YjFmNTYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklHOUZUSnJWVHFJc0M5bmVuc2V0bXc9PSIsInZhbHVlIjoib3RWdmFML0piYXcwRTBGeWQ5ZjRvcVp6bHNOb3FRZC9FeDBGRzdkWjhHdGF0Vi83WVZUZHRtcW8wTEtpR0FiVU9WODZ2WDltMkNoR1kydXdkMy94Q1JuZUR5bkRwelZDQjFlazRtZ1MyY0h4bFJxZVplak5SWWVRSXh5WFpSczRoZHpHN0pEL1hCQXM0RWJTUkhrV0h2djF4VVVpU3BZUUwxTjFxTzNxYXJoK0JpQzR3UVJKYTMvbHJSd2hycktkbTdnQ2tobVFwdmdBWkh4VG5DMGt1b2ZqR0tFbnJ5NHlKbUplSmJQZWllMFk4cGlLVERKZU9pbVhqY291QlRnNnNGTmsyZk9PdDB2Y3dnaTkzQ21yaUhhdC9VeVQyanBHRDAvYVIzVEJ2RGZTdEZMNHdIcWh3dTN2eEpHQ2pRVVZIZkl5L1EwYVNqMXp3dkhFaURjN3RVeGJNS1hvVUcyeGJtWW0yTXFmTHJ3L1N0V1hKV2N6MVFoMkVqTHo4MWdmZE1iVHRETlNPMUUwVmpmOFdDcjVrVHJML2xEcmNuTWNxa0hHQW5wR1RmTCs1VjVJK0hqbXNJRE9QcFg3am5NNVRmbkJabmMrNmpZaGJlQkdGNFEzYVhSTTJKcVhWc1FIU1d5WGEwUGtIU1dYNmcwWGhteTVIamNzZWpwNjd1dHUiLCJtYWMiOiI2ZmYwYWRkNDUxYTI2M2E5YTU0YzkyMTAyYzZmOTc4NTU1NGJjMTE1ODM4MDgzNGM3ZTUyOGE2OGYzMWI0NmQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215136136\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1461572425 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461572425\", {\"maxDepth\":0})</script>\n"}}