{"__meta": {"id": "Xd545a89fceb9a7789e92a5587353b4c3", "datetime": "2025-06-08 15:09:35", "utime": **********.391333, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749395374.745706, "end": **********.391353, "duration": 0.6456468105316162, "duration_str": "646ms", "measures": [{"label": "Booting", "start": 1749395374.745706, "relative_start": 0, "end": **********.255353, "relative_end": **********.255353, "duration": 0.5096468925476074, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.255366, "relative_start": 0.509660005569458, "end": **********.391355, "relative_end": 2.1457672119140625e-06, "duration": 0.13598895072937012, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131096, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02878, "accumulated_duration_str": "28.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.306072, "duration": 0.02393, "duration_str": "23.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.148}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.341809, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.148, "width_percent": 2.536}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.363851, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 85.685, "width_percent": 2.884}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3674219, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.568, "width_percent": 2.745}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3744562, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 91.313, "width_percent": 5.281}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.38013, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.595, "width_percent": 3.405}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-885799962 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885799962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.373055, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1143454597 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1143454597\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1482163829 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1482163829\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-159543181 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-159543181\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1055871126 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZNdnluc3BEWGVGZzJmdjZsaDB1amc9PSIsInZhbHVlIjoibElQcjk3cmVYQksyaGZCemVDNkhhWElYak5DNFhmZ1NnU3FmTzA1RkswSElpVWdUcXdYRFo3Z0lIcXVTNWJ2S0xEelUzVEp5SjZCWFQxWXZTRzRKK1VJeGtHSUtGTXU4Q2RpakxacHBERzVmSk8vQWhZZW80bnpWWHdhMWY2ZFJMZm1pUHhRL1VJSno1RlJWSVJJcWR0cnNSQms3ZGxQWng4NXl2NWRBOTEwT1NLWUVFUEg3S0pyUkdGOFlaVnc0TFFteHBHS2NLUW80VGx3NnpWb3djK2lZKzEwWDJWcHZHSEttcXBBSGx6MkNPVVRaSmtVNk9GRmo2OW00amJRRHJETmIyK0JEZlF5cnZqTWhoNnJEanlJRWVzYTRkYndEYm1NQ2VlN2QveDZaeitycEhnTFZ3RjhDcW9VcmNiRCs4ZWVqYUMxeWszUElXNDlsM1dOSWhkMklWNWdlV0dhSlZibHRBamZiOFhVWlozQ0J0TC9MV0Y3OFNMczRhdEJIMzlJTlo5Sk5nVDlXWjRIQ2c0T2ErUHc4UGtMTlNJMEdXb0VTVUZRTGtLSDJ2VXpsZFlRWjN0c2Jsc2xjOUJ1bjFSMkNVb09TMXQwS2l4WFgvMklWZm5uY2JPVU9IeGIyOTZscExFM3B6YWtMZndHSWFlRzhCOVNLem95RXQvUTkiLCJtYWMiOiJkZjExMmU1ZGViOGU5NDZhZGE4YWE5MmQ1MWI1Mzk1ZmUzMDNjZWMxNjhjMDI4MDg3ODg0OGIzYTk4OTk4MDc2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhXaHA0M2MranFsN3VKaDM1RjNSK1E9PSIsInZhbHVlIjoiUTdLdVVZWFRQT2JLRlZFOUtiaURZbEpLL0tzMHhmYWxGVld4eDVSajQ2Vkg1MjNTVzVVaVYya3J1eWp4M3hDYklYNTVKZmZFdEdzRGVLaUluRHZOL3UrbjRUK1MzTHZXeFF1SzNuUGZ6czN2V3dXdU0vWHJKNUxhQ0h6ZVpxOVFKTVRPcCtpL2RHOFdDWDFSTGF1QVB1Z3B4Wm1NTW1yVUxicTI3TE1tcEoreUFpc0NMQWxjRUlRdXNaMmQzS2c3QmZqVHZBcGdLUjBGMm1CN3B2eVlHK0tna1hVM2ZBeHYrd2pyT0FqSGdKZW9IZmovSlZLWWRET2NKMGFaTFhOUjNSSnhoNFVUaVVnajhBbFAvSVFWY0owaHdab3RrYzhvelEySDhMV24wT2Z6RUVvSkdhaGZJTWtMLzZ4WTgrZlI3cTdoTG0ram01RjdYSjZEckhURFNTVkM2K25hazZKQy9TMXpucCt0L1NQOEFpbWo5YS9ud2hYaHpLUzJBaWF0eGI2c3c5U0JaYlV2cGZBTVRaUGNkL3p6bERQUk16Vm5ZWmFFL2FFTzdTZVcyZ3FidVZYRGE4SlV4d25UQ2o4SGQ5K3FQSE5Ob3ZKR1dmdTZ0UHRWSVNaSWE1V3RQWVRqd1pQWXFyVkY3WkpoRytwTTdyWE5QQjE0NmV0akJnejYiLCJtYWMiOiJiNjM0OWE2M2ZmZTliMmI3ODE2ZjU0ZGI4ZWE0ZTdlOGM0YWMxYTg0YTA5NzdjMGU1MmNmMjM3MDQwM2Q3MDI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055871126\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1768039459 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768039459\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1204955084 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNGM2NzMlZGQlhPcVZWWERWN1NYYmc9PSIsInZhbHVlIjoic3JqbUI0MGJjWVdsY3QwMjM3T1dpTnRZTWV2RCtyZExsaDNWeTBRSjJYeG9saXowdEcrNXFmalQxZXBIUnVLVDB2MnE1ZWcvVm1HdHROd1ZHM0w4di8ya1pQRGNKZnVub0RtSWM3OFV2VnI5bFZUbDk0ZWFQalhKZGFVOThJM09jZndaK2UrZHdxYkZXMFkwK0RZWktSdTBJbVM5T1NjVUQ4dGFPcHRiV1U5K0hqbm82ZEd0Y013RVRHWW9oZVVQMGJUQ2Y2aXQra1dTMzlqZ0M0VFpmRGFaa0xJUVMvRzhQb2M2a2RORElkOThQbG9mQk9lRldQb1FJeDBFNHhoZlJ0L2VNUEU0VjdsQVh5MWZRcE1UcHk3WTU2bkxrZW5lcHhyNEdwdit4S1FRcDJvdEdsWXljS1F6R2xtRThldmtqMm9kSUIxTjJPbFByUVZaR3FlRjIrbXRGY3NZcVYxSzhxR1FzdGlGTFh5UHlMMGpYYjRjTHZ0NVZ2eXpKNm1Eb1NoVlRkUGNWZ3RxM203bVRDeXowRjZOZ1cwNWZscnBOUlJBVldNenZ2ZzVxdWlZK3JFY1Zuc2NldTd5Y0pIcXI4SHRXVXhDRm0rYlRlQisrRks0Ni9vWk5uanpNVEpCaDY2anFQcjBQaFluM3RyclpxcDY2WnVXZGJ5QkN1MkIiLCJtYWMiOiI4YTJlZWRiNTdlNWZiOTA2ZDNlZDQ1OTA4YzU3ZGZlY2U1YzQwMzkzODUwYzFiYmViYmU1YmFmYWUwMWExZDdlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imd2WFZkNGJEK0hscjhocENuZGc0M2c9PSIsInZhbHVlIjoib1hSUVQ4UFQ4U2Q5UTBTRTFlbGJlWVZzemxtL1NQanUyaHlmVWE2czUrSmlVcUVqb3ArQXg5Sno0VDNUbHVseVR5TmFkanZGVHg1TVQvM3FNNEFTTEZzQmNMT0daaU01Uy9rb25XdUw2dmoxbmNxQkxpb1RwRUhTZkVZUnAyeUFIaUdOampoQXNDRWZWbmNTRS9XYUZIS3RwQmNERGw1azBnd2RSY1A2QWJaME43bU93NkZVaGxjdGZzaTRQNWZvYmNzalVIdlZiRkNjc1VhUTJJU1hNWDBRakQ3Mkl3QU9ZOEpCVG1CTmpKbHpkOXcvQWdaUG0xcU51SlFOVkh4YnZjRnhBL1gxYjZZaUhJUERVTzgybHdmWGk3Z1F1cFI5Sk9UcWlHQVZSbWtOMjU1czV6c2V5UGlFTERDTlAxYVNwWCtjNkw4ZnZJTVMzZzBQMTVDVnFXN3NhWlA2MHYyNWVLWWY4QlV3VGJHbnZndU40bWd2dXd4bjYrSjJxTzhHMWJZMXhCS1NyQ3pUR0ZYeVBsTURKdlYvVWVNeElrZURabEx2RzB5eTE2NlFVT1p5YkIySzV5U1g3ODN0MmhiUXgzNXBFY0JJenpQTjdlY0FmU29kZDk4aVo3dmdBeThsdWI5SGF0T2lGaVh2a1ZLNkdZMTJTOEVoU0Q1Y1hxYzMiLCJtYWMiOiI1Nzg3NDhmOTM2ZWI4YTUwNTcxZWIxNWFmMDhmYzkzNmJmZDFjMjY4YWExZGRkMDMyNDc1YWFlMzI2ZmVjYjc5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNGM2NzMlZGQlhPcVZWWERWN1NYYmc9PSIsInZhbHVlIjoic3JqbUI0MGJjWVdsY3QwMjM3T1dpTnRZTWV2RCtyZExsaDNWeTBRSjJYeG9saXowdEcrNXFmalQxZXBIUnVLVDB2MnE1ZWcvVm1HdHROd1ZHM0w4di8ya1pQRGNKZnVub0RtSWM3OFV2VnI5bFZUbDk0ZWFQalhKZGFVOThJM09jZndaK2UrZHdxYkZXMFkwK0RZWktSdTBJbVM5T1NjVUQ4dGFPcHRiV1U5K0hqbm82ZEd0Y013RVRHWW9oZVVQMGJUQ2Y2aXQra1dTMzlqZ0M0VFpmRGFaa0xJUVMvRzhQb2M2a2RORElkOThQbG9mQk9lRldQb1FJeDBFNHhoZlJ0L2VNUEU0VjdsQVh5MWZRcE1UcHk3WTU2bkxrZW5lcHhyNEdwdit4S1FRcDJvdEdsWXljS1F6R2xtRThldmtqMm9kSUIxTjJPbFByUVZaR3FlRjIrbXRGY3NZcVYxSzhxR1FzdGlGTFh5UHlMMGpYYjRjTHZ0NVZ2eXpKNm1Eb1NoVlRkUGNWZ3RxM203bVRDeXowRjZOZ1cwNWZscnBOUlJBVldNenZ2ZzVxdWlZK3JFY1Zuc2NldTd5Y0pIcXI4SHRXVXhDRm0rYlRlQisrRks0Ni9vWk5uanpNVEpCaDY2anFQcjBQaFluM3RyclpxcDY2WnVXZGJ5QkN1MkIiLCJtYWMiOiI4YTJlZWRiNTdlNWZiOTA2ZDNlZDQ1OTA4YzU3ZGZlY2U1YzQwMzkzODUwYzFiYmViYmU1YmFmYWUwMWExZDdlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imd2WFZkNGJEK0hscjhocENuZGc0M2c9PSIsInZhbHVlIjoib1hSUVQ4UFQ4U2Q5UTBTRTFlbGJlWVZzemxtL1NQanUyaHlmVWE2czUrSmlVcUVqb3ArQXg5Sno0VDNUbHVseVR5TmFkanZGVHg1TVQvM3FNNEFTTEZzQmNMT0daaU01Uy9rb25XdUw2dmoxbmNxQkxpb1RwRUhTZkVZUnAyeUFIaUdOampoQXNDRWZWbmNTRS9XYUZIS3RwQmNERGw1azBnd2RSY1A2QWJaME43bU93NkZVaGxjdGZzaTRQNWZvYmNzalVIdlZiRkNjc1VhUTJJU1hNWDBRakQ3Mkl3QU9ZOEpCVG1CTmpKbHpkOXcvQWdaUG0xcU51SlFOVkh4YnZjRnhBL1gxYjZZaUhJUERVTzgybHdmWGk3Z1F1cFI5Sk9UcWlHQVZSbWtOMjU1czV6c2V5UGlFTERDTlAxYVNwWCtjNkw4ZnZJTVMzZzBQMTVDVnFXN3NhWlA2MHYyNWVLWWY4QlV3VGJHbnZndU40bWd2dXd4bjYrSjJxTzhHMWJZMXhCS1NyQ3pUR0ZYeVBsTURKdlYvVWVNeElrZURabEx2RzB5eTE2NlFVT1p5YkIySzV5U1g3ODN0MmhiUXgzNXBFY0JJenpQTjdlY0FmU29kZDk4aVo3dmdBeThsdWI5SGF0T2lGaVh2a1ZLNkdZMTJTOEVoU0Q1Y1hxYzMiLCJtYWMiOiI1Nzg3NDhmOTM2ZWI4YTUwNTcxZWIxNWFmMDhmYzkzNmJmZDFjMjY4YWExZGRkMDMyNDc1YWFlMzI2ZmVjYjc5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204955084\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1215540736 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215540736\", {\"maxDepth\":0})</script>\n"}}