{"__meta": {"id": "Xd122c1fd42f1ab60504bc731b1b337ed", "datetime": "2025-06-08 13:05:58", "utime": **********.607632, "method": "GET", "uri": "/pos-payment-type?vc_name=7&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387957.289942, "end": **********.607662, "duration": 1.3177199363708496, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749387957.289942, "relative_start": 0, "end": **********.302077, "relative_end": **********.302077, "duration": 1.0121350288391113, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302097, "relative_start": 1.0121550559997559, "end": **********.607666, "relative_end": 4.0531158447265625e-06, "duration": 0.3055689334869385, "duration_str": "306ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53390464, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.598, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1424\" onclick=\"\">app/Http/Controllers/PosController.php:1424-1532</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.028329999999999998, "accumulated_duration_str": "28.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4041388, "duration": 0.02062, "duration_str": "20.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.785}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4489849, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.785, "width_percent": 4.024}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.496838, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 76.809, "width_percent": 3.706}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5032442, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.515, "width_percent": 3.6}, {"sql": "select * from `customers` where `name` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1435}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.517264, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1435", "source": "app/Http/Controllers/PosController.php:1435", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1435", "ajax": false, "filename": "PosController.php", "line": "1435"}, "connection": "ty", "start_percent": 84.116, "width_percent": 4.553}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1436}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.524598, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1436", "source": "app/Http/Controllers/PosController.php:1436", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1436", "ajax": false, "filename": "PosController.php", "line": "1436"}, "connection": "ty", "start_percent": 88.669, "width_percent": 3.212}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 531}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1440}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.535414, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "PosController.php:531", "source": "app/Http/Controllers/PosController.php:531", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=531", "ajax": false, "filename": "PosController.php", "line": "531"}, "connection": "ty", "start_percent": 91.881, "width_percent": 4.306}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1519}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.573765, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1519", "source": "app/Http/Controllers/PosController.php:1519", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1519", "ajax": false, "filename": "PosController.php", "line": "1519"}, "connection": "ty", "start_percent": 96.188, "width_percent": 3.812}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1298578916 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298578916\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.514468, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-765186087 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765186087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.533721, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1473089468 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1473089468\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2071989414 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071989414\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1622499532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1622499532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2076112490 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRNNEN6K0ttYnhvVGpFRXQyWStkQ3c9PSIsInZhbHVlIjoibTBZL3lJWTd1cHZUYytTcjVLd2ZCaVVzQzcrdU5ZOFRVZmYzZGw2Y29KaWI5OElscmxLemFNdTN0d0V5bFBONkhPZnNZYSt6SkNWZytGM1VDeVhNdEpmUVE2ZzhGVUVrWjNGbDJNSlNSaTVMUmVjTG0yOGV2d0FyN3c3UWk3c0pycW15SC82MStqSmp6TEJWaEFVUGFtME1LTW9OeGxFQW1aeWNnOVhuMXRncHZVK1hIVVlOMk1nTDI5OU1pUU92WmJTTDEwSUt0VGJtd3l1U2NJK0JXY0d2TU1KczBmTnY1TXd4Q1VsdUVVTVhLYTZvRTIxWHFLZTJPaGRIaHkzbTNvdXdQU3YwRXNqdmttenp0R1hxenl6alB2YW50ZUNmWkxOdWVtekM0ZnpyWE80TC9peEQrVUY1TWlPQndnYmxHc1MrRTZla1lKcFZuNXNTRmF5TWRGQ1NSZXlYT1JkY1dNUCt0aVZFb1RERzZURjh5OVI1cE00Ymw2MVJPMjR1VnVNalFWdnFqRkxXekFTcFZRQ3dpR3VGOURBYU0xaEV5Tng2RmR5d2I0VExtN2U5cTlERFFjVEZzeUdCdTNiR1FxU2JzZHFGNkpQSlpQcHUxTjhTZlZaMXpEZEZybU42UUlYeGF6MHB4OVhwYlAzWVFxcWxNZEc2emtRaWVRQ04iLCJtYWMiOiI1YmY1YTE0OTFhNzYxMTE5MWJlYjcyMDA5YTAxYTc0OWJlOGNmYWNmNjE3NWZmOTc0MmRhMDlhOGU3MGFkZTQwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFsVkFVZlpCbkVUVjd3aDlRMU9QU2c9PSIsInZhbHVlIjoiTVMvNFhPdGM4dGFwN0JtUVpqZjR1QnFFcXRrNzR6UURxTzNKTE5DSXpwSEh0TUloZjBPNnZQVlhJY3RnSU9jQWJCV3Y2RGQwNFJXYXhCdVlsSk1VN09xaVp6NU5kSkxudktvenNIZkVTRHFLN1A5Yi9QeW9pYU4vakRZQTRDV3k4WEQ1YkM4aEd2SGNFdkorcTREUlF6eUUwOHdaQlNOZ0d5OGZsbEdQRG9CdnVkSzdhMHV5a0Uvai9ubnJMWDBMdEh0RFAwTVd5aHFPcFlvQjNKQkFDbS9IQ0xWRmc3c1FNRGN2TWlqRmZBc3FTYzB6cGkvaVY4KzZXeGNNQXBjNk5tREU2SGQ4VHN4c3ZSOGFTWEh4MS9adDBiaGtCZFN2MHlXakZxMHBJYWRHdU11VG1yV2ZzdVg0anJCYlg3UUlvQ0lzL3JLZFdxWWNZVU1vTnEzYXpMTktBcndnYnlMa3M4MjlxZU9lbzBGakdXMmUzckhXVStQOFpyTXdScEYwd0ZnR2FBbzB2dnFyOEluRHVWczN1eEM5ZmhLbU9OL1lFSDhNQUUxVGRGWlRGdFN2S3dscHZNRmlBUWM2RVVuNXBIOFYwUXdNK2N1aU1iejd6TnVGZ2U5dTVxYzBodGZRcWpDWTFDTzZJVUs2MURSTTdtZ2ZhU1ljTkhJNTJvV0wiLCJtYWMiOiJiZTg5ODMzYTIxYmUyMzhkMmNlZGRjZTQzYjk1MWY2NWJiODc2MjYwMmI0YjVkYmE1ZmZhOTE4YmQxMzIwNWQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076112490\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1193519821 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193519821\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-352321163 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFvR0ljMjFHZWhFTGhZcXV1WkJlcXc9PSIsInZhbHVlIjoiN1o4a3NSMUFuYURPa3JRZ0lhV2YrWmg3eWhuQW1LbW0yNnEwWmoyWGM5VTZKZ3VOMnZpWDByd2pBN1NWWU5jMHB1UUhaWTRTcFNYMVZOdTNCeTh2NWxiZWRTalQ3Z05qN2tYU056SXdKNFJkR0NYV3dtNHdTQnIrRU94bHZRSldBTzZYdW00WGVEZ01leVllekhhMXR2Yys5aWl4c09qcEhtaEVRd2xEOVN1TTN6Z3JDRjUxWUFvNmRPekVXNXNVampWZDBHV2JyTGVEb0pKMktBUGRMZVFjcHAwcE1OSDcvUGt6cXU1ajVEZnF6blhkcnkwNEM3TWpDd3VLZkxyNC9vRmo4QStOYXpyVHdiZzliVjN4SlZlV0dGb3AxUS9FK29RS1g5MVEwaGlRWStjWi9sYTBWYUUvaGJoVmRsMldHTVJSU3lFb1BFVWxuSzNUWk5OdFlHbE8zc0RpbmRRU3A0UWp0RTNWVmRkR0VheG4yQW8zR2ZBRHpvci8rcnlTUkZFZitNZ1FRWDhBY1VCU2VtQlh6Yk14d2NQazQyRVgzblZFYTFWTkpRSXFpZHQzbzRHZVdTb3JhNE1sbktUS0hsRk15alVqeHowRXkyT2ZwYy8vY2pCcFpVcFo2MUdGSEg0ODVOc1JRUUs2MTJXVlhLTW5LVWQ3THo3ekdMcXMiLCJtYWMiOiJkOWQ3YmUxYTAwNmZjMDlmMjQyOGJlZWY1YjgxYmVkMTU5MGY2ZGViMDU0MDMxOTEyNzRlZTg3NjBhYzVmYmU4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpOTkFYbE9idTJSUWE5S0tVVXR2Wmc9PSIsInZhbHVlIjoiN0tITmRVUkswTW9xUFNia1R0byswMEZ1R1FvaTZnQnJNT01vV1pSanh0UnhSQUNvQVNWWjBLV0dLNG5SZk1jSXg4eEtpMDBYR1A4bGF4NzN1MTNYTllZaEFsZ2FSZ0dSRjRvTWtoVVdFZmFCN0MvQlJ1dEZSeHJMRUhoMFpEVGZGQzRtWTUrbWU2N3RLSnF0ajQvRDZOc2JkRHBvWkpXZTRmK3dVQzJTVnJPNDdEdGhsbzhrZnlpRTYrTjZBNmQrSXozRzd4UTFWc1VlZDBEVlgrQUVIVkxLRWN1SHh1WGhxNktMVWg1WkhlM0pQMHFOZm5BVHh0RFhaYngwa0ZGbVhaN1lLSGtFYlhDRUxSdkN5dUJWYVZrbnNtK0hhZ243Q2ZCbElHZTY2UVpYdUFTMS9qNitkZWJvenlpMXZCUzVjWGJ1SytDMTJZRmVEc1R6SnNsZEVZL2ttS3I0OWEzaU5iRlpLR3N5VWMvZS9MNmZTRFphREE0TDV4LzI5a0VFRlRRZEpGKzE2WTI0TzdrOTI5c0JrVkRMc1QzTk1MWC9DaWVveHlma2NEdENsWVVmMVZtdXhpR291NnowcjVmVXl2cVQrKzBZdm0zTStWU3liZjZKamQwWTdzNFhrU0VIRUUzNXAzcWZyOFg1WU9nUHFlZTVCd3dnZVBaNnRmVEgiLCJtYWMiOiJiZjgzZWIzM2IwM2YzNGUyM2YxMTk2ODAzY2IzMzQ1YjA4YmYzNWM4ZTE2ZTc0ZDA5NjU4ODNkNWNhM2JiOGY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFvR0ljMjFHZWhFTGhZcXV1WkJlcXc9PSIsInZhbHVlIjoiN1o4a3NSMUFuYURPa3JRZ0lhV2YrWmg3eWhuQW1LbW0yNnEwWmoyWGM5VTZKZ3VOMnZpWDByd2pBN1NWWU5jMHB1UUhaWTRTcFNYMVZOdTNCeTh2NWxiZWRTalQ3Z05qN2tYU056SXdKNFJkR0NYV3dtNHdTQnIrRU94bHZRSldBTzZYdW00WGVEZ01leVllekhhMXR2Yys5aWl4c09qcEhtaEVRd2xEOVN1TTN6Z3JDRjUxWUFvNmRPekVXNXNVampWZDBHV2JyTGVEb0pKMktBUGRMZVFjcHAwcE1OSDcvUGt6cXU1ajVEZnF6blhkcnkwNEM3TWpDd3VLZkxyNC9vRmo4QStOYXpyVHdiZzliVjN4SlZlV0dGb3AxUS9FK29RS1g5MVEwaGlRWStjWi9sYTBWYUUvaGJoVmRsMldHTVJSU3lFb1BFVWxuSzNUWk5OdFlHbE8zc0RpbmRRU3A0UWp0RTNWVmRkR0VheG4yQW8zR2ZBRHpvci8rcnlTUkZFZitNZ1FRWDhBY1VCU2VtQlh6Yk14d2NQazQyRVgzblZFYTFWTkpRSXFpZHQzbzRHZVdTb3JhNE1sbktUS0hsRk15alVqeHowRXkyT2ZwYy8vY2pCcFpVcFo2MUdGSEg0ODVOc1JRUUs2MTJXVlhLTW5LVWQ3THo3ekdMcXMiLCJtYWMiOiJkOWQ3YmUxYTAwNmZjMDlmMjQyOGJlZWY1YjgxYmVkMTU5MGY2ZGViMDU0MDMxOTEyNzRlZTg3NjBhYzVmYmU4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpOTkFYbE9idTJSUWE5S0tVVXR2Wmc9PSIsInZhbHVlIjoiN0tITmRVUkswTW9xUFNia1R0byswMEZ1R1FvaTZnQnJNT01vV1pSanh0UnhSQUNvQVNWWjBLV0dLNG5SZk1jSXg4eEtpMDBYR1A4bGF4NzN1MTNYTllZaEFsZ2FSZ0dSRjRvTWtoVVdFZmFCN0MvQlJ1dEZSeHJMRUhoMFpEVGZGQzRtWTUrbWU2N3RLSnF0ajQvRDZOc2JkRHBvWkpXZTRmK3dVQzJTVnJPNDdEdGhsbzhrZnlpRTYrTjZBNmQrSXozRzd4UTFWc1VlZDBEVlgrQUVIVkxLRWN1SHh1WGhxNktMVWg1WkhlM0pQMHFOZm5BVHh0RFhaYngwa0ZGbVhaN1lLSGtFYlhDRUxSdkN5dUJWYVZrbnNtK0hhZ243Q2ZCbElHZTY2UVpYdUFTMS9qNitkZWJvenlpMXZCUzVjWGJ1SytDMTJZRmVEc1R6SnNsZEVZL2ttS3I0OWEzaU5iRlpLR3N5VWMvZS9MNmZTRFphREE0TDV4LzI5a0VFRlRRZEpGKzE2WTI0TzdrOTI5c0JrVkRMc1QzTk1MWC9DaWVveHlma2NEdENsWVVmMVZtdXhpR291NnowcjVmVXl2cVQrKzBZdm0zTStWU3liZjZKamQwWTdzNFhrU0VIRUUzNXAzcWZyOFg1WU9nUHFlZTVCd3dnZVBaNnRmVEgiLCJtYWMiOiJiZjgzZWIzM2IwM2YzNGUyM2YxMTk2ODAzY2IzMzQ1YjA4YmYzNWM4ZTE2ZTc0ZDA5NjU4ODNkNWNhM2JiOGY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352321163\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}