{"__meta": {"id": "X0da16a52ba4c572afb139d5f2484620d", "datetime": "2025-06-08 13:34:57", "utime": **********.485819, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389696.197809, "end": **********.48585, "duration": 1.288041114807129, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749389696.197809, "relative_start": 0, "end": **********.30546, "relative_end": **********.30546, "duration": 1.1076509952545166, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.305481, "relative_start": 1.1076719760894775, "end": **********.485853, "relative_end": 2.86102294921875e-06, "duration": 0.18037199974060059, "duration_str": "180ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45587448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024179999999999997, "accumulated_duration_str": "24.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.383436, "duration": 0.02123, "duration_str": "21.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.8}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4326, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.8, "width_percent": 5.749}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.459072, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.548, "width_percent": 6.452}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1730370101 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1730370101\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1728640261 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728640261\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389691574%7C33%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9TaU1vakh0M0tVWEQyMXFOMitDbkE9PSIsInZhbHVlIjoiQ1M2MkxLNlZBSno2OVBYUDVFWHk1eTNhUG1UeThvSHl1d3FhbkhxeTVUa1dIb0VOWnQwRGRvbHdqSzNJaHRzSTdtdzlGMXBYQ0RiUG9FVTRxbStvckRLT3l3d2hWZ0lGZ2crUEhrQ3htTDFNbFFZVEdoN25DbmlaTEttVm85K2NBYkFSbTNDMnRuSkNqVmZoVG9oOUYrcDR6eWVGUVV6Tm5uYUJiQ3dFRVEyeC82dGtMTW1YSXRJMGE3REt6MVFVZFQ1UklhQ3ZHQVNzblA0bGlRRStVdXZleGR5VkFLeFB2RmJGZ1BOek5HODNLYWlhZVgvZlRMZkdUYmpDNHFnVDU3KzZ6clZxUDhNbkpXU0MzaDdrUTdkL2RyczZtcHNROWxwKzhoKzl6YzFTeXF2R0p3V0ZLR25wbisyS3pJS1J4ZmRKUks4SW1qMlA2d0JZSWc4OXYwRXJDaUM3Ukp6UDFpZUZoTHprYThFdUNoQkFBMFJvNTNOeFN0eGU0QnlnS2ZoVkxEeWs3YnBrRUV2bkpXWlFLZHN0VVR3SDlZdjl2VXl0SGx0YzhDbngyY25vU0NReks1dzZ6WUlmemFYVU5aVktTU3dRS3pOUEI4UHlpSzNycHJLNlZGOUl1M0x2cTl2Mm1xQWQ1ODhmY0llcU4yMGdxR2FrSE5JaXVyazgiLCJtYWMiOiI3OTRhMGQwNWY5ZTZiMzc2YTFiYjE5MDBiNzRjNGFjNGMxNzY4MDhlZDFlNGY5YjVhM2Q0OThjZDBmMDcyMWMzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InROUnhYK1laWUJkbTlTN0k3Z1hpRkE9PSIsInZhbHVlIjoiRUtYb2FGUms3UGNKOXh0OU9ockRCcmRjUENna2VJR3oxKzJKNW96eUlWUVB4bnVvSlEyOEtTejV5WldJZFNwcXd5Y3cyaUlNamV0K21QdGZKNEVDUmh6WVlWbi9md0RnWm1jQ28yeGFpN3FUMy9MSjFQQk14M0dza29KendLTjZxdm5aTURhRnNBdlF6VHFoS05nRnJMTGJPR1FMVHBoamk3TjJ3YjVWUE16aGNmNklwb3JTL2E5bzJzdy9kWm5zVTRQV1RzNUlDQ2IxektRWktLUEJFYjRubEpFVHhwZGRuSEVaZ3o2TVBrVE5XdkRyOXl5N2ZiOEFPTG1sc1dYQmZ1eU5QRklHQ2M5bERMb3ZpL3l0RHRpNzNhbkYrUmgxREtZVi9jUFc4STUwcXlqdW1ZVlpjUWQxNkJsc0FwTVdXSDkrakh0UjVkTlFzckJ3Sk1YMXBFWTNFRE05M09lWUpNajNpU2p6N2pvbm5BWkZoWGdrOGdOaVBHNkFCY1cyOC9zSVp3QnFYYzdBSEt3ZlRGeU10R1EwWW5uMm9aSGNJbVVRSWdhbVpRZUwxK1U0Vzd0b2pCYnhFTzd6UUREQzlzVjdJNUxzMHFqRVNTejJ2RGMwUHJRVW9rYUk1T2pDcHYybGtuSFNmZ3BhRXMvU3JKMmJ6WHIzbTZKdG16YisiLCJtYWMiOiJlM2I5YTFlMTllNmUwODhhMTMwOTQ5NmU4ZDU2ZDI2OTdhNDQzMjZlMWNmMGViNDZjYTU3OGQyYzcyMDAwMWQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-931940215 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931940215\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-536765144 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ6Tko4Snd2ZE9vTkhEWVQ2d2RPT0E9PSIsInZhbHVlIjoiOStEdE1oamZJeEdneVVUbmM2NStEUUo2NHFsZWQ2cForWEtkTHkwU2F5WVhwQVh3eG9sTjFONExsREZjeitBWG0wRWgyVzAwZDF0ZnI0cUxtS3IySkJBdXBUdXY0S2pKQjBQOXkxMVJSN3NlZ3FZOTFlRzlsczluMHM3c3FpTnUya0ZsRWtFUEdGbmprNG12bThNaW94enl1d0crSVJtQTBoSCsxZmxjV1NGdE5iL3I3akFuL0dBZVNkSVZYaTRwSXNoT2xiK0RHVGxNbFJuYnlyRFlDMGl5bmpSS00ybGZXTjhUM09rQ2hNMUtibEw1VVlqSW1rV08wUytvc3MvMmRiTmQySE1iRnNBZ1ZqZjFvSCszcy9FYk1Mek5LNTFNMjRCVzZsWE4zZ25HMnlhRE9IZkVVYlZjbHR2UFIvUmRQczM2SmhXQU5DVkRRK2Y4c256R1J3VFpMVmJodHZJODAwVEpUUEhxQTdVWjNnMnd1M0FQNXJlL29GaGc0SVR2eGdUVHRNUEJhUmtzSm1Nd1RWZ0p1djBVWkwvUmExSVVlYitQUG03N3o1cys1cXNIc3h6N0JETjMxbjl4ZEo1VUkzVGZDMGVqY3ZIVHc1OHF4ZVRhN1hPbGkrMFpjTXQzSEkwcEJuc3pKYkw3b3dZekdUeC9CNzZYWmp4SWhPSWciLCJtYWMiOiI0N2RjY2JjYjgyYTcyMDU3YTNlMzYwMjAwYzE1YzcxZmMzNjAyZDE2NDZjNjQxMTc0MzQ5OTI1Y2E0ZmExNWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imo5MTMySWZmQUlwZGNWK1lFM2FYL2c9PSIsInZhbHVlIjoiTy9PSWdFRUpKdHFqK2JLTjVyU3pVL0FrN3loSUFlTTBpTVZLWWJBeHRZZWxJNmw4TjJaSU5jSkl2UXBjclUySkNQYmdsYk9pRzhWTHl2QU9SdFRkbnBxY2hDTnpJSm1CWXp4ZWZHNVUxcXZzOXFrRVpxbHFxc213dDhnL01Ud0gxdmdyQjdOMmZqZkZRandBeVJiOHVnMy9PWGYzeUdMaW5xdjJQMmpFTEY2VnhvSU90Q2RiNkNiQmU0UGNJUEhWTi9GSnRsSXlLbmZ2RERLZjBWcGxaZVllZ1BZMjRwZkJDbWpRWnJYNGM5Y28yOXVsMndqNm9vSGN3SHE0QXAxWUk2aWZ1T1l5VHpUQ0praERTRm9HTEdCWTZ0cnNpSDl0cmhQaEdiZ0RQUHR0MnNvWnQ0RDNENlNqZWk5dlR0aWhRMkREcHg1dzR3Y3h4amh3bVl1dktOYWU1dkZUaE4yU3FhMGVnUm1DSVMvWERNK1V1SCtNcXFXbUEveW14UThIOE4vT2p3UzRlbFlmQXVCeTQ2QURwOWtqTTlxckttRHNMcHJpWTRoYzlsQXRmMFBlSVd5MC9lUUs1ckFENlhlbFhtZm9zZjV5cHc2cmFnenQ3NVFNWUI0R3hoTHFQNS9USXlUVjFESWZyUDlha1V0alpGanJiNkFsb2JXNlY0cVkiLCJtYWMiOiI2ZjY4YjI0YmJmNTk3ZTU5MjUxNjI2YWMwMjA3NzRlNmM4MmUwMjdjYTllODQyODg1Mzc2N2IxOGNhOGJjZGM4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ6Tko4Snd2ZE9vTkhEWVQ2d2RPT0E9PSIsInZhbHVlIjoiOStEdE1oamZJeEdneVVUbmM2NStEUUo2NHFsZWQ2cForWEtkTHkwU2F5WVhwQVh3eG9sTjFONExsREZjeitBWG0wRWgyVzAwZDF0ZnI0cUxtS3IySkJBdXBUdXY0S2pKQjBQOXkxMVJSN3NlZ3FZOTFlRzlsczluMHM3c3FpTnUya0ZsRWtFUEdGbmprNG12bThNaW94enl1d0crSVJtQTBoSCsxZmxjV1NGdE5iL3I3akFuL0dBZVNkSVZYaTRwSXNoT2xiK0RHVGxNbFJuYnlyRFlDMGl5bmpSS00ybGZXTjhUM09rQ2hNMUtibEw1VVlqSW1rV08wUytvc3MvMmRiTmQySE1iRnNBZ1ZqZjFvSCszcy9FYk1Mek5LNTFNMjRCVzZsWE4zZ25HMnlhRE9IZkVVYlZjbHR2UFIvUmRQczM2SmhXQU5DVkRRK2Y4c256R1J3VFpMVmJodHZJODAwVEpUUEhxQTdVWjNnMnd1M0FQNXJlL29GaGc0SVR2eGdUVHRNUEJhUmtzSm1Nd1RWZ0p1djBVWkwvUmExSVVlYitQUG03N3o1cys1cXNIc3h6N0JETjMxbjl4ZEo1VUkzVGZDMGVqY3ZIVHc1OHF4ZVRhN1hPbGkrMFpjTXQzSEkwcEJuc3pKYkw3b3dZekdUeC9CNzZYWmp4SWhPSWciLCJtYWMiOiI0N2RjY2JjYjgyYTcyMDU3YTNlMzYwMjAwYzE1YzcxZmMzNjAyZDE2NDZjNjQxMTc0MzQ5OTI1Y2E0ZmExNWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imo5MTMySWZmQUlwZGNWK1lFM2FYL2c9PSIsInZhbHVlIjoiTy9PSWdFRUpKdHFqK2JLTjVyU3pVL0FrN3loSUFlTTBpTVZLWWJBeHRZZWxJNmw4TjJaSU5jSkl2UXBjclUySkNQYmdsYk9pRzhWTHl2QU9SdFRkbnBxY2hDTnpJSm1CWXp4ZWZHNVUxcXZzOXFrRVpxbHFxc213dDhnL01Ud0gxdmdyQjdOMmZqZkZRandBeVJiOHVnMy9PWGYzeUdMaW5xdjJQMmpFTEY2VnhvSU90Q2RiNkNiQmU0UGNJUEhWTi9GSnRsSXlLbmZ2RERLZjBWcGxaZVllZ1BZMjRwZkJDbWpRWnJYNGM5Y28yOXVsMndqNm9vSGN3SHE0QXAxWUk2aWZ1T1l5VHpUQ0praERTRm9HTEdCWTZ0cnNpSDl0cmhQaEdiZ0RQUHR0MnNvWnQ0RDNENlNqZWk5dlR0aWhRMkREcHg1dzR3Y3h4amh3bVl1dktOYWU1dkZUaE4yU3FhMGVnUm1DSVMvWERNK1V1SCtNcXFXbUEveW14UThIOE4vT2p3UzRlbFlmQXVCeTQ2QURwOWtqTTlxckttRHNMcHJpWTRoYzlsQXRmMFBlSVd5MC9lUUs1ckFENlhlbFhtZm9zZjV5cHc2cmFnenQ3NVFNWUI0R3hoTHFQNS9USXlUVjFESWZyUDlha1V0alpGanJiNkFsb2JXNlY0cVkiLCJtYWMiOiI2ZjY4YjI0YmJmNTk3ZTU5MjUxNjI2YWMwMjA3NzRlNmM4MmUwMjdjYTllODQyODg1Mzc2N2IxOGNhOGJjZGM4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536765144\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1430705127 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430705127\", {\"maxDepth\":0})</script>\n"}}