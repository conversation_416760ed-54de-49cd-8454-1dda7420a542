{"__meta": {"id": "Xe46a1a67bdbd2ab5c79d31f609f44f8a", "datetime": "2025-06-08 13:27:37", "utime": **********.073397, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389255.748551, "end": **********.073439, "duration": 1.324887990951538, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749389255.748551, "relative_start": 0, "end": **********.824432, "relative_end": **********.824432, "duration": 1.075881004333496, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.824455, "relative_start": 1.075904130935669, "end": **********.073444, "relative_end": 5.0067901611328125e-06, "duration": 0.24898886680603027, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131096, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.029299999999999996, "accumulated_duration_str": "29.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9233148, "duration": 0.02184, "duration_str": "21.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.539}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.971059, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.539, "width_percent": 3.447}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.019855, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 77.986, "width_percent": 5.631}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.026768, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.618, "width_percent": 5.324}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.040585, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 88.942, "width_percent": 6.962}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.050333, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.904, "width_percent": 4.096}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-313778340 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-313778340\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.037907, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-324655138 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-324655138\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1471919563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1471919563\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1779964890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1779964890\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZPWmxBZVN5YUdweTYrNjAzWnM3MHc9PSIsInZhbHVlIjoiWnY0VnViYUVQcDNhQThmOXhZeUJWRGRsdjRKaGFnWWJpN0RNdTI0aFZUcUFtbWo1UEE5ZHRsMlErNld5MGYvcXVoKzRLWElDUzhhQnUwWDBKbjlpY0dYSzkveDUyc0VZUXduQ3ZMMUpvL2hnY3lESWZVNnp3Y1VCS2drYi9mcDI5SVduclo5Y1pGbnpQeXVmK05jWW11WnFYZ2MxSWpKV0ZadSt6cFhOcU5hbXE4TEtvSWFYMXE1SE5ZL0Zha1U1RC9weGZxcTdiN2l2Ym1MNGovbnYyMXRZY2RxcHJrcXZMOXNITmFNeFQ3YjlHaWpDbFplVlNMcnVKOGxHUW0wUWZHbnlWVy9TSXlRV2pCT3Z6VS9md1ZuLy9acmJWZTU4cDlIMHZOVGhmTmhKZFcyNEs5ZlZUQzJZdGxldFpkRHpwNldrQllyL3hVSkhUcGJaS2NYUmY2Q3VSNld3ZXp0WUNYanBpZFdHVWlwVTVKZ3RuNlpHZTNudE9yUTJtbTBMWGFaalN4bTYzY1pEVVA2dU5XT0ZNSm5RMytueE0xdXpObW9yMVIzSks3NzBHcmQzMnphOTlJSC9pS0FNN0JQNnFVV0IyVFNpd1NiTk5ucWJucXdCSU5rQVJRZXNBMXdvRU5vbEM1Z25Zd1ErQnNEQUJPS3pyaWUvYTRXZWQ2TjQiLCJtYWMiOiJmZjA0Yzc3OGEwNDBlZGQyZmViY2I2MzExYzU2Y2U3YmRkOTVjOWY2ZDAxOGY1NTcyMjZjZDQ3NzFjMTIxNzlhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhqL3dJVkxxN3o1Y3YxWlJIUlhycVE9PSIsInZhbHVlIjoiMjJ1NnBJU3hoTXlQNC81MUxQMlRmVC9RV2JFOG5MOWloOVFPVHBhU0tQM2xmSkV4Y2NIYUd3NTZ6TkUxRUtvbEsyeU1KODlyN2VOZVZHNHRiVmpGSVNObTJrakRUWWRTNmk0RUhnNUJqV2FjVTRXUW9ZaXJGalhaRmhXSU0rajZqWG9OS0ZSTmJnVDl4YXJhUm1aWVR6cVhyOGpoTml4dWI2U3BHRUlvUkNjQlpDY0dRQ1p5NUc5b2JlV05PMVdLRngvemxGMnZnT1J1bFFKWFdna2NCSGdjeGozQy9qeW1UT05iSmluWXZRSG9PVXlubTZ6c1J5cHd4WmpSZDB6YnpGM3k2dFdJU0hYYmxXN01FRkUvUG9zQXNkWDUzcGtCZzFqczRVa1hCYkFOUzFmNjlQZ1VFN2lHaGdLU2gzdG5ldm9YcG1HZmJwRkhLMnpDUVhvNUs0dlhFYmY1MFMwbnNqOVZjckE5RXh3MEs2a1hZYmg5ZGNEOC8zM2lFRmw1QVlyMFUxU1NjUGVoaHVULzNHa1NpNEZoWE44aG9GcTNtcFcrVWhSUk5LcTQ3WUIvZm9HSkxkSUhuWUQ0d3M5TzJsckU2YmhKR0dZRHlUVHJXTFJZU0tSZG11cjg5QjZRWmdpeit0UkdyQzNhSTVJVHpMOS9hZWRxTlR4QnVIS3IiLCJtYWMiOiI0Y2QzYzMwYzIwODhkNGYxZjdjNjUxZGRiNDc3M2QxNWE4YTdkZmRiNjFkOGZjNDZhNWJmZjY1NDVjOWI5ZjczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1716127550 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InM2eERaTHFsNC9xZkRrMDdwTDZTenc9PSIsInZhbHVlIjoiUzlKcmhKMnAwcmlIYnJKYVVRL2dBZ3VnNjJEVUdyQXRWUHBLSzJ3UWhscnhqZVBOUTJBN1hDcE11MlIxNkNZT2JLNnpOWmsvVWw5U25RdkZaaCtBYlMzYzZYVm5MRzczRHpHeDZVdWlVc3Rsck54SW1UVzJxQmM5WG1RK1RsanFrY3Q0b1pCQ01uMFc2aWk1Nk93eDFXU2wxcFhNYW44c1FBZkVJd0hFLy9wUGtxZEVpMkJONThKUXBuWCtLanYydHFOUjNDR0FWTThJZTNHYWZ2TjVkTHk0ajdKSEdrWG9DTE1qUFB2MjNXbGxYUjNaTVhTVTFjeFppaDlOSzlIUEZvSnRPR1NrdWMxTDhaZlNWbzFyUW1iUXhNMHJBZ1BiWENqMTZDbHZ4M0dIT2p5RjN3YnRCQTYzOVdrT3RDb2VMcFcrRDl5UDVwbjhna3RHUHZEQTdEYlQyUTlVZFliazBvQU4vUEQ2ZXpHWEx2a0xOay9uYUtwMHJScVJhM3lTeVgzK24xcVBpOVR4Y29zSlNUb0YrNDZqK0NKaDF2TkU1aVU5SW5mOFo5NEdobzhYSjJhTXpKOEpaZDBmb1UxN0grdHlRcUhncmZxZjFMTzExTlgxd1E0Q01HMEppbTFhamR0N1RYN2xXMVRVUHpqN2FlcnBGdThaRThLTnpxSHgiLCJtYWMiOiI2Y2Y3MTc3NDU2Y2FlOGYwMGYyMWM2YmNmMjEwOGRkMTRjODE3OGIwODdkNzdhMDRiMmUxZDlkOWRiNThjNDlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRvUkNycng2M01YaXZ4OXk1VURKcFE9PSIsInZhbHVlIjoiYUN1S01VRkRMbEhRcXRYY3I1NURDSGFKczBVMnFkWHNBUmRJMk5kUjYvdGJ2VStXNFByeXRsK3NoTzVMd1hXQVFYODl5WVlValF4YlQySmtWWTNBVnlUdWlFVjhBU2ZaRlBBaXkxVmpudzZDOURlSG5NdWlKc2NVTVRrRDBsK1F3dHBPZzB6YS9iMDFNV2ZIMldUN0gxcnBlaWFjZnRDRkd6WHI5NjJZZDJkQTRKOU1zOTJBeEtZVER1UTB2cHlxNnZ4RWUzYkFDWkpQZy91cTdKdGtZQy9TSk9VZVlKS0gxYUlLWHFCSFEweVJaQXBRcnNtcHpkaTRyYjBwSS9zaFpwN0lMNEVxMUZkNG9JZU8yMUdYNFlaeEcwKzl0OE1GbHlGODQxeTFvK0tXcVk0ak91cHhyZFVIUFhERm5tZ3R1VTdzbkkvN1JhWVVMYWtubUJSeloxSEFhWFlURGhmT0VEUld3QjVlS1ZaSmE2NHlzeHZabjdvcFlud0VnSGpJeWthdGZ2eVM0UkpuMFFjOXVQTHoxajh6MXVqakduZnVFcE5JZEhNa2xwNFF1d2crOHZTbGMwZGZ3TnM1VitPTGFQRDFzRDh2MThKQkdLSlNld1VEYmlvRW9uSjF6c0p3ZjZFaWJ1VVVwbXJTVm5sbEhPMHBKM1p5S1htUWh4SDIiLCJtYWMiOiI5OWEyMmMyNTczZjg5YzgzMTA3MjU2ZTEzYmQyMzIxZDg0NDZmNWIzOGYzZTdhNmRiYTAzNmQ3ZTM5ZGVmNWJlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InM2eERaTHFsNC9xZkRrMDdwTDZTenc9PSIsInZhbHVlIjoiUzlKcmhKMnAwcmlIYnJKYVVRL2dBZ3VnNjJEVUdyQXRWUHBLSzJ3UWhscnhqZVBOUTJBN1hDcE11MlIxNkNZT2JLNnpOWmsvVWw5U25RdkZaaCtBYlMzYzZYVm5MRzczRHpHeDZVdWlVc3Rsck54SW1UVzJxQmM5WG1RK1RsanFrY3Q0b1pCQ01uMFc2aWk1Nk93eDFXU2wxcFhNYW44c1FBZkVJd0hFLy9wUGtxZEVpMkJONThKUXBuWCtLanYydHFOUjNDR0FWTThJZTNHYWZ2TjVkTHk0ajdKSEdrWG9DTE1qUFB2MjNXbGxYUjNaTVhTVTFjeFppaDlOSzlIUEZvSnRPR1NrdWMxTDhaZlNWbzFyUW1iUXhNMHJBZ1BiWENqMTZDbHZ4M0dIT2p5RjN3YnRCQTYzOVdrT3RDb2VMcFcrRDl5UDVwbjhna3RHUHZEQTdEYlQyUTlVZFliazBvQU4vUEQ2ZXpHWEx2a0xOay9uYUtwMHJScVJhM3lTeVgzK24xcVBpOVR4Y29zSlNUb0YrNDZqK0NKaDF2TkU1aVU5SW5mOFo5NEdobzhYSjJhTXpKOEpaZDBmb1UxN0grdHlRcUhncmZxZjFMTzExTlgxd1E0Q01HMEppbTFhamR0N1RYN2xXMVRVUHpqN2FlcnBGdThaRThLTnpxSHgiLCJtYWMiOiI2Y2Y3MTc3NDU2Y2FlOGYwMGYyMWM2YmNmMjEwOGRkMTRjODE3OGIwODdkNzdhMDRiMmUxZDlkOWRiNThjNDlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRvUkNycng2M01YaXZ4OXk1VURKcFE9PSIsInZhbHVlIjoiYUN1S01VRkRMbEhRcXRYY3I1NURDSGFKczBVMnFkWHNBUmRJMk5kUjYvdGJ2VStXNFByeXRsK3NoTzVMd1hXQVFYODl5WVlValF4YlQySmtWWTNBVnlUdWlFVjhBU2ZaRlBBaXkxVmpudzZDOURlSG5NdWlKc2NVTVRrRDBsK1F3dHBPZzB6YS9iMDFNV2ZIMldUN0gxcnBlaWFjZnRDRkd6WHI5NjJZZDJkQTRKOU1zOTJBeEtZVER1UTB2cHlxNnZ4RWUzYkFDWkpQZy91cTdKdGtZQy9TSk9VZVlKS0gxYUlLWHFCSFEweVJaQXBRcnNtcHpkaTRyYjBwSS9zaFpwN0lMNEVxMUZkNG9JZU8yMUdYNFlaeEcwKzl0OE1GbHlGODQxeTFvK0tXcVk0ak91cHhyZFVIUFhERm5tZ3R1VTdzbkkvN1JhWVVMYWtubUJSeloxSEFhWFlURGhmT0VEUld3QjVlS1ZaSmE2NHlzeHZabjdvcFlud0VnSGpJeWthdGZ2eVM0UkpuMFFjOXVQTHoxajh6MXVqakduZnVFcE5JZEhNa2xwNFF1d2crOHZTbGMwZGZ3TnM1VitPTGFQRDFzRDh2MThKQkdLSlNld1VEYmlvRW9uSjF6c0p3ZjZFaWJ1VVVwbXJTVm5sbEhPMHBKM1p5S1htUWh4SDIiLCJtYWMiOiI5OWEyMmMyNTczZjg5YzgzMTA3MjU2ZTEzYmQyMzIxZDg0NDZmNWIzOGYzZTdhNmRiYTAzNmQ3ZTM5ZGVmNWJlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716127550\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-294177921 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294177921\", {\"maxDepth\":0})</script>\n"}}