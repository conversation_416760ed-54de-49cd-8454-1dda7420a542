{"__meta": {"id": "Xbff64fb7be108665f976cdb661719798", "datetime": "2025-06-08 13:33:03", "utime": **********.542488, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389582.232293, "end": **********.542522, "duration": 1.3102290630340576, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749389582.232293, "relative_start": 0, "end": **********.378938, "relative_end": **********.378938, "duration": 1.1466450691223145, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.378963, "relative_start": 1.1466701030731201, "end": **********.542526, "relative_end": 4.0531158447265625e-06, "duration": 0.16356301307678223, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45587680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0079, "accumulated_duration_str": "7.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.466211, "duration": 0.005730000000000001, "duration_str": "5.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.532}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.49754, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.532, "width_percent": 12.658}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5174022, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.19, "width_percent": 14.81}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2046980095 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2046980095\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-7554212 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389576859%7C25%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklDTWVzZEhZUnFjUzJsZVBBOVdyc2c9PSIsInZhbHVlIjoiQndrTDE5MUpBaDdjUldFRFBtSmRoNUNQNER1M1N2MWk3bW1wUk5BUHF6bnNQNnpwcW5Gd1hKKzZBZ2wyVjlWa0NQT1J1RWZ2ekg2Z1VEV1p5R0xMSWdZOWJyV3ZLaXZOT2JrMDVkeGVHTG5GN3B1SFYvR3NxV01GazM1UzVyRHpiNDh1SU1lZllYTmVQNG1KSGIzSGw2aUJoS3RBeTQ4b3dMK3ZVcWtUY3VFK1J3WU4yUTFwZmc5R3JPRHlUY2l5Ri9DeloxQWxQS1pYWFJ6azg2TGVTWG5qY1N0b29vVzJLa0V0aGdkTTUwQXIvdVM0Y0tjSFlvWFMreVhpNFNtNmRuT3VDVGlVVUpKd3hKZHFqZVB6Y2hsUXZ2MlFJTmhQbkJHZTFjNVI2d3ZJUWpTZ0Y5ZjZLQ0FpSlFVYUNpMXB3Qm9yY2x3QWp1TnNXK0g2a2VBZTNrVXFzVVlPOTNFelZXSkgzYXpZNGxMVUdrNHFmNFBSMkVWU0xEMU1zc3FpNlpVV2lTbGRLbGJ3a3RUQStJM211TUdxWCttTzZLZWlpbUpqdEd4ZGozRG44dlF0aWwxcXp1T0V0NlZhcE41YzlVM3c4cmVINHdyTjc5eFZKQkZSN3Q5dG1GOVI4cXQ0ZDUrY1ZmVFBoTndQZmM3SlFJYmF1NEl4cVBnUURFUjMiLCJtYWMiOiI0N2QwZGFlMzEyYmZkYzliNjhmNmNmZWQyZWM2YWMwZjBlZDQwOWM0ZGQwNzUzYzBiYzY4MGNmZjFlZTFhNjIwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFxdDA4WXd4citSZGczRkZMSDBzM0E9PSIsInZhbHVlIjoiUHlmN0kwVjBsUDRkSnpZdjhMQkNvZ2oxUTNiQytUS2picUZ5Tkh5dUlST2x5RHlobksrR0R2bGhUOTJnYkVoK09Tc0ZubDdudXNRR3RMQUR2Q0U2c2VCOVl4OUxQMnhqQUhvSVBXbzI5ZVBnQmNraVdvNEtBdWVIcmc1eXFpL3dGK1NvZklyRVRVMU1TK0RtQytsdDF4SnpCOU9sYWdGaXhxVS8yRStHbVJvQUhGS2owQ1pzelBBWWwzLzFHNlZvS2pFVnN5ZU0velFkM2JMQytFaHdUQStCQTNjMUhvV0N0TzRZek00Y2twbldzcEZ5S0N3aEU1SExucmliTGxwMU9obHlrMHRiL2tzcVJlY3Q1bEhNMGdJNVdSKzhhbjZKaGw0Qk8yQVNwV3dENGg0ZkhqS3gzQWxMSzlYYXExRFAxUUoyMTdFTUdvVU5kemVTa21nc3lid3JYa0lndlZJRktPNUtZMWJ3Y2RHa1ZOSi82OS9va2dtSGFmQ1BwRVlkMktTcytPVFMzWUhtMDhIMTBURkVVek50WjM2UnplT1A2cHBSdHdtNEI5c2RLbXNIVDRmVXl2aVpHc3pGRFc2dmZUdEU0U2ZGWHV5Z3o0L0JQamMvL2tla1NSUjJiZ0JkMFpRb2JZT1RDQlU0WEI5SzhibnBWOGMwWkx1RWs0MzYiLCJtYWMiOiJlNDhkNjMyY2FhZjZkYjdjOTZhNzMxYWU3MzM5MDMyYTBiZmZiZDI0OThhNzdiNjNiNDRmYjM0ZWEyZDdlNWY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7554212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1434697985 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434697985\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-564757790 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:33:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJYNjNQbFppb1BQYXUwZHhsc2RVN3c9PSIsInZhbHVlIjoiN1h2MVFaQlhmOG1lSlFoNU1LSmcyZWZNenV5VHlEUjVwWGJRbVZzQUN3SnFjSDZQbTBFck1ad3B4OVlkMDVHdjltV3FtT1pBTHdDZ2lrbk1VdnlMazRyWHRSR0F0OGZFNHNxVHFVaDlhVWs0QUY5L3N2TExCMjNJKzhxUXI2S280L0F5bzV5clJvZGF0SVlFRkIyQWg3V2JwSzQ5Mm1qSW04ZFFOZE5oQXU4L0NCd1Z0UmtYMzh6Y1F5YmJUQnhTZ0hNc0VzcXZwUXFlNzZOeHh6MmJZOWxhZWJBNlJiSGYwZmRkZ3d5QVFQeUxBV1k5bnRsU3o0d1VBSDcrbmpLa0xEQWdpcXlQaStoYXljVUJSeGdtYVQ5ZFFmNTkrcVo3MHFod0l4NU5ZbHNXZ0x5akdHQXl5Um85VHJQNk9ndG82ZHlobFYvZCthTlpnQXJGclpUKzArUzV3TjVzaDAzZmg2N2pSWVZ0QVlCYU9tVjRleWt4UDRxdGpYQ1NVaE5Yd21lZWlmZlF3c3dlajBsRWxSdHA0akZUNkpoU1BvNTRCbG9ueEpKNlRQTTZFTW4ra0NJRzM4ZDZUcjZ0OStUY0tLODJzTmM3eEFiYUNpU2NZN2ZEUC82MXEwVVhsVjVzMzUwR1pNRk9OTHAwVkFHcCtPTFhtK0t6VVBvTDBGYmQiLCJtYWMiOiI4OWM1ODY2YjI3MDkwNmZkMjExYzJkMGUxOGU3ODNmYzc5YmQ2MDgxYWFmZmViZWZhMGZlNGExMDIxZDZhZDFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:33:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlhMXhsTjdoY1JJeFhXSWgvMU1WN2c9PSIsInZhbHVlIjoiV25MMFMwSWRycmdZcEFnOTZBNjQwTDVWR3oyd0EyaVFNVmlJV1p3VDBEM284UitFdVJ2UGF0aitoaHBzVHVPaDlLNEtlQ0tIRGMrWEVjdlVPSDVjaVBWU2JSSS9MWEdYTFEyT3J5bythQm1MMjJlQnhNL2JGOThoU0pZNGptWmViWFFCeGUzUmI4VnFFc21reFBCZ1JsWHFuaWZ4TnYyWElDTm94czdXRjcrVnpjbUlhTzZBb3FmdUpVN2wrMGs1LzczVmNEOVd1WEZockRKaGpWazhXVjdOdkdLdCtFOWRlS25UeU52RllwS256TzZCV1hRcnZWaldGdjNFMGhLUGVEblZ0V2NoellNeGRkOW9CSXpSUkpIb09PWXRIMnZsOGE5SFZPZVlDZGUwL1Z1MmIyVjVkbXdZZnJTYmRzRGI2em55RnhvbzFHTTM0UzZxcmU5bTUzcXBKV1dLcC94OTViakZEOFJ4MHh1YjR6bnhKRU8zdmFWMmFwMFJUc01KenUydmVYSHZ6bXRDU0I2R2NSa1k3eDNsYk41Rzg4SDZkTy9nSUtCOFdxVGNDQTFzaVd5STBqdldsSVNJUitVZ2JCdCt0L2ZkQXRDaUtIaWYzY1BDZjRNK1dzLzlSalZyaURyNzl6ZFZoT0hRUnAxdmVLQTdZUys5V3B3eCs3ZEwiLCJtYWMiOiIyNTEwNTBlNDU5MTIyOTQxYzBkYzRiNDFmYjJiODg0NzhjZjBjNjZlODRjMDU0YjdkODg5YzQwODEzZTk5ZTg4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:33:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJYNjNQbFppb1BQYXUwZHhsc2RVN3c9PSIsInZhbHVlIjoiN1h2MVFaQlhmOG1lSlFoNU1LSmcyZWZNenV5VHlEUjVwWGJRbVZzQUN3SnFjSDZQbTBFck1ad3B4OVlkMDVHdjltV3FtT1pBTHdDZ2lrbk1VdnlMazRyWHRSR0F0OGZFNHNxVHFVaDlhVWs0QUY5L3N2TExCMjNJKzhxUXI2S280L0F5bzV5clJvZGF0SVlFRkIyQWg3V2JwSzQ5Mm1qSW04ZFFOZE5oQXU4L0NCd1Z0UmtYMzh6Y1F5YmJUQnhTZ0hNc0VzcXZwUXFlNzZOeHh6MmJZOWxhZWJBNlJiSGYwZmRkZ3d5QVFQeUxBV1k5bnRsU3o0d1VBSDcrbmpLa0xEQWdpcXlQaStoYXljVUJSeGdtYVQ5ZFFmNTkrcVo3MHFod0l4NU5ZbHNXZ0x5akdHQXl5Um85VHJQNk9ndG82ZHlobFYvZCthTlpnQXJGclpUKzArUzV3TjVzaDAzZmg2N2pSWVZ0QVlCYU9tVjRleWt4UDRxdGpYQ1NVaE5Yd21lZWlmZlF3c3dlajBsRWxSdHA0akZUNkpoU1BvNTRCbG9ueEpKNlRQTTZFTW4ra0NJRzM4ZDZUcjZ0OStUY0tLODJzTmM3eEFiYUNpU2NZN2ZEUC82MXEwVVhsVjVzMzUwR1pNRk9OTHAwVkFHcCtPTFhtK0t6VVBvTDBGYmQiLCJtYWMiOiI4OWM1ODY2YjI3MDkwNmZkMjExYzJkMGUxOGU3ODNmYzc5YmQ2MDgxYWFmZmViZWZhMGZlNGExMDIxZDZhZDFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:33:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlhMXhsTjdoY1JJeFhXSWgvMU1WN2c9PSIsInZhbHVlIjoiV25MMFMwSWRycmdZcEFnOTZBNjQwTDVWR3oyd0EyaVFNVmlJV1p3VDBEM284UitFdVJ2UGF0aitoaHBzVHVPaDlLNEtlQ0tIRGMrWEVjdlVPSDVjaVBWU2JSSS9MWEdYTFEyT3J5bythQm1MMjJlQnhNL2JGOThoU0pZNGptWmViWFFCeGUzUmI4VnFFc21reFBCZ1JsWHFuaWZ4TnYyWElDTm94czdXRjcrVnpjbUlhTzZBb3FmdUpVN2wrMGs1LzczVmNEOVd1WEZockRKaGpWazhXVjdOdkdLdCtFOWRlS25UeU52RllwS256TzZCV1hRcnZWaldGdjNFMGhLUGVEblZ0V2NoellNeGRkOW9CSXpSUkpIb09PWXRIMnZsOGE5SFZPZVlDZGUwL1Z1MmIyVjVkbXdZZnJTYmRzRGI2em55RnhvbzFHTTM0UzZxcmU5bTUzcXBKV1dLcC94OTViakZEOFJ4MHh1YjR6bnhKRU8zdmFWMmFwMFJUc01KenUydmVYSHZ6bXRDU0I2R2NSa1k3eDNsYk41Rzg4SDZkTy9nSUtCOFdxVGNDQTFzaVd5STBqdldsSVNJUitVZ2JCdCt0L2ZkQXRDaUtIaWYzY1BDZjRNK1dzLzlSalZyaURyNzl6ZFZoT0hRUnAxdmVLQTdZUys5V3B3eCs3ZEwiLCJtYWMiOiIyNTEwNTBlNDU5MTIyOTQxYzBkYzRiNDFmYjJiODg0NzhjZjBjNjZlODRjMDU0YjdkODg5YzQwODEzZTk5ZTg4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:33:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564757790\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}