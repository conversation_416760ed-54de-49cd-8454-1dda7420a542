{"__meta": {"id": "X060902efab56c4b230d02a9380e8383c", "datetime": "2025-06-08 13:27:42", "utime": **********.372348, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389261.06618, "end": **********.372378, "duration": 1.3061981201171875, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749389261.06618, "relative_start": 0, "end": **********.245444, "relative_end": **********.245444, "duration": 1.1792640686035156, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.245464, "relative_start": 1.1792840957641602, "end": **********.372381, "relative_end": 2.86102294921875e-06, "duration": 0.12691688537597656, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43918496, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00625, "accumulated_duration_str": "6.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3371098, "duration": 0.0048200000000000005, "duration_str": "4.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.12}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.350988, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 77.12, "width_percent": 22.88}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-2080646685 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2080646685\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1754959434 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754959434\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1461067557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1461067557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1780456815 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjQ3bmJpRlFNWWErdWhCc3ZheTNxUlE9PSIsInZhbHVlIjoiYnNvU0M3VUpNekpqZWVuZzF2WmFsYU5qTkd4aVJuLzU3YTdLdGRRSHY2aFFzc095UXdFZDNGUnRyTkE5RTd6amZiREpiWFVWQzh4SGJnd09BWlVDMWpYVnhvZDFKUk1zY000dnVCZzdDcHNNUVVHalNTSlAvOXBDYnJIUE55OUlNazFJOTRST1h4eHhsSm5iemtrR1RCTzBVejcwb2VFNDc2UUFKUHB3cHlWdlk5T2pDdEVjM2tZdWdZSVh1U2ZuVHRNVkp1RCtXaUp6c3M2NW9vaWozbWt3dEk1TjlDMXBNekhqbG4ycldMUndqQ2hvSGtyTXh6Y0hJV0tjeUZJR1VPTk5Kejgxa1Zhcmw2RG5rZC9Sa2ovZ0RGVHRLOENqaThMcG50aFhMUldCdGdoSjRzNHNRaFZSb3ZseEhvWkNDRWtMRE5jQlBYam15WU43UVR6d3ZudllmdjNEMGJ5VUVMTVN1SEx6dk5POUZVQkhGOEU4RTlKZThNYzlzQzRzODZ1S1c0bzJsdisxRy9uZTNBdUVVYkg2ZUhjdXNObVBCdS9MVkdiRXk2UEp2K1JkTFR2RjhhRlNxQTJhNUkya1JCMWZXcVd6RVRFaFNHQm1XZ3l2d0poSXNSUmdhd0lTeUU4RUpRUmIyN3FlelB3alVXUFJCdC9jWjVjb3Q3S1UiLCJtYWMiOiIzNmIwYmVjZGQwNDVjMGQ1NjM3M2ExODNiNjAxMjYxMWE3ZDA4M2NlMTdlYzZhNDQ4MzU1ZjI2NzNlNGQzYjg1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhKS2tFdEdicklCWTJkbHVnRUFBOHc9PSIsInZhbHVlIjoidUppNEpKVExJOVZ4T0dVU00vY0lNdmVKVVVFVkxnN3dTZ0E5cFlaVG0ydGZzcFJyMk9zSklLTHRpTktjRFJZYkRvYkZDWG1Yem1qcGRjNGxiVnJiTlZYRGY4NzFzaHUxQ1JjeExnVHNxVytVZWJtZzdIL2EzLzhJb3V2MFhNODh3cEV5VWdmVHlKVjJlSnVRYkZTa3V2UVN5ZExoZmNEcXJqSmZaUHN3TVpJSW5xYUJYajdMMDhVcGFtUWM4M2QyNm9KTnY3ZzlkWVZMUTFoWUhxMERkL0xBVEh5Ymg2K0lNWnFoNk9nWi81US91eEJOMjdqR3RCalZ1SXF3V3pseWU5NEVRZDVheGQ2a1hOUG5NdEdzS1duNHEzZWVHNk5OOElYYWQ0b0tiaUFGTVBjQ05BQVNIY0svL0p0NlBvMDFzc0FDV05zeVh1eEVzdmdhRmJORU1FVUJFRWlUd3VkZjRWMGxydDU0SWl5TkI3V3J4WWJYNVdaVjhGaG5RUVlvMUpWYUpvVEZZYkxtcFlHVG5uNkt3Y21YQXUyeEY0QjVwYm4rM0lDVVYvekh1WUhXbGRzWDU3T1VDWng4TmF0Q2pNV1I0MllVMlVGdGRIdVBreVZqTjF4V1JnWXlWWm9CUEI0bTlSUEJocnVGNDBGaGRxc2dnVmZvWndrWVA1cUwiLCJtYWMiOiJlNmQzZTQ0OWY3ZDBhODMzOTFjMjE5M2RhYWE5NzZhZTg2ODIyNzFiOWMwYWI0ZTk5YTdjNWJhZjMwMDBlOGQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780456815\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-139171734 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139171734\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2108015795 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpwNmFBVTRiMHhuM2pBckduQlFrUGc9PSIsInZhbHVlIjoibjFWNUxHT2J6WWp4NDQ3WjlhOTF4QnlLOEZ0cElsQlFFajQzek0xRTZ2Smoya3J2N25ZbDJ6aE9iaWpsNmREbWlEeHJUVHZ4RUgvR2xGeHNzVDdUc29vYklJL2FsNU1pQlZRMDllZkNCdE10cXhuSzliTEVKVUUwQlVXUk0wbHIvUEtKZmNzdEdxMGZSS3BIS3V0RTdIS1UwVmF3UlVHQklaTWdpcnFPVFc1bmQ4cGZWWVFrSHZKS2J4MGl3RDZHU0ovU2RiRW1YQ1dnZ2YvUCtvQUJWQlhrNXJxRjB2SUlQdGxvV2lWZm1HcjBhTkl4N0VyeUVKdnhUTmhZZGRwUWhGZDRWSE05Wmc2SGV6elNQeGdIQnc1MkpoTmNtbTF0Sm5CalM4YnZ1MHNaMHdhcUl6RkRqUjdVbC9VbDZEUm1hWDVQcjFNdmZmb01Kd0o1TVRpdElobDI5V2hLZXJBMCt2MWE1M3F2TEJrSGt0NVF2TEswak9SL2h4aVd1TDdVRDU1Z25EaDlJYXB6TG0xR1dUSExQYU41TnVNRnNUV3lCYTZDYUM1VG04bHhkRFd0bm9zaS9uWVBTTDBpUGNlUWNMd0lMTm5rWXViTEpTdTdZVDVVendqaG1VWFg3czZGenRQZWllb1k3aENhQ2RxYzdmOHRiWU1YL1p6L0ZrSW8iLCJtYWMiOiJhZTI1MjMxYjFkN2QwNDMzZjFmYzFlNDcwMDI1OTg1NmExODZjMjQ3ZGE1ODY0OWY3Y2VkODZkNGEyZmRlNDc3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZRelRhOHR4SWFXak8zV0xDQXZmQ1E9PSIsInZhbHVlIjoiZXQ2MVF6eVlkcVBIVGdxMExOUXhxZHhITWhxbk1QZE5aeEhWOFQ1OVRUSWwwQzdZYXJiSVRDbXBBYnRkNWpGOThpWG1hbXFDQmF2NHJvcUpSemxHa3VsbXpqRzhNdHM0c3ZiMjlBMnNGc282bEcyTVgxQVYyZXlPakRjZ0F4bnpGcGlubHRlUlhTTHlzMDllNFV6ak8vSGF2dklLUGkybGZMei82UmJrVFVyS0dHVytKdngwbFZLNG5nZzZJbGQwa0gzYWs1YTZOdStUc2EzamZHTjRMSm1lL1ZGcVFOaUtzTURsL2NEWWhwVnZpTCtrREZoZGF3WUFzejlxTDI5MVM2REk4UERpTlhUKzRhY3NPNG1DbjlPVHFzaUs2TEJoenQycjkwTmZFNERSOGJJNzhDUGN0TVdyUEwwcDBKWkg0M00ydFJXMmFzVTN5V2l4OXovMzIwTjFiU1BpUUt2RFJxN2U5WnNFb2MreXVXaHFjQXVEQXd5bW96Umt6aHpKQlU2eGRDeEhkaHVHL3pMbHFQc1E1OWR6OVFlZDd1Mkd2Z0ZMaGN4NFhWWkpyYmkyTE9vNmRBM1NRM0lFWWhWVkt3dDN0anBpYUdjWlpxejFxbkRaKysvY0t1UGZPUVY4aE5UWmpEQXgzWGI0UnJDYXl1RDFoYTlnb3hBdE1pSGUiLCJtYWMiOiI3NDE4ZjIyZjVkYTc0OGRjMjQ2NTFhMzllMzI1YmMwZDM4ZmQ0MDllNWQzYzY3YThlNTYyMWQzMWZlODFmZDQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpwNmFBVTRiMHhuM2pBckduQlFrUGc9PSIsInZhbHVlIjoibjFWNUxHT2J6WWp4NDQ3WjlhOTF4QnlLOEZ0cElsQlFFajQzek0xRTZ2Smoya3J2N25ZbDJ6aE9iaWpsNmREbWlEeHJUVHZ4RUgvR2xGeHNzVDdUc29vYklJL2FsNU1pQlZRMDllZkNCdE10cXhuSzliTEVKVUUwQlVXUk0wbHIvUEtKZmNzdEdxMGZSS3BIS3V0RTdIS1UwVmF3UlVHQklaTWdpcnFPVFc1bmQ4cGZWWVFrSHZKS2J4MGl3RDZHU0ovU2RiRW1YQ1dnZ2YvUCtvQUJWQlhrNXJxRjB2SUlQdGxvV2lWZm1HcjBhTkl4N0VyeUVKdnhUTmhZZGRwUWhGZDRWSE05Wmc2SGV6elNQeGdIQnc1MkpoTmNtbTF0Sm5CalM4YnZ1MHNaMHdhcUl6RkRqUjdVbC9VbDZEUm1hWDVQcjFNdmZmb01Kd0o1TVRpdElobDI5V2hLZXJBMCt2MWE1M3F2TEJrSGt0NVF2TEswak9SL2h4aVd1TDdVRDU1Z25EaDlJYXB6TG0xR1dUSExQYU41TnVNRnNUV3lCYTZDYUM1VG04bHhkRFd0bm9zaS9uWVBTTDBpUGNlUWNMd0lMTm5rWXViTEpTdTdZVDVVendqaG1VWFg3czZGenRQZWllb1k3aENhQ2RxYzdmOHRiWU1YL1p6L0ZrSW8iLCJtYWMiOiJhZTI1MjMxYjFkN2QwNDMzZjFmYzFlNDcwMDI1OTg1NmExODZjMjQ3ZGE1ODY0OWY3Y2VkODZkNGEyZmRlNDc3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZRelRhOHR4SWFXak8zV0xDQXZmQ1E9PSIsInZhbHVlIjoiZXQ2MVF6eVlkcVBIVGdxMExOUXhxZHhITWhxbk1QZE5aeEhWOFQ1OVRUSWwwQzdZYXJiSVRDbXBBYnRkNWpGOThpWG1hbXFDQmF2NHJvcUpSemxHa3VsbXpqRzhNdHM0c3ZiMjlBMnNGc282bEcyTVgxQVYyZXlPakRjZ0F4bnpGcGlubHRlUlhTTHlzMDllNFV6ak8vSGF2dklLUGkybGZMei82UmJrVFVyS0dHVytKdngwbFZLNG5nZzZJbGQwa0gzYWs1YTZOdStUc2EzamZHTjRMSm1lL1ZGcVFOaUtzTURsL2NEWWhwVnZpTCtrREZoZGF3WUFzejlxTDI5MVM2REk4UERpTlhUKzRhY3NPNG1DbjlPVHFzaUs2TEJoenQycjkwTmZFNERSOGJJNzhDUGN0TVdyUEwwcDBKWkg0M00ydFJXMmFzVTN5V2l4OXovMzIwTjFiU1BpUUt2RFJxN2U5WnNFb2MreXVXaHFjQXVEQXd5bW96Umt6aHpKQlU2eGRDeEhkaHVHL3pMbHFQc1E1OWR6OVFlZDd1Mkd2Z0ZMaGN4NFhWWkpyYmkyTE9vNmRBM1NRM0lFWWhWVkt3dDN0anBpYUdjWlpxejFxbkRaKysvY0t1UGZPUVY4aE5UWmpEQXgzWGI0UnJDYXl1RDFoYTlnb3hBdE1pSGUiLCJtYWMiOiI3NDE4ZjIyZjVkYTc0OGRjMjQ2NTFhMzllMzI1YmMwZDM4ZmQ0MDllNWQzYzY3YThlNTYyMWQzMWZlODFmZDQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108015795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1389606182 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389606182\", {\"maxDepth\":0})</script>\n"}}