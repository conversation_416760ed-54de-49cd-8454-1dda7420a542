{"__meta": {"id": "Xc685517bb66a255a94ae87f500c7fc1f", "datetime": "2025-06-08 13:05:39", "utime": **********.269035, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387937.925216, "end": **********.269067, "duration": 1.343851089477539, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749387937.925216, "relative_start": 0, "end": **********.100455, "relative_end": **********.100455, "duration": 1.175239086151123, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.100475, "relative_start": 1.1752591133117676, "end": **********.269071, "relative_end": 4.0531158447265625e-06, "duration": 0.1685960292816162, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02581, "accumulated_duration_str": "25.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.19367, "duration": 0.02334, "duration_str": "23.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.43}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.24078, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.43, "width_percent": 4.572}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.250309, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.002, "width_percent": 4.998}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-279084969 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-279084969\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-324816265 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324816265\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-501139009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-501139009\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1193879914 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVDelRzK1FOWnIzaXV1NW1CdDM5Znc9PSIsInZhbHVlIjoiaTZGQUJCSytJdlAyNWtOZVlnSEVzQ0RETEJpNzZhb1UyUjJ6UXhVWHRWNlJLSG9MTzBaNmJReVZWQTNZeFUzbVJiNHNFSFhqTS82dWF5UVBzS0tHME5rc3FkWnpyS1pMNkxrNk0xOEpWajczZmxjZlBpQ2VFeTltQTV5Q3h2aFY3alhNSmtnZHd2M2RSSDZGMW92ZjFRUUpUZ2NnckVFbjBEc0QyZzJGKzE4SHlXU0FOUGlhNFU3Zm1WTU9ITVVJcW9BN0pueWROWmlZb25XMHhLMGJDemVBVzhSZEFKNnJBbllmZHBIcVF5SnJhQlhidzMvSitDcTdwVzUxSEt2QVptSXZLQUt5Y0RseWRBWFhnNnZXSzF3aUdFYUl6Z2o4UWhPU3orYTR3dkZ3NGJsSFJ1K0VvODFnWnNadko5WGxRYUVDZE5PQ3Rodmcra0xnVEFLVkdNcVhXSGVkVkpUcmwyaGlhYVBiM3Z1ckdqUjVzU0xtNjlKOUJlS3J4eDV6SWdZbG9mQTRRMWcyMk1Lb3hYK3RxS3JKcm01Y1MwVnV6UGY5YURYdHJaR0tDbEM5KzQyTEcwM0ZINHoydFY1Snl5T21pVWxKblVFQ3hleExTUjBMUUZTOUVvYmEvUGREdjk3SkE2QUdEa2oxSnUrRXA4amlMU1BWeVNMOWVSWE8iLCJtYWMiOiIwNDM3MjJlNDdhYmU0NWFkOGRkNzUxYTVjYjUxOTVhMGNiODM0ZGUwYzExMDIwMjUxMjdmNGU3NzhmZmMwODhmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJUMExOWkNpZTF3MWdjQ09RN3ZYQXc9PSIsInZhbHVlIjoiS3JaRzZ3Z2V0M25UQWdxRmgvTTdlamFYY0srQ3ZGQUJ6YkUyTkxNU2FrYkYxOXE3cSs0Vm9seDlDQUI0OFFKeWcwSDlqdmc4T3pSbUxZUFUrcXFoR2lFYlYzbHJ3UXNURzBzY3VVczFoOU8rNjdkSElWMDR1S3JoOEp5dXU5ZEc3amdUNW8zcnhxTjJlaVNKUWwxQmtKZmJBRE9QRmc1RmM5TFgyNVM2dXlTUG9uaEE3cEFaeUZYVEZRc2UrWm8xUmFtMWZGeFprczJ0SUVSMy9CclJOK2lMbGNoR1JUSkpGMGtmVnE5SzJGb0NOS2hmZWlrRmV6NXZLNjh0Rys5Y3pjem9wNWNnVDU3V0E2amI4clVuQ0hiRVplS1NVeENmVTBITEI2dnpTZ2F5SlJObG1VOVprdHNDZEZGMzdXWHVGcjh2UURlVC9jcWQwZUphTExYZ1ZMUDFQalk0SWFEVlFOZGZXaFdGL0N0dnF2Q0hKL3hVTGZnMXVJVFIzNW5Hd1NSNUZLYTNDbVBuUDZuR1prdC9mUko5MXptYnBLQTduTkx4K3pIblhZcVhtbjB1Rkp2d1FzdmxVNXRmM24xNFJHVFROWUhPZ1RyVmRnMkwwMlRUMGtaTXVKUkpRM2hqR1JVYVBrNkU3ZFkyQitWR1JGdVV2ZHQ5N2wyaXlXOGciLCJtYWMiOiI3OGQxNGJkZTMxZmNlZjcwYzMwNTUxNzc5Mjc3NzJlMmM5ZjYzZWE0NzI0YTcyMTljNmQ2Y2FjNDM3Y2U3YTEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193879914\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1415448737 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415448737\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1925224322 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVxNlBhUkJoQ2pwaTZOVmNoa3l1MlE9PSIsInZhbHVlIjoiOFVIYzFTb3hJTzFWVm8yNjN4L0xvTkhDWHJLL25NWmoxRS9iRXo3WnMzVXg3T1ZlZlhiL2ZoNU9QdTVxZEpXMjhidkZPNm16WVJpcCtON1hBWktoOWZZWWNuUGJpUUc2UnlhVGwrMVJsRHA0TmtCTm5aMVVMNUpJOURiUXNWcDYxamlHWUhFQk94Rk9DcjZkdy85L09QYk1VYnVyaXBtV0dKYzVzd3pBUHJ3c1VuQTZhMmw0YUVxd3VMa0xYQkhCNE9BV2xtNGdUK1hsRlRmTTN0V3pPOFRkeHU2NnhCdUQrMVFqbGpRYjkrWllmZ2RVKzhIY3hNcXdWcHljbUtmWFVsbzFDczk5Ky9tSWFXNWhGODh0RnJ1Qml4dldGSnU4QU9UMFBJVFgxVXVCa2JwZkpEcWo2ZTdHbEV6aGJMZGhpQWtQR00xUm9vQjdXVlp5T0xuTVFoOU1Gb2xreDdCYTJEcHlPaFgwWmJZWHRSNnBTRlBGVDNlY2VQN1phNGdZNnZQbktiZVN0MXhmNG1TSWlFMHhySVk4czQ2Vnc0dTZnNlFnZGViNHA2NVlURVpOZ3FFZ3IyMHRONk4yc1hMckg1ZXZ4UlhIKzVoK0VKQUVFYTNoT1F0dU1NWTVXZDZVQ2VlZ29uaVJ2TWM2Q2JHOG9EN3FINTkzZkowMkNKWGciLCJtYWMiOiJhZDY0MjM2NTgxODM0YWIwNGJiNGM2ZTk2NDk5NWZiZWY5MjljOGQzNzIzNGM0MDE3NTdjMmI4MzE2YmFlMDNmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBYRlZCTG12VHpYa3FHeXdkanNrU2c9PSIsInZhbHVlIjoiY1NCOWlsc0hrbE9YNmxUYkNmcE9GazIvQUpPQ0tXMXdQZnZ0ekJNOHJuMjF1bHNzdGdnS0V5VUtyOW96N2lqOWRJcm5iQWdZNUt3UFJKY1F4YU5oVW8xUHNkZVhKWGx5VGlRT05kbGNMWURvSFpQM3pJSFFudnd4U01uU0ZGaEZoZUV3N3UvQzMrdHFDY3lLc3FNc0U4NnU3Skc0ZVBpZ1pzZ2lNVGFzekM1SlFneE1mTEtTRFV2ektCcU9qYlpLZTVNQy9DMGlSY0xyTThySTloZC8zaFFWUmk1SE5UV1RpdS82ckFobDczT1BDSkNRTHRmK3l1WjdGYjNmaXRXRUc2Tkl4Nk5qOXFzRjFRSWx0VXBrc1h4Ym1LK3dndnhXNFNBamNsTjN4MGpOQ0RRdk5FMUNUQXB2NGtpOVFQNGlBVUxONzRBR2d3TlRCTDZmSjE0TTNOWTEwUVY0NWpraEhabzZndFJERHZVK3RPY2dHWHl6Zjlab3lNQW44U2l4TWNKdUpDVWtWUGZlbWJIdUhYNEZkTWJCSDByaFVaY1NveEJXS2RqM291Y3hmby9iMDBFUFdpbTZZUXo0a0l6UEFlem1MTVVBdzFWNnZuMFMwTFUwV3UvM1lPTC93cXkyaG1pMlR0Y3FyYjgxV25TM1lNNUpwU1V1S3BqbG16THIiLCJtYWMiOiJhZmE2Mjc5MTlhZjQzNWU1MTY2ZDNjODQwZDFiZjlkOGFmYWIzYzYyZGY0OTFkYjZiZTk5MjM5NGI5NmYyODUyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVxNlBhUkJoQ2pwaTZOVmNoa3l1MlE9PSIsInZhbHVlIjoiOFVIYzFTb3hJTzFWVm8yNjN4L0xvTkhDWHJLL25NWmoxRS9iRXo3WnMzVXg3T1ZlZlhiL2ZoNU9QdTVxZEpXMjhidkZPNm16WVJpcCtON1hBWktoOWZZWWNuUGJpUUc2UnlhVGwrMVJsRHA0TmtCTm5aMVVMNUpJOURiUXNWcDYxamlHWUhFQk94Rk9DcjZkdy85L09QYk1VYnVyaXBtV0dKYzVzd3pBUHJ3c1VuQTZhMmw0YUVxd3VMa0xYQkhCNE9BV2xtNGdUK1hsRlRmTTN0V3pPOFRkeHU2NnhCdUQrMVFqbGpRYjkrWllmZ2RVKzhIY3hNcXdWcHljbUtmWFVsbzFDczk5Ky9tSWFXNWhGODh0RnJ1Qml4dldGSnU4QU9UMFBJVFgxVXVCa2JwZkpEcWo2ZTdHbEV6aGJMZGhpQWtQR00xUm9vQjdXVlp5T0xuTVFoOU1Gb2xreDdCYTJEcHlPaFgwWmJZWHRSNnBTRlBGVDNlY2VQN1phNGdZNnZQbktiZVN0MXhmNG1TSWlFMHhySVk4czQ2Vnc0dTZnNlFnZGViNHA2NVlURVpOZ3FFZ3IyMHRONk4yc1hMckg1ZXZ4UlhIKzVoK0VKQUVFYTNoT1F0dU1NWTVXZDZVQ2VlZ29uaVJ2TWM2Q2JHOG9EN3FINTkzZkowMkNKWGciLCJtYWMiOiJhZDY0MjM2NTgxODM0YWIwNGJiNGM2ZTk2NDk5NWZiZWY5MjljOGQzNzIzNGM0MDE3NTdjMmI4MzE2YmFlMDNmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBYRlZCTG12VHpYa3FHeXdkanNrU2c9PSIsInZhbHVlIjoiY1NCOWlsc0hrbE9YNmxUYkNmcE9GazIvQUpPQ0tXMXdQZnZ0ekJNOHJuMjF1bHNzdGdnS0V5VUtyOW96N2lqOWRJcm5iQWdZNUt3UFJKY1F4YU5oVW8xUHNkZVhKWGx5VGlRT05kbGNMWURvSFpQM3pJSFFudnd4U01uU0ZGaEZoZUV3N3UvQzMrdHFDY3lLc3FNc0U4NnU3Skc0ZVBpZ1pzZ2lNVGFzekM1SlFneE1mTEtTRFV2ektCcU9qYlpLZTVNQy9DMGlSY0xyTThySTloZC8zaFFWUmk1SE5UV1RpdS82ckFobDczT1BDSkNRTHRmK3l1WjdGYjNmaXRXRUc2Tkl4Nk5qOXFzRjFRSWx0VXBrc1h4Ym1LK3dndnhXNFNBamNsTjN4MGpOQ0RRdk5FMUNUQXB2NGtpOVFQNGlBVUxONzRBR2d3TlRCTDZmSjE0TTNOWTEwUVY0NWpraEhabzZndFJERHZVK3RPY2dHWHl6Zjlab3lNQW44U2l4TWNKdUpDVWtWUGZlbWJIdUhYNEZkTWJCSDByaFVaY1NveEJXS2RqM291Y3hmby9iMDBFUFdpbTZZUXo0a0l6UEFlem1MTVVBdzFWNnZuMFMwTFUwV3UvM1lPTC93cXkyaG1pMlR0Y3FyYjgxV25TM1lNNUpwU1V1S3BqbG16THIiLCJtYWMiOiJhZmE2Mjc5MTlhZjQzNWU1MTY2ZDNjODQwZDFiZjlkOGFmYWIzYzYyZGY0OTFkYjZiZTk5MjM5NGI5NmYyODUyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925224322\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-799210023 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799210023\", {\"maxDepth\":0})</script>\n"}}