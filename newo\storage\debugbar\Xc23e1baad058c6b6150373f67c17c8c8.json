{"__meta": {"id": "Xc23e1baad058c6b6150373f67c17c8c8", "datetime": "2025-06-08 14:52:20", "utime": **********.589211, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394339.943836, "end": **********.589235, "duration": 0.6453990936279297, "duration_str": "645ms", "measures": [{"label": "Booting", "start": 1749394339.943836, "relative_start": 0, "end": **********.510981, "relative_end": **********.510981, "duration": 0.5671451091766357, "duration_str": "567ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.510994, "relative_start": 0.5671579837799072, "end": **********.589238, "relative_end": 2.86102294921875e-06, "duration": 0.07824397087097168, "duration_str": "78.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43933360, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01772, "accumulated_duration_str": "17.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.555086, "duration": 0.01656, "duration_str": "16.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.454}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.576502, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 93.454, "width_percent": 6.546}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1434857760 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1434857760\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1463789001 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463789001\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-455350930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-455350930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-647293747 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394122823%7C63%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdVRDhhekZqMG5ZZEI2elhxT0xsSnc9PSIsInZhbHVlIjoiL3QrZzBLaVozL2xlM0thVWFGbUt2bUVZYVY0aCtmVElFOThyeGk0RnEwYmFuK3dhbWtOeUNRZno1dXhiTWJXRnY3Tzg3NEhrekswMWlua0Y2c3JiSVVLSGZ0NndJVHE3ZENObXB4aWg2QUFzMGh3bnYzZU9acXhKa2NJZHZ4OGp1clhzdUM3c2t3OUZyRDdpMXRjUFRmTEFUTTdHSmx3czlPQmt0M0dOSzFESGNLT1V3dUcwQVRUb3NveExpdUNqaVlVbDg5Zy95ZlR5aVl1NmlTYng3U1M3UmJkQmtUYTBsOFZiTnFSOFZGbEZkekhSMTVvSjBRY3M3MGFkSElwQkVvWkd0amhrV2Z3Vy9WalBLbnBYUHVZc043aEszVjdlMjJCTXZJdGZDdzNZVlpuTndQSUovWmdGOXY0OVMxbVhqSEFtNnUrbkJ5N0xxKzJFODNtR0VvVDVPaGF2dE9DRkovWXJPakJJMFllWGJINFl4VmQraXMrb2d0ejMySlVYL0EzNEVDVytIbjJZR3p5bk95YXh0YXZkNDA2T28yclV5LzZVUFVpc2V4cmp6L0grWnRDWTJmNmFsT3dnYkNva2dkV2NRZ2p1cnBJVWRRc3NyQmZTWm96dHlvbDdXd1NSUXkraElpUmdFL1Fua1ZyK3FldUViS1VPVzM3UmlPRlgiLCJtYWMiOiJmMjY2YjVmODhjNzBmN2YxY2VlZWIyOTcyNWQxMjk5M2I2NWU2NGRiMDVmOTUxZDI0MDAzZjhjNGY2MTUzYzZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imtla2hnYXZraVloU0Jkc2VPS0tONlE9PSIsInZhbHVlIjoiYWlEWEJxSWVOaTVyK2lwcEJJZmRUMHNWb0FHeUlIY2MwNmQ0eGNGODVGalR4eWh3bWVqU0o5OC9pbS96cnVrKzdqenQ0MmRZMDZHNWx0SzNKSmJwUmE2M0dOQlBnT3ZKcXV3UWVHRzd3THZzaDVGSXRHUWJ1Q0M0ZnVMRmRDVDViMXdpeGRlcEYyc1EwU1FhaDdnWUMrdGt0THlZT0ZCejQxcklkZDVrL3pHTndyUWpXWTVicnpZM2ZnRzlWSE0xTlkwSzJTUkwvc2dJaXFzVkVaTmg3WGY1dE5Ma0phNWVtSEdSR2dLMUVhUVJDQkQyVVRRaFl3d3J1M2pDWGZtOHo0cys5UkIyVlNTZlhRcEFtTE1YRk9MNXRqY0F1YTJYSmlSVnZaaWlGYkZXNnhXRFkwWjQrTml0MTlLRVFlaDFOdi9nR0R5V2VQQVd5TkQzL0piMFlqTUZxaHF4VzJpUGgzbU9ndnJNMFcrbHRNQ0lnQ1VQOWxOVjRPU2FabXJFOFkyR2ZqYXB5QkVVaElIT0hTSGZnM2FLdTZ6cHZLc0d3eklKNXNLalBFMm96VklLMHBua2pITUl6REo4L2d5K2p2R2lyOGVhc3NIeTErMkpKdEFLclFpcHlqemJTZlRvYmJ6T05MR3pBaE0zMmxYbWJjMkNUN2NoVWpheG9OUUIiLCJtYWMiOiI2MjljYWM2ZTY5Y2QzOWZiNTQzODBjZjJiYTkyYzgyMDU5N2EwMzRhMzRhZjRkZTE1N2RmZjJiNDA4OGFjMjBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647293747\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-65522457 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65522457\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-600553266 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZBZGVETnRFRVpUOGlRNW83VFNIUkE9PSIsInZhbHVlIjoiMVZOVkp2ZmgzVStuZWRsd3NSRHAxcDdoZHk1ZWFPNWVmWVFXU1hmcGdDN0xyOWZBZTR1VUNZamc1eUp5U0xJMGFLN3lxZTVkcEYrUEJnSmxHa0ZJWE9pSld5NDIwZzdORTZGdWpXZlhrNFZIcUZvRTgrNlplR010cEVaajRkSkdzc01QUUU1ZW1HVzdCZnZ0M05kMmtUcm1IbnBLQk1SbTJQVjNGUVMzOHFBWVNhdDh3ajd2TGFtR1RNZHpYTTY4OXJyU1RZaDRIcy81MG51SXppTWxNUmRLYlNsWDJTeXBUQ0JablNrZ01TKzJBKzN6Z3FYRmxTNUtWclR1TWV0aWhZUFNENnl0ZGJGUFZHUFNoM2xtNjk1UWRIaFFEUkhjTHdGSWhRM3lSRitHMHIzTGhNUzQ4TzEzZGN4UHg0N205K3JMSjE3TXl6ZG51TG5lK2pYYll0VW91MVJML3pVM2czbiswRnFmajN4VDhzZUpZUFIxSW13NzIyaFB5RXVIMi92S2NGVDltdHhZMVNLYUE3M2E4MktiWnpEVitRRFlvTGovKy9pQjhreklsZHNFTTloZTJUZVdSRWtGV1B1ajFuNG9veDlTaHFpcHMrRlE5MWhKd05GbDA2RHFvSzlHUW9YNjh1akVGSGljd2l1c1RFN1prT05rNjZqWWF5RU0iLCJtYWMiOiJjNjQ1M2ZmZGQxMDQwYTBhMWM4ZjQ0YWNjM2VlMjQyNmYxYzYxNDNhYzdkNDYzMDY1YWEwNTZhN2MxNDBjNmMyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJUM0ozMm9lc2dMWE1TeE45cElpRUE9PSIsInZhbHVlIjoiRU52MU9HQlNZRjdPa1N5WE8wSW9PQkVjWVdNSzJ1SHRKTCt1MjVMc0dITVNGQ285NnMvMkF3cDVBeDFEOGI3SEtpS2tsK2o3WkVRQ0FBN29ISmEyY1pLVnpiZlIra0llWWhCYmVoVzRLbjNGUmkrczB2QlVkb09NVllaNnFDL2dwUENHUENXQnRCQWtUTEUvZmJYVU9kV1R6QTZJb3JOb3FVU2lBZVpad1JlYWdoc0VmbUNvL3VWaC9VMU56S2VVUWFWRklIUGxqZGc5ME90SnludEtERnRPQVdHNmt4ZENPOEVsRWNRUG56T1lyQU5vdm0xWUpNWHdCMUE2a3c5U2ZoRW1mZU9RaWw1ckw5RERkbEE3Z0U0MjYxY0hWdEJycXpYYXJIMmVuYVRpcHdkMFBuMzMxbFBHYUh3YS81UXJ0Y2pMS2ZmTkFVUnZNdFB1ZUpnaEJuZC9GbEhRbW9sbnNEMHUrdlFLOHpSazZzZWZjM1JETlZnRHEwS01wUFppQ0c2Q1M0bWpDYVUwaWFXUXFGNHZlblcreE1MbVFyc0EwWnkxRllOYVpoOHBtKzgrTnk4MkNBdWttejdTV0hpMGk0dllKUXJOWHVZSzI5N0EzZjR2QWVvcTVjRjlWb0QvR2IrczZkdk80RzF3cnB3UEtWVGowSi90NU9PQitObEwiLCJtYWMiOiJkZDA3YTVhY2ZiOGJiOTEyMjVkZWFlOGMxOTczOGJkOGZiZTU2ZjQ1NDk2Mzc5ZWZiZDliNzY4MDZhMTJmMGRiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZBZGVETnRFRVpUOGlRNW83VFNIUkE9PSIsInZhbHVlIjoiMVZOVkp2ZmgzVStuZWRsd3NSRHAxcDdoZHk1ZWFPNWVmWVFXU1hmcGdDN0xyOWZBZTR1VUNZamc1eUp5U0xJMGFLN3lxZTVkcEYrUEJnSmxHa0ZJWE9pSld5NDIwZzdORTZGdWpXZlhrNFZIcUZvRTgrNlplR010cEVaajRkSkdzc01QUUU1ZW1HVzdCZnZ0M05kMmtUcm1IbnBLQk1SbTJQVjNGUVMzOHFBWVNhdDh3ajd2TGFtR1RNZHpYTTY4OXJyU1RZaDRIcy81MG51SXppTWxNUmRLYlNsWDJTeXBUQ0JablNrZ01TKzJBKzN6Z3FYRmxTNUtWclR1TWV0aWhZUFNENnl0ZGJGUFZHUFNoM2xtNjk1UWRIaFFEUkhjTHdGSWhRM3lSRitHMHIzTGhNUzQ4TzEzZGN4UHg0N205K3JMSjE3TXl6ZG51TG5lK2pYYll0VW91MVJML3pVM2czbiswRnFmajN4VDhzZUpZUFIxSW13NzIyaFB5RXVIMi92S2NGVDltdHhZMVNLYUE3M2E4MktiWnpEVitRRFlvTGovKy9pQjhreklsZHNFTTloZTJUZVdSRWtGV1B1ajFuNG9veDlTaHFpcHMrRlE5MWhKd05GbDA2RHFvSzlHUW9YNjh1akVGSGljd2l1c1RFN1prT05rNjZqWWF5RU0iLCJtYWMiOiJjNjQ1M2ZmZGQxMDQwYTBhMWM4ZjQ0YWNjM2VlMjQyNmYxYzYxNDNhYzdkNDYzMDY1YWEwNTZhN2MxNDBjNmMyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJUM0ozMm9lc2dMWE1TeE45cElpRUE9PSIsInZhbHVlIjoiRU52MU9HQlNZRjdPa1N5WE8wSW9PQkVjWVdNSzJ1SHRKTCt1MjVMc0dITVNGQ285NnMvMkF3cDVBeDFEOGI3SEtpS2tsK2o3WkVRQ0FBN29ISmEyY1pLVnpiZlIra0llWWhCYmVoVzRLbjNGUmkrczB2QlVkb09NVllaNnFDL2dwUENHUENXQnRCQWtUTEUvZmJYVU9kV1R6QTZJb3JOb3FVU2lBZVpad1JlYWdoc0VmbUNvL3VWaC9VMU56S2VVUWFWRklIUGxqZGc5ME90SnludEtERnRPQVdHNmt4ZENPOEVsRWNRUG56T1lyQU5vdm0xWUpNWHdCMUE2a3c5U2ZoRW1mZU9RaWw1ckw5RERkbEE3Z0U0MjYxY0hWdEJycXpYYXJIMmVuYVRpcHdkMFBuMzMxbFBHYUh3YS81UXJ0Y2pMS2ZmTkFVUnZNdFB1ZUpnaEJuZC9GbEhRbW9sbnNEMHUrdlFLOHpSazZzZWZjM1JETlZnRHEwS01wUFppQ0c2Q1M0bWpDYVUwaWFXUXFGNHZlblcreE1MbVFyc0EwWnkxRllOYVpoOHBtKzgrTnk4MkNBdWttejdTV0hpMGk0dllKUXJOWHVZSzI5N0EzZjR2QWVvcTVjRjlWb0QvR2IrczZkdk80RzF3cnB3UEtWVGowSi90NU9PQitObEwiLCJtYWMiOiJkZDA3YTVhY2ZiOGJiOTEyMjVkZWFlOGMxOTczOGJkOGZiZTU2ZjQ1NDk2Mzc5ZWZiZDliNzY4MDZhMTJmMGRiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600553266\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-229230791 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229230791\", {\"maxDepth\":0})</script>\n"}}