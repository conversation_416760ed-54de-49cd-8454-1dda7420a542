{"__meta": {"id": "Xea4c6b3d1f29a49c65d929bc35aadf8b", "datetime": "2025-06-08 13:27:18", "utime": **********.084476, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389236.707689, "end": **********.084506, "duration": 1.376816987991333, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749389236.707689, "relative_start": 0, "end": **********.865868, "relative_end": **********.865868, "duration": 1.1581790447235107, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.865895, "relative_start": 1.1582059860229492, "end": **********.08451, "relative_end": 4.0531158447265625e-06, "duration": 0.21861505508422852, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50877896, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02692, "accumulated_duration_str": "26.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.969867, "duration": 0.01768, "duration_str": "17.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.676}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.014918, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.676, "width_percent": 4.495}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-08 13:27:18', `closed_by` = 16, `shifts`.`updated_at` = '2025-06-08 13:27:18' where `id` = '1' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-08 13:27:18", "16", "2025-06-08 13:27:18", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.047402, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "ty", "start_percent": 70.171, "width_percent": 16.568}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-08 13:27:18' where `id` = 16", "type": "query", "params": [], "bindings": ["1", "2025-06-08 13:27:18", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0576699, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "ty", "start_percent": 86.738, "width_percent": 13.262}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "success": "Shift Closed Successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-1511620726 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1511620726\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1636809694 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1636809694\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-344778059 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImNYdmI5TEdxVGNyT2VGVUdBU0dZM3c9PSIsInZhbHVlIjoiNXZGOWozeWsyS3QrbTVBS2xpT3RjYUlXdFFZR1g0ZkJlVjJMSlB5UzU5bFZrVUtNV2R3cU82T3pMYmxpZGUrY2dNTWVEK0VrbE0zZGFmR01xM0ppd0IrQnV5U2ViNDNuZUxOb1Y3L1IxbjRsejF3NnZmbENhRGVWNnduKzZrRitzV2poZW1aM3d5YmFoRmpkUlhKbnI5Z004R2s3NW1OemVTNFR2VktBaXVxelZnMFRJd21ramhvNUZBZmZscWd1Nyt2Y3dEd1FGZGJ0bVptSUxVeUpTZ3VFU0xqd21RTHEyazROdmFxSnAzbXFOZGxhNUVIb2pJdGhxc2ZSRjhrQ1NDdzF3aWxHU2trSWMwYzZhOXBaYUkyZDdkRnVLUkhKM3NrRURIMlM1d1JsTVFwVHUvWnlnVVIvQnEwTEQ4Sm1HckVYWFIrTHJmUEM3ZFlRWmgyNUc4TlV2NlQ5akZlckRncDBtL3A4cGcwRFJMdVNWY0c2cDdwWUE5S2UyaDJMYmR4N1U0amcvUEw4UExUeWdFWURSWWxPNVlrTHRoVmU1ZVVkbExRQWxpN1JFQWpob2tZbXR0cXZENllHbnlZcHFiRlJTME1WZ1pqSG5HNXNReklzQnZWZ3UveDdhcG8yb0J4bHVGODNqK3p0cUhnUTVxaUpZZGZ3UTNBblJkdkMiLCJtYWMiOiJkZTc5NjBjZWE2ZTc3OTU2NTk0M2I3YWFjYzBmMDc4MDNmNjk0NWJlZTczZGYyNmZkNDUwMGVmZGNjOTY0YzI3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9qTlBmRm5hTHFPdktJc1ZnQU5KYVE9PSIsInZhbHVlIjoidFZaSm54Q3ZjbWU2WU05T04velRTTVBWVklick1Ra1A4LzNxeHhMUnFDU2hiZk5Mam5OYUZIbTlWTkhrdHNsOFJydVA3bW8zZWlkbTc5QXlxRzhiN3lGM2pmcm5SQmd0ZlZqSjA1M3kvS0p0bnNqNG44UnY5RkJFcU01bEpiQzZVTXI1OHRHSUx3NWs3Ym9FRkwxck1sZC8zS1BiNjN6b3MydzdKcnQ3VXB6VCtBMUU5OUpWSUQzZVdSZTZjNHZvSlJMSEpnbUJSZVFyM0hhczZGcHc1R2ZZOGR1dUh6WXJ3SSswVWV0YVEzTnJNbWNFanNYRzNLRmdrZWFLVEdBWFcyQlkreCsyNXE1c1ZDRTZyTm9YUmNKUFU4eEpzREVjVFZqNEVWRWh5aGZHalFUNXhTdmJmOHJDVGVmVUVYcUtFZFF5cVMySjc4Wm1HZTIzVDNOdXkvdzNheWIwSU5NY25LTUs2L2NJZTRpNGJFdk82eFQxNkFOSWdOc2UrNmpPODkxODhEOFNwQ1dZaktmRHM1ZThaRHBISll6UWhaNDNvbmpadnYxdGlIZEtvQU1pUE9CMXZqdnVveU5GM2Q4Mk1HZG5kR0poYVhTTlFJZEhUNGRJSzhJQ0p5Y05lSVlEaExDN1U1RXhQOEtYbFIyK2IvelBJdEE3Q2RFSXNtcGEiLCJtYWMiOiI4MmVmNzAzNTI4ZTRiZDE1OTA4NjBhOTk0YmUyYjk0NzQwMGFjODJhMWY1ZTY1ZjMwOTI3OGYyYjYwYWYxZTAzIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389236177%7C21%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344778059\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-368350418 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368350418\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlM3bDZjSG5mazZEa0hYV0xHdXduVnc9PSIsInZhbHVlIjoiVGszdjdQc1pkN2k5UDJWODJEaW12Q29tbkVCWm9qdjBYUi84YWRSRjBpZkFJdHZlM0w2RGdyWnZReFVBZ0NmRVdENzZyUnh5VXNqQmxtM2w2THI0bTYySjJWN2xCTjNEWCtvOThkQXVUU1dKTk10OVBUQjJqZTJpQ2lEN0ZTZnczeTY2Z29HdGJyRkR0bmtWNExPSHAxOXVGN2liWWlKMXlGK3h1TlZlOCtIWExIMWI0SmZ5Q2RnL08rZTUrVGtrV1dhY0pXU0pZaEQwc1ZJZW1UbWt5enVmTjdXZDZFQlN3VVFIUTdqU3B1N0dHNFA1d0hmL1VsS0RkMjhQaWRSUFZ6L0NtUWozUFJ5N1NCQUtGY3pnQzhtaEJubjZoODYrYjRGQmZ2YmNCYnlRYlp2YkRXYVFlKzNSdGdmTlRkWVpIQ2VwQ2FlUHBEelRNK25EdlkwcGEraXdJTkFzRGs3K3lBcFJHM0RZZTVNS2ZxSzk5d2FoR2FTRjFQV09HblJIemtYZDBkQTJCSTZFOHQ1Z245Y2trTS82WUtXb3dUeWJaamZUdmhHeUJPS2ZhUXVXc1ZPRThGMzNPU3BFOWxhU242N0FqazhuQWRjYm5SMmtrWXFlZ0dUVUt6YURycnJHcEgxVjMrQUdYUlNDYWxyNTc2dU1KYnJxd1NTb0NCcksiLCJtYWMiOiIyZDAzMjViOWRiZjE4MGRjOGQzYjg5NzBkZTZhNGEyYzk0Yjk2Y2MxNmZhMzQwNTljYmUxZWY3NzcyNDVhNGY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFydzc5ZldCa3U1ZkRTVkhBY05VeHc9PSIsInZhbHVlIjoiZ0oyaFQxVE4yQmlLbU1TeEpsbjdCMjF6b2JlQkk0VzE2SU52NmJqZ3AwMXFQQzA4ODRRaHN2TDk4clZLYURJaHRQbFpSZ1VjcEd6ajd0c29nQlRDbEtpR2FhMkRiaUVPbFU0RzAxZGp4SzlpRnJsdUg3V1h1NVBFZXVYaWl5bTVzZHVIVFpZUFI4L1NMbDNQM2l5ckNMZDQxemhiNmJySmNkWWJDZER5aGd0cEtsNWgzNmt0QTZyVVppN0x3cjY1elpIdmhiZXJlQ3JVSUFydk9uQzlkQWl6ZndJTDBRRTR2UDA2a04yV0JCdzNRdTNwWnhUTUMwVHN4OERZVmIyWkk3S1B2aUpLU0pXY1NyMW1BZ051dktVVmRDWlNsMERiY2hGdWZIZGc3cklINzExeXFhd3M0VkhDK1FLdUVYZUVFR3JHUDU3TDE0Q0VxeDBKUnRmbUVFMFd1aC9rMGRhekMveEpqWjBRL3RSeElueEdTTWhReHdwRDVXOGJKZWdVaEQ0ZStqOVNZOHI1NkZldzZ2dXdXcFdSdnc5QWVQbkI1L2JuSnBnRU5PQm9ZWDlxanYxbm8rVTJaR3VaeGdvUU8rc1JicDVMcm5hbklyY1FZRURwRG9GNVowaURHeGJTUksycWNmVDNIMFRJVEJTMkVoSHJaeGVDbzg0d1NFbGgiLCJtYWMiOiIxZjY5ZjA5YzI2MjdlMzI2Y2I5MmYxMTJmY2YxMWVmMTlhOWY5Y2JmZWQ0Yzc4YjM5MjdkZjIwMWViYTQyMjIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlM3bDZjSG5mazZEa0hYV0xHdXduVnc9PSIsInZhbHVlIjoiVGszdjdQc1pkN2k5UDJWODJEaW12Q29tbkVCWm9qdjBYUi84YWRSRjBpZkFJdHZlM0w2RGdyWnZReFVBZ0NmRVdENzZyUnh5VXNqQmxtM2w2THI0bTYySjJWN2xCTjNEWCtvOThkQXVUU1dKTk10OVBUQjJqZTJpQ2lEN0ZTZnczeTY2Z29HdGJyRkR0bmtWNExPSHAxOXVGN2liWWlKMXlGK3h1TlZlOCtIWExIMWI0SmZ5Q2RnL08rZTUrVGtrV1dhY0pXU0pZaEQwc1ZJZW1UbWt5enVmTjdXZDZFQlN3VVFIUTdqU3B1N0dHNFA1d0hmL1VsS0RkMjhQaWRSUFZ6L0NtUWozUFJ5N1NCQUtGY3pnQzhtaEJubjZoODYrYjRGQmZ2YmNCYnlRYlp2YkRXYVFlKzNSdGdmTlRkWVpIQ2VwQ2FlUHBEelRNK25EdlkwcGEraXdJTkFzRGs3K3lBcFJHM0RZZTVNS2ZxSzk5d2FoR2FTRjFQV09HblJIemtYZDBkQTJCSTZFOHQ1Z245Y2trTS82WUtXb3dUeWJaamZUdmhHeUJPS2ZhUXVXc1ZPRThGMzNPU3BFOWxhU242N0FqazhuQWRjYm5SMmtrWXFlZ0dUVUt6YURycnJHcEgxVjMrQUdYUlNDYWxyNTc2dU1KYnJxd1NTb0NCcksiLCJtYWMiOiIyZDAzMjViOWRiZjE4MGRjOGQzYjg5NzBkZTZhNGEyYzk0Yjk2Y2MxNmZhMzQwNTljYmUxZWY3NzcyNDVhNGY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFydzc5ZldCa3U1ZkRTVkhBY05VeHc9PSIsInZhbHVlIjoiZ0oyaFQxVE4yQmlLbU1TeEpsbjdCMjF6b2JlQkk0VzE2SU52NmJqZ3AwMXFQQzA4ODRRaHN2TDk4clZLYURJaHRQbFpSZ1VjcEd6ajd0c29nQlRDbEtpR2FhMkRiaUVPbFU0RzAxZGp4SzlpRnJsdUg3V1h1NVBFZXVYaWl5bTVzZHVIVFpZUFI4L1NMbDNQM2l5ckNMZDQxemhiNmJySmNkWWJDZER5aGd0cEtsNWgzNmt0QTZyVVppN0x3cjY1elpIdmhiZXJlQ3JVSUFydk9uQzlkQWl6ZndJTDBRRTR2UDA2a04yV0JCdzNRdTNwWnhUTUMwVHN4OERZVmIyWkk3S1B2aUpLU0pXY1NyMW1BZ051dktVVmRDWlNsMERiY2hGdWZIZGc3cklINzExeXFhd3M0VkhDK1FLdUVYZUVFR3JHUDU3TDE0Q0VxeDBKUnRmbUVFMFd1aC9rMGRhekMveEpqWjBRL3RSeElueEdTTWhReHdwRDVXOGJKZWdVaEQ0ZStqOVNZOHI1NkZldzZ2dXdXcFdSdnc5QWVQbkI1L2JuSnBnRU5PQm9ZWDlxanYxbm8rVTJaR3VaeGdvUU8rc1JicDVMcm5hbklyY1FZRURwRG9GNVowaURHeGJTUksycWNmVDNIMFRJVEJTMkVoSHJaeGVDbzg0d1NFbGgiLCJtYWMiOiIxZjY5ZjA5YzI2MjdlMzI2Y2I5MmYxMTJmY2YxMWVmMTlhOWY5Y2JmZWQ0Yzc4YjM5MjdkZjIwMWViYTQyMjIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Shift Closed Successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}