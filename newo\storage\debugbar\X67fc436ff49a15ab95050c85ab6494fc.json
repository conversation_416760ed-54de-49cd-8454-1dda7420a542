{"__meta": {"id": "X67fc436ff49a15ab95050c85ab6494fc", "datetime": "2025-06-08 13:11:59", "utime": **********.5349, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388318.019278, "end": **********.534937, "duration": 1.5156588554382324, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749388318.019278, "relative_start": 0, "end": **********.361086, "relative_end": **********.361086, "duration": 1.3418078422546387, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.361108, "relative_start": 1.3418300151824951, "end": **********.534941, "relative_end": 4.0531158447265625e-06, "duration": 0.17383289337158203, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45053384, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02082, "accumulated_duration_str": "20.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.446124, "duration": 0.01821, "duration_str": "18.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.464}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.490491, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.464, "width_percent": 6.1}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.510638, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.564, "width_percent": 6.436}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-839833604 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-839833604\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2136197797 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2136197797\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-414714594 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-414714594\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-891450535 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749388306480%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBIdmh2eG9wVVZ4TXhoUUI2TE5OckE9PSIsInZhbHVlIjoiTEFjNThydWNuelFBbFNtdWdSdFlVOVJ6RnYrdWJUQzVkMjlRbEx4STB3K0tFOW5kY3M4TjZYM3VaZUsvRjAxU1pDcmQ4UkpoalZoVkJMU25wL3VwN296S0Fxbm5PRXk0MWtqamowaTNpWk8vR1VRVU0wcVhQc3A1TUhqQWZIOU1LWmFrTjd4TXdZNmdCeGdKYmVuZkNJWXpQMW1rSEtLK1IrSXppdXBqMmtyQ2ZKdi9tdzl6N2RyMnNkOXM2TDk5c3JUeE5CTW5HUTRDenprTmhhQ0l2Q05ESFo0NGc1SzFNYlAxTFZRZ2FUR2ZhSmE2T3FkdFZCbFJmTTg2bUwwdWY0akpWeHR0UGFtT2dENExZc3M3YmoxLzdpb0FSc2FpbWdVWExjUXpUVkFpUDB1Qi8vci82V1NxM3VJWmtFai9XYVliZHIyb3h4NzRWTUF6SDRRNFA1NitvZ1ZFS1ZVcVJoZFhkVHVRUEdMVTRvRFRScHRyT01IT0EyODczS3I5Vk5TUmZDQmtkRmZnVE91L3hJL3hWbEJNZnZsK09lUVpkbjBPUFNYL0pyMU1QSXNXcG4wRVRLMWFCdFhHOU9vcWY0cHRYWXk3N01mc1Q2NTVYK0FtQ3IySDF4akJ6akJqMUsrUlJUcW5telk3c1FhRzdWWmVnM0JVUnlQcEtBOWoiLCJtYWMiOiI0Mjk5NWVjM2M2MDY5MzE0ZDYxZDQ3ZDdkNjc5MDEwNGNlZmNjMTcyYjc0NTM4NzA5MmQwYThlMGJhMzM5YzFiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imc5YTU4V0xKZzlHalJ5MXdlaXF6TWc9PSIsInZhbHVlIjoiU3R3Qk1Gb0V6TzE1TGI4V01zK3ZYbStodnAzbzFVaHEyR2sremVsbXVNVHovVG1CMmVPOVIrSEovSERWd1ZhZGcyTXpJMEpTSjlkUEo4N0tNNmJJRnFWRkFORkI2N1VwOEx3YmdrbWJDai9aa3BTczlEL2w1Yk13VzVuMEsxZDErVURzYXZVYUZZc3lzMDhLNGQ3dTZuaTBtdHp6eWhEZ09aOTFKbUpHOUd2WUNlSUt2Z1l4cCtseWt3UVlIbzlOYnJubUtOYm5zc1FsRkoxbFNueUZ0SDl6WEV0b1p0V2swWEQ1M2FwTk4rajlwVzdSS29oVFd0SVU0SmZqWlFBdTNIMVlJVFJ5ZTRoSkxYMEpnZEhnbFNRN2d1RTBuemNBMG84UWltOGFzRVZhajEyaUhhSEdCbEloaHYzMU0vSUR2dVVtOW81QW14QXJmc0I2ZEc2VUlLczlDc0cwejJpcTJBNHVUOXZHNnIwSFdQeDVzMmJkQVJSaCs5eno5S1lBNW9FcnlGNHp3SGdHS2ZpZmdXeEpZNFNiN0FsQnBqUDNOcUN4bUNVMmJYQ2xoRC85cnh3MHcwcWY5Z0FJeEc1cjV2Q0ladm1SOUp4QWszWDRyUUVhYTVmZHJ4NjBwdVJ2ZUJKMFdWY2t5R1YwdnNiY3BES2RCeWliM2NTcytsYWciLCJtYWMiOiJlNTcwMGZiOGMyNTdkMmM3ZTVmMjUzNmY3MjJhMjVmMzVkYTgyODNlMTAyOWY2MTlkYjhmYmExNzJlMGMyNzRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891450535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-583257582 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583257582\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-356976625 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:11:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik56Sy8zV0RyZGQ4SklOSFo5WHdEOFE9PSIsInZhbHVlIjoiUDYwM3hka3RoN21iTG90SGhhT3hvZ2xUV09zdktjSm1QM3ErZFE5TGRLNHF3bG9ZWHVFL0ZvM2tuSGZ4dUQ2bkZVYU41U09OSml0R2IyMmN5elZnalNkOWp3eDFRUzZJSVFwbC9nZlZ0eDdBdCtnR0haVzhOOW5uNlJMY2RZUXJQelBBMm1wdG41bGdYbVNnWW11RUFJOWVESitxa1ZiVzZhTkZYQThHQnE1UyszY3hob2RQbG9YZGNpRVpnZm13dWdLOGFzSDIxM3hxRjc0a05HTjJLcUtuQjBpbmE2dlJZVTEzVDVTczdVQWp1bVVZeHA1aSszSTBjeXMxYXMzZ0N3cXBkYmd6NDJ5TXVaYm1OT3ZTc2gvLzJWSzF5QUJIVDhDWmo5OTYvTXc0NE5vSzZKOTlQanM4WXl1Q1lBa0JLMjNnZXlBS0c3UU15b3BCTUNwQXdDSERmcjBvNlhGcEpNMEQvQlpHeSszWm9qK01lYUxaUWJBdVB1SDlKbHZRa1FtY0ttZXp6SHNXVnBKU0FJQ0d6Nk0zSmdsZ0NNbjRXK0tHQUZHQTNVQi95QnYyelJJOWwvNFJia1hBeE9aa2FyU3NyZFloRkVHdFJHeFZQNWZLSXZ3bnBsZ1hFbVF3V2xTbTNQSXErK05IQUJRMjZlTVlXczFSRE5yaktoelkiLCJtYWMiOiIwN2I2ODA4MWMwNTM4MjhjNTU4OGQzNDlhMGY4MGRhODEzNDg4NGE0NDI4NTgxNzg2ZTFlMTBjNTk2MGRmNGU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:11:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZDQVlWcXd6L1F2TG0vRG5OSkhXWkE9PSIsInZhbHVlIjoiT0NQWUc0ZVdMaER6N2w0MEI4OUVpeWJlV1VvU29QV042NXdwMGlyR3pqTDloNWQrdzRhdGdlNyszekxOalBwWmhtY2svTHA2WHkzcDJWZFF4T2R0SkhlTjJwNzVMZlNhNXVGWGZ4TjFlUktXcS9KWTZpb2RCdE9Cd2k2ZUhvWHdQZnNYWWE3WXBqd2RocHpVQ1lsR1NVUGtHZ0hsMXVyVXg3N1ZBeEdmclFrSXZiVWZ3NTlka3JGQzlYR0pseDlkZTZ4WkxWaXJucERVckJ0Q2N1TXlEcEVON2dKS1pleWJYZkRXVnVhc25Qc2JyRlNuWG03c3l4RG9rWVhzMUJDTWpYTEJwZW15aDVjS21OdTcrMEVNQWpWOU1TSk96dFIyY3BLVWtsTFFyQmNqZEt4aHRJYnNZZVFWOUVUTllYaHduTWtrdnBkTEppUkJSalIza1Fsa3BtVDd2OER4Q25OTzcwNUhLbS9kOFg2OWpab1dCVVJ1K0lPQ3VLUGxXMitPNVdpMmtkMDdDRXQzK3BFcEowZ0ZyaUNrRTc5RVBjOVN4a0NDcTdkRkNKSG5GUjIyeXJleENPajdIOTlMNDNvSElTMHdReEFHZUFaZWlSckVVYU1tNGd1SGlXbDRvZzlsZ3ExSFllaTNiMkZmWXcxWEFwcFdKUGpJaDB3RzhFZm0iLCJtYWMiOiJmZmM3ZDIwYTU2NTdlOWRhMmJmZGNiZjcyOTkyOWM3ODMyZTBhYzRiOTBlNGQ5MjQxOTYyNzllZGM4YTNmZmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:11:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik56Sy8zV0RyZGQ4SklOSFo5WHdEOFE9PSIsInZhbHVlIjoiUDYwM3hka3RoN21iTG90SGhhT3hvZ2xUV09zdktjSm1QM3ErZFE5TGRLNHF3bG9ZWHVFL0ZvM2tuSGZ4dUQ2bkZVYU41U09OSml0R2IyMmN5elZnalNkOWp3eDFRUzZJSVFwbC9nZlZ0eDdBdCtnR0haVzhOOW5uNlJMY2RZUXJQelBBMm1wdG41bGdYbVNnWW11RUFJOWVESitxa1ZiVzZhTkZYQThHQnE1UyszY3hob2RQbG9YZGNpRVpnZm13dWdLOGFzSDIxM3hxRjc0a05HTjJLcUtuQjBpbmE2dlJZVTEzVDVTczdVQWp1bVVZeHA1aSszSTBjeXMxYXMzZ0N3cXBkYmd6NDJ5TXVaYm1OT3ZTc2gvLzJWSzF5QUJIVDhDWmo5OTYvTXc0NE5vSzZKOTlQanM4WXl1Q1lBa0JLMjNnZXlBS0c3UU15b3BCTUNwQXdDSERmcjBvNlhGcEpNMEQvQlpHeSszWm9qK01lYUxaUWJBdVB1SDlKbHZRa1FtY0ttZXp6SHNXVnBKU0FJQ0d6Nk0zSmdsZ0NNbjRXK0tHQUZHQTNVQi95QnYyelJJOWwvNFJia1hBeE9aa2FyU3NyZFloRkVHdFJHeFZQNWZLSXZ3bnBsZ1hFbVF3V2xTbTNQSXErK05IQUJRMjZlTVlXczFSRE5yaktoelkiLCJtYWMiOiIwN2I2ODA4MWMwNTM4MjhjNTU4OGQzNDlhMGY4MGRhODEzNDg4NGE0NDI4NTgxNzg2ZTFlMTBjNTk2MGRmNGU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:11:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZDQVlWcXd6L1F2TG0vRG5OSkhXWkE9PSIsInZhbHVlIjoiT0NQWUc0ZVdMaER6N2w0MEI4OUVpeWJlV1VvU29QV042NXdwMGlyR3pqTDloNWQrdzRhdGdlNyszekxOalBwWmhtY2svTHA2WHkzcDJWZFF4T2R0SkhlTjJwNzVMZlNhNXVGWGZ4TjFlUktXcS9KWTZpb2RCdE9Cd2k2ZUhvWHdQZnNYWWE3WXBqd2RocHpVQ1lsR1NVUGtHZ0hsMXVyVXg3N1ZBeEdmclFrSXZiVWZ3NTlka3JGQzlYR0pseDlkZTZ4WkxWaXJucERVckJ0Q2N1TXlEcEVON2dKS1pleWJYZkRXVnVhc25Qc2JyRlNuWG03c3l4RG9rWVhzMUJDTWpYTEJwZW15aDVjS21OdTcrMEVNQWpWOU1TSk96dFIyY3BLVWtsTFFyQmNqZEt4aHRJYnNZZVFWOUVUTllYaHduTWtrdnBkTEppUkJSalIza1Fsa3BtVDd2OER4Q25OTzcwNUhLbS9kOFg2OWpab1dCVVJ1K0lPQ3VLUGxXMitPNVdpMmtkMDdDRXQzK3BFcEowZ0ZyaUNrRTc5RVBjOVN4a0NDcTdkRkNKSG5GUjIyeXJleENPajdIOTlMNDNvSElTMHdReEFHZUFaZWlSckVVYU1tNGd1SGlXbDRvZzlsZ3ExSFllaTNiMkZmWXcxWEFwcFdKUGpJaDB3RzhFZm0iLCJtYWMiOiJmZmM3ZDIwYTU2NTdlOWRhMmJmZGNiZjcyOTkyOWM3ODMyZTBhYzRiOTBlNGQ5MjQxOTYyNzllZGM4YTNmZmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:11:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356976625\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-436269087 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436269087\", {\"maxDepth\":0})</script>\n"}}