{"__meta": {"id": "X049952cc6a765849ea7d08bc44e37959", "datetime": "2025-06-08 13:34:57", "utime": **********.495882, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389696.201015, "end": **********.495924, "duration": 1.2949090003967285, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749389696.201015, "relative_start": 0, "end": **********.332927, "relative_end": **********.332927, "duration": 1.1319119930267334, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.332949, "relative_start": 1.1319339275360107, "end": **********.495929, "relative_end": 5.0067901611328125e-06, "duration": 0.1629800796508789, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45604472, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009389999999999999, "accumulated_duration_str": "9.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.410661, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.253}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.446616, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.253, "width_percent": 30.245}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.470766, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.498, "width_percent": 11.502}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-89248881 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-89248881\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-47630468 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47630468\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-168307384 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389691574%7C33%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9TaU1vakh0M0tVWEQyMXFOMitDbkE9PSIsInZhbHVlIjoiQ1M2MkxLNlZBSno2OVBYUDVFWHk1eTNhUG1UeThvSHl1d3FhbkhxeTVUa1dIb0VOWnQwRGRvbHdqSzNJaHRzSTdtdzlGMXBYQ0RiUG9FVTRxbStvckRLT3l3d2hWZ0lGZ2crUEhrQ3htTDFNbFFZVEdoN25DbmlaTEttVm85K2NBYkFSbTNDMnRuSkNqVmZoVG9oOUYrcDR6eWVGUVV6Tm5uYUJiQ3dFRVEyeC82dGtMTW1YSXRJMGE3REt6MVFVZFQ1UklhQ3ZHQVNzblA0bGlRRStVdXZleGR5VkFLeFB2RmJGZ1BOek5HODNLYWlhZVgvZlRMZkdUYmpDNHFnVDU3KzZ6clZxUDhNbkpXU0MzaDdrUTdkL2RyczZtcHNROWxwKzhoKzl6YzFTeXF2R0p3V0ZLR25wbisyS3pJS1J4ZmRKUks4SW1qMlA2d0JZSWc4OXYwRXJDaUM3Ukp6UDFpZUZoTHprYThFdUNoQkFBMFJvNTNOeFN0eGU0QnlnS2ZoVkxEeWs3YnBrRUV2bkpXWlFLZHN0VVR3SDlZdjl2VXl0SGx0YzhDbngyY25vU0NReks1dzZ6WUlmemFYVU5aVktTU3dRS3pOUEI4UHlpSzNycHJLNlZGOUl1M0x2cTl2Mm1xQWQ1ODhmY0llcU4yMGdxR2FrSE5JaXVyazgiLCJtYWMiOiI3OTRhMGQwNWY5ZTZiMzc2YTFiYjE5MDBiNzRjNGFjNGMxNzY4MDhlZDFlNGY5YjVhM2Q0OThjZDBmMDcyMWMzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InROUnhYK1laWUJkbTlTN0k3Z1hpRkE9PSIsInZhbHVlIjoiRUtYb2FGUms3UGNKOXh0OU9ockRCcmRjUENna2VJR3oxKzJKNW96eUlWUVB4bnVvSlEyOEtTejV5WldJZFNwcXd5Y3cyaUlNamV0K21QdGZKNEVDUmh6WVlWbi9md0RnWm1jQ28yeGFpN3FUMy9MSjFQQk14M0dza29KendLTjZxdm5aTURhRnNBdlF6VHFoS05nRnJMTGJPR1FMVHBoamk3TjJ3YjVWUE16aGNmNklwb3JTL2E5bzJzdy9kWm5zVTRQV1RzNUlDQ2IxektRWktLUEJFYjRubEpFVHhwZGRuSEVaZ3o2TVBrVE5XdkRyOXl5N2ZiOEFPTG1sc1dYQmZ1eU5QRklHQ2M5bERMb3ZpL3l0RHRpNzNhbkYrUmgxREtZVi9jUFc4STUwcXlqdW1ZVlpjUWQxNkJsc0FwTVdXSDkrakh0UjVkTlFzckJ3Sk1YMXBFWTNFRE05M09lWUpNajNpU2p6N2pvbm5BWkZoWGdrOGdOaVBHNkFCY1cyOC9zSVp3QnFYYzdBSEt3ZlRGeU10R1EwWW5uMm9aSGNJbVVRSWdhbVpRZUwxK1U0Vzd0b2pCYnhFTzd6UUREQzlzVjdJNUxzMHFqRVNTejJ2RGMwUHJRVW9rYUk1T2pDcHYybGtuSFNmZ3BhRXMvU3JKMmJ6WHIzbTZKdG16YisiLCJtYWMiOiJlM2I5YTFlMTllNmUwODhhMTMwOTQ5NmU4ZDU2ZDI2OTdhNDQzMjZlMWNmMGViNDZjYTU3OGQyYzcyMDAwMWQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168307384\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1457681612 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZJRFQvM0s1UUlqbEdzREptWWx6OVE9PSIsInZhbHVlIjoiQnBXcGFwWUFxOFBLK1FrUkVaNmNVNFNvYzMrTnJJcUhDWENmQmtrL3ltbm1ieGVSdysxT1NHVU12WkhHTGc0Y2pSd21SUDZQQWxQQk9ZVUNKZzZHbk9tRkM0cS9KenZIUC9JMkdLYlRkNTNUWGVyR0xOb2grdXcwdHp5dWxDcWpBNWl3V1BnZEtsMnI3Mk96cDFac2dZQTFBT0ZwamphVGZDM2ZTdU8xSmhSV3dWeFprOXQrUWFuZE1hcjFYU3hXQUM1bldFa1VnMG5sM3FOS0VVdnBKK2hCMWVwVDU0YlN0WHJlbFFOMzNvQktSL1VQY0psaHU0TmMrQzRmM3ltYW84TzA3cGllakw3dkswYXM4b0pTUm5CemI3c2ZUN3I2TjdQYXFYQnNjQmZ3ZkhIekRPSE1lREY5WkV1RHgvOWhHcnprOGhxWDFsd21oSDVJK2I5TFJiWEZrRHFsdUg3dzJwL1ZkTVAyYzhCWUpHemVKcm5GMjVaUGdWL2g0TVFnMmdFdDN2bUMzMno5SHUvMlpaSFdKdU1XbTdEbWx4Z3lsNXNnUk5paHA5TFNuNnJ1b0s4Y2VkWVprYUZkekdSQXJJak5pSlJTSU1uSThTcEZQSjd1TUVWTm1kUzZGcmIxUGttMERZQmY2Y0tRblB4VHNqVGE3V1hwNXFucnJqSDUiLCJtYWMiOiJkYmM3ZDhiMjRjYWU4OThmOGVhZWMwZWVhNzYwZjJkMTEwMmMxZmYyYjA1MWQ1ZTExOTNiOGRlNzg0OWE1ZTNlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhQZk1TUnF6MkRUQXRYMGdWQTNGRmc9PSIsInZhbHVlIjoiZCtNdjJsSDNDdjhJams5VzJ2cmJySnNPUjg3RitGWGJZaS9ZeXdEVGU2aWlkREFtRDZ3WjBwYWczVHRRbmtNbkczc1lVU3hZM0tWVzFXQ0E0MCs4VmNTbXVES3R4a3JrbENvdEtNcER4TkUzWXNwMkhuWUVtT0RvRHQxNE91cVM5UTJoc01aaElmVCsyMEVNRFcyUUl2QTRVdXhYWkI1UzVFTVVGMHlTMjN1UlUvWGdiUSs0VEpZaDA3STM1cVpTbU9rd09RV1BEeWQxR2NzSEpTY3hadG5PVE9kakZ3QkVyOFhGa05zVUQybW9vQXRhUS9tUFhQS0NXYkZlb2JlN1h5Yy9pNHd3RVBGSytuaitkRUNKbW1rQU5LRkJkMGNreWFHTGlzQ1lHczVvWVY1WHRjNGhoNW5TcEZMQi9NVUdTS21RUWgzcnBOOStwSTAxUGpneW9od2FzODJRQnBCMURlbE9qMCtqRXFYUnRlaXV5b2VGQlo2cXVRRFEyQjVmMi9Ka0xZVmF3N09ndnFiQmxHcTdYNi90YmZKQnMvK0JBaklZTmFhVDFwTlJDUU1oZ3hlM1dqK1pWLzFYRmErWDNSNzJaVFZRSXQxanlQM0hCTWtTVmpOa29TWDh1OGpPNldIazdPZVJmc2xnRHMycXRER2NraUdJL0tJYWJWM0MiLCJtYWMiOiI4NjlhMDgzMDZhYjY2NDZmZTFhZGI4OGZhNDVkNjg3OWZmNmQ0ZGFkOThlY2ZhZGJlNDRjMjFiMzRkMmI0NjAyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZJRFQvM0s1UUlqbEdzREptWWx6OVE9PSIsInZhbHVlIjoiQnBXcGFwWUFxOFBLK1FrUkVaNmNVNFNvYzMrTnJJcUhDWENmQmtrL3ltbm1ieGVSdysxT1NHVU12WkhHTGc0Y2pSd21SUDZQQWxQQk9ZVUNKZzZHbk9tRkM0cS9KenZIUC9JMkdLYlRkNTNUWGVyR0xOb2grdXcwdHp5dWxDcWpBNWl3V1BnZEtsMnI3Mk96cDFac2dZQTFBT0ZwamphVGZDM2ZTdU8xSmhSV3dWeFprOXQrUWFuZE1hcjFYU3hXQUM1bldFa1VnMG5sM3FOS0VVdnBKK2hCMWVwVDU0YlN0WHJlbFFOMzNvQktSL1VQY0psaHU0TmMrQzRmM3ltYW84TzA3cGllakw3dkswYXM4b0pTUm5CemI3c2ZUN3I2TjdQYXFYQnNjQmZ3ZkhIekRPSE1lREY5WkV1RHgvOWhHcnprOGhxWDFsd21oSDVJK2I5TFJiWEZrRHFsdUg3dzJwL1ZkTVAyYzhCWUpHemVKcm5GMjVaUGdWL2g0TVFnMmdFdDN2bUMzMno5SHUvMlpaSFdKdU1XbTdEbWx4Z3lsNXNnUk5paHA5TFNuNnJ1b0s4Y2VkWVprYUZkekdSQXJJak5pSlJTSU1uSThTcEZQSjd1TUVWTm1kUzZGcmIxUGttMERZQmY2Y0tRblB4VHNqVGE3V1hwNXFucnJqSDUiLCJtYWMiOiJkYmM3ZDhiMjRjYWU4OThmOGVhZWMwZWVhNzYwZjJkMTEwMmMxZmYyYjA1MWQ1ZTExOTNiOGRlNzg0OWE1ZTNlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhQZk1TUnF6MkRUQXRYMGdWQTNGRmc9PSIsInZhbHVlIjoiZCtNdjJsSDNDdjhJams5VzJ2cmJySnNPUjg3RitGWGJZaS9ZeXdEVGU2aWlkREFtRDZ3WjBwYWczVHRRbmtNbkczc1lVU3hZM0tWVzFXQ0E0MCs4VmNTbXVES3R4a3JrbENvdEtNcER4TkUzWXNwMkhuWUVtT0RvRHQxNE91cVM5UTJoc01aaElmVCsyMEVNRFcyUUl2QTRVdXhYWkI1UzVFTVVGMHlTMjN1UlUvWGdiUSs0VEpZaDA3STM1cVpTbU9rd09RV1BEeWQxR2NzSEpTY3hadG5PVE9kakZ3QkVyOFhGa05zVUQybW9vQXRhUS9tUFhQS0NXYkZlb2JlN1h5Yy9pNHd3RVBGSytuaitkRUNKbW1rQU5LRkJkMGNreWFHTGlzQ1lHczVvWVY1WHRjNGhoNW5TcEZMQi9NVUdTS21RUWgzcnBOOStwSTAxUGpneW9od2FzODJRQnBCMURlbE9qMCtqRXFYUnRlaXV5b2VGQlo2cXVRRFEyQjVmMi9Ka0xZVmF3N09ndnFiQmxHcTdYNi90YmZKQnMvK0JBaklZTmFhVDFwTlJDUU1oZ3hlM1dqK1pWLzFYRmErWDNSNzJaVFZRSXQxanlQM0hCTWtTVmpOa29TWDh1OGpPNldIazdPZVJmc2xnRHMycXRER2NraUdJL0tJYWJWM0MiLCJtYWMiOiI4NjlhMDgzMDZhYjY2NDZmZTFhZGI4OGZhNDVkNjg3OWZmNmQ0ZGFkOThlY2ZhZGJlNDRjMjFiMzRkMmI0NjAyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457681612\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-70073131 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70073131\", {\"maxDepth\":0})</script>\n"}}