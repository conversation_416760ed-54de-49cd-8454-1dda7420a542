{"__meta": {"id": "X615eb1b3af2d088cac61d3621aae6d63", "datetime": "2025-06-08 13:19:38", "utime": **********.353622, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388776.966112, "end": **********.353649, "duration": 1.3875370025634766, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1749388776.966112, "relative_start": 0, "end": **********.101409, "relative_end": **********.101409, "duration": 1.1352970600128174, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.101427, "relative_start": 1.135315179824829, "end": **********.353652, "relative_end": 3.0994415283203125e-06, "duration": 0.2522249221801758, "duration_str": "252ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48125080, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03656, "accumulated_duration_str": "36.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.197091, "duration": 0.02924, "duration_str": "29.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.978}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.249983, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.978, "width_percent": 2.79}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.301586, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 82.768, "width_percent": 4.759}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.310593, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.527, "width_percent": 3.911}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.324065, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 91.439, "width_percent": 5.99}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.333405, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.429, "width_percent": 2.571}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-80475275 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80475275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.321195, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-413184124 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-413184124\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-333766589 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-333766589\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2072732543 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2072732543\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1658279775 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9zVzBMa0dickNwaTVHd3AzclBkQ1E9PSIsInZhbHVlIjoiVmNhSGlRM2s2SG5LdmdTTVBWM3BoMXVGOXQ3Z1gyYVk3eHoxVElyODFpYzFkeFM0U0l4cFRjaDcrRTluRHhEREJqaG9yUnhqNTlOMDk4N05mcjhYcmVWNHJSZTlvVkRwMWlKWUw5WVVuWkFMWmVXVTZDUWR4SXVKREJUNS9VQ2tzNWVpWjNjSjFJLzRIMlNtQVlRZWJ2NzM2cDlSTVhFZlpOS0FjUHpENzA4T3htSW13Y3g0T2ZKNW5OV1JoMkxJSVRHTWZEV3hjT2x1VkVxN2NyTTFrTTNqbmR5OXcwVHVUMUVlVFp5Uk90MDF6L1Y4aVNScko0L2dnWmNzM1RETjYxaWVRQ09aZklScGJSczN5d1M1aWNnYUxLWGZiYVhaKzU2OHRRcFZiK09ITDJXdUNhOGVVYzV3bnJXcU1uVmdLaUJlNnlOa0x3aEM4N1FlNnJ0c1hUSzQyZ2hRSCthTTFIRWErY0xRdldUOUw0Z2ZvUWM3aUhVYzNEcUt2RDM0MVd1T1RmQUNZd3J6NHhEdndPT21Id2s3UVVZWXFwSDFoeVFiejRwdjFuRHp3S25BbHlZSWRhcVRNZlZtQkJEVFNZbk9DQ1djREgzNDV0M1dxd3ZaTC91NytCU3kzanR5RG9ydE91Z2wvYmVNT2JwUk1Qc0ZBN0YvM0NPYWtMYUciLCJtYWMiOiI3ODBhZDcxOGI3NmRlNzNjYzA0NGI5OTk2YzQzZDQwMTAyMDUxNjBkMWY5ZjE3ODMwOGZhMjFmNWY1NGFmODFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im0remdtKzlmdWxuTWpuRUZmSDlCOUE9PSIsInZhbHVlIjoidzFBanBpOTVGMEc5VUp6dlpTNjExMWNucVRjZHl5amF0QWFhK1dFdENqck9ld0ZscWJBdW1Qd1lwdHY3RUl4SnBNZ1pSSzBYU25OQ2ZhUTV4WUluQ1pGNUlMR0kzYXQ3OFE1UFZXNXE3MGdROXlNWWZXck1uejlscndBRTdiL0RvOEFDQlcwS2FsSEZLcmRiNGZOK3piamd3dHBlVklTWlErNnY0eCtlaW9UeFgrTzZMeEhQL0lVeXRpdEV6dVdoNnU1eSs4RmRZQm5pL3kvbGVhaEdma3J6NjhIblBraThhNDV6SnErWS80ZmFoTTRUekNkY25JZnNpTHpjUHpKRkYvS3dGSXY5R0VDWVVEYW5aMVdqK2RSVS9GUjBCdlF2alNzU3FLUXNzelFORWQ1ZGhCWEdSWVcxUWZjR2NCd09IOEhCenZUT0pYWHNCUFRuSmRYeDZZRlNpVW40cmZ6MVlpRXhienhMbkVwV0tDQWY0NVBCbjR2MVRqTFFoL29PY3ZXaGhaVjU1NWV4ZE14cGRIZEtxRDBnZDI1MDQvTk9CSmYySVhjd1hPYWxSREtHcGhDWG1VcEhqamZjU0U1cjBzQWFoczJjWnBhZkdNQytWckhGUkp3dmZDV0IxeU1Vb2Q0OVB5L3ExdEhVeUpNTWFNeWtlVFgrVTJwMmE0eG0iLCJtYWMiOiJiYzZjMWRhZDM4MTFhYWYyMDg3NThlN2YxNjUzYTZjYWZkZmYwZDQ5YzMwMGZlZDVhMTlkZjMzYzA1ZmY2N2ZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658279775\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1074739381 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074739381\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1436845728 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNuVUtNL3Y2OGlSMVc1RVJUT3l3Nmc9PSIsInZhbHVlIjoiSElObWxZYktUZ3RNR1l4VVQva3YxV0lBeFQ3U2tkTkRxcE42OWdhUTlFWHg1T0haKzB2bzY1bkpSamFjYUNVb0pkZngvNGFGb3p3UEtsMjZiUms3dStueTFCakx5U1VKb3pSSXJHd0kwL29iTW9HOWdJVkNTWFVXMTFLWkhDeXZtNUQ1YWN3VWVWUXIzeUR6WEQ5Tk5GKzRWRzhDdHgrY1pNejZhWDJncXRZQkx1WjUzQ0lyeGJmaVRaV2VQZlAwUU9odjk3a212QnFMcGkrYlp4V2h0aDFQUFhwNWpZR1MzVjkzNkRqNWQ1Y3R6YUtwK2xNZ1JwcUUwSkZPdWQ2ZHAzemtaWVZXWTZUSnNaazkyZnJ3VEhvSEJscUFYSjJqU0ZDblBFb0pabE83NXVFSFhsRlUxWkFobmE5NFVGUU0vbkRaU1NHVWIreUd0UUJ3b1E2ZThRN1g5UjNLRUU0VFZHVmQ2UHNoNCtXNHlaSWFQQ3QyWlY1c0tNR3RjeHZpTDd2QXNqOWVvd1A5dmNYTyt1dUw3K2RDTGFvYlNIZkRGUWRWemkxV3dpOEVQTW9ZRERJeDJNMll6ZmdUc1BxZmJwRS9pSHRaR0MwRHJnb2pFY3BrdmQrMitHVWFsU2t4Sk5pc3AxZ0I3blQyV28razRKc2dlU1pSbXVCR2l6TWwiLCJtYWMiOiJkMjEwZjcwNDNmMjE0YWFkZDFkODQ0YmZlNzEyMmNmOWUwNjIwNTFkZDAwZjJjYTkyZTAxMWE2Yjc5MDNjMThiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVGa2taMGVqMVA0ZXVQeFJJbWM1RlE9PSIsInZhbHVlIjoiWm5raFlRbFpzcmVDdXBZbTFNOGpDekw1eGNrU3RoUlA2STM1UEJ3U0RYRFJsQ3ZhV1BGaHZiV0k4andYZlZCMVp3a3d6L3NnaXFMU3duZHQwRFgyZllLT09aV0NtU2hYS1BqTUNsTjUwaVh6Szk5M05abDNCT1JrRkYyczlkczBZSzViNWVaVktWRUZ5Y21aZ0pQdkc1RGlXSWxrTS82WVdlNWNhdkhIZjB6YW42VVdrSjFJclVBZlJpV0VYZXZ1RkxZLzJSVVZhNCs1blByTEFNNWlreXcxQzBLMlZrMHdhRG00dEpRVXhRZjdsSDZLdlRyM1FVMHBhZlNwa2N1SzdpMVJFaTdaNjBKa1Yyank5aWdJVVIrNHhaR2hKc2pNZ3Nxem9sREZQS2ZIZ2JmcnpmaUlCNHNNRGxoZXZIbXF2MFFNZitDbS94ajYvYzN0RGhqU0NOQ1g1ZjN3N25ucnJVaWdKUERGdlNJY1BSdzJLM1AzOEh1MjczSlR2U0dweE1qaExmN1NqUzkrNkdGaEphdWdmdStoMG9aSzE3WU1jOU9mVE1hNnhJSnNiU0VrQkdxV1pnaFJGdzQxb0s5MzUwMGRmaEx3Ui9kaEdtWGVwR1lIcHNremwyNUZMaUlCems0ZkxkZUd3SEZtWGpkYVBxWWI2SVp5OWdNeno1ek4iLCJtYWMiOiIzM2QzOWM3NjdkOTMzNDUzZTcwYzg3NzM4YjZiM2IwMDgzYzIxZGFiMTJlNTU4NGVjNzU2MjgxMDg0YWY0ZWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNuVUtNL3Y2OGlSMVc1RVJUT3l3Nmc9PSIsInZhbHVlIjoiSElObWxZYktUZ3RNR1l4VVQva3YxV0lBeFQ3U2tkTkRxcE42OWdhUTlFWHg1T0haKzB2bzY1bkpSamFjYUNVb0pkZngvNGFGb3p3UEtsMjZiUms3dStueTFCakx5U1VKb3pSSXJHd0kwL29iTW9HOWdJVkNTWFVXMTFLWkhDeXZtNUQ1YWN3VWVWUXIzeUR6WEQ5Tk5GKzRWRzhDdHgrY1pNejZhWDJncXRZQkx1WjUzQ0lyeGJmaVRaV2VQZlAwUU9odjk3a212QnFMcGkrYlp4V2h0aDFQUFhwNWpZR1MzVjkzNkRqNWQ1Y3R6YUtwK2xNZ1JwcUUwSkZPdWQ2ZHAzemtaWVZXWTZUSnNaazkyZnJ3VEhvSEJscUFYSjJqU0ZDblBFb0pabE83NXVFSFhsRlUxWkFobmE5NFVGUU0vbkRaU1NHVWIreUd0UUJ3b1E2ZThRN1g5UjNLRUU0VFZHVmQ2UHNoNCtXNHlaSWFQQ3QyWlY1c0tNR3RjeHZpTDd2QXNqOWVvd1A5dmNYTyt1dUw3K2RDTGFvYlNIZkRGUWRWemkxV3dpOEVQTW9ZRERJeDJNMll6ZmdUc1BxZmJwRS9pSHRaR0MwRHJnb2pFY3BrdmQrMitHVWFsU2t4Sk5pc3AxZ0I3blQyV28razRKc2dlU1pSbXVCR2l6TWwiLCJtYWMiOiJkMjEwZjcwNDNmMjE0YWFkZDFkODQ0YmZlNzEyMmNmOWUwNjIwNTFkZDAwZjJjYTkyZTAxMWE2Yjc5MDNjMThiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVGa2taMGVqMVA0ZXVQeFJJbWM1RlE9PSIsInZhbHVlIjoiWm5raFlRbFpzcmVDdXBZbTFNOGpDekw1eGNrU3RoUlA2STM1UEJ3U0RYRFJsQ3ZhV1BGaHZiV0k4andYZlZCMVp3a3d6L3NnaXFMU3duZHQwRFgyZllLT09aV0NtU2hYS1BqTUNsTjUwaVh6Szk5M05abDNCT1JrRkYyczlkczBZSzViNWVaVktWRUZ5Y21aZ0pQdkc1RGlXSWxrTS82WVdlNWNhdkhIZjB6YW42VVdrSjFJclVBZlJpV0VYZXZ1RkxZLzJSVVZhNCs1blByTEFNNWlreXcxQzBLMlZrMHdhRG00dEpRVXhRZjdsSDZLdlRyM1FVMHBhZlNwa2N1SzdpMVJFaTdaNjBKa1Yyank5aWdJVVIrNHhaR2hKc2pNZ3Nxem9sREZQS2ZIZ2JmcnpmaUlCNHNNRGxoZXZIbXF2MFFNZitDbS94ajYvYzN0RGhqU0NOQ1g1ZjN3N25ucnJVaWdKUERGdlNJY1BSdzJLM1AzOEh1MjczSlR2U0dweE1qaExmN1NqUzkrNkdGaEphdWdmdStoMG9aSzE3WU1jOU9mVE1hNnhJSnNiU0VrQkdxV1pnaFJGdzQxb0s5MzUwMGRmaEx3Ui9kaEdtWGVwR1lIcHNremwyNUZMaUlCems0ZkxkZUd3SEZtWGpkYVBxWWI2SVp5OWdNeno1ek4iLCJtYWMiOiIzM2QzOWM3NjdkOTMzNDUzZTcwYzg3NzM4YjZiM2IwMDgzYzIxZGFiMTJlNTU4NGVjNzU2MjgxMDg0YWY0ZWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436845728\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1441434697 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441434697\", {\"maxDepth\":0})</script>\n"}}