{"__meta": {"id": "X2e4251f86efd1c8b925ce0269f6659e7", "datetime": "2025-06-08 12:56:00", "utime": **********.858718, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387359.457816, "end": **********.858754, "duration": 1.4009380340576172, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749387359.457816, "relative_start": 0, "end": **********.692579, "relative_end": **********.692579, "duration": 1.2347631454467773, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.692597, "relative_start": 1.23478102684021, "end": **********.858757, "relative_end": 3.0994415283203125e-06, "duration": 0.16616010665893555, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45053384, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007809999999999999, "accumulated_duration_str": "7.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.779584, "duration": 0.0057599999999999995, "duration_str": "5.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.752}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.813739, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.752, "width_percent": 11.524}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.832812, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.275, "width_percent": 14.725}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1210119290 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1210119290\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2016919099 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2016919099\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-385260090 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385260090\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387338613%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhJYzBvSDR6NExmekNrSjJ2N1FVTWc9PSIsInZhbHVlIjoibURxbXk5UzBOSmNjQm80VHNBelpDZ1hIMVpXRm15ZFV2M2lqN01QNkdmWVRLa2VRK2I2RWJiM3BPY0Y0dVllMGVVbjZnNWVEUEFJbTcyTHdmR1ozYUxnb2E4ZDRlM0hSR2Rhdm1ySFk3UVdZWmo1NkpSVEo1MWZFV05ESUZMT0tUeGh3K21qVFRuTlFCSXJCcVArSXladlRmOThiZG9QeUp0K1M0OTcrc2dkNFI5eWlIT1pWWk5zK3ZGdFhRN0hQMUZwM3o3YWZVdHBwOHNndGRERi9STUJHcDdrRWRUMTRsTjJjN0JoS2s2YkRFTG1xSWVpcGVOekptN3BwRWVTZ0Nua1VJT2hvZWtPZDAxaWpXeDFUVCt4T0t1dUZhZVJUTnRNb1Rwb1EyTzhsUGQrWS9vY25JWHlYTTkyNlQ0NXI4UjVKMTdiNUw4ZHY0SllpSlFTekRxZjcwTkNGZThYaDZUeXJ3V2hla3RYSnh4b05jTW85UitRbkI2MnFRbXEzalZlN3UyQ1l3bjRMNTFRd0h2QW1OM0MySktzMWZrSmJydjAyOGFlcFdnWjhXSjBZM1NiNjZIR1BycnZRVzcxTGZ3bWtPMll3R1FYKzEwL3BtU2kyeENDQWtndzBTZ25BeVZUYkEyYlMyTDNyV1BXWktVY1h0Ym9rakhRT1phS3IiLCJtYWMiOiI3OWI0OWFkM2ViMzQyMTk4OTAzZGNhZTM5N2JmMjQ5ZWQ4MWQzYjI5MmQwOGYwMzQ0ZGZhN2RjNjE3MmE0NjAwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InphZFBHRjc1NVdPdjBickxXN1ZLbGc9PSIsInZhbHVlIjoiZkYwWE9QMXlnREh2Qi9uYS9aZXBDRk9kKzRYV0ozNG1QNi9aNm9YTW1VaTJPSUFhQlpUVjFwam9leUpQWHFzRkxtdEhKN3NkRWU1UUVLUzQ5V2Vma2NVQ2N4SVdTRDRicXBDN3lnQ3AyUWkycndEU1hpZ0NFeUkzbHlFSWN6UmJqS05TZHZDM2JWdjNObFVpdGtPUlREa2EwcG9uaTl2VmQxdERSLzJTeERraVVoVnllbnI4STk0UzF6bmo3Y3hPY1NMQlluelJBQ0xkUHI1YmNzOFB1T2dNY29iTzZmRnZjbDkvWGNSWFVYSHVLOE5IRjJEVTJCQTk3SG9TdCtFeStDd0JwTHRySjcyQm5COXpYQmE1c3lYQWVodWl2ZEtyamxtRjV4QUhQVnFLWkV3OUlyK0dscGs2bE9kNnFwMUdDcm1nM0tZU1FtZUZlTnJxYjFKS1dranFUSVQvUzIweTB6TklXWlFhR3BWUk1TY21ySXVrby8zYzBzMXU5YUx3ZUdVbDUrM1RYdUYxWFhmeWV5NjI2M1lnL3JSdUNPcFFKcDNLUmdLaGdEbi9mK0VDTllEWXNicVVVRnJZOCttZ0JkN0FMaS9iZ3YydjcrbTlaZEpxcjYvU1dIMmxFcWgrTkdxS1ZSVFpvdnhSbEltM1k2a0NBSFVaVzZVeElUd3giLCJtYWMiOiJjMDA3NzcwYzA1MGUzZjA2M2IzNTIwMWE3YTQ0OTY5MjY3NTY4MTk2ZDAxM2E2YWU0OGMwZTU5OTkyMWJmOWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-125889930 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125889930\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1941904586 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:56:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InE3RWE5N25uMDA1NHV6OUpOY2lCYWc9PSIsInZhbHVlIjoiTDR4OVI4WUY2T3pIL1JtWXA2SXNBL3VPR09WYmdvZGI3eHlmcVB0OVhkNGo2QVBhNTkwWFgvSHExQjhieDF2Y1A4WFFZaTF3V3Vuc3BCS0MzTEVzZEVsWUNzaUtMNXB4dS9rRDZFa00xOTlFbVViU2FJSTZGK3RCNm9NbUgwYnlMa3QvK3o5WHFMZzV0V29BU1cyUVNnY3ZLOXNLdFVSQ2JiYzR2eHlzaHdTd1BQaUlsS2pTYzEvV3ZrZUFRQURycE53THdwNG9WOWpONWpnRnd6b3JZUlpyRm5YVUlhUlFpbkhGdDJUVjg3WUpBeWtDOEtuckd2QktTaGI5cGw4NGxqelo0MmJzQnNGbkpKWjgzODdwaUVxZWJtaHorcENXbUcvUXh2dVdtSlZubTcwOERIa3BTcHV6VHBRV3RWWEg5SzZ5Yk1LY20wMFhkZFBvMkZNNHRNQTNWZC9ZM1V6NHRnaWw2QmoxZ0QxbWF3YWZmN2lyTVZ5dStXZEJML2x2c0VJUzIyU3VBNTgzUGErSDAwajJEdG9EVG84QnZEYmVVMURmb3BWVVMxdEJ5NUNWeGZvbkdONDh5S04wTWVGTFpsU0VxODNYV004Tm9PRm1Qc2xrb0pNL09xS05ucDZnRDIzMVFyVll5ZkZWbnhEZUI4QjdsZldSQmFlNlhVQkEiLCJtYWMiOiI1YTI5ZmI5N2ViZjU5NjBiNDkyYjM3NDcwODY0NDhhOWEzZTZlMDU5NGMxODFmMGNmNjU5YzRmMmZiMmQ5MGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpsL0FuRE1VQVVBL2lDMSs4dTJDM3c9PSIsInZhbHVlIjoibTJ5UXVsVDYxaTgwa3NKQmZvV3BERzZXd25vVjJzZ01DNk0xVTBzQ0ZiQXBZSkV2aktHYXZOcTk4Y1VuUm9VZkpCcDg1YUV4YkxEam1aTi9NdmZSK1Mva2JuT1hhYTIwZUZuWUw3MEYxVGVBd0Q3eGFKa1Y5VGtyeGNLL0lWREhOSjZPS3MvTitmVzBhZ3JGYTFneTdmeEpKbU1qdW5IdUJHNXFUQitEZ2hoeVZVaGpxNGZiQlEwTGFGRVVLS2IvbktwVTJtczh4VXVIdDVXZW5rc2ZpT09mOUdtbEZSK3QvNDNEL3hDdzBPWGRRTWtiSi9UNFZWa3lXQ3BTRmc4V1VCczlpVUwzV3dERkpNM2VXVGhFRHF4ZE1NRndmSWZXTTFBcnRMcGg5RExWYllGT3gvQ1ZsM1FhaGgyMHgrYnZvRDZWdEkwRmZheVNSWDFOZlRGVlE1dU9BS01haEJmdGlCRUFqbnUwQTVpajFGYXo0Wi91Rk41RGVBbWI3bGdON245b1VGTTJncVhOc0lRdElZdFRxMDhWYUxPVTQ3dXh5cmNPcXpBMkRsU1pTNnB4ZkdTaU5MZnphVlVYK1lmdWFZUWcxYXdseEJud1V0UHc2R1Y4NGtlSTJuUnVETldvOU5yQi9xN1hYc3N5Z3E1Qmx1eWdIZG5tdWdNSzVENTYiLCJtYWMiOiJiMDY1YTZiODQ4NTVhYTA1OGQ4NjcwNTI1OWI5NmQzZDA5YmU3ZTllNmJmNWU3MTdhN2I5M2U5OTYwZWQxOWY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InE3RWE5N25uMDA1NHV6OUpOY2lCYWc9PSIsInZhbHVlIjoiTDR4OVI4WUY2T3pIL1JtWXA2SXNBL3VPR09WYmdvZGI3eHlmcVB0OVhkNGo2QVBhNTkwWFgvSHExQjhieDF2Y1A4WFFZaTF3V3Vuc3BCS0MzTEVzZEVsWUNzaUtMNXB4dS9rRDZFa00xOTlFbVViU2FJSTZGK3RCNm9NbUgwYnlMa3QvK3o5WHFMZzV0V29BU1cyUVNnY3ZLOXNLdFVSQ2JiYzR2eHlzaHdTd1BQaUlsS2pTYzEvV3ZrZUFRQURycE53THdwNG9WOWpONWpnRnd6b3JZUlpyRm5YVUlhUlFpbkhGdDJUVjg3WUpBeWtDOEtuckd2QktTaGI5cGw4NGxqelo0MmJzQnNGbkpKWjgzODdwaUVxZWJtaHorcENXbUcvUXh2dVdtSlZubTcwOERIa3BTcHV6VHBRV3RWWEg5SzZ5Yk1LY20wMFhkZFBvMkZNNHRNQTNWZC9ZM1V6NHRnaWw2QmoxZ0QxbWF3YWZmN2lyTVZ5dStXZEJML2x2c0VJUzIyU3VBNTgzUGErSDAwajJEdG9EVG84QnZEYmVVMURmb3BWVVMxdEJ5NUNWeGZvbkdONDh5S04wTWVGTFpsU0VxODNYV004Tm9PRm1Qc2xrb0pNL09xS05ucDZnRDIzMVFyVll5ZkZWbnhEZUI4QjdsZldSQmFlNlhVQkEiLCJtYWMiOiI1YTI5ZmI5N2ViZjU5NjBiNDkyYjM3NDcwODY0NDhhOWEzZTZlMDU5NGMxODFmMGNmNjU5YzRmMmZiMmQ5MGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpsL0FuRE1VQVVBL2lDMSs4dTJDM3c9PSIsInZhbHVlIjoibTJ5UXVsVDYxaTgwa3NKQmZvV3BERzZXd25vVjJzZ01DNk0xVTBzQ0ZiQXBZSkV2aktHYXZOcTk4Y1VuUm9VZkpCcDg1YUV4YkxEam1aTi9NdmZSK1Mva2JuT1hhYTIwZUZuWUw3MEYxVGVBd0Q3eGFKa1Y5VGtyeGNLL0lWREhOSjZPS3MvTitmVzBhZ3JGYTFneTdmeEpKbU1qdW5IdUJHNXFUQitEZ2hoeVZVaGpxNGZiQlEwTGFGRVVLS2IvbktwVTJtczh4VXVIdDVXZW5rc2ZpT09mOUdtbEZSK3QvNDNEL3hDdzBPWGRRTWtiSi9UNFZWa3lXQ3BTRmc4V1VCczlpVUwzV3dERkpNM2VXVGhFRHF4ZE1NRndmSWZXTTFBcnRMcGg5RExWYllGT3gvQ1ZsM1FhaGgyMHgrYnZvRDZWdEkwRmZheVNSWDFOZlRGVlE1dU9BS01haEJmdGlCRUFqbnUwQTVpajFGYXo0Wi91Rk41RGVBbWI3bGdON245b1VGTTJncVhOc0lRdElZdFRxMDhWYUxPVTQ3dXh5cmNPcXpBMkRsU1pTNnB4ZkdTaU5MZnphVlVYK1lmdWFZUWcxYXdseEJud1V0UHc2R1Y4NGtlSTJuUnVETldvOU5yQi9xN1hYc3N5Z3E1Qmx1eWdIZG5tdWdNSzVENTYiLCJtYWMiOiJiMDY1YTZiODQ4NTVhYTA1OGQ4NjcwNTI1OWI5NmQzZDA5YmU3ZTllNmJmNWU3MTdhN2I5M2U5OTYwZWQxOWY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941904586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1200145978 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200145978\", {\"maxDepth\":0})</script>\n"}}