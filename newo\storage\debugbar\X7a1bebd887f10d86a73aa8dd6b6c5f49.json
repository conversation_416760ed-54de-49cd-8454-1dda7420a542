{"__meta": {"id": "X7a1bebd887f10d86a73aa8dd6b6c5f49", "datetime": "2025-06-08 13:35:16", "utime": **********.611798, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389715.29937, "end": **********.611826, "duration": 1.3124558925628662, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749389715.29937, "relative_start": 0, "end": **********.48798, "relative_end": **********.48798, "duration": 1.1886098384857178, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.487998, "relative_start": 1.1886279582977295, "end": **********.611829, "relative_end": 3.0994415283203125e-06, "duration": 0.12383103370666504, "duration_str": "124ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43918496, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0051400000000000005, "accumulated_duration_str": "5.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.580652, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.463}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5940402, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 83.463, "width_percent": 16.537}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-691009272 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-691009272\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1393190303 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1393190303\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-525749434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-525749434\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-62297422 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllqNjRVZmc5WlZPazI5SjkyVHNhdEE9PSIsInZhbHVlIjoibEhrblpaVDJqMjFwNmYyNzV0WlAzL0JPN2pIRy80R0FYMEtFajdHRHlvL1lEVlZaUHQ3bmYvaWcrM1VCYk1jSUN1ekFuZTV5ZlRlOWRvWER4NlJTSGl3ZUJZVktHRW53L1hOUmdiaUgxK2FBNkxyNldxUnlDN2pDRGRKcHhXcmE3ajVHWjlRYkVVSmRqeU4xL1hYRDJkdUphd09FRHRhMEJVRDVqYldROVc0ZnRwbDNoMWZXSzhBckVidnFDODNGeEgySVhHcmNlMjdJZkU1ZkZ1Y2JYYy9saFdZRUZ6SVlnL2pVSjc5VTZja0s0bHFqSjJTLzF0TEdVeDk1TTU3ZjRZZ09ONXgvSDNxZVN6V01OaS82dnYrMG9YRzRZMFNSUmF2a3AvQnZJbTlseEd1clZlZU9ZMUtuVjdXSUlBWSt2ZndQWGE1bVR6ZmRVSDBVUTJkN2h5VHpESVFIRHorbHIycEFDTVovUFZJL3RnTVJnbkZOd1B2cnhwK01GWUtLVnA1RjRuR1ZVbjNqNGJMM0lDdGx4VFY3NmFSUWdRSjNvVnhjRDIwSGptam5mbHphUmJZbmczd0pZd1h2enJqd2NvOENldVdTNktGS2VVM2h6M29aejdsVTdrOWtUSXBDT0ZLdHVrdkVORlFQaWJnaDVRcXplV2k4SkFETTIydHkiLCJtYWMiOiI2MmE4N2RmZDZiZTJkMjhlZjYwM2RiNWQ2NDkwNDgyNmM3NzY3NGQzODU1NGZkNWFiZjllMWNiYjQyNGQyYjA5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlI4YzlpaTRmQXJMS2syUzg2aUo5bnc9PSIsInZhbHVlIjoiaklibDFacEpnZ3F5ZXN4RzI2RDRzbDBqUnlGaENVUDFCd3RGZW9wZ0RqYm43ZnRlQnlSUUVIZHV2MDVhUHVueWV6ZDkzNGVqVHMycVAvR2Nmc2RiMXpnVlFHYjl5U0dkQzBrdDVqTHJMRHpIbnJjNlo1VDlwaEozWnBkK1kwVU1MbjFORWUrMHdpZFBENEVMSnhOUUE2SkJDNXNGeTVNOXl1TE1nMGN2SzlZektqMXZDMkVSUFQ4TS9NSXpjL1lqM0hoeDk2WUExampXRndrZzNwWFlQWUlSRFlBN0JFMXdjOGFnV1BsakJCWjJRNGJ2MEoyYndOdUJUZUNTZURuYVg5b1NWZ3ZvZlhNaGdOM2NmbzNxVEtFWVlHWW5YYi9YOXVXNDMzL28xeEU2Wm52U2VJWCtuOXZVTTNtdXhPUm1TRGhiNG05Z3RVd1k4cWUvR3J2LysyTEpwWS9yU2dxSVhCdm0vU055QnR5dmZvOHgvTFR6NmFNSG5GYkQ2NHJYZVA4RHpwSTlETjNqeGlPWWNzSnVIVmVndkd4TjJVVWExY0txMEtoVmw0b29WQWcvWktjSTBRYmxzOWt3dXQ4NWZjUndoWU5RQUt3ZStBT0F2WW5qTGxHZmxyemJGM2JnVjdMbU1pOUx4MXZWdEVSd3dPNlg5djd5MHNla3V5NzUiLCJtYWMiOiI2NWFkNDgzNTQ5NGE3NDk2MDNmNTkwMTgyNzVjODcxODIzMmQ2YWQ0NjY0ZmExZjAzNWZhM2Q4MmNmMDI5MzBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62297422\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-370757415 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370757415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1842962455 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:35:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNTQTkrTElLaXdDcDY5MHc5b1RmVFE9PSIsInZhbHVlIjoiait4bVRYQTdvOHRxMWFQMHJ3ZXNteWFIYzRWQ20rZUZjbE40UEgxclFiaWs3VHQ0dEJUcDFDaU90eUh6MHU0Q3dKTGhGYVRCSjVaM2NJQXk4T1Z3Q2FtenordnVKYmRET3ZZUnp6R2w1U2xVNHNsTzVWdklveGNwdkdhdWEwZkIrWUtoQ3g4d1d0c0RGdytkaTVocFJwRWx3b3lreXNFZ1M1NGg3SCtSeHBtTzNTR0xyRTFTaXRmL2F0bG9GZUdFcm50aGRiYSt3UzBhenpqeUxpWk1GNkh2WWNOMjUwNzVBQXlmUHhCRmk4Q1ovZnJMbEhJQytDSWNmZU02TVc4N3pCTnRqR0ZzcnhLaEFIS3NZZ0pZbDhvOGs3L0xMbVlFU09wZTJnTThNWWdyYWNzMWRDWjk5aVl0OXVxN3Rxa3hhUFc0NEJrTWdPSVRMZVd4bExSelVRL2cwWjZnUVFKZk1kV2w1QTZ2MTNjZXZjaTRIUDRITUIzd2VaWi9IK3NrN2IwalNtNkVwdnVOY2YvUzF1OHMvVUxOcURxTklPVFI1RUtKS2xQaGowVTN2cnoxN1RKakxKRnFnZnBZUVRLc243MmVqK0IrRXlrN2ZEQ2dmR1ZKN0daaU4vdFhJUGt4djRBM1AwcmVJbTVIRktKZWlqTVNiQmExa0RPT2F3RXEiLCJtYWMiOiI4Y2ZkMmU5M2RkOGJhZDlkZWM4NTYzODUxODJjMWU3ZDQ1NDQ2MDQxODQxMjU5OGZiZDlmYzlmOWRkZWYyNGZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndEcTVCeXRBV0MwdndyR3JReW03M0E9PSIsInZhbHVlIjoiL1BtTmJkZ055c2V5SlA3UVZITjIvNmtaVklNdHdKYWYyMnB1dGRlY3hDWDFpKy93UzAzYVN2MlhtRDY0VFlhZmJtY0RMS25sVm1WZVVqZjRsZnA1RFFVMCtUcVVPR0JKTHBkT0ZTYy9VVkJtRCt6R2cvblIzTXRXakNnb3hHbEFycWxkdURqMHozNmwvWkFSRnp5RzVYZjRPMWl4NkNDK0kyNVJTVFZScFVvcDdIM2FmcysxNlRRN0w5V09Zd09ORnRkZmNkYjFkZWFpc3VMS2JFSzRNSS9xQndCaG5UYVR6WEdLTkRUZE1ZV0VlRmc1VnI3MUhuQ0lDSThPZEptd24yNUtNeDE5YmtVb3lnSmNOMW1mV2V2NEIxOXF5a1laSE82T1ZlUEFadHFXODRZcWJucFVOa09XbnZ0V0ZPQ1l5a255dDR0b1BXbk5LRWMxVXIxY2xvQ3U5RGVCWHVUL0FxeVZ6OFZLV28vbFJQMHV1T3YzNlQ5OUNvTGVaNXZxU24vV3JKSUxENXZQSSs5YjNLWEkxbXFNbGJFRU4xUGpXOC9qd1NDRWVWWUFUcmQ0cUNaRVdsVFV5VGJxL3dCbFpLbEdDVDJhZ0xhMDRKcWtFVE9jNDVmd285Zk13Kyt2bmN6WlFWSHBGbG9sOWZSZTAvNnYwYWYvN3RHT2MwaDIiLCJtYWMiOiI3Njc2OTZjMTRhZGE5NzI3Yzc5MzliMTI1ZTczMTVlMDA4NTg3M2NjZDBkZWJmNWU0YTgwM2ZhNTU5Njg1ZWJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNTQTkrTElLaXdDcDY5MHc5b1RmVFE9PSIsInZhbHVlIjoiait4bVRYQTdvOHRxMWFQMHJ3ZXNteWFIYzRWQ20rZUZjbE40UEgxclFiaWs3VHQ0dEJUcDFDaU90eUh6MHU0Q3dKTGhGYVRCSjVaM2NJQXk4T1Z3Q2FtenordnVKYmRET3ZZUnp6R2w1U2xVNHNsTzVWdklveGNwdkdhdWEwZkIrWUtoQ3g4d1d0c0RGdytkaTVocFJwRWx3b3lreXNFZ1M1NGg3SCtSeHBtTzNTR0xyRTFTaXRmL2F0bG9GZUdFcm50aGRiYSt3UzBhenpqeUxpWk1GNkh2WWNOMjUwNzVBQXlmUHhCRmk4Q1ovZnJMbEhJQytDSWNmZU02TVc4N3pCTnRqR0ZzcnhLaEFIS3NZZ0pZbDhvOGs3L0xMbVlFU09wZTJnTThNWWdyYWNzMWRDWjk5aVl0OXVxN3Rxa3hhUFc0NEJrTWdPSVRMZVd4bExSelVRL2cwWjZnUVFKZk1kV2w1QTZ2MTNjZXZjaTRIUDRITUIzd2VaWi9IK3NrN2IwalNtNkVwdnVOY2YvUzF1OHMvVUxOcURxTklPVFI1RUtKS2xQaGowVTN2cnoxN1RKakxKRnFnZnBZUVRLc243MmVqK0IrRXlrN2ZEQ2dmR1ZKN0daaU4vdFhJUGt4djRBM1AwcmVJbTVIRktKZWlqTVNiQmExa0RPT2F3RXEiLCJtYWMiOiI4Y2ZkMmU5M2RkOGJhZDlkZWM4NTYzODUxODJjMWU3ZDQ1NDQ2MDQxODQxMjU5OGZiZDlmYzlmOWRkZWYyNGZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndEcTVCeXRBV0MwdndyR3JReW03M0E9PSIsInZhbHVlIjoiL1BtTmJkZ055c2V5SlA3UVZITjIvNmtaVklNdHdKYWYyMnB1dGRlY3hDWDFpKy93UzAzYVN2MlhtRDY0VFlhZmJtY0RMS25sVm1WZVVqZjRsZnA1RFFVMCtUcVVPR0JKTHBkT0ZTYy9VVkJtRCt6R2cvblIzTXRXakNnb3hHbEFycWxkdURqMHozNmwvWkFSRnp5RzVYZjRPMWl4NkNDK0kyNVJTVFZScFVvcDdIM2FmcysxNlRRN0w5V09Zd09ORnRkZmNkYjFkZWFpc3VMS2JFSzRNSS9xQndCaG5UYVR6WEdLTkRUZE1ZV0VlRmc1VnI3MUhuQ0lDSThPZEptd24yNUtNeDE5YmtVb3lnSmNOMW1mV2V2NEIxOXF5a1laSE82T1ZlUEFadHFXODRZcWJucFVOa09XbnZ0V0ZPQ1l5a255dDR0b1BXbk5LRWMxVXIxY2xvQ3U5RGVCWHVUL0FxeVZ6OFZLV28vbFJQMHV1T3YzNlQ5OUNvTGVaNXZxU24vV3JKSUxENXZQSSs5YjNLWEkxbXFNbGJFRU4xUGpXOC9qd1NDRWVWWUFUcmQ0cUNaRVdsVFV5VGJxL3dCbFpLbEdDVDJhZ0xhMDRKcWtFVE9jNDVmd285Zk13Kyt2bmN6WlFWSHBGbG9sOWZSZTAvNnYwYWYvN3RHT2MwaDIiLCJtYWMiOiI3Njc2OTZjMTRhZGE5NzI3Yzc5MzliMTI1ZTczMTVlMDA4NTg3M2NjZDBkZWJmNWU0YTgwM2ZhNTU5Njg1ZWJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842962455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-389337262 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389337262\", {\"maxDepth\":0})</script>\n"}}