{"__meta": {"id": "X7630673417126b597e85bcc3d3393ed9", "datetime": "2025-06-08 13:00:14", "utime": **********.679461, "method": "GET", "uri": "/receipt-order-warehouse-products?warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387613.313742, "end": **********.679489, "duration": 1.3657469749450684, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749387613.313742, "relative_start": 0, "end": **********.479593, "relative_end": **********.479593, "duration": 1.16585111618042, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.479614, "relative_start": 1.1658720970153809, "end": **********.679492, "relative_end": 3.0994415283203125e-06, "duration": 0.19987797737121582, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46009480, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET receipt-order-warehouse-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptOrderController@getWarehouseProducts", "namespace": null, "prefix": "", "where": [], "as": "receipt.order.warehouse.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=106\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:106-144</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.03226, "accumulated_duration_str": "32.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.57424, "duration": 0.02686, "duration_str": "26.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.261}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.62565, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.261, "width_percent": 3.41}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.635725, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:121", "source": "app/Http/Controllers/ReceiptOrderController.php:121", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=121", "ajax": false, "filename": "ReceiptOrderController.php", "line": "121"}, "connection": "ty", "start_percent": 86.671, "width_percent": 4.154}, {"sql": "select * from `product_services` where `product_services`.`id` in (3, 5)", "type": "query", "params": [], "bindings": ["3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.65054, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:121", "source": "app/Http/Controllers/ReceiptOrderController.php:121", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=121", "ajax": false, "filename": "ReceiptOrderController.php", "line": "121"}, "connection": "ty", "start_percent": 90.825, "width_percent": 5.394}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.658358, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:125", "source": "app/Http/Controllers/ReceiptOrderController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=125", "ajax": false, "filename": "ReceiptOrderController.php", "line": "125"}, "connection": "ty", "start_percent": 96.218, "width_percent": 3.782}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/receipt-order-warehouse-products", "status_code": "<pre class=sf-dump id=sf-dump-1885880674 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1885880674\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1836744416 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836744416\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-259790392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-259790392\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-191819370 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387610576%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im4ybFliQis3WFBDZ2Q3ZDBpYVozbnc9PSIsInZhbHVlIjoiK1J0QlVaUURsODJFTzdQdnVtaXJWelh3cjdtQkhId1JhUEJpUnZhbVVDL0JBWGgwV2t4aXpQTWNoV0tiTy9YblRiVVhOVE1jdDdHSHAvOVMydUtwSE5RalphTE1xS1dIcGtIQlNIRGNUS1E3enVLSDlrYisyQlgzMXptS1dYeWlxVHh4TE52UDhCUXEwaDkrRFZVR3MzcXJ5anZqVTM2ZjdYNng4NDhIdDV5Tmc1ZzFoMGFZSlFMaWdhbnoydmJzUjJzY1FmSHhZZ0FJdHh0M011dTlpYVFwSkxaVS8xajl5bHBVWitRNDF6REdEeXdmVGErbWh4QitMa3h4Tmw3QWdDb0FRblg0TVBZRGFlZG54V3lOVUhGYWE4ZGJtUlhSUVVNMVJueEJXcEgwQjBjaUp2dk5Lc0hsdXlFeU1DeFhKNWdzUkpYVXZ3amJLUGVZVm4wZTdQS29uRGVkU2NXVnJJT05jMFRtSmUzcFIwMzlDRVFxMmhleG9ncjJXdmlzdXlnZCtUL3ZmVG05NE9HRUtUWUg1Z2xxMFI0b0ZCc0J4SmJ6QWsycWFQcElTc1ZoSEFqY0I2dTAzK2ZhZTh1MUVzMnNkcHlTSW1raUZ1NUtIZXVJbmpicnNINVUyeWsxaFd1NEtYWGRXZlV5a2VuZWllN2V5ZzArUjRuRGtrVUgiLCJtYWMiOiI5MzZiOGQ0ZTQwNTZjZjExNWJmNjk4NzAzOTZlNmRiNzRhZDgwNTc4ZmZlOWQ1NGNmMmVkOTFkZjk4NzkwYjA5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjgzSnNaMklJcXR3R0p5OGJONEZOeWc9PSIsInZhbHVlIjoiOUg4cnA0ZFlTcjNjYlFYSVFvaWd1OG04OTBraWs2UjJSL2FTaS82c1U5aHd3VXpUa1Z3Z0VjaUJ5blJzVnRYZUNaZGlqeThaZnUvVXJVRll5UTl2eEMwYzVycTczK0lraEZPUktYWWxGVkpnREpwUGJldmlNOTVBd2x2SE11aWkrS0VRWWRhZ0NvZTFDYTFXZW5FOWswN0ZWQWJzNm1YTjFGaVlyblZyN2FHd2pDN05DMEVubUJYUTY2Y3VPK2dqUEl3dG9YaHJCTEhzYVlYOWs4VzV1L3czUnNybHltUkVEamhjYVY4L1FRSzZqV0IvQzRuMXJndVJjc2RkSWp5RGFLVS9RUnJpajJUSlFVYVlTc292akREQ1lIY0NKTnBDOXZhcENzc2M0QUY2MFVqQ05mL1BORkNHdmFhanc5ZUlrSlMwejBRdFVOSWtGY3FsdlBTT0JhWWs0NThBdFlGTmtvYkpzeHY2YzQvMDBldFNOclJ4OXB2NjJteS9lU25JRDZkdmt0c0JyRERxZ2tuUnIvaE1vLzR2Qmh1TmVabkNSV3dKV3RYZ3VYTWRRU2htMHJ2WlRVQXFUbkR4S1RBL2l3bkptVXQ1REF4ckEvb0xMRE1DZEc2aTgzRkV2SURVSExvRVFMVHZiRXpVbVIxeWhWRjd4aXBBN3Y2d1FEOHkiLCJtYWMiOiJlODhlMjczZWUyZDlhOGE3YmU5NDNjNjJhNTg4N2EwZTk5Mzk5ZmM3OWI5YzA3Y2M4YjcwYWNiMjRlMjRjYTM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191819370\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-230699480 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230699480\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-73108191 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1DbUlRZWtpYWdZTE5SWm5LdXdES0E9PSIsInZhbHVlIjoiZHd1eVdXSHM5cUJieExnM0JVTkJIcEdMWnNFaGtpdExQZVA5SFJvYStWeTB3empBelZrekwyV2NCRFpHRzJ4M3lLSGsxS3AyS3gydXlOb254Q1YyY2ZQMm9nV0s4TGRyR0phemRqVmx3b3NaUHZ3THErNGdQcGdteUp4MWp4SStEbS9vbllqMmJaQkNGbndwS080RGxSZkt3UzcrV2toMGNDUVRFTmVpdkdvRXdkR01jSU9ZOUNmNlV1dGZOajMyYlpSaVRrT3pGVURHRlgwK0ZHTHM3U2pkb2kvSE54SjJGeEIvYU56SnhNbXFBSXFaK0FKZmh0ekNna2FvZjZIcDdka0lqVjE4em5DT0R2SHUwdkNnYUFtOHdFbjBoRGx4Y0NWRXZaUTZBajJJT3AyT1lTY0pxNEM1bmE5R2llTEtRemUwakxvcGhuYlBXWlhjSDFqSzA2eTNMNmNHd3pPa2pIeHI3RGE0L0UwZHpLemJZYnBTWkxNRVAySlFVcWczV051ODlPZHpsMG1EUGVBUHU1NnJDcmwvYitnZGNtME9RcDFDUkRnaEVEa2pJdC82SmJVbjFQUHZlQVh2eGNJQ3o0anpzMTRyRXp5enQzSHQzQlBwbDRFWWhVMVl6ekxqSXlzZkJUSlQ1T0R0UCtsNndPVWNrNU9sc0FlREUxQVEiLCJtYWMiOiIzZTk0ZDViZGFiODBkMzFkZTlkYjM3NmE2YzBmZWQxMTFhNzBjMjY1MmNjMWVlYTkzZWNhYjQ3ZmMyODgzYjAzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtkVGZZU3l4OG1pNEt2dndUM21xSHc9PSIsInZhbHVlIjoibmlBWnQ1NU5BOExqcHJzYmFLTG1ZQmxpdVE2SEFDNFZ2OGxGemtTZFc5NWZiMTZldG1neXR2R1JGTUR5L3doeGFRdUpYTVJuOHN0N2M4bXQ0cGtwcVU3U3Vwc0ZYSmRVeW5CWnRaVDN1dU83bHFXL04yZ0xFYzM5Y2lpNERPQVBlSERTbytITm5VWi9CUWd5bWRYdE1FNXBVTjM2dm03M2pLWTRPM25RMnh5T3FkcUVqQnp2Y3pUQ0FsZnpYZ2JaaEIrbmdPeVdkTmx5YVVkUmhCQTBEdG8ySU1Zd1had3NRektVNXRFMWx6bUxpRHBFMGJwQzloNlFXTXpVa3o3NVpSWkt3WVE3OUpCTHpxOTNWLy9YVVJiT0h1VEZYaFRPcWlPMG4xL2hTTmk1RzBoeUJBenFxajYzMEdJTTlsSERtWjVWV1hlTnYyWmRreUlicTN2RGpYLzBvS3pKV2Z4bHVnaFlYOTUzNnVaOFhrbmhydXhIN05MWVhibk1WVklCR3VkZ09TeXZFUXViSUFaamk3aG1LSmcybFdaYW1OMUVnUWo4RTVTeVNlNzZHMk5JNjF5M3libVYxaWcwYWZUZkJmWDFhV1QyeDJWMm1Famd2cjJkam9UMDFjSUQzK0UvWjgwR0ZvWWxZa0l6aDBrYnNFQ3dxTFRISStFT0FSckkiLCJtYWMiOiI5ZTRjYTgyMjBmOTUyMzhjMzBmZmI3NjdkNzdlNTc4ZWQ2NGY4N2I5NGQ3OGY2NzY2OWM0YjE4ZGE3MTM3MjVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1DbUlRZWtpYWdZTE5SWm5LdXdES0E9PSIsInZhbHVlIjoiZHd1eVdXSHM5cUJieExnM0JVTkJIcEdMWnNFaGtpdExQZVA5SFJvYStWeTB3empBelZrekwyV2NCRFpHRzJ4M3lLSGsxS3AyS3gydXlOb254Q1YyY2ZQMm9nV0s4TGRyR0phemRqVmx3b3NaUHZ3THErNGdQcGdteUp4MWp4SStEbS9vbllqMmJaQkNGbndwS080RGxSZkt3UzcrV2toMGNDUVRFTmVpdkdvRXdkR01jSU9ZOUNmNlV1dGZOajMyYlpSaVRrT3pGVURHRlgwK0ZHTHM3U2pkb2kvSE54SjJGeEIvYU56SnhNbXFBSXFaK0FKZmh0ekNna2FvZjZIcDdka0lqVjE4em5DT0R2SHUwdkNnYUFtOHdFbjBoRGx4Y0NWRXZaUTZBajJJT3AyT1lTY0pxNEM1bmE5R2llTEtRemUwakxvcGhuYlBXWlhjSDFqSzA2eTNMNmNHd3pPa2pIeHI3RGE0L0UwZHpLemJZYnBTWkxNRVAySlFVcWczV051ODlPZHpsMG1EUGVBUHU1NnJDcmwvYitnZGNtME9RcDFDUkRnaEVEa2pJdC82SmJVbjFQUHZlQVh2eGNJQ3o0anpzMTRyRXp5enQzSHQzQlBwbDRFWWhVMVl6ekxqSXlzZkJUSlQ1T0R0UCtsNndPVWNrNU9sc0FlREUxQVEiLCJtYWMiOiIzZTk0ZDViZGFiODBkMzFkZTlkYjM3NmE2YzBmZWQxMTFhNzBjMjY1MmNjMWVlYTkzZWNhYjQ3ZmMyODgzYjAzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtkVGZZU3l4OG1pNEt2dndUM21xSHc9PSIsInZhbHVlIjoibmlBWnQ1NU5BOExqcHJzYmFLTG1ZQmxpdVE2SEFDNFZ2OGxGemtTZFc5NWZiMTZldG1neXR2R1JGTUR5L3doeGFRdUpYTVJuOHN0N2M4bXQ0cGtwcVU3U3Vwc0ZYSmRVeW5CWnRaVDN1dU83bHFXL04yZ0xFYzM5Y2lpNERPQVBlSERTbytITm5VWi9CUWd5bWRYdE1FNXBVTjM2dm03M2pLWTRPM25RMnh5T3FkcUVqQnp2Y3pUQ0FsZnpYZ2JaaEIrbmdPeVdkTmx5YVVkUmhCQTBEdG8ySU1Zd1had3NRektVNXRFMWx6bUxpRHBFMGJwQzloNlFXTXpVa3o3NVpSWkt3WVE3OUpCTHpxOTNWLy9YVVJiT0h1VEZYaFRPcWlPMG4xL2hTTmk1RzBoeUJBenFxajYzMEdJTTlsSERtWjVWV1hlTnYyWmRreUlicTN2RGpYLzBvS3pKV2Z4bHVnaFlYOTUzNnVaOFhrbmhydXhIN05MWVhibk1WVklCR3VkZ09TeXZFUXViSUFaamk3aG1LSmcybFdaYW1OMUVnUWo4RTVTeVNlNzZHMk5JNjF5M3libVYxaWcwYWZUZkJmWDFhV1QyeDJWMm1Famd2cjJkam9UMDFjSUQzK0UvWjgwR0ZvWWxZa0l6aDBrYnNFQ3dxTFRISStFT0FSckkiLCJtYWMiOiI5ZTRjYTgyMjBmOTUyMzhjMzBmZmI3NjdkNzdlNTc4ZWQ2NGY4N2I5NGQ3OGY2NzY2OWM0YjE4ZGE3MTM3MjVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73108191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1963114764 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963114764\", {\"maxDepth\":0})</script>\n"}}