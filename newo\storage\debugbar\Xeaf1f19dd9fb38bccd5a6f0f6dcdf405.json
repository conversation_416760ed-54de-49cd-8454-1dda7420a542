{"__meta": {"id": "Xeaf1f19dd9fb38bccd5a6f0f6dcdf405", "datetime": "2025-06-08 14:15:07", "utime": **********.348892, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392105.977911, "end": **********.348925, "duration": 1.37101411819458, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749392105.977911, "relative_start": 0, "end": **********.201933, "relative_end": **********.201933, "duration": 1.2240219116210938, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.201955, "relative_start": 1.2240440845489502, "end": **********.348929, "relative_end": 3.814697265625e-06, "duration": 0.1469738483428955, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43935704, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0307, "accumulated_duration_str": "30.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.290797, "duration": 0.02935, "duration_str": "29.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.603}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3295531, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 95.603, "width_percent": 4.397}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 18\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1541077595 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1541077595\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1926256131 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926256131\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1913191437 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749391833855%7C52%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJrTm13S3Q0ZlhsRnI2WnpIL1RTSnc9PSIsInZhbHVlIjoiYlQrTzZ1ZW9zNFQwYVhEN2F5OHNSR0h1ckkwamF2N1lJRWx2dUtnNDhpR2srQU9ueFAwUjhIUUNNdStmcmhMT21OUG9ISGV4aUpWblprWTlDY1IvazRMUHRxREkwV00zWVY0MlpTNVE0OFJNUmV4UURPamNEamticjljWElHM0E0QkJnWlVLODdtZkx3d2xtUmwvNUQ2aVZaQUJxTlRSV0NaSk9ua3RZcSszeFB4SDFZc0s2enFaNWlNbUNtWmJNbGROV2M1bkFFN1BtVEZNTWJkSitBOW83VXAvcXJGZUgzRW1MTnkvL0R6aXNpOGlKWGFFaE00eXFHN0lIZU9nUlpLSzN6d1A5VUNSazhhWXI1QVNEa1BvT3ZwOTZYYUxFWUpCcGw1M0UxSFgveVhvS2ZqV2tNSkh2bW5FdWs5b0IxenJrNXoxcS92MzNHeFl4K0tWSGpiS3Y0TW5JR3pxSEVpb291a2ZTNnBSUlpOZGg1V3FucHlhdW9GdUM1Y0p0VEphakZxT2V6NEtMcHd0VldWY1NVRVFuZ0pSdmU4dTRQcVlBcTJPVkFIS3U5TWcxa2l0TFl0dXdvNDFMWjZuOTNWTkY1aVdsc05yK2E1NGJ4ODFmZjBHbXBwRWVOVllFVWs2elJIWE8rWFNhV0VWSDVObVFUK2grMzlBaDBSQnEiLCJtYWMiOiI2NmE5NGU1ODRkODRmNjNjYTIzNDhkOWQ2YzJkMjgwMTY1MjM4NzcxYjZhMTU2ODMzNTJjYzIwZTBlYmEwM2NkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVlK2ZuNGpXOWFCNnFsT0YzaE5SR1E9PSIsInZhbHVlIjoiaC84RnFBWHZQYlQ0d3lQbkkwaEgzTWM0WjJhL3c0MWRacG5ucktSV3V1bW85TURaTlNrd2RBMkVwRVp6U2E3c2dJYit3cVBZYnZaL1VmM3BxOUh3Q3lESEdpY0dkREJkWXdZaEZmVjF3WGRIQktmNlR5VS90bi8vMkRrMlZ2b3Y4WmYySnlOZVVaRVVIeGkzd3RucXZJclQxMVRtTEIxanVHVWJpajBjcUgwT2hzclB1cDdySkpNbkN6Ym9ZSUtvWE90dk1LMlh1Ri84K0JYZXRoSGVRS3BGWW9VTitMV2RHTEpKRyt2WVM3M0NkOFNUSSs4alAxdWJNUDlZR24zUklBWjlEOWptMDBxY1UzbFA2QkwvMUxqNlVBT0pITnlVbVl6RnV2c2EwdTN0MU5PYmVwRFpsY2NYT29PZGZkbkhKNEVJQVk5ZW10VER3Z0ZSOTdQSHRMYmlYU3VFNVZhOXc3QWh0OGUwWVlETENHbmNIWURnWSs1MUI4QkFRUWwrT3BXWmdEc1RoVFlRT1A1UUZwN0VYQXBEMEFOWGI1U3NvL1dKR0JhZTlJd280Rk1sVU9lWkhXckVWZW5PWmd0NDVBUTBLVTY1RDIvRlU1WEVwT1NJRWZSc1VaSU5zdnBHcVYxWGd2MnlEbGIzZC9nS0xCamJJYy9obXU4ZVo4NkgiLCJtYWMiOiJjMTJmMzY4YjdmNTExMjZiN2Q2OGZlNTA3OWJmZTkwY2RhMThiZjRlN2NmZTRkY2MxMjU5ODEyZWZmZTEyZDliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913191437\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-819655966 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819655966\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:15:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZ6K1ZOcUtmczhFRUJZUDN6Z0FEZVE9PSIsInZhbHVlIjoicC9IdURueXVxbFZiRS9EV05JQU5LQS9ta21uV2RVQ2pnYXpkbGYvcXRoU0RnVDF4Y2I5N25EMytKRTR2WFNUOThQc3ZKSGZHNEFHU2pScGhLYkp4Sm1QMDN6NXBzN2M5bGNYNmpabnlOSHhnc2lrQmJIbGVDdE11OVlFdXhBaVlTMXVYSXkzRS9RaUhUQ0NUZkF2R3hlTThZa3FUckZRMXBNYktXZUhFcTd1WC9SdXMzVXlTaDJZSWY0bkFrWUxOMmRITndlV1c3RFBYVFlmNTUyREFLa1RVcUFqdnN3VGh2bTZHVzZjQWtMNlh4bmlndk9BbGlsbDNpUktDQWtCU3g1UzdhbDUyVVZ4QnRUem1VZXFrdnBUSUZMT1VzeS8rWHRrcjVRUTJscDF0SXUreUdQK0p3UkJCemhYUndqaXIwWHIwbVRJMWE1UGZaYURIQlZnTHdzWmNtdXJTYWVqZ0c3aTZXODNkMEdtTlFxc2FSOVdaNGlpUnI0bllRVHQ1dTBQWTN1Mm5XTXRIM3RPbk1LeFV4THFnR2NzYkoyRzYvUHVtOVdNWnRtT0hzb01ZT2xqN2VmNHFlSU5CQk1MT2pCWFRnOTluekMwUm50aEVpOHBuckdyQW5CWWFraC9OeDhPUHFqOUdtUWhCc01qTlpNL3JzTlRPN3FKOE5ycEUiLCJtYWMiOiJkZjM5YzMyYTc3ODExNDYxODg5YjE4OGNiYmNiNmExZDRhNTVhNThlOTgwODk4YjlkMmIzYzlhZTY3ODAxM2Q0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:15:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZMNlZjOUEwZzI5NTN6aDExVG1QMmc9PSIsInZhbHVlIjoiL0lyNnNvNU9XWG9pbkR3dUFLZit0SlNTU0J4OHRVcHhkZkVZN2NTZFVJNWQ4MFRLM2hod2FNNUlDQ3BJbk5FZ1NpN0o3RlVLVnUxL0NiRG43Q1pjMEpHdVVOMjF2RXczNHRSSTlnTVVoN013NTlmRkJWbEx1YUxrUWtINUs0a2ZwVUNzV2dMYTVBQ0RZb3hTenVYeVBhc2ovWTBFenlrbmxaNFBoWFBxclJJblcrMTVOTUlOcUZNQ1VaZ1hRcC9UUnZDSmZGYzI0SWZvdWVsT1VIQ09vZDR4VnZIQlZHYWVDdW0xOUt0UkE2NHd4a1VYRUkwVkg1aGgrY2hJbGgzMkN4VmYxS0FwdkgyS09FbzNhOCtDcHgxUjJIa3IrR29ydHBBUG5aaXFaSFp1VTFUT25uSkhGMEpvYjBKWFgrS2Q1M0RHdjZ4eGJUNEJQaUUvblJqelhvSFVXd0l3RmpGTFNsVnVmOEVuVXQrTTJHM2tFUGFSVFFZbzdTcVBhbjZkdEpVOXNvVEhmVmR6Qk16Y1FHRVlDWmZSVW4yZUVTOHJKOFZrQ2ZqY2R1MWVEVFQ3ZC8wc2pzcE1Ic2NSRkdvS3NBc1YrM093MUl4K25QM3VOdFdUejhNODdac25GTTVjODhQZjZDYU1uZ1J3YXVnOG03Q2RKWGxDVlZhbzV1SlIiLCJtYWMiOiI1YTU5YWYwNjVhNTYyMWM3NGNmNDQxMDJhZDE2OGRiMWNiNGM0NWE0NzQ2NGY2MzU4MDRkY2Q3YzIxMjgwZTNjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:15:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZ6K1ZOcUtmczhFRUJZUDN6Z0FEZVE9PSIsInZhbHVlIjoicC9IdURueXVxbFZiRS9EV05JQU5LQS9ta21uV2RVQ2pnYXpkbGYvcXRoU0RnVDF4Y2I5N25EMytKRTR2WFNUOThQc3ZKSGZHNEFHU2pScGhLYkp4Sm1QMDN6NXBzN2M5bGNYNmpabnlOSHhnc2lrQmJIbGVDdE11OVlFdXhBaVlTMXVYSXkzRS9RaUhUQ0NUZkF2R3hlTThZa3FUckZRMXBNYktXZUhFcTd1WC9SdXMzVXlTaDJZSWY0bkFrWUxOMmRITndlV1c3RFBYVFlmNTUyREFLa1RVcUFqdnN3VGh2bTZHVzZjQWtMNlh4bmlndk9BbGlsbDNpUktDQWtCU3g1UzdhbDUyVVZ4QnRUem1VZXFrdnBUSUZMT1VzeS8rWHRrcjVRUTJscDF0SXUreUdQK0p3UkJCemhYUndqaXIwWHIwbVRJMWE1UGZaYURIQlZnTHdzWmNtdXJTYWVqZ0c3aTZXODNkMEdtTlFxc2FSOVdaNGlpUnI0bllRVHQ1dTBQWTN1Mm5XTXRIM3RPbk1LeFV4THFnR2NzYkoyRzYvUHVtOVdNWnRtT0hzb01ZT2xqN2VmNHFlSU5CQk1MT2pCWFRnOTluekMwUm50aEVpOHBuckdyQW5CWWFraC9OeDhPUHFqOUdtUWhCc01qTlpNL3JzTlRPN3FKOE5ycEUiLCJtYWMiOiJkZjM5YzMyYTc3ODExNDYxODg5YjE4OGNiYmNiNmExZDRhNTVhNThlOTgwODk4YjlkMmIzYzlhZTY3ODAxM2Q0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:15:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZMNlZjOUEwZzI5NTN6aDExVG1QMmc9PSIsInZhbHVlIjoiL0lyNnNvNU9XWG9pbkR3dUFLZit0SlNTU0J4OHRVcHhkZkVZN2NTZFVJNWQ4MFRLM2hod2FNNUlDQ3BJbk5FZ1NpN0o3RlVLVnUxL0NiRG43Q1pjMEpHdVVOMjF2RXczNHRSSTlnTVVoN013NTlmRkJWbEx1YUxrUWtINUs0a2ZwVUNzV2dMYTVBQ0RZb3hTenVYeVBhc2ovWTBFenlrbmxaNFBoWFBxclJJblcrMTVOTUlOcUZNQ1VaZ1hRcC9UUnZDSmZGYzI0SWZvdWVsT1VIQ09vZDR4VnZIQlZHYWVDdW0xOUt0UkE2NHd4a1VYRUkwVkg1aGgrY2hJbGgzMkN4VmYxS0FwdkgyS09FbzNhOCtDcHgxUjJIa3IrR29ydHBBUG5aaXFaSFp1VTFUT25uSkhGMEpvYjBKWFgrS2Q1M0RHdjZ4eGJUNEJQaUUvblJqelhvSFVXd0l3RmpGTFNsVnVmOEVuVXQrTTJHM2tFUGFSVFFZbzdTcVBhbjZkdEpVOXNvVEhmVmR6Qk16Y1FHRVlDWmZSVW4yZUVTOHJKOFZrQ2ZqY2R1MWVEVFQ3ZC8wc2pzcE1Ic2NSRkdvS3NBc1YrM093MUl4K25QM3VOdFdUejhNODdac25GTTVjODhQZjZDYU1uZ1J3YXVnOG03Q2RKWGxDVlZhbzV1SlIiLCJtYWMiOiI1YTU5YWYwNjVhNTYyMWM3NGNmNDQxMDJhZDE2OGRiMWNiNGM0NWE0NzQ2NGY2MzU4MDRkY2Q3YzIxMjgwZTNjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:15:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}