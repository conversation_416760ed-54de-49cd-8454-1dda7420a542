{"__meta": {"id": "X7ac252776bdb0a59d2123e391e2df300", "datetime": "2025-06-08 13:48:41", "utime": 1749390521.021317, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390519.630165, "end": 1749390521.021348, "duration": 1.3911828994750977, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1749390519.630165, "relative_start": 0, "end": **********.762611, "relative_end": **********.762611, "duration": 1.1324458122253418, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.762639, "relative_start": 1.1324739456176758, "end": 1749390521.021352, "relative_end": 4.0531158447265625e-06, "duration": 0.2587130069732666, "duration_str": "259ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02594, "accumulated_duration_str": "25.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.869515, "duration": 0.0184, "duration_str": "18.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.933}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9134371, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.933, "width_percent": 4.433}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9644809, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 75.366, "width_percent": 6.207}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9718819, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.573, "width_percent": 6.399}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9873402, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 87.972, "width_percent": 7.787}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9983718, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.759, "width_percent": 4.241}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-540216576 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540216576\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.984749, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2075956253 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2075956253\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1007984529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1007984529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-992000680 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-992000680\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNzZVBoZ21IdVZvTVFaTW53RlJYelE9PSIsInZhbHVlIjoibnZpcTV5ZkZRRGlYaHhOdXhHdGQyT2llL3liTExETXlsK0x5QkNkZTFyZ3JUclV2MHJ1S0RDMnlnMWd2Z2ozNGxBckxkYUhqUUpXSWNmNWN1TnJzQmt5NCtXbnl1M244K3hScDR4WEkzZ0I2RzBvUnZnUmJWdkxibklJTm5hZWwzUUxaMDRLeWJ4N3FWZWZWVE1ySkZZZm8xSmxhdkRKbXlqSG43SWZCMkpqeStrb240dXJSdEtSN2trODZUQndiVDJLZzdVNUFIZk5iN2tnbi9FNkR3SXorVG50TlhOZ21HQzNFOTdSWG4yV2tUa3czZjVsZGk5S01OcXc2aWhOaWV1YWFWTEZHVGFrMnRRb2VWbkhjOHp5S04zZ0cyZitocVJIMDRIRFl2MGtMMzdLNXlqY08zbHBkKzlTODVLYUlIckVpSjZpcVk5SVY4OUFyQmcrRjREZGJ5NkVqSGhmUzdaUks4a2tmdTQzSFNXZjhERC94bTcycVd2ekhKSHVNYkxsLzkwZU14U1hKZjdnU3JqcENZZnQ4UHh0RmwrNURrT2VXUWU0aU9mVzUyLzBYS0ZNaVpmR2tCekl0MkVtTm5EU2hPdktlSW05VEZrdU9kalBZaUVBTjJ6bjBKaG5GYks3UHhIc0xJTUFCSlEyWEFpTDJGdFZXSHpYbWx0bXgiLCJtYWMiOiIxNzUyM2MzNzJhNGY2ZDdlNGMzOWQ4YzFhMTVmMmMwODQzMWVjM2Q2ODFjNjA1ZGQ0YTcyMDdmMzUxYmI4MTUyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRFVjcxdFIxUGh6ZlVEblp0d0VsK1E9PSIsInZhbHVlIjoiN3l1ZEdoQStJemxFamwzWVBUdExGY0JOR3BjN25nT1BtWkszZkRkRHNRdTdmMk4vSTBqM0RSYzZTb2VhL2xJQjdMNFhBQWlFaGdtaS9GWlZOazErcU42SkRrMzZtUEsvaGM0dUpQajdmdmxKOWFZYnpDV2sxQ2dRa0U4b2k4TGJSK2s3TTBsVDE3bXZwZk1HSXNUZVFsRHd1U3JHWDRaRmpJa3k2RG1rOVViZE5yV0x0bUJNM05EczZ1UGxKNXFpbE9OdjFPaUVlOFFuaTZ3Tk93WU95dE9KaWNmaWVpL01LYU55OHRKTU5LVVBjK1QyRUVwQ2RFNGt4K0Frdmx1WmRQWnhSSnRHaDk5N3o5aFJZeHpFaTN6Vld1eVFIZ0YxTVRRMS9QcjU5RU1oUnNGUGtNWnRDUktxMjJ6eThzYUUvUklVbGlSWWxLblRJTzBoQk5FYzJOR08zR3VTSGNEV0pOOWtFRTBUM0Vkd3hEWG1maUk1SmlDSUtrTWZxYzhnMGUvKytsb291WkZCM1dxc3F4N3owZGNkVGdFWEI3dUlLRjlhcU1GQUNRdklVMHZLNnkrVjRUQ2MwNGQzR3FEWWRWNEpoK014SEFXeG1hckMrL0gwL3JZaWJIRi9aa2hUclMvWTI4dzQybTlTUVFkcENOQUZ5L3Btd0x1NnVuQ1UiLCJtYWMiOiI2YjlmN2MzMDNkNGIwZjVjYzk0OTY4ZThjMjU1Njc0YjYxY2I2MTU2OTQ4ODk2Yjg1NmExODVmYTc2NmEyMmY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:48:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1CcXJVY1RSNjU3eGdNSGVPVmYzbHc9PSIsInZhbHVlIjoielVkeWhnci9xcnJYdTJpWGtaejVLc3E2SHdWZG1CcFI3Qy8vbGxMYlZ3cTBFN1gvdjFHOUwxRHpxTHdhWjNoTDlHZVNLQU0xR3BtSGxkbzQ2YVNkV0poeVY2d3YwblFkVVVHQ1VxdXh3cjNkSTV1cjJUbFV3VGRxNU9XM2ZkWWFGL1ZmMml6R1B0SWoyckIvbk1EcjN0QXpqcnVWK0NhOVpHdUd5Q2F0R0hPWWVGaC92eXFXQ2N2d3VrMzNmTlhaS1cxRVBnallhbDI1KzJYdXMwaERoeXZtQ3JlZ1lWSDNnYkpiZC96aWU5b0d3bWVmL2RJT05wQ3BrQmExa3dQWHFFNlgyZHNuOWVMNndQMzRtck9CR2hFemxWclVkMnlkUFNLeE1kVkUrVVY3ZnFkb09waUhGMDA3aEdBWml5c1d4UEpKaHdjNkMrbXV5SjhVejk2RDVHWmhKTXhINkJ2bXg1TDRHcFNETC9vd0NVUjY3U1A3T0VrQzdvV04rckloUldQekQzSHFuNUZEeWVpZnh5bzZnZjhUMm14eUxWU2o4blFLZGRwT0FBeDMweW5EV2w3RXpLajR3ZE1kVzVqOUdXWkRwY0FBQ1Jkc2ZpTkJrZzgrSUZRUEVlR0dEMUYvb0piVkJaVUxSaTh6ZWkyUDd3cHlpWXBoYWFLbm81OFkiLCJtYWMiOiIwMTlkMmQyYmIzMzU0N2IyYjRjNjIyMzQ0ZDFjZGFhNjVhYTc4Nzg5MDc1ZjYxMTI1N2I2NjZiYjdjMzZhM2YyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVRWk9haWhzMU0zTkQ5cU04QzJ5cHc9PSIsInZhbHVlIjoiL1Y0Q2hncXBrVTF5cXg3WFBQL3dxb0xaa2hBTUhORVh5VjJXZ0czOUhTRnhScVdyU1R5VWtLL0FxMnhFVi9vL0tEem0xWFhHYkF4bnNQVEttTnFjeU5TWHZMQ1phMG93QUxCNWVkaUpqejBPTkkraDdoeE1HNjdtUWVNcTB1SndGdTZYNW9SYzIyak5KdVptSERoVUx5UWZkOWxNOFB5enI2RU1GZkVUem5DQW50dUVQNVVzcWJ6bzNxZmREd0hNMTVrZ3pMSERvU0IvWkZ3azFmWlA4aHVZYTRpYjlVTjlFZ2pzbWM5WUN3dG0wR0YvNnNMUTB0QlpOekhTL1J4N1RPakI2YmNzL3BoVVF0TWpHdG5TYmk0NGxFL3FnbUlWSnFCWmFFeVJqUTlITDVXSWo3UitnU2VEZEdTc3V3Z2RKZzRjcXBrQmo2Qi9ldnJXdmlTS2RUVUFSbHB5VGtqUDRkV01ZcDcxTVZoYWJPR3dJVFlTNVhCVHg5Z1pHTS9oUFIxUWp2RnB4c0pYWHNmUFFRNXR4VEtMSURnWFJHeEdmSmhNNVV2Vzl2WnZidFNJSktWRHo4Yk1sM21iSEV0aVNhQklNQWc1bjJlcmRIREUwamY1THYxa1NEbm42cFk5bFlyUkZFSVUwaS8zZTgwbWRqVVRnZVlvTWFSSjQ1VHEiLCJtYWMiOiIxNjRiMmY1MjgxMTM0NWYwNTI3MDRiZTQ5YjUwZTBmZWUzNzFiOTNiZmFhYjkyOGNlMWU1N2E0MWQ1MDE2ZDUyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1CcXJVY1RSNjU3eGdNSGVPVmYzbHc9PSIsInZhbHVlIjoielVkeWhnci9xcnJYdTJpWGtaejVLc3E2SHdWZG1CcFI3Qy8vbGxMYlZ3cTBFN1gvdjFHOUwxRHpxTHdhWjNoTDlHZVNLQU0xR3BtSGxkbzQ2YVNkV0poeVY2d3YwblFkVVVHQ1VxdXh3cjNkSTV1cjJUbFV3VGRxNU9XM2ZkWWFGL1ZmMml6R1B0SWoyckIvbk1EcjN0QXpqcnVWK0NhOVpHdUd5Q2F0R0hPWWVGaC92eXFXQ2N2d3VrMzNmTlhaS1cxRVBnallhbDI1KzJYdXMwaERoeXZtQ3JlZ1lWSDNnYkpiZC96aWU5b0d3bWVmL2RJT05wQ3BrQmExa3dQWHFFNlgyZHNuOWVMNndQMzRtck9CR2hFemxWclVkMnlkUFNLeE1kVkUrVVY3ZnFkb09waUhGMDA3aEdBWml5c1d4UEpKaHdjNkMrbXV5SjhVejk2RDVHWmhKTXhINkJ2bXg1TDRHcFNETC9vd0NVUjY3U1A3T0VrQzdvV04rckloUldQekQzSHFuNUZEeWVpZnh5bzZnZjhUMm14eUxWU2o4blFLZGRwT0FBeDMweW5EV2w3RXpLajR3ZE1kVzVqOUdXWkRwY0FBQ1Jkc2ZpTkJrZzgrSUZRUEVlR0dEMUYvb0piVkJaVUxSaTh6ZWkyUDd3cHlpWXBoYWFLbm81OFkiLCJtYWMiOiIwMTlkMmQyYmIzMzU0N2IyYjRjNjIyMzQ0ZDFjZGFhNjVhYTc4Nzg5MDc1ZjYxMTI1N2I2NjZiYjdjMzZhM2YyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVRWk9haWhzMU0zTkQ5cU04QzJ5cHc9PSIsInZhbHVlIjoiL1Y0Q2hncXBrVTF5cXg3WFBQL3dxb0xaa2hBTUhORVh5VjJXZ0czOUhTRnhScVdyU1R5VWtLL0FxMnhFVi9vL0tEem0xWFhHYkF4bnNQVEttTnFjeU5TWHZMQ1phMG93QUxCNWVkaUpqejBPTkkraDdoeE1HNjdtUWVNcTB1SndGdTZYNW9SYzIyak5KdVptSERoVUx5UWZkOWxNOFB5enI2RU1GZkVUem5DQW50dUVQNVVzcWJ6bzNxZmREd0hNMTVrZ3pMSERvU0IvWkZ3azFmWlA4aHVZYTRpYjlVTjlFZ2pzbWM5WUN3dG0wR0YvNnNMUTB0QlpOekhTL1J4N1RPakI2YmNzL3BoVVF0TWpHdG5TYmk0NGxFL3FnbUlWSnFCWmFFeVJqUTlITDVXSWo3UitnU2VEZEdTc3V3Z2RKZzRjcXBrQmo2Qi9ldnJXdmlTS2RUVUFSbHB5VGtqUDRkV01ZcDcxTVZoYWJPR3dJVFlTNVhCVHg5Z1pHTS9oUFIxUWp2RnB4c0pYWHNmUFFRNXR4VEtMSURnWFJHeEdmSmhNNVV2Vzl2WnZidFNJSktWRHo4Yk1sM21iSEV0aVNhQklNQWc1bjJlcmRIREUwamY1THYxa1NEbm42cFk5bFlyUkZFSVUwaS8zZTgwbWRqVVRnZVlvTWFSSjQ1VHEiLCJtYWMiOiIxNjRiMmY1MjgxMTM0NWYwNTI3MDRiZTQ5YjUwZTBmZWUzNzFiOTNiZmFhYjkyOGNlMWU1N2E0MWQ1MDE2ZDUyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}