{"__meta": {"id": "Xb44409079203e33c33c7b56dd086c4d8", "datetime": "2025-06-08 13:04:24", "utime": **********.165057, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387862.549379, "end": **********.165088, "duration": 1.615708827972412, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1749387862.549379, "relative_start": 0, "end": 1749387863.946307, "relative_end": 1749387863.946307, "duration": 1.396927833557129, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749387863.946332, "relative_start": 1.3969528675079346, "end": **********.165092, "relative_end": 4.0531158447265625e-06, "duration": 0.21876001358032227, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45269928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00726, "accumulated_duration_str": "7.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.09723, "duration": 0.00632, "duration_str": "6.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.052}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.136983, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.052, "width_percent": 12.948}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1681581198 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1681581198\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1396334001 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1396334001\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-755957533 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755957533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1339011850 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjY1VGhUUTgvQzdMcUp0dnpXYTE0aUE9PSIsInZhbHVlIjoiREU2VTVPUG1qYzdvYXhLM2JjVDhtRnZ2TmQwbm9KeXkydlJvMjc3ajM1RURDM0NVck5LQXZ0MkNHVURPQ0tuNEd4RWN3ZE5lR0pZM3Q0WElvN1k4MWJ0QXRrZXFISWxuZkpHOFZxcG5ERGFIOC9mdTdKb1JyNnA3Qy9QL1o4ZE1HenkwRW5sVTI1TVVQcjJGdUJIRFM2anZOU1B5QU1CSXEwMk9HQW5rT3p2THVsZnByMk0rL25XbTlNQnM3RzEzZkIrMFJSNnZtZHJnUm44S3B5dXlKQWVFWHFaQUlpMWJ5MlpWUGNWdnlhVFNSb01PVUZzbzJXZitPWU5oUGhYaHV1c1dTOEliWFhVVktGd3RvRjloWmxacnF5QUF4U0Z3ZzNDSVlMZGNadHc3dW82YkM5THJKVTZzdGJsQjBzRUVIeHp3QVhVN3ZDT1V2c1ZINDAzYlFxVm5zZVVHTWx3OGZ3YStqKzQzRXpFQVJoY3JqcCtYWlh5TkdxcGxPZU1CZmhKQ1ZNdnVRVE1yYW1DYVljcWZYcDR3eVBHNFZTS01xOTd0YUJ2aGJxdC91Q0Y0cjdzaitZUWxCTmlxWlNrRXVPL0RJcFE2S09KTHMzenZPMmtRVGVqTkZGbUFjNE5zR0VMUVhrWjEyeXc3NEVINU9CTzU3QTQyR1ZiS0I1bVgiLCJtYWMiOiI4MjUzZmExMmQxNTUxMzI1MDU1ZTk2OGEzMWQ2NTQ1NzAzNTNjNGU3YjM4MTIxYWZlMGI0OTAwZjU4YmFmYmE5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndUYUFBUzRrb2xSSEhLTEE2SnpxSVE9PSIsInZhbHVlIjoiSk8rVEhIL1NDQXNNNGVzRjY1WVRXME0yd1VYS1FUY3ZWa1BtNld6VFRmRVZlRm42YUxNV3JhaWJEYWhOSitXY3pxL2lad0NCblBlWEhDZXliZU45emVnNXFwcHVIL0JJVmVZemUrTGpBVHRYWFkwUTQrTW5vQ21LT3Y3cGJtVHQ2bTg2Y2M4elFxOWlZUW4yYUpoZjhSdXU3Z0NBWERkMW5wbmE1bFdyN1JHUUxxOUNaOHFiTXIzRS9ROVdwUis4WThkNXhkRmx3QVhLc09KVzZxVjlDQ1lCdjJZRGVYUW9QdFZiZURGTU1IM0JkNG5qam9ydmoybkZzenhJMUtiZkM3a1pFcVR3M1ZWRWpFNGhrT3VPblFTdDlrYzdPNUY1aWdhNGp3WHZSN1FzOUJKRld1eW5iM3hvbWxmNGRqSlJuTGQ4OVhHZWlqYmFRMW02bDM2SUtOaGhVOW9LdGlEYlZ6c1NYQ1VFNStNZFo5Q1JEcHAveTQraVdyNTk2bUI3aUdOeVFKay9UZXRrN0U5eldpMDYyYWRaWkowT1JPUmlDQlZIN0ZyQk4rajB2NGxoVmtvQTZLS0duSGdNdE5vY2pNUlpKMUFJQ2lkVnEzaWplNVNGVXpTTWlEdWJWNUV4S2ViV1RvbVhGVTJyUHhWako5dkQxaXBFdCtCamk3OXEiLCJtYWMiOiIzYWY2NTgxMDFlODdiNjkxYjk3ZWI2YTQ5M2UzYjg0YjdlNmE5M2RhZWVhZjE1Mzg4ODMwMDkxMzY3YTU5ZGY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339011850\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1111396752 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111396752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1508530465 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:04:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNNREJ6U2Q5WmxjZzY4YlJIWjRpM0E9PSIsInZhbHVlIjoiVVlZZ1dCcmVuRUNRdUFJUDNoTVd3cyt0OUlVTDR0Wk1lUVU2eXdEU0U0V2loZnVscVMxRVkrWkN4L3lSeGtHc2Q2MW1MUS9CSmw4VVdIb2dIa0dmZVkyTUNsbm9XeVpseEdQRWh6N2k0N2g4MWRWZStoMUJCR04yNzJRYkYwVGppMlJ3YUlHRUx5bm1aNGJNRkRHZDU4bk9IVGxhUWk1alB6VEJMWnhlcUFrS0t6ZnZXNDJ3WXdrWCtBOVNFcldPUWorbnBtM1hPbzRHMXJYUmdOdmFtdkl6REQ2SDBpYXhJczVzQnZzN1RadHdvek1HUkpyN2dnU3NQVXQ5Zld4cWhnWE9POGVBenNHZG5mUnoyWDRhMlJJRVNrZGhHbGVpZzFyQkZUOGtHZFFvejBVOHQwM1ZuSklOVjRzd1NLdjVBN2pRUlRYS3lHZDJzMmcxdjhrZzY4RlNsNHhYSzFpWkV4S2tSOHgwQmhpeXhZS3NQdlR6WEtINVEza3NHelp4YStRNnBZVitCZWtoT3JuTWhldHRiU3lzZHA5VXlTT05WVVVmM1VoNThMbUh4SnVMVytqbVd1SjBra29ueHVnTnlRY2dYQ25CeGY1dVlINmkrZWk1NUxGMmNUZ0VJQWJ0YXA5amJNSWJGZkZOSmpDMnQ2bG0xcWVRRDAwQy9hNlAiLCJtYWMiOiI5ODA1NGUxY2MyNmQ5MGI1ZGYxYjViODE1ZTFmZGQ2YmM3ZmNiOWIyZDhiMDkwYWE5NjYwMTc5Nzc0MzU5ZGI4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBYQ1FoaW1JY0cweUwyRlhkNGwwWVE9PSIsInZhbHVlIjoiY3FMclhBTWNKbVQ0V1VLTVVMclhsdEJaWDdUSjkyV3YrdGxjNENwMFFkRW82VUV3RytLOXFUWFlKR1VDMTZWSHNYNUpOdU1UWmN5ZTFxREpHM2xmN2JIVWRJb3VIMys3RTljOE50ajhlS3R0SzVpQUc3aVEwbFd6OW1ZWjVBNHRueHlFOTRxWGF2WTF3UVRQaW5XNkRjMklpUjh2T2sxT25DTVZIVUorQzd2UzNvbnhzUVJ5d0s2MENTNWNIVjhCODJjeC9Sb0xzaDV5Y3BXRXBjZnluRW1zWkwzUXQ1SjhJZ0llMUpTZDdKS3ZGZTBjaC8zaFVQMVhmSE94bHBXYkhlZFNUMGU5aG9NaTJJYmMxSDByNkp2bTVsdGdvNDNkRGFBek9kRU1sbGt5bFZlcXM4US9yRUM0eThoVGpRSkR5b1pYQmpaMnVicE1LeGJWN3d1cE15MHlkNGE2ZlMxdjN5TCtvSTQvTDVDSVd2WWo1ZXdWeUlUSWhpWXJWTXgrVW56anZxOFpmbXkyR2JHT3RPcDJzMW1tMm9sNks2bms5UmplTjZONis2UkwyU0Z4cjk5am04M1V2OFBtbWtTT1FUeDdNYTRydzNON1ltL01GSEFCVDRHZGN0cDJpME1oY2dJeDZERUpyRWlxZ2l0YllvY21vZzlnVGxBNGMwV1EiLCJtYWMiOiIzNzA3OTI4OGYyNmIyNDBmM2Y2ZTY1MWE0ZWUxZDM1ZDI0ZDVkYzljYjkzOTg3ZTFmOTE5Nzc5ZDM4NGNmOWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNNREJ6U2Q5WmxjZzY4YlJIWjRpM0E9PSIsInZhbHVlIjoiVVlZZ1dCcmVuRUNRdUFJUDNoTVd3cyt0OUlVTDR0Wk1lUVU2eXdEU0U0V2loZnVscVMxRVkrWkN4L3lSeGtHc2Q2MW1MUS9CSmw4VVdIb2dIa0dmZVkyTUNsbm9XeVpseEdQRWh6N2k0N2g4MWRWZStoMUJCR04yNzJRYkYwVGppMlJ3YUlHRUx5bm1aNGJNRkRHZDU4bk9IVGxhUWk1alB6VEJMWnhlcUFrS0t6ZnZXNDJ3WXdrWCtBOVNFcldPUWorbnBtM1hPbzRHMXJYUmdOdmFtdkl6REQ2SDBpYXhJczVzQnZzN1RadHdvek1HUkpyN2dnU3NQVXQ5Zld4cWhnWE9POGVBenNHZG5mUnoyWDRhMlJJRVNrZGhHbGVpZzFyQkZUOGtHZFFvejBVOHQwM1ZuSklOVjRzd1NLdjVBN2pRUlRYS3lHZDJzMmcxdjhrZzY4RlNsNHhYSzFpWkV4S2tSOHgwQmhpeXhZS3NQdlR6WEtINVEza3NHelp4YStRNnBZVitCZWtoT3JuTWhldHRiU3lzZHA5VXlTT05WVVVmM1VoNThMbUh4SnVMVytqbVd1SjBra29ueHVnTnlRY2dYQ25CeGY1dVlINmkrZWk1NUxGMmNUZ0VJQWJ0YXA5amJNSWJGZkZOSmpDMnQ2bG0xcWVRRDAwQy9hNlAiLCJtYWMiOiI5ODA1NGUxY2MyNmQ5MGI1ZGYxYjViODE1ZTFmZGQ2YmM3ZmNiOWIyZDhiMDkwYWE5NjYwMTc5Nzc0MzU5ZGI4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBYQ1FoaW1JY0cweUwyRlhkNGwwWVE9PSIsInZhbHVlIjoiY3FMclhBTWNKbVQ0V1VLTVVMclhsdEJaWDdUSjkyV3YrdGxjNENwMFFkRW82VUV3RytLOXFUWFlKR1VDMTZWSHNYNUpOdU1UWmN5ZTFxREpHM2xmN2JIVWRJb3VIMys3RTljOE50ajhlS3R0SzVpQUc3aVEwbFd6OW1ZWjVBNHRueHlFOTRxWGF2WTF3UVRQaW5XNkRjMklpUjh2T2sxT25DTVZIVUorQzd2UzNvbnhzUVJ5d0s2MENTNWNIVjhCODJjeC9Sb0xzaDV5Y3BXRXBjZnluRW1zWkwzUXQ1SjhJZ0llMUpTZDdKS3ZGZTBjaC8zaFVQMVhmSE94bHBXYkhlZFNUMGU5aG9NaTJJYmMxSDByNkp2bTVsdGdvNDNkRGFBek9kRU1sbGt5bFZlcXM4US9yRUM0eThoVGpRSkR5b1pYQmpaMnVicE1LeGJWN3d1cE15MHlkNGE2ZlMxdjN5TCtvSTQvTDVDSVd2WWo1ZXdWeUlUSWhpWXJWTXgrVW56anZxOFpmbXkyR2JHT3RPcDJzMW1tMm9sNks2bms5UmplTjZONis2UkwyU0Z4cjk5am04M1V2OFBtbWtTT1FUeDdNYTRydzNON1ltL01GSEFCVDRHZGN0cDJpME1oY2dJeDZERUpyRWlxZ2l0YllvY21vZzlnVGxBNGMwV1EiLCJtYWMiOiIzNzA3OTI4OGYyNmIyNDBmM2Y2ZTY1MWE0ZWUxZDM1ZDI0ZDVkYzljYjkzOTg3ZTFmOTE5Nzc5ZDM4NGNmOWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508530465\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1794435766 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794435766\", {\"maxDepth\":0})</script>\n"}}