{"__meta": {"id": "X47a1ca1b5c205f29ec5af2a663aa4397", "datetime": "2025-06-08 12:58:09", "utime": 1749387489.038452, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387487.492545, "end": 1749387489.038482, "duration": 1.5459370613098145, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1749387487.492545, "relative_start": 0, "end": **********.836806, "relative_end": **********.836806, "duration": 1.3442611694335938, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.836825, "relative_start": 1.3442800045013428, "end": 1749387489.038485, "relative_end": 3.0994415283203125e-06, "duration": 0.20166015625, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02189, "accumulated_duration_str": "21.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.932699, "duration": 0.01918, "duration_str": "19.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.62}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.98647, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.62, "width_percent": 6.259}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749387489.0093691, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.878, "width_percent": 6.122}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2122333012 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2122333012\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1357181408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1357181408\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1278671558 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278671558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-361286542 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387444987%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRkNVFJZkZEYWEyd3dNd3JoN0cyMUE9PSIsInZhbHVlIjoidzBVRkNkYU5BNjJzNnd4SlZhVXMxcnZmT0dGcHc3dU4xZ296SDdXTWlBaVRrdHExTlRHVSt4dmdySEdDYlVqL2lRTDZzdzNVQzQ3ZElyY1J1SmJqR0lraFJKYnRMZWZiaDBiUTFmQmhVSFYrZEdPUVNRQTlsWlBKTXdSOFVqcGtJc1duekY3dGpCMXc5UlF2MUhQaE1RMWl3bGhqYWFMcHVSUUxqVWJBZXBjQXY4QzRrZ0hCc2g5U2pyR3BjY21Fa0hvNllwcUozNmYyUFZLMTQ2M0lmMUcwRTBGaytheDc4Q0pnenM5eFlhWVFjbWtIMGMwdHNHd1BhOXVtNTNCMXUrbnNqaHMxWmkvNjlzWWZGQXJoMWhBQklpamlHOE9zU0tQeTlBTzJMSE11a1g3ZEZ5SWk3WjBlZ05WRjZVdStBVUVRM3JvUGVWLzUyNXMxRmxRUGhYTWNOcnp0dk1kblZVdWp5Vy84b3NkeVJsZUlLcExoNk45aFFBVDNpTFFvMGF6UW5xZFRNaWNTcHZTeDlab1R0MDE4cVBQeWlEWGJJNnVxKzFhSGI5MElVZXJHUEQvQ1RHRUVKcHhLcExzY0Z3RVVVT1BGa3hqWXpDS2Y3bDZ3UjNwZmZBNGYrNjlVREZLQmlFMldqNmYzcWZXY2ZxTVpmYnJRSHRaM1dKT1giLCJtYWMiOiJmZGI4YzFjNWE3ODZkNWRhMzc2ODBiZmE0ZWEyNGQwNGJlYzU4ZThhZjU2MjJmNGQzMDFiODg5YmUyNjRlOWJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im50cHBSRzhuWUZTeGQ0MzFCazRwUWc9PSIsInZhbHVlIjoiNGFvemxWcEVrajF6eGtKSUUvcCtpTExjQkdDb213UEhWN045WWxUY2Z0Y2grUkJuUDFCMDBFdFA5NWw4WjJrZ3A3RmpvakQrMWErTlJtZnNqTGdqM3VBSW43Nzc2U3dqSjcxRmJQcnYxL29HNGRRclpUMTByS2UxZlpZblZITDM0VDhkdERjbitBUjNRUlNxaGJkeGpqUHQ0V3IxODluN0hwcFkyQlFHOU1GMFB6Q3UrSEs0aUlRN0lkcWI1Qkh2ZXRwbUlRbFBQQUxWTTRqNDFxZXdmenExQitpeWs4dUtENzV1Q1gxeVYwdnd0bzlqSXRDUFBsaFo4RGR1SDYzTysyYW9ubTZiQ0pBSVl2YldzbmNDdnduUkVQalV3OUpRSG5jRFZPWGZiOXJmdkRwMUpPUE5OMHlvaHZRMzBqMC81NHQ3UXhHQXZHZDJWMUVpbHhmTW9lZlp2NHdtamZoQWtuR001b1BCaWtOTllNNVNCdFJGSGpqcExWb0U0WnhXT0VGZWVScmo2QmszdGRRaVNtd21sK29LeFEvV3VzWmUzdzhiSUg0QTdYZ3FHdGhNczhTSzVpUEpKRGNhVjlwbkY5OXVZZEUwaGtScngzWm42cWE3NVhVam8vMGcyUE83VVQ5YUVHcjNrbzc5UmhCRkRqZW1yem95UUQ1L3BUcU0iLCJtYWMiOiJhOGEyN2E1YTVmNDUzMWEyODkzODcwM2JlODk2NWNkNjdjMDNkOWMzMTU4ZDYyNDA3M2Y0NTgzYzg0NWZjOTUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361286542\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1985494815 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985494815\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-149321837 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:58:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inl1emorY2NoVW5sMk5ZL2VVbytEM0E9PSIsInZhbHVlIjoiVkpmQnhicUF3enE1Sm54ZVZBR0xJbEpDMk4wbW9TUGRzeXM0VnN0UUlNbHZEYUtxc2lkc0xCSXRuWmpsWElQaEtDYVZMNnhPeCtCT2hCUzRCd1pNTjgvQWYySldQZ0l5TG1VSlNZN1FtQ0xpNElpd0ZneE8rVjMxRDFWUllZajNSd2lmajBWYTN0cEwvaDZwUGlCUzhHSzZNSGN6b0VLdlYvQVFERnNBaUdSTENIdzdHK2hjNjQzRkZUbTV5cUFvZlR5SGE5emErOEF1U1l1L1pQMzNTbWI0Q2tab0wvQ1hxVWlodXhDWmVMdFlKZW10QjlSUldFVDloaEsrNEp6VG1ja212ckp0ZlFSV2pwaXBESW91bFlFSGMyYk9MQ2k3MlFGRDB0UTcvT0hJR1ViQlhHbkRrd3dZNmVJcXFnUVFtTHlLYThNeHpBTHhUd1d0QkJ6NEtIM2NMcW1CZEVDUnBlWEI0L2dzVWQrU3NKanloZ3UxTnRITnNmbWJaSS9wcUhJaXB1Q2pDNU14R0NQSmZpVmczVmpIZ2Z1QmtJUVNXSFRadjhyR2FTdTVwWjB0UjZxZXo2SHlUWERMcGtSRDN5bGZqeWZwajFIckJFc1lKZGlCUnpScklNdVAyQVE3V0RpbE5ubUk4cG1qeEM4cnBYL0xwSys0UmRoaDR0VDAiLCJtYWMiOiJkOTI2NzZiZmZjZjIyYWY1N2JlNmRhNWY4OWE4NDE3YWRmZDMyMTFkN2M1NGM0MWU0OWY3ZWQ4YjZiZjMwYzJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkQyQzdFaVpNSDJiYXo3ZkRXajBrRWc9PSIsInZhbHVlIjoiVVdKcVRGSFB0MXdsMEd5S295N3krUnJwcXVzUFpmbW9lK1lTQVBxZVFpcWg2NDZqZ2JoNnhjSFN2SDhIY0dUbDh6dE5sZmttcjV0L3N1dDh5S01RRXU4UTlyZlRBaHc3ZnJXdnkydm9ITUNhUHpXZjY1OU5BVEM2TW5pekxGSHRTMW5BOXFWN0h6bWROZFlnakVIREZ3WDk3ZEFqME1NSGVMZ20wRS9SdENBVkUzOFFac3AzVE5Md2dXWmdZTG5DODRmdXdPMGQxTnB2dTlsYSs1aDRIczZnNENsVkpHT3J1SEVqcytMbmw0Q25HWVhwUzk4aWZSWkJSalE4cFNJMWdTKzVZUktDdUR1ZFk3NktBSllZeHlONXZjWlB2MUtyYzJZRW1mUjF5Qm4vMlhISy9PcjZxZjBCdEozRnVrcWJ4UU9uV2tkVThMOHduY3ltalduMFJLaUNqNVdBZXRITGFxWEZCVUlYNG9FdzFuU3hKY0QrWk4rTGtDNTE4bk9ucS9IN3AzNHA4V1lDQ3p2eEpOZ1EySnBpZFN6ZnRBaEhjNnVnMENrcFp6dkQzMm5TTDk5QWt3ZVBnUGlVYmc2Ylc0MkNEMHZhNklFd0dWeFd4VkE0TFZkU2FmR2tKTHA0YURlRkxZVkE3KysvOExDOEI4eWRvZWswNVVjdjJ5OGMiLCJtYWMiOiIxMmM0NDJlZjczNDgwMzhkYzE4MTdhYmZjNTM5NTk4MTEyYTQxZTdiNDI3YTE0MGM2MTU4NjQxOThhZjE0NTg5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inl1emorY2NoVW5sMk5ZL2VVbytEM0E9PSIsInZhbHVlIjoiVkpmQnhicUF3enE1Sm54ZVZBR0xJbEpDMk4wbW9TUGRzeXM0VnN0UUlNbHZEYUtxc2lkc0xCSXRuWmpsWElQaEtDYVZMNnhPeCtCT2hCUzRCd1pNTjgvQWYySldQZ0l5TG1VSlNZN1FtQ0xpNElpd0ZneE8rVjMxRDFWUllZajNSd2lmajBWYTN0cEwvaDZwUGlCUzhHSzZNSGN6b0VLdlYvQVFERnNBaUdSTENIdzdHK2hjNjQzRkZUbTV5cUFvZlR5SGE5emErOEF1U1l1L1pQMzNTbWI0Q2tab0wvQ1hxVWlodXhDWmVMdFlKZW10QjlSUldFVDloaEsrNEp6VG1ja212ckp0ZlFSV2pwaXBESW91bFlFSGMyYk9MQ2k3MlFGRDB0UTcvT0hJR1ViQlhHbkRrd3dZNmVJcXFnUVFtTHlLYThNeHpBTHhUd1d0QkJ6NEtIM2NMcW1CZEVDUnBlWEI0L2dzVWQrU3NKanloZ3UxTnRITnNmbWJaSS9wcUhJaXB1Q2pDNU14R0NQSmZpVmczVmpIZ2Z1QmtJUVNXSFRadjhyR2FTdTVwWjB0UjZxZXo2SHlUWERMcGtSRDN5bGZqeWZwajFIckJFc1lKZGlCUnpScklNdVAyQVE3V0RpbE5ubUk4cG1qeEM4cnBYL0xwSys0UmRoaDR0VDAiLCJtYWMiOiJkOTI2NzZiZmZjZjIyYWY1N2JlNmRhNWY4OWE4NDE3YWRmZDMyMTFkN2M1NGM0MWU0OWY3ZWQ4YjZiZjMwYzJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkQyQzdFaVpNSDJiYXo3ZkRXajBrRWc9PSIsInZhbHVlIjoiVVdKcVRGSFB0MXdsMEd5S295N3krUnJwcXVzUFpmbW9lK1lTQVBxZVFpcWg2NDZqZ2JoNnhjSFN2SDhIY0dUbDh6dE5sZmttcjV0L3N1dDh5S01RRXU4UTlyZlRBaHc3ZnJXdnkydm9ITUNhUHpXZjY1OU5BVEM2TW5pekxGSHRTMW5BOXFWN0h6bWROZFlnakVIREZ3WDk3ZEFqME1NSGVMZ20wRS9SdENBVkUzOFFac3AzVE5Md2dXWmdZTG5DODRmdXdPMGQxTnB2dTlsYSs1aDRIczZnNENsVkpHT3J1SEVqcytMbmw0Q25HWVhwUzk4aWZSWkJSalE4cFNJMWdTKzVZUktDdUR1ZFk3NktBSllZeHlONXZjWlB2MUtyYzJZRW1mUjF5Qm4vMlhISy9PcjZxZjBCdEozRnVrcWJ4UU9uV2tkVThMOHduY3ltalduMFJLaUNqNVdBZXRITGFxWEZCVUlYNG9FdzFuU3hKY0QrWk4rTGtDNTE4bk9ucS9IN3AzNHA4V1lDQ3p2eEpOZ1EySnBpZFN6ZnRBaEhjNnVnMENrcFp6dkQzMm5TTDk5QWt3ZVBnUGlVYmc2Ylc0MkNEMHZhNklFd0dWeFd4VkE0TFZkU2FmR2tKTHA0YURlRkxZVkE3KysvOExDOEI4eWRvZWswNVVjdjJ5OGMiLCJtYWMiOiIxMmM0NDJlZjczNDgwMzhkYzE4MTdhYmZjNTM5NTk4MTEyYTQxZTdiNDI3YTE0MGM2MTU4NjQxOThhZjE0NTg5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149321837\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-621363923 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621363923\", {\"maxDepth\":0})</script>\n"}}