{"__meta": {"id": "Xdeb59abf38f1587fa6fba14b43cc9269", "datetime": "2025-06-08 13:00:11", "utime": **********.759973, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387610.238895, "end": **********.760005, "duration": 1.5211100578308105, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749387610.238895, "relative_start": 0, "end": **********.552328, "relative_end": **********.552328, "duration": 1.3134331703186035, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.552355, "relative_start": 1.313460111618042, "end": **********.76001, "relative_end": 5.0067901611328125e-06, "duration": 0.2076549530029297, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45592312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025080000000000005, "accumulated_duration_str": "25.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.657283, "duration": 0.022940000000000002, "duration_str": "22.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.467}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7101102, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.467, "width_percent": 3.907}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7314441, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.375, "width_percent": 4.625}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-437387021 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-437387021\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-37696432 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-37696432\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1792603662 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792603662\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-558676826 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387607479%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNTNC9lUE1TNVJ2NmIvNXVYUjlGbEE9PSIsInZhbHVlIjoiUzBDamE4OVViYzJTbXhJMVNOYVN6dUFkVTFCNXo1cmszTk5BeVltNkxJdG5Qb0duNFRGb0F1Y0V6c2RodnVBRG1Bck9CNzZ2MVBUTmZjQ3k5UUZsLzNXVzBVRGtOOGdFZEpNTWVXRjFVRmYzcDVTcWpjWUpEUE5ib2xMQ2FYRzdORStLcnp1VGswQnhud2JsSlNrM0R2eVhmZ09JNVdCZ0xDRWU5RUUyUXJrRXJoUXlDSUVLT0tZNmVkdUh1bVRYZldCNm5ldlZ4bkV4YnUyR0QwTkZyVkFoU2Y2bXdJZGJxaGdlSlJsKzJJczNGZlV0SmFjZkdIREEwNjFDeEVwNkpHNDBia2ovU3hHaUpuMnVrdXM2d1dBQlEvWXE5UmUrVWcyUUQwYVlvWHlEdStydmY5dXZpeDhBd0kyVEx6YnROVmszZkQvbXVvWWFuN3Jkc1hWNnRjSlZDUHZsaEUwMThrZU9NWE1BQURVM2VxUlh4MlN1ZlFLak5tKzN6TURoUE5VV25VQVlmK1pGeWRmUWdGQ05MaDJHQXRQNkFvakQvVytBWm41SjVVOUxDNFVJR2NTVXdXTzdNc1E5WlJiOGx6QWw0YTgxMVpvNFZMMEdvU1JORHUvOW1sbThmZ3FiVWxLdkc2MHYxcnlzRkEyKzdmMUNqZjVRckQrcTB1T2kiLCJtYWMiOiI5ZjIxMjJlY2U0Njc3NDc2Y2YyY2ZlZjVkOThlNGRhYTEyMDNiNDgzZTQzZmZjNjM0MmViOWE2YjM0MGE3M2U4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImF2NGpla00zUHZ2cFQxMUZOZnN5SHc9PSIsInZhbHVlIjoiVlFHbE03Zi9CeDJpdkV0ZlBnVWNaQldWQjlTcXZlNFJLb0NQSE10RzYvN1VOeDNPV1lrZWxHM1BlV2lQd0V3ZGJGMVUzRGFCTFlsZi9vUTRic0ZEN21qTXdjeDZjNXdkVUpXZGVtamxUL21sZFVyQzVxY1FMRzEydlNwTlpSNW0zbXE3RWNBS0wzT3ptTUs2T3BoTCtxbXg1Z001KzIvYndUb2VpZHdVa3E4Wk1rSTcrbUlxZUkyOEcrazh0emFSa1o4dFhQeS9CMXErVDVYOEhhWmNKUHByTUlYK0tJOEFMN2RSSXhHY3MvQk5BR2hSNVdTbkExVDFMZ084Ymg2aEhLcFBlYzBHd1RVbGgvdnRRcENjYzhUMzA5NDhHTWhzOE14RER2WXcyQXBIL01XbmpUTDBhRG1KTk5KVy9BdFU3QnVDdnBCUW4vSEUyQ2tJR3U4MHdZRC9leDNDczc3ampIdXpueDY2QzBjb3dlcC9zVTVXZ0t0dm40aGRROEFlVHRZK0NMbU90RlhZd0w2bzVRazMrWlIvSm1yNUhxVUtBUjA0YWd2YllMR25BeC9PdmM5RVJtS1l1MHlyZXdGZitZZWExaEpYbGtyRHpXelFlMXRTQ0VndG9ldXVZc2V1RTVvZzhmYTdUaXdQc2VoVitXM2w5eUFheWM5dGRvMDYiLCJtYWMiOiI1ZjU5NDAxZTUwODZhMDM2Y2U3YzMxM2Q2Y2RkOTA2NWRmODQ3YjI0NmY5NmY5NTFmMDc1ZjQ3MjNmYmYyOTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558676826\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-133519035 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133519035\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-644110879 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkozOC94emQyVWd3OXZlWlpETnVNL0E9PSIsInZhbHVlIjoiNVJNd3pvN1NqK3V2c3dDWnN5TWE5QWxUQU9WbitJRDJ1dGRINlRnU01IZmNhVitJRDRjd3ZkMWE3M3I2MjFsczJRbVY3MHRqYUpTYXJ4K2tmSmtEMWlIRnBUaFZRNWpzL1RqTE5qMVFGY0JuS2FueXU2MmtyNWxEc0YzMzArdlNPMkNmNE9peUZNN2pmK2hwdkVySDVWK1RhckgxNUtrdVA4THkrb1ZKWGttb3RCa1dQUG0raDRzYWVaOHdxUEUwR0d6VFRieUxWY3lIVWtra3hnc3h6MjBsaU4xc3NYQW51ek9uU2MyMnJBVkZQTFVZM0xCeDkwRHY0STNnck1sNjU0L3VNZHNTWEVTL09RanJhZ1BNTUhFT0JweXVhYTZMVUg3N2sraDZNaUYvU1o1VVlXTVk1eXdZRHNibUcvaGIrU1JGMnlJTWZjZ1FlcEk5UnN5RlJCZ2xDWG5xTmNnaG5kOC9EVndEK1MwcWJNRzNSaEoyaWpSZC9rMkQzOWp1bjBtREVWT24rMGwybjN4L1pPL0lsU0VjVStMMmRuR1Q5VDdta1RTdlB0L0xEeFI5Mlg2eXUzOVNzY09lcVZPcXArazFLVTZzelJiUThlTlRPM0FZcWdBKzJISTZYMjNPbXFCZ2xiWVZEbzdZR0VpV3BrbUJSREdJaEhBeC9GRDciLCJtYWMiOiJjOWFhMDczYzk0OGVlMzVmZGNmZjFjMzYxZTg3ZWQ4OWY5ZTJjZTcwYWZiMWFlYTViYmFiYjI2MThhNDQ1YmRhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZHMG1ENEduTExBM3FXWEtaY050dGc9PSIsInZhbHVlIjoiRi9KRnlMWXEyMW9qRzdMeFZNb2NOM2crVXVEOGhJNDhDWDhRelZFUzdDczBCcVNGcGdteEpkVDhyMHRqZUhBclh3Qlk4cHRuTlhyWVdUYTdIU3NROUkrMjF5bTluS1Q5NnpaYWh3NlhMcW1pMUZuRHQ4ZU5KbjlPbDdDb3NoOGxTN1hZVGxMdVBrL0NQWjJKUlh4c1pNK3AxTlBOajhpb2Rzc2tZbXRoWUpvLzEycnpVQVgySi9KY3dXdFltVWFabjFhUU45TmorbkZPeW9ycWtiYXhtRjhWSXV6QVRWZjdmYWp4c1FDd1JEWUlFc1M5N0hvT21nRVJpSG9jK1NsQnBtYkdIREV6dUJoMzZQRUU0WTdFSWVZbXVOUDFZWnJGdldPV08xYTVneldlZGVJYlZHNlJhUTU4dncrV04yS1p2RU5lZGpqOFV4cXFLUVgyL2VRT3d0VEdaUHRpVGczd1FYVUdpZ2ZidHpXbytrVlpGbmIvMTRkSnR1VFNIMEFYTXdWUVQyMTUxVTg1WndPZnRkOE1XRCtidFhvd2tZczlWQStJcVRaZ2xtNmFFRGtzT0VkUjRXRUkxaGVzR3h5RE1xU09Jb2ZUdjhyZVZhWVdLWndBdGRnRTVjNm5VQWhBR1FmaU9GRmd3djJoNHhaQUpYR04vNTEwZ0FJRFlwbm4iLCJtYWMiOiIyMzcxNjIxYjRjNDZhYmM3ZDFkM2E0ZjhlZmZmMzQ4MWViYmI0NDYyOGExYmJlODNiNGJjNjk0N2E1NmE0MzVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkozOC94emQyVWd3OXZlWlpETnVNL0E9PSIsInZhbHVlIjoiNVJNd3pvN1NqK3V2c3dDWnN5TWE5QWxUQU9WbitJRDJ1dGRINlRnU01IZmNhVitJRDRjd3ZkMWE3M3I2MjFsczJRbVY3MHRqYUpTYXJ4K2tmSmtEMWlIRnBUaFZRNWpzL1RqTE5qMVFGY0JuS2FueXU2MmtyNWxEc0YzMzArdlNPMkNmNE9peUZNN2pmK2hwdkVySDVWK1RhckgxNUtrdVA4THkrb1ZKWGttb3RCa1dQUG0raDRzYWVaOHdxUEUwR0d6VFRieUxWY3lIVWtra3hnc3h6MjBsaU4xc3NYQW51ek9uU2MyMnJBVkZQTFVZM0xCeDkwRHY0STNnck1sNjU0L3VNZHNTWEVTL09RanJhZ1BNTUhFT0JweXVhYTZMVUg3N2sraDZNaUYvU1o1VVlXTVk1eXdZRHNibUcvaGIrU1JGMnlJTWZjZ1FlcEk5UnN5RlJCZ2xDWG5xTmNnaG5kOC9EVndEK1MwcWJNRzNSaEoyaWpSZC9rMkQzOWp1bjBtREVWT24rMGwybjN4L1pPL0lsU0VjVStMMmRuR1Q5VDdta1RTdlB0L0xEeFI5Mlg2eXUzOVNzY09lcVZPcXArazFLVTZzelJiUThlTlRPM0FZcWdBKzJISTZYMjNPbXFCZ2xiWVZEbzdZR0VpV3BrbUJSREdJaEhBeC9GRDciLCJtYWMiOiJjOWFhMDczYzk0OGVlMzVmZGNmZjFjMzYxZTg3ZWQ4OWY5ZTJjZTcwYWZiMWFlYTViYmFiYjI2MThhNDQ1YmRhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZHMG1ENEduTExBM3FXWEtaY050dGc9PSIsInZhbHVlIjoiRi9KRnlMWXEyMW9qRzdMeFZNb2NOM2crVXVEOGhJNDhDWDhRelZFUzdDczBCcVNGcGdteEpkVDhyMHRqZUhBclh3Qlk4cHRuTlhyWVdUYTdIU3NROUkrMjF5bTluS1Q5NnpaYWh3NlhMcW1pMUZuRHQ4ZU5KbjlPbDdDb3NoOGxTN1hZVGxMdVBrL0NQWjJKUlh4c1pNK3AxTlBOajhpb2Rzc2tZbXRoWUpvLzEycnpVQVgySi9KY3dXdFltVWFabjFhUU45TmorbkZPeW9ycWtiYXhtRjhWSXV6QVRWZjdmYWp4c1FDd1JEWUlFc1M5N0hvT21nRVJpSG9jK1NsQnBtYkdIREV6dUJoMzZQRUU0WTdFSWVZbXVOUDFZWnJGdldPV08xYTVneldlZGVJYlZHNlJhUTU4dncrV04yS1p2RU5lZGpqOFV4cXFLUVgyL2VRT3d0VEdaUHRpVGczd1FYVUdpZ2ZidHpXbytrVlpGbmIvMTRkSnR1VFNIMEFYTXdWUVQyMTUxVTg1WndPZnRkOE1XRCtidFhvd2tZczlWQStJcVRaZ2xtNmFFRGtzT0VkUjRXRUkxaGVzR3h5RE1xU09Jb2ZUdjhyZVZhWVdLWndBdGRnRTVjNm5VQWhBR1FmaU9GRmd3djJoNHhaQUpYR04vNTEwZ0FJRFlwbm4iLCJtYWMiOiIyMzcxNjIxYjRjNDZhYmM3ZDFkM2E0ZjhlZmZmMzQ4MWViYmI0NDYyOGExYmJlODNiNGJjNjk0N2E1NmE0MzVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644110879\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-311904115 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311904115\", {\"maxDepth\":0})</script>\n"}}