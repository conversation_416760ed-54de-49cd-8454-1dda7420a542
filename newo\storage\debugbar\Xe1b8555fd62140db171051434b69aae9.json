{"__meta": {"id": "Xe1b8555fd62140db171051434b69aae9", "datetime": "2025-06-08 13:44:59", "utime": **********.681812, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390298.317252, "end": **********.681843, "duration": 1.364591121673584, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749390298.317252, "relative_start": 0, "end": **********.406721, "relative_end": **********.406721, "duration": 1.0894691944122314, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.40674, "relative_start": 1.0894880294799805, "end": **********.681846, "relative_end": 2.86102294921875e-06, "duration": 0.27510595321655273, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53614912, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.024609999999999996, "accumulated_duration_str": "24.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.507278, "duration": 0.01843, "duration_str": "18.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.888}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.550075, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.888, "width_percent": 5.648}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.598585, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 80.536, "width_percent": 5.12}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6062179, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.656, "width_percent": 4.632}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.623892, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 90.289, "width_percent": 4.957}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.63717, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 95.246, "width_percent": 4.754}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1132300642 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132300642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.618898, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 20\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1716671603 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1716671603\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2053776763 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2053776763\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1724058095 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inh4cC9oOVd5K2FMRzBhTGtNVWdub2c9PSIsInZhbHVlIjoiN25QWm9ZQS9DWit5akVpTGxUYWtndkhIOXh5VlNwTGYzR3NkOXBuS2NaSUN4bWVyTUZuUkduRjVoa1M1T2NEbE02Qk1WbEEzcTZKL0NGNVBxcGRwTEFES2g5OWY3d0twMUhZMXllQ2R6My9NVGNYV0NoanAxRkhkRWhKdkNOZERJQmhCWk02MnJLZkVuTS9FL0xyK0hhVVg2dWFDRllmZXVtQnpVZEFYNDNuUkhMM0FTRUEzZkZuRkNUZUNmNG5OTDJ5Q3ZISG9wS0ZxUC9CNWI4U1VDcnBPQVlZTUVvQnBuSjFGUUJBemMxL0JYRlJHZ0VNYzhBMitBRWNiL1BrMVNyWUQ2aEU3aG9kNUk0bkpnYWExMUxuNk5BZFZWYkdHU0VBcmdYZERGZ0VXNC8wNnh6ZUo1WWUrc0RRd3RNaVNDNndvdGNxZjc4UDB2NXYwcWVXaUp6NFFwTEluZDdSdlZiNVM0aTlQL2ZWdUVLYWxXVDM2eUpWSHZaa3hrSFNWbzZQeW5nSk12V2RsaEhvL0JvWDg5ZUc1VmEwa2ZIdEVHbWJEK3BlU0hmdTBpWHNCV2VPUHdJSWpCcWF2ZVNxcE43dHBqampUUkoyczB3SWUwK2MrTG9LZVpwY21sNjB2QUc1UnVEcW1RekxDcXdpYlpNNjRKamlMVFhPaDFLcjAiLCJtYWMiOiJjNjVlOTNiMjY4ZWRkMjUyOWEwNGQyZDU5ZDM5MjYzYjY2ZTZhMTkwNDEwZmU4ZDA1MWRlMWM2ODU4MWRiZWVhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJ5TnhFT2pjTnhwUVU3WnlFK2IwQmc9PSIsInZhbHVlIjoiUUFNYm96OS9FRTFGNVpxQXhlT09OK0NMRGovZlBzNjFnMU1uS3RYM1NBTnNJd0hlUmxDVlZ4WkZKWmgyeEtpK2o2M0tTSXJMRnMrTlZPaWFaZTJLalhPL3A0K2pzaDNjVlZ4WkVRTTZNTWh0QVZ0SEJGUFRhZy9uTi9jRjY5SFVjUEZHSW9EMm1iaTlObDBsODBXNlR1aUYxMFhLUXFKbjFOVUtyNTRrZTlwTnhVeSs5MXVMbndxZ0xOZUZkQ0U1Yy9mV3kvYWRhaDgwTXJqRHg1T090REVQTk9BSXBobGp6YWJjUlVYRXgzS0w1S3l2cG96TEt4SERENUR3Uld0RkJaeUtoYW01eDBjTHJtL0t1VG1QQkMxM1U5RFUxZDc1MVlQa2QvZnB4REdGbFVJckg1QXB2L0xTTDN1dzhUbWZjRnN4OWMyd3d6eEV2MjV4TzE5ZUMvRzVBVUU0ZmNjWnAxMFVTQnBqa1BMNjJoRlpiTVdjbjVXbzJXOGlEektQdHNDTGxwVTV5eTZ1NFJBRkpUWlVsZ2hYVW9rMzlyNWdqb1owcFUwOXl6aVVZZE5XTW8wL3dWRUU3L2kzREg2aGJmc1BaWll0ZEVCek1jaDFZTWVlcDl3aHlUOHBzbTZ0T00za21tNi9vQmkwd240MmxkT0dnY2xvU09KSE5KU04iLCJtYWMiOiJjMmEwM2Y1MjRkYjljMTU2MDU0NDdhMmNhZTBjYTFmOGMzNDZhZGZjNGNmYzk2N2FmMWY0Y2M3YzZiYjMwNzY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724058095\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2140642179 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140642179\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1678258071 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNkeWc2cVRmVW90VXJ3QTZERm1qM1E9PSIsInZhbHVlIjoickw3SGxyYThJZ3FWZkF2T1pwaE9mS3pnSjlEc09OM3gzRSthQmRtdG9ETHdPMVBFTUo5RDhtVjQ3d0JQZEhaWWNqdGlMNWg2YVdoampoV3Ezd0F3bEtNcGF4MjJNbTlxRFMwQkU0dDVIKysvTklVejR3RjhuVmlxQVZmV3B2RDlIcmtxSlRVeTJxK0hQV2lRRDl3L1dtc1diK0tDb0tWYUFSS2lReitOVThIczBKa3NVNElZbHczbDhSL1JpS0l6ZW5qbDY5ZnRlTEIwdXFsdjQyYnE2OUF6c1pzSzBzS240Tm5Ub0h4LzRlaGlJS1AwN1QrZGJ5NjV0VWlUSVdzb0cxSkZ4Z05hMkxiRnNIN2h1dHVZUHB0U2w5dEdqa3M0dCtGNlZtZ2JiWnNVbGVMTXRjZmQ5NWxVMlhaSHhVeXhvQmJsbG9aMUExdHg5MVNVQ01BdTlzRGxVa21OTW1sVVJ2azJwbHZSTmJ2aXp3Z0o1TmhEYWVQT1cvMGRDYWVnR0RtRDJxbGFLTm5jTXc5N1hTRWcvcmNhNTZDTGpNK0l0dHROM1NRSzkxeTIrVWdGT3pFUjN4L2t3ZTFBMC9YRUUvUmNKdHJZMHM0aThYNUtRMmRDMENDNDZjWWNweXp1ZGt2ZDlhZFg4OUFwcFYxOTBIUVJkcWFWS1FYaTVvN3oiLCJtYWMiOiI4NGU3MTFiNzkxOWJlNjIxNWExMmY1Mjk5NDE2YjNlZTM0MjVmMDkzOWRjZDFkZGRlMDlhNGIyY2VkYzcxMDFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJ2cVA3V3VRZTdSYnZaSFVHZ0d0a2c9PSIsInZhbHVlIjoiN1pBSis3SEdGUElySHhVaDNpQmUzRjlCOXR3eWJTYmtNUnEvTjVTTThNekxaU2NsRGF3UUJ3WlhKUWlmSDIyVGFLUHVic3NaY1BuWHdwS2NTZTU4RHB4cVhScUFQNDBGWGNnWUp4YXF0WC9nS2hObDBLcEt3YktFUnpSQVBPRzcxbFhTUGRSa25nUWZaaDVPVVlweWhwQk9ScWRXWjdUNExia2RUUUhrRVJ0cHRZbUM3eHROTTdsZ0FJcm1Lb2VBcUJ2K0ozTzkwY1JOSnNQa0owV3ltR2F6cXBSNSt3c2JwOXlvc2F3UFdLbWZZbG1Md0FPZ243RzFhUjI4ZHhWbXh4YkNUM25UTFk4M2xTcVB6NWEvS1F1ZTRGMFlxNVVtSFNLMUZ5M3lXTXNoTVBBaVJ6QUFHUDlIOEZqSWJheStBTGFVejNES25CSThHdXFJSFlzVkFuUG4xTmRpcHBZUmZFUkV2UDA0RUhxMjFYbGZheW1mRC9WWjRhclJLNk5RNWZrVHh5M1htSkJRdWF3SmV4RmJLYU93VVlBMmh0Vmt0bVBQWVdxYjNzNU5xa1JvS2lvQWwwSFhoblp0elJKU0d6bDkwN05Nb0hCWW9JeTRncFJIVWs0M0w4dzJFSVRyS1Q5V0M0alRvVkFDV1p6MXN0bVB6bUEvUlpYK0tGOFIiLCJtYWMiOiJmY2E0M2I2NjAxNjUwZWIwODZhMzliMTA1YmI2MWUyZmY3ODUwZDg2OGUzMDhkNmIxNDcwNDc4YjBjZWRjMmI2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNkeWc2cVRmVW90VXJ3QTZERm1qM1E9PSIsInZhbHVlIjoickw3SGxyYThJZ3FWZkF2T1pwaE9mS3pnSjlEc09OM3gzRSthQmRtdG9ETHdPMVBFTUo5RDhtVjQ3d0JQZEhaWWNqdGlMNWg2YVdoampoV3Ezd0F3bEtNcGF4MjJNbTlxRFMwQkU0dDVIKysvTklVejR3RjhuVmlxQVZmV3B2RDlIcmtxSlRVeTJxK0hQV2lRRDl3L1dtc1diK0tDb0tWYUFSS2lReitOVThIczBKa3NVNElZbHczbDhSL1JpS0l6ZW5qbDY5ZnRlTEIwdXFsdjQyYnE2OUF6c1pzSzBzS240Tm5Ub0h4LzRlaGlJS1AwN1QrZGJ5NjV0VWlUSVdzb0cxSkZ4Z05hMkxiRnNIN2h1dHVZUHB0U2w5dEdqa3M0dCtGNlZtZ2JiWnNVbGVMTXRjZmQ5NWxVMlhaSHhVeXhvQmJsbG9aMUExdHg5MVNVQ01BdTlzRGxVa21OTW1sVVJ2azJwbHZSTmJ2aXp3Z0o1TmhEYWVQT1cvMGRDYWVnR0RtRDJxbGFLTm5jTXc5N1hTRWcvcmNhNTZDTGpNK0l0dHROM1NRSzkxeTIrVWdGT3pFUjN4L2t3ZTFBMC9YRUUvUmNKdHJZMHM0aThYNUtRMmRDMENDNDZjWWNweXp1ZGt2ZDlhZFg4OUFwcFYxOTBIUVJkcWFWS1FYaTVvN3oiLCJtYWMiOiI4NGU3MTFiNzkxOWJlNjIxNWExMmY1Mjk5NDE2YjNlZTM0MjVmMDkzOWRjZDFkZGRlMDlhNGIyY2VkYzcxMDFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJ2cVA3V3VRZTdSYnZaSFVHZ0d0a2c9PSIsInZhbHVlIjoiN1pBSis3SEdGUElySHhVaDNpQmUzRjlCOXR3eWJTYmtNUnEvTjVTTThNekxaU2NsRGF3UUJ3WlhKUWlmSDIyVGFLUHVic3NaY1BuWHdwS2NTZTU4RHB4cVhScUFQNDBGWGNnWUp4YXF0WC9nS2hObDBLcEt3YktFUnpSQVBPRzcxbFhTUGRSa25nUWZaaDVPVVlweWhwQk9ScWRXWjdUNExia2RUUUhrRVJ0cHRZbUM3eHROTTdsZ0FJcm1Lb2VBcUJ2K0ozTzkwY1JOSnNQa0owV3ltR2F6cXBSNSt3c2JwOXlvc2F3UFdLbWZZbG1Md0FPZ243RzFhUjI4ZHhWbXh4YkNUM25UTFk4M2xTcVB6NWEvS1F1ZTRGMFlxNVVtSFNLMUZ5M3lXTXNoTVBBaVJ6QUFHUDlIOEZqSWJheStBTGFVejNES25CSThHdXFJSFlzVkFuUG4xTmRpcHBZUmZFUkV2UDA0RUhxMjFYbGZheW1mRC9WWjRhclJLNk5RNWZrVHh5M1htSkJRdWF3SmV4RmJLYU93VVlBMmh0Vmt0bVBQWVdxYjNzNU5xa1JvS2lvQWwwSFhoblp0elJKU0d6bDkwN05Nb0hCWW9JeTRncFJIVWs0M0w4dzJFSVRyS1Q5V0M0alRvVkFDV1p6MXN0bVB6bUEvUlpYK0tGOFIiLCJtYWMiOiJmY2E0M2I2NjAxNjUwZWIwODZhMzliMTA1YmI2MWUyZmY3ODUwZDg2OGUzMDhkNmIxNDcwNDc4YjBjZWRjMmI2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678258071\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-24813383 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>20</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24813383\", {\"maxDepth\":0})</script>\n"}}