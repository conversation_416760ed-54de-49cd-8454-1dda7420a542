{"__meta": {"id": "Xd3fb76fa5e4f2a5f0c487d3c051d8936", "datetime": "2025-06-08 13:15:15", "utime": **********.848317, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388514.469071, "end": **********.848346, "duration": 1.3792750835418701, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749388514.469071, "relative_start": 0, "end": **********.668029, "relative_end": **********.668029, "duration": 1.198958158493042, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.66805, "relative_start": 1.198979139328003, "end": **********.848349, "relative_end": 3.0994415283203125e-06, "duration": 0.1802990436553955, "duration_str": "180ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024399999999999998, "accumulated_duration_str": "24.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.748418, "duration": 0.022269999999999998, "duration_str": "22.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.27}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8031569, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.27, "width_percent": 4.59}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8223472, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.861, "width_percent": 4.139}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos?only_available=1&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1717808471 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1717808471\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-48288228 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-48288228\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2108727322 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108727322\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-190128637 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">http://localhost/barcode/pos?warehouse_id=8&amp;only_available=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388512377%7C17%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImkrNVRMSEp2SkYxQnZFUmJDaFJXT0E9PSIsInZhbHVlIjoiNmcxUmdYdnVKenFqUGxwK2ZYK0pBZkt3OE0yVGtUMit3SkxkZjlsTWVkdGZXUTBsTnRIeTNSN0Z4d0JzVlBhUjhIc2RmWXIvQUFQWkZiMmhudkY5TklEUkZncEluZmJ0blBNTEtPcFBUa2ZWamNFY2FEL0NjWTlDMVl6ZmxaVWIyTktKNFdDUmFyVDBRV25QSjNHZ2lxcThxWU5ENkl3ams5Yk9PalUwbjgreWR4VGJTT0lPWXU1QWllMDlqN1l5ZGV6d2tscERKNG5PSkZMdkF3U3FZVDVHMVhNdkdFUXZKbjBXUU0yT2UzdnRsRHp6RzI5Rks5c2paVFIrN1hQM3RMZC9CQlpRdi9GWVY1MkNYTUpNeWFGNWJ1dC84Q21xSjVlZTJXdVd6RStIbnJOZmd6SkFQS0lZVjVhanNkS25zeTJuOEpWSTRqQXpRWnpQZzhhcGpXb2w1YmIxN0hWKzd3ZTdGU1N5Ync5eFRPVWZYd1BkSGZwT3liOUJGWTJ3R0w1R1RkdUxYdG5OSVkxMkhBUVpZSDZ6UWhOdUFjZ0NlTEhuNVIzSDQxTVNjR0RCOUZhR3oxY2Q0VWs3UE51dkFxOU5xWlQ0aGN4VHdHaUYzSFk2QWxKbXpUUGZOSzNNQzR5ZHZDNDk5bDNXdGJMR1JnSXh0akl5amZ5bkMzbmkiLCJtYWMiOiI4MzYxN2QzN2RmODQ3NDViN2U0MTIyMTJmNjM0ODM1ZmVhZjJmMTcwMjZjZGEwNDJhZDllZTRkOTRlY2M5OWQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhNNXhFTldyRi9XaHRUYm1sT0lOcUE9PSIsInZhbHVlIjoiYzJ3MDB6NWRaUnc0NC9sZDM2SVFqV2pWTmdkY1d5YnAxOEdjdFBhQUc5ajYyeDdRQmdUUTZwNUJ6THJQdWtoZzB1ZFpHeFRDL1BKTW54UEMrMXFTaFVZZmgrQ3lPQUlIWXQ4YldaYzA2ZXhkZVBVYlJQbEZ2ZUNRS3J1QmtVVzB6THdEMGsvektncTNjbTRGS0FlNWQ4RjFoTFB2MklYZVNrL1ZEMW9DNGJCWUUyMVM5cExWVTQ3VnBISUVSWk1iTWdmQmZTbFFxTGk4bnhNTzNEV3FRQWt6d0VKR20wK2pZN2YrZ3BHMFBBMGUwaUlsVGlvVksrTUtVV1QvREdpTnNYR1NBaklnRjV2dHcxckZ1am1oYnlVeDZZMVVmZHc3OGU0eC9yZXI2SG93Nk02dm1ZalY2U3JXUzZCVkVwUW5laC9qb1lRQnVuQU5aRisrRHFJaTc3Z0N4dDNUaElLZFpETCs3T3ZUTGVxVWl2L3dIZTUvQ0ppUkhjWlNMc20xNGpHa2xpRVQwa3p4NEhGdHAwZFRSRWVZaUo4TVIvRDJMb013em5hbUtkRERUUm12NkpQQVVWdjhWdGdKTHpoRTZxclk5L1ZUN0RzbWxtSXd0YnZUaHlUbHRZb3dCOU1VN2hDMXUwb2FyMU9RQjRUVkFYdkNuMHNNTklDeml3VE0iLCJtYWMiOiIwNGZjNGU3YzQxMjk1NzE1NDc4Y2VhNTM1NGYzYmZlNmUwZTExMzU0MzBlNjk1MTBjMDlhZDQyYmI1Njc0OWU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190128637\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1838852947 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838852947\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1592449156 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:15:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlIwSWM1ajRFRkhiOVJ3S3lDMTU0QWc9PSIsInZhbHVlIjoiZFVySnM1dHlLTVZZZHAxRExMVXlDOS9YRmhqWVhua3dRSEcvVmF5V2JoR2tJdEtSQnIyVm5RVWZzcDdzWm1HRUpXTFh3QldWcXo1MUhBS0IyTUtxaUJSTERXSVhmSEgxd2Jxb0poWmtXY1FlS1ZtbWdLMnJzaTJyUnJHdW1LalBJYUh5RDM2VWtsS3g2dXFnZ2ZPLys2VjhxWEUxOWJXZkpYV2FKYjl1ZmxxS3E1SlM5UTk2bFRHWE40aFVILzBUMTRDcDV1eSs4RDVNazVhQUQwdlNJRXJpU29yc0dPM3NFR2VOYk9TVDBFeDdGUnVubE5QZ3plL3U3OWlzT29BYkxZeGdScUJsaklwUmtCREt4MkVDbWdSM1Q3Ty9UaS9EUUN0NGZ2bzBjRUJrU2dJb0lEWUhxWm9UM3c5TlN0WUNOdUlHcVAybkEzOGZNVVpmNTZzNzlEK3FpellpaXBtQmo4WkRzK0NmeHdmUGhXS0RjQmVza3U3UDQrSEhQbU9nV1hRSjkzaERkQTBFT3JnaW9HYm12UFl0WFB4NkZBaThSekdFTUVMWExtMGk2N2NUNThXZHRmOTVkYTcvbUkrWUpDWlg2YTl5QXpPTmRCSmdWTGFIbG1mVDBIdkJucXN1U2piQkd1UUsyR0ExMno0VnEyTmFWdDJVNXhLY0NQR3AiLCJtYWMiOiJiNmZiYWRkZWIwZGI0ZDcyODg3Yzk1MDE0MWFkYWU1YTQyNzlhNTI3ZDIyYWIxMDkyNjc4NjA2N2IzMTk3ZDY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhvdE0zTzZDUnpaQjZYdkZnRDBwa0E9PSIsInZhbHVlIjoiSG80VGltNG5hbmVxbnJXQ1FyZXYveGFBcHBKUzcwd1JocVRPYVFCUE1VQWV2Z1AwV29EcU44MVVvb2NMR0xpUUx6a0FDenRJYUNGRzdNWFJQY3ZvYXhOdXcwdHVod2dHWmVhQXVLZUhmTG5xemljNnF3eEF2aW94STZOR2wvOXhkb2Jkdy91UVprZXV3UFpqRkV6L2I0aDVYSlNnZDBXS25ERVJLVWtRcEYzSDg0QkxIT284RlpaM2NTcDB4aFc1THFkdElBQU1LVXViYnIrMHNsU2JiR2QvbjZ0ejB5M2cvNTdmcDR4eXU0Yk5yNHVidHNGMGFsMmNyRjliZHBoVkZGaFdHOTd0L2R2Vm43emNTWEEvTHJZZTFNU1N5K2VtNDgwVGY0UDZQbWdPSkFCS25zeEVpTHlQaWpCdHZueGV1M2l6U1BTSUJNWW9iSFpjSS81cGswK1RMYW9FRWxDWHJhYWl2V3pNMmxYdnFiMmtSeGRHQnhqQ1UrOW1vOXRqWUhWbnVZVHFPZ1NzTTlidmRhVFRoMVV6QjVQNjdOcUZ4MnYwb2JjeDREV3lVU3lpS2dPaGRWd2ljRmovWVZvNytaRkRGbEdtc1luZCttUVNYeW1PcEp5aXNOWjRZRlpXUkljV3F5ZWZ3ZTRtMXhqNENXRWFlOUkxVldzblMvQWciLCJtYWMiOiI4ODQ4NWVhZjdmZmI0MjUzNzI4YWI4YmE4NmU1Njk3YjA4NTM1MTZiMTNiN2I0Mzk0ZTM5MjhlMDkzN2FjNzBlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlIwSWM1ajRFRkhiOVJ3S3lDMTU0QWc9PSIsInZhbHVlIjoiZFVySnM1dHlLTVZZZHAxRExMVXlDOS9YRmhqWVhua3dRSEcvVmF5V2JoR2tJdEtSQnIyVm5RVWZzcDdzWm1HRUpXTFh3QldWcXo1MUhBS0IyTUtxaUJSTERXSVhmSEgxd2Jxb0poWmtXY1FlS1ZtbWdLMnJzaTJyUnJHdW1LalBJYUh5RDM2VWtsS3g2dXFnZ2ZPLys2VjhxWEUxOWJXZkpYV2FKYjl1ZmxxS3E1SlM5UTk2bFRHWE40aFVILzBUMTRDcDV1eSs4RDVNazVhQUQwdlNJRXJpU29yc0dPM3NFR2VOYk9TVDBFeDdGUnVubE5QZ3plL3U3OWlzT29BYkxZeGdScUJsaklwUmtCREt4MkVDbWdSM1Q3Ty9UaS9EUUN0NGZ2bzBjRUJrU2dJb0lEWUhxWm9UM3c5TlN0WUNOdUlHcVAybkEzOGZNVVpmNTZzNzlEK3FpellpaXBtQmo4WkRzK0NmeHdmUGhXS0RjQmVza3U3UDQrSEhQbU9nV1hRSjkzaERkQTBFT3JnaW9HYm12UFl0WFB4NkZBaThSekdFTUVMWExtMGk2N2NUNThXZHRmOTVkYTcvbUkrWUpDWlg2YTl5QXpPTmRCSmdWTGFIbG1mVDBIdkJucXN1U2piQkd1UUsyR0ExMno0VnEyTmFWdDJVNXhLY0NQR3AiLCJtYWMiOiJiNmZiYWRkZWIwZGI0ZDcyODg3Yzk1MDE0MWFkYWU1YTQyNzlhNTI3ZDIyYWIxMDkyNjc4NjA2N2IzMTk3ZDY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhvdE0zTzZDUnpaQjZYdkZnRDBwa0E9PSIsInZhbHVlIjoiSG80VGltNG5hbmVxbnJXQ1FyZXYveGFBcHBKUzcwd1JocVRPYVFCUE1VQWV2Z1AwV29EcU44MVVvb2NMR0xpUUx6a0FDenRJYUNGRzdNWFJQY3ZvYXhOdXcwdHVod2dHWmVhQXVLZUhmTG5xemljNnF3eEF2aW94STZOR2wvOXhkb2Jkdy91UVprZXV3UFpqRkV6L2I0aDVYSlNnZDBXS25ERVJLVWtRcEYzSDg0QkxIT284RlpaM2NTcDB4aFc1THFkdElBQU1LVXViYnIrMHNsU2JiR2QvbjZ0ejB5M2cvNTdmcDR4eXU0Yk5yNHVidHNGMGFsMmNyRjliZHBoVkZGaFdHOTd0L2R2Vm43emNTWEEvTHJZZTFNU1N5K2VtNDgwVGY0UDZQbWdPSkFCS25zeEVpTHlQaWpCdHZueGV1M2l6U1BTSUJNWW9iSFpjSS81cGswK1RMYW9FRWxDWHJhYWl2V3pNMmxYdnFiMmtSeGRHQnhqQ1UrOW1vOXRqWUhWbnVZVHFPZ1NzTTlidmRhVFRoMVV6QjVQNjdOcUZ4MnYwb2JjeDREV3lVU3lpS2dPaGRWd2ljRmovWVZvNytaRkRGbEdtc1luZCttUVNYeW1PcEp5aXNOWjRZRlpXUkljV3F5ZWZ3ZTRtMXhqNENXRWFlOUkxVldzblMvQWciLCJtYWMiOiI4ODQ4NWVhZjdmZmI0MjUzNzI4YWI4YmE4NmU1Njk3YjA4NTM1MTZiMTNiN2I0Mzk0ZTM5MjhlMDkzN2FjNzBlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592449156\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1917610028 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">http://localhost/barcode/pos?only_available=1&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917610028\", {\"maxDepth\":0})</script>\n"}}