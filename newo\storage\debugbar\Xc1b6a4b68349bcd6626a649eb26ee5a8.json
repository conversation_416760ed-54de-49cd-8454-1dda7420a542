{"__meta": {"id": "Xc1b6a4b68349bcd6626a649eb26ee5a8", "datetime": "2025-06-08 14:41:15", "utime": **********.097822, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749393674.539016, "end": **********.097851, "duration": 0.5588350296020508, "duration_str": "559ms", "measures": [{"label": "Booting", "start": 1749393674.539016, "relative_start": 0, "end": **********.019415, "relative_end": **********.019415, "duration": 0.48039889335632324, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.019431, "relative_start": 0.48041510581970215, "end": **********.097854, "relative_end": 2.86102294921875e-06, "duration": 0.07842278480529785, "duration_str": "78.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43933616, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01831, "accumulated_duration_str": "18.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0643559, "duration": 0.01725, "duration_str": "17.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.211}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.086668, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.211, "width_percent": 5.789}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-53428828 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-53428828\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-428405378 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428405378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-712085783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-712085783\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1633643409 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749393346722%7C61%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5HckdlblJ2RXY3QjBvaFJORUdSVnc9PSIsInZhbHVlIjoieUxyUFVUdC9BalBSdXVzZTN4YmF6U1ZJcEU3OEQ2bW01ZlRRQS9jY3NUcXBaK0ZjcHJHUGVaRW1qS1ZLV2RpUUhBbThzcUJlQ0xtOFBURHRsWkZhRUVoTDU1bDNqREpNODNHd2Q5bWhLaXJuNXBIR21MaFhzT2puQWNkMDNrL1NGT2hpczV6V2xQek12QnB4dkhVK2JyT2h2b2tOS0dkS0tNeXJHYkpxaVVkcmM3N0ZrdVZZdlZ6SEZBQmtXbFJoWVJVWE9MeTAxWFM3UUtuRGh2Nm42QkN0aHFMeElFSCsySFpwRm8wRjVMNU9VVjRUeWhKTmFZMzIxV0RwcDVNd1Jobys0VTJtOFhiSm1jYnN3N29jb1ZzSUJJcS9qZjVuL2JadUY3elpVQzNRWFpYTlNDUmVzUk9ZcDlqdytqZ1dROEt1YmdmcGx3ZHhOUmM3T3M5UVVZd0VYV3R0M3pzTHVQN3U1YmNOSUxRVEwyVWpKU3Y5VTVibzZUNjhqYi94K0I4bDZBZVJzU2VDdm1OQm5qWVh4Q1RvUXo1MlFiK3QzVDdGUjRUeWVFZzRUSkNjZjBEVWZMSmRvMHNHNzRFT0hFSDJhaXZkYTQvZ25xeHZwbkxKVG0yVG1WTGtydWwvMjlxU1ljUVpoNU52aTNCVWEyZlZybTVBNnVIWUYwR2siLCJtYWMiOiI0ZDZiMTEyNDBmMGMyNThmMmZiZjVhNjZmN2QyMzVhMThiMTdhZjYyOTMwOWYwYTg0OGE4NzkzYzUyYzI1M2JkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilh2MGJoQjBzNis3eUoycm52ODhvelE9PSIsInZhbHVlIjoiRXRsZ3VjeXJQMEM4d0R1SFVrNlVweTNCOWM4ZnJ3K01aS0tNT2RKZS9wSGhQazA5aUthQVlCcklwYi9rMHN3OWhDamZqeC82OWF4TE5tamdLN0ZQenkrREhqUzl4MHJ0U3ZGWHVkZ080TjJIaXA5enoxV290R013WGRmZ3ZmYi90bWxXMXZ5cmhMTW44cm5OK2lDNk9TeXdaTThlMUJlMERKNWNCK3lVTWtQNXBuSERYZXRROS9ZcWdZL0R6bDBHZGxTTDY3bUYySXZWajhLRlMrWGFTbmVUY3UyeThzTkJueTZCYXdTRC9nVEJKQm9yYmFkS1VKV1JTWHYvQys2SDZ6Q2U5cjM1RFNacWZRNFpzRlEzd2FIQjJuTmgxZ2M2KzFkWmJOSGxQMzNzME9FaTBoRlNnWkp2S2ZmU3JEYlVkcStZb1pndVhlYW92dGJCaEhWSVRmU3duamxydTRqSkl6WUUwdVdESk9lY2s4c1RGMDRDd0NhYmV1bWk0TVhybnEwSUtHanZyQXZpNUY0Zis3d2x0MytLQUNHazZYTWlVYWR3MFdTdGE2ZVZGZjNMdXFBa2ZUUnJRZ2xGRjFpOHRMV2doTHBNMFpKYWZkcjVjVFdNVTgrMjNacVh3MSt6L0VGTHhITHEraU56dk9HbVdxRmdtQXVWZUs0ZlNHK0MiLCJtYWMiOiJmMjVmYmIyYzkxZjQ4MTdkZDgwZTFjMDhjYTljYzYyYjgwMGRjZTM0OTI2M2U5ZTU1YTQ2Y2M1ZmRhZDM0NTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633643409\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-639147064 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-639147064\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-194054213 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:41:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ildac05GWE9LRWhyZFl4VnFja1ZNT2c9PSIsInZhbHVlIjoiQUlRMGhqYWZaeUhzcGpWdGllWEcrbUZqN0gyQmNpUGRBS0N4U0VhdTZweW5rcldnaFlKSWNPTTBKVzJNbTkwRUpOZVN3RUVKZ0pnaUY2ZTlKcjM0aEkrRndPZU9wL2xQQ2QxdHd5d0NiWHR2SmtvSTQ0U0lYYXpyK2N3ZXhtczNBNmFOenk5eHRPNkVOQzBlaHJxZHBud3RCVUo2QS9zWjFrK3EvWmdRbDFkSGovT3JsOHMydHZaMEN4c1BocVNYa1JBM1JHUGozeEp1Wk5ZdElwMHpsY0hpU29OcTY2S043WnRwbllBeXVIRXFaUk5NMGE0U0R1RXVMcWZJVVZEeVVCNCtDYkxuN21LS0t5QUJXSDF4RTlETDhmdTJXRWtKSXN5QnJJS3JtRkQ1bVBISTRzakJpdnBJNzNtb25aS1lrL29MVEZGR0hqU2NTYzBzRmtMSEFNVktsTUFYblJkYjNOUHl2bHI5U0tLQkdKWitYNzRqWUE1bjdhOFkxVlB4K3FuQ0YvU0tHMFZSRmJ1MVdaa2UyZWhwK3BWTDBqbDJyOG5NbnlCZUFFSEdQNElKQytwSUtGTEw4bGxTK3V5WFdkVkZLMUtjZ1FoRFNoVWRqd0UzTGx6QityOXAvaUR1TGs0d2RLVG5LUS9VRWhtSlJSSGNlZW9meSt2Wlgya3ciLCJtYWMiOiJmYjZiZjczYWQ3ZTNlZThjNjk3NzdkYzAzZTBiMTlkZjMzYjc0MDdlYzc1ZWZmNDYxYzBlN2ZlZDkwNjU5MDJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:41:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldoL1BwcFVhdXBYaUt5czBBTE5vcUE9PSIsInZhbHVlIjoiRkc0UWkwNzJBY08xRkQ4K3kyLzVjVzFvdSttYWpmZDN5bm5jL3pYVTlkbUR1T1FKMnJtR3hwK084d3RqQkh2aVFOTTlLWjJtL2MzbnMwRHZkeUhhdG0vN3ZLcFFBSUlnRDkrOGdrQmJ5alBnN0tWaFBSNVI5U0ZYOXFMMjlCT0VBaU1sWm5FMkFXTEg0TFVOa3RnOXN5UTV2eWNjcnc5NndicXR2NEdIelVQbk1ZcUNFcml1YWVEdGplWGgxWmNKTGtnVDNCRTd5cmVUN0YyMFdLcDcxWHNMR1BucDZhY0lFWDM2NXl5anRrRjd1bHJobkhtTUVrcFZZRXo3czhBVThJd0QyamhvWDNac01SQXhZdVM2RjdYNFM2emNSVVlqNTJaZGVaQW1DdXFIZHJCRHlNdTIrTGU3TmEvak1aUTBQVjdRUm1aMzJHakZvbzdLUzJUK21kZm04cTRSRitSMzB1bGFnOXFlL1J4WTViTGw3dW56U2tKNmhVKzk3MUpQUzFIbVM3NFFHWGhZb284RVUxM2ZnRkFBcVlmOXQ1Vk9zeXdGZGJ6cnRKVGd0YWx1Rno5REhkUE0vS1ZKOXN3aXNYbGErMHpZR3JJUzdQRFJiVXlkZGZWbDZrUXNNOGhaSjVkRGtpWndoVVpzODJ2MEhKMDhtWjNOY1JMUzdPSTIiLCJtYWMiOiJmMmE2MTk1NTlhZGQ3YTEzM2M2ZTYzNjc3NGZjMmE5YzMyYzZmZGIzNWZiMTZlOGZiMjQwYjAyMTAwMTA1ODM0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:41:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ildac05GWE9LRWhyZFl4VnFja1ZNT2c9PSIsInZhbHVlIjoiQUlRMGhqYWZaeUhzcGpWdGllWEcrbUZqN0gyQmNpUGRBS0N4U0VhdTZweW5rcldnaFlKSWNPTTBKVzJNbTkwRUpOZVN3RUVKZ0pnaUY2ZTlKcjM0aEkrRndPZU9wL2xQQ2QxdHd5d0NiWHR2SmtvSTQ0U0lYYXpyK2N3ZXhtczNBNmFOenk5eHRPNkVOQzBlaHJxZHBud3RCVUo2QS9zWjFrK3EvWmdRbDFkSGovT3JsOHMydHZaMEN4c1BocVNYa1JBM1JHUGozeEp1Wk5ZdElwMHpsY0hpU29OcTY2S043WnRwbllBeXVIRXFaUk5NMGE0U0R1RXVMcWZJVVZEeVVCNCtDYkxuN21LS0t5QUJXSDF4RTlETDhmdTJXRWtKSXN5QnJJS3JtRkQ1bVBISTRzakJpdnBJNzNtb25aS1lrL29MVEZGR0hqU2NTYzBzRmtMSEFNVktsTUFYblJkYjNOUHl2bHI5U0tLQkdKWitYNzRqWUE1bjdhOFkxVlB4K3FuQ0YvU0tHMFZSRmJ1MVdaa2UyZWhwK3BWTDBqbDJyOG5NbnlCZUFFSEdQNElKQytwSUtGTEw4bGxTK3V5WFdkVkZLMUtjZ1FoRFNoVWRqd0UzTGx6QityOXAvaUR1TGs0d2RLVG5LUS9VRWhtSlJSSGNlZW9meSt2Wlgya3ciLCJtYWMiOiJmYjZiZjczYWQ3ZTNlZThjNjk3NzdkYzAzZTBiMTlkZjMzYjc0MDdlYzc1ZWZmNDYxYzBlN2ZlZDkwNjU5MDJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:41:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldoL1BwcFVhdXBYaUt5czBBTE5vcUE9PSIsInZhbHVlIjoiRkc0UWkwNzJBY08xRkQ4K3kyLzVjVzFvdSttYWpmZDN5bm5jL3pYVTlkbUR1T1FKMnJtR3hwK084d3RqQkh2aVFOTTlLWjJtL2MzbnMwRHZkeUhhdG0vN3ZLcFFBSUlnRDkrOGdrQmJ5alBnN0tWaFBSNVI5U0ZYOXFMMjlCT0VBaU1sWm5FMkFXTEg0TFVOa3RnOXN5UTV2eWNjcnc5NndicXR2NEdIelVQbk1ZcUNFcml1YWVEdGplWGgxWmNKTGtnVDNCRTd5cmVUN0YyMFdLcDcxWHNMR1BucDZhY0lFWDM2NXl5anRrRjd1bHJobkhtTUVrcFZZRXo3czhBVThJd0QyamhvWDNac01SQXhZdVM2RjdYNFM2emNSVVlqNTJaZGVaQW1DdXFIZHJCRHlNdTIrTGU3TmEvak1aUTBQVjdRUm1aMzJHakZvbzdLUzJUK21kZm04cTRSRitSMzB1bGFnOXFlL1J4WTViTGw3dW56U2tKNmhVKzk3MUpQUzFIbVM3NFFHWGhZb284RVUxM2ZnRkFBcVlmOXQ1Vk9zeXdGZGJ6cnRKVGd0YWx1Rno5REhkUE0vS1ZKOXN3aXNYbGErMHpZR3JJUzdQRFJiVXlkZGZWbDZrUXNNOGhaSjVkRGtpWndoVVpzODJ2MEhKMDhtWjNOY1JMUzdPSTIiLCJtYWMiOiJmMmE2MTk1NTlhZGQ3YTEzM2M2ZTYzNjc3NGZjMmE5YzMyYzZmZGIzNWZiMTZlOGZiMjQwYjAyMTAwMTA1ODM0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:41:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194054213\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2091014055 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091014055\", {\"maxDepth\":0})</script>\n"}}