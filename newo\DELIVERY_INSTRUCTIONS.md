# تعليمات استخدام نظام التوصيل المحدث

## التحديث الجديد: حفظ مباشر بدون شاشة اختيار نوع الدفع

### ما تم تغييره:

1. **عند اختيار عميل لديه صلاحية delivery:**
   - لا تظهر شاشة "Select Payment Type"
   - يتم حفظ الفاتورة مباشرة في جدول POS Summary
   - يظهر تنبيه: "هذا العميل لديه صلاحية توصيل - سيتم حفظ الفاتورة مباشرة في POS Summary للدفع لاحقاً"
   - يتغير نص زر الدفع إلى "حفظ طلب التوصيل"

2. **بعد حفظ الطلب:**
   - يتم إعادة التوجيه تلقائياً إلى POS Summary
   - تظهر الفاتورة بحالة "جاري توصيل الطلب"
   - يتم تسجيل المبلغ كعجز في Delivery Cash للكاشير

## خطوات الاستخدام:

### للكاشيرز:

#### 1. إنشاء طلب توصيل:
```
1. اذهب إلى POS ADD
2. اختر المستودع
3. أضف المنتجات للسلة
4. اختر عميل لديه صلاحية توصيل
5. سيظهر تنبيه أصفر: "هذا العميل لديه صلاحية توصيل..."
6. انقر على "حفظ طلب التوصيل"
7. سيتم إعادة التوجيه إلى POS Summary تلقائياً
```

#### 2. تحصيل دفع طلب التوصيل:
```
1. في POS Summary، ابحث عن الطلبات بحالة "جاري توصيل الطلب 🚚"
2. انقر على الطلب للدخول إلى التفاصيل
3. انقر على زر "تحصيل الدفع 💰"
4. أكد العملية
5. سيتم تحديث الحالة إلى "تم توصيل الطلب ✅"
6. سيتم تحديث إدارة النقد تلقائياً
```

### لمديري النظام:

#### مراقبة طلبات التوصيل:
```
1. POS Summary: عرض جميع حالات التوصيل
   - 🚚 جاري توصيل الطلب
   - ✅ تم توصيل الطلب

2. إدارة النقد: مراقبة عمود Delivery Cash
   - العجز: الطلبات قيد التوصيل
   - التحصيل: الطلبات المكتملة
```

## الميزات الجديدة:

### 1. حفظ تلقائي:
- لا حاجة لاختيار نوع الدفع
- حفظ مباشر في قاعدة البيانات
- إعادة توجيه تلقائي لـ POS Summary

### 2. تتبع محسن:
- ربط كل طلب بالكاشير المسؤول
- تسجيل تلقائي للعجز والتحصيل
- تحديث فوري لإدارة النقد

### 3. واجهة محسنة:
- تنبيهات واضحة للمستخدم
- أزرار مخصصة لكل حالة
- رموز تعبيرية للتمييز السريع

## استكشاف الأخطاء:

### مشكلة: لا يظهر التنبيه عند اختيار عميل التوصيل
**الحل:**
1. تأكد من أن العميل لديه صلاحية delivery مفعلة
2. تحقق من صلاحيات المستخدم (Cashier أو manage delevery)
3. امسح cache المتصفح

### مشكلة: لا يتم الحفظ عند النقر على "حفظ طلب التوصيل"
**الحل:**
1. تأكد من وجود منتجات في السلة
2. تأكد من اختيار المستودع
3. تحقق من وجود وردية مفتوحة

### مشكلة: لا يظهر زر "تحصيل الدفع"
**الحل:**
1. تأكد من أن الطلب في حالة "جاري توصيل الطلب"
2. تحقق من صلاحيات المستخدم
3. تحديث الصفحة

## ملاحظات مهمة:

1. **الصلاحيات المطلوبة:**
   - Cashier: يمكنه إنشاء وتحصيل طلبات التوصيل
   - manage delevery: يمكنه إنشاء طلبات التوصيل
   - manage pos: يمكنه تحصيل طلبات التوصيل

2. **إدارة النقد:**
   - العجز يُسجل تلقائياً عند إنشاء الطلب
   - التحصيل يُحدث تلقائياً عند الدفع
   - جميع العمليات مرتبطة بالوردية الحالية

3. **الأمان:**
   - جميع العمليات محمية بصلاحيات المستخدم
   - تسجيل تلقائي لجميع المعاملات
   - تتبع كامل للعمليات المالية

## الدعم الفني:

في حالة وجود مشاكل:
1. تحقق من ملف `test_delivery_system.php`
2. راجع ملف `DELIVERY_SYSTEM_README.md`
3. تحقق من سجلات النظام (logs)
4. تأكد من تشغيل migrations
