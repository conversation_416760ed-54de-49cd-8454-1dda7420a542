{"__meta": {"id": "Xbe1764d9076f10f57a42a3586ed0eeef", "datetime": "2025-06-08 13:34:36", "utime": 1749389676.050002, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389674.724572, "end": 1749389676.050032, "duration": 1.3254599571228027, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749389674.724572, "relative_start": 0, "end": **********.894248, "relative_end": **********.894248, "duration": 1.1696760654449463, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.894273, "relative_start": 1.169701099395752, "end": 1749389676.050036, "relative_end": 4.0531158447265625e-06, "duration": 0.1557629108428955, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45604728, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0063, "accumulated_duration_str": "6.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.976767, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.873}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749389676.006475, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.873, "width_percent": 17.937}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749389676.02312, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.81, "width_percent": 16.19}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2099215462 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2099215462\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1294404196 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294404196\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-156148104 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389653152%7C28%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdLUkpCaVVURnFTMm5tRW1aN3JLUnc9PSIsInZhbHVlIjoidHdnRnVqcnJ5MDBrY2h1VG5nZzd0NC9wMUEyajRYY2JNdVdIWDZ5SHFVUkFCZnZva09Xd1AxQ2lnNFF6bVFXZ0FETmx0cVVtRm1aOHZEWjhKUGJEMGVFbWhuNTB0bG94a2RPbncvemNOUWFzamE0Wmt1Ky9oRGtuMzZHM1ZSN01mMCs2Wko3cVZqMjkydlIrdGpDWUUyNGlXTG9ZR2VKMm1GOW03cUl1UFJWQXFzM0J4SzEyZnRqYXkrTW92NE8wVzFnMTlWajZiVlVWWlkzMnRwVFJVSWVPMWRNOEFXMGdGTXNnZVNSbmtrdmlBS2NIT0J6Z3ZoSjRIbW9LZWo3L0I4UTViZklYNHhWcjhIbnZqVVRaSGkySFQ2SWlLMEFpWVR1bGNZMTltd3Znd1E2TFNWQlZXdy9jOUUzSEJmQi9pT1ZKWWwweldPUDRyZ2pFSVVoOUNSMytKZW9OQzFveVhFVlZMNjc5TEt2SFpxeGhsZmJFdXBzQXRRVndGeTR2TkVDZjBjSnE5MXpCSW9XSFpmaldEaUQ2a3p1NmFtK3FmSEF2RkVESkpFbGg5di9YM2lVbjBkUU1TMUdoOW5wR21Rd1VMM2FGa3k1ZmsyMk04cVdjSG94MkdxaUk3bk82N3RZMmFSUS8xcStOR3U0QjhqdTFNeEZMWnBmRCtzclUiLCJtYWMiOiI1NzE3OTAyNDM2MDMyNDcyNjI2NDJlNjhkYTA1MDcxY2ZmNTk3NWIyODk4MGNjNzdhYzgwNTM1ZTljNjA1MzUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJRdDk1QThwRVBjZTNRazRibUVQVlE9PSIsInZhbHVlIjoiN2xHYXp6SUVBYS8yR0s0djY1bW81U3FmaXdTZ3hDVS82TFpvSC9VTldYOTZDd2RGelYxQk5NMGV3Qm5yTkZpV3BocTBicXNtZUo3cVpyRXYxOGt3Yy9ibDBYRmJBdVordUNPalpWYitrQUlMMVVGL1orRkJlaEJ4TzJ4YVNFd1dwdlJtNHNTWnM0VHVXd0NBaWZPdUdSakgwK1pQSjBzV3lCOTNJYVZZRE5nc1hPRTVQaVM3a0FsWjF2SXJVaUhEOC9XZzA1UzhxTkUzbW5McnBzSEIvOUtJeWJGb2R2UW9aWGZJekVFTXlYVEVYMWpxQWk5clhoR0tmc2xQRExsOGFxcUxadUY5OEdsbmxjMEhubWNBMDcxZTk2RG9IU3pnaDlMRkpYSDRvd2s4RU83d1Y4VW5kYzdRMk5nT0dmVXlBODFJcjlyeGMwT0JkTFFMUWpRbDRVZ0dQMHRlNE9GTmdNbXRRditKQjJXL083NXBMdnVXYWRwUGY0NzRUR1lyTnA3a3hHMjA0ekxpN0tUSlB3MlZzVFhDRVNTWUdGamlhSVllTXM4VUlhTDRIdnRqSEhSemhBYzNLczYxNFVLQS9VQWVqeHNiTXNJUWhOcXlZTW9oZjgrQzArT08yQ3JwZXpTYmtnWTU4OER3MkJnTmc1dWg3V1dmSmJlZnFPMzYiLCJtYWMiOiIwNTY5N2E3ZjgzMmRiMjZkMzdkYmNkNmNiMGQ1YjYyMzA2YjJhNzI3MDBjZThhZjIyY2EyZmI5Zjg3YzhlOTU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156148104\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-394766466 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394766466\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitZZjFOcW8wNjlFYnM0dGpvL2dKaFE9PSIsInZhbHVlIjoia0gvZXNVUXZjb2MxT2xvaWtrOTJMWEdDc3gza1FobzFROHN3bkd2aVJGeTlRQ0JIV0xLVmJKNTNmdFdlTjJRV0VzMi9DVE9wMkNWSFpaanY5ODdiNUZGRjdsZG1JdjBITVptK29pTWhldHIyY2JEaTg3dTFpaEprSFlGTmN1bjR6MzdpZGkxVTdJck1DSmY2Skp1V0RLcFN3ZWJHSzFNM2h1QXN6ajg4ZWc5dFZJRGZzcWVHYUtkdTVGT2c2T0oreHFMNytKeVgvUnJ3blhMenEzVHdUOUdJQzkzM29kZWZ6enczaVBQdUlxcWwzN0lHcWtlc1RkSFp3T3p6Z3FRa2htOW9OdGZva1pKaDhlQmYwMFYwS2g5RWFoV1Ivc3g1TWZ4aUFhQTRLa3VwTk13NTU3OHc5akVRNDZNWGxDa21kTjlWY05Pc3pvY0pQck40RVI2Q2ptYzBSMk9qYXN6aFkydFRtSVYzTWtLV2hIL3h1KzZEMGhrWkJKTUIxSW1YVUdOM3hGTkdySnBHU3lPRVdtZ0VLazNUaEt2VDJiUFVZWXd4M2ZSbmpmaGJvRVRLeVNYQ3JDazdkMTVpNkNiSkNlZ1JuTU9VZ3JLbGRzS0hTbVRCOXFEVUZLRUtrT3poTFppcVdZaHJRbGdWRVNoS01CeGJmdUFidm43aVNvVzkiLCJtYWMiOiI3ZTNhYWM0YWZiNTE5MzYxYzA3MzIzNTk0MjhkNDc0MDZjNTBhZTNkNzdjNzU4ZTE3YTMyMjNjNmVhYjQ4YTRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJ0Q21La2Q5eVdwNmIwYlFEV05mVkE9PSIsInZhbHVlIjoiMzJMODgya0k5cHRhL3RxbVM3T0pOelZLbmhqeUhjUnJTUU9EYzFrelZGb0orOVJpeVFOcE9KbU1NVUhwNnhyRmhNSXZtUzFOaTQvOFBxWVV1Q1BoVTVaMk1INFVTUWdYV2h0MGJOeUUzbzZMeUg0K0pMbnplSWFxa2Ztby9JeE4vSDEwendPZUkvV0tuaTJONnhiYzFra0FYa0dwTWU0TlhNeHovQmZ1LzkweGZWTGhpd0pmWGhWYm56Z2xLVXZDbkdQK2pvRXZxV2pGcWZUUmJrTEJ3R1R1VzZ6c2p2emE4VHJXdzNTbE5saWxnQ3JYdkYrZ2k3SWVVeERPV2N3YlBFWWhhSEdYb2g3MWRqMTRrdERQU0ZrN1dOVUpSVUViZEc2UFhOWXI4Umh2dGplUm1QSHl1aEhBejU5TDYyaU5iVzZjcUZ6MVR3OEVURnlDdkpaUlVxa21IMjBTVGVPNktUeDEwWXZTakQ1NGlKcFlDaWZSdDlHdS8xbnRDNS9qaDhIdDVkZlVobkVMd0FwUExIa3BiYStpZzRZSzR2QlZlTDVIeFJ6SzV2YjUxckxFVFNhcllVYWNBQXp3SG9saGk0SThsR0hTaXMxNDlXUklmNG93STdvZkJsYWtmTk03Y21SV3NrRS8xYmJqSEI4UlBteERiQzhnQVVjQXhneVAiLCJtYWMiOiI4OGQwNGI2OGYwM2VkMTc1ZjAwZmMwMTFhNTYwMGMyYzRiNTRiZTk2YjFmNGNkZGYwZDZlYzdlYjc3MTBjZjcyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitZZjFOcW8wNjlFYnM0dGpvL2dKaFE9PSIsInZhbHVlIjoia0gvZXNVUXZjb2MxT2xvaWtrOTJMWEdDc3gza1FobzFROHN3bkd2aVJGeTlRQ0JIV0xLVmJKNTNmdFdlTjJRV0VzMi9DVE9wMkNWSFpaanY5ODdiNUZGRjdsZG1JdjBITVptK29pTWhldHIyY2JEaTg3dTFpaEprSFlGTmN1bjR6MzdpZGkxVTdJck1DSmY2Skp1V0RLcFN3ZWJHSzFNM2h1QXN6ajg4ZWc5dFZJRGZzcWVHYUtkdTVGT2c2T0oreHFMNytKeVgvUnJ3blhMenEzVHdUOUdJQzkzM29kZWZ6enczaVBQdUlxcWwzN0lHcWtlc1RkSFp3T3p6Z3FRa2htOW9OdGZva1pKaDhlQmYwMFYwS2g5RWFoV1Ivc3g1TWZ4aUFhQTRLa3VwTk13NTU3OHc5akVRNDZNWGxDa21kTjlWY05Pc3pvY0pQck40RVI2Q2ptYzBSMk9qYXN6aFkydFRtSVYzTWtLV2hIL3h1KzZEMGhrWkJKTUIxSW1YVUdOM3hGTkdySnBHU3lPRVdtZ0VLazNUaEt2VDJiUFVZWXd4M2ZSbmpmaGJvRVRLeVNYQ3JDazdkMTVpNkNiSkNlZ1JuTU9VZ3JLbGRzS0hTbVRCOXFEVUZLRUtrT3poTFppcVdZaHJRbGdWRVNoS01CeGJmdUFidm43aVNvVzkiLCJtYWMiOiI3ZTNhYWM0YWZiNTE5MzYxYzA3MzIzNTk0MjhkNDc0MDZjNTBhZTNkNzdjNzU4ZTE3YTMyMjNjNmVhYjQ4YTRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJ0Q21La2Q5eVdwNmIwYlFEV05mVkE9PSIsInZhbHVlIjoiMzJMODgya0k5cHRhL3RxbVM3T0pOelZLbmhqeUhjUnJTUU9EYzFrelZGb0orOVJpeVFOcE9KbU1NVUhwNnhyRmhNSXZtUzFOaTQvOFBxWVV1Q1BoVTVaMk1INFVTUWdYV2h0MGJOeUUzbzZMeUg0K0pMbnplSWFxa2Ztby9JeE4vSDEwendPZUkvV0tuaTJONnhiYzFra0FYa0dwTWU0TlhNeHovQmZ1LzkweGZWTGhpd0pmWGhWYm56Z2xLVXZDbkdQK2pvRXZxV2pGcWZUUmJrTEJ3R1R1VzZ6c2p2emE4VHJXdzNTbE5saWxnQ3JYdkYrZ2k3SWVVeERPV2N3YlBFWWhhSEdYb2g3MWRqMTRrdERQU0ZrN1dOVUpSVUViZEc2UFhOWXI4Umh2dGplUm1QSHl1aEhBejU5TDYyaU5iVzZjcUZ6MVR3OEVURnlDdkpaUlVxa21IMjBTVGVPNktUeDEwWXZTakQ1NGlKcFlDaWZSdDlHdS8xbnRDNS9qaDhIdDVkZlVobkVMd0FwUExIa3BiYStpZzRZSzR2QlZlTDVIeFJ6SzV2YjUxckxFVFNhcllVYWNBQXp3SG9saGk0SThsR0hTaXMxNDlXUklmNG93STdvZkJsYWtmTk03Y21SV3NrRS8xYmJqSEI4UlBteERiQzhnQVVjQXhneVAiLCJtYWMiOiI4OGQwNGI2OGYwM2VkMTc1ZjAwZmMwMTFhNTYwMGMyYzRiNTRiZTk2YjFmNGNkZGYwZDZlYzdlYjc3MTBjZjcyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}