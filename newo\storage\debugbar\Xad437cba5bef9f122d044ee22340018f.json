{"__meta": {"id": "Xad437cba5bef9f122d044ee22340018f", "datetime": "2025-06-08 13:35:58", "utime": **********.451294, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389757.265368, "end": **********.451321, "duration": 1.18595290184021, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1749389757.265368, "relative_start": 0, "end": **********.305819, "relative_end": **********.305819, "duration": 1.0404510498046875, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.305838, "relative_start": 1.0404701232910156, "end": **********.451324, "relative_end": 3.0994415283203125e-06, "duration": 0.14548587799072266, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45186992, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022770000000000002, "accumulated_duration_str": "22.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.381485, "duration": 0.02067, "duration_str": "20.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.777}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.425163, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.777, "width_percent": 4.875}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4337862, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.652, "width_percent": 4.348}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 2\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 20.0\n    \"originalquantity\" => 20\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 40\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1297972617 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1297972617\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1941894113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1941894113\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1819106633 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJRUGIxNitrVHZueXB0NDFFYXQ3ZVE9PSIsInZhbHVlIjoiVlNIbHBRbFVVclU2Mm9WT1NDN3FxMTlydnlETjFRc0V6NXpHM0lrWWNzVEQrcHY3cWpqT0NnR0o1RnlNV0tCZkhvNUh0Y1h5VGw0TE5wSjJLNVZucytKTytEM0JXZk5TdWZIMURKOXFSczg1YU00RXNMTUU5L2ZZVUpQbXJGcWM4VGd2VGRQZXhhQlpHcG94b0JCeVdWYXhESVNaWHA4c3VBTHcwdTB6NjFqay9ZOW51aHRyazNNNFMvMVhYVWg0RWMvc3N1U1ZzMzlnc2VpMW53Z1l6Y3NzQXloaHZGWk03YVZDM0JQT2FMeXJoNk5udmZsdzJzT05HWjRjcXRybkpzRDN2WFlxUEdXMDdxQjBHUEo2cGhQVnR5azRCSlpBZnlVbzdTeGRMcXp1bjhZK3B6bzRpTWpKQUNJdTljeXYwdHpnNW93YnR5NHFNVERZR1YyQ1dvYmR1YlBWRVpxcEVRalY5NzhaNHMwMlAxc1YxL0ROT2VOU0w1YjdwTStDSVlSM240SEV5ZkhDNDNXTURUWGNrb1RQSzl1Vk9ZSVRvSzFPNlBlbGVjekFoV1NJSUc0U3JpU1owYmpEVVRmd1M2YUtHdE9xamhxL2VlVTFwVEtwaW5GaG41WWxWbkFMeERUTUR2U0FoUXNleVAxNi9lVFBvV0ZzUXlFTFNManIiLCJtYWMiOiJmN2VjYmI2MWJhMGFlMmY5M2NjNmUwZjVlYmYyYjIxOTAwMjVjODVkY2JlNTkyZjYxN2QyODdkZWRmNjdmMjZjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlR4SnNoWWVXRDlUMmhreXFsNUZlS0E9PSIsInZhbHVlIjoiQktQWTk3WXlmSGZDYjh5MzdsNXZHa29pKzdVV3A1SVRCdXQxKzhFZW5zV3RxYnBGY1J3OXVrVWF0eXFsbldMNjJuWmx2ZFcvWE8vLzdCUVpCK1g1NTBDKytIV3NZQkI1SEdyaTRzU3pTeUoxRnVBQzQwZCtFNW9XUENjL0k2blFWQW5wNW4vc0JaV3RvRGppRzluZ1hJck1IOGdkUDY3Nm9qTzlrMG5HNzRzWVA2bVZRUGlMcWM4YWhuZHFINlRncVl1cms2NTl6czdaSHQrVDlRVWNiMHJ4VjNxWUdpY0x0OVIyNmxDS1BaVXRGcTZ3cGljTmZBcDlyV3prU1VCRXNzRTkySjVHQ1R4RlBuUWRxK20zOVg1YUVtUFlsOE52bWpnY1NhRXhyaDFXRU5PUnRva01sUys2cVJZVWZQSHZSWXZnd3V2NWZDZFJ5Q2JSZjRiNER1dVFNK1lyVlYwSlNVRjFFa1FJOEZDcmFxNGRVek9SLzZmdmlRNytOZkd4NGxUeDBOb2NUYW9XSzdJclRBM0lQRjU2VkJ5cVdxcDNnL2EzTmU0LzBOTzFVS0QwVXZpQnBiZFREcHFodElRa1MzM2wyV2dUQXkwMmJGY1BHUTVmRUlUMlJPc2grZkxNdmw5YlQzbnYxM3lkRlZqbXRmTmFtQkkxalYrR0U2VkwiLCJtYWMiOiI3Y2MxMjdkNTNjMTE4MTA5ZTI2YjhjMjgzMWZjYTVmYThkNGU4NDY3NDc0NTdlODlkMjEwYzEyZjIwYzQ5MTQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819106633\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1804538063 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804538063\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-211996705 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:35:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjE3M2hjWXcyZHZIaUdraTlYdVVxOWc9PSIsInZhbHVlIjoiakdFNlBuRGN4SjNWNTYzVmNpNVVzSXZ0NWlXK1NnU2JOWmJpcVI2N3lxSkczbExERktCRmFIaTl2bE5jWE9nOURGNTRmNWZrQ2s5U2xBOTY3dllZMTJ0S3lrNUZFeTcrQTdLc0JrYVJYWDZjaUUvVU9NOFFsTDM1bGQzanN0R2M3SG5pOFU4TUFNSDAvTVRnN2xaamEvWEhqbFFCTzZya2J3SWN1ZVBuSWdtNHd3UTRCSWFMMDBvWmlTMWxsK3hDOXdBUlJISCtURG1lRWxvTnpTVGRvaXV4cTlzQmZCN0FXN3pSYm5KYUFWeG9hSjU0S1VvWXNabzZ5djQrNTkzVlVhclg0M0J4NWJiMVJHbmg5RUx2ajVMMlRuSTlYTlEvZVVhdVJCUkVtUjVraGFxa0I4d04zY3R4MEhPQUJZVDZvTVIxLzlLMVU2eDF1cHBvYVZFV3ZZRmtoZkZEQXBNN05YS29MOVE4KzhONEtlSEZQalVvc0ZibmNWNklxVk8wTHhyZXRmRmRYWmJ2TTltWUc2ZHFVMTVvNHUzZnF5NVdPYjlRNmk3KzlnbVRnY0pMdHNJd1dEVWFsd2ZYYmk2YU5SNm9BRnJwVGRQUm45RGNpRHhweHVURmV4Z2VPMithbGR0bDN1dXkyb3FWSWhpM2Zha3V1WFYwV2xLK1F4QnAiLCJtYWMiOiI2MmI2OThjMTVjNDA4MzY3ZmQ0YTI0NWY4MTdkMWVkODRjYjM4YWI5NjRhMTJhNTlkMWM3NzAxMThhYzQ3ZTBjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhHelptc2Jla3F2ZEtLMVRBdGhZTHc9PSIsInZhbHVlIjoiNk4rMWlmM0hNU2htaHZTekE4M3pScjR0aFpzcjJlNUUzdlhUN0luTWlOSFhpRE9KUnExQm5lam5HR1pMR3FpSWcwOXJjMW1GNHIrZFc5TGU2WlFDcmRNNHQ2VzVzK2Y2ZVV3Tnc1WS9qN2NkaXZXdk5qNjFhSzFWcU1OOWRYR3lhMURINVVvK0QraFVmQ1c2UXZwS3p3NlE1ZGgzbUk5VkxVUXpKWU1hODJSbzNNZlZNbmoxMkNOY1FScUVkbGJsUUNLMFphQUtjODJBTXh1U05BRFpSbHE1R2x0WnNWK01DSkh4aE00elRVdDdiSUhtenhzK0Zxc0U5MThnMmRjaVRMSWwyY1drbHcrbFVCQkFuMllRRnJqd0djVVVaNzh5S1p0Ym1LNlpVRHVreEhQQk1PMHJtRkZFdWtjNjJraWtiTG95bml3dHU0UHVpQ2lYcjNLNFExVUpqb3VCYjNzUDZWZ0szdHdJK0Zrdm5hVC9hZDl4MjhGYUpaOEQyQjdFdjJTQzJZYWNUUlRRcG5ZTzFmeDd0NW1YVW9ZK0IyRkp6VEFzMjBZUXpubUNEdmhQdHIxVjdvQnFiVDU5ZmNMK1JlSnYvNk45U21LT0tuZjV3bXlsNC9HcE1IR2h0cFJLWTdFZlhoTEVzNGxZVDRwR2ZhV296K3JYS0U3eHdMVDEiLCJtYWMiOiI0MjBmZDM2OWM5YTlkYzM0MzRkNmYzYzY5ZWY1MWQ4YTc3N2Y1YWE5MzBiMjNlMTcxNjE5NmI2ZDFkMTc2N2ZmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjE3M2hjWXcyZHZIaUdraTlYdVVxOWc9PSIsInZhbHVlIjoiakdFNlBuRGN4SjNWNTYzVmNpNVVzSXZ0NWlXK1NnU2JOWmJpcVI2N3lxSkczbExERktCRmFIaTl2bE5jWE9nOURGNTRmNWZrQ2s5U2xBOTY3dllZMTJ0S3lrNUZFeTcrQTdLc0JrYVJYWDZjaUUvVU9NOFFsTDM1bGQzanN0R2M3SG5pOFU4TUFNSDAvTVRnN2xaamEvWEhqbFFCTzZya2J3SWN1ZVBuSWdtNHd3UTRCSWFMMDBvWmlTMWxsK3hDOXdBUlJISCtURG1lRWxvTnpTVGRvaXV4cTlzQmZCN0FXN3pSYm5KYUFWeG9hSjU0S1VvWXNabzZ5djQrNTkzVlVhclg0M0J4NWJiMVJHbmg5RUx2ajVMMlRuSTlYTlEvZVVhdVJCUkVtUjVraGFxa0I4d04zY3R4MEhPQUJZVDZvTVIxLzlLMVU2eDF1cHBvYVZFV3ZZRmtoZkZEQXBNN05YS29MOVE4KzhONEtlSEZQalVvc0ZibmNWNklxVk8wTHhyZXRmRmRYWmJ2TTltWUc2ZHFVMTVvNHUzZnF5NVdPYjlRNmk3KzlnbVRnY0pMdHNJd1dEVWFsd2ZYYmk2YU5SNm9BRnJwVGRQUm45RGNpRHhweHVURmV4Z2VPMithbGR0bDN1dXkyb3FWSWhpM2Zha3V1WFYwV2xLK1F4QnAiLCJtYWMiOiI2MmI2OThjMTVjNDA4MzY3ZmQ0YTI0NWY4MTdkMWVkODRjYjM4YWI5NjRhMTJhNTlkMWM3NzAxMThhYzQ3ZTBjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhHelptc2Jla3F2ZEtLMVRBdGhZTHc9PSIsInZhbHVlIjoiNk4rMWlmM0hNU2htaHZTekE4M3pScjR0aFpzcjJlNUUzdlhUN0luTWlOSFhpRE9KUnExQm5lam5HR1pMR3FpSWcwOXJjMW1GNHIrZFc5TGU2WlFDcmRNNHQ2VzVzK2Y2ZVV3Tnc1WS9qN2NkaXZXdk5qNjFhSzFWcU1OOWRYR3lhMURINVVvK0QraFVmQ1c2UXZwS3p3NlE1ZGgzbUk5VkxVUXpKWU1hODJSbzNNZlZNbmoxMkNOY1FScUVkbGJsUUNLMFphQUtjODJBTXh1U05BRFpSbHE1R2x0WnNWK01DSkh4aE00elRVdDdiSUhtenhzK0Zxc0U5MThnMmRjaVRMSWwyY1drbHcrbFVCQkFuMllRRnJqd0djVVVaNzh5S1p0Ym1LNlpVRHVreEhQQk1PMHJtRkZFdWtjNjJraWtiTG95bml3dHU0UHVpQ2lYcjNLNFExVUpqb3VCYjNzUDZWZ0szdHdJK0Zrdm5hVC9hZDl4MjhGYUpaOEQyQjdFdjJTQzJZYWNUUlRRcG5ZTzFmeDd0NW1YVW9ZK0IyRkp6VEFzMjBZUXpubUNEdmhQdHIxVjdvQnFiVDU5ZmNMK1JlSnYvNk45U21LT0tuZjV3bXlsNC9HcE1IR2h0cFJLWTdFZlhoTEVzNGxZVDRwR2ZhV296K3JYS0U3eHdMVDEiLCJtYWMiOiI0MjBmZDM2OWM5YTlkYzM0MzRkNmYzYzY5ZWY1MWQ4YTc3N2Y1YWE5MzBiMjNlMTcxNjE5NmI2ZDFkMTc2N2ZmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211996705\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1400800044 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>20.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>20</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>40</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400800044\", {\"maxDepth\":0})</script>\n"}}