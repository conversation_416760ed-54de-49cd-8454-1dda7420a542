{"__meta": {"id": "X6adbbdf297d1ed93ec29e4a64849ce6e", "datetime": "2025-06-08 15:09:57", "utime": **********.960835, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.145339, "end": **********.960862, "duration": 0.8155229091644287, "duration_str": "816ms", "measures": [{"label": "Booting", "start": **********.145339, "relative_start": 0, "end": **********.847199, "relative_end": **********.847199, "duration": 0.7018599510192871, "duration_str": "702ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.847216, "relative_start": 0.7018768787384033, "end": **********.960865, "relative_end": 3.0994415283203125e-06, "duration": 0.11364912986755371, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45603776, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01352, "accumulated_duration_str": "13.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.891736, "duration": 0.0115, "duration_str": "11.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.059}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.922522, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.059, "width_percent": 8.062}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.93849, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.121, "width_percent": 6.879}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 33\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1780896584 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749395395437%7C72%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcvVmZEeGNubVdieW9kcUlvbjhzNWc9PSIsInZhbHVlIjoiZjJ2WFhzMW5HQkVtcGUxUTVSeTZPZjBlU1VCUSs4TTF3d3BvQnliditjYzV2MG4xbzNrdm5mWkxyYkV4KzBRT215ZVJSNlhBR1JLZm9XNGg2ZVBrMEt6cnVad0Y3WUFyZEltekkwUGlXZmtHcS9wQzMvb3Q1VkcrUnhNQ1BwQ3Jpb3g3TGhYZE1lc1VOQjlzeXdVdnV6MTM4T3JtdHpOWmFycjd0OERkTmpoRzZhcnF3Skx2STVnTjlmY0pLT3dNNWMrL3IzblNma21qVUJ0alZDUHQ2NUV6b2dSZUFBbHlsdmZpWW9DTmNUUHZEVkdUZ1pFWndEbkJRUGI1QkhBZm1PMjFucFdnWmY4TmRvemxaNnVJYmt1Z3NteVpkeTlkOW9lWE1CQ0NjbWVka2dPcEV2Tk5TQWU1MW5POENQTzFXOGR0dlBPTmd5V204a3Y2V2xhM3hGVGVvK0RFQnJBZ0k3ZDBJRTlaVUU5ckdjK2RvYXc3N3FEZmdMcWYrdWRDRVBveWxZL2pDTjJnOThndlYrdFNuODFjanBpelh6eEVSODlrRGNWdDNaOEtnUk5VbkxkQ1lqblhwNit4Y0FxaGtIcXFhSHNVTUxxRS80YkloVWtPQ0NVQVJFcUFRbXBnUlVEUzB6T0FYZUxpd3NxNStRM0hGOC96aERNbkhqNm4iLCJtYWMiOiI5MGQzOGMyNjgxZWMyZDAxZGQ0Yjg4ZTBlMzZlZjc1YmVjYzFkMzM0NmE2YzA4NDI1MjE4OGM2MDQ4ZGY1YjNhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVQd1dRRlduYWpUMXdOS0Jkak11bXc9PSIsInZhbHVlIjoianB2aGJlMU5DQmNHYjFZM0dKV1JDUzVpc0s5N3hYK21XSlkrWlVJMXg0SElaOWYzK3l2OE16U2o3ajIvREZXbmNSVC9GRTJaMkZwU0dwNlY2aXZJaC82Wkc4WGRNMHl4NXp3Y1dQUlpEYkdtaFJxTkhkVlA3aE1wQXhkWEgyQ2R1U0NCWjFndzNQd0ppUTUya0k1ODhvSGprNmlFUlRFeXlmYXRPT3dsR1FIMTZ4OTRibSsvZUJLbDdseXBFTk9DOUwxWFQzWDNFbHFRdTA1aDJSYTdhTGhoS2hVLzU3KzYvWGJWajJUSFFvcjJaYUVDSFBiUzJVWTM1bFlrdHgyc3JiWDZOMnVoQzVVZTJ1VjY3Uk9CdkQ0SS9XeEdqWDhxQXZ5NnJPZUptQWQ5a2J6Sjd4eVVZeWhka1plZmlDYkdyTlJIVm9NQ01FdSsxTklLUkV2RGhzOFg2VUFURDV1MnR3azBnbGVsMmNabGVQb3FqZzNYYStpZnVxU0taUjl2QUhBdmJJUm1XVWNRUVpCaGdqRHArQW94MzZjcW9zckNIZndwL2xURnh5UlZhYVdTZkJjV0FHdVR1ZEtVTTNob0wwRHN0VDF4TE8vZWpKUlF6bEFBY3htbTErVXhlN0lTQUFvZVZKb2EwUjBxK2phOXF6MGZGNExtSFNNbzkyVG8iLCJtYWMiOiJmYTNhMTQwNjIyNDZhYjUwZTJjNGViYmJmNWFkOWZlM2FkODMyMWIyYmQ5YjQwN2RmMjc4NmNmN2RiY2RiN2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780896584\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1068477354 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068477354\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1693624642 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFzM0lHa3h3TjFjR3ZZbVUxeTAvZnc9PSIsInZhbHVlIjoiN1NOWUZFVlFMTThRdWhHWFkwZkc3RlMxZXNVaDhYTk9SU2N2WlVMWm9paDRaNjFTa2ZHM01DaDU0WndZeUN3MTdMc0lRZkQwcVpOYjBpR21pMW92eEszQ01lKy9mRU95aS9qOHBVQURTanFjaUt2ZUFId1BpU1cxL3VLeGY1a3NHQzVNanBtbE9ackU3NHNTdFcyMTJQSW1MbXNscnhDTnNNMFpKcDk3V0tLYzQ1TXkxTW1wVEZNcWZVWWF5QmlTNFB3RStsRXN1Wjc3bG9xRlpkQzNXZEtiaGFyT1B3N05jQlJlRHVIdEdlNTQ2S1NyTG9BQTdrL1JMcSs2d2RBWTI0bU9pZzRUVjhQMmlXV2lQQ0hQQkxpRmhQcEVpRElKZ2pXMXdUaXZGb0ZyVU4reERwa2dURm1LQnJYS1d0RUUrNnpWNlZWR0NwcGdhbkpYMlNxY1R0Q0lHWTUxYmZqZ2kreFdyaHl5Rk5JK0tlUXpFUVhKdjFqaSsrVDdPZ0F3QmpQZkxFL2N4ZSsyaitTaXppU2pWUy9wSEFVeTdHNkI2QVVFOGhjMHpJNDhkdlYxRHhsbG5zRUZjaG1OaHdOeHNSa3g3dnBhbG1hcTBPUVFPaGxNMS9mb0REQTdQK1p5UXVkUEV4dmdnNWQxQjdPc084N3VXS0U2NnJ0dTF0Y2UiLCJtYWMiOiJhMWY1YzA4MTZjMTlkMTc4NjU0NjY4MDRhMDBlOWYzZThkNzQzZGViZGVlMzQ0Yzk5NTQ0ZWZiNTg0ZjAzOWEyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjIrbXR0a1NLNmhMRDMrRlFZZWI3bEE9PSIsInZhbHVlIjoiNkxucktjQ1JPWVg4T1JYZ25MOXRSdHBraW9jaFBzWENoVk1YaXRiczNqeWY4RVhqcFJrR3RidzQ1eHkwWkVBRVBadmt5dmQ0dzBpT1hXd2kwT3E2UWk0MHZZcXVTMFBubzNaWTV4QmF2VnBLOG40WlJrbTI5VGJxa20xbXlQSVRpZzlRK0VsQkFMbWZrdGNYWlhacUxrbDZuTkl6RXZ2U2RLRFUxTmpyWVNaRkpDcm11dGV0R1hhU3NVMGtZeXRQUnhTUk4xZ3pNZ0RBNWJsaDdERnQ2RU8zdllqRGJFSkRiVTdoSmFEaUJYd2txcmE4YWxJdEVaemg1RklUREJPekJMUUNMUDZnV0E0aEZtSUFkVWo4ejllK0FVUy9FOWZYa0pEMTkrVDRYVWIwTVRqMVJLZ2FlaHlXV1ZRTkFFMnh6UXl6VURMa2lHak41bDRrd2ZzTUUxWGVQeWYyMnlVcFl3UXJjTXcyRG5mRUtqSFlDTFFCL2l1TEtiWEhyMnZzdWpOUXZQMlBnRXIzUG45MDhBQks1UHEzMnF5bkhFaHl5bWJaemlmdkxXMGM5akRzM1NUN1NvQUVPY2t0dCtzR1laeWMxeXhYQVJxcVh5TUlzTXpsZ2ZyT2c5SEdmbEtRNFpkN21nNTFIZDY1N3RudHRER0F3S1J5M0RZaDBYZTMiLCJtYWMiOiI5MDgwZmU0OTEzZTYxNDc4YjVhM2FlODI3YmRmM2YyNTMyYjk5OWFhMWUzNjU0NTRjY2I4NzFkMTM3OTRlYzIzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFzM0lHa3h3TjFjR3ZZbVUxeTAvZnc9PSIsInZhbHVlIjoiN1NOWUZFVlFMTThRdWhHWFkwZkc3RlMxZXNVaDhYTk9SU2N2WlVMWm9paDRaNjFTa2ZHM01DaDU0WndZeUN3MTdMc0lRZkQwcVpOYjBpR21pMW92eEszQ01lKy9mRU95aS9qOHBVQURTanFjaUt2ZUFId1BpU1cxL3VLeGY1a3NHQzVNanBtbE9ackU3NHNTdFcyMTJQSW1MbXNscnhDTnNNMFpKcDk3V0tLYzQ1TXkxTW1wVEZNcWZVWWF5QmlTNFB3RStsRXN1Wjc3bG9xRlpkQzNXZEtiaGFyT1B3N05jQlJlRHVIdEdlNTQ2S1NyTG9BQTdrL1JMcSs2d2RBWTI0bU9pZzRUVjhQMmlXV2lQQ0hQQkxpRmhQcEVpRElKZ2pXMXdUaXZGb0ZyVU4reERwa2dURm1LQnJYS1d0RUUrNnpWNlZWR0NwcGdhbkpYMlNxY1R0Q0lHWTUxYmZqZ2kreFdyaHl5Rk5JK0tlUXpFUVhKdjFqaSsrVDdPZ0F3QmpQZkxFL2N4ZSsyaitTaXppU2pWUy9wSEFVeTdHNkI2QVVFOGhjMHpJNDhkdlYxRHhsbG5zRUZjaG1OaHdOeHNSa3g3dnBhbG1hcTBPUVFPaGxNMS9mb0REQTdQK1p5UXVkUEV4dmdnNWQxQjdPc084N3VXS0U2NnJ0dTF0Y2UiLCJtYWMiOiJhMWY1YzA4MTZjMTlkMTc4NjU0NjY4MDRhMDBlOWYzZThkNzQzZGViZGVlMzQ0Yzk5NTQ0ZWZiNTg0ZjAzOWEyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjIrbXR0a1NLNmhMRDMrRlFZZWI3bEE9PSIsInZhbHVlIjoiNkxucktjQ1JPWVg4T1JYZ25MOXRSdHBraW9jaFBzWENoVk1YaXRiczNqeWY4RVhqcFJrR3RidzQ1eHkwWkVBRVBadmt5dmQ0dzBpT1hXd2kwT3E2UWk0MHZZcXVTMFBubzNaWTV4QmF2VnBLOG40WlJrbTI5VGJxa20xbXlQSVRpZzlRK0VsQkFMbWZrdGNYWlhacUxrbDZuTkl6RXZ2U2RLRFUxTmpyWVNaRkpDcm11dGV0R1hhU3NVMGtZeXRQUnhTUk4xZ3pNZ0RBNWJsaDdERnQ2RU8zdllqRGJFSkRiVTdoSmFEaUJYd2txcmE4YWxJdEVaemg1RklUREJPekJMUUNMUDZnV0E0aEZtSUFkVWo4ejllK0FVUy9FOWZYa0pEMTkrVDRYVWIwTVRqMVJLZ2FlaHlXV1ZRTkFFMnh6UXl6VURMa2lHak41bDRrd2ZzTUUxWGVQeWYyMnlVcFl3UXJjTXcyRG5mRUtqSFlDTFFCL2l1TEtiWEhyMnZzdWpOUXZQMlBnRXIzUG45MDhBQks1UHEzMnF5bkhFaHl5bWJaemlmdkxXMGM5akRzM1NUN1NvQUVPY2t0dCtzR1laeWMxeXhYQVJxcVh5TUlzTXpsZ2ZyT2c5SEdmbEtRNFpkN21nNTFIZDY1N3RudHRER0F3S1J5M0RZaDBYZTMiLCJtYWMiOiI5MDgwZmU0OTEzZTYxNDc4YjVhM2FlODI3YmRmM2YyNTMyYjk5OWFhMWUzNjU0NTRjY2I4NzFkMTM3OTRlYzIzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693624642\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1074172396 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074172396\", {\"maxDepth\":0})</script>\n"}}