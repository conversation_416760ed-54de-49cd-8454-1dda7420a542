{"__meta": {"id": "X653b39e30cd0c38da480ef75e2741d66", "datetime": "2025-06-08 13:15:13", "utime": **********.355688, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388511.98577, "end": **********.355716, "duration": 1.369946002960205, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749388511.98577, "relative_start": 0, "end": **********.19828, "relative_end": **********.19828, "duration": 1.212510108947754, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.198297, "relative_start": 1.2125270366668701, "end": **********.35572, "relative_end": 4.0531158447265625e-06, "duration": 0.1574230194091797, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45596592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01703, "accumulated_duration_str": "17.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.275579, "duration": 0.0148, "duration_str": "14.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.905}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.313772, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.905, "width_percent": 6.166}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.330975, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.071, "width_percent": 6.929}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1372137130 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1372137130\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1675373296 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1675373296\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1101018332 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101018332\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1921749754 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost/barcode/pos?warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388507289%7C16%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFuYTJ2UTZDRVhub3cwSGt1eGlUTnc9PSIsInZhbHVlIjoicmxzaHRhdWlDTXZxOERFZ1hRNUh0Z1pWNHZvY0VTWGNnWUNkTWFPUXF3WlF0bjVESDVIUlc0d1hqWUtzcFRzLzhxRWJBYXgxSnJEY1lWckhzeDgyRTFQYVFaVS9Rak92TU8rbjErajM1NXRQMkk1VG9FeE41TGltaEZ0aUFhcmZEdytwdWx4eFBxUjdmK2lPQVlHSGFFd0ROYzc3YnRKRjRWZTJLd0k4ZkF1NW1pYkRTdUJ4Qk8vem5lUUR6SStzdG0wbFNtMTc3Z0w4aEpxM1NVdi9qaDNnYUM1eEVQc1hjcnhlcFBwZEQ3bUx2QkwwU1M4c0oybjBvb25PbmlLR25TNThSU091UUJQb1haeW9wbFY2L2pTSmR1ZnFwcnBBRHA0Y28zMFpqN2RXOVlWS3N3c3VZOEVEdG1EM0xkaVFQcGVKSDFNRjBmNmdoUHBXZWxiM0NSY2VPZnVwaDNBRkN1MlY4d05HUzY1VTBLdzc1b25EcnU5NDBDcjBmV0RvMDF3MjNHdlk3cWI2dGtxZEJMNGwzQnoyK0ZsUnZaWml5anFWRkJ0ZlpRaWtvSUg3Yk5JMGM0cTNlb0dUMysydVVmZ2t2cTB6dkFEMVM4NnhTYW9zaFZJejVnQlp6VE9jaE5OVHozaVdPbzhOOWFtV0FXNmN4WnE3RDN5UlhxdjQiLCJtYWMiOiJjMjAyODY0NmI3NjgwZGZlM2JiODFhY2RkNWY4ZmExMTgyNmQzMzRlY2IwNmQ1YjNkMTg3MmE1M2Q0YjVjOTc2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZhc2tvYXVxK091ckNnaVFLdzlUdGc9PSIsInZhbHVlIjoiU21UQTdaVlBGeGZvcW9PMHdYMkIyTWxlOEY5SURyMEtyQVFjNmNsM3FJZHN4TUF0T3R1UHk5aDRIYTRzQkZzdzZpMS9nL0dLa2VGWTExSmlSQjNIbUxTUDE3dWRKazQ5Q29TbVJYNTBRdkJldVkzMEc5TTI1U1FUNzd1L3A4NXRvcU5KUVUvb05Lb2dWWWxFeEJBWUNSNWgyOCtaWTNUZGlIZkh5V2l4c0pybkZjWEprbjliUnIwUzFsTjY2ajRUbzJUVHRUS3RDckc0WVF5cTFzUHRpZUUvZENGU2JzMk10M2grVUF1TVZLYmpPVC8vTWt3NU1uZVFQOFFZeDhQTDNRcSthaFdERHp1ZTFTYkFvRCs0Qng5WVcyV05SMWorN3pGMXg5T3I3c3RhdlE4UTJtTjlYMWdWeVRZOUpCUUhJMEwvUXhGU1FMeEhOUUROM25RN0Y5aThOdjNpL1RpTEorOHpJNFBaRnI5K01VcjVqSUIzazFZdkQwVnNESi8zM1pLNmhmbk4rTnNlNjlsZUJEbXM1TU1xZVZ4T3lSQ3dtVGNKVWswTndST1NBdmwzbEFrZXBjN2Q0SHdhWGc2ekVDMXpOdDk2T2pXV1FXTGxBMlo3L0VIb3VCTHM3UlVPZ1czRUFYVGFpWHBITjBZbGJWUFRMQUtsMkV0MENSSGwiLCJtYWMiOiI4YWYzYWI0YzBkZDM1ODZjMTZlZWYzOGM4ZDhmMjgyZDUwZDg2ZjhhZDEyYjA2Y2FhMzZjZWQxM2NjNTczMzBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921749754\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-67641409 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67641409\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1152132148 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:15:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFhTjlKVitxcDJNMzZqRGhxWnlZT3c9PSIsInZhbHVlIjoiV0lzY216WUc0OGtQc3VCeFdHSStSazArQ0NJTzAzdjlhV1ZhUVc2aHBreHJ3ekg1dXNwZzhxakFZQWhRSjVHZnNrbWlWd1M1Wnd6MlEvRTQrMzllYlRiSEdOTnU3dFlRYll2TXFvVE9XOEFKZXJwRTlwdVVNRHVwTThXNkdyOGZlNlArS3FsQW04akl4bC9qSlR3cWVVZnZaVVZ2NWFMcUoxbm9NVFRWNEo1Tm5VQWJHcU90Zms3N0JzcU9ROFgxNlBRN3BzKy9GNTBpWFhyYjk4Rk5VZ1IvTGxFNkhGeVd6VHRHY0FoVVoxWGpHcmxUODQwK3A2Y1lSelhwN1lqM0w1RkM5dEpKd3c5MTR3elZzVUNaK1lkWkQzYTgxdGhLdm16SmVyOUtHOGVKZUxNc1YwdUo2SWJkY2pCb3lsMURKS3duOHpITjUzc2JGczl5M3l0MVV4S1RlMTU4a3M2aG1ZaWFLZ2R6aWF6MkFMdXhTbENyNGNsbGFkVHlya3dUeis0V2F6MG1kRVVrczBCaHlQdzhqSjJMMTdPN3RlcUtkMzN4Nm9HZFFYakh2VzBrb1E3TTgrclU3RmlacHd1UWJHUnBqbGJBOTBqVXdjbWJqeG5XMnBoeDd6eXBWS1BJUzZLNmxxclJwSlZiWFVpK0tCRDJtQWNLTkNCazRiblIiLCJtYWMiOiI0NDY0YzhhNjkwNWJlMzc0YWMzNWRlYTk4NzczNzEyZGJlZWM3YThkZmRjNjE2N2IyOGJmMDM2M2QzNzk3NmYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikl3akJOOWhaQ3V2VEdLU3FaLzErN2c9PSIsInZhbHVlIjoiaTdkQkhtK0pJUXRLUnNPMlZZZ2FCOGRCUGxKRmJ4a3I2N1EzQTlFcG12Y3p0aXQyT1RBc0UzTDlJSEJFQTFRRUltVG5FNmxVa0Q3SmRidkI2M25IQjBHRytJNXg2YTdlaEdYU25qOGJ6UkExNmtlTVdzYUliRFh1Si8vMm1XSEYva2NpaDlobHJRU0VXenFMOXFsaTJSREx1dFJ1K1p3VzVZSUVtaDlBWDJvRDNndFFJVEJMN1JlRVhkUW0xUjlFeWIzVnowVlRoNnU1RXdvbXJHZmJoYlhlSzNZYzVxUnV0Z2hQODF2L3hDQzlXWEYwaDRXZmoxVExyb0EyOVBCVlhSTy9UK3I2OTYvRTNBNzUzWXVpSG0yQVM0ZnFzZUNlYkFueVpzVkllVGJLdzdrVGxHTDl0NVdvSzVQd2lCREFVTkxZTWl6aGFuaHh2NnNLdDk3UDI3S3drSEN5TDZ3RXJtWjBTZzkzYWcrR1YvN1kyNjhrUnNObU1pdWl1MmxQVnVYUHAwQUM3Kys1eXV3MGN4SzhBc1VYWFB4RkNyZ24zcE1vQlFubzloRE84Skc5eEJoNGJnamxWK0F2MlBVZVN6MS80RVBwZkM1eVY3c0pVWSt2c1ZTWGluSjB4bGpScUYzUkRJNU81NER2UzN0UE04R1lhaERDeFFlZU9ZYS8iLCJtYWMiOiI4MTE1YmZmYThhNTk4MTI2ODc2ODNhZDE5MjcyMGVkNzc5YWI4OWUyZjcyNGVmMmZlZDhkOTZhZDNjNmE1ODgxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFhTjlKVitxcDJNMzZqRGhxWnlZT3c9PSIsInZhbHVlIjoiV0lzY216WUc0OGtQc3VCeFdHSStSazArQ0NJTzAzdjlhV1ZhUVc2aHBreHJ3ekg1dXNwZzhxakFZQWhRSjVHZnNrbWlWd1M1Wnd6MlEvRTQrMzllYlRiSEdOTnU3dFlRYll2TXFvVE9XOEFKZXJwRTlwdVVNRHVwTThXNkdyOGZlNlArS3FsQW04akl4bC9qSlR3cWVVZnZaVVZ2NWFMcUoxbm9NVFRWNEo1Tm5VQWJHcU90Zms3N0JzcU9ROFgxNlBRN3BzKy9GNTBpWFhyYjk4Rk5VZ1IvTGxFNkhGeVd6VHRHY0FoVVoxWGpHcmxUODQwK3A2Y1lSelhwN1lqM0w1RkM5dEpKd3c5MTR3elZzVUNaK1lkWkQzYTgxdGhLdm16SmVyOUtHOGVKZUxNc1YwdUo2SWJkY2pCb3lsMURKS3duOHpITjUzc2JGczl5M3l0MVV4S1RlMTU4a3M2aG1ZaWFLZ2R6aWF6MkFMdXhTbENyNGNsbGFkVHlya3dUeis0V2F6MG1kRVVrczBCaHlQdzhqSjJMMTdPN3RlcUtkMzN4Nm9HZFFYakh2VzBrb1E3TTgrclU3RmlacHd1UWJHUnBqbGJBOTBqVXdjbWJqeG5XMnBoeDd6eXBWS1BJUzZLNmxxclJwSlZiWFVpK0tCRDJtQWNLTkNCazRiblIiLCJtYWMiOiI0NDY0YzhhNjkwNWJlMzc0YWMzNWRlYTk4NzczNzEyZGJlZWM3YThkZmRjNjE2N2IyOGJmMDM2M2QzNzk3NmYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikl3akJOOWhaQ3V2VEdLU3FaLzErN2c9PSIsInZhbHVlIjoiaTdkQkhtK0pJUXRLUnNPMlZZZ2FCOGRCUGxKRmJ4a3I2N1EzQTlFcG12Y3p0aXQyT1RBc0UzTDlJSEJFQTFRRUltVG5FNmxVa0Q3SmRidkI2M25IQjBHRytJNXg2YTdlaEdYU25qOGJ6UkExNmtlTVdzYUliRFh1Si8vMm1XSEYva2NpaDlobHJRU0VXenFMOXFsaTJSREx1dFJ1K1p3VzVZSUVtaDlBWDJvRDNndFFJVEJMN1JlRVhkUW0xUjlFeWIzVnowVlRoNnU1RXdvbXJHZmJoYlhlSzNZYzVxUnV0Z2hQODF2L3hDQzlXWEYwaDRXZmoxVExyb0EyOVBCVlhSTy9UK3I2OTYvRTNBNzUzWXVpSG0yQVM0ZnFzZUNlYkFueVpzVkllVGJLdzdrVGxHTDl0NVdvSzVQd2lCREFVTkxZTWl6aGFuaHh2NnNLdDk3UDI3S3drSEN5TDZ3RXJtWjBTZzkzYWcrR1YvN1kyNjhrUnNObU1pdWl1MmxQVnVYUHAwQUM3Kys1eXV3MGN4SzhBc1VYWFB4RkNyZ24zcE1vQlFubzloRE84Skc5eEJoNGJnamxWK0F2MlBVZVN6MS80RVBwZkM1eVY3c0pVWSt2c1ZTWGluSjB4bGpScUYzUkRJNU81NER2UzN0UE04R1lhaERDeFFlZU9ZYS8iLCJtYWMiOiI4MTE1YmZmYThhNTk4MTI2ODc2ODNhZDE5MjcyMGVkNzc5YWI4OWUyZjcyNGVmMmZlZDhkOTZhZDNjNmE1ODgxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152132148\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1131847675 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131847675\", {\"maxDepth\":0})</script>\n"}}