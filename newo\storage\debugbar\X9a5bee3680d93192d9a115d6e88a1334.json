{"__meta": {"id": "X9a5bee3680d93192d9a115d6e88a1334", "datetime": "2025-06-08 14:14:51", "utime": **********.093861, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392089.616498, "end": **********.093902, "duration": 1.4774041175842285, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1749392089.616498, "relative_start": 0, "end": **********.78327, "relative_end": **********.78327, "duration": 1.1667718887329102, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.783289, "relative_start": 1.1667909622192383, "end": **********.093906, "relative_end": 3.814697265625e-06, "duration": 0.31061697006225586, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03656, "accumulated_duration_str": "36.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.903869, "duration": 0.02807, "duration_str": "28.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.778}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.961964, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.778, "width_percent": 3.009}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.023367, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 79.787, "width_percent": 4.923}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.030166, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.71, "width_percent": 2.899}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.050426, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 87.609, "width_percent": 8.917}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0642369, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.526, "width_percent": 3.474}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1600903883 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600903883\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.046682, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-509695030 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-509695030\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-127071785 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-127071785\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1077568681 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1077568681\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1979239794 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749391833855%7C52%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdYNU0vcjloVy85NkxKd0ZRTlM2YWc9PSIsInZhbHVlIjoiN0JvY3k1WFlGYmdtOFhDVEc3b2hVdHpiSDZGMTFhWWZsNUUxQjg3aVpWZGtRRE5CT0VPRi9SMzRuaUREdHVHZ0RzZFhEVWVXQVE5bDU4NXJpZUlFQmNQZXdSd3F1Wk1VaWE0RXRXTm5oUDl3bjhKVHE4YXZGeTFzYlRscC82VUFyZldXL0pnTlk2cVpJL0o4aGM1Mm1iMlNVTXNFdVZla2p4V2xTazhDR2FrQVJPYXFLSG9Vd1hPdjBKY2kvWUZOTzQ2b3ZMb3BPZnFGRVREMTN2bm0xd3ZiNDVIUUY5bmV3WHVwTDRISWQ3MkM5UFIrdjkydUV1NlRhd3RtakFoT3FyVzM1aG1pT2pDcFA1M1J2emYzbjcyYlV2NjFqVThGS202RFVwQmR5SklsREt6OGtGUXBLSzl1VzE4Z2dKWTJRNXdyaTk3N09IMitOcFVJSTNUNlZCRHk3NlZ3QURaSmNHK3pabUQ5Y2FxWXBZWCs0bGFvS3BKWVl5MkpqY1V4b0FpaFRidFZia256Mmp2OVNyWnN1VlR5bU56ajVSNWtFZFlQM3o2ZTNvb1lQNXYxdWh0TWErVWJtVi9XYmVWVVp0dllGU0dZQnB3ZElKMHgzamxhQkNMK1pVbHlPWnNJQTg2eS84dHdWYTFSQ3Y2VFl0dWZiVDh6UHpMczltR3IiLCJtYWMiOiJmMWE4ZTU4MzU3NTk2ZTIyZmYwMTE4MmJiNDY1NzA3NmZlOTFkNTQ0OTIzYWU4YjExY2FjNDA2ODY0NGMxMzUwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZyaTd1RjNuMXduZEIvZFRPMmdvblE9PSIsInZhbHVlIjoidTd0RzAwSDIzUEM4QW5iYk1KcEhnMnpjVEkzL1MxUjZVQWF3N3BhYWhHcjg5T2dBQzArTlk1Y21LT3U1d3h2eTROZ0JwOFVmeUNuaWpFVHdtWm1uWUNtN0ltTEhLdHg5L3pBalJOMkpZTll1MkpLcStab0J4NTBBMXNSSkcvL2tYUTBaaWwzUHUzODFjNWp0bGNBSHd0dWlwdHFyWnVQM0xCT2orYVBSR3c2a0dsUHBYcTdramhFYmlFNUF6Z1l4SThrRy9PMy82U0xubDgrVzFabjgzT3l2MnUrT3h2b0FvMzZxM0xsRGV2M3hzRW12dnVSVHhja2pMWjdTS3djcmliUU0vSUt3SVRWTTJjcjNIblRTTW11bkZFSzJBVC9TQkFpaXVUZSs5YzZZMzBpZXFOeENQWG1KODRaL094bWpxOXoxVzQrRDJnTzhSSWE5NGtRZ1krTk8rejZMK1pOZFlURzdRSlJSVnhJekZXcVdhU1dnYlVDSVhQKytNUXFZbXVRd1hHbTZrOEI3aURva08vQ1hacy9zejJQMU9Qb3I0WnVqVDlMYk1jTVppdkdYanMvdmVwcnRmQkk3QjdzZmxxa0dXaWNEc3J1SUliM1hvRWNyUHl5d1h2L3Z3S0NjQkdIeFdiRllRTzhpd3k5OGgvTWIyUzVXbEM4eHJCQ2YiLCJtYWMiOiIzMTJiMDc4YWVhOGYyMzk5MjAyZTAzNWJiOTlkNzMzMDA2NjMwMzdmY2JlM2EzNTIxZmUzYzc0Yzc4NWY3NzllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979239794\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1208316381 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208316381\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1196881591 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:14:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InAwVVBVbmIwN2dzT1BjQ0JRTEtkL2c9PSIsInZhbHVlIjoiczFwc0lyc09Sb3lTNWVEUlh4TlcwNy8zMlN1alV0ZVhSR0JoMFN3MHlWbGVtMTk3bElCV3BOaUIxeTVIaWxqVTU4c3ZqbTBid2YzdDlmdGtNUnBROS9pbDJrUEVmeHNVSGlMbm5hWmJFUi82TENCWHEvdzNRa0cxa0xLUGkyOG8vd0dZU2l4Snd1enp0MWtSUlpyQmFnY1YvWHVENTVpTjBLQkxQWk9GRWlJZWRkc0lFL1orZmNzb2VPRUFDOWRaY0pJcG1qKzFOeTllY3I5eFp0aHJSckpXaTRPb2pCRXgzVkErTGN6Y1BPaCswOWpYZm1VOW51M3ZGZ2VYNitLU2tNa29nRFlnK2JCRFRzUTMvaVpsWHphTXBlSjFzTXQzNWFnREZBMFJiUWlWbGFQR3VZeW5RejR6emRXNDh0aDhKNEhqV29KYW5lbVdkV2VuK29iY01zTSsrZDhDVzhWRzlGWG9rUmhpN25VeS80UHpRSStMbHdJUWNFTzJKYVh3N3MwL0c4OGttTFluOXBVUno3b2F3QzZwTmtTdWxpb3cweE9paEZQV2RHN21qemtOYVVuUkRtQXNBR2h5QUNzWHFnWi9OdVB2bSswSGpnK0NtM1I2c2JhWUxiR2FSa0RpTjRQcURtaVJIdkg2OHlzS2YvYnVHTEFzWUpqMTdiKzIiLCJtYWMiOiJmMTU1OTAyMWM4N2IyOTFlZWI0ZjJjYjc0ODhkZDUwMDgwNmNmOGZmNGIxOTE3OWQ1YjI1NjE1NDYxMWE5OTc3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRaWVdLRXhRd3JVc3RBNm9kM2VXQXc9PSIsInZhbHVlIjoiMDc0OTZpbFhsRmduZms5LzI1ZXlCWTdPbGVTb2QrSWdKUmowM2E0eUs1MWcxOEdtK1ZyVmV2Q3VNcUkyc052NnFjNnlxSUROZEVtQzdRTGJLaEFRWkFpREdyM3prNDRpT1RGZmh1a2hSbXZ5aUlDSjZXQmVyMlQyY21sL0kxblpRSVZ6YklGemhpTGVad2FCWXk0OEE1citidTVLcXFuQXpnMmgvVHM1T2dHZXBOazI1bU1NQ1ZJNmV5d2kyRk1xZ0dSNGdmdjdZaXIzUmlTc0VuZkFscFN6YkNSbEFOZ3lBeGl5MlErTW9iRnQ2UHJncTFvbmUwWGJxb3dNNzBJckNUZmtoakJMVGp4dEZCbDVvMXFKVHp6Nm9pK2hVL0lTODE0MWJuQWV2SkdhNHJXYjl1bnd1QjU1K0h1VTVnRkNuTCswNVFXYlRNOVZxZ0NMTk5zSXNpMlBsVWdCVXhuYklGZ2srTVkwWENRczNBRG1sQ1lVbnRWNSsvdHh2eTFiZjgvamJ6aWpGUnFQTjc3M2E3UzZSMkNzL1ZXclZWcXdNVHNMcmFLVHJPYkYzOHFreDJqN2sydDUyZS9GRW5ZQkx0M1NKZmVzZWpLUk9VenRGSGFNOWFBa0NlVDVhVDI1UG9HMjNBSlBoRjBjQjlqZHNjaHZjajVkaCtsOW5FQ0wiLCJtYWMiOiJmZjRlZjdlYTkwMDgwYjFkZGY2MWRmMzZhODIxZjk2MWY0ZGJiOTRlYWIzOTBiZjBmMTNiZGY4MTBmOWJlZjhlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InAwVVBVbmIwN2dzT1BjQ0JRTEtkL2c9PSIsInZhbHVlIjoiczFwc0lyc09Sb3lTNWVEUlh4TlcwNy8zMlN1alV0ZVhSR0JoMFN3MHlWbGVtMTk3bElCV3BOaUIxeTVIaWxqVTU4c3ZqbTBid2YzdDlmdGtNUnBROS9pbDJrUEVmeHNVSGlMbm5hWmJFUi82TENCWHEvdzNRa0cxa0xLUGkyOG8vd0dZU2l4Snd1enp0MWtSUlpyQmFnY1YvWHVENTVpTjBLQkxQWk9GRWlJZWRkc0lFL1orZmNzb2VPRUFDOWRaY0pJcG1qKzFOeTllY3I5eFp0aHJSckpXaTRPb2pCRXgzVkErTGN6Y1BPaCswOWpYZm1VOW51M3ZGZ2VYNitLU2tNa29nRFlnK2JCRFRzUTMvaVpsWHphTXBlSjFzTXQzNWFnREZBMFJiUWlWbGFQR3VZeW5RejR6emRXNDh0aDhKNEhqV29KYW5lbVdkV2VuK29iY01zTSsrZDhDVzhWRzlGWG9rUmhpN25VeS80UHpRSStMbHdJUWNFTzJKYVh3N3MwL0c4OGttTFluOXBVUno3b2F3QzZwTmtTdWxpb3cweE9paEZQV2RHN21qemtOYVVuUkRtQXNBR2h5QUNzWHFnWi9OdVB2bSswSGpnK0NtM1I2c2JhWUxiR2FSa0RpTjRQcURtaVJIdkg2OHlzS2YvYnVHTEFzWUpqMTdiKzIiLCJtYWMiOiJmMTU1OTAyMWM4N2IyOTFlZWI0ZjJjYjc0ODhkZDUwMDgwNmNmOGZmNGIxOTE3OWQ1YjI1NjE1NDYxMWE5OTc3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRaWVdLRXhRd3JVc3RBNm9kM2VXQXc9PSIsInZhbHVlIjoiMDc0OTZpbFhsRmduZms5LzI1ZXlCWTdPbGVTb2QrSWdKUmowM2E0eUs1MWcxOEdtK1ZyVmV2Q3VNcUkyc052NnFjNnlxSUROZEVtQzdRTGJLaEFRWkFpREdyM3prNDRpT1RGZmh1a2hSbXZ5aUlDSjZXQmVyMlQyY21sL0kxblpRSVZ6YklGemhpTGVad2FCWXk0OEE1citidTVLcXFuQXpnMmgvVHM1T2dHZXBOazI1bU1NQ1ZJNmV5d2kyRk1xZ0dSNGdmdjdZaXIzUmlTc0VuZkFscFN6YkNSbEFOZ3lBeGl5MlErTW9iRnQ2UHJncTFvbmUwWGJxb3dNNzBJckNUZmtoakJMVGp4dEZCbDVvMXFKVHp6Nm9pK2hVL0lTODE0MWJuQWV2SkdhNHJXYjl1bnd1QjU1K0h1VTVnRkNuTCswNVFXYlRNOVZxZ0NMTk5zSXNpMlBsVWdCVXhuYklGZ2srTVkwWENRczNBRG1sQ1lVbnRWNSsvdHh2eTFiZjgvamJ6aWpGUnFQTjc3M2E3UzZSMkNzL1ZXclZWcXdNVHNMcmFLVHJPYkYzOHFreDJqN2sydDUyZS9GRW5ZQkx0M1NKZmVzZWpLUk9VenRGSGFNOWFBa0NlVDVhVDI1UG9HMjNBSlBoRjBjQjlqZHNjaHZjajVkaCtsOW5FQ0wiLCJtYWMiOiJmZjRlZjdlYTkwMDgwYjFkZGY2MWRmMzZhODIxZjk2MWY0ZGJiOTRlYWIzOTBiZjBmMTNiZGY4MTBmOWJlZjhlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196881591\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1471982994 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471982994\", {\"maxDepth\":0})</script>\n"}}