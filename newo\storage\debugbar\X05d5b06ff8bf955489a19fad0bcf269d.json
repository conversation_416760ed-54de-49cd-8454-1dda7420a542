{"__meta": {"id": "X05d5b06ff8bf955489a19fad0bcf269d", "datetime": "2025-06-08 15:09:37", "utime": **********.567085, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749395376.665801, "end": **********.567116, "duration": 0.9013149738311768, "duration_str": "901ms", "measures": [{"label": "Booting", "start": 1749395376.665801, "relative_start": 0, "end": **********.464326, "relative_end": **********.464326, "duration": 0.7985248565673828, "duration_str": "799ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.464341, "relative_start": 0.7985398769378662, "end": **********.567119, "relative_end": 2.86102294921875e-06, "duration": 0.10277795791625977, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00434, "accumulated_duration_str": "4.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.526435, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.581}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.548181, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.581, "width_percent": 27.419}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1702877006 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1702877006\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-517391114 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-517391114\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-995187963 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995187963\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-30031457 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNGM2NzMlZGQlhPcVZWWERWN1NYYmc9PSIsInZhbHVlIjoic3JqbUI0MGJjWVdsY3QwMjM3T1dpTnRZTWV2RCtyZExsaDNWeTBRSjJYeG9saXowdEcrNXFmalQxZXBIUnVLVDB2MnE1ZWcvVm1HdHROd1ZHM0w4di8ya1pQRGNKZnVub0RtSWM3OFV2VnI5bFZUbDk0ZWFQalhKZGFVOThJM09jZndaK2UrZHdxYkZXMFkwK0RZWktSdTBJbVM5T1NjVUQ4dGFPcHRiV1U5K0hqbm82ZEd0Y013RVRHWW9oZVVQMGJUQ2Y2aXQra1dTMzlqZ0M0VFpmRGFaa0xJUVMvRzhQb2M2a2RORElkOThQbG9mQk9lRldQb1FJeDBFNHhoZlJ0L2VNUEU0VjdsQVh5MWZRcE1UcHk3WTU2bkxrZW5lcHhyNEdwdit4S1FRcDJvdEdsWXljS1F6R2xtRThldmtqMm9kSUIxTjJPbFByUVZaR3FlRjIrbXRGY3NZcVYxSzhxR1FzdGlGTFh5UHlMMGpYYjRjTHZ0NVZ2eXpKNm1Eb1NoVlRkUGNWZ3RxM203bVRDeXowRjZOZ1cwNWZscnBOUlJBVldNenZ2ZzVxdWlZK3JFY1Zuc2NldTd5Y0pIcXI4SHRXVXhDRm0rYlRlQisrRks0Ni9vWk5uanpNVEpCaDY2anFQcjBQaFluM3RyclpxcDY2WnVXZGJ5QkN1MkIiLCJtYWMiOiI4YTJlZWRiNTdlNWZiOTA2ZDNlZDQ1OTA4YzU3ZGZlY2U1YzQwMzkzODUwYzFiYmViYmU1YmFmYWUwMWExZDdlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imd2WFZkNGJEK0hscjhocENuZGc0M2c9PSIsInZhbHVlIjoib1hSUVQ4UFQ4U2Q5UTBTRTFlbGJlWVZzemxtL1NQanUyaHlmVWE2czUrSmlVcUVqb3ArQXg5Sno0VDNUbHVseVR5TmFkanZGVHg1TVQvM3FNNEFTTEZzQmNMT0daaU01Uy9rb25XdUw2dmoxbmNxQkxpb1RwRUhTZkVZUnAyeUFIaUdOampoQXNDRWZWbmNTRS9XYUZIS3RwQmNERGw1azBnd2RSY1A2QWJaME43bU93NkZVaGxjdGZzaTRQNWZvYmNzalVIdlZiRkNjc1VhUTJJU1hNWDBRakQ3Mkl3QU9ZOEpCVG1CTmpKbHpkOXcvQWdaUG0xcU51SlFOVkh4YnZjRnhBL1gxYjZZaUhJUERVTzgybHdmWGk3Z1F1cFI5Sk9UcWlHQVZSbWtOMjU1czV6c2V5UGlFTERDTlAxYVNwWCtjNkw4ZnZJTVMzZzBQMTVDVnFXN3NhWlA2MHYyNWVLWWY4QlV3VGJHbnZndU40bWd2dXd4bjYrSjJxTzhHMWJZMXhCS1NyQ3pUR0ZYeVBsTURKdlYvVWVNeElrZURabEx2RzB5eTE2NlFVT1p5YkIySzV5U1g3ODN0MmhiUXgzNXBFY0JJenpQTjdlY0FmU29kZDk4aVo3dmdBeThsdWI5SGF0T2lGaVh2a1ZLNkdZMTJTOEVoU0Q1Y1hxYzMiLCJtYWMiOiI1Nzg3NDhmOTM2ZWI4YTUwNTcxZWIxNWFmMDhmYzkzNmJmZDFjMjY4YWExZGRkMDMyNDc1YWFlMzI2ZmVjYjc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30031457\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1003784252 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003784252\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1063047173 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZrU1VGZXkralhBenJuVm9KeUtDVlE9PSIsInZhbHVlIjoiV2NlNjd4R2RJeVVHUlM3ckYyeVBXMllJQW9EMTdpNEN1YmRUN3JqSVNCbE5FM284ckFjbnpqMDVDVUQ4RnhtcHpwS3ZZeTJjMGxlWHhmZ0EzMlhsaHRmclI5cFMvVGVvc0dmVUk5ODZDOCtEZzFuL0owZmtTbGpZZVc1RnhjUEFnZTNleTFwRVlyODFNRHdCbmpMNUE1WklQSEY4cUdKMlZTUE9lMDhxeWpxVlBVRGZSOSttV0xsVGF0OEUra1FNaEsvMk9XUVlmQmZRVjB3VVYwNVFlcjRPTDh2Njgva1NNTzYvcllTLy82Ti9ENjhHZFY3ck1rQjdmUWhJejA0NDRaN0dMY1V0RFVwV3ZQWnE0ajJvYlUxNHk1ek8vYkw3bUUyUWNSODNIQ3RGcGppOUxyZkNCZ0V1b0RINlpEU25kcWFpVGlMUWxtQ2JIelRod0tvUkdiUjl1b3JWOFJWdDhiU3phNUhwdi9Td05sRjIxYWsza015NmJRY1FwSVRlRzlWdHcrSnFKK1M0UlJ3VWREaEFXU293cm1CdDdxSnN5TldDM1N4OTE5S3J0SHFnVXg1dWZIQTNUck0rc3ZUUkdramdaaC8vVUp6ZjExSEcvZ2wwYWk0bVFINzZKcWpXSXVRU3JxdTlKYlhObmlSQUE2NzRvamNZVUNmV200cGIiLCJtYWMiOiJmZGQ4OWJhYjcxNGQzNTc4ZDhiNGFlYTE1MzU4MTQ4OGJmNjkwODUxNmE2ZWI4OGZhN2E3ZGFkM2ZkMzk1ZjVkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklmdTMyRmc2a3hBZmF3VEd1N3loRmc9PSIsInZhbHVlIjoiTnZPd1Jla01MYURydS83Qm9pMmZWcG54K3hpYVdXTXF1OTU1UUYxSUo4UTRLVTZVMjlsenQ3SFZoZXViTzJRNzJjN0VBNHVnKzlTMEtDck1NZ0l1aENVd3hYaWlHaFVZTjRhcE9qUUlFUWZ5ZlJFYXlvMExaZUphMUVoZHVlb1VuR0lQblZtRzNRcWpOalZWWmFHNFkwUWtHVWtvM1hLMUhzVnc5dVl1d3FSa3ZHT1BOMXdMMGxRSUZ4dDZtT1Zad2hhZ2JvN3VSM1dJV0orNnFBeXF6MzJZbEZoRnYvcjF2RVVpQW44UEtrdmx3RVdzWlRLSWlvTkJoVndlZlpGRTRjM1NWVjQvR081NlZKeGUwODFRUTVjWTNLQXJwbzlWc3dpODRQTENTNldnRjRUQkc4TlRJOWp4MFBPMExTVjFxQ0NoazVIWEw3cmcvNmZaV0NvYkdTdGZxYlpjcEh2NkQvSXVxeGhWYUY5K0hPVThlRXdMaGdNTmVoNHg1VStraktXNThJVGJOY290WW9EUHlBbzBQSCtrYjhNQ0t3OUtCYVdkekJSSEJYa2s5TCtYSmpZaEU5MFh2elF6akhBNXVVMFpqWmJWQ1hUbWhzdG9QNWkxa0lSRSthakRTVVlCNllYdG50Q05vcGN4QU1sajg5U3A4TlBYR3RrbVIrRDAiLCJtYWMiOiI1M2UyMzE3NzM0ZmM1ODc4ZjU4ZTQ4ZWQyNDk3YzJiMjUwN2UyYzc1ZDVkNTJjOTg2ZmIwNmNmYjkzYThlNWU4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZrU1VGZXkralhBenJuVm9KeUtDVlE9PSIsInZhbHVlIjoiV2NlNjd4R2RJeVVHUlM3ckYyeVBXMllJQW9EMTdpNEN1YmRUN3JqSVNCbE5FM284ckFjbnpqMDVDVUQ4RnhtcHpwS3ZZeTJjMGxlWHhmZ0EzMlhsaHRmclI5cFMvVGVvc0dmVUk5ODZDOCtEZzFuL0owZmtTbGpZZVc1RnhjUEFnZTNleTFwRVlyODFNRHdCbmpMNUE1WklQSEY4cUdKMlZTUE9lMDhxeWpxVlBVRGZSOSttV0xsVGF0OEUra1FNaEsvMk9XUVlmQmZRVjB3VVYwNVFlcjRPTDh2Njgva1NNTzYvcllTLy82Ti9ENjhHZFY3ck1rQjdmUWhJejA0NDRaN0dMY1V0RFVwV3ZQWnE0ajJvYlUxNHk1ek8vYkw3bUUyUWNSODNIQ3RGcGppOUxyZkNCZ0V1b0RINlpEU25kcWFpVGlMUWxtQ2JIelRod0tvUkdiUjl1b3JWOFJWdDhiU3phNUhwdi9Td05sRjIxYWsza015NmJRY1FwSVRlRzlWdHcrSnFKK1M0UlJ3VWREaEFXU293cm1CdDdxSnN5TldDM1N4OTE5S3J0SHFnVXg1dWZIQTNUck0rc3ZUUkdramdaaC8vVUp6ZjExSEcvZ2wwYWk0bVFINzZKcWpXSXVRU3JxdTlKYlhObmlSQUE2NzRvamNZVUNmV200cGIiLCJtYWMiOiJmZGQ4OWJhYjcxNGQzNTc4ZDhiNGFlYTE1MzU4MTQ4OGJmNjkwODUxNmE2ZWI4OGZhN2E3ZGFkM2ZkMzk1ZjVkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklmdTMyRmc2a3hBZmF3VEd1N3loRmc9PSIsInZhbHVlIjoiTnZPd1Jla01MYURydS83Qm9pMmZWcG54K3hpYVdXTXF1OTU1UUYxSUo4UTRLVTZVMjlsenQ3SFZoZXViTzJRNzJjN0VBNHVnKzlTMEtDck1NZ0l1aENVd3hYaWlHaFVZTjRhcE9qUUlFUWZ5ZlJFYXlvMExaZUphMUVoZHVlb1VuR0lQblZtRzNRcWpOalZWWmFHNFkwUWtHVWtvM1hLMUhzVnc5dVl1d3FSa3ZHT1BOMXdMMGxRSUZ4dDZtT1Zad2hhZ2JvN3VSM1dJV0orNnFBeXF6MzJZbEZoRnYvcjF2RVVpQW44UEtrdmx3RVdzWlRLSWlvTkJoVndlZlpGRTRjM1NWVjQvR081NlZKeGUwODFRUTVjWTNLQXJwbzlWc3dpODRQTENTNldnRjRUQkc4TlRJOWp4MFBPMExTVjFxQ0NoazVIWEw3cmcvNmZaV0NvYkdTdGZxYlpjcEh2NkQvSXVxeGhWYUY5K0hPVThlRXdMaGdNTmVoNHg1VStraktXNThJVGJOY290WW9EUHlBbzBQSCtrYjhNQ0t3OUtCYVdkekJSSEJYa2s5TCtYSmpZaEU5MFh2elF6akhBNXVVMFpqWmJWQ1hUbWhzdG9QNWkxa0lSRSthakRTVVlCNllYdG50Q05vcGN4QU1sajg5U3A4TlBYR3RrbVIrRDAiLCJtYWMiOiI1M2UyMzE3NzM0ZmM1ODc4ZjU4ZTQ4ZWQyNDk3YzJiMjUwN2UyYzc1ZDVkNTJjOTg2ZmIwNmNmYjkzYThlNWU4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063047173\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-584889173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584889173\", {\"maxDepth\":0})</script>\n"}}