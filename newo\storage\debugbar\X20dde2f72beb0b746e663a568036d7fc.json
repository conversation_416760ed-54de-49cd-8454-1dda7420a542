{"__meta": {"id": "X20dde2f72beb0b746e663a568036d7fc", "datetime": "2025-06-08 13:44:43", "utime": **********.485113, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390282.196365, "end": **********.485144, "duration": 1.2887787818908691, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749390282.196365, "relative_start": 0, "end": **********.339767, "relative_end": **********.339767, "duration": 1.143401861190796, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339789, "relative_start": 1.1434237957000732, "end": **********.485148, "relative_end": 4.0531158447265625e-06, "duration": 0.14535903930664062, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45602288, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00907, "accumulated_duration_str": "9.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.41253, "duration": 0.00688, "duration_str": "6.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.854}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.442948, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.854, "width_percent": 13.23}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.459114, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.085, "width_percent": 10.915}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 2\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 20.0\n    \"originalquantity\" => 20\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 40\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-22073873 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-22073873\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1440769722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1440769722\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1328827470 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328827470\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1020108084 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390278581%7C35%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhCV3hoeEYzbGlCVzM4UHVLa3RJWnc9PSIsInZhbHVlIjoidkF2RTlidFFpdHBIOWo1RTRUU094WW5qWDB4QnNGem1XRTFpaEVVT0NMaWhDaS83cHlFc25reUZWaWhzdVhZelpCN0gzL0FsUnNJWmRyM3MrUlFYYlREcTUyMzZ1aHBLTVViMFFVVWROSzNLQmVQYlNuRVBvQUswU0Q5RXc3cFB6RkUweWtBdm1GNVFRT0haMG40N1JDR2phZENGVnlwbitUMXFZNmFYd2lrc09BbXNmNCtqa2N1VnRRMjBBYkdzZGllYlg4NDFvMWFBQ0xYZ2NQQnYzaGpWWktKOUxQd2Ivelo1NUpTS01XNW9pOE5tb2ErU3piWFA4R2QrVHM1dnh3aDgrKzdJcTJnR0FqM2kxRkU2QzJibk12VllqMllIR1F1LzlaYUlDUkZMcStxUUN1bGJsZE5Vbm9EUzNNZmc5UE5XRTljbFN0VGdiTkNmdThwU3RUV2pHcWoyWVUrMlRrcVArNGxJL0xieklUWEtNcm1TWXRLSlRlQXNlZmFSZlhKZXBRU0tiSWtGQU9iYThweEtXVWM2RzJCZ293RExzV01wMzhJeU5ET0VnOWZaTWhmTHU3R0FweDVTaEI2MXcxU0NEc3orb3dXVGlUeFJncmw2c2plSFhiaW5RZFBiTW5qKzV3UUNhU2hzVGV2UEZ0SklGUytYdG91M2dmZ04iLCJtYWMiOiI1MzcyNThlNmE4MjVmODJlM2FmMDIzMzhlY2Q2ZTQzNTllODI2OGZhZDIxY2RiNDQ2ZWY3MzdmYzIxYzk1ZTljIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikx3ZjZ4cGZrbVVJc2xtU0dxb0RUY2c9PSIsInZhbHVlIjoiNXhhWmFnSjVFNHZWeHdDUmwrdGxwSEJIYVJOVnJ4QkUwUksvOFZkdGNOR3lTQ01oZ09adDB3bm5aOEhnYkZNbjFHYmRscjZEd3AyYjRVWk16MHN3VTBwWGw0aFQwTFhESnJvU1Z3TGRBNXV6dUd6SlkyclJyV2tSUEV4NkpRdURQNC8wRHpLQVhUOGx6dFlpWmlld05CeC9NQ2c4c0ZEY2RKbFRidnAwejFwL1k1c0p5SkhaS2ZZQ2pmU1dyKzlraEw5aDFJY3BBSXRvQ0NlYUJEVG56QjdCYzQ0SnNvUXN2cDhlVWdLRzhOYVBZNmhCYjNRbm8wTHVtOXRDZWhqWVBMTGdkOXgyRkRUVWRLWkRvbWRQTmgyVDNoUlhVZjdaRkx1ZWx5UFZvaEM4V1BDLzJ5L2Nud01pK1pjR2NMNWt3WHp4UHlyTlhFRzRUYXhHTjdZck5WK1ZOczZyY2EyaHJKNUVkekc5NUZJUzNJeWxFTElTcWQxdzJudmxDYzg3bHlPbFBJcy9oWWozVHVObWJidS9EcDNZWHRzVEUxV0Z2U0JMZE0vYmtsaUtVUWp0TkFJQXNaczVCUW8wTWdrZEVSSEM3Wk8yY2Zxa3d5VnVpa3h0V3hTT2IzZG5Wc1htTmZISjBmWjFVQnNma3kyRG1uUEVLMk9UcEkrd2Yra2EiLCJtYWMiOiI5OGJlMjBhOGQwMTU0MDY0YTM1NTBjZWFjZDg3NjRmZTk0YmJkNWQwYjU5N2E2OWIzODQwZWQwZGU4YmI1ZWMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020108084\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2138750136 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138750136\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2002849272 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJYUG9qb3B5cVoweWEva0F5QVd5WWc9PSIsInZhbHVlIjoiYWJOcm9QR1kyYXlrRE8yNU0vSnFPMGtGMTJMMC9GeTBkUzNoMjhkNDM3eGhLN1ZQTEdseGdBOTRwa0FBU3VmWHkvb1drWmFDT3prcFloNERSUGp1OXE0azJmWGpycG1wRG1YOGNFMjNzR25wTXF1SGxhRWw4M1RQdTlaZVlsMmpKbXkrbkV3RGVLSUdVU01rbnNoMUZtTU1nRGdjK1VmZ3FGblFRZ0lRbHpBWUl6TVpnM3BKaHQyMlNxUHNGa0FBU2pDcFkvSlBWYUFaMW50eml6U200aEw1K1RabHpVUWR0bTAwRm1qNzducERiWEtZbXQ2NXRpbi9Bay9lalZvZnR6dkZ2aDJacHRtdVJ6b242NzF4NHdYR3Jsb09xaUZhSG1JUGRwYVJuR0dTVUVOUmxxRHhaSGpkT3FvanJOOTZGWncwZmNTNmhsREFTcGJCZWpLU0tjMjZvRmRJUTVRMzRkT3RKNWpQWkpjMmwvSTR3Y0ZQdTg1Uk1LNVFNYWdUbGd2YkJQUzN2UTlYeXUyTlMzTVQrMUdrVVgveUtVSzRsVFlrZktNWGU1MDRVZm1MMmVrWU9yN1RkdDd4d0V6Tk5qM1JmM2NQTjdKb2JQRzJFWjVNa3Z5dkVwcWRiVVUvajBUdTQ4QSticXFUSVJBZU5ocEZKUXhDMit4dzdLd04iLCJtYWMiOiI2MjA3ZTczMTM1MzU2MjcxYTYzNzkwYjViODVhZmJhYWVlMTljMjAxZDUwZTA2ZDZhNDdiNzZjMjY0NWEzM2QxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBEOEtMU1J0VDNzZkJ6YkIwdHkrWXc9PSIsInZhbHVlIjoiU1VEK2RRcitCZjdjMEV0MWJvaGwxZlg3SG5JNG40SStxSWNtWGNNd1hCeVNOWUJIOFlYUWIwNkNZelVyZ3R0VkdHbktwMnlyNkIwZUc2NWRKRDlXTjBCSjhTbkZhZG82Q094TWwvWCt4N3E0WTZHZFhhZDJZdmJpSndydzd5UzZSeXM2VlFVZ2MwSStzSkU3SHdDV1A3bE5YY0dNWm1OSGt0RC9Ed0pERHdnM0VLeGRheXExMjMxeWJHZW4xVzRWSDZmNWFHZHFvYjRYcUNFeHRtbE82eDA3NXhockhHaHBaWVFVNkxBSDBtQUx3MFMyOFdZSEdtSTRIZC9wcnpNZlFwd1lYb3ZFSDJLU1hsSndJU2hKWU1LTTFvV251ajZiZW5tRTNkamU1RjN3bys0VmsxbHRZSUFkTVpMN0JyYkJLUk5iSWE5YlMzejE0QWUrOVl6bThXOG1uVkVMZ0pQTVZrRXlYcmdYSVdod0pJQVBldS95NjBoa1FNTkhJWXkvdHdWOTZYZUorY05LYVdseFNPakxyQjUxMTl4MHV0dWRrRG94TGxHRlMyWHAwdVlaMXRkSTJ5Y2xrazlqd3hWd2EzOW1ZYjZCOHlzTnBCNTU1aTZFMVJlNDlJZ1ZrdzFsUC83VE9JSWtoTmZKNE54SmQxUzMxK2Zaa0FLdThaTW8iLCJtYWMiOiIwMzY4OTJjNjEwNjNkMjUzNWU0MmZkMDg3NWZhYjMwYjUzNDBhNzhhMjIxMGFhZjNhYWU5NzA2ZGYwYmVhYWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJYUG9qb3B5cVoweWEva0F5QVd5WWc9PSIsInZhbHVlIjoiYWJOcm9QR1kyYXlrRE8yNU0vSnFPMGtGMTJMMC9GeTBkUzNoMjhkNDM3eGhLN1ZQTEdseGdBOTRwa0FBU3VmWHkvb1drWmFDT3prcFloNERSUGp1OXE0azJmWGpycG1wRG1YOGNFMjNzR25wTXF1SGxhRWw4M1RQdTlaZVlsMmpKbXkrbkV3RGVLSUdVU01rbnNoMUZtTU1nRGdjK1VmZ3FGblFRZ0lRbHpBWUl6TVpnM3BKaHQyMlNxUHNGa0FBU2pDcFkvSlBWYUFaMW50eml6U200aEw1K1RabHpVUWR0bTAwRm1qNzducERiWEtZbXQ2NXRpbi9Bay9lalZvZnR6dkZ2aDJacHRtdVJ6b242NzF4NHdYR3Jsb09xaUZhSG1JUGRwYVJuR0dTVUVOUmxxRHhaSGpkT3FvanJOOTZGWncwZmNTNmhsREFTcGJCZWpLU0tjMjZvRmRJUTVRMzRkT3RKNWpQWkpjMmwvSTR3Y0ZQdTg1Uk1LNVFNYWdUbGd2YkJQUzN2UTlYeXUyTlMzTVQrMUdrVVgveUtVSzRsVFlrZktNWGU1MDRVZm1MMmVrWU9yN1RkdDd4d0V6Tk5qM1JmM2NQTjdKb2JQRzJFWjVNa3Z5dkVwcWRiVVUvajBUdTQ4QSticXFUSVJBZU5ocEZKUXhDMit4dzdLd04iLCJtYWMiOiI2MjA3ZTczMTM1MzU2MjcxYTYzNzkwYjViODVhZmJhYWVlMTljMjAxZDUwZTA2ZDZhNDdiNzZjMjY0NWEzM2QxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBEOEtMU1J0VDNzZkJ6YkIwdHkrWXc9PSIsInZhbHVlIjoiU1VEK2RRcitCZjdjMEV0MWJvaGwxZlg3SG5JNG40SStxSWNtWGNNd1hCeVNOWUJIOFlYUWIwNkNZelVyZ3R0VkdHbktwMnlyNkIwZUc2NWRKRDlXTjBCSjhTbkZhZG82Q094TWwvWCt4N3E0WTZHZFhhZDJZdmJpSndydzd5UzZSeXM2VlFVZ2MwSStzSkU3SHdDV1A3bE5YY0dNWm1OSGt0RC9Ed0pERHdnM0VLeGRheXExMjMxeWJHZW4xVzRWSDZmNWFHZHFvYjRYcUNFeHRtbE82eDA3NXhockhHaHBaWVFVNkxBSDBtQUx3MFMyOFdZSEdtSTRIZC9wcnpNZlFwd1lYb3ZFSDJLU1hsSndJU2hKWU1LTTFvV251ajZiZW5tRTNkamU1RjN3bys0VmsxbHRZSUFkTVpMN0JyYkJLUk5iSWE5YlMzejE0QWUrOVl6bThXOG1uVkVMZ0pQTVZrRXlYcmdYSVdod0pJQVBldS95NjBoa1FNTkhJWXkvdHdWOTZYZUorY05LYVdseFNPakxyQjUxMTl4MHV0dWRrRG94TGxHRlMyWHAwdVlaMXRkSTJ5Y2xrazlqd3hWd2EzOW1ZYjZCOHlzTnBCNTU1aTZFMVJlNDlJZ1ZrdzFsUC83VE9JSWtoTmZKNE54SmQxUzMxK2Zaa0FLdThaTW8iLCJtYWMiOiIwMzY4OTJjNjEwNjNkMjUzNWU0MmZkMDg3NWZhYjMwYjUzNDBhNzhhMjIxMGFhZjNhYWU5NzA2ZGYwYmVhYWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002849272\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2005913077 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>20.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>20</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>40</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005913077\", {\"maxDepth\":0})</script>\n"}}