/**
 * Debug نهائي لنظام التوصيل
 * انسخ هذا الكود في Console المتصفح لاختبار النظام
 */

console.log('=== Debug نهائي لنظام التوصيل ===');

// 1. فحص شامل للنظام
function fullSystemCheck() {
    console.log('\n🔍 فحص شامل للنظام:');
    
    // فحص الزر
    const paymentBtn = document.getElementById('payment');
    console.log('الزر:', paymentBtn ? '✅ موجود' : '❌ غير موجود');
    
    if (paymentBtn) {
        console.log('خصائص الزر:', {
            text: paymentBtn.textContent.trim(),
            classes: paymentBtn.className,
            'data-delivery-url': paymentBtn.getAttribute('data-delivery-url'),
            'data-ajax-popup': paymentBtn.getAttribute('data-ajax-popup')
        });
    }
    
    // فحص البيانات المطلوبة
    const customerId = document.getElementById('customer')?.value;
    const warehouseId = document.getElementById('warehouse_name_hidden')?.value;
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    console.log('البيانات المطلوبة:');
    console.log('- العميل:', customerId ? '✅ ' + customerId : '❌ غير محدد');
    console.log('- المستودع:', warehouseId ? '✅ ' + warehouseId : '❌ غير محدد');
    console.log('- CSRF Token:', csrfToken ? '✅ موجود' : '❌ غير موجود');
    
    // فحص السلة
    const cartItems = document.querySelectorAll('#tbody tr:not(.no-found)');
    console.log('- السلة:', cartItems.length > 0 ? '✅ ' + cartItems.length + ' منتج' : '❌ فارغة');
    
    return {
        button: !!paymentBtn,
        customer: !!customerId,
        warehouse: !!warehouseId,
        csrf: !!csrfToken,
        cart: cartItems.length > 0
    };
}

// 2. محاكاة طلب التوصيل
function simulateDeliveryRequest() {
    console.log('\n🚀 محاكاة طلب التوصيل:');
    
    const check = fullSystemCheck();
    
    if (!check.button || !check.customer || !check.warehouse || !check.csrf || !check.cart) {
        console.log('❌ لا يمكن إرسال الطلب - بيانات مفقودة');
        return false;
    }
    
    const requestData = {
        customer_id: document.getElementById('customer').value,
        warehouse_name: document.getElementById('warehouse_name_hidden').value,
        user_id: document.getElementById('delivery_user_hidden')?.value || '',
        discount: document.getElementById('discount_hidden')?.value || 0,
        quotation_id: document.getElementById('quotation_id')?.value || 0,
    };
    
    console.log('بيانات الطلب:', requestData);
    console.log('URL:', window.location.origin + '/pos/store/delivery');
    
    // إرسال طلب فعلي (اختياري)
    if (confirm('هل تريد إرسال طلب فعلي؟')) {
        fetch('/pos/store/delivery', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('✅ استجابة الخادم:', data);
            if (data.code === 200) {
                console.log('🎉 تم حفظ الطلب بنجاح!');
                console.log('رقم الفاتورة:', data.pos_number);
                console.log('رابط الطباعة:', data.thermal_print_url);
            } else {
                console.log('❌ خطأ:', data.error);
            }
        })
        .catch(error => {
            console.log('❌ خطأ في الشبكة:', error);
        });
    }
    
    return true;
}

// 3. اختبار معالج الأحداث
function testEventHandler() {
    console.log('\n🎯 اختبار معالج الأحداث:');
    
    const paymentBtn = document.getElementById('payment');
    if (!paymentBtn) {
        console.log('❌ الزر غير موجود');
        return;
    }
    
    // إضافة مراقب مؤقت
    const tempHandler = function(e) {
        console.log('🔔 تم النقر على الزر');
        console.log('Event:', e);
        console.log('Target classes:', e.target.className);
        console.log('Has delivery-order-btn class:', e.target.classList.contains('delivery-order-btn'));
    };
    
    paymentBtn.addEventListener('click', tempHandler);
    console.log('✅ تم إضافة مراقب مؤقت للأحداث');
    
    // إزالة المراقب بعد 30 ثانية
    setTimeout(() => {
        paymentBtn.removeEventListener('click', tempHandler);
        console.log('🗑️ تم إزالة المراقب المؤقت');
    }, 30000);
}

// 4. فحص الشبكة
function checkNetworkConnectivity() {
    console.log('\n🌐 فحص الاتصال بالشبكة:');
    
    fetch('/pos/store/delivery', {
        method: 'OPTIONS',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('✅ الاتصال بالخادم يعمل');
        console.log('Status:', response.status);
    })
    .catch(error => {
        console.log('❌ مشكلة في الاتصال:', error);
    });
}

// 5. اختبار تفاعلي
function interactiveTest() {
    console.log('\n🎮 اختبار تفاعلي:');
    
    const steps = [
        'تأكد من اختيار مستودع',
        'أضف منتج واحد على الأقل للسلة',
        'اختر عميل لديه صلاحية توصيل',
        'لاحظ تغيير نص الزر',
        'انقر على الزر'
    ];
    
    steps.forEach((step, index) => {
        console.log(`${index + 1}. ${step}`);
    });
    
    console.log('\nبعد كل خطوة، استخدم: fullSystemCheck()');
}

// 6. مراقب الأخطاء
function setupErrorMonitoring() {
    console.log('\n🚨 إعداد مراقب الأخطاء:');
    
    // مراقب أخطاء JavaScript
    window.addEventListener('error', function(e) {
        if (e.message.includes('delivery') || e.filename.includes('pos')) {
            console.log('🚨 خطأ JavaScript متعلق بالتوصيل:', {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno
            });
        }
    });
    
    // مراقب طلبات AJAX
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        if (args[0].includes('delivery')) {
            console.log('📡 طلب AJAX للتوصيل:', args);
        }
        return originalFetch.apply(this, args)
            .then(response => {
                if (args[0].includes('delivery')) {
                    console.log('📡 استجابة AJAX:', response);
                }
                return response;
            })
            .catch(error => {
                if (args[0].includes('delivery')) {
                    console.log('📡 خطأ AJAX:', error);
                }
                throw error;
            });
    };
    
    console.log('✅ تم إعداد مراقب الأخطاء');
}

// تشغيل الاختبارات التلقائية
console.log('\n🚀 بدء الاختبارات التلقائية:');
fullSystemCheck();
testEventHandler();
checkNetworkConnectivity();
setupErrorMonitoring();

console.log('\n📋 الأوامر المتاحة:');
console.log('fullSystemCheck() - فحص شامل');
console.log('simulateDeliveryRequest() - محاكاة طلب');
console.log('testEventHandler() - اختبار الأحداث');
console.log('checkNetworkConnectivity() - فحص الشبكة');
console.log('interactiveTest() - دليل الاختبار التفاعلي');

console.log('\n✅ Debug جاهز للاستخدام!');
