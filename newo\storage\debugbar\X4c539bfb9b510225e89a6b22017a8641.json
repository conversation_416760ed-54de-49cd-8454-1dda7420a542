{"__meta": {"id": "X4c539bfb9b510225e89a6b22017a8641", "datetime": "2025-06-08 13:05:41", "utime": **********.935908, "method": "GET", "uri": "/add-to-cart/5/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387940.467665, "end": **********.93594, "duration": 1.4682750701904297, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1749387940.467665, "relative_start": 0, "end": **********.612698, "relative_end": **********.612698, "duration": 1.1450331211090088, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.612727, "relative_start": 1.14506196975708, "end": **********.935946, "relative_end": 5.9604644775390625e-06, "duration": 0.32321906089782715, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53622296, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03413, "accumulated_duration_str": "34.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.724986, "duration": 0.02732, "duration_str": "27.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.047}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.77843, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.047, "width_percent": 4.454}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.829289, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.5, "width_percent": 4.483}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.858061, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.983, "width_percent": 3.897}, {"sql": "select * from `product_services` where `product_services`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8768551, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 92.88, "width_percent": 3.018}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 5 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.892086, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 95.898, "width_percent": 4.102}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-129879657 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129879657\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873988, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/5/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2015375878 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVxNlBhUkJoQ2pwaTZOVmNoa3l1MlE9PSIsInZhbHVlIjoiOFVIYzFTb3hJTzFWVm8yNjN4L0xvTkhDWHJLL25NWmoxRS9iRXo3WnMzVXg3T1ZlZlhiL2ZoNU9QdTVxZEpXMjhidkZPNm16WVJpcCtON1hBWktoOWZZWWNuUGJpUUc2UnlhVGwrMVJsRHA0TmtCTm5aMVVMNUpJOURiUXNWcDYxamlHWUhFQk94Rk9DcjZkdy85L09QYk1VYnVyaXBtV0dKYzVzd3pBUHJ3c1VuQTZhMmw0YUVxd3VMa0xYQkhCNE9BV2xtNGdUK1hsRlRmTTN0V3pPOFRkeHU2NnhCdUQrMVFqbGpRYjkrWllmZ2RVKzhIY3hNcXdWcHljbUtmWFVsbzFDczk5Ky9tSWFXNWhGODh0RnJ1Qml4dldGSnU4QU9UMFBJVFgxVXVCa2JwZkpEcWo2ZTdHbEV6aGJMZGhpQWtQR00xUm9vQjdXVlp5T0xuTVFoOU1Gb2xreDdCYTJEcHlPaFgwWmJZWHRSNnBTRlBGVDNlY2VQN1phNGdZNnZQbktiZVN0MXhmNG1TSWlFMHhySVk4czQ2Vnc0dTZnNlFnZGViNHA2NVlURVpOZ3FFZ3IyMHRONk4yc1hMckg1ZXZ4UlhIKzVoK0VKQUVFYTNoT1F0dU1NWTVXZDZVQ2VlZ29uaVJ2TWM2Q2JHOG9EN3FINTkzZkowMkNKWGciLCJtYWMiOiJhZDY0MjM2NTgxODM0YWIwNGJiNGM2ZTk2NDk5NWZiZWY5MjljOGQzNzIzNGM0MDE3NTdjMmI4MzE2YmFlMDNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBYRlZCTG12VHpYa3FHeXdkanNrU2c9PSIsInZhbHVlIjoiY1NCOWlsc0hrbE9YNmxUYkNmcE9GazIvQUpPQ0tXMXdQZnZ0ekJNOHJuMjF1bHNzdGdnS0V5VUtyOW96N2lqOWRJcm5iQWdZNUt3UFJKY1F4YU5oVW8xUHNkZVhKWGx5VGlRT05kbGNMWURvSFpQM3pJSFFudnd4U01uU0ZGaEZoZUV3N3UvQzMrdHFDY3lLc3FNc0U4NnU3Skc0ZVBpZ1pzZ2lNVGFzekM1SlFneE1mTEtTRFV2ektCcU9qYlpLZTVNQy9DMGlSY0xyTThySTloZC8zaFFWUmk1SE5UV1RpdS82ckFobDczT1BDSkNRTHRmK3l1WjdGYjNmaXRXRUc2Tkl4Nk5qOXFzRjFRSWx0VXBrc1h4Ym1LK3dndnhXNFNBamNsTjN4MGpOQ0RRdk5FMUNUQXB2NGtpOVFQNGlBVUxONzRBR2d3TlRCTDZmSjE0TTNOWTEwUVY0NWpraEhabzZndFJERHZVK3RPY2dHWHl6Zjlab3lNQW44U2l4TWNKdUpDVWtWUGZlbWJIdUhYNEZkTWJCSDByaFVaY1NveEJXS2RqM291Y3hmby9iMDBFUFdpbTZZUXo0a0l6UEFlem1MTVVBdzFWNnZuMFMwTFUwV3UvM1lPTC93cXkyaG1pMlR0Y3FyYjgxV25TM1lNNUpwU1V1S3BqbG16THIiLCJtYWMiOiJhZmE2Mjc5MTlhZjQzNWU1MTY2ZDNjODQwZDFiZjlkOGFmYWIzYzYyZGY0OTFkYjZiZTk5MjM5NGI5NmYyODUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015375878\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1952682349 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952682349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-843563820 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFEejJmQ0pwS0F4OWpvWjJsSE9hdVE9PSIsInZhbHVlIjoiTHF3MHpjbnY4NWU5SFVueGVHMVJVRE9vQzkzV1YyRm55L2xzaVBlZEFuY0N1YUNSK2piNUx3em0wOUVpNzdwSWE3cVl1RmdpL3orbkdvREc2ZEFCdzAxSGJpSitQZ21CbDJ1M2ROd0pIQmowU1crYkJGY285NExlMU5UajdpUHNHMVVZS2ZvMWNvNFpsTVJVdWQ4NEYwejJTVGtmVHlRNmlCQkZMREFBcjVpNkY5UVVseU9kL0xNYlBTN2VMdU1EaDNzWllkWGR6Q3dlU0xrYVF4Z2FueXFXTVRXT0NSK1c0VHNtV0xUeXMwZ2ZCalFPUTA5V1EwMXdOOFE3ZUpBWGljZVFWQktSVTd5U1h5aFhaQjY5YTF3T0krdTV6UVJoNitVZi9RQ3FzTUoxM3FiR0czUDF3N2pqR0FQYlBkUDJwb0c2Y2lPRnNRdUpPeCtYWUxhZmlVUXlNTWZiRzROLy9jcVhVZ1VUSVJvOXNKRDI5R0pzb0thZGxrcmZLT283SjN0VnZoVjQzaW1kV3lFd0FMWDJmRExtWUEvQXRINXNibjgwMmxLYmZMd2xabVloUVRTN0cyRlJtREtMUTVMcEdVN1l5Nk5BZFkraDRPdlJyakh5UW9XUjRnZU9hSlQySjF4Y0JpNjUwUThEMzBFMXZ4aWIwSnliWDZZYm5KK1oiLCJtYWMiOiIxYjMwYWRlYjJjZWMyMmQ1YmJhMjNhZDBmMDRkZjI0NDBjNGQ1ZDU4YThlZGU0NGRjMDU4NWU4YmE5ZDAzNjM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdMeGZGZTFMTjRXdXFSd0N4OGVTNGc9PSIsInZhbHVlIjoiKzk4ZE9UZS9YTnNQbWNQY3h1SGtDQk8wTzdzQUFoZkJOcFIwREtNcHJHaFY1dFZIWllXaWhjeEF4SzFWQkMyb2FRRkNvMXl0VWlsZGV3YUlOd1AvQU5PWDZnNXJpKy9tbENUd1NKOGxuS2h1QVZjdlB3L21JTnVxU2Z3NDcxTGFXbktHczF0Y3BhSUo4b0VSdndWYUFHUTJwazMzT3E2c2VYRHhzb2pjQmd3UDhqb00yU1lOb1hMbTc4TVBBUHJ4RGliWTNNcTJQbW5UWERjOER6NTZpeHg0S2gxTFl3aDNTOEJUVXJZR0x5cTdZOWZibHdxR1NxSHZFVnA4dkEvcUFCUGtOZEtkZ3h6eVYrWVdtYmdBMU55bDA0azJtaCtsNWUyeFRUWDI3ZWR3K2JaKzk1Rzd5djd4eWZUaHRVTlRaS2xncDdSTmZqTjRmYndkWlh0SXFzc3BIRGZQdFhIaW1sb2NPM3RTWTQvYzE1cFJMcUtmNVEwdFUrUUpLMHpLQmNSakppaW9RZ01jcTEyYlZZT2FQVVhxRXlNby9iTE1oN0M0bUQ5TnlmOFB1MDBReUU5ekpDL1BNQnI5anlLSlhyUzV2L003TEFCWllMT1JTT3gwd2lnbXZZakxiZTY5MGcwM2pzRmhFTHRydnlaSS9rWVpzTHlRMStMdkY2dlciLCJtYWMiOiI1OWU0ZjcxNGFhMzQ4OGNjYTBjYzI2YzlkODgyNjc0YjRiNjM3YjFhZTVlOWE2NzkwMjllYWU4YTIwMTJmM2M2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFEejJmQ0pwS0F4OWpvWjJsSE9hdVE9PSIsInZhbHVlIjoiTHF3MHpjbnY4NWU5SFVueGVHMVJVRE9vQzkzV1YyRm55L2xzaVBlZEFuY0N1YUNSK2piNUx3em0wOUVpNzdwSWE3cVl1RmdpL3orbkdvREc2ZEFCdzAxSGJpSitQZ21CbDJ1M2ROd0pIQmowU1crYkJGY285NExlMU5UajdpUHNHMVVZS2ZvMWNvNFpsTVJVdWQ4NEYwejJTVGtmVHlRNmlCQkZMREFBcjVpNkY5UVVseU9kL0xNYlBTN2VMdU1EaDNzWllkWGR6Q3dlU0xrYVF4Z2FueXFXTVRXT0NSK1c0VHNtV0xUeXMwZ2ZCalFPUTA5V1EwMXdOOFE3ZUpBWGljZVFWQktSVTd5U1h5aFhaQjY5YTF3T0krdTV6UVJoNitVZi9RQ3FzTUoxM3FiR0czUDF3N2pqR0FQYlBkUDJwb0c2Y2lPRnNRdUpPeCtYWUxhZmlVUXlNTWZiRzROLy9jcVhVZ1VUSVJvOXNKRDI5R0pzb0thZGxrcmZLT283SjN0VnZoVjQzaW1kV3lFd0FMWDJmRExtWUEvQXRINXNibjgwMmxLYmZMd2xabVloUVRTN0cyRlJtREtMUTVMcEdVN1l5Nk5BZFkraDRPdlJyakh5UW9XUjRnZU9hSlQySjF4Y0JpNjUwUThEMzBFMXZ4aWIwSnliWDZZYm5KK1oiLCJtYWMiOiIxYjMwYWRlYjJjZWMyMmQ1YmJhMjNhZDBmMDRkZjI0NDBjNGQ1ZDU4YThlZGU0NGRjMDU4NWU4YmE5ZDAzNjM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdMeGZGZTFMTjRXdXFSd0N4OGVTNGc9PSIsInZhbHVlIjoiKzk4ZE9UZS9YTnNQbWNQY3h1SGtDQk8wTzdzQUFoZkJOcFIwREtNcHJHaFY1dFZIWllXaWhjeEF4SzFWQkMyb2FRRkNvMXl0VWlsZGV3YUlOd1AvQU5PWDZnNXJpKy9tbENUd1NKOGxuS2h1QVZjdlB3L21JTnVxU2Z3NDcxTGFXbktHczF0Y3BhSUo4b0VSdndWYUFHUTJwazMzT3E2c2VYRHhzb2pjQmd3UDhqb00yU1lOb1hMbTc4TVBBUHJ4RGliWTNNcTJQbW5UWERjOER6NTZpeHg0S2gxTFl3aDNTOEJUVXJZR0x5cTdZOWZibHdxR1NxSHZFVnA4dkEvcUFCUGtOZEtkZ3h6eVYrWVdtYmdBMU55bDA0azJtaCtsNWUyeFRUWDI3ZWR3K2JaKzk1Rzd5djd4eWZUaHRVTlRaS2xncDdSTmZqTjRmYndkWlh0SXFzc3BIRGZQdFhIaW1sb2NPM3RTWTQvYzE1cFJMcUtmNVEwdFUrUUpLMHpLQmNSakppaW9RZ01jcTEyYlZZT2FQVVhxRXlNby9iTE1oN0M0bUQ5TnlmOFB1MDBReUU5ekpDL1BNQnI5anlLSlhyUzV2L003TEFCWllMT1JTT3gwd2lnbXZZakxiZTY5MGcwM2pzRmhFTHRydnlaSS9rWVpzTHlRMStMdkY2dlciLCJtYWMiOiI1OWU0ZjcxNGFhMzQ4OGNjYTBjYzI2YzlkODgyNjc0YjRiNjM3YjFhZTVlOWE2NzkwMjllYWU4YTIwMTJmM2M2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843563820\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}