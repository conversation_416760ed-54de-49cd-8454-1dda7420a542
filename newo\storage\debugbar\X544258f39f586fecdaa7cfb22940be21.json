{"__meta": {"id": "X544258f39f586fecdaa7cfb22940be21", "datetime": "2025-06-08 15:11:07", "utime": **********.972572, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749395466.996663, "end": **********.972612, "duration": 0.9759488105773926, "duration_str": "976ms", "measures": [{"label": "Booting", "start": 1749395466.996663, "relative_start": 0, "end": **********.847337, "relative_end": **********.847337, "duration": 0.8506739139556885, "duration_str": "851ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.847354, "relative_start": 0.8506908416748047, "end": **********.972616, "relative_end": 4.0531158447265625e-06, "duration": 0.12526202201843262, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02196, "accumulated_duration_str": "21.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.902268, "duration": 0.02035, "duration_str": "20.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.668}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.940296, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.668, "width_percent": 3.461}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.954678, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.129, "width_percent": 3.871}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1749987095 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1749987095\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1445950495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1445950495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-995130107 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995130107\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-541088027 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749395465224%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZaMzY3aSsybEMvbnozYmFBRDhZcGc9PSIsInZhbHVlIjoiY3BmYzJERHdwdkxTSFJXcW0zblhOQ0VhemhmZTdaU296M1BnRjRMa1E2NjRTd3BCVzZwelhxTzdYS3kzTkVzakxDaGhKWXpISDJIVUg2cjNIUHFGTURBRHkvR2lwRXQyTXpQaHk1VFZPaE8zeVhYYlVJYXJjL1k5ZVVDQWo2NGkrYmREcW9tZWdNdGUrVGZ3ays1UjJHQklQWVZRVUE5T3Q5OWZDbExVUk5yUGpLUVNKeHJhTkZIa3NFNlBIUkdySG16Y1dJSXNLeUE4Z2h5THpESExtdXdpQlFEQTM5VEJFVjdidkZOTnJhTVArTG1SaFhBNnErRXFmU09jMTVyWXVFTytRUHNNMHRmcDUvbGVYMTFLcmFKM2w0MERIS1dUSGhkdEl2aDRSYjlEVUhMdDh6T2Z1emtSSVo2MDQ3VkhqRHVFU1ZIZ0dUVkZWSUFHN1JQZVdOSzN0UkJlUDFJQU5VazRJeU5nSGcvMHdwRlptWEd0c0dGZXFKSkhZU0pjNGVHS2dPTlQzaGZUalNnV3lmYVBWdkpaTjNEV0RjQnZ6UHUweFNTMUFaWnRaSDJ2S0x4cGJRZmswQzJuUGg3VlJTcWVZRllnSXhjaU5zSEJDcmpDUnN2SWhqalRIa1F6cEsxSlJ0cStYWDhqdWpqRHlrQUpsNi9IOFN5WFp6U0oiLCJtYWMiOiJhNTY5MzgwZjY4MzBkMzJjNWI4NWU1NGE2NTNjNzc2MWViNjk5ZTg4NzFiNzJiOTFkZjMwZjBkYmE2MTQxNDQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikxvc0dJTmJKNUR2aEFORm80ZXVob2c9PSIsInZhbHVlIjoiaGJVU0dhRDBhUHQxRGNWWlcxbnVqTjlDM3BkSThLQWxkSHRITnFxUSt6WVgvdGpIYklJR1VKVm90S1M3NnR2V2R6dklLQUNjNjc4dytIWTdaVWdod1VXWEQyRVFXZEtMYXZWVDJHakJFRHpuaUYwZ0ZkT3RyLzc2SU1wcjB5RzNWdjBabnhFSW5wUkNBZVp5RkM4U0xBUDNmY2N4RjVIK3RTd0V3bWF3UGxxMDRjS1VXTnk0bHA2M3E1NzZJRmc1WmhxbWlScG51SzlGVXRUUzliM0FzME5aRDJ3cVNyVEtZSC9PWlFHK1pyQVg1N0V5KzNXZ21kSS9rM3FYRXM3Z0xYQjZiZUlvTnJxRE1OeUQ1NDljdHRmMmRpWWxVWEVEVmJMZnNob3lQQklLTFJHUDVWVHBwY2xNTTJsYkdkU0JxM2lST2ZGY0xNQ3VkVWpNOGI0NUF3aXFSb3FxbXdkdnlpTnEvRFM4NVFaV2lENWI0anJuR0FSYkVRVitpUW1JcTZHTkhLWkFCS2wrOFNGb0ZJMDZudVlNMDJBMDRkcXk3cHV3cld5eXZudjNEYVBFN1g5MHArdlFiamIxQmRGbnkzSW5Ca3NGRk9VdnVacEg3Vm11c05zZFdzZWVoSW9xOTlZVEp0czZrYS9XbEpDRzI5aWdjejl4dWxKNU8yT1QiLCJtYWMiOiJjMTNjZjQ1MDZjYjUwMDNiMmI4N2MzNmE2ZjAyNWNkMTk3MzA1NWRhNTYxNzkxNWIwMTgxZmNiZTE2ZjQ3MjNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541088027\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-723127664 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-723127664\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1034873484 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:11:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZlcHY0NG1sRjQzYTFQZ2VpYVMwNUE9PSIsInZhbHVlIjoiUFpWdCsrZWRBVjV3UFhQSSsxNjJMSklTT212VzBiZXhoamFwRmRPcGxpV2xtSVAzOUwxNkE4MkdTZ0M3RGkyV1loeTJEQXRNYzgxdmhodEVRQUt3QnNqZGhZbUN3bDA2WEtNRTlJYTJ1dGRWeVMrTVBUcXlHV1p2Vks3MUNxV3BvQlNHQ3pxMm9VVHlhMS8yWjlOYzV5RlFmZitEZHdndm5FSjE5eXlydFdUTDhpNHhGZWlqRVpmRm1Sc0MrOStHLzJDUVNGcGhuTWVLV0JScGVaZDdwRDlwL2lPTkl4a25WZ0RSeHNMZWxtc3FXMzErYVVEb05qd2hYVnh2V0VCMEhGY1p6MUZUTlNEdEsrRkdwdjhDckMvVTd5RTdEMThyd2R4bmtJUzE1aVZRb1R4Q2liejZKZmREQ2VoMHc5Z0xLbFprLzNkajlOSWViMHB1MGRuNk5sazNjVUd1ZzB0cis4Y1R4cEpGRW1ROWZENTYxUnFtQkFKNElxZEMrYjBWeXA5UFk4cGo5UVBhOFc1ZUx6NWRkVzFXNXBIRUg5VDJybkdsRkM2TXI5VnpYL1NERmRLTHUvb1F1aXNmMC9USE5zdE9xNFI2VE9nTmVLelVidGtyendpMm9HWVFzdmhpeERaREczYVZVWG5IZ2FNaDd2ZXZOYVVucXVwc1lSSFMiLCJtYWMiOiIzYjUyNTUzYjAzMTc1ZWVkN2U4ODVhOTkyYTlkNzczZWIxMTJjOGIwZGE4NmNjOTkxYWRlYjczZjRmZDhiYjA1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:11:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImU1MzdSVGp1RUpsQmJQb0RxUHlmT2c9PSIsInZhbHVlIjoibjVXNjhUZlhDSk5VYWRPWG5ZbWJidUhDTXcyZXBnaE82SUkwcWV0Q1ZRUzVZakQ1a2JEcklSRGE2RVhZSExhUnd2TkVFVkhnbCtaL1FJalFkT0xNbkI0Y2RqTTdNRURSZzZ0U1hQY2dGSVFESTZ1aEJrL2FTRk1Wa1N4RkdoTUs2SnRxTGRsQmFabGRJaTJkRjRCTUNsdzMyZkdVeDEralRlK1p6S05qaitJVmc3RUpYSTJhRVBlZ2pWMm83ajkrUGYrd0pXeFZkRDRGeDI0b3dWSUdud3dTQk9pdnFYbVRJcjVRSitmZXVxdDJXRXRDbms2d0N3YXA2ZnNJS2RqSEVScjE0L244RmRuYmZQVkV0Z2RnL3NsdXprWUl5azVPMGNrV0diQmVqTFhFT3lnc3ppVW5HODMzeURLZnRXUUFWaVcwa1dUcndOelBEaG5nNFdsVWUwVTlEazd6M1lKeEhNdEJqMlZRZk1vSjdIQkw4SDByZHJnTEUza002QnQvYVNFUjc0NkRYWFZiRUErNkZLNGxaYUl2aU1XU0hSZ25UM3JJeXFVRUhLK1Y1V2hwSU9VTzEyOTNMMEk3SG5VVUt5dWx0L09xb05HdmhWcStkcjc5eVJOM2VxUUovYkc2UzFiU1ZMR1NaR2tsbHF0QzA4TUJEcVF0VjhFY2ZpcysiLCJtYWMiOiJhMWM4ZmM2OGU5NDljM2ZiNzViZTRlMDBkYmU1YWFkYjcyZTNkOWNiMzAzMTU4ZWMxMzkzMTUxZTNlOTI5OWE3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:11:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZlcHY0NG1sRjQzYTFQZ2VpYVMwNUE9PSIsInZhbHVlIjoiUFpWdCsrZWRBVjV3UFhQSSsxNjJMSklTT212VzBiZXhoamFwRmRPcGxpV2xtSVAzOUwxNkE4MkdTZ0M3RGkyV1loeTJEQXRNYzgxdmhodEVRQUt3QnNqZGhZbUN3bDA2WEtNRTlJYTJ1dGRWeVMrTVBUcXlHV1p2Vks3MUNxV3BvQlNHQ3pxMm9VVHlhMS8yWjlOYzV5RlFmZitEZHdndm5FSjE5eXlydFdUTDhpNHhGZWlqRVpmRm1Sc0MrOStHLzJDUVNGcGhuTWVLV0JScGVaZDdwRDlwL2lPTkl4a25WZ0RSeHNMZWxtc3FXMzErYVVEb05qd2hYVnh2V0VCMEhGY1p6MUZUTlNEdEsrRkdwdjhDckMvVTd5RTdEMThyd2R4bmtJUzE1aVZRb1R4Q2liejZKZmREQ2VoMHc5Z0xLbFprLzNkajlOSWViMHB1MGRuNk5sazNjVUd1ZzB0cis4Y1R4cEpGRW1ROWZENTYxUnFtQkFKNElxZEMrYjBWeXA5UFk4cGo5UVBhOFc1ZUx6NWRkVzFXNXBIRUg5VDJybkdsRkM2TXI5VnpYL1NERmRLTHUvb1F1aXNmMC9USE5zdE9xNFI2VE9nTmVLelVidGtyendpMm9HWVFzdmhpeERaREczYVZVWG5IZ2FNaDd2ZXZOYVVucXVwc1lSSFMiLCJtYWMiOiIzYjUyNTUzYjAzMTc1ZWVkN2U4ODVhOTkyYTlkNzczZWIxMTJjOGIwZGE4NmNjOTkxYWRlYjczZjRmZDhiYjA1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:11:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImU1MzdSVGp1RUpsQmJQb0RxUHlmT2c9PSIsInZhbHVlIjoibjVXNjhUZlhDSk5VYWRPWG5ZbWJidUhDTXcyZXBnaE82SUkwcWV0Q1ZRUzVZakQ1a2JEcklSRGE2RVhZSExhUnd2TkVFVkhnbCtaL1FJalFkT0xNbkI0Y2RqTTdNRURSZzZ0U1hQY2dGSVFESTZ1aEJrL2FTRk1Wa1N4RkdoTUs2SnRxTGRsQmFabGRJaTJkRjRCTUNsdzMyZkdVeDEralRlK1p6S05qaitJVmc3RUpYSTJhRVBlZ2pWMm83ajkrUGYrd0pXeFZkRDRGeDI0b3dWSUdud3dTQk9pdnFYbVRJcjVRSitmZXVxdDJXRXRDbms2d0N3YXA2ZnNJS2RqSEVScjE0L244RmRuYmZQVkV0Z2RnL3NsdXprWUl5azVPMGNrV0diQmVqTFhFT3lnc3ppVW5HODMzeURLZnRXUUFWaVcwa1dUcndOelBEaG5nNFdsVWUwVTlEazd6M1lKeEhNdEJqMlZRZk1vSjdIQkw4SDByZHJnTEUza002QnQvYVNFUjc0NkRYWFZiRUErNkZLNGxaYUl2aU1XU0hSZ25UM3JJeXFVRUhLK1Y1V2hwSU9VTzEyOTNMMEk3SG5VVUt5dWx0L09xb05HdmhWcStkcjc5eVJOM2VxUUovYkc2UzFiU1ZMR1NaR2tsbHF0QzA4TUJEcVF0VjhFY2ZpcysiLCJtYWMiOiJhMWM4ZmM2OGU5NDljM2ZiNzViZTRlMDBkYmU1YWFkYjcyZTNkOWNiMzAzMTU4ZWMxMzkzMTUxZTNlOTI5OWE3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:11:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034873484\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1681763541 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681763541\", {\"maxDepth\":0})</script>\n"}}