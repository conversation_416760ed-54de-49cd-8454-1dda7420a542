{"__meta": {"id": "Xecd1b941be20357220134ece13d12fc4", "datetime": "2025-06-08 13:15:08", "utime": **********.385009, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388506.952108, "end": **********.385041, "duration": 1.4329330921173096, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749388506.952108, "relative_start": 0, "end": **********.183643, "relative_end": **********.183643, "duration": 1.2315351963043213, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.183664, "relative_start": 1.2315561771392822, "end": **********.385044, "relative_end": 3.0994415283203125e-06, "duration": 0.20138001441955566, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00864, "accumulated_duration_str": "8.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.286505, "duration": 0.00644, "duration_str": "6.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.537}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3312, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.537, "width_percent": 12.963}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3512669, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.5, "width_percent": 12.5}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2101264303 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2101264303\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1684331477 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684331477\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-874671510 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388152047%7C15%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1SbTZXOXRlVEYyZ0VhekZXa3RNSGc9PSIsInZhbHVlIjoiZnJwUmdqSHNCYUdhSHJkTm9DM0tZTUJ2RkJmbm1hK2h5YmQ1enBqMkdGeXdqUVVQOXdKMEp3UTVzampVanNMakVQV3RnczZ3MXJqbHBnUzZMWFhmZGxlOFI4aXpmNE9YeHY0UFE4cUVWSWRrS2VrSDIrYjFTSDcwVy84YVRTYUNNemtyUkg5dzFDMGx4anVGcUp3MFJXNHBZRVFwV0FYc3RCMUhHVHBHZWhmQ3RYcmUwTmZpakREaFRwV2UrV1FMUUVGZ0ZyaFlDb1dNaks5eGs1SUdYcDBLblFXOW5zY1RacHBrVURqMDRKeXl1VndxNWo4RDhaY2ZTNHF5R2MwVElKcVJjU0lZQ3hrSDRmcDltOUg1bTN5ZTcwUTI2dThnZVpZN2tmcmNPUmJVMjhQcXdtdFFaRkNodTJYd052a1g0eHJFdXdtMkxaeWd6ZFB0NW1qUTBmTWxiRURUTkQ4eW5vaVBuSVJWZWZKNXhkVVR4SHo4TmRIcjdKQ0thczh1SnI5dForWTNYNEdabWlqQ2FFYkp1dncyTTVybXE0djRPRDRSMTNrMFJhdFdLOGpmWEo5T3NzMmxxdE5xeFRLbFFob0RyS3ZxWURzRnNUekVrWnQwQVFOMld6bEo4WjZpa0tmbWJBakNyRVpLdE5XdjVHVkVBNDJmQzVucTlQeTAiLCJtYWMiOiIxODAzZWE0MWZmMGRmYTY2OGZjMjJlMWZjMzQyZGRkN2JmNGNmZjc4ZmY0NTliMTliODlmY2Y0MWY4ZmNhNGZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im9LdzZBUVQvTXFDRFlRRFo4bkhIV0E9PSIsInZhbHVlIjoieHpYRmM3bG1tdFRkbmNCdmxCd0g4REx3dzdFYnkzdmJhczBsZC9rTEZKdEdyMVBiME1QUkVkLzlFYmx1aGo2TWZ4cFNuMC9GbkpxUHBnQy9EN3g2cUJOdE84b1YzREF0NFA4ZjRrQUxyZC9vanEvWGpMNCtIc0FhR3ZpVHRwZDgzbVhqMEZ1aGN4RG5OVllMS0k5YU5GRGtSakpYQTBBSWpjZndVQkxmS3FrdUtxaFdjWTFwUHN6d0xWS2h1cW0wVktscC9zbXp4L0VtcUxmcDR2bmY2YkVFa3ppN1J0NEcyTHVMZEJTNXIxY0FIS2t2cGZ1a2dIRGhjWERYekJWdEVlMzFDK1B0RkhveUNFQjVJN2R3NktnNTRRUmF4MEMvelZFNXhZZWJqNVo3S0ZCK0RNNU84VlVHUjB6Z0lJbEx0Q3F5cUkyNmNYVGxBVmtVaXdVd3UzMWh6bEpSS09kMmpaaGhpZFJUNTdiSkVxemplWkhEKzUvOFpZemlZQktWUEUvT2VUL3FBL1NhNG5EQkhNRFYyVVptRlZvbjlCbUQwSG5WMFJWSERQWWQwdzQ1QXF5Q05KQmZzR0U2QkFDbTFjWkI4bTV0WXd5bTlWcnpQUXZEUnVDLzJvUTdKUTJvS20xNm1wMzk2SElvbmRDeHhhWFJWQWRpbkIvbFhiQlciLCJtYWMiOiIyM2VmNTkwMDg4YjRkNDAxZjhhNDM4YjdlOWI2YmM0MWNiNGVjZTlhZTk1NThmYTViZGZhMTQ0NGUwNDg3ZDlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874671510\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1478668308 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478668308\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:15:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFrTE4xVVBKa1kvWkRkN1RNOFBaREE9PSIsInZhbHVlIjoiVjJKcUxSeFNjUjNVbVpQVFRuSFZrNnN5a2lWa2dsMDBycGt1Q1dHUURPZE44UCtzbTlvRWRmQWVqSlBXRDgwenpRYXlXRVQrcTROSk1yblFIOC95dnhYYlp6K3B1VU8wSFlIbEVSTUNYZVJXUzh3UURlTzMyOUlWOENjQW1SSnZqcnRVVGZxbjNWUHdFNDZrZlcrQ1FnNEMzNlV1aUk0ZFdhTiticDFGTmthZVhZWkF4ek53Zkl4RFRFcWs0RFR4SGNFVXZxcTJsa3ZDSGxsMDVNNzFGOUl2Ny95S1hOdkpzcnZuY0t4MmhLRFJlR1BVZTJ0eFliTnBQWjhiRm5TQXRZRVp3bjFrTldMWThxOGI3dzlWcTBpVlZQeWQ0NW5XbDJmSDdRa0ZRaW9NUlNIZ1ZYWS91UzhwdGFvZmtvTER4N29qVTNjejl5K1g5b2c4WkdMTHN4TWo0WDNwNU5SMVNuaUdkL2Z4MGl0MkxYU3NXS3hLeWV6TVlaTWhVRGlmYWNKaUpSWXArY2RiMTBnbFBiZWFVZFBhZmtyUitnMWJaTVlRVlRFRWI1Q2tYZTh2VDJMbjdNak9zTnJmRXc4cUkxR2JYemxUbk5XUEJJdHJCcnBUOEkwUjhjcFJWM1dVUm9zbWxPR01sYzhJZUcyYzZMTTAySlFqcmgxOHRxdGkiLCJtYWMiOiI5NmZlYzZmMjY3ODQ3ODg2MjBjNmQ5NTlhY2E0MDUyNjRkZWRjNTYzY2U2NTk0NGQwZTYzNTVlOTc0YWY0ZTRhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJjRUYyTWdlM0NEZXZ6U0toblVVVWc9PSIsInZhbHVlIjoiM1pMTGdqT2FwZ0hqaVEzUFp4U1pVN0doVjlUYlhtamZSR3ZNcUJnZ1VIenNvZkNuWk4yRzV0TzR1Z3R2ZnlMMHNvSW1XNW14c1RaNXNrbU01Y01EeEVWQ1ZGSldVckMxR3U1eTVrVUlDakc5STBqenZZZFRMNFZKSDMrTUh6L01VVnpTQnA4a2FVNzVLR1NqdzhJc09WRkJzZmNyaGxoU1hNVWpHNHR3TmlGaDVscFBWb2ZWTVQrVXc0bzJpaFQyNm84L1RWYzlWeUxzdVBSMW9zZVBZMXB6TXA2OFV2d0NSUDZ6WVZCditLbVZBWHh2ME8zU1FDNDduOUdKb1VHTUZTbmwwUU85K0hrZldrbmhEVTU3ZDZjS3JKZHJXeTZ5b3U0aUhJdGxJUUpBdXJEdmQ1NFFIOG8rWlNuZFlHblcrREo0UjRqVTZZNHA3NzErU1FqSXo1a2RGR0ZlbitXWmFIYjU4T0xUbkdDb1VBVkhWMmZQM2V4K3A3QXZpZXdwKzJDYnJrSWswT0RqTVdyaU1KcGNnejFHZkgyY3hYTTBpT0hROXZMWEZjOGFkMVV2WWhtampkYUxJWktKaVZYYSt6bGJNU1cxSEV2eUpITThBdjh0djAwamlCTmhVcW5WV2tOOVp0bWZ1dWtRSHVKQzVIUmNxcUVyV0JpL3ppU0siLCJtYWMiOiJjNGY0MDAwMTQ5NzU0ZjIyZTAxNWU3ZTA3NjUwYWFjYmM5Y2ZiYjNkYjdhZmU4YWM0ZTg1ZWI3YjVkMzUyZDJmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFrTE4xVVBKa1kvWkRkN1RNOFBaREE9PSIsInZhbHVlIjoiVjJKcUxSeFNjUjNVbVpQVFRuSFZrNnN5a2lWa2dsMDBycGt1Q1dHUURPZE44UCtzbTlvRWRmQWVqSlBXRDgwenpRYXlXRVQrcTROSk1yblFIOC95dnhYYlp6K3B1VU8wSFlIbEVSTUNYZVJXUzh3UURlTzMyOUlWOENjQW1SSnZqcnRVVGZxbjNWUHdFNDZrZlcrQ1FnNEMzNlV1aUk0ZFdhTiticDFGTmthZVhZWkF4ek53Zkl4RFRFcWs0RFR4SGNFVXZxcTJsa3ZDSGxsMDVNNzFGOUl2Ny95S1hOdkpzcnZuY0t4MmhLRFJlR1BVZTJ0eFliTnBQWjhiRm5TQXRZRVp3bjFrTldMWThxOGI3dzlWcTBpVlZQeWQ0NW5XbDJmSDdRa0ZRaW9NUlNIZ1ZYWS91UzhwdGFvZmtvTER4N29qVTNjejl5K1g5b2c4WkdMTHN4TWo0WDNwNU5SMVNuaUdkL2Z4MGl0MkxYU3NXS3hLeWV6TVlaTWhVRGlmYWNKaUpSWXArY2RiMTBnbFBiZWFVZFBhZmtyUitnMWJaTVlRVlRFRWI1Q2tYZTh2VDJMbjdNak9zTnJmRXc4cUkxR2JYemxUbk5XUEJJdHJCcnBUOEkwUjhjcFJWM1dVUm9zbWxPR01sYzhJZUcyYzZMTTAySlFqcmgxOHRxdGkiLCJtYWMiOiI5NmZlYzZmMjY3ODQ3ODg2MjBjNmQ5NTlhY2E0MDUyNjRkZWRjNTYzY2U2NTk0NGQwZTYzNTVlOTc0YWY0ZTRhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJjRUYyTWdlM0NEZXZ6U0toblVVVWc9PSIsInZhbHVlIjoiM1pMTGdqT2FwZ0hqaVEzUFp4U1pVN0doVjlUYlhtamZSR3ZNcUJnZ1VIenNvZkNuWk4yRzV0TzR1Z3R2ZnlMMHNvSW1XNW14c1RaNXNrbU01Y01EeEVWQ1ZGSldVckMxR3U1eTVrVUlDakc5STBqenZZZFRMNFZKSDMrTUh6L01VVnpTQnA4a2FVNzVLR1NqdzhJc09WRkJzZmNyaGxoU1hNVWpHNHR3TmlGaDVscFBWb2ZWTVQrVXc0bzJpaFQyNm84L1RWYzlWeUxzdVBSMW9zZVBZMXB6TXA2OFV2d0NSUDZ6WVZCditLbVZBWHh2ME8zU1FDNDduOUdKb1VHTUZTbmwwUU85K0hrZldrbmhEVTU3ZDZjS3JKZHJXeTZ5b3U0aUhJdGxJUUpBdXJEdmQ1NFFIOG8rWlNuZFlHblcrREo0UjRqVTZZNHA3NzErU1FqSXo1a2RGR0ZlbitXWmFIYjU4T0xUbkdDb1VBVkhWMmZQM2V4K3A3QXZpZXdwKzJDYnJrSWswT0RqTVdyaU1KcGNnejFHZkgyY3hYTTBpT0hROXZMWEZjOGFkMVV2WWhtampkYUxJWktKaVZYYSt6bGJNU1cxSEV2eUpITThBdjh0djAwamlCTmhVcW5WV2tOOVp0bWZ1dWtRSHVKQzVIUmNxcUVyV0JpL3ppU0siLCJtYWMiOiJjNGY0MDAwMTQ5NzU0ZjIyZTAxNWU3ZTA3NjUwYWFjYmM5Y2ZiYjNkYjdhZmU4YWM0ZTg1ZWI3YjVkMzUyZDJmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1408998740 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408998740\", {\"maxDepth\":0})</script>\n"}}