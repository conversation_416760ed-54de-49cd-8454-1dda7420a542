{"__meta": {"id": "Xd4d4b6e15dd3bed52cdfe705de9aad61", "datetime": "2025-06-08 13:34:47", "utime": **********.063118, "method": "POST", "uri": "/receipt-voucher/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389685.637834, "end": **********.063159, "duration": 1.4253249168395996, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749389685.637834, "relative_start": 0, "end": **********.821386, "relative_end": **********.821386, "duration": 1.1835520267486572, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.821412, "relative_start": 1.1835780143737793, "end": **********.063163, "relative_end": 4.0531158447265625e-06, "duration": 0.24175095558166504, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45779896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST receipt-voucher/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptVoucherController@confirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "receipt.voucher.confirm", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=146\" onclick=\"\">app/Http/Controllers/ReceiptVoucherController.php:146-172</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.043219999999999995, "accumulated_duration_str": "43.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.91094, "duration": 0.022449999999999998, "duration_str": "22.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 51.944}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.960438, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 51.944, "width_percent": 2.568}, {"sql": "select * from `voucher_receipts` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 151}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.970945, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:151", "source": "app/Http/Controllers/ReceiptVoucherController.php:151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=151", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "151"}, "connection": "ty", "start_percent": 54.512, "width_percent": 3.031}, {"sql": "select * from `users` where `users`.`id` in (16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.981163, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:151", "source": "app/Http/Controllers/ReceiptVoucherController.php:151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=151", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "151"}, "connection": "ty", "start_percent": 57.543, "width_percent": 2.684}, {"sql": "select * from `users` where `users`.`id` in (16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.988658, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:151", "source": "app/Http/Controllers/ReceiptVoucherController.php:151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=151", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "151"}, "connection": "ty", "start_percent": 60.227, "width_percent": 2.337}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 346}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.996671, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:346", "source": "app/Services/FinancialRecordService.php:346", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=346", "ajax": false, "filename": "FinancialRecordService.php", "line": "346"}, "connection": "ty", "start_percent": 62.564, "width_percent": 3.17}, {"sql": "select * from `financial_records` where `shift_id` = 2 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 352}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.00784, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:352", "source": "app/Services/FinancialRecordService.php:352", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=352", "ajax": false, "filename": "FinancialRecordService.php", "line": "352"}, "connection": "ty", "start_percent": 65.733, "width_percent": 3.216}, {"sql": "select * from `financial_records` where (`id` = 2) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 364}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.014933, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:364", "source": "app/Services/FinancialRecordService.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=364", "ajax": false, "filename": "FinancialRecordService.php", "line": "364"}, "connection": "ty", "start_percent": 68.95, "width_percent": 2.846}, {"sql": "update `financial_records` set `current_cash` = 100, `total_cash` = 1100, `financial_records`.`updated_at` = '2025-06-08 13:34:47' where `id` = 2", "type": "query", "params": [], "bindings": ["100", "1100", "2025-06-08 13:34:47", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Services\\FinancialRecordService.php", "line": 364}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 156}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0216122, "duration": 0.00924, "duration_str": "9.24ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:364", "source": "app/Services/FinancialRecordService.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FServices%2FFinancialRecordService.php&line=364", "ajax": false, "filename": "FinancialRecordService.php", "line": "364"}, "connection": "ty", "start_percent": 71.795, "width_percent": 21.379}, {"sql": "update `voucher_receipts` set `status` = 'accepted', `approved_at` = '2025-06-08 13:34:47', `voucher_receipts`.`updated_at` = '2025-06-08 13:34:47' where `id` = '1'", "type": "query", "params": [], "bindings": ["accepted", "2025-06-08 13:34:47", "2025-06-08 13:34:47", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.040652, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:162", "source": "app/Http/Controllers/ReceiptVoucherController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=162", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "162"}, "connection": "ty", "start_percent": 93.174, "width_percent": 6.826}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\ReceiptVoucher": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FReceiptVoucher.php&line=1", "ajax": false, "filename": "ReceiptVoucher.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-voucher/1\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "success": "Receipt Voucher has been Accepted successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-voucher/confirm", "status_code": "<pre class=sf-dump id=sf-dump-1875563715 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1875563715\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1687779744 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">52</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389683194%7C31%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZ2N01sbEtHQjNXYk92M1lZajMwTkE9PSIsInZhbHVlIjoiQ1VFdXNxdmpVK3k1TGY2NHU5MG9XM2hYcGFQQnJuZVNQL3Qyb0N5SE5zbm01dTdPYzFxekJHN2Q4S0tVZXRhd1ZhSUdONWltRjd2bXY1VFdSYWZINmgvQm9PQnNpZEJzaU9OdUF1YUhzak9OWVVvQ2x0bm51RmJYOXd5ZXNoRVRLOFUvUjJTTlNPcFk3UjBjNnFUOHVzNXlLRHV4VS94ZEQ2VmFLSVEzMDhpdE5VQlRSYkt6ZGRhdjNrUS90ZjZMbk1BTXlJNFpXbStORUZKZUE5MWNxeVRBUzVwT2tWYktUM2JxaTI2S1RvN3JCelhFMGN3Z2FaS2RDRFUwU1VBNzRjNkVxSkt2SVdyN0hGRUU0WEU0b0xWMEIzdFM2NmtzMk5DalpvZlNsSWRVMXpWTVRMTHB4Qkt3NGMzTTlLZmhhczB5Ukd0Y2tBdWVyc2xkMFIvMkNCWVJlRG1OcjB0ZWJsVU5uNCtLV0VvZEVDMHNwWHozbTZGcUwvV0krV201WExQUGZFUnRYUEdCd3lNWXdIWlVwZzBtcyswRUFDbDA1R3VaTTJiQy92azNLNnJvTUNHQjNpd1RWRDZVUDFpbktXZlV1bmJJYkduVGRQc1Bvb2taM2JJc2RYSXpTRGxEWVN3M0RPUEQrM1ZzZDMrd2NLQmpHZkNhR1pBa0NUT3kiLCJtYWMiOiJiZGI3ZGRlNTc3NWI1ZWYzYTY0ZThhNzU3NjAzMWE2NTk4M2JjYjhlMDU0ZmJhYjA5ZmExM2JkNGI3MWRiMTA1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFjU2N6R05JT2hRS3J3cHpjeE9neGc9PSIsInZhbHVlIjoiSUoweGJQTys1aUFFNmlxakFCZ05rU2FnVlBzR3dCeEtYeGxLbE9UMGlZckFwWGlaSHFrNjgyc2dseVJ2aUZNanlNTHJ5TmV1OEsxakprK1FWcllGRTFlQ1p5Ymp3OHE4aThRekJkdWwzUElyU24yZytkYlB4MjhiWjZIbkQ3N3MzMnFQbytDazVCMkpxRDZuM2VUZWFTTU1TdDVtY2JqaTFvZE5RUE4xOStkTVN3UVE2VUNZdWE1VWxaMjNxYmw5TzZ4WHdkZ0pTYjNzVTlxeTl2bTJCUFhEN0ZMd0IwWmpqTmhPU3hudThCdjFaTXk4KzhzSFBDTUNRRXRUYUVIVkhrdWxxK2hsOHNMdHJWSXZCRW51TUUyZjc3ZU1hWnR6cTZTdzNGWkFHeWJ1SEZGUTRLMW1YMWd5WGpWUmFRYVZ3MC9KWTFrR3haN2dKUitMTkU4aGdabHNVU0E3K2pFQjhBZzQvVEpBTWJpTktDYnNsTDFFLzV0NjEzYWpmK0Q3dmg3bzhTY3VLU0lqUTBPMUIvVzducHh2ZVpFWkJPZGF0MXVaLzdSMGhqRG5JbjFUTjU4SzBtU1FHeW5Xc3JkSXY1NjJhUlZrSGJmYmpoWWxNVUE4dlFjSFV5cUtpa1E4UjQ2ZmlqaWVnTUVxVElkN1VSMUdaM3FqOEhDZThhbGMiLCJtYWMiOiIyMTUzNWNhMzFkNzkwZjRmOTYzOGRlOTczM2QzZGExM2ViNzQ2MDQ4NzNjMDBmNTY5YWUzMWYxNGRjZTdlOWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687779744\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1291948236 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291948236\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-53223186 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlYbmNXRS9iejBJZmZydCtUbnJBVkE9PSIsInZhbHVlIjoieFhKaGhISW9mbUtndGN4a3UzNXdvTjNTb1BlNEpoMDRPWUNxc2g1NXdVWnJ5ZVlyNWFFYXdiSXNlT1lhVFRHVFE4RDFSczFDL0w0UE9ha1ZQN3Z5UVR6VTJwU0d0WXRodm1HSjg3bVJwbWkzdGNCdC9LVjBMZ1VpRENPY3Nlc000QlNpSk5BR1dhTUFyQml3RHYzTEhFYjZFcjNpVFY3Q3dzQTc4QXNIRVYzd3VYbTMxRTRyWHVPSUlSeDJmRjN5STNzSmk3bEUwUjZ6bVhnMjV6Vm1ZOTUyYVZqTkJrUFdMbkJ6c3Nlazh2TC8xRm1Kd2pXYWo5WTByTi9sTXVqVGorNlQzcGwyNWVKaHNWK2FhbnVJdGFxcEVHRE5tWm5FaWRlaFFUdUc5WkdLSmJrcFhJUjdtUEZpWHNuUGhhL0VJMEMyQ3l2aXk5L3BoQjc1V0cxRThEZkQ4VThibG01OEhGakJDTksxNXhWMDBhU3Nkejk3NHJ3MGU0dUtNZzBmVlpSZVBLYTVCNmNnY2V1TjhuRHdLQzZGUWZCTlhSME1rU2ZXWHkxZ0ZKOUJNT1VhNVZJMXBpZjdySU9OdHFCbGRqZWJ1N0cyUkdLS3g5WXREU2tYM3NhUWFIVmt3UVVGSlhNd2lrS3JCeXAxd1AwMHlzTklzQ0I1VjVuM1NkMmIiLCJtYWMiOiIwMzg2ODIyOTIyZmFjOThkYmZiNTljODg3ZmIzMmIyZGQ5OTc4OWNlODA5ODQyZWM0ZGQxNzEyYTVkYzkyMzJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik83UFNLZmZsQ29RUlVRV295Y1NLeVE9PSIsInZhbHVlIjoiTW53dm81VXBKdmFmRkhkUTVkYTd0d3dDWCttOU9mZnhxRXRpTkZqYUV3M3AyMVFUNis5WTRCTUtaSUNTSDB1NkRWSytibnBTYmkxRUxrb3ZMQ0JBQ0xVT291WmdrUFlzUUFSSmxGN2lRTjBhaWxxNm13VGhBdS9PbTFSMWtrUDZkTDkzTHBSRThHdEJ3VGNlSlJvYmZ2elozNnNPNnNPeEZ5L1cwc0tLMkZSSlN3dUwvUWJ2anRwbVhzV0I4K0dhbUxyc2d2VDRKV2JSM3ZHaVYrZzYwNEk2eU5zUlM4VUNPQm1pY29CZlNrZitOSnNGenJsdWo0cDJmVWovOUNPZThpMGNDZmoralcrcjRKeWpQTVd5a3lsOWc4aGlPempOOWFvKzdsaVRaNGcxeGpTKzhWd29raFpZVWxFLzhtanlrKytxU3l3elVJL0RVYzhzdGthbS91Q1doV1FUdFRaRm0vUTdITVh2eXBSbGV4SEZMbjl6cmk5enJZbjJxU3FWeFRreDNhd3Bpa3A3dml4a2JZZWFtWXZVVzhkUjlGeHhDRmprck56KzhCWldjdEF1RTZyam1YU0FNZ0VhL2ZrTVNFMjBkeEwzOG5iTUN1SDRmL0c3N01SYi8yd0s4bEhUY1pQU25tVTJYTnVOWStBTGRXSS9QNElJci9pZGZjc1ciLCJtYWMiOiJjNjY0NjQzNDlmNDEwNTgyN2E4YWE1YWQzNTI4ZjQ0NTUxOWNkNDExMjY1Y2FlNDk4OGYzNmZkNGJkYjdiZmRkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlYbmNXRS9iejBJZmZydCtUbnJBVkE9PSIsInZhbHVlIjoieFhKaGhISW9mbUtndGN4a3UzNXdvTjNTb1BlNEpoMDRPWUNxc2g1NXdVWnJ5ZVlyNWFFYXdiSXNlT1lhVFRHVFE4RDFSczFDL0w0UE9ha1ZQN3Z5UVR6VTJwU0d0WXRodm1HSjg3bVJwbWkzdGNCdC9LVjBMZ1VpRENPY3Nlc000QlNpSk5BR1dhTUFyQml3RHYzTEhFYjZFcjNpVFY3Q3dzQTc4QXNIRVYzd3VYbTMxRTRyWHVPSUlSeDJmRjN5STNzSmk3bEUwUjZ6bVhnMjV6Vm1ZOTUyYVZqTkJrUFdMbkJ6c3Nlazh2TC8xRm1Kd2pXYWo5WTByTi9sTXVqVGorNlQzcGwyNWVKaHNWK2FhbnVJdGFxcEVHRE5tWm5FaWRlaFFUdUc5WkdLSmJrcFhJUjdtUEZpWHNuUGhhL0VJMEMyQ3l2aXk5L3BoQjc1V0cxRThEZkQ4VThibG01OEhGakJDTksxNXhWMDBhU3Nkejk3NHJ3MGU0dUtNZzBmVlpSZVBLYTVCNmNnY2V1TjhuRHdLQzZGUWZCTlhSME1rU2ZXWHkxZ0ZKOUJNT1VhNVZJMXBpZjdySU9OdHFCbGRqZWJ1N0cyUkdLS3g5WXREU2tYM3NhUWFIVmt3UVVGSlhNd2lrS3JCeXAxd1AwMHlzTklzQ0I1VjVuM1NkMmIiLCJtYWMiOiIwMzg2ODIyOTIyZmFjOThkYmZiNTljODg3ZmIzMmIyZGQ5OTc4OWNlODA5ODQyZWM0ZGQxNzEyYTVkYzkyMzJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik83UFNLZmZsQ29RUlVRV295Y1NLeVE9PSIsInZhbHVlIjoiTW53dm81VXBKdmFmRkhkUTVkYTd0d3dDWCttOU9mZnhxRXRpTkZqYUV3M3AyMVFUNis5WTRCTUtaSUNTSDB1NkRWSytibnBTYmkxRUxrb3ZMQ0JBQ0xVT291WmdrUFlzUUFSSmxGN2lRTjBhaWxxNm13VGhBdS9PbTFSMWtrUDZkTDkzTHBSRThHdEJ3VGNlSlJvYmZ2elozNnNPNnNPeEZ5L1cwc0tLMkZSSlN3dUwvUWJ2anRwbVhzV0I4K0dhbUxyc2d2VDRKV2JSM3ZHaVYrZzYwNEk2eU5zUlM4VUNPQm1pY29CZlNrZitOSnNGenJsdWo0cDJmVWovOUNPZThpMGNDZmoralcrcjRKeWpQTVd5a3lsOWc4aGlPempOOWFvKzdsaVRaNGcxeGpTKzhWd29raFpZVWxFLzhtanlrKytxU3l3elVJL0RVYzhzdGthbS91Q1doV1FUdFRaRm0vUTdITVh2eXBSbGV4SEZMbjl6cmk5enJZbjJxU3FWeFRreDNhd3Bpa3A3dml4a2JZZWFtWXZVVzhkUjlGeHhDRmprck56KzhCWldjdEF1RTZyam1YU0FNZ0VhL2ZrTVNFMjBkeEwzOG5iTUN1SDRmL0c3N01SYi8yd0s4bEhUY1pQU25tVTJYTnVOWStBTGRXSS9QNElJci9pZGZjc1ciLCJtYWMiOiJjNjY0NjQzNDlmNDEwNTgyN2E4YWE1YWQzNTI4ZjQ0NTUxOWNkNDExMjY1Y2FlNDk4OGYzNmZkNGJkYjdiZmRkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53223186\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2054218753 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Receipt Voucher has been Accepted successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054218753\", {\"maxDepth\":0})</script>\n"}}