{"__meta": {"id": "X15e601fc2998e78e5efe94e053d40182", "datetime": "2025-06-08 12:54:30", "utime": **********.848738, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.300061, "end": **********.848772, "duration": 1.****************, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": **********.300061, "relative_start": 0, "end": **********.656701, "relative_end": **********.656701, "duration": 1.***************, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.656724, "relative_start": 1.****************, "end": **********.848776, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01372, "accumulated_duration_str": "13.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.757846, "duration": 0.01051, "duration_str": "10.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.603}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.796018, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.603, "width_percent": 10.35}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.825402, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 86.953, "width_percent": 13.047}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387267916%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRJVEpQanBsb1Z1ajBOcEwzOU1wRFE9PSIsInZhbHVlIjoiNlJCY0hHc0ppVm4wOUREaVVlNy9DVHkweTVJRTVYZmJHVmYvY2NXcjNxV0N4YUN3Z3JXWUwvR1lJcDM0RDB0TnJERW9ZZ2xRQlNMMUdMaXhFVTc1Rm5oVHBEVllQeU5YVzhYc1J5empyZmtORGlweWY5dWNzVjZZSVErRzJKNVBDRUVYY080WWVJcDExZ1BucForUXJVaFNNdDRIdHR5cHFKVGZiRkxDQjZhUFZwZmdIMUgzRWxQbDhGZDg1YXZYOWhrSHU3eENOMXZIc1lKWlJpRDM3V3lsdjNpcDRmcXl2S2ZYTys1NklXQkFHbDR4a3AvRzhJVXE4ZmdpTDJzOE5kcWhpSW8xOS9nTHNmc0RoR1p6YnRWTS9VaENpNG8rWWpySG44czJyZWpEUmJFRWI5RHRKaFpHdzhTVHBLOHJWUDlXL0l4OFNTYW5uOHgrdHJkbk55cjh3MThQcUJZczhyVmhKQjRmTDFkYWIxRkdCaURlYnVLU295ZTZaK09vbG82SzMva2RiNHpGRnZocXkyRGc4WVlsQVdwWlpQeGxEdGV6SDFDTzhjdlFBQi9xZFBpbUZ0akdPaVh5aGdxQ3ZHRnBWbFlOWVFIdUlHREpVVUJ5cjZXOGpHRjdyZU5DSGt5QUZtN0NjUHpXVkRzdzFiS3cvbllCUmFDM3Nab2wiLCJtYWMiOiIyOTljMDc1MGE0MzNjYWM4NzA1YTU0NzliYmYxNzI4YjdjOTMyNTA5Yzg2YTEzOWFkM2E2YmRiOTBjZDgzM2MxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhBYmFwRGQ1aUExSUlWUGdCblZjckE9PSIsInZhbHVlIjoiQVhpNzNOeGtlQ3B1bFVTazRKR2tPQWNSWngxa0M2ZWtMNkM2ZkJQSnR1SmY0WkpDbWY5M1kyS0xHdURZRkVmenNVeWF0Ti9QUElmMzJTVXNjMnBCd1dYTjdLQUlPODFtV09FaFdGU1dFOWZCYUc0SlhENmZYNEZScmo2OFlrUm1jYTdOUzlaVEpZdVIwMzhJWkNaSkkzenhEZVR2MC9zTmpIUFlIQlYxNDdMWm1sVlMxRjRxb1pzTG16T21UQTlrU01lS3U0aTF6dHhRQTBjOUZzdHZiOERCYTFhZkg0ZkhMNUk0YXZxc2RGcjFzTzJrZ3BFRTVMWm1wVDFOZmNVenlkOTlQbDZjZTVXNnJaY0JkNXNudXBJQUJTenZ2Z2dnZ3VVZmt3eERFK0pIdWxMbmp6OEI0YXJtVHhzb3EwN2NxaGtTSVdLcjZxTVNna0lvaHp6NGpUZmZkT0Y1ZUE4aTROaUh0YXc2N2ppcTJ0Q2ZLZ0VlZXZna3FmNzVGK3pLTEJaTUo3N20rTzIxTno5ZU43cUt2ZWsxeUxGUUd4L2oyaFJybHZ1OEw0UVM3djl6R2FFbGhianNMUEF2MGdCYTIySVpzT2NKb0J5SlRaUEw3MUJJalBuQnFzRG5uQ0RzU0dXVmxBR3lvSVFIRjc0YmhsQ2VEb3RhSXNJMmJ6UlMiLCJtYWMiOiI4YTJkOWU5NmFmN2IwNDk4MTcwZGRkMjQzMGUyODk3NGEzNjhmOTQ3ZWE3ZjI5ZmVlZjYxZGNkNjNlM2ZkMDY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-270310757 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270310757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1371193921 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJmU0YzbjJMeUIvOFQxcGh1alZHYkE9PSIsInZhbHVlIjoiU3VuTUtOYWNjdkhjdkpOcy9SdElIMlJ1Zm9yNVJ6bENBcDZFVWtqdUdsQjJGamEzZktTV3VIekQ0d1Eya1RIYm1XVWxmQTgwcTAvc0FWSEdhdkxOdkFmWVEya2VEa25MMFk4b3liOG40VjBuNGwwbGQ4bE1ZbjZOU0tzam92L3hmaCtLUGpaT1JTUGNENUZIVWVFMzBWS0pQd0JyY1dkNDRoemVKbGdzWVhvRjdNdnV5MW0vdlplQkwrSjVkbGZIa3dWMkpWMmlvcWwrbHVHTHNrOWp5T2hheVZ0LzRkeHBZR0xYSnUvVzhaZzBwTVZOUHJhQlhVcGp0U1Z0WHBrQlhpZEJtN2tIMUtIMkEweWFjQXRmZGlrUElpWFNnSU82eTNyenVQMXNXY2ZDbTNYOW1pNnN3SEtzRVdyVFVDSW5ZYjJoRnVPNnV6cVdnUjZkb1E1NUV1VGs3M0xWdi9SL0I0cnZ2cU1VOGtxRVNua0Y5eXR0YXFlRFZKam1IdER5N1FBM1cyeGxLa1c2dlNWelg5Ti9GZllzOTZUQTlueVJYTVlYYmZlWnJZWmlyL0RBTTNiMEJEZWF2NGErdjE1OGJBMytYeGxqRmhUSlNPRVVLa2tEam94ZjZqNXYxRFNtaFVScVBMelE0YVJiaStsTmRBSFdkSForNUdEaGVMem4iLCJtYWMiOiI0YmNhYzNkMDlkNjViMGU1ZTcxZTA2MzAyNjdmYzFhNzY5ZDE4MGMwODNjOWQ0OGU4MzIzMzFkYWI3ZDdjNzhiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjVRbXE1aXZOMkt2cFkxRjR4cG5Lbmc9PSIsInZhbHVlIjoiZFpPS1BycXdGYytEK05WNWljOEt5V1MxY3hhQnN5Qmpmb3FaN3lLVnB1TEtWZmpFRHROVUNtWDU0SmlEc01FcHpRdFpqeHJDYTdVZGZXbWJxOFJyaVZNbEVaYkkrelZ4YmdQNFZnQmlCU0JndnlRQVVWbEtuTjMzcVl6dXAydyttbUlrR0c4d1VpVUhMNHp3cFFsZ2hsU0lBRWt2bzhiNW95d1F6bTlwbjk1Z253MmdHMElnL2RXYnNTbUtLcHpWdGVkOEk1RkoxaVhEQlhVRVpwZFNKbmFPT2VSaCt3ejhmcm9wQ1NxUS9iaFk3ditmYmptZnh3bFJyMEVtM29DQVdtUitVODE4WXFjc3ZDVG13bnF3ZDlrRlNodCtCbmxWOTJJMHF1WFByamUvVFdGNzlHQWgzNndUTkU5WnJmb1Joc1pKanI3VE5hMUVHdHVGdHVRM3Q0UDhDcjFIclF4cUFrT3k2dDA4Sjg0VEJGd1c0NVF0WThTVEpSRHJTMVI1WkhCZS82akpLWTIyNTVXRStSczIyOEM1RUZacFN2SXIvbW8zbUtkRU44WGpWZk5KYjViUW9IODEwQ25iNWVVVmRsc20yTDhLc3NvYUpTdDJ5Z1A2ZThHci9oaUpsbHE1R0lMcUJ0WGRTcWxESzVWellXUkJ6Y294M2I3N2UxRFgiLCJtYWMiOiI2Njg1OTRkNzg1MGVjMTA0NGNhZmY1NWJlMzZiMjEzZDgzMDE5MDA5ODUzMzQ2MWI4OTFjY2JmMGIzOWZkYTFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJmU0YzbjJMeUIvOFQxcGh1alZHYkE9PSIsInZhbHVlIjoiU3VuTUtOYWNjdkhjdkpOcy9SdElIMlJ1Zm9yNVJ6bENBcDZFVWtqdUdsQjJGamEzZktTV3VIekQ0d1Eya1RIYm1XVWxmQTgwcTAvc0FWSEdhdkxOdkFmWVEya2VEa25MMFk4b3liOG40VjBuNGwwbGQ4bE1ZbjZOU0tzam92L3hmaCtLUGpaT1JTUGNENUZIVWVFMzBWS0pQd0JyY1dkNDRoemVKbGdzWVhvRjdNdnV5MW0vdlplQkwrSjVkbGZIa3dWMkpWMmlvcWwrbHVHTHNrOWp5T2hheVZ0LzRkeHBZR0xYSnUvVzhaZzBwTVZOUHJhQlhVcGp0U1Z0WHBrQlhpZEJtN2tIMUtIMkEweWFjQXRmZGlrUElpWFNnSU82eTNyenVQMXNXY2ZDbTNYOW1pNnN3SEtzRVdyVFVDSW5ZYjJoRnVPNnV6cVdnUjZkb1E1NUV1VGs3M0xWdi9SL0I0cnZ2cU1VOGtxRVNua0Y5eXR0YXFlRFZKam1IdER5N1FBM1cyeGxLa1c2dlNWelg5Ti9GZllzOTZUQTlueVJYTVlYYmZlWnJZWmlyL0RBTTNiMEJEZWF2NGErdjE1OGJBMytYeGxqRmhUSlNPRVVLa2tEam94ZjZqNXYxRFNtaFVScVBMelE0YVJiaStsTmRBSFdkSForNUdEaGVMem4iLCJtYWMiOiI0YmNhYzNkMDlkNjViMGU1ZTcxZTA2MzAyNjdmYzFhNzY5ZDE4MGMwODNjOWQ0OGU4MzIzMzFkYWI3ZDdjNzhiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjVRbXE1aXZOMkt2cFkxRjR4cG5Lbmc9PSIsInZhbHVlIjoiZFpPS1BycXdGYytEK05WNWljOEt5V1MxY3hhQnN5Qmpmb3FaN3lLVnB1TEtWZmpFRHROVUNtWDU0SmlEc01FcHpRdFpqeHJDYTdVZGZXbWJxOFJyaVZNbEVaYkkrelZ4YmdQNFZnQmlCU0JndnlRQVVWbEtuTjMzcVl6dXAydyttbUlrR0c4d1VpVUhMNHp3cFFsZ2hsU0lBRWt2bzhiNW95d1F6bTlwbjk1Z253MmdHMElnL2RXYnNTbUtLcHpWdGVkOEk1RkoxaVhEQlhVRVpwZFNKbmFPT2VSaCt3ejhmcm9wQ1NxUS9iaFk3ditmYmptZnh3bFJyMEVtM29DQVdtUitVODE4WXFjc3ZDVG13bnF3ZDlrRlNodCtCbmxWOTJJMHF1WFByamUvVFdGNzlHQWgzNndUTkU5WnJmb1Joc1pKanI3VE5hMUVHdHVGdHVRM3Q0UDhDcjFIclF4cUFrT3k2dDA4Sjg0VEJGd1c0NVF0WThTVEpSRHJTMVI1WkhCZS82akpLWTIyNTVXRStSczIyOEM1RUZacFN2SXIvbW8zbUtkRU44WGpWZk5KYjViUW9IODEwQ25iNWVVVmRsc20yTDhLc3NvYUpTdDJ5Z1A2ZThHci9oaUpsbHE1R0lMcUJ0WGRTcWxESzVWellXUkJ6Y294M2I3N2UxRFgiLCJtYWMiOiI2Njg1OTRkNzg1MGVjMTA0NGNhZmY1NWJlMzZiMjEzZDgzMDE5MDA5ODUzMzQ2MWI4OTFjY2JmMGIzOWZkYTFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1371193921\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1806335080 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806335080\", {\"maxDepth\":0})</script>\n"}}