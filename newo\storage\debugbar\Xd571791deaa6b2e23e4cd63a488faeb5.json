{"__meta": {"id": "Xd571791deaa6b2e23e4cd63a488faeb5", "datetime": "2025-06-08 13:26:48", "utime": **********.943391, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389207.630134, "end": **********.943423, "duration": 1.313288927078247, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749389207.630134, "relative_start": 0, "end": **********.692462, "relative_end": **********.692462, "duration": 1.0623278617858887, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.692481, "relative_start": 1.0623469352722168, "end": **********.943427, "relative_end": 4.0531158447265625e-06, "duration": 0.250946044921875, "duration_str": "251ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131096, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.012199999999999999, "accumulated_duration_str": "12.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.805593, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 37.213}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.835945, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 37.213, "width_percent": 9.098}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.887388, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 46.311, "width_percent": 14.344}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8939378, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 60.656, "width_percent": 13.443}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.909455, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 74.098, "width_percent": 16.885}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.920729, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 90.984, "width_percent": 9.016}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-738409184 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738409184\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.906838, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1175364741 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1175364741\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1221226526 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1221226526\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2126355882 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2126355882\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1940773896 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJGVFllUmNxRVZtSExpQ203Ti9LS0E9PSIsInZhbHVlIjoiNkltUGpRTk9ZOXlVZ3R0OGZHU3ZCVjJ6OUcwWkZBR1c3N1NETURUNlp5RlRCcHJlbmE2d05uUTVESzhCSDhTOVJkbzhnSGNoamkrVzg0UFl4eGJ4Y3poa3h5TUtQZ3ZRbWdOaUZURXljdmcxbjBvVVNBU0FnZ1BOTnkyYVg3RzNGRzZUenNOdkN4cHJNeWY3Z01Ud2VaSmNYTUlnK0k2MnRqeGx4M3BMclRBN1VINzNzam1aaUZwazZjQWpvVHJFL3lJeG5OOS9oUHZGbnVkL0ZkZlFnbWxBWFVMd05lV3FHTUxram5VSmwxeGp2bmtzYVk2MHZBTmVMcU9OcTlrWVZIalNEd3BtajdZK1FqVFNRbzZjQmpIbFdySFV5cFVkNTlBdEt4TlRJYUV6Y0pBeVdwYW5nb0lJWldvOWlxRGx6VG5ZUWJsRjRnVTF2dUhwNC9xQmdqdE80bDJOOFlpUitMVU1VeEQ1NVdJZGxXTUxGZjJ5RU9Ca25mMXlERVhPdlJ5RlRHNyt5Qk8wNXNUR3ExNXArNU9qQ2ZCNVVaYm8xc3o3TE5DdGwySitaOXkrVnpseW10SW5SeGlzN0hlRGhFaXJsQy8raENLZS80di9FVXZ4VnV1emNrOGE2L1M3YXlqUWJON1B6ZmJSZDk1MTV2YTZnT1I4YmdMSlBPcHAiLCJtYWMiOiI0MWM5ZDE1YTM2MjBmZTczMzk3Zjg4MzM2OTllZjMzZGMzNGJkZjNmZDRjNWNiYzg0Y2ExYTAzZDI4MGExMzY2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllsanRYeDZLZE4zbGdiS2VxcmVTbVE9PSIsInZhbHVlIjoiRDRBVjNtMVZrZURpd0lDQmQveTNOZXptZVgzNnZCdGIxckUxOXdqdmdPWjVMOEtwU010cWhOOGV4a25KRTh1WER3aERQMjJrY2Y3ZXNWY1RjR3h5TXBuWjI0VVhwUGU4Szc5M1pLZVZFMHZQeUNjcFd0QWdpRlYrdnJJVXUrQnI2dkowMHdEcHJZMnRyOVVQa0dwdkZDN1BMaTQ2NHRLRDRObjdoSWVOdHAzSWJLKzJCQ21ESGZmY0JTUmJZbnd0MGRidERZTGt1KzFhRzlVZWYrVkRuZHBYemVWZkZHWnNMWEl0bGszakdvWXppczJPNDdJN2JJU1J1bFdzalIwZWg0aGRvMmttOXNtSW90a1hhc0RENVV6V09RYlQ5NnFLTUttR0k0ayttVUM2dVlpOXNDbGJDOVN0dXpsSWlQUkdnTHdTdGp2dUpBNzRwVmNuc3Jncy9YRFdOUkhBTHJkNVpxTXd3cnpLUzd2ZVZaZnVlbXJ1eDhZRkEyOUhQeEZ4TUFQMzN0cVlTUFd3SVJsT3B0UFRtQlliV0Y4R2IydFJ6WVlORjRDZCt1QUk2WGFYVElZdTdRK1FWdmFxV21ScVhSK1VCc0ZVTTRsTXowYkVaSllrb2w2QURtM09tQlpHc1pnT1RDeE5PdzRRenVzT2tuN0F2YVBqUG9LclA0M0MiLCJtYWMiOiJiNWFmZWE5ODc0NjE3MmM5NTRhNzAyNGJjM2MwMjJmODIxZmZlZDM0ODIyYjE0NzUyZDkwODA4NmE2ZjA2ZTI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940773896\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-994250616 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994250616\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1583474219 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:26:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImthQk9KV3lTbE00QkFCMFdFWDdZMUE9PSIsInZhbHVlIjoia1IxblZCVVo0ZytYRGs3c3pVSEtmUHJOTEhHanNIZ0s1VERWWWZBeEpkQjM1NmdVM0QrTU5BVG9Ic0tpSklYWU1xVlFnRGpqMkZZeVowS2wxc0pwWWsrWEp4TjVPSUx6R1ZaSGFQclpFcWswTjdTZEtJSi9nd2duUEpLTWgvYXZoRUpzeXdZM0QzZmc4WlE5Nis3UmNOd2NaR0MwUnBQTzNQSmM2cnFkNmUvYXk5ODZWb0ZUYkJUQ3Nva0RFQjRyT2szWk5jZVpKV2p5R0hOcld4c1VyNEpyb2NWeWR6TFc1WWszN2RmYTY5dTZwdVpqbjNvU0kxNGp3N1gxMndHMEFlNUdUTkMwZjBCY3FPQ3hxNHRMbmVWVmFYT0xOK0VVUzYzNHprZ3NCLzBXU3dTMXRCRlhTbXpYL1kzQVRuQWZpd2ROL3cvNVhxUWI1ZG9UNkp1TDRnTmlVQjFoODV5Sk1oVFdpekY4cW1WeEFHb0dvcGVBQU1uRFcraTduVGdjbGlNeDduZ0UxM2NndjVOeHh0MERKdGU4VjhFUERZWEJGTWhwUEkyR1Ayd3dmWUg1U2tnR0kreXVPWU5jcUhsczZzb1ZSWVJLcWhPK1pSNkN6WXh2RW1ZVzZCa2ZIcWZ6UU1EaGhlVzU1aHR2UGZ3aFNMSU9CbDljTEZmZzBmS00iLCJtYWMiOiIzYjZkZDY0OWE3OTg4NjEzZTI5MTdhNDRiYTQ3ZTRiNWZlZDU3ZjBiMTY4NGIxOTdhOGI2N2RhNTZlNWU3NWVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikp2VDA2U3FYUVNKSjJGQUpxbERURHc9PSIsInZhbHVlIjoiWnlzeGNydkZpL0ZxQ2czQktPUGxqc21rVE82TkJNbGNSVFdhbDFKSmd4U29jMjVHU3JlaUVBTHlPREJ4ZGRIY1VMbmxJVEw3OWk4K0dtcjVISGtWeHpLK09rdWVEQ084eHZ3aFFlbFZ2QnlBM3pRbThGd0RsVlVmMGxjMFlzOGVUMCtyTlUvTC9WNkkrb2wvRFR0Q0dzeVJaR01KL0FXWjVYcDlqcTVRdUhHQ3FqOUcvbXZWQnNCZkVoWEsreE94d2tIYWdMbFFnZzhXMHJ5NDBoaVFEUUZiRUtoSndRSFN3UmhwUlcxMTkwbmY2dFU3VXhVKzF4MUZNQ0t1eHlubnFiM0pEakJRc2RXaERYRE5FdHdDTld3SDY2UFZxREtPK0xMMTF2UXR2b3VOcVVYYXZyL0hiZ1NzRTNidDdDZllmSVF1ZVFXNU9GWXFwdmZhS0FXQ293NUF6Tm5TWUtDdXhtR3NnS25lbEwwaVM5U0JGT25NSVF6WGZrUkdZcklURVg0VXBiYjMxYU00bEMzUUxxS2NSdkl4OU1vNWJNMURFSCs3OFVmNzFSSHU3amVRYUUvK21ocmovNDcxTStCeHBXd1BjNHRLbnZKVVlQSlcrOWxBeThsVzZtdXA3NC9ZTm1KMEdQcEYvUmppMHg1eEJJN3BLQnVyWERTVy8wTHoiLCJtYWMiOiIzMmJhZjNlNGY1MjNmY2RmYzVjNGQzMTQ0OTkyM2ZjOTVkNTQ5MjU2ZTQ4YjZhMmY2NzA4MDM2MjhmYjRhMDQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImthQk9KV3lTbE00QkFCMFdFWDdZMUE9PSIsInZhbHVlIjoia1IxblZCVVo0ZytYRGs3c3pVSEtmUHJOTEhHanNIZ0s1VERWWWZBeEpkQjM1NmdVM0QrTU5BVG9Ic0tpSklYWU1xVlFnRGpqMkZZeVowS2wxc0pwWWsrWEp4TjVPSUx6R1ZaSGFQclpFcWswTjdTZEtJSi9nd2duUEpLTWgvYXZoRUpzeXdZM0QzZmc4WlE5Nis3UmNOd2NaR0MwUnBQTzNQSmM2cnFkNmUvYXk5ODZWb0ZUYkJUQ3Nva0RFQjRyT2szWk5jZVpKV2p5R0hOcld4c1VyNEpyb2NWeWR6TFc1WWszN2RmYTY5dTZwdVpqbjNvU0kxNGp3N1gxMndHMEFlNUdUTkMwZjBCY3FPQ3hxNHRMbmVWVmFYT0xOK0VVUzYzNHprZ3NCLzBXU3dTMXRCRlhTbXpYL1kzQVRuQWZpd2ROL3cvNVhxUWI1ZG9UNkp1TDRnTmlVQjFoODV5Sk1oVFdpekY4cW1WeEFHb0dvcGVBQU1uRFcraTduVGdjbGlNeDduZ0UxM2NndjVOeHh0MERKdGU4VjhFUERZWEJGTWhwUEkyR1Ayd3dmWUg1U2tnR0kreXVPWU5jcUhsczZzb1ZSWVJLcWhPK1pSNkN6WXh2RW1ZVzZCa2ZIcWZ6UU1EaGhlVzU1aHR2UGZ3aFNMSU9CbDljTEZmZzBmS00iLCJtYWMiOiIzYjZkZDY0OWE3OTg4NjEzZTI5MTdhNDRiYTQ3ZTRiNWZlZDU3ZjBiMTY4NGIxOTdhOGI2N2RhNTZlNWU3NWVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikp2VDA2U3FYUVNKSjJGQUpxbERURHc9PSIsInZhbHVlIjoiWnlzeGNydkZpL0ZxQ2czQktPUGxqc21rVE82TkJNbGNSVFdhbDFKSmd4U29jMjVHU3JlaUVBTHlPREJ4ZGRIY1VMbmxJVEw3OWk4K0dtcjVISGtWeHpLK09rdWVEQ084eHZ3aFFlbFZ2QnlBM3pRbThGd0RsVlVmMGxjMFlzOGVUMCtyTlUvTC9WNkkrb2wvRFR0Q0dzeVJaR01KL0FXWjVYcDlqcTVRdUhHQ3FqOUcvbXZWQnNCZkVoWEsreE94d2tIYWdMbFFnZzhXMHJ5NDBoaVFEUUZiRUtoSndRSFN3UmhwUlcxMTkwbmY2dFU3VXhVKzF4MUZNQ0t1eHlubnFiM0pEakJRc2RXaERYRE5FdHdDTld3SDY2UFZxREtPK0xMMTF2UXR2b3VOcVVYYXZyL0hiZ1NzRTNidDdDZllmSVF1ZVFXNU9GWXFwdmZhS0FXQ293NUF6Tm5TWUtDdXhtR3NnS25lbEwwaVM5U0JGT25NSVF6WGZrUkdZcklURVg0VXBiYjMxYU00bEMzUUxxS2NSdkl4OU1vNWJNMURFSCs3OFVmNzFSSHU3amVRYUUvK21ocmovNDcxTStCeHBXd1BjNHRLbnZKVVlQSlcrOWxBeThsVzZtdXA3NC9ZTm1KMEdQcEYvUmppMHg1eEJJN3BLQnVyWERTVy8wTHoiLCJtYWMiOiIzMmJhZjNlNGY1MjNmY2RmYzVjNGQzMTQ0OTkyM2ZjOTVkNTQ5MjU2ZTQ4YjZhMmY2NzA4MDM2MjhmYjRhMDQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583474219\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-76624459 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76624459\", {\"maxDepth\":0})</script>\n"}}