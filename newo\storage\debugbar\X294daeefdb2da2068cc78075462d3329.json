{"__meta": {"id": "X294daeefdb2da2068cc78075462d3329", "datetime": "2025-06-08 13:28:00", "utime": **********.181625, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389278.912757, "end": **********.181657, "duration": 1.2689001560211182, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749389278.912757, "relative_start": 0, "end": **********.039721, "relative_end": **********.039721, "duration": 1.1269640922546387, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.039742, "relative_start": 1.1269850730895996, "end": **********.18166, "relative_end": 2.86102294921875e-06, "duration": 0.14191794395446777, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45186992, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01835, "accumulated_duration_str": "18.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.115241, "duration": 0.01589, "duration_str": "15.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.594}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1549392, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.594, "width_percent": 6.322}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1639838, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 92.916, "width_percent": 7.084}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1591259491 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1591259491\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1087225676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1087225676\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-550168932 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRpaXIxRHV3Q21FZEFqSHdaSW5MK2c9PSIsInZhbHVlIjoibXY3Ky9vUjVuelQ4dmhScTNqckpkZDZQTERWMFRUbHVoWFd5MkgxL1paYnI4WVJXaXVqV1VIV1RoTkFDS3pqanUvSDJVaDVVNU9WY01TOUNJU1NDQ2JlZHBRY2JRNEJpM2JxOTU1WUdqSkIwc0RtcVExcmw4d0VhV0dyeW5NTDJBNElPazkrL29McjlSZGJVMzB2dmd1VHA4V2MzSUN0NllKQjl6ZEo4b3ovYmhQMDVITTJFbWpDM3lCSDlpR25mU1gzUUozeHlxYlZUWmdxWHEySmxiUlJFZGtqNllxSS96eDl3aFU3OHhPZVI1MFhkVG1nTGRLMlRHMzdhODZ2WWJmalZLU044cWJOMnJPRVFLdVJxd0RQVDJFdGxCa0d4OWFWNzhBTm42VXExTDFjT1hwTVBhYXBhemdBTW4yMThaODhXd0wrTm1Nd2VITVUrNFkvbUE2OTZmd2twbGVpOTBPUFZqNFFwM3hkR2ErcTlwaktaQ2J5MCtNN2ltVUYyVFN1S3A1ZFlWSzRoUG9qZE52bzB5NklwQXR5RlAzSjRZYjU3aHVvcW4zeE4vM1QwbkJiQzRhL0xkWEZvdWdYQ3FNQUxWNXIyUGpKTnpveDlPZ1IrUHIrMkNiVUkybDV0S2ZpdTNuMzNEa0FUSWF5UTJmYVpNN09lSUFHTWo2UTkiLCJtYWMiOiI5Y2UyMmZmZjM0MGVlNGFlMjUwYzk4NzBjNDA2OTkzODgwOWQ5ODRlMGZhOThkY2Q5ZjdhNWNiOTMyNzc4YzNhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1PNkdHaUFJTng2S0o2YjRDVi9SSGc9PSIsInZhbHVlIjoiN2ozdHhPVFI0d3FXajNFaGhEUjNUYm9NUzg2YkVkaGQ4em84ZE1YK3RnQ1FzTEZOL29Qa0RudEhhQUU4eEN2aEFVWXNObC9KVlRmUlhSeVdqQzFnQTI3cE5SRTRUK2d2VSt4YVp5bk95VGpSQlRtY2dGM29EaUFlMVhNV1dNNE9CYkJ6YmRZVmhoVzlVdlJHUFh2S3JOTFlFcjJOa3EzLytpK0ZZQ1JFOE9tQWxxcDdtV3pwWUY1dGFGWUVDbTJGUjNJYUwzUTM4dzRBb3huOXFDSG1Rb1E0MWZIQ2phcFFlTW9iOFk2M2RXNjFBZDZSMHlMdEdNQnl0cFYrR2k5VXVVbkNneitKblFQL2Y4NnlGaEtoUW50NFBZZ0U0c0RORHpYdEZmT0RUQjU1U2dOUTZsWGtDWEZveG1BdDRTZVpjTE1UamwzeVY3R1lRcm5kMk1LNnlxUC9RT1dnNkMyVW1pN0Y2UngzWjB0WE03RmNRc01aaTIwRDMzRDVDTjZ2aFVOdHNYMnI4cFV1TTkycC8yUThMayt4eVFoWmExUlNBK3ZwT0RxVmlXZEdDWkQxek9lU2tLcGZVcCtaVk1EaUVFUU9haVhEV2NzazNIcFJDZU14STFSRW1GZWV0RnE0ak14M3BvN0h5bmprUHpVdU5QQmV0SzJQSjgvbEFMOXkiLCJtYWMiOiIyZDg5MjEyNDgyMzZlZjA5MTc0NWM0MWRiNTAzOGQ3ZTlhNzA1N2YzY2E0Y2U1ZTM4MjVjYTNmMzkyMTIwMmM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550168932\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2102427948 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102427948\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1705963039 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:28:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjV0OEtFL3o1R1pPTFVTV0REU3F0L1E9PSIsInZhbHVlIjoiN0x5dGdMNkxmMUlzSUwrZk5XeEI4dlRHUDNBaEc5UEhROGQ3aExlUmVpenpTdFB4MENwMGh5WDJVR3IyQ1hGdzBvek1QamJyd2xuNk5tQVJBSUd1R2JheWdJZlVQMU1NRjkvZVlxZCtMQnROVXg5cjlVUW45SW5xM1RPR3dGRXRHQkVvdThsZ2x5YkJOUzdQcS9RUG9tMFByK3hBRnJCRW5Hc2FndmtSMWlNVnFpWjhoc1RFMy9VdkViYVZnNHlvQjNEVkZKSC9wVGNrckhIVklPNjdTd0V4VHdiUlJ4RjAxNTBtWnJ6K0l5NE9MNE1Sc3V5dlVQRTRndXFCMUxIdDNjRDBZL1AvczZqUHBXaHhuOWx4bVlBN1BSbTF6NUZmakE4blBmemtqTkZ5c0x6bC9DR1JiaUZmckZCUFo5bHB6RDhudHB1UEprQ1hadHQzUDJpWW5QbGg0SGI3ZkRUczdyZTRMM2lHOFpBWlZqL0s4YUVXVGk3dmF0dFB5bEp4bE1FK2Z2UXFLN01XZGQxOFV2RnBpaHFxNzZrdjZGMjMrckczSGNHd1NKL2FhR1I5Q3V3UmZ2Y3k5SjFVYStESjREaGtRUDErem1tVGNhQmsyKzQxMi8xUnZ2WUZybDNTc1c3ek1xeGhLQ2w4OUlEaEJSYnlCTTZUSkNKYndva0QiLCJtYWMiOiI3MDU3YTQyNDAxMzQ2ZmU2ZjU4YzU3OGM1YTM2NzAxNWRhYjQ2MjJlNDllN2U3NmIxODNmZWQzMWUzYWVhOWIyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:28:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IncxSXVBNjhNSG5JY1pORVVzTnV1aWc9PSIsInZhbHVlIjoiREJrTzhnMHUxQmdVNTB1TDN3aWRPeGhZRnp2WmRjbVNveXpKZWs5VnhpSHVJUlJyZTJxd0JxdG9JeU1BempwU0lTSDVpMFpIQW91bHM3L05jK1ZXTWU5bitRMVB0NUJnWW51Y0huVmxkZVUzWmdLU2pCeVpQRTduUHIxbUJYRVNoc3pvNkRmUndGZ0gya202dkdmM2twaEU1S2hRSUJjOEtEQU93S1o4MDJKd0pVbXhTd1NlNzJQZnUwRC9US3pUT05YVlhMSVBKancrZFREVHRSeThHbWRpb0ZEZ1VVZFNuZnB3TkMzWVBpcGdGQ1cyNUtZa2x1YzcwQ0tqT0dYNitkMlhURHcrQzdqWnlqcFU0bEhhUkNEeEFFWGlBZ0ZadDg5eWlncTRzZkJENEROZG1MS0NuaE94eWdZVFdUNkd0d2htVnJxc2JUa1llOW1BdU9XWDVnNGplcHdwZ0gwVXl3TmI3QktXUWZKTG1YbFNSUkdhWDlNbFk0UDBISVBWWUZPYlpJWXVjN09rVEJCejQ1aGhLQmV2bDVFdnhTV1pERUVQd2lRRWl0cklJazJSaUN3dzZKaCtpdGFLa3lVb0xQZitZUUlCYk5aNWpOQWJIbTdXYlRZaFJMNTdaTWtDUE1kbnRoSG0xazhGei9YQTVNOEFVZ1hsS2pneFJCb3MiLCJtYWMiOiJlMDI2MDQ2ODAwNGI0NDdhOThlM2FiMzM2Y2E5ODI1NjM4ZTViY2NjZjI3MGUxNzQyOWNkOWMxMmQxNGIxMDkxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:28:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjV0OEtFL3o1R1pPTFVTV0REU3F0L1E9PSIsInZhbHVlIjoiN0x5dGdMNkxmMUlzSUwrZk5XeEI4dlRHUDNBaEc5UEhROGQ3aExlUmVpenpTdFB4MENwMGh5WDJVR3IyQ1hGdzBvek1QamJyd2xuNk5tQVJBSUd1R2JheWdJZlVQMU1NRjkvZVlxZCtMQnROVXg5cjlVUW45SW5xM1RPR3dGRXRHQkVvdThsZ2x5YkJOUzdQcS9RUG9tMFByK3hBRnJCRW5Hc2FndmtSMWlNVnFpWjhoc1RFMy9VdkViYVZnNHlvQjNEVkZKSC9wVGNrckhIVklPNjdTd0V4VHdiUlJ4RjAxNTBtWnJ6K0l5NE9MNE1Sc3V5dlVQRTRndXFCMUxIdDNjRDBZL1AvczZqUHBXaHhuOWx4bVlBN1BSbTF6NUZmakE4blBmemtqTkZ5c0x6bC9DR1JiaUZmckZCUFo5bHB6RDhudHB1UEprQ1hadHQzUDJpWW5QbGg0SGI3ZkRUczdyZTRMM2lHOFpBWlZqL0s4YUVXVGk3dmF0dFB5bEp4bE1FK2Z2UXFLN01XZGQxOFV2RnBpaHFxNzZrdjZGMjMrckczSGNHd1NKL2FhR1I5Q3V3UmZ2Y3k5SjFVYStESjREaGtRUDErem1tVGNhQmsyKzQxMi8xUnZ2WUZybDNTc1c3ek1xeGhLQ2w4OUlEaEJSYnlCTTZUSkNKYndva0QiLCJtYWMiOiI3MDU3YTQyNDAxMzQ2ZmU2ZjU4YzU3OGM1YTM2NzAxNWRhYjQ2MjJlNDllN2U3NmIxODNmZWQzMWUzYWVhOWIyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:28:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IncxSXVBNjhNSG5JY1pORVVzTnV1aWc9PSIsInZhbHVlIjoiREJrTzhnMHUxQmdVNTB1TDN3aWRPeGhZRnp2WmRjbVNveXpKZWs5VnhpSHVJUlJyZTJxd0JxdG9JeU1BempwU0lTSDVpMFpIQW91bHM3L05jK1ZXTWU5bitRMVB0NUJnWW51Y0huVmxkZVUzWmdLU2pCeVpQRTduUHIxbUJYRVNoc3pvNkRmUndGZ0gya202dkdmM2twaEU1S2hRSUJjOEtEQU93S1o4MDJKd0pVbXhTd1NlNzJQZnUwRC9US3pUT05YVlhMSVBKancrZFREVHRSeThHbWRpb0ZEZ1VVZFNuZnB3TkMzWVBpcGdGQ1cyNUtZa2x1YzcwQ0tqT0dYNitkMlhURHcrQzdqWnlqcFU0bEhhUkNEeEFFWGlBZ0ZadDg5eWlncTRzZkJENEROZG1MS0NuaE94eWdZVFdUNkd0d2htVnJxc2JUa1llOW1BdU9XWDVnNGplcHdwZ0gwVXl3TmI3QktXUWZKTG1YbFNSUkdhWDlNbFk0UDBISVBWWUZPYlpJWXVjN09rVEJCejQ1aGhLQmV2bDVFdnhTV1pERUVQd2lRRWl0cklJazJSaUN3dzZKaCtpdGFLa3lVb0xQZitZUUlCYk5aNWpOQWJIbTdXYlRZaFJMNTdaTWtDUE1kbnRoSG0xazhGei9YQTVNOEFVZ1hsS2pneFJCb3MiLCJtYWMiOiJlMDI2MDQ2ODAwNGI0NDdhOThlM2FiMzM2Y2E5ODI1NjM4ZTViY2NjZjI3MGUxNzQyOWNkOWMxMmQxNGIxMDkxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:28:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705963039\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-28707689 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28707689\", {\"maxDepth\":0})</script>\n"}}