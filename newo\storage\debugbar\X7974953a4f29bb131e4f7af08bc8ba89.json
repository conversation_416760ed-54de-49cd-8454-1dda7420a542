{"__meta": {"id": "X7974953a4f29bb131e4f7af08bc8ba89", "datetime": "2025-06-08 14:52:50", "utime": **********.922943, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.176901, "end": **********.922974, "duration": 0.7460730075836182, "duration_str": "746ms", "measures": [{"label": "Booting", "start": **********.176901, "relative_start": 0, "end": **********.818303, "relative_end": **********.818303, "duration": 0.641402006149292, "duration_str": "641ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.818315, "relative_start": 0.6414139270782471, "end": **********.922978, "relative_end": 3.814697265625e-06, "duration": 0.10466289520263672, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45618136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018830000000000003, "accumulated_duration_str": "18.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.861851, "duration": 0.01694, "duration_str": "16.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.963}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.894829, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.963, "width_percent": 4.78}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.905944, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.742, "width_percent": 5.258}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1897912987 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1897912987\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1394957791 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394957791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394122823%7C63%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBLeVRvSHhhbnBEeHFlRnprTURSL3c9PSIsInZhbHVlIjoiTngyMXJkdXZtbE5QUWVWc2g1Sk83YTN3TUlkL3FsZk9zaXQrTXRFY2cyVmI1UTZpUEhTcFNleGIwMXVIdURyamxQR0FwM3BoREJXL3BEenNEYWJPdGJzRnhOVzI1WlRxU3ovelhHN04ramdFaFo5c3Z3Q1BzblM4YmloL3hHSW5MSHhGaWRQR29Fa0lON0tCOFdVRnovVytXY0JpREtrSUFqZFVPVldTSjdVTnd1eCtidEJrcHlSSVM4TTVkUEZmV3BpdnB0SW42d0lpZmp4NlpiKzdNSkdFTnIvdlBGQi9aQXF3SHJmZkRlNTdBanVEdXdDS1pyWnBHaHFMdWh1YnVlNi9iMGRpOVBiUG91SUEyMlVFZm1VOVoveUQwbGloM1ZtK21PU2RITm9hV0R1dklmbHVZTGVSeUYvcjNLQlpnd3VieGN6U2lDRnJhTDdyb3VjaU5zVFBKeklBMnJoZnJNaTdMUlVOeDJSVDU0NGRJUEdVK0lLUEhLc3hYaUF3Q0lMenhwOWJ5UFhQZ05SV3V5TTM0eU1QZ3B4OTBjc0wwUnY2Z0ZGYVljRGJSV3l1eDRGRXlhRW9QQTROOGdNdlBBMEZUQWIzY3JDaE5RaGV4N1lsby83dmJ1TG1YSVJGak9POUJMeGl4MlFzbzlWODNEYUszWlVQcXVJMUtxbjQiLCJtYWMiOiI3OWI0ZjBiYmM0OTVmNDRkZDQ5ZGNhM2QyNDkzOGM3MGJlNTdjOWU1ZWVhNjkzMDNhOWI5MzA4ZGMxOTYxNjMwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRRMjF3czBVdERDNzh2eHIzRlNvUVE9PSIsInZhbHVlIjoiS1VMKzlFeHRTZ0lPNDVCdWFINE4ycTZadDNzcTE2dnBtOHR0Y2VEd3J4SDcyOXUrK0lBUGV5VnV6YU5jQW1ka3JpSTJLTWFyV3JKTHZEYkdpMFUwa01XZ283ak1vVlp1czdHTy81cG5VRmV4OGlzUjllbGYwb1V5TnBDdnZBeDFVZGdMUjZnbjA4NlVuSURwbjN4ODUvY25PdWpQakN1d3JZaFVBWEJhZVE0NEs5MFVyZmRIdEpxaDFaZVBuWGJyK21KZ0xaSmdybS9LSUZ4djMvY1VJaUdRcnoyZGtIZ0lmbUxIVEk0dmQxQVI0QUowWlJoSk9lS2tqOTd2dHhYZjFldEJSVnlqNEZ3bm83ZEQzSVNHNTJNdExURGJZcU5VSXFENGFWZm53blVpakNlTld4Vnc0ZHJaUnJkZ2Jsa3d5SW1RbkhzMXNKL1RVUGVyT1puTXIyQnpuTUMvWEhXczRDZWF0bTdmbDVIODliTnk3ckU2TlFTaVgzZzhNeCttOTVBYmVKYVRhdlN3L21rTllnTEF3KzRGaE5XK0dYNloxaE9PcTJ6SlNod0Y4Q25RalZwZ2U0Z2JvZHJxYWJyS2xTb0s1a0NaSlV0bitGUUhvMHRwWEM4NStzZzRpV3c2M211OG5NNHRDdHkyMklXS21hUkJ5RzcxMXJYZlF2YlUiLCJtYWMiOiIzN2YzZDA3ODdkYTEyZThkZTEzMzRhYzBhZTAzNmU1NGU4M2MxMzc3Y2IzOGRmNTk4MDBmZDJhZGIxMDI1ZGUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-586680095 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586680095\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikt3am4vaHZHV05ncStJWk56TjB2WEE9PSIsInZhbHVlIjoiaHVTY2FhTGJHYkxCYTFnYk1hWk03SnErbzBPRXQxd0JSSnhrYnJUZXBpam83SjNiSDZkbzY0TDhYRkVYSzBNd3lHU3BEaXlwMkh0S2srMWZlM2N1N3V3UTVmUlVPVzU0emoxUHQ4eHZlNkRIYWFxeFh2d3VhaEd2VDVjblhVaHMrTm94emYrNW1aaVhQWE5YZFl0VGpveGZjTXBMTzBGTDBvUGNqdXl5QWdQTUM3MlhvVVNwOVA0d1ordEZ3eE5PRHphRTZaZG9USitxQzg2ZkMvS3lZY0J4alVNeWxTTDYyOXNHN2ZmMEt4eXQvaVBMWWpweXN0OW5DTHlVaHJhYmgrRWNHeDVmblNnaFJySXQvNlFhdGx2WWU4VldNbVM0UWF3TE53dFM3YkkwTTBLM1JXdHdVVEVOU0dWcTNzL2VYbUdvT3NoUUhWZXhydDhUZFdHQVloWXp2NGxkdnVEVTAySi9KQ1lRc3loa0hRODIrTnZLVXdEZXk3MWxhQlVNVHBYelkvOVJNWm4wMG1MdFZHYkRZQ3JJZEt2RHFDM1NBN1llOHUzOVc0YW1XUjZ4bDdwZGMzQTRBSm5BQVRac0ZnRWhsdG96YXdlUVovaUp2S1NWWE56OUd1U1MyTlJrQ25xQmRkZC9WREdoUHcwTWwvR1czY2tYeFVOZDhPYVUiLCJtYWMiOiIzMTE5NmRhOTRkM2NlZTJmYzI2YjU1M2NmZTFiYmUxNTRmOGY1NTM4MTllN2ZjMjFkM2U4Y2I2NzQ0NTQ2NzY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imt0ZDkybExOdTBOME1tT00wMTMwT2c9PSIsInZhbHVlIjoiL2lBMEFLS2VoMW9ONUIwWXNQWVR4ZENlMThIRHBxSSt2UXhrWm1ONzZqdFB3MWZ5YTFiK3lPbjFtYkVteGs3a0p0QWRDWUZTSWwzZWNCem03T1RHUVdHYk1yWU5ORGpOZ2tpdjlMOStPN1ZRK09vQ3ZXVzgydS9LVFkrUCtIQ0Rmdlc5M2lWVVBIaktLSkdqeXVmSWdSeUtpVGllQjJkMXNPcSttZmJiQ1l5UXZDc3FlOGNXdzJvc3RMWEg3ZXIvYTB2azJ6bWlVd0JOSytBZlBORDUzNVpoSVh4VTBlUzIxeUppWjdHSGlNem8zZjZqU3htVFBVU3Y5TEVpbzNOYUloS3VVenRYcnJZY2NScWFkR3o3WHhBK2p1bzJ3RDNWSUVQU1BkVXloajhQbkVBbENWUWcrTUdwaWtpWGpDZFdNcFgxRHVUQ0I4czZRMlBqRnNpQ1psM2pJY05YQVFjL250bkFCMjVUTEdlM0RHSGxxTENPbUlvbmE4dHNFYWNMN0NvQVFTcnZVaTR1dVhSRzRDTFNjRkk1c3krVndJblJ6WFdqWjRwVERGR0RoSTY3Q2x5QUUwd1pEWlVCaFhxWTM4T1FXVnhPdk5lVlliRHVtZTNNa1VaT2xvQWFkU3hteC9ycCtQQkhmdHpxd1A4YWhqSDA2cVcxS0w3Uk53Y1EiLCJtYWMiOiJhMzdkMWFlMTUzNjU3OTJkYWU4OGZmOTE0NmE5MDgyNmYwZmU4NTU3ZGRmZTllMDU2MGM1OGFhMTBmMDM3YWFlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikt3am4vaHZHV05ncStJWk56TjB2WEE9PSIsInZhbHVlIjoiaHVTY2FhTGJHYkxCYTFnYk1hWk03SnErbzBPRXQxd0JSSnhrYnJUZXBpam83SjNiSDZkbzY0TDhYRkVYSzBNd3lHU3BEaXlwMkh0S2srMWZlM2N1N3V3UTVmUlVPVzU0emoxUHQ4eHZlNkRIYWFxeFh2d3VhaEd2VDVjblhVaHMrTm94emYrNW1aaVhQWE5YZFl0VGpveGZjTXBMTzBGTDBvUGNqdXl5QWdQTUM3MlhvVVNwOVA0d1ordEZ3eE5PRHphRTZaZG9USitxQzg2ZkMvS3lZY0J4alVNeWxTTDYyOXNHN2ZmMEt4eXQvaVBMWWpweXN0OW5DTHlVaHJhYmgrRWNHeDVmblNnaFJySXQvNlFhdGx2WWU4VldNbVM0UWF3TE53dFM3YkkwTTBLM1JXdHdVVEVOU0dWcTNzL2VYbUdvT3NoUUhWZXhydDhUZFdHQVloWXp2NGxkdnVEVTAySi9KQ1lRc3loa0hRODIrTnZLVXdEZXk3MWxhQlVNVHBYelkvOVJNWm4wMG1MdFZHYkRZQ3JJZEt2RHFDM1NBN1llOHUzOVc0YW1XUjZ4bDdwZGMzQTRBSm5BQVRac0ZnRWhsdG96YXdlUVovaUp2S1NWWE56OUd1U1MyTlJrQ25xQmRkZC9WREdoUHcwTWwvR1czY2tYeFVOZDhPYVUiLCJtYWMiOiIzMTE5NmRhOTRkM2NlZTJmYzI2YjU1M2NmZTFiYmUxNTRmOGY1NTM4MTllN2ZjMjFkM2U4Y2I2NzQ0NTQ2NzY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imt0ZDkybExOdTBOME1tT00wMTMwT2c9PSIsInZhbHVlIjoiL2lBMEFLS2VoMW9ONUIwWXNQWVR4ZENlMThIRHBxSSt2UXhrWm1ONzZqdFB3MWZ5YTFiK3lPbjFtYkVteGs3a0p0QWRDWUZTSWwzZWNCem03T1RHUVdHYk1yWU5ORGpOZ2tpdjlMOStPN1ZRK09vQ3ZXVzgydS9LVFkrUCtIQ0Rmdlc5M2lWVVBIaktLSkdqeXVmSWdSeUtpVGllQjJkMXNPcSttZmJiQ1l5UXZDc3FlOGNXdzJvc3RMWEg3ZXIvYTB2azJ6bWlVd0JOSytBZlBORDUzNVpoSVh4VTBlUzIxeUppWjdHSGlNem8zZjZqU3htVFBVU3Y5TEVpbzNOYUloS3VVenRYcnJZY2NScWFkR3o3WHhBK2p1bzJ3RDNWSUVQU1BkVXloajhQbkVBbENWUWcrTUdwaWtpWGpDZFdNcFgxRHVUQ0I4czZRMlBqRnNpQ1psM2pJY05YQVFjL250bkFCMjVUTEdlM0RHSGxxTENPbUlvbmE4dHNFYWNMN0NvQVFTcnZVaTR1dVhSRzRDTFNjRkk1c3krVndJblJ6WFdqWjRwVERGR0RoSTY3Q2x5QUUwd1pEWlVCaFhxWTM4T1FXVnhPdk5lVlliRHVtZTNNa1VaT2xvQWFkU3hteC9ycCtQQkhmdHpxd1A4YWhqSDA2cVcxS0w3Uk53Y1EiLCJtYWMiOiJhMzdkMWFlMTUzNjU3OTJkYWU4OGZmOTE0NmE5MDgyNmYwZmU4NTU3ZGRmZTllMDU2MGM1OGFhMTBmMDM3YWFlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1679281404 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679281404\", {\"maxDepth\":0})</script>\n"}}