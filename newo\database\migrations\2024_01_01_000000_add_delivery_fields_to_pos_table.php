<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pos', function (Blueprint $table) {
            $table->enum('delivery_status', ['normal', 'delivery_pending', 'delivery_completed'])->default('normal')->after('status_type');
            $table->unsignedBigInteger('cashier_id')->nullable()->after('user_id');
            
            $table->foreign('cashier_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pos', function (Blueprint $table) {
            $table->dropForeign(['cashier_id']);
            $table->dropColumn(['delivery_status', 'cashier_id']);
        });
    }
};
