{"__meta": {"id": "X91ba70d0d70dce58ab5fdaa7cb588da4", "datetime": "2025-06-08 13:44:38", "utime": **********.844579, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390277.400026, "end": **********.844623, "duration": 1.4445970058441162, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749390277.400026, "relative_start": 0, "end": **********.673388, "relative_end": **********.673388, "duration": 1.2733619213104248, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.673411, "relative_start": 1.2733848094940186, "end": **********.844629, "relative_end": 5.9604644775390625e-06, "duration": 0.1712181568145752, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45396120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026329999999999996, "accumulated_duration_str": "26.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.768279, "duration": 0.023829999999999997, "duration_str": "23.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.505}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.816796, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.505, "width_percent": 4.596}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.826438, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 95.101, "width_percent": 4.899}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 2\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 20.0\n    \"originalquantity\" => 20\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 40\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1483639463 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483639463\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1920537238 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtDVG03SHV5RFQxTmQ2dGpDUUM2VUE9PSIsInZhbHVlIjoiOGd6TXRzS3pBVnhKVFpvQjZqaEgyQWljOGFIdTRJRTJxMzF6amtHK0o0V2NTMTRZNXB0Z1A0QjAvMzZrZU4zTXZsV2RMd3lqQk5uMzlLRjR3ek10V2toSHlUNi9sMVBMN1p6a2pIdEZTT2FJOEErVnpEcHNIQVd3TXlFN0lJZ0RXTUFJR29mTUViYy9wTWRma29mMGFhTWRnRXZ6WWxKU0RsbGU4a1ZGc25EcGNjQk05V1ZkMVVkK1NNRmJ0eTEzZ1MzeGlCOUw3dTRvNFZNZlFFM3lMNFp0WlZLcERLS2k2Tzk2QzBOTTF0bFo2R3h1VFk0OWdZNVBzdG9yM3Vpc3ZEdDlDRWE0Q0ZJdS9UdFVMM3pjeExuTUFNNjE4aVFFMnVtRnAvTTJQbUpJd0EwWHRtRTVPdTZKUFJNRGRqSHJwMXlRSlh2dzkxNjdkNnZEZGVoTDNGYWZvUFhVS1VWckV0ZlQyNGRZL2RGc0xzNDRWUUh5RkVpeWJMVkFVU1dKWDE2QWlyV0hQOFp0SXViQVB5dkJNaHYzTHdkRk1xdnU5cGNVTVJEOEkrdk4vdnpPSGtrQ3UxdEUwZXFGRklWaC9zOHBiWGZCM011MzU4bE9MN0VRSGhNdFFkVGpzM1ZlMVFoWlBiVWhjZTkybXlVQ1lRVUVkSDZLUWd1VjMyT1MiLCJtYWMiOiIwNjllNTZlYThmODI3YWM1ZTA2ZTk3YTM0OGJiY2ZmMWU4M2ZmNjAzOWU3Y2FkZjM5MjUyMjM2ZGI5YTRjYjA3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpodWVEcTNLSnhOTEIydU42aC9JSUE9PSIsInZhbHVlIjoieG8xVWY0VFBCM29paUxWMithZVZCMU94WUFzTmJVRzhVdi9CY0dJVGtld3FCbTV1VmlQSjFXOU9MWEdjWTBHWjU1dm11S2hOZmxSVjBueXlvTFB3ZDNSd3ZOKy9vSEJieERabXMxajJoSkgybitsUnNjd2tBajhJVm9KYmVIWG9JVFlka0hlLzhXSjFkK3Vlbnc1VGl1VGJ3Y2lHSEo0eG0yb0llMktidWtvcWZKZjJIanhwMDhwbHQ1ZnJIa1Zpai9YMHMzQzB3Y1FQRTlkb3lmbTV5SkxiV1VJWXJyTGN0cUVWekdid1VCOUEyRGkvcWtyZjIvcFV3VzhLNjVnQkhxWW0xVE5zQ0x6TG1JbTNITlFDTzZCRFZYRFdGYU5pTXExaHdsdXZud3c5NDBibHJQd2tBcTBJNVVoWnVXTUhNTEhpK1lGOXJ5WGY0QU1XVWlwY1JGZld0ZGhra003Tmd5Y1R3bEFKR0ovV2M5RGFMTCtQbzdLWHlPcTVDNXJPQzlTZFZ1L0N6MXdiYmxwdC9aWWVkZFgrRE5RTGpsMnNLcWJDNUJNNkNzL0dLWUF1aTNjNzJiVUZ6eGZ4ME9jSFRPSVF1cDBmbm0zaTlWdGV2dURmQVRmTjU1NEdQSitzZEdkU2ptYTRiUEdFKzl5UXJ2dm5EWGRIQ1grcTdsYjUiLCJtYWMiOiJlODQ4YWM5MmUwNmJkNzFiZTJmZjc0N2M3NTRkNDRhNGQxOTkxYTU2YTZkZDg4OGFhN2IwZmM0ODcyZjVlZDNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920537238\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1336823856 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBaZVV2Q0EzVGVUYkxxNXdaMHVGOEE9PSIsInZhbHVlIjoiYW5UZXQ3dVM3SEVIa3JlQUU1R0g1ZUFoT1hrNlNtSlI1dGRrb1lJSlV4Y3dOSjY1OFNxTEpMc0NCMDJ3OGd6TThrUUlrQnVFRUM3QWoxRUVSaGZ3WmxvS0VzdkxOUVJNb3Bxd0ZFaTFnL0FJNS9NSDc0S3pYT1lQb1h3MWc5dDdkUDVoZEx5V0MvdEZYTG15bzlTOTBUZUxEQk10NlA0enBMeGJaWm0rRENjNXF1Nmo4MERBR2xKQ2tnVVhpL2p2T3VYcE9JcEMvMHRWdndJLzdSa0JBMXhGNkdHS05JZDFMTjFOYzhUNkliYVllY0t2aUZjN2cvWEFtN2pobmVoMzFjMTFueUJ5cmc0aGFEN0ozVXVLNnZtME1wM2hFK3g0bXUxTytHUGszSG1RRGpxSkwvT05uUHJ5cGtFN014WFhQSUlCelVCUzdHR0ZwUHd4bVd4Q1pCV2hYVmJuT1pjQWpjQVptUWhNNmJpS2NwT2RoNDJuRlVBTkt5S2VPSEVGeC9BR1Q3RGh1REwvc3YyZlk2Qjl3enFWRWJiRmhadFpZb0FBK2JXenVNaFdHeUJXZGx0NXZGZzNzOVVpTzlmanhTMVEzelRodnd6T0FCS0FUOUJBeGtDRE5CRER3SWYyaUJxc1dKV0xzaVpFcGhoR2c0bElhWnlTWUdiVHZUZEUiLCJtYWMiOiJiNWM4MTM5ZmFmOWYxYTJmODUyNzk3ZGEwYzJkMDAxM2Y1NjI0NTdmMTU5MTkwM2VmOThiMTBkMjQ5NGViNWU2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikl5cUkzM2pZYkxmd09KUU0zRWdWRGc9PSIsInZhbHVlIjoiTGQ3aktpWkFRUW1LTVB2MHo4YnhoOTZyMzBkVUd5b1RRNGRCejAwL3VQc0JqUVdtczB4WXdRY0EzcUg0ZTdja1FXaVFiNnpWM2xScmtzYjVGNjhwMnBwcnJWbGVudFl2S2UxWWwyS1lrWHJ3UC9WSEd2WXlhbk8zNm1RK0Fkd3RQRDllTmxScm1RVnRhU1ZxMkRQN3JKTURXOE5YQ3RzME4weWM5cEhpVElTTmVTNlFFeUlpcXhqMzZiSnU0WEJ2eUFGSTlVcU1aZTlZV0VqNGFiR0Z6NS9IUUJyN1c0UUFBWEV1eFN5U3g2dnd4WUlEb1ZWN3ZRQTJ3V2k1WEZrRzNIS3U5dzFFVEpMempEOXZnRlBuV3YwSWNMVk5BMkpZTnl0cjR2MzlxWkZGOE1hbDc5NjUwQ0IvVytjN1ZQVFRGL2l5NTA4RFJNT3psdG1PWDBWeVQ0YnN2eWVwT1lvK0NzRjllaUdEdS9QaWt4dnY5NlYvZ05relFhbEVJWjNRQk05KzNrYm5FK01PaEJyM1R0dXp0NmVwNmZHVHZLMWpsM0ZHVTlVaENyLzNNNFY5TENqNXNLVmE0cHJ4RmMrZDJSL25rMlJSU0J2RUVjdmtaMmhvcUcyNWVKK0Fmb2ptZUxYelJldVhLS3RmMUZFWXd6WmEwMzh6anRPZjlDYjYiLCJtYWMiOiIxZTZhYWQ4MDA2NTkxNTUzYzZmZmQwZDM5NDQ3MjA5NjY5ZjNmNzRjNmU3M2YyZmJmOTA5ZDAxYTMzNjkxOGE0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBaZVV2Q0EzVGVUYkxxNXdaMHVGOEE9PSIsInZhbHVlIjoiYW5UZXQ3dVM3SEVIa3JlQUU1R0g1ZUFoT1hrNlNtSlI1dGRrb1lJSlV4Y3dOSjY1OFNxTEpMc0NCMDJ3OGd6TThrUUlrQnVFRUM3QWoxRUVSaGZ3WmxvS0VzdkxOUVJNb3Bxd0ZFaTFnL0FJNS9NSDc0S3pYT1lQb1h3MWc5dDdkUDVoZEx5V0MvdEZYTG15bzlTOTBUZUxEQk10NlA0enBMeGJaWm0rRENjNXF1Nmo4MERBR2xKQ2tnVVhpL2p2T3VYcE9JcEMvMHRWdndJLzdSa0JBMXhGNkdHS05JZDFMTjFOYzhUNkliYVllY0t2aUZjN2cvWEFtN2pobmVoMzFjMTFueUJ5cmc0aGFEN0ozVXVLNnZtME1wM2hFK3g0bXUxTytHUGszSG1RRGpxSkwvT05uUHJ5cGtFN014WFhQSUlCelVCUzdHR0ZwUHd4bVd4Q1pCV2hYVmJuT1pjQWpjQVptUWhNNmJpS2NwT2RoNDJuRlVBTkt5S2VPSEVGeC9BR1Q3RGh1REwvc3YyZlk2Qjl3enFWRWJiRmhadFpZb0FBK2JXenVNaFdHeUJXZGx0NXZGZzNzOVVpTzlmanhTMVEzelRodnd6T0FCS0FUOUJBeGtDRE5CRER3SWYyaUJxc1dKV0xzaVpFcGhoR2c0bElhWnlTWUdiVHZUZEUiLCJtYWMiOiJiNWM4MTM5ZmFmOWYxYTJmODUyNzk3ZGEwYzJkMDAxM2Y1NjI0NTdmMTU5MTkwM2VmOThiMTBkMjQ5NGViNWU2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikl5cUkzM2pZYkxmd09KUU0zRWdWRGc9PSIsInZhbHVlIjoiTGQ3aktpWkFRUW1LTVB2MHo4YnhoOTZyMzBkVUd5b1RRNGRCejAwL3VQc0JqUVdtczB4WXdRY0EzcUg0ZTdja1FXaVFiNnpWM2xScmtzYjVGNjhwMnBwcnJWbGVudFl2S2UxWWwyS1lrWHJ3UC9WSEd2WXlhbk8zNm1RK0Fkd3RQRDllTmxScm1RVnRhU1ZxMkRQN3JKTURXOE5YQ3RzME4weWM5cEhpVElTTmVTNlFFeUlpcXhqMzZiSnU0WEJ2eUFGSTlVcU1aZTlZV0VqNGFiR0Z6NS9IUUJyN1c0UUFBWEV1eFN5U3g2dnd4WUlEb1ZWN3ZRQTJ3V2k1WEZrRzNIS3U5dzFFVEpMempEOXZnRlBuV3YwSWNMVk5BMkpZTnl0cjR2MzlxWkZGOE1hbDc5NjUwQ0IvVytjN1ZQVFRGL2l5NTA4RFJNT3psdG1PWDBWeVQ0YnN2eWVwT1lvK0NzRjllaUdEdS9QaWt4dnY5NlYvZ05relFhbEVJWjNRQk05KzNrYm5FK01PaEJyM1R0dXp0NmVwNmZHVHZLMWpsM0ZHVTlVaENyLzNNNFY5TENqNXNLVmE0cHJ4RmMrZDJSL25rMlJSU0J2RUVjdmtaMmhvcUcyNWVKK0Fmb2ptZUxYelJldVhLS3RmMUZFWXd6WmEwMzh6anRPZjlDYjYiLCJtYWMiOiIxZTZhYWQ4MDA2NTkxNTUzYzZmZmQwZDM5NDQ3MjA5NjY5ZjNmNzRjNmU3M2YyZmJmOTA5ZDAxYTMzNjkxOGE0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336823856\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-13******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>20.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>20</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>40</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13********\", {\"maxDepth\":0})</script>\n"}}