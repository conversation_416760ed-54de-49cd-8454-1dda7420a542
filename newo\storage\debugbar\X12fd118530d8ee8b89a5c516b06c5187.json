{"__meta": {"id": "X12fd118530d8ee8b89a5c516b06c5187", "datetime": "2025-06-08 14:57:24", "utime": **********.116853, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394643.452722, "end": **********.116879, "duration": 0.6641569137573242, "duration_str": "664ms", "measures": [{"label": "Booting", "start": 1749394643.452722, "relative_start": 0, "end": **********.016883, "relative_end": **********.016883, "duration": 0.5641608238220215, "duration_str": "564ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.016895, "relative_start": 0.5641729831695557, "end": **********.116882, "relative_end": 3.0994415283203125e-06, "duration": 0.09998703002929688, "duration_str": "99.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45602312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02437, "accumulated_duration_str": "24.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.057263, "duration": 0.022719999999999997, "duration_str": "22.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.229}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.091758, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.229, "width_percent": 3.447}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.101635, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.676, "width_percent": 3.324}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1770571285 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1770571285\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1961248935 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1961248935\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1515459548 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515459548\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-395799249 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394379618%7C66%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InIvb3pjNFN0WGtUS2YwczQwVUFFNXc9PSIsInZhbHVlIjoiOEpYSmNhUDFXVDdiQ2xBNnpINXJDNjRmSTBpQTJGQVFZeUJUQ2lrSGJ1V01jTVJycnA0aEI1RGRXd3RFeFR0Ty9qa0lMMk1Yck9lVWdlaENTeUNVdEFTejBmM2wza0Raa3dBYUQvWllVTTV2VDhkeVd5NFJUYzY3akNUdWxHRXFXbExtalV3d3VDNEpZaTcwZFJSZmNnSFY1b2ZYa3ZTZXQrTUx4NWxnSjRiSm45a1dkL0E3SXpWRW84MzQ2VWFVNmhsdzJvQTRpOENQVFhLc2dZQ0Mwd2drQlF1OS91cE1tTVgwMUpZM21jeXgxYUlHK2ZWR1RDM1ViOXNucEpsSElPa20ybjRkUUNKaTVmMjY3MDlaOFUwazNKVXpBU1ZXL1ZIUnFKSnpNaGROSWtLZTliQlZabndreWpXc1NOV09pOFFabEN0cm9wN2V0NDRCQnJ1OHV1VnpjRGVGQXVSUS90Y2lQMHpHQ2tSS3ErY04yK3AvL20wRTFPUnVnVEw0NEtjSFVJcVpMTE9UdG03bElxNXdXUnppUlFZUVVUTGZleFk1eVp2c3NWcUhGVTk5cndDamNIRGZLaTBnSE14ZE5OcXE4NzdueTNsbGVvdVErSnllS0F2QVJoQVNWS2dpVVFrYTBpVEdkZDF4RTBUaTdMWlVvMFJqK3ZZOVJUbnkiLCJtYWMiOiIwNTdkZmQxZTQ3NGQ2NjY4ZGJiZmU5NDEwN2QzZTg2MWZmMDc0NDUyMTUyMDAyNmNmMTc1ZTkxYWJiZDNlNzNhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImpYRzR2T1VwMXdqV21ZWC9FdWZsTHc9PSIsInZhbHVlIjoidHIyMDEycC9XbzY3ZGlaSXlVQzdrZFJRQXJsQ01nOUZkTWVOenBUNk85TTMvZkV3ZERCa2RsQ3dKb1Y3Z0xGQ3U4NlI3MWQ0YklsU1NuMU9JU045UlZQekdUeWg1UzVJVXBYckE3R0thaVdUTVBOR1drTEJiMlhmbWpaNllyZjRQVE0xZ29GcWp0UElERVM0ZGVFcXNKbWdkeGFINVROK2VEME0vMytYWlB6VUcrbmdTYlhzUnQ2SHJka0VzY1dJREQvRXBZY251R1REOS9kQVhhMmUvdWNMOU5WMjlvcGlucm9lNXdEOEhPVEh1R3FzaCszYlNzc0ZoRVJVVmlmUlpTcTlma2lzcmdzL3dWVmZneUVwYmNQVjVzaUd0MEVEVEV5L1BydkU1eHJjSWRUMW5ZTHMvOFlwQW40UG1SdTZ6Zm92TlNBU0tiZndXV3ZNd2J3T3AzUVZKbjhBKy9YbGpFS3JmeDROdjRwQ05zdVozWVZJanlrN3VVZVlVSkpScHkxVWlpNzIyaEc4MEdlczRSdWIzWDB1amFoeUhYdm9DU2R1SFBmSVdCUUtQL1AvVnQxOEg3WmtLTVhaZXRhZU1BMVdkVEtaRG9rZWJQcm1EOEFzekF0K3NGNlRENnhWQU9Dc3dhcjZOU1JPQlZFQUdwc1hmS1hBellmUFBQdkQiLCJtYWMiOiIwZGM4MTdjYzE5NzE5ZjY1NGM4ODQzZDJlODMzNjRlNGQ2YjhjMGNlZDg0OGI2ZjRmMmUzNDFiMTMxNjBkMTUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395799249\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-141894808 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141894808\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-562919786 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9ZaWRYODhXU1c0UEVhMmVEZUlzY0E9PSIsInZhbHVlIjoiSWNwdDdoSDlZMVMvVlRjaGZmNGoyTVM5ZC9ldFVocHQxU1M3S0RneGFwaEFMQWJHSk1uUE9vbVNneU80TjdBRW1wQmxta25tWmlmNnZHT3VzVmJNL3lmMmJXVHhuUnNvajdsNENjVGdqMmx1Ry8rVW03RXNUUnY0b3V5TkREd3J4MlljQlNUSlRDRTY2UzRORCtKQnptTWRNRXRPRWs3VHc2QUo3VWp1NlFwdlZRQzNSSkorWThzenJqbHpqUUdzZjJNMGFOM29IbEVYN2hMRVQ4MVhnSUYwUDVxUHc2RFQ0Qklxek1jUlU0bHdjaFRTKzdSbThITWM0cHhzTjRRK1NPVlFSQnpxTjVnYzdUWGZxUnNWMUNoSmdMMXBoOFJacHdyUTFQK0g2S3gzWGNyU0dwd3ZaRGZtN1YvWHIwcW1BakZrZjJVN0trc3gzVG05OWNkTk1Ocy9zd3JZem1ieWJMUXUvdmJmT1lJM1F0QXdySDVlUlgybUJ2TXYvNVRvWTNNMS9MMVN2OExEc2ZVaEFXZm1XWXFxeDViQStYamVtRzA1UnhYNHV2WGl6WDVVa1VnVFl6YWZwai9pa3JhWDYxRkJXa0s3VVhjeHV5OVV1MUpQOVZyUnZZWnFpNDZuS1VnS3dlNFVxYVcvaHpHUG1iQURocFV4eDZsbkNTZlEiLCJtYWMiOiI2YWIyZDBjMzU5NWEyOTFmMzBjYmQ4N2E4NThkNTRhNGI5OWE5MjBkYmVkYjFhNDkxMGE3MGJkMDgyNDYwNWI4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRxTmF2enlGMDJTeUNnT0p2SnFVclE9PSIsInZhbHVlIjoib1RmOWpUZVF1WjhYVU9jdU4vR09kSFByYTFYd21ENW5Vc1RtZG5BT0NWREN3ME8zaFh5dFhuTURWcm9YZmlkRDFKSW5kMG5paFVDWjBnaTZyVU56U1ROdm1sWU9CTjNJMkVGOUZ4ODJoZFZ1Q0Z0NldlZzBDUzlhNkE2Z2VSSDBxRXc4Y0FHSmVkeHVMVFAySTBxVG5oc1R3cXAvYVdEd1BsNmZEN0R0dU8yY3hBZmRrcS9KTlIrUUEyU3dURHJQNS80aHZXeWd1MnRnM0YraEpJNzR1Vk4yd1RnL1FBa1Y4OFRhWStjckpuVnFvOU1xTTZ2WnduS0NobjZRTEluTGVpU3p3UzBjZkxLVkZ2QzlYMGhFRUNVZk1icUxSa3RFK0ljenhKRUQ2MGhlWXpmRUQ3SVl0SVh2OWZrV3gyWS9pUE4xQUNtU01tdUxpVVFBYnZuTE1RNXhxWnJKbFVCRUhTLzkwM2Y0VWhManBtZjd0MmZXdjJLK0ZHUnkvc05BWjhzVFNmaUtmWEVBVFJsSE04Uk9xaU1mbFljVnpJUW1pRWRsaldDN0VKbHVWdG9qWnErN1JpZEhmWFVsTWR6R2x2NHpWSEJaV0Z6NVdzV1BYeUVlVkZjb2pXclRGYU5tTTFDaU1seHJqSGxBWnI5aG9vSWFCcTVmcy9GL25ISnQiLCJtYWMiOiIwNjZlYzJmMDllNWU0NWNkYWE2NWE4MzVkNzMxYmIxOWVkNzQ3ZjUzYzlkZWQ0ODA2ODRkNWZhNThjNGQxODE0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9ZaWRYODhXU1c0UEVhMmVEZUlzY0E9PSIsInZhbHVlIjoiSWNwdDdoSDlZMVMvVlRjaGZmNGoyTVM5ZC9ldFVocHQxU1M3S0RneGFwaEFMQWJHSk1uUE9vbVNneU80TjdBRW1wQmxta25tWmlmNnZHT3VzVmJNL3lmMmJXVHhuUnNvajdsNENjVGdqMmx1Ry8rVW03RXNUUnY0b3V5TkREd3J4MlljQlNUSlRDRTY2UzRORCtKQnptTWRNRXRPRWs3VHc2QUo3VWp1NlFwdlZRQzNSSkorWThzenJqbHpqUUdzZjJNMGFOM29IbEVYN2hMRVQ4MVhnSUYwUDVxUHc2RFQ0Qklxek1jUlU0bHdjaFRTKzdSbThITWM0cHhzTjRRK1NPVlFSQnpxTjVnYzdUWGZxUnNWMUNoSmdMMXBoOFJacHdyUTFQK0g2S3gzWGNyU0dwd3ZaRGZtN1YvWHIwcW1BakZrZjJVN0trc3gzVG05OWNkTk1Ocy9zd3JZem1ieWJMUXUvdmJmT1lJM1F0QXdySDVlUlgybUJ2TXYvNVRvWTNNMS9MMVN2OExEc2ZVaEFXZm1XWXFxeDViQStYamVtRzA1UnhYNHV2WGl6WDVVa1VnVFl6YWZwai9pa3JhWDYxRkJXa0s3VVhjeHV5OVV1MUpQOVZyUnZZWnFpNDZuS1VnS3dlNFVxYVcvaHpHUG1iQURocFV4eDZsbkNTZlEiLCJtYWMiOiI2YWIyZDBjMzU5NWEyOTFmMzBjYmQ4N2E4NThkNTRhNGI5OWE5MjBkYmVkYjFhNDkxMGE3MGJkMDgyNDYwNWI4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRxTmF2enlGMDJTeUNnT0p2SnFVclE9PSIsInZhbHVlIjoib1RmOWpUZVF1WjhYVU9jdU4vR09kSFByYTFYd21ENW5Vc1RtZG5BT0NWREN3ME8zaFh5dFhuTURWcm9YZmlkRDFKSW5kMG5paFVDWjBnaTZyVU56U1ROdm1sWU9CTjNJMkVGOUZ4ODJoZFZ1Q0Z0NldlZzBDUzlhNkE2Z2VSSDBxRXc4Y0FHSmVkeHVMVFAySTBxVG5oc1R3cXAvYVdEd1BsNmZEN0R0dU8yY3hBZmRrcS9KTlIrUUEyU3dURHJQNS80aHZXeWd1MnRnM0YraEpJNzR1Vk4yd1RnL1FBa1Y4OFRhWStjckpuVnFvOU1xTTZ2WnduS0NobjZRTEluTGVpU3p3UzBjZkxLVkZ2QzlYMGhFRUNVZk1icUxSa3RFK0ljenhKRUQ2MGhlWXpmRUQ3SVl0SVh2OWZrV3gyWS9pUE4xQUNtU01tdUxpVVFBYnZuTE1RNXhxWnJKbFVCRUhTLzkwM2Y0VWhManBtZjd0MmZXdjJLK0ZHUnkvc05BWjhzVFNmaUtmWEVBVFJsSE04Uk9xaU1mbFljVnpJUW1pRWRsaldDN0VKbHVWdG9qWnErN1JpZEhmWFVsTWR6R2x2NHpWSEJaV0Z6NVdzV1BYeUVlVkZjb2pXclRGYU5tTTFDaU1seHJqSGxBWnI5aG9vSWFCcTVmcy9GL25ISnQiLCJtYWMiOiIwNjZlYzJmMDllNWU0NWNkYWE2NWE4MzVkNzMxYmIxOWVkNzQ3ZjUzYzlkZWQ0ODA2ODRkNWZhNThjNGQxODE0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562919786\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}