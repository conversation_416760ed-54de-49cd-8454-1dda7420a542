<!DOCTYPE html>
<html>
<head>
    <title>اختبار طلب التوصيل</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>اختبار طلب التوصيل</h1>
    
    <div>
        <label>Customer ID:</label>
        <input type="text" id="customer_id" value="1" />
    </div>
    
    <div>
        <label>Warehouse Name:</label>
        <input type="text" id="warehouse_name" value="1" />
    </div>
    
    <div>
        <label>CSRF Token:</label>
        <input type="text" id="csrf_token" placeholder="أدخل CSRF token من الموقع" />
    </div>
    
    <button onclick="testDeliveryRequest()">اختبار الطلب</button>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

    <script>
        function testDeliveryRequest() {
            var customerId = document.getElementById('customer_id').value;
            var warehouseName = document.getElementById('warehouse_name').value;
            var csrfToken = document.getElementById('csrf_token').value;
            
            if (!csrfToken) {
                alert('يرجى إدخال CSRF token');
                return;
            }
            
            document.getElementById('result').innerHTML = 'جاري الإرسال...';
            
            $.ajax({
                url: '/pos/store/delivery',
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    customer_id: customerId,
                    warehouse_name: warehouseName,
                    user_id: '',
                    discount: 0,
                    quotation_id: 0
                }),
                success: function(response) {
                    document.getElementById('result').innerHTML = 
                        '<h3 style="color: green;">نجح الطلب!</h3>' +
                        '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                },
                error: function(xhr, status, error) {
                    document.getElementById('result').innerHTML = 
                        '<h3 style="color: red;">فشل الطلب!</h3>' +
                        '<p><strong>Status:</strong> ' + xhr.status + '</p>' +
                        '<p><strong>Error:</strong> ' + error + '</p>' +
                        '<p><strong>Response:</strong></p>' +
                        '<pre>' + xhr.responseText + '</pre>';
                }
            });
        }
        
        // تعليمات للمستخدم
        document.addEventListener('DOMContentLoaded', function() {
            var instructions = document.createElement('div');
            instructions.style.backgroundColor = '#f0f0f0';
            instructions.style.padding = '10px';
            instructions.style.marginBottom = '20px';
            instructions.innerHTML = 
                '<h3>تعليمات الاستخدام:</h3>' +
                '<ol>' +
                    '<li>اذهب إلى موقع POS في متصفح آخر</li>' +
                    '<li>افتح Developer Tools (F12)</li>' +
                    '<li>في Console، اكتب: <code>document.querySelector(\'meta[name="csrf-token"]\').getAttribute(\'content\')</code></li>' +
                    '<li>انسخ النتيجة وضعها في حقل CSRF Token هنا</li>' +
                    '<li>تأكد من Customer ID و Warehouse Name</li>' +
                    '<li>انقر "اختبار الطلب"</li>' +
                '</ol>';
            
            document.body.insertBefore(instructions, document.body.firstChild);
        });
    </script>
</body>
</html>
