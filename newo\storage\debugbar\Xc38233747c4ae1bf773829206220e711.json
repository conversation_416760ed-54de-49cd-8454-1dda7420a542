{"__meta": {"id": "Xc38233747c4ae1bf773829206220e711", "datetime": "2025-06-08 13:26:44", "utime": **********.934553, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389203.56145, "end": **********.934585, "duration": 1.3731350898742676, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749389203.56145, "relative_start": 0, "end": **********.780813, "relative_end": **********.780813, "duration": 1.2193629741668701, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.780831, "relative_start": 1.2193810939788818, "end": **********.934589, "relative_end": 3.814697265625e-06, "duration": 0.15375781059265137, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45170040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022829999999999996, "accumulated_duration_str": "22.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8604572, "duration": 0.02047, "duration_str": "20.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.663}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9069438, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.663, "width_percent": 4.95}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.916012, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.612, "width_percent": 5.388}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1364425836 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1364425836\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1973151974 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973151974\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-515835978 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-515835978\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1439920341 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5Kd1M4Vmhja2pnYmJQNDVaR2FjTWc9PSIsInZhbHVlIjoidUNPTElUZGV4Y2s3R0gzNHRNWS9zMjF0Qk1PcTdodVpSYVJzMlY3bE9ubGpZdm1GbS9LNnlvdElrbE1mSjZpb3cyNHNocDd5RXNTbWdnVEloRndZQVpRZUs0djBzY041eFByZzVMeGNTd1lKYmZ0S2xjTHg3Qm5jNlJBUEIwaGpETUcxaElqOW9sSmN1NFlJTFluUVFBTDVkd2VrMk5tWGw4cHMxWXIvc2wvZzVlVUxXYW1wZjU1cTRZa002U1JYMDJzdTFWb2RGMEQxTllDWXVkMnJTZEhNbzBWem5mL2dBM3dVVW5ETTBCR09EUGF2UkhMN0tVa0xIUEMzRkdDRVQrRm1icXptUFc4UE1aRWdIRFVpRFV1R2d5WWlndkFZRVhsbEhIL0YyTXBkZGZUTmE1dDgrNld2T1B6aFdNZC9JMUhGV1RrOE1hOCtWblFpaUdPRTNmOHN3VC9jVklMMGRYNnkvM1VjSlJFeGN1dUJkbFlrOS9DWFJEYTVoQ2QvYkI1N0cwUFhvcFI3VU9kQWcrTlpmc2VZNUgyaUZJckE5bTF2bThvTCtqbjh2U2FuVlg3aldIbytsNWMvck1rdzV6MldabldaKzA2UDNPVGF0bjRWejVVVkpzZVFVRTRJM2JzMnZXOWdna0tPMExsdk9UMWZldmgySUxMM0FVQlEiLCJtYWMiOiIzOGNiY2FmY2UxZmMyMTU2MjA3NzQ4Y2IzYTE3MWNhYWY0ODlkODJiMzVmNmQxMWExM2Q5NmQxODc1MWZmNzBmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFNaEtNLy9Tc3p1M2UvalVOcWVqelE9PSIsInZhbHVlIjoiY1YvVHJtaVpReE1KKzRWOS9QVXpzWERnaVV6Z3V0aUp3L1hzU1VvRGtWT1dBaGYrbDhwNmpCSU15eW0yUE04Sms0dFg2QUdZbkkwM0NJZ3p3MmJhLy9qQmgwNDhMK3piNTFjaVpoMnBibFA2N3JkU283Q2tUajdERWhscVl5Y1BzYXptM3FsN1E3UzRsWi9Bc0ozbGMxUnBiQS9DWWZueUxpV3hVRTczWTE4dThRMk80SFBJbFdaQ0lBQzRRSTUyTjdlY1UxYlRRd1J6ZnBONzJGK1oySERYZFMxR2xPa20zS1pvRlRlQzZITDdlODZJV3Bkb2YvT2NERUhxVGxxZWY4OER0Q1dLNlNIazZCWXJMSlptN1lkMFBvd2dFR3JldmM2N2luREZMaUs1QmZjYlhBZWJLY0tIbU5rbVVOUFpaUjhURFhrVVc1bFZyaE1DZVVCanRZazBid2Q3cmxCczMxZ0tWRkc4UFJFd3BqVDY1Sk56SS9QUFVjdXlzTVVHWmhMbStteFlIWFFDb1hEaXJLaDE5WmFjc0hLM3pFUFpJUTNFVEx5K0M1WU1palMrRXBkMUw2V0xzNmg3SXA3SVpmVjNsY1NIdkE2eW5ub25IeWkvTkNBRXAycFFSUG9UdVZZUy9FaURHZmU5QXQvV1JmcFRPcDIxa2N1MHVXR2ciLCJtYWMiOiI4ZTU4NjFiMmUxYmIyYWRkNjRjMDJlMWUyZjQ5ZDEwYTAwMDQzZjgzMzJlY2JiZDc5ZWE4MDNmZGZmYmQyZTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439920341\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1543955663 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543955663\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:26:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndKbGJpQ2lRWEpaRFVzYy9HdXRJdlE9PSIsInZhbHVlIjoibTZVbEpTSVRwRCs3SGtFalFrZlNMOGlmNkZCWGVRMFBKSDFxTkhzZ213WVFoK2x2RmJKODRuZ2NOSTljZTZoTHhHdzdXbHRZT0lNeW9jQkdSa3k0UDVWanRDYWtsWVdEUXVGUlJ0U3cxb1VzanQxRXphaGd0dnB4U0VPZnpYY2Foc2ZWNSsxQkRiUEh6d0RuZUppVHVDV0lLZ0JLRFpjOFZDU1JOQytPZXB6SmxrR0dhQ2Fva0NXTDdxdWZ3N1FNNjZlcGs2Wi9hMWJDNmhWdVdKNzB1bTI2QmVsQStYalluc2JYeXBEMndPQjVyQXg1UFlENDFITjZ6WUZrNERtanpDeTdDN2FlWW0rdDY5b3dsL3hUcWZhSnhWRGc0T2hVR0RxMk5BZjB0eEdOeEJDRDl0MWNEVGk5YUxXcFpnUXJtazZKaFBnaWNpTG5SSDJrQkZCTERZcUlSazc3TCt5S0xFcTZGRnB4U2FPVm5McjVXZE80YjNWbmdpMDRuVU1sWVZsdlpVeVEybnhLOG5kTThhQXU4QVMra2R4ZVJCeDVWd3pSQzRpd0IwQ01UKzdIbzNuZzJsZFpTNmJobGVoNEZnekVZWjhNU21HUG1nQmc4ZGpBbzloWnVHRk1EM05QN1JITFIyRFVTQmxNRmJRekpnV05JQXZIdk1tdWJNSEEiLCJtYWMiOiJmYjhkY2FhMDc5MzYyMzAwNDVkZTE2MjUxNDMyNTA1MzlkYzE0YjEwMDNiMWQxMmM2MTgwMjAzY2E1NTg1NzI5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkcvbjBYUjN4Ym5BVE9rZ1I0RVA1MHc9PSIsInZhbHVlIjoiMjBxY0d3bmJ2SGZFYzY5OUllb2o3SldZOUFIaDJaWmJlZVdrVnVjTjNoR1hyMzhVaEtKVFpkc2J5K3F2S09MUzRzc2l2VmtNSmRxNGcwdmEwWkV2d2lNYWpFUWt4SWhWVExwcmNtVkFwL3JvTjJVdHJrb1NwY1BRb2llQlhqTDBiUm1KWXUrWUtXYTlETWVrV3JKdllDWVcyMnpiRXVST2Z6NGNCRTRYVDJ3c1JtOXpSbWtTSzNiNFRlUmE0VDRsUlVhd0ZvMFVwUE1PMW1pZXI3OUZGWUZKbW9kWUFqR1U3ZkdNY0o1bmVURm5DdGNMQ0JQNzF1L2Q4elBidTBuVGg5Y2tUdUNjVklMMTM3NFJzSWh1ZUNFaFdQRDExWHhOcFJ3ZDB3SVlwdS9DbnpCeC9OQnhicjA2ZjNSY24rQTM0bDBSTVFyV1dzWm5pVS9xNTJjcGRLS1U4c1VldklzcS9TTWNKZGF1a0YrSlJnVHJKWnU4QldPalU2eDhkc29EOXZOZkY5UWxXb1VWYWM2VEZEdDdycnlzNkFwMWdYVVR6aGlrTGZZeDZMcWp2TlROVVVTNEpaNDEvVWx0eE9zMHJiZ2VzNUhyNWgwKzJoMDdGZXQ3UFdGNmNUdUpVMGtYSDV5S2E5dEFzWDNWSlQxd1d6ZEl1d01NV3lkSkxubm4iLCJtYWMiOiJjODViODczNzMwZWY5MGM3NTMyOGQ0MmMxNDY0ODQxNDJkZjhlMDBkMjY3ZjljNjRiOTgxN2E0ODg3ZTQxYzM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndKbGJpQ2lRWEpaRFVzYy9HdXRJdlE9PSIsInZhbHVlIjoibTZVbEpTSVRwRCs3SGtFalFrZlNMOGlmNkZCWGVRMFBKSDFxTkhzZ213WVFoK2x2RmJKODRuZ2NOSTljZTZoTHhHdzdXbHRZT0lNeW9jQkdSa3k0UDVWanRDYWtsWVdEUXVGUlJ0U3cxb1VzanQxRXphaGd0dnB4U0VPZnpYY2Foc2ZWNSsxQkRiUEh6d0RuZUppVHVDV0lLZ0JLRFpjOFZDU1JOQytPZXB6SmxrR0dhQ2Fva0NXTDdxdWZ3N1FNNjZlcGs2Wi9hMWJDNmhWdVdKNzB1bTI2QmVsQStYalluc2JYeXBEMndPQjVyQXg1UFlENDFITjZ6WUZrNERtanpDeTdDN2FlWW0rdDY5b3dsL3hUcWZhSnhWRGc0T2hVR0RxMk5BZjB0eEdOeEJDRDl0MWNEVGk5YUxXcFpnUXJtazZKaFBnaWNpTG5SSDJrQkZCTERZcUlSazc3TCt5S0xFcTZGRnB4U2FPVm5McjVXZE80YjNWbmdpMDRuVU1sWVZsdlpVeVEybnhLOG5kTThhQXU4QVMra2R4ZVJCeDVWd3pSQzRpd0IwQ01UKzdIbzNuZzJsZFpTNmJobGVoNEZnekVZWjhNU21HUG1nQmc4ZGpBbzloWnVHRk1EM05QN1JITFIyRFVTQmxNRmJRekpnV05JQXZIdk1tdWJNSEEiLCJtYWMiOiJmYjhkY2FhMDc5MzYyMzAwNDVkZTE2MjUxNDMyNTA1MzlkYzE0YjEwMDNiMWQxMmM2MTgwMjAzY2E1NTg1NzI5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkcvbjBYUjN4Ym5BVE9rZ1I0RVA1MHc9PSIsInZhbHVlIjoiMjBxY0d3bmJ2SGZFYzY5OUllb2o3SldZOUFIaDJaWmJlZVdrVnVjTjNoR1hyMzhVaEtKVFpkc2J5K3F2S09MUzRzc2l2VmtNSmRxNGcwdmEwWkV2d2lNYWpFUWt4SWhWVExwcmNtVkFwL3JvTjJVdHJrb1NwY1BRb2llQlhqTDBiUm1KWXUrWUtXYTlETWVrV3JKdllDWVcyMnpiRXVST2Z6NGNCRTRYVDJ3c1JtOXpSbWtTSzNiNFRlUmE0VDRsUlVhd0ZvMFVwUE1PMW1pZXI3OUZGWUZKbW9kWUFqR1U3ZkdNY0o1bmVURm5DdGNMQ0JQNzF1L2Q4elBidTBuVGg5Y2tUdUNjVklMMTM3NFJzSWh1ZUNFaFdQRDExWHhOcFJ3ZDB3SVlwdS9DbnpCeC9OQnhicjA2ZjNSY24rQTM0bDBSTVFyV1dzWm5pVS9xNTJjcGRLS1U4c1VldklzcS9TTWNKZGF1a0YrSlJnVHJKWnU4QldPalU2eDhkc29EOXZOZkY5UWxXb1VWYWM2VEZEdDdycnlzNkFwMWdYVVR6aGlrTGZZeDZMcWp2TlROVVVTNEpaNDEvVWx0eE9zMHJiZ2VzNUhyNWgwKzJoMDdGZXQ3UFdGNmNUdUpVMGtYSDV5S2E5dEFzWDNWSlQxd1d6ZEl1d01NV3lkSkxubm4iLCJtYWMiOiJjODViODczNzMwZWY5MGM3NTMyOGQ0MmMxNDY0ODQxNDJkZjhlMDBkMjY3ZjljNjRiOTgxN2E0ODg3ZTQxYzM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}