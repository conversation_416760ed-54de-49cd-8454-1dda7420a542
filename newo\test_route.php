<?php

/**
 * اختبار سريع للراوت
 */

// تحقق من وجود Laravel
if (!function_exists('route')) {
    echo "Laravel غير متاح في هذا السياق\n";
    exit;
}

try {
    // اختبار الراوت
    $routeUrl = route('pos.store.delivery');
    echo "✅ راوت pos.store.delivery يعمل: $routeUrl\n";
} catch (Exception $e) {
    echo "❌ خطأ في الراوت: " . $e->getMessage() . "\n";
}

// فحص الكنترولر
$controllerFile = 'app/Http/Controllers/PosController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    if (strpos($content, 'storeDeliveryOrder') !== false) {
        echo "✅ دالة storeDeliveryOrder موجودة\n";
    } else {
        echo "❌ دالة storeDeliveryOrder غير موجودة\n";
    }
} else {
    echo "❌ ملف PosController غير موجود\n";
}

echo "\nتم الانتهاء من الاختبار\n";
