{"__meta": {"id": "Xeef50b263c14662e64d9b4dfb107a256", "datetime": "2025-06-08 12:54:40", "utime": **********.675801, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387279.247841, "end": **********.675838, "duration": 1.42799711227417, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749387279.247841, "relative_start": 0, "end": **********.450976, "relative_end": **********.450976, "duration": 1.2031350135803223, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.451006, "relative_start": 1.203165054321289, "end": **********.675842, "relative_end": 4.0531158447265625e-06, "duration": 0.22483611106872559, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45087000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.011139999999999999, "accumulated_duration_str": "11.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5564349, "duration": 0.006019999999999999, "duration_str": "6.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 54.039}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.587202, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 54.039, "width_percent": 11.311}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6264992, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 65.35, "width_percent": 18.223}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.648737, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.573, "width_percent": 16.427}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2005508254 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2005508254\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-331583645 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-331583645\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1407519991 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407519991\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387267916%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktwWUdOZFVEVTNNeTYybWp0SG1iNVE9PSIsInZhbHVlIjoiUmNBTldVd0NkR1FqMUcya2hvMy9ZbVRwU1haa2swRFlBdE5zYzZNZmtSVFowS0F1bjBUaGVKa0lSLytVQkV2Qk1mSm9ZTnd2Rld6aFBPSlpQUFFuQ0x1SE1US2NseERwREl6NUF6TjhYRWNjOWdKSFdSSkVDb1dpVW41V2wxa0ltaHJySmVKQTJnZFd1cW5QVVpZdUoyWXU3ZVoxSTFKRGVYQmx0S1V0MEw4OGdSaXBvcS8rN1E1aEdTYXY2c1NVWGxTWlF0WWFycFAyMmd2N1ZHNldrcHZWdUc5cGNzY2RTVzJYVThucFR3cGNNNGRRV21KbkxUYXVlYmliL0cwUHYzQWtya09SbmRoR0xxQ0hramtDN1hOWDRVdDFCVmdTcmpRTUxOa2I0Q0UwMkozVFc1aThvc1hxL1JlaDNlNjNGVHgwNHhwMWVyckxtQTUvSWpmL0RvUFdYSEhmUXNZNjY3aFZmSCtqakREQ00rYmJaV01wYmY3VXJRV05mQWdKQXBzSDFMODZYVlBXMXpMejFZc1BTamluaXYxa1M0WVIwNjlrbzF5NHhyeUtvaUIwb0JVYXVBaXg5RHRVdEx2dTBDaWo5OXlmVlpHOVZqR1JXeThMTFMvMzMzTytDZzhEVTltbGVSMDRJRG9Wdk9tYkZGbWZDNjQyMUNVUzEyUFUiLCJtYWMiOiI4Y2E4NmJjY2RmZDlmZTVlYmMzNTFmZTcyMzQ2ZDUyMWZlNzExZDIxNjM1YTZlOTIyMDZjNjVhYjYzMTAwMjVmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlQyaUZEOTRhZHNOKzN3QnFBcG5PeHc9PSIsInZhbHVlIjoiWjg3elJmMjR4ZFF1RnppZVdpWStlS2tST2Yxbk1pVDJsZW45QzNYUTdyZzA5U2FJL1NqR1N0cngwNFNpZENVYnlybEpTdmRQMHJickE5Z2Nlczd3R3AyTXhyaXoyM3Z5Q3dRWDg3QzcyNU5ZQUVxZEJaK0FrUnorMjNwa3M0K3VyZmN5Q2Y1NnpadzNFandOUGhwYU41KzVnanNlWkplZWoyVzRmTlF4K09MZ0FCYis4Uk9rL2psOUpJRnhhT0U0Ly9TYnF6OWNvSTVVcE9zYldZTE1ES2N6NzdCT3lWOVNUK09ZK1hkTk9WTVg5Q05kUjhXaWdIRVdvM2JpRjhhaXJjd05CdjVIcTcrazJQWGE3c1pvVGJxNWw1NXVhMFZhTHlkTS9zSGlhWGllOWZqdEJuQmhnaFkySXN0WDhXd2dMUDU4ZXpadUdyYWVSQ2pVeWcwZDIxdXRua3RWYVJQSzJqNzM4ZXVkNHVoSlJVclVYVm9ySmt0eWFmL2g2aFc2VnZyc0YraVB4RUJaZWR4WkpUdEJ2V2d3cVhsNmY5TlFGcGFFZTY4eFA5SUZGVXQzZ2VIOFliVExFd2swWm9DZEpqdFlxanpIeEJJbkc4MHNZV0VIVG9GWHN1WlFEck0vaU0zSVhWTmorS29rQzVLL2ZNL2lEd1BNdmc4RUhCeHYiLCJtYWMiOiI0YjQwYThlZjM0YzI2MDUyMDgwNWU4MWQwYmJkYjAwYmJjMGM1ZTBlYTUyOGZmNGVhZTVkYzBkNDg4ZDYwMDFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-149573873 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149573873\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkF5QW1rc1VEQlFQaG1YQk81VEFyY0E9PSIsInZhbHVlIjoiS3k4a2hGcmZzUGJHNXBnZGhKRHU5MXZ5bVhadURzUWRuL3RoU2VuWGJPVm5SeUlKa1F2VUhYQkpoNlZoRjdFUzJNbWhReHNXd1gzSDJhNTJMRFVLUkhXMnFDUmJyWEw1YjAvRzdLRm9Rc0l2SjM0UHJNclpFWGRLa0xsM2VPcGw2R1lhOFRQd0UzbnM2NHJpQS82ZUZ0OTNpcFd4aDB4Yk1jU1YxVk9WaGpoeGdyL0ZGUnd1bkYxZ0JlUkFlaE1CYy9NamFpNXZYZlh4NXg0RzNLS01kbTlRT0VMT0Q2dkV2Z0NaR1VUQTc5RmVuajdTTEVRMCtrenBxYmRzTVhqWFdpYUNhSk1iaVA1QjZnclVYck0xYTBGTzNaQWpORk1vYVp2RDNRTWNPTmZ2Q1UvNXR6aFZ0T05LWDhwWkx0NGFVWUxtbjk5NzFHcjBnRHZFd0VYSVFGOU5MNzNjWWV6TFJwK2s1bWNOS2VuOWk4K05VR0Irc0c2dTlTdE5iZlFJclFFdHFHZWZuY0syd3huRnZxRUV3aDVSSGFDV3hBQWIrSjRBamJjd0Via0ljYUlMRjVsenpDUE5JeEwxbkZmYWUxeWV0blhtUEczeG4xMjJsUlQ0Rmc1OWxYWDJ4cGhBVTh6Wm96c3JkUXRJU2FrdlB0YkdDbko1VlhJWERKWnkiLCJtYWMiOiIwZjlkNzVhMmY5NTVkNzc2ODQ0NDFmYTU4YjE1ZjJmOGZkYzk4ZGU5NzhjOGEwNjVlMzkxYTZjZmJhZDRjZmZmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVVS1hSbkhyNG51bCtVMVMwbkNjUkE9PSIsInZhbHVlIjoiNVlXOURjWjVpVi9UQVNsSHR1KytVSUdaZGhnSzYvQ0lDa3B4dnYzVWpIdFlDMVhKQ1NKWlBjM1hCQ3pCVDBqRkt0eEdxdnVJVW92YUdMcHhtOE1WalFGeG5KbVFQaGpIMm1aN1NkWjVvTkExc1ZEUnNXaFhQbzdUU05HbkpxNElNMjl3cXVTenVFeDY3ZmxhRlE3bmk5L292aXhvUkhCYzlKUkpyVksyMnE4cWkzRE5ta3FQMTBna0g0aERoTVdMWFB2Rjl1SEtjTGpYWnB6cU4razd1enA1eVRDa1JIZ2RISWxEMko2b1RLUTdOYmdYNnNBR2RnUVVGVFFrajBNRngvUlBqaUxSZFRQQWpmVEZGdlM1Y29yY0ZaOFBDbWdRRjU1NVdMRkdTUVNzTXRpZFJjSUFVQVE3RWN2T1phQjc2ZUFqT0UvWXVUZWR0MXhMV0psWGVjRzZMckRUamJYSTZ0VFE1ZzgyZ0ErZVJaUm8vRVdqdGZuclRMQkJIYU5GS0xKS1FnU2ZMT0pNZ1Q5bmNpZXlVQ0tOSmROMzVQZnY0UUFOcndHaG9lTnc0RHQrUlZEQXd3TTFrUzF4UVdhMlFQdkdGRnB6a2tad0JCTFMycGt0cEJQeTdHM0lnTVBUNTY1ZEdZM3hTUU05ZnV3SFc3djVLVjdWV0lmQjR2MEciLCJtYWMiOiJhMTNiZmJhMmExMjIxMTgwODMyY2M4MjRhZDk0MTU4MDMxYWIxMWJhYjdlZmFmNzkxMTcxY2UwYmE1MmQ4ODU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkF5QW1rc1VEQlFQaG1YQk81VEFyY0E9PSIsInZhbHVlIjoiS3k4a2hGcmZzUGJHNXBnZGhKRHU5MXZ5bVhadURzUWRuL3RoU2VuWGJPVm5SeUlKa1F2VUhYQkpoNlZoRjdFUzJNbWhReHNXd1gzSDJhNTJMRFVLUkhXMnFDUmJyWEw1YjAvRzdLRm9Rc0l2SjM0UHJNclpFWGRLa0xsM2VPcGw2R1lhOFRQd0UzbnM2NHJpQS82ZUZ0OTNpcFd4aDB4Yk1jU1YxVk9WaGpoeGdyL0ZGUnd1bkYxZ0JlUkFlaE1CYy9NamFpNXZYZlh4NXg0RzNLS01kbTlRT0VMT0Q2dkV2Z0NaR1VUQTc5RmVuajdTTEVRMCtrenBxYmRzTVhqWFdpYUNhSk1iaVA1QjZnclVYck0xYTBGTzNaQWpORk1vYVp2RDNRTWNPTmZ2Q1UvNXR6aFZ0T05LWDhwWkx0NGFVWUxtbjk5NzFHcjBnRHZFd0VYSVFGOU5MNzNjWWV6TFJwK2s1bWNOS2VuOWk4K05VR0Irc0c2dTlTdE5iZlFJclFFdHFHZWZuY0syd3huRnZxRUV3aDVSSGFDV3hBQWIrSjRBamJjd0Via0ljYUlMRjVsenpDUE5JeEwxbkZmYWUxeWV0blhtUEczeG4xMjJsUlQ0Rmc1OWxYWDJ4cGhBVTh6Wm96c3JkUXRJU2FrdlB0YkdDbko1VlhJWERKWnkiLCJtYWMiOiIwZjlkNzVhMmY5NTVkNzc2ODQ0NDFmYTU4YjE1ZjJmOGZkYzk4ZGU5NzhjOGEwNjVlMzkxYTZjZmJhZDRjZmZmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVVS1hSbkhyNG51bCtVMVMwbkNjUkE9PSIsInZhbHVlIjoiNVlXOURjWjVpVi9UQVNsSHR1KytVSUdaZGhnSzYvQ0lDa3B4dnYzVWpIdFlDMVhKQ1NKWlBjM1hCQ3pCVDBqRkt0eEdxdnVJVW92YUdMcHhtOE1WalFGeG5KbVFQaGpIMm1aN1NkWjVvTkExc1ZEUnNXaFhQbzdUU05HbkpxNElNMjl3cXVTenVFeDY3ZmxhRlE3bmk5L292aXhvUkhCYzlKUkpyVksyMnE4cWkzRE5ta3FQMTBna0g0aERoTVdMWFB2Rjl1SEtjTGpYWnB6cU4razd1enA1eVRDa1JIZ2RISWxEMko2b1RLUTdOYmdYNnNBR2RnUVVGVFFrajBNRngvUlBqaUxSZFRQQWpmVEZGdlM1Y29yY0ZaOFBDbWdRRjU1NVdMRkdTUVNzTXRpZFJjSUFVQVE3RWN2T1phQjc2ZUFqT0UvWXVUZWR0MXhMV0psWGVjRzZMckRUamJYSTZ0VFE1ZzgyZ0ErZVJaUm8vRVdqdGZuclRMQkJIYU5GS0xKS1FnU2ZMT0pNZ1Q5bmNpZXlVQ0tOSmROMzVQZnY0UUFOcndHaG9lTnc0RHQrUlZEQXd3TTFrUzF4UVdhMlFQdkdGRnB6a2tad0JCTFMycGt0cEJQeTdHM0lnTVBUNTY1ZEdZM3hTUU05ZnV3SFc3djVLVjdWV0lmQjR2MEciLCJtYWMiOiJhMTNiZmJhMmExMjIxMTgwODMyY2M4MjRhZDk0MTU4MDMxYWIxMWJhYjdlZmFmNzkxMTcxY2UwYmE1MmQ4ODU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1796881195 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796881195\", {\"maxDepth\":0})</script>\n"}}