{"__meta": {"id": "Xf52f97c2ea4e7d152e4f84031519f0ad", "datetime": "2025-06-08 14:22:52", "utime": **********.593441, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392571.157571, "end": **********.593472, "duration": 1.4359009265899658, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749392571.157571, "relative_start": 0, "end": **********.39548, "relative_end": **********.39548, "duration": 1.2379088401794434, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.395499, "relative_start": 1.2379279136657715, "end": **********.593475, "relative_end": 3.0994415283203125e-06, "duration": 0.19797611236572266, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45589584, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02067, "accumulated_duration_str": "20.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4951198, "duration": 0.01744, "duration_str": "17.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.373}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5426, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.373, "width_percent": 6.047}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.561956, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.421, "width_percent": 9.579}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 18\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 38\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1831818089 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831818089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749392377642%7C54%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjYxZ0lKdHllTllqS1ovbWpYcEg5WGc9PSIsInZhbHVlIjoiY0RTamxsc1owTXNTTS9mRGltTW9FZmlnUWVUc28xRjcxdXk1MVV3Uk9PaEV1MUR4VnFoakhVcVpmd3NpK080cmRMNjJ1ZFQ0bFJGaTQ5dlBiQ0NXNEc2bzNOM3JnZ09QZm1UdUQ3TUJIaURHWDVSd0w5b3VFSnkzZEd0WDlGYTd6cXdjTUkvVldVVFhvYy9MWDNuV1VJa3RXQUhFelNqVXdEaVN5cUdQMzVINUJMcWlrVVVwMml2cTVON1d3Q2p3Ty9GdVIrUnkvNUx4SWNsSXVRV2VaQVNyaEZHK0FyZ1E3WVB1WDNnaGZ2R3NLY0t1VUJaY0lCNm1QV25sZ09vbkhZdSs1QldZc05MM2F2STdBZWpUTGVOaXJzNXV6cFJDd3h5Q0xsRzIyUG1taEdRNWwwUWlRUmEreCtyeWVFY1FJVTFuREJPUDBTWEl6MUhtQlIvcE5RaUp6S0JTN0k5OGFPTldMQzVmOEJQNEdYYU81ME9odHA1TUFnaVpEc2lNc2Z4S3JBU1VPT3YzVTFWaVJzc3FEQlV5UkNDYkg4M2l1YU5jRkU1cFRQdUdhOFdGS0FiOGF3WTRicjRzSjA5YU1GYWdCU24yYUpUc1NMYnVhL2tscC9rMk52cDAveHl5TnNxVlU4ZEE2MmcwMlZNRzFHajYwbkY5RjMyS25Ed3MiLCJtYWMiOiI4ZDE1OWM3YTE5MDc1ZDMzN2I2NzlhYjc4NjRmYWU1ZjdhYzA0NTM4YjhmYzEyNGQwNTNhNjY4OWIxY2QzOGVhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJpT2c4cnR2aldGeVdSaTE0K0x5U0E9PSIsInZhbHVlIjoieHlhTzJLTms2WjMrR2hUbE1iSmJvNnFZNllKMytPd0RZVWFrUTVteGM1SVhyWnQzalkydTY0cHZJYlQ1QnFRdHFTNFhDdlhiYjc0Y2N3bzh1NnFwbTY2YVBhZTdlSkgwMkFUcVB3RjNzZ2tDeXRpd2V0N1lIUklQbzd5WHlILy9IZE9nZ05xTnZER1VWbk5Gd2ZlUEl3L2xsWm5Hb3F6VXNITVVqRHBqY2JES1E0MldkWENKemVaSGU3UXdyYzZTZFIrSkxRY3NxaGl2clFVQ09PK0oza0xwTC9kYTVPRnc5MmxZZENwblo1eEVkUGdKbE92Wkw0djNNTm9ENEo0amc4WXl4YmJiUDNWbGszMGtVT2hUUDlMajZySmRXRTVFRzRkcGxSQnluN252V2x1b0laTW0vcW5aUTZEMjZCWGM5eW1VYnNGQStZb1hYRnM0YStzZnVsbFA5cXZXbFJZSzYrVzhjV0NHSUZiQ29uSXd5WTZmUjFibm1SSk15Q2t0YjZROERkbGdzcW9Ndis3ZjlDTFlEYkQ4em9leWFpMFlhTTZmOUltK0RINjlSd3BrSzFxSGt2NjJ0bVZSSXpOV2pZQVU1ajNEMi9ScklhTUdOaDlsc09kc25NenBnNmdjeFIrU1FPYS9mNmZ0TmYxVnY3S0RvSElHZEd2aE82OUkiLCJtYWMiOiI2MWJiZGUzMWY1YTczYTVlZDhlNTVmYmM2N2JlZTIxMmI0MGY5YTYwMGVmMmRlNTJhNTcxYzE4OGRiZGY3YmZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:22:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdTMlZ4bkNselFTelRrL1hVQWo4Y3c9PSIsInZhbHVlIjoiWDlVZXNsUWgrWkpIb0tYemN2YUNJTjFzQVdBaEtQN3V3RlBpVUlpdUNBVjJaaGhUZ2FZSVFLbHUwb0xuS2NXRjdNZExETC91b1BVb0QrMjFXR0JHaEJBNUJMMmlRZ2F3VzVPcmpQdU9aRUIwaFRJUVA2T2UrRVdpREJZbTBVZTFqY25EVHYzNTc3VGdDaW56blcwRGJpSkRxcmU3Z3BrbFNnUEhmZ0tZeEtSZzZWbXQ3YVRyUC82VEY1Si9ySDRpVkI2MkFMNUhQSytEczhQVXdudUEwRHMzZ0dCdnhOT3NOcUY3d1lDVkY0eHg0NTM5RWMrOStHZlRKQ3lwa1BGYWdSMWRDM1JlTm5GUUpMSm54a1Fwak9Sa3pFVHh2ZUMzN1dGbmRJbHJuUkpvSUxLZkV1eFFTaTkvRW51dDVLUjI3TXlHYlNyRGRsbEs1akptM09IdEhEYXZCaE5BTmxGc1Z6b28vYVlueGsrWFdpem1PT1lFc3oxSDRrUHZDVWxRdStYNDE5T1dIbzRvMU42M0M5N0lTeFRqUWJidlBBUlFLS2Y1NnZNSnNtTTJhV1ZnZnlIT1JDSklZQ3NvUm5PRjVmQ0dEbGZLdnlOYTJ6T2hvclEzUGY2MlZkcEdaYXVlYTh1NzJMUENaUUhFUFA4K2tSZS9PelJhKzdKUjVDb0IiLCJtYWMiOiI2MmIwODBmZDc1MmViMWNjNWZkOGM0ZWU3MDEyNjAzMTVhZTMxYTM3MDM0YmQ1YzlkNTQyN2E1ZWJhOGYyNDIzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:22:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilg5NHAvQ24yaURYbExBci95RGk3a1E9PSIsInZhbHVlIjoicDQra2dxdU5SS1JTR3JqbTNBKzRjMUpkMU91bHE3aTZvQzhXOGdlaVFsQ0lOVFNpQXNWcEdxMlo1cWF3ZW9uRG1jVDlQUTdublZBcGNYQ0VSc3FNa0l1eXZ2Tm5udWVPWFcrbHJ3NmE2SUxQQ0RSYUx4bGFYaTBaSXBiaUIwSTJBSnpIUHpqUW9NYU8xSTZlbUtLOURnY2tjS1U5bkVSRkxFbUN3aWNDZ0p3YXhEUVE3NUN0akQ0L1g0YkpJUVB6bkNYTjVMUW9sdEZYSmI4SHc3UlJzOE13Vk9SeEdFcnV0VndBV2ZyMEVtdGQ1emRPTkFYZzB6Z2pMUWdLdzlwci9kNkxXZ05VQnBSZzBGanJvcGFOd0k3bDAxYU9Wd2M1VDh2RnN1TjM5SXR5TWUxSHpSanRiR1phWFgwcW1MT3M4T3VtL1dVOHNDbHNwclh5MTFyaE9oaTBqSTlpQXRaKzh3L3IvaThXN1lSakdOMG8zTWM5QTRxUlRROUF1TXd2SnhyM21OOGJJbTgyMllMNjJ3UmdZb2dpQXdKTURqMWQ0SGMvMFVxYnFZTzA0WUJ3SmdiRGs2SkZzZC9VR29FRm0rVTNlTTFUYm5YcWt1Wm5YSGtOdHlXVmlwUXEyY2ozZ1BJREFNbFRweXcyNW1Tb3FIdEZYdVl6VW0yYU1PSUciLCJtYWMiOiI3NzFkNGYxODFhNzM5ZWY3NTI3OGFjYmQ0NDZiYjRjY2UzODVkNzEwZDlhZTM3YWQ5MGY5Y2IwOGU5NGQzNzRkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:22:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdTMlZ4bkNselFTelRrL1hVQWo4Y3c9PSIsInZhbHVlIjoiWDlVZXNsUWgrWkpIb0tYemN2YUNJTjFzQVdBaEtQN3V3RlBpVUlpdUNBVjJaaGhUZ2FZSVFLbHUwb0xuS2NXRjdNZExETC91b1BVb0QrMjFXR0JHaEJBNUJMMmlRZ2F3VzVPcmpQdU9aRUIwaFRJUVA2T2UrRVdpREJZbTBVZTFqY25EVHYzNTc3VGdDaW56blcwRGJpSkRxcmU3Z3BrbFNnUEhmZ0tZeEtSZzZWbXQ3YVRyUC82VEY1Si9ySDRpVkI2MkFMNUhQSytEczhQVXdudUEwRHMzZ0dCdnhOT3NOcUY3d1lDVkY0eHg0NTM5RWMrOStHZlRKQ3lwa1BGYWdSMWRDM1JlTm5GUUpMSm54a1Fwak9Sa3pFVHh2ZUMzN1dGbmRJbHJuUkpvSUxLZkV1eFFTaTkvRW51dDVLUjI3TXlHYlNyRGRsbEs1akptM09IdEhEYXZCaE5BTmxGc1Z6b28vYVlueGsrWFdpem1PT1lFc3oxSDRrUHZDVWxRdStYNDE5T1dIbzRvMU42M0M5N0lTeFRqUWJidlBBUlFLS2Y1NnZNSnNtTTJhV1ZnZnlIT1JDSklZQ3NvUm5PRjVmQ0dEbGZLdnlOYTJ6T2hvclEzUGY2MlZkcEdaYXVlYTh1NzJMUENaUUhFUFA4K2tSZS9PelJhKzdKUjVDb0IiLCJtYWMiOiI2MmIwODBmZDc1MmViMWNjNWZkOGM0ZWU3MDEyNjAzMTVhZTMxYTM3MDM0YmQ1YzlkNTQyN2E1ZWJhOGYyNDIzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:22:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilg5NHAvQ24yaURYbExBci95RGk3a1E9PSIsInZhbHVlIjoicDQra2dxdU5SS1JTR3JqbTNBKzRjMUpkMU91bHE3aTZvQzhXOGdlaVFsQ0lOVFNpQXNWcEdxMlo1cWF3ZW9uRG1jVDlQUTdublZBcGNYQ0VSc3FNa0l1eXZ2Tm5udWVPWFcrbHJ3NmE2SUxQQ0RSYUx4bGFYaTBaSXBiaUIwSTJBSnpIUHpqUW9NYU8xSTZlbUtLOURnY2tjS1U5bkVSRkxFbUN3aWNDZ0p3YXhEUVE3NUN0akQ0L1g0YkpJUVB6bkNYTjVMUW9sdEZYSmI4SHc3UlJzOE13Vk9SeEdFcnV0VndBV2ZyMEVtdGQ1emRPTkFYZzB6Z2pMUWdLdzlwci9kNkxXZ05VQnBSZzBGanJvcGFOd0k3bDAxYU9Wd2M1VDh2RnN1TjM5SXR5TWUxSHpSanRiR1phWFgwcW1MT3M4T3VtL1dVOHNDbHNwclh5MTFyaE9oaTBqSTlpQXRaKzh3L3IvaThXN1lSakdOMG8zTWM5QTRxUlRROUF1TXd2SnhyM21OOGJJbTgyMllMNjJ3UmdZb2dpQXdKTURqMWQ0SGMvMFVxYnFZTzA0WUJ3SmdiRGs2SkZzZC9VR29FRm0rVTNlTTFUYm5YcWt1Wm5YSGtOdHlXVmlwUXEyY2ozZ1BJREFNbFRweXcyNW1Tb3FIdEZYdVl6VW0yYU1PSUciLCJtYWMiOiI3NzFkNGYxODFhNzM5ZWY3NTI3OGFjYmQ0NDZiYjRjY2UzODVkNzEwZDlhZTM3YWQ5MGY5Y2IwOGU5NGQzNzRkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:22:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>38</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}