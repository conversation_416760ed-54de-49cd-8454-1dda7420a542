{"__meta": {"id": "X796a6b2a854d47746dd88e5236bd84e3", "datetime": "2025-06-08 14:46:27", "utime": **********.487875, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749393986.899772, "end": **********.487894, "duration": 0.5881221294403076, "duration_str": "588ms", "measures": [{"label": "Booting", "start": 1749393986.899772, "relative_start": 0, "end": **********.37079, "relative_end": **********.37079, "duration": 0.47101807594299316, "duration_str": "471ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.370801, "relative_start": 0.47102904319763184, "end": **********.487897, "relative_end": 2.86102294921875e-06, "duration": 0.117095947265625, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.018430000000000002, "accumulated_duration_str": "18.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414226, "duration": 0.0149, "duration_str": "14.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4401958, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.846, "width_percent": 3.31}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.462325, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.156, "width_percent": 3.581}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.466009, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.737, "width_percent": 3.798}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.472785, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 91.536, "width_percent": 5.426}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.477616, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.961, "width_percent": 3.039}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1769526981 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769526981\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.471426, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-995954264 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-995954264\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1506938631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1506938631\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1000186577 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1000186577\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-998502299 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749393346722%7C61%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhyLzFuUnlGa216N093OTJxdjMzQXc9PSIsInZhbHVlIjoiSU9pOUpxZ2d4Q0xsYU1UZlVUak9kQlRhbStsVW5nYWNBYnlGTXIvMEZub1pnZjRpdlpscUxzRWpUZ2tQNXFnd2xGTXNwUHRhQ2gvUFFJSU9iWThab05lZUN0L2x2N05QLy9yMFk1WkpPb1JObktBUzVxSDJYWTJoMlZPOXJVUlk0YnRjamFtL0VNTUdRU0k0K2NLUGk2S3N1KzluRjVDT08wQklsU2g0ZzVuek9yUDNGZXBJOVJscENRWHpRWEE1MWROejBIR0JieVh1Vk1MY3lrQWh5N3lUZWxVSktRWXdUMzIyNDdDblhBY2lEdXJ5SHZJRXViL0l4U21XY3VVTTVTMUEyLzV1VlJXa1JtZVhDYVIrQTZIUEJ5OVowRG1oZk1jZGJGQkZ6ZXMvZjBNRk9GcVVCbm1jNUVrM1VLT29ZM1FVSDlYd1pkeE83S3VUa243aENlOUpyc3hSOVRaYjF0MjhyZ0tIZUFvbFNyQnJOZ2NQVEJPZUF1bGdKMGh6eHZIL3Q1NzJ4VmJ5L3daUGNQZDdzUXBLdzZ0S2MvQTdvYjdkWHE5QzhDdkVWNWpBOHkvKzNiSXh6Ri8wUVF5TWJmWmgzZlhINHM3cnh1cnRpelp4Vit2TStTNzlBLzJQVVhwTlRUOGliSmFRVUViejVGZ0RKMEJDdFljVVlxbHAiLCJtYWMiOiI3OTIyZDhjMmViYmYzYTVhMWZhYTFmODM5NGEwYmEzZDRjZmY1YjU3YzUyNGMwNjlmYmMzMmFiMThlMjE4YjhjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVKcG5vNlpwT2Rac3hXc0taaUdWaVE9PSIsInZhbHVlIjoiazE4dktobnZoV2NYQXBDVjlJWGxORDFHQm1TRGh2Qk5HcWpvazZFVGZJM1lJM3NzSmVESWxVdTdHTjIzUTdJUUlVNlM1OTc5aE0rektWQlVzcFRBYzQ3TFBkY0grZFJ2b3J5ZnQrNnRRanpRQThrUlF6WHEyVUV1LzEyTDNGaHhxUDFDT3JHcWdtNkJPL3RKN25UM0RaUngzd05OVXhKa2x5WWxqWmh5TlJtczJzNmF3bzExU1RxNm1jOS8zK3MyKzFmUExpTXpoWWVMWTh0QSsxRy96akNSVGFqeit1RGhHaGt4VW95MklrbmlCaitpV2Z4d2EwYzNZWjZjb3ZTMFUyQmtRMTltSmVpa2lCa2xtWForeWNVZFNSY1M2Mkh5UzF3T3dyeVlrVnhaOVpXWGxqWGR0aWhDUzZua2tKOUtQMjZqU2RFRVBCanhzNE1kOC9ZMkl6TkdGdU5vMXh1TERheFZ4MENWL0JNQ0NBTC9wR0g5aFREUGFSKzd5bktxR2NScnY3eUtLNDdxbzJSU3pEVnNFYTZGWXlxT3BYNzNUMUswZ2NRVW1JbFhGeE1UdHVNZkVzMWs3SmJ2eVR1N0UvWlp5U3FYSlRURTdsQ3RiUVBTZy80RkZ0ekVnZmxiTjczcUkxTjdBTTRDWFhndWc3VjQzWnZoMzJPMlR0WGwiLCJtYWMiOiI4YTExNmYzOTYwOGM0ODc4YTRhNTNkZWNkZTU3MDRkMmIxMjcyMDE0ZjA1MDk0YzUxYmI5OTc3NWI1Y2FmZDU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998502299\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1663108991 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663108991\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1378154651 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:46:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBGR2lkNnh6WmxnWHY2NWFCVlo2Nmc9PSIsInZhbHVlIjoiL2NqVXNXU25oWWZjanl4RjA0dUZzY3Z0Tm9IcG4zV0ZvTVVTMy93K2VrZERYWEo3LzJQci83V21yZW0rZXUyRmxMdWw3NVEwVnFhNjRiMjdzZmZxcEFaMExGemg5NjIyMWhkeVZQbjhtbkowZVBGMHVOYVF6c2FIam9ZNDNKMWhpYlVIeVd2ZVBYVXptbWk2WE9DVmdxVWhJNER3WjBISWFQdjk0bG4rUUk1REFsOWVNamJOejg1OXQxclVRUVg0V1hCakNRZEU1UVRMZU1wdVVlODdwOHdyQjVOdE9ubnQxejV3OWREVmw5Y09JTzQ1TmFCTWNjS2F5T0tlaEZrd3FySWRBTEVYOG1Cdy9XQlU4TTNUMXJTMHRRWXZXejIvVkFRa2FWUXh2VTVLTFBsK3VyMVVjdlNSdEY4SVJ2SFY4SmF1dWM5NERRQXluZjBqYTlodE5uT1liTHYvSzNndE1PakxXVkJwNFpMUFFDZFgvQUQ4czN5NmZBWlFHdU0ycUtmTVdoclNSRjNYVFNFa0U0WDJrK0pUVG0vcHFTWldscVdrR2h4SWNjZjNzTDRGMjg4NHBzNXg3MVNCeTdMbGlRc0pNNjhlOXZ5OGtCenlCKzIrNnRhdHFxMGtZNGczWW1zbzF5KzRNY3Rya0gzdERPVnVObnJWblA4TUtTRVoiLCJtYWMiOiJmMGJmMGJlYzM0YmIxNzIzODQ3NmJjYTgyNmFjZDE1YjJhZmZlZTc0NGU2OWNlY2NmNDdkOGQ4ZGQ4YjYxMjgzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZ0REFxT1lhNUNkY25wdzA5Smg5dlE9PSIsInZhbHVlIjoiaWdPcTlMODhycUZIRWkrYm9aYXBGdWZxdnFTNHFDbDhyc2Q1Qkl4SlRvNEtyRHpaTnlPRVFSV055QlJLd2kvcnVtZGVIZ0ZQYkZjNmVuU2dQakZMdUFEM28rYVc3b2tubEpaU1UyY09Zc2tCOVBKeW5PZlFMTEdFbC9acTBFSmhzT2xVZ1Q1TkNOSnV0UWZDNTdFWEhxSUtBV0JxdG9iQm50VUZxTnBVdnZpTklXVmFOZGx6aGorWFVXK1g5d3FrNTJhZ1RnMEcxd3RHME9VbjZ4SFRocmZUdUhqS2dZTFowREdtdUlZclMwNkx1cDFGcGYwVEZIMW9sRTladHQzaVZhazExelZ3OUVKL3BEcVc0N2loRlNLYXdNU0R1K0kyaVdQdTdWdjYwWEUzRmtxMlhaaGlFemlQemFqWXB1Y3IwdnBwNGE3ZXZTQUtoMUFRT2tGbjQ5czlyYkszQktFOWFoZXJMenRsTUpCTitQL241ZGN0Z0MvT1JIcGE5RVJ0ZTA3b2xRNEtsdnZCb3I5L1A5TkRkNnpwMVFDS1BJVFREbFQ5TjNNNGh2cXluNHEvbHZXUDljcnZJVVZweEw0ZWNhTC94bnRHN3U0TmV6OXVyd2d0emhwbTRaRTA2dmRGZFRTdDYzYytpVDNIZkRLTjNCREJOaGtpcmRkdDk1UWEiLCJtYWMiOiJkMWViZGU1NmQwNzYzODc4NjkzY2I3MmQwMGYwYzA2ZDU5NTUyMDA3MWVjYWQyZTM4OTkxMGMwZmUxODNiYjllIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBGR2lkNnh6WmxnWHY2NWFCVlo2Nmc9PSIsInZhbHVlIjoiL2NqVXNXU25oWWZjanl4RjA0dUZzY3Z0Tm9IcG4zV0ZvTVVTMy93K2VrZERYWEo3LzJQci83V21yZW0rZXUyRmxMdWw3NVEwVnFhNjRiMjdzZmZxcEFaMExGemg5NjIyMWhkeVZQbjhtbkowZVBGMHVOYVF6c2FIam9ZNDNKMWhpYlVIeVd2ZVBYVXptbWk2WE9DVmdxVWhJNER3WjBISWFQdjk0bG4rUUk1REFsOWVNamJOejg1OXQxclVRUVg0V1hCakNRZEU1UVRMZU1wdVVlODdwOHdyQjVOdE9ubnQxejV3OWREVmw5Y09JTzQ1TmFCTWNjS2F5T0tlaEZrd3FySWRBTEVYOG1Cdy9XQlU4TTNUMXJTMHRRWXZXejIvVkFRa2FWUXh2VTVLTFBsK3VyMVVjdlNSdEY4SVJ2SFY4SmF1dWM5NERRQXluZjBqYTlodE5uT1liTHYvSzNndE1PakxXVkJwNFpMUFFDZFgvQUQ4czN5NmZBWlFHdU0ycUtmTVdoclNSRjNYVFNFa0U0WDJrK0pUVG0vcHFTWldscVdrR2h4SWNjZjNzTDRGMjg4NHBzNXg3MVNCeTdMbGlRc0pNNjhlOXZ5OGtCenlCKzIrNnRhdHFxMGtZNGczWW1zbzF5KzRNY3Rya0gzdERPVnVObnJWblA4TUtTRVoiLCJtYWMiOiJmMGJmMGJlYzM0YmIxNzIzODQ3NmJjYTgyNmFjZDE1YjJhZmZlZTc0NGU2OWNlY2NmNDdkOGQ4ZGQ4YjYxMjgzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZ0REFxT1lhNUNkY25wdzA5Smg5dlE9PSIsInZhbHVlIjoiaWdPcTlMODhycUZIRWkrYm9aYXBGdWZxdnFTNHFDbDhyc2Q1Qkl4SlRvNEtyRHpaTnlPRVFSV055QlJLd2kvcnVtZGVIZ0ZQYkZjNmVuU2dQakZMdUFEM28rYVc3b2tubEpaU1UyY09Zc2tCOVBKeW5PZlFMTEdFbC9acTBFSmhzT2xVZ1Q1TkNOSnV0UWZDNTdFWEhxSUtBV0JxdG9iQm50VUZxTnBVdnZpTklXVmFOZGx6aGorWFVXK1g5d3FrNTJhZ1RnMEcxd3RHME9VbjZ4SFRocmZUdUhqS2dZTFowREdtdUlZclMwNkx1cDFGcGYwVEZIMW9sRTladHQzaVZhazExelZ3OUVKL3BEcVc0N2loRlNLYXdNU0R1K0kyaVdQdTdWdjYwWEUzRmtxMlhaaGlFemlQemFqWXB1Y3IwdnBwNGE3ZXZTQUtoMUFRT2tGbjQ5czlyYkszQktFOWFoZXJMenRsTUpCTitQL241ZGN0Z0MvT1JIcGE5RVJ0ZTA3b2xRNEtsdnZCb3I5L1A5TkRkNnpwMVFDS1BJVFREbFQ5TjNNNGh2cXluNHEvbHZXUDljcnZJVVZweEw0ZWNhTC94bnRHN3U0TmV6OXVyd2d0emhwbTRaRTA2dmRGZFRTdDYzYytpVDNIZkRLTjNCREJOaGtpcmRkdDk1UWEiLCJtYWMiOiJkMWViZGU1NmQwNzYzODc4NjkzY2I3MmQwMGYwYzA2ZDU5NTUyMDA3MWVjYWQyZTM4OTkxMGMwZmUxODNiYjllIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378154651\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-538491207 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538491207\", {\"maxDepth\":0})</script>\n"}}