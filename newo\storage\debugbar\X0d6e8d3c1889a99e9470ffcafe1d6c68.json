{"__meta": {"id": "X0d6e8d3c1889a99e9470ffcafe1d6c68", "datetime": "2025-06-08 13:05:09", "utime": **********.996452, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387908.676988, "end": **********.996484, "duration": 1.3194961547851562, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749387908.676988, "relative_start": 0, "end": **********.81947, "relative_end": **********.81947, "duration": 1.142482042312622, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.819492, "relative_start": 1.1425042152404785, "end": **********.996488, "relative_end": 4.0531158447265625e-06, "duration": 0.17699599266052246, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45399520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01805, "accumulated_duration_str": "18.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.92668, "duration": 0.015380000000000001, "duration_str": "15.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.208}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.96693, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.208, "width_percent": 5.762}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.977042, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 90.97, "width_percent": 9.03}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-832505597 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik14Vm1iTnZCV1lhcEwvZ1pVNmt2aEE9PSIsInZhbHVlIjoiaTM4YWhKaWJOakgrQ0FNV2ZIZ1FuZHo4cUhNYjBlUGVJRVk3Z09OWkpYL3dzODl1Z2l0K3dYM1pPUE4zcGpZWHBiU0RseGRIdTdFMUtNMDY5bENwRTA5ZVg0WUEzVnJQaDd4RjlNaUNtYnNmeUpkVWV1K04rL3N6WEhKYXV0a1I2NHlnU2lnZUw4bXk5V1U5aS94RFFzU3BMeEZSSVpZM0hlRU9uZzVqZ0ZLNERoMmpteGx2Z0VLUEFCaTA4YmwrN29jS3ljSGh1cURNU041TUNLTUpCa2EwVC9oSTNYZTBjSVlKai81U3FuOHV6cVZ0TGw1VHJWUXo2c01QZnQvVDlJTElGb04xU2V6Rkpkc0dIVCtSejZGNzltL1JwL0k0MFFEZDF6THpoRDQvNlV0MGpyR28xVE9yQlp6RUFvZHV3UUNWT2RCeXBPM3htSkJXZDZLZTkzSUxzbmNOQVRraCt1ZG43V2NIdW0wbnpCVUVBd3FISTFKbmFwcWhRSXpuY21UTEVLMWVYNlovRlhtVEtUZ01mNGVzbUVQOTl6Rno5NEdjZ2IrRDZRMHhoMjUwaWM3aHQyVXIzdlQxSERVZXVFbDJsNi85NzEwMnBrRXU2WkhyMllQMXVpSlBBeGhVbnB5MWlqVWEvQXp2SWVHRy9OVExuZklNaWRMSy9Ob1giLCJtYWMiOiI5ZWUxMzljMjVlYzM1MTAzOTU3Yjc1MWYwMzkyZDlhNTk3YWZlY2NjMTQxYjI5N2IyYzkyOTFmNGNlOTM5M2QyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFMeHVTOUlxWkNQT1JOL1RDWGRBVGc9PSIsInZhbHVlIjoic0pmc01uUW4zQ2FTZkRKSDl1T0pvTWZCN0I3NzlqQWhJNlZiUVF3QmZKNHhEN0xIc09MVmZBTWNNblhvSFF3TURkUDBSUTNqcDFzR21hL0VEOEpQc2pBcWJrdHdISWdPTHlQZng5QldZU1Q3WTNNbnA2T3BMVW9keURRc212MDZ6c0tqL3pUMnE1b1Y0US8xL2dLK3FEdlJneXNjN1VDd3oxOFMwSGJwWEdXN3VlbjhzVTNodHRzbHhYN2dPUGJWMGV1Uk43SHJUTFVqaTNoT3NiNlhIbE9LNFNlREY2YzhySGg5QTluck5xQ01obGlXQXVLSzlqOURaZnA3RFl3VkxTV1Z6aFR6eFVaVTJmSjhyY3JBaG9nOTBxaUR1US9tNUVXa0xaTXdzK0VZTzE5d3hyRnhNOXUrSGNXVzM1UWViT0NaQlNEdGlWdmpPcENIcGpmVlZXYkNUNnZxb3JWdU1RY05kT2llei8vOVNIRlZIQjBtVzQ2Z0JLRDVMTFRIRUxMN0ZKeFp5bFhxYTNjVlpKdTVuelpvMVZMUDN3VERhNG54bXcwUnNmL3dVUGYyWnpRK2htOVZ4RDRSUXRQY3NKNUt4Vk1OenRTeVpIK3JRMHcvWnhVcHJuNHhjOVNZOERJd0RxcXZqNkJOaXdIZ05idE44UWJscDM1ZlUxZkciLCJtYWMiOiJmNjU3ZDM5MWEwNTVjY2M4NDVhZjU1NTkwNWU0NWU0MTk4NjIxNWQwNzM4YzI4OTJkZGM2MjdjNDE2YzVkMjA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832505597\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1624152428 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624152428\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-96647463 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik45Q2pFaGYwNU1RSDJGVVAvQXMrY0E9PSIsInZhbHVlIjoiek1jQlNvUC9DaUl5UU1TdWtQT3JKYzNZUjRtQkZjU1JVM3hQdGpyckNySWcrNkVJWm5vUVFwUG9GT3lqclJnTUZEektIUDZXWnZLcW1adWlnWWRWbkFvbFptZjQ2TzdEamVpMzlOSXpVcW9OK25rOHJiUkYwd3FHUXJkOWxnTXF6elc1VmFUNEdJQmZjYzlaR0J2RzJ2TEZVK25ZR2lQMmtuSU9TMGZoYmZzTGtmQ2gzbEprZ1d3Nmt5NXExNmhtM2NJN2RoL05RbWZXbHFVZGNRenVWdmYvT1hidVdiTFhIQ1JmeEkxRkQvRXRXTVlGdnFabWhrc2pIUFNqZnJyN29xTHAxenRhajB3ckdFZHgwRXNONHlROGVXc1k2YTVXblFjdjZJSm5hODIrRGprNlhnTHJoMTNKbml6TllmQnMyYlNhL3d4U3JjM3pyakpQVFEvMzlSNG4vUGdibFhkaytWYm12Qys4bHF5QnB2aGpYRThxalZPWmc2Y3NFT2JZMlRJNmorMDI2OE5CanhsY3hPRWpETVFkVHI1ak1sMTB2MmU1dng2ODcweFpzQ1hpQlJLcmRCWWpRdE43UkVCYmsvdkVkN1NBMnRSZ0dmZ0lOL0ZTSFRiZnRkSE52NEVUL1NKMnI4UUg5WDdPSEpLczViYnh1S3AvcGlEU1k2YWkiLCJtYWMiOiI3YjYyNjYwN2M4NDA4MDJlZjg0N2MzYTg3OThhNjM4MTgxZmQwY2FiNmM2ZDI5NWVjMWI1MmY5NzkzYThlMzJiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:09 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijdwd0VsclhSSmgrWVc2RmdCU2VSd3c9PSIsInZhbHVlIjoiVXk4NDYzQ1QyMnREZnppdnpINHF5OEJPdDJWUkwxVFNqQ0VNQWgxL2RQc1dpaXZlTk02Z3lpWnRBR2wyU1NwTjhtSXFGWVNib0NWVHM1OExjOUxvSFZZaEg5OVlvUDFEaDRTeDRzdVhTL21RNTk1SnVQQ0pjYnA3NjhBbU4xem9MNXRuRVlqeWhJVEpxSkZIMnhEYlhsTGgyZ0RYS0FwOGNFdnBJZ29KQTdMUnFFSGNJdTExM3NoWjdaU2lNNmJ2dlRxRE9td2p5elRtdmlBM0JhR2RlRXRIVEJ6VVRtVFVtek41WUViV05CYVB2QjlEbWNSTVJUblJ5Ums1NVpoUGJIQW5rOU5tYWlVV1VyNmJOTVViM3hSUVpGOTV1b3owVGxzTVlvUGtLTDlrUjMwa2d5d3BmMFJ2aDcvNWNFUXZhVlZCQkU4dG1BS3Z1M0pzdmRwaDhDTzRxMmRLdXBXbkllbW84QTA1SlFOZ3ZkT2g1NWhzT3ZzYXhnZHRZYThiQmtFU3NWTHhuQXcraDhJSXBEUnlVVG0vajhqVzMyUDlURksrQXFxdDlCZ1ZzTm0ydEcrNW1YckoveVBYcE1vejJiSEcrUUJ3aVRsNHRRMVJoWjNweDd4QW9SV3RSWVRrcmFwUmR5TG53ajlWdTFZSktPQTdpQm12cWJVbHJOVC8iLCJtYWMiOiJkZWM1NmI3YTQyOWU2MThmNDM5ODc3YjY0ZTU3OWU0NDE5ZDFhYTM4MzEwMzAzNDI2MjAyODlkMTVjMDgwZWVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:09 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik45Q2pFaGYwNU1RSDJGVVAvQXMrY0E9PSIsInZhbHVlIjoiek1jQlNvUC9DaUl5UU1TdWtQT3JKYzNZUjRtQkZjU1JVM3hQdGpyckNySWcrNkVJWm5vUVFwUG9GT3lqclJnTUZEektIUDZXWnZLcW1adWlnWWRWbkFvbFptZjQ2TzdEamVpMzlOSXpVcW9OK25rOHJiUkYwd3FHUXJkOWxnTXF6elc1VmFUNEdJQmZjYzlaR0J2RzJ2TEZVK25ZR2lQMmtuSU9TMGZoYmZzTGtmQ2gzbEprZ1d3Nmt5NXExNmhtM2NJN2RoL05RbWZXbHFVZGNRenVWdmYvT1hidVdiTFhIQ1JmeEkxRkQvRXRXTVlGdnFabWhrc2pIUFNqZnJyN29xTHAxenRhajB3ckdFZHgwRXNONHlROGVXc1k2YTVXblFjdjZJSm5hODIrRGprNlhnTHJoMTNKbml6TllmQnMyYlNhL3d4U3JjM3pyakpQVFEvMzlSNG4vUGdibFhkaytWYm12Qys4bHF5QnB2aGpYRThxalZPWmc2Y3NFT2JZMlRJNmorMDI2OE5CanhsY3hPRWpETVFkVHI1ak1sMTB2MmU1dng2ODcweFpzQ1hpQlJLcmRCWWpRdE43UkVCYmsvdkVkN1NBMnRSZ0dmZ0lOL0ZTSFRiZnRkSE52NEVUL1NKMnI4UUg5WDdPSEpLczViYnh1S3AvcGlEU1k2YWkiLCJtYWMiOiI3YjYyNjYwN2M4NDA4MDJlZjg0N2MzYTg3OThhNjM4MTgxZmQwY2FiNmM2ZDI5NWVjMWI1MmY5NzkzYThlMzJiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijdwd0VsclhSSmgrWVc2RmdCU2VSd3c9PSIsInZhbHVlIjoiVXk4NDYzQ1QyMnREZnppdnpINHF5OEJPdDJWUkwxVFNqQ0VNQWgxL2RQc1dpaXZlTk02Z3lpWnRBR2wyU1NwTjhtSXFGWVNib0NWVHM1OExjOUxvSFZZaEg5OVlvUDFEaDRTeDRzdVhTL21RNTk1SnVQQ0pjYnA3NjhBbU4xem9MNXRuRVlqeWhJVEpxSkZIMnhEYlhsTGgyZ0RYS0FwOGNFdnBJZ29KQTdMUnFFSGNJdTExM3NoWjdaU2lNNmJ2dlRxRE9td2p5elRtdmlBM0JhR2RlRXRIVEJ6VVRtVFVtek41WUViV05CYVB2QjlEbWNSTVJUblJ5Ums1NVpoUGJIQW5rOU5tYWlVV1VyNmJOTVViM3hSUVpGOTV1b3owVGxzTVlvUGtLTDlrUjMwa2d5d3BmMFJ2aDcvNWNFUXZhVlZCQkU4dG1BS3Z1M0pzdmRwaDhDTzRxMmRLdXBXbkllbW84QTA1SlFOZ3ZkT2g1NWhzT3ZzYXhnZHRZYThiQmtFU3NWTHhuQXcraDhJSXBEUnlVVG0vajhqVzMyUDlURksrQXFxdDlCZ1ZzTm0ydEcrNW1YckoveVBYcE1vejJiSEcrUUJ3aVRsNHRRMVJoWjNweDd4QW9SV3RSWVRrcmFwUmR5TG53ajlWdTFZSktPQTdpQm12cWJVbHJOVC8iLCJtYWMiOiJkZWM1NmI3YTQyOWU2MThmNDM5ODc3YjY0ZTU3OWU0NDE5ZDFhYTM4MzEwMzAzNDI2MjAyODlkMTVjMDgwZWVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96647463\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15********\", {\"maxDepth\":0})</script>\n"}}