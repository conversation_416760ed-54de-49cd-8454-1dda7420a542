{"__meta": {"id": "X786c97c69d1c6ad950220a0b9f86508f", "datetime": "2025-06-08 13:19:45", "utime": **********.771741, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388784.344325, "end": **********.771803, "duration": 1.4274778366088867, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749388784.344325, "relative_start": 0, "end": **********.582027, "relative_end": **********.582027, "duration": 1.2377018928527832, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.582087, "relative_start": 1.2377619743347168, "end": **********.77181, "relative_end": 7.152557373046875e-06, "duration": 0.18972301483154297, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45179016, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01982, "accumulated_duration_str": "19.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6802719, "duration": 0.01659, "duration_str": "16.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.703}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7360709, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.703, "width_percent": 7.467}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.748042, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 91.171, "width_percent": 8.829}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-658179055 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-658179055\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-127792958 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127792958\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1628617158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1628617158\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2017750335 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNuVUtNL3Y2OGlSMVc1RVJUT3l3Nmc9PSIsInZhbHVlIjoiSElObWxZYktUZ3RNR1l4VVQva3YxV0lBeFQ3U2tkTkRxcE42OWdhUTlFWHg1T0haKzB2bzY1bkpSamFjYUNVb0pkZngvNGFGb3p3UEtsMjZiUms3dStueTFCakx5U1VKb3pSSXJHd0kwL29iTW9HOWdJVkNTWFVXMTFLWkhDeXZtNUQ1YWN3VWVWUXIzeUR6WEQ5Tk5GKzRWRzhDdHgrY1pNejZhWDJncXRZQkx1WjUzQ0lyeGJmaVRaV2VQZlAwUU9odjk3a212QnFMcGkrYlp4V2h0aDFQUFhwNWpZR1MzVjkzNkRqNWQ1Y3R6YUtwK2xNZ1JwcUUwSkZPdWQ2ZHAzemtaWVZXWTZUSnNaazkyZnJ3VEhvSEJscUFYSjJqU0ZDblBFb0pabE83NXVFSFhsRlUxWkFobmE5NFVGUU0vbkRaU1NHVWIreUd0UUJ3b1E2ZThRN1g5UjNLRUU0VFZHVmQ2UHNoNCtXNHlaSWFQQ3QyWlY1c0tNR3RjeHZpTDd2QXNqOWVvd1A5dmNYTyt1dUw3K2RDTGFvYlNIZkRGUWRWemkxV3dpOEVQTW9ZRERJeDJNMll6ZmdUc1BxZmJwRS9pSHRaR0MwRHJnb2pFY3BrdmQrMitHVWFsU2t4Sk5pc3AxZ0I3blQyV28razRKc2dlU1pSbXVCR2l6TWwiLCJtYWMiOiJkMjEwZjcwNDNmMjE0YWFkZDFkODQ0YmZlNzEyMmNmOWUwNjIwNTFkZDAwZjJjYTkyZTAxMWE2Yjc5MDNjMThiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVGa2taMGVqMVA0ZXVQeFJJbWM1RlE9PSIsInZhbHVlIjoiWm5raFlRbFpzcmVDdXBZbTFNOGpDekw1eGNrU3RoUlA2STM1UEJ3U0RYRFJsQ3ZhV1BGaHZiV0k4andYZlZCMVp3a3d6L3NnaXFMU3duZHQwRFgyZllLT09aV0NtU2hYS1BqTUNsTjUwaVh6Szk5M05abDNCT1JrRkYyczlkczBZSzViNWVaVktWRUZ5Y21aZ0pQdkc1RGlXSWxrTS82WVdlNWNhdkhIZjB6YW42VVdrSjFJclVBZlJpV0VYZXZ1RkxZLzJSVVZhNCs1blByTEFNNWlreXcxQzBLMlZrMHdhRG00dEpRVXhRZjdsSDZLdlRyM1FVMHBhZlNwa2N1SzdpMVJFaTdaNjBKa1Yyank5aWdJVVIrNHhaR2hKc2pNZ3Nxem9sREZQS2ZIZ2JmcnpmaUlCNHNNRGxoZXZIbXF2MFFNZitDbS94ajYvYzN0RGhqU0NOQ1g1ZjN3N25ucnJVaWdKUERGdlNJY1BSdzJLM1AzOEh1MjczSlR2U0dweE1qaExmN1NqUzkrNkdGaEphdWdmdStoMG9aSzE3WU1jOU9mVE1hNnhJSnNiU0VrQkdxV1pnaFJGdzQxb0s5MzUwMGRmaEx3Ui9kaEdtWGVwR1lIcHNremwyNUZMaUlCems0ZkxkZUd3SEZtWGpkYVBxWWI2SVp5OWdNeno1ek4iLCJtYWMiOiIzM2QzOWM3NjdkOTMzNDUzZTcwYzg3NzM4YjZiM2IwMDgzYzIxZGFiMTJlNTU4NGVjNzU2MjgxMDg0YWY0ZWIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017750335\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhJcGRnVS9MZHcwR1VxYU5OZkRpdVE9PSIsInZhbHVlIjoiUm9tbG1DRi9ZRzlBODNjSkxxWjVDeGwxVVNoRlBjc2VwbWdsVDd5ODBTay9yd3RyM2MwTExHaWxUMURTMTE1bzBTT1pvZ1JHNDRrZjIwdm1UZDh3WDEzTFBucTViZTNLV3pqWE5kb3YrbCsrT2ZrMjVZUXFqeXJzclJrY1NMSjR6L2hVRkZpRVhpbXZNL1BsMFRmaDR3a3VxdmorSEtSS0VvZlQrSld2WTN3ZlVYbzJkZXhWZDFya05PcjNOQ3J6NFFFa3A3MzlvRTRKZkxSU1NwaG1VYVdlVkpKVnYwSlBpeCtrM2RLWjlLU3BIQmZnU2M1ZVFMbWNvTUpjUVVYcS9EL2tQb2VjcmF2NXQxZFBpODNldmdSZFFJV3YzSkNHVE1nMFBoRW93N1Y0ckF1R0dCSXRHeFg0TXpyVTJIYlU4b2dabEZoNFQ4S2tPZkl4S3pDRU9IeHBQU1BQeUFaTTkrV1lyaVRneDJaY0JTd0l5dTZ3bEUrRGVpQXZ6a3dVVU1USC9vMUxSWmFZeW50MUc2WWUzdFNiNXZrUitKQjE3eVNtbGdWM1BqdFlTZHNGbFppYVV0VmtBalpHZ21rTS9LOHhtVXpxSTR3eEhvaU1CLyt0cHBVQWZ0bFNnOUZ6Q2NlUS9sRzdwQ2dhb3BMVUZrRWYrbnFKRE5oalhHZFQiLCJtYWMiOiJlZWU4NjM3MjgzYjBhNzdmMDdmODkzZmY5MTMxNjE5ODI0YmQ5MWFlMjE0NzkwNGE5YmM4OGE1ZGNmNmVjZWE1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilo3eWJNbHhLQkpXRHRtbE9TaHBRb0E9PSIsInZhbHVlIjoiL3lkRGlVd3lJRG96cFdpQXZBYy95VlpUR2tSVWJBWFo4ZFVsYTNvSVJvbFhHam40dlltbGwxcVdyQXBod1NKUFpqYkdhTGRZcjBMTnFGeS9OQVhXaUVZa2UwcVd1TjdCWnNHRkRYN0hlbDREczhzNXhjYkFoOWIwTnpPNkhpeHpyck5GMVZ0UHpTYkRhcXkyVjJpY1Z3ZTI3YzN3NlFrS0lTTDV2VjdSaGc5UHRMUVQwa0ZXZUEyOUVHQmZBZWw3Q0pFd0dUV2I0V25xaUhDekt5NHpJbUExUVYyWXlTVzhUUFMwZXJLd01UeS80eHpiKzVPTytxQUxjbEg4a21IQ0NSL2FZUysxeFZBZ0ZHbWpQQlVEL3VGa3lLR0dCVVBIUFhsaXJVVU12TDlTemw2OXZzTm9uamJkdVFsRmNaTFNkK0FyNVdEOUNjY2NERVE0OFhDUVlLcEdxV1k1akZuMEFwcTFkalFTc1h4U0VrYzlzeUpCeTJ2WkRsZEhiS2ptd2ZuVHdhR2lmK3RBUEZSNkk0RndUWnhpcDNNenpmeUVIOXYzUm5OSWZLb3VERkdqb2hub2VpaE4rb0tORE9oUzZ4UVhWRVRINGZteTJyMmQwZG0xTXFqbUNPRUQ3WVdNOU1XL2tRZlYrdE8wK3RjTGsxdHBNMWFEZGdPekhsQU8iLCJtYWMiOiJkMTY5OWJlZjgyOWRmMjRhZGI5ZTVlMDNjZDI4ZjdhZDJkN2IwNmZhNGU3NTBkY2M2MjI0OWIwMjZkYjc1YjE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhJcGRnVS9MZHcwR1VxYU5OZkRpdVE9PSIsInZhbHVlIjoiUm9tbG1DRi9ZRzlBODNjSkxxWjVDeGwxVVNoRlBjc2VwbWdsVDd5ODBTay9yd3RyM2MwTExHaWxUMURTMTE1bzBTT1pvZ1JHNDRrZjIwdm1UZDh3WDEzTFBucTViZTNLV3pqWE5kb3YrbCsrT2ZrMjVZUXFqeXJzclJrY1NMSjR6L2hVRkZpRVhpbXZNL1BsMFRmaDR3a3VxdmorSEtSS0VvZlQrSld2WTN3ZlVYbzJkZXhWZDFya05PcjNOQ3J6NFFFa3A3MzlvRTRKZkxSU1NwaG1VYVdlVkpKVnYwSlBpeCtrM2RLWjlLU3BIQmZnU2M1ZVFMbWNvTUpjUVVYcS9EL2tQb2VjcmF2NXQxZFBpODNldmdSZFFJV3YzSkNHVE1nMFBoRW93N1Y0ckF1R0dCSXRHeFg0TXpyVTJIYlU4b2dabEZoNFQ4S2tPZkl4S3pDRU9IeHBQU1BQeUFaTTkrV1lyaVRneDJaY0JTd0l5dTZ3bEUrRGVpQXZ6a3dVVU1USC9vMUxSWmFZeW50MUc2WWUzdFNiNXZrUitKQjE3eVNtbGdWM1BqdFlTZHNGbFppYVV0VmtBalpHZ21rTS9LOHhtVXpxSTR3eEhvaU1CLyt0cHBVQWZ0bFNnOUZ6Q2NlUS9sRzdwQ2dhb3BMVUZrRWYrbnFKRE5oalhHZFQiLCJtYWMiOiJlZWU4NjM3MjgzYjBhNzdmMDdmODkzZmY5MTMxNjE5ODI0YmQ5MWFlMjE0NzkwNGE5YmM4OGE1ZGNmNmVjZWE1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilo3eWJNbHhLQkpXRHRtbE9TaHBRb0E9PSIsInZhbHVlIjoiL3lkRGlVd3lJRG96cFdpQXZBYy95VlpUR2tSVWJBWFo4ZFVsYTNvSVJvbFhHam40dlltbGwxcVdyQXBod1NKUFpqYkdhTGRZcjBMTnFGeS9OQVhXaUVZa2UwcVd1TjdCWnNHRkRYN0hlbDREczhzNXhjYkFoOWIwTnpPNkhpeHpyck5GMVZ0UHpTYkRhcXkyVjJpY1Z3ZTI3YzN3NlFrS0lTTDV2VjdSaGc5UHRMUVQwa0ZXZUEyOUVHQmZBZWw3Q0pFd0dUV2I0V25xaUhDekt5NHpJbUExUVYyWXlTVzhUUFMwZXJLd01UeS80eHpiKzVPTytxQUxjbEg4a21IQ0NSL2FZUysxeFZBZ0ZHbWpQQlVEL3VGa3lLR0dCVVBIUFhsaXJVVU12TDlTemw2OXZzTm9uamJkdVFsRmNaTFNkK0FyNVdEOUNjY2NERVE0OFhDUVlLcEdxV1k1akZuMEFwcTFkalFTc1h4U0VrYzlzeUpCeTJ2WkRsZEhiS2ptd2ZuVHdhR2lmK3RBUEZSNkk0RndUWnhpcDNNenpmeUVIOXYzUm5OSWZLb3VERkdqb2hub2VpaE4rb0tORE9oUzZ4UVhWRVRINGZteTJyMmQwZG0xTXFqbUNPRUQ3WVdNOU1XL2tRZlYrdE8wK3RjTGsxdHBNMWFEZGdPekhsQU8iLCJtYWMiOiJkMTY5OWJlZjgyOWRmMjRhZGI5ZTVlMDNjZDI4ZjdhZDJkN2IwNmZhNGU3NTBkY2M2MjI0OWIwMjZkYjc1YjE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1226092668 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226092668\", {\"maxDepth\":0})</script>\n"}}