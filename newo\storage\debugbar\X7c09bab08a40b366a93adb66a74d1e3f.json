{"__meta": {"id": "X7c09bab08a40b366a93adb66a74d1e3f", "datetime": "2025-06-08 13:09:11", "utime": **********.518031, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388150.218281, "end": **********.518067, "duration": 1.299785852432251, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1749388150.218281, "relative_start": 0, "end": **********.349047, "relative_end": **********.349047, "duration": 1.1307659149169922, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.349069, "relative_start": 1.1307880878448486, "end": **********.518071, "relative_end": 4.0531158447265625e-06, "duration": 0.16900181770324707, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45594664, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019450000000000002, "accumulated_duration_str": "19.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.428415, "duration": 0.01668, "duration_str": "16.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.758}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.471191, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.758, "width_percent": 7.404}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.492778, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.162, "width_percent": 6.838}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2021573741 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2021573741\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1288767123 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288767123\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-12078936 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388028184%7C14%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlER2NXUUdsdFBlcE1ZV05GZmtJeXc9PSIsInZhbHVlIjoiM0QzMGtTTktEL1lQNXM4RGZUZDF0c3BwU2ZFOE45SUZZVDB2TU9heVF0cTNxZDdxWE10UndlTHAzVjRTdTlBVThHRlFNTXdBUkpSTjNjcW5UY2RJaFBkTHFGOHFIYU5sQUI3eXRJbWM4ZVExeDRPSWFnZlE3UXN6WlNkYm1KV0M1QjZNY0drRFNjWUI2UjBHREJaRm9nL1B3c0pqd1Zia1paTWs3aGRzNEJuSi9lVWdUbDc4dVFsdGY1anc2RlFPU0NWMDh6eHpKZTJxWkFLdVZRRWMrT2E5ZE9PWUpBWTd5enlWQlN2ZmYweFpxbXBIbWV2V3pKUHdhSHhtYXBWZW1kVTBIZE1Qa1JjSmxKSnJGN29iWmxNWXNjcW1ZUmpWZWl0Q3Z1MFcwRzFmNnFmcFhsQmdnV2tPYm1ZTElHL1h1KzQ4dnZYR25qOVJxcUYraDZGVTZZcjRCTFhYWjE4Uk1nSm90d2U5bEI2Mm81dUFzQkN2OXhVajNsblhLTTJ1WERQMCs4clpCOGJyRWMyNVkzajFXMU1PVVFlcEtPSzV5K1k4MW9NYTNiVlNtTEVKOEdQeWhmRGhYWlRFcENPRkJrMVlzYUxURGNqMU94blBvL2Z5RUJTb3FDSk9YL2kxZlFzZ3pwdzJ1ejJyNkZSYVVjYlVha2c0aDlLQ1I1N08iLCJtYWMiOiJlNWU5M2I3ZWYzNDY2ZTFkNGRkMWViYWM5MTA5YTE4NzYxNmM4YjM4MGE3NzgwNWQ0ZWY5ODFmNWQwZjM1NjQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilk0ME5MUmJJZVJIL0J3cGFlZ3ZnM2c9PSIsInZhbHVlIjoieS9xa0ZYYVlLQUM1MUdsUVZIRDVMdVRYOWJJVWJqcW5rbXpwV3h1RjhBR0owRkJWdm9hUXRGVlEweWVYUW5zZURKZWN2MDVUUTFHdmhKdyt6bnBFMmdQenlUb2lEa2VUTEpjQWRBYllqL3ZPQlM0eHp3YnNKUDNiWms0NmRJQmRha1pPMkxxK3QyalhCdExXdUw1eE9RUnA2TzJJR2xKdW01Z1pHMWhaVUFENjczaTJzZ2UyK0M4dEZSQ1h3YTNCYWZEQUM1Ti9oYjRUWEVnY3BZNXRjWHJBUlN5UzZnWG1IUFU3dmt1QmhwWUtkYmsrY2Q1dEk2bk1DWUsrRHQ5RmNNU0t4VStjS2ZoeHhuZVNmVW5xT2JKNXJLVXcrVDVjMFZ5bXdYWlB3TkFjWmtMWkxneGtCaHo2aDNoSVFWODRMMmRMNHNPcEl4KzlsZExHc29ocUU3WFl2RHRrVWxvclF0MWtadVN4WHNGOEhMb3RkYWJpUGFjNXFYUUFvZ1FPMlhwdjN2a0NSMG4xays5S3pRS0gyNlJ4Yk4wT3IvMml4OGxnZU1LTUp6QVpqSUVYN1pLNDB4dVNwYWRhcmMyV2FnNzRMZElueHRUWldiYk9ZR2NoT2grS0JTbFU0UjVFZ0gvNGRLdWpnZkJscWxCQkExU0h0REJuM1A1QWo3MzAiLCJtYWMiOiJjN2Y2NmNkMzI0OGE0MDk3MzI4Y2E5MDI0OThjMzljOTQwZWYzZTVmYjFhMzhhNDMzNTM0MmY3MjdhMDdjZjUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12078936\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-839225094 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839225094\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:09:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNXUG1DNlVSU1R6OEcwdjduVkhqZVE9PSIsInZhbHVlIjoiZHVDd25jZlBsVi9ocHFyVXo3c3g5OGtiSlRDcXBISHRUVy9pUmM4SWZSZEF4QVVNNEF5Ti9UeGpvcGp6SHpzcXdqODgrbkVuSWE3aWpGUHRJYW5uUFhZSERUeENPZTJ5ZmN6V2hWS3B1YVRhVCtBOEVSeHlNNktZSDJ4VDZZb21vRmQ1Mnc1WDVYcWsrL05BOFM1K0k0THN2NGxOcTZxd3d6V1lCS0l4SzhBRFpJUVpZd1l5MzZJZDBDczNLaFhaSFNVcW9yR2VxMWo0MmJrUDMvZnpyQ05KeG9sTkZsc1BnZFBzb3FTMVZWZnlKRkVPVGc1WnY1ZWVmUmh1ekY5Ui9VVWxWUVFmU3dNc2JkaGNzRWNjL210eEg5aXluYTdFOWZBQzI4K09INVFqSFBsTkJnZEIwamxJNWtUTjNHQm45cXR3WldWVUQxS3cyZnpIMk5jRUdnLzJYc3V2NG44V1U3YVp6OWE5LysxTVZMTmhBZmFzMFMwWTJUeFhsL1hFa3BQNFhmVTR4OFBjNEVBUThRVzFqT0Q5WkluVUJxbTJzRW9FVVNHOG9yQUltR2diRlRRR0ZHMlZTd05jRG9jMDFueHJtKzMwVnpjQjVSSzJLQytiek1jc0ZvZEJ3d2RlTzlxaDlPZ0RZZG1Qd1FOa1FsWFlPemVsSjBIelN1cUkiLCJtYWMiOiI0MTdkZGZlNDFlODZjYzE1ZmM4ZjVjNzJkODg2ZDgzMGI5Y2Y1ZjBlMGEzMjUyMjE0NTBkMmRkZjFjNTUzOWM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:09:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdmNzlVOEZCUk5DL1o0cFR4WkpWZkE9PSIsInZhbHVlIjoiQmpMc1hhNGcxcGpUVjJKbW5FTGphZ1liY2ZFZjg3ZGRhQ21LL28zdU96bXlhNVd0T2NwRzBrTW1YVzR5bnpWNzNOUTNvU25OK0srZkpMYmN5NE9uRXJ0cTRiWXVvQ1JlWDN2VXplam85UFlYTnFZWDJHTlBYaTJYOHhyNmdWMktJMUNkUnJWOEc1SHNCMjRCYUZJbm9lVUMyUVhOQ01za1EwOGl3WFMwblhYSjlGODRtdGF4dUxEUzdBM0o3RFZDVldOQU1aM0Q5dHM0cUFmdnMwNnMzUXBMYW5pdjd6OUFxMUtxTzFCcm10S0tBL2JoSlQ2cGlxTWRVci9SSUNlVHhOdWE1VlZqSXlVci8zZDkrRnM2ekJBMnN1VTRnM3dySFRvUU1VcVdHdnVDclNiZnkzMnpTcEhwbHZXSDVqTisySHFzeC84a0toajZuamlNL2VXWjBSTk94WEg4emdzekNGaTRKTHBNbHNzcmVDTWRPOVlBcjFiV2pBWjFFWko1SkFGK0lZNFRpQU0wZkpaWTBDNkV1L3JMdFl6aTh4aXhzRzc3VmxNL3B6djFLajQyL1pGWDk0YjdXV2pMYWRia1F6T1NEbjF1N0c1clB5UjhwdnlFUitLTjJpOWpUbGN2TW5OWW5hZHA5M1Y2Q240QnhGMExEaGdqQ2lvTjFxcTEiLCJtYWMiOiI5OGU0ZDkxZWVkZjU0YTcwZWVjMGM3MGZlNmY2NGRkYjM4YTVlN2RhNjVkNTM5NDUzNmUxZjNlZjMwNjhjZGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:09:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNXUG1DNlVSU1R6OEcwdjduVkhqZVE9PSIsInZhbHVlIjoiZHVDd25jZlBsVi9ocHFyVXo3c3g5OGtiSlRDcXBISHRUVy9pUmM4SWZSZEF4QVVNNEF5Ti9UeGpvcGp6SHpzcXdqODgrbkVuSWE3aWpGUHRJYW5uUFhZSERUeENPZTJ5ZmN6V2hWS3B1YVRhVCtBOEVSeHlNNktZSDJ4VDZZb21vRmQ1Mnc1WDVYcWsrL05BOFM1K0k0THN2NGxOcTZxd3d6V1lCS0l4SzhBRFpJUVpZd1l5MzZJZDBDczNLaFhaSFNVcW9yR2VxMWo0MmJrUDMvZnpyQ05KeG9sTkZsc1BnZFBzb3FTMVZWZnlKRkVPVGc1WnY1ZWVmUmh1ekY5Ui9VVWxWUVFmU3dNc2JkaGNzRWNjL210eEg5aXluYTdFOWZBQzI4K09INVFqSFBsTkJnZEIwamxJNWtUTjNHQm45cXR3WldWVUQxS3cyZnpIMk5jRUdnLzJYc3V2NG44V1U3YVp6OWE5LysxTVZMTmhBZmFzMFMwWTJUeFhsL1hFa3BQNFhmVTR4OFBjNEVBUThRVzFqT0Q5WkluVUJxbTJzRW9FVVNHOG9yQUltR2diRlRRR0ZHMlZTd05jRG9jMDFueHJtKzMwVnpjQjVSSzJLQytiek1jc0ZvZEJ3d2RlTzlxaDlPZ0RZZG1Qd1FOa1FsWFlPemVsSjBIelN1cUkiLCJtYWMiOiI0MTdkZGZlNDFlODZjYzE1ZmM4ZjVjNzJkODg2ZDgzMGI5Y2Y1ZjBlMGEzMjUyMjE0NTBkMmRkZjFjNTUzOWM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:09:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdmNzlVOEZCUk5DL1o0cFR4WkpWZkE9PSIsInZhbHVlIjoiQmpMc1hhNGcxcGpUVjJKbW5FTGphZ1liY2ZFZjg3ZGRhQ21LL28zdU96bXlhNVd0T2NwRzBrTW1YVzR5bnpWNzNOUTNvU25OK0srZkpMYmN5NE9uRXJ0cTRiWXVvQ1JlWDN2VXplam85UFlYTnFZWDJHTlBYaTJYOHhyNmdWMktJMUNkUnJWOEc1SHNCMjRCYUZJbm9lVUMyUVhOQ01za1EwOGl3WFMwblhYSjlGODRtdGF4dUxEUzdBM0o3RFZDVldOQU1aM0Q5dHM0cUFmdnMwNnMzUXBMYW5pdjd6OUFxMUtxTzFCcm10S0tBL2JoSlQ2cGlxTWRVci9SSUNlVHhOdWE1VlZqSXlVci8zZDkrRnM2ekJBMnN1VTRnM3dySFRvUU1VcVdHdnVDclNiZnkzMnpTcEhwbHZXSDVqTisySHFzeC84a0toajZuamlNL2VXWjBSTk94WEg4emdzekNGaTRKTHBNbHNzcmVDTWRPOVlBcjFiV2pBWjFFWko1SkFGK0lZNFRpQU0wZkpaWTBDNkV1L3JMdFl6aTh4aXhzRzc3VmxNL3B6djFLajQyL1pGWDk0YjdXV2pMYWRia1F6T1NEbjF1N0c1clB5UjhwdnlFUitLTjJpOWpUbGN2TW5OWW5hZHA5M1Y2Q240QnhGMExEaGdqQ2lvTjFxcTEiLCJtYWMiOiI5OGU0ZDkxZWVkZjU0YTcwZWVjMGM3MGZlNmY2NGRkYjM4YTVlN2RhNjVkNTM5NDUzNmUxZjNlZjMwNjhjZGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:09:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}