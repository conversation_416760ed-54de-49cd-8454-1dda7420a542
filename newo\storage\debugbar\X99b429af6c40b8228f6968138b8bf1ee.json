{"__meta": {"id": "X99b429af6c40b8228f6968138b8bf1ee", "datetime": "2025-06-08 13:48:44", "utime": **********.645475, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390523.32577, "end": **********.645513, "duration": 1.3197431564331055, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749390523.32577, "relative_start": 0, "end": **********.466483, "relative_end": **********.466483, "duration": 1.1407132148742676, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.466504, "relative_start": 1.1407341957092285, "end": **********.645517, "relative_end": 4.0531158447265625e-06, "duration": 0.17901301383972168, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45170296, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023200000000000002, "accumulated_duration_str": "23.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.568626, "duration": 0.02007, "duration_str": "20.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.509}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6154459, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.509, "width_percent": 5.431}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.62448, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 91.94, "width_percent": 8.06}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1937255329 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1937255329\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-836649060 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836649060\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1534902251 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1534902251\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1041231593 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Img0aVVTNEh4NExTVHlvRGpwQzdBQ0E9PSIsInZhbHVlIjoiNDJIMmVsZnRMQVJ1ZkcrVFM4RFNoU09zQTRMV2tNaU90Wk5rNjJjSnAvVURWeUpVcTB2QmFYK1J1OE1tdEFNbFlWU2VuQzNxOXdGZDlqSVpVdGJQV1NRbWxNaDhhbWdqSnB0VHJaVUlrMUd6Q3c1SklsMjBRSDltQnR5c2N0KzJXOTZJdlpGWkk3VW85c0RIWnprMWNJanFacndCMm1IdWtuM0gyeGx4ZHZPOHpxSk1GMURZNVM5UytDWHVvZnBxNk4vbVJ0ZlgvOC8xT1dNTW1ERWNDaE5QOVc5dEFTTkZvY3pMU1FrWWtFTjQxZkJmZ0NqU1pXTm90Tm1DMDNaTnlmcStMT3dvVFhwdW5IVE9XZWw0NjAwNmRBbk1lWHkyTitWWmsxSWNPa3I0S3VaVnZ0Q2U0OWNtRk9xTlZTVy96ZndBRzZ1NHRMZEZMZ2g5QS9hN0Z4SjRiVVJTVDl1dC9KYXhkTGQ2Wi90TkxqbUcwUkFPOGMrNHJqZE5zZVdmVVB5VkZ2YUJnRTVpMXZVWmtUaDVHNjBIRXF5aFpCU3JLTVZZdkM3TVlFMHdBTllBNURkZmlyeGYvUUFSVkFmR1BXVHJadHJ3ekZaMW5weU0wd25SSEswdDZGS2xLMzV2R1JFb3NTMUJZNzRTOGh6SE9QdTdmRXp4NkU0WHV0bjkiLCJtYWMiOiIwMThiOTc5NmJjNGY4NWVhM2E3NWExZDQ2MDFmZmI2NmZiMjkxZmRiZmJkNzMxYWVjNmY1MGFkMTE5Zjk5NDYxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjEvb250bWtMcXUyR0hUMkdSejV2emc9PSIsInZhbHVlIjoiY3lpOXo3MFpYbHlHM1dtR0VGS2xQd1hodVBwQ0drbmxKb0s4NCtZbVZTdWtvZFp3TFpiWE9kaS93ZndnWmtwbHl0QTM5SndnSzBtL2c0VlVmeXVCZmg2dHdlL2lpeWpRYU1kU0g4MEdycW1mQmNPVGdnVldubUdnOEx2bi90UXBXdW4wTCs5bkcza0F0SFlOb3JQQW4ySSs2VjhsOEV0dzJtRkpVTDRrZlU1dTY4ZXlSdmFGRXhwT2pNYUlCYXpkK1RjRXMvYXl6YmVMVzg0OGIwRUlRdDBhZkU0dWN4ZlJwNmx4dUxuYnpZQmtrRWg3ZHdnUWdIZk9DTGZYbUlrWFYxTzdHRU52YTl6RFl5dnZXVmJJRXVuYzJ0SDIwT1JVbDZNNm0rbEVQaWFEYlRzazZLVzhBNncvN3ZmRitHcjkxZWM0SkdCNU9PQTUybUxHVE1zanh0U2JiUFBZdC8wRWcwZEttWjB1UDRkeDUzY1JDcWowQmt4VlFTWGs5S2pmQXBXQ2xnYVNYTHpUd2hWYXkxWEYzSlFhdWRmSjYwSDUzZVNQTVBMOXR1a2M0TnRVYzRISzJCQ1dIa1RzK3hvVUZQVDBZdENoZkZwbWN3bWUxbFRvL0JWY3ROOHQrcHZuMW9LQXcxRHIwWDVHU25KdGJreTlTVnBSMU9qcEEwaysiLCJtYWMiOiI5YWUxMjg4YmZhODgzMDcxNDYxZjg4YmFhYzRkN2YxMTNiMDQwN2RhMWQyMTI3YWQyZmJmMDc0ZDFlNTZiZjNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041231593\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2071334503 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071334503\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-220886320 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:48:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtGZTg5aGVLcm82SitLcHVkZURBMGc9PSIsInZhbHVlIjoiZS83cXlCSW45TktSNUNXT2lxT1dHVWp6RTNPU1M1aE9xV09tcHEvTTVoM2Z1ME9NOVJsMnJWbTV3ckhZcjRWczJ6YjdaNlVFSmoxbDZXbTAxdXBHcDM4aUk0V1pWbG9kRGNndEdJYi9oOGVYYWxuU1Z6cmMzanpKOEJ0LzhUeVc0K1BoY1NuK1FWczVyNFVGRnRBQjA5N3ljRU5sWUNEQ05CWE1xd3FEei9aajhQc3RETytMSWFpcWI5ZkJuaWxZQk93em5EUjhCZzduWmk3WXVURHI2Uko5WGdzQWFZZGdzQmVYdUh0UHlKaUdYb1pkYi8vM1VtckQ0dCs4WFFlcE9sdUROd1ZLL2haSkxBOEgwUkh1QXZyRGRld2ZTREFxdXpncDRldHlYVUIwRVRORGoyQm52a2swYWozeHY4RDZnZU1ZZDNUYzBZMGVWWVh4WmtUVmU3YUFhOGFyUk50d2ZEazhDYkN1a2JzNTRhbFNIRUJ6QjNaNmNnR3pNc2xoeTBjaUQ0b1NHcDMydkM5MERZTkZZdUIxWGpma2ZQOEUvMG9MeEYvUDBCaWhjWitMNUUxTzlxdVVwRmhiSFJEOVVkMlNEa0NDUHRHMDRSc09tWWkwUjh5eHplNTVtc2VFSERIcE5SbnJMM3hCMjViTys1WlpaMHpHUEdvZ1JOQUIiLCJtYWMiOiJlZjcwMWE0NjY3NTg5MzJjYzMzMDY0NzQ2NDRmMTBmMjg2ZTNlYjc4N2E2YjM0YWY5MTgxZWZkMTE4YjFlYjQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJTNTQ5N0lvSkdvNGFkM3dJVm9kYWc9PSIsInZhbHVlIjoiclZMOG5ndm12cEJFb0QvTUVJeCtMeFRWWndsZFZMS2RiaW9yM2xwKzhUSjcxUWRnQkpyWml1czcwSjRmR3IwRTNrVENXQlE2N0JHWFBHUlc5YlBkbkpCOVRqVnh2bGNFbXhUYUs5RFJwZVRaR0QxMFhmOGdGSVc0eWl2cWZvTXN2NC9qSGZ2NDQxTHN5VjZvTnBteGxraHpTVWg3VEtjSk9SQll6SjVYNXA4UE8rN3pobHNuMVN6eWI5TXpsaG9wUy90UWhyRTA2M0hjdUNYOVZvNmJiSjlTMm1XQlRsZG1ZdGtyTk1zSCsrTlVmc1NZVHlWSkZYMCtaT2JKeE1mRFZWNmJZMjcweEdRZjVESFFDSFNNcEtZdEtLcnVLWVh3eVhmbTV1Vkl1eEhMMDZQb1JaMWxySVNUQWhVZzdzQmlPaEFEMnJnNVNhbGNFK3l1WDFlb21qNjJJV2l0bm9yeFlCZGZSTjc2NCtITzV1Rit6a1ZFUXoyL0Z4a0dlTVBjT3ZGbXJxUTdnVnRvNUZKa0djdmpUTTdTOXRDZ0FIN0k0UVZEK1pCUXZaZWRRWU5lVjVhVWFEU1JCTlQ4SENFSzJ5dlZ0VDEvbWpaY1NtYjZhaVJydEI1TjRQaysxeFhFNUdIZUtpOVE2MGVmWXcxMVA5RGZaQXRubVFWbWJuZVQiLCJtYWMiOiJhMzBjZmVkYTNkOTk5NzFiNGViNDc3MDYwNGVmMGY2ZTY5MWYzY2RlYzIwZWNmODQ0ZTVkZmQwMmEyMDI2OTQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtGZTg5aGVLcm82SitLcHVkZURBMGc9PSIsInZhbHVlIjoiZS83cXlCSW45TktSNUNXT2lxT1dHVWp6RTNPU1M1aE9xV09tcHEvTTVoM2Z1ME9NOVJsMnJWbTV3ckhZcjRWczJ6YjdaNlVFSmoxbDZXbTAxdXBHcDM4aUk0V1pWbG9kRGNndEdJYi9oOGVYYWxuU1Z6cmMzanpKOEJ0LzhUeVc0K1BoY1NuK1FWczVyNFVGRnRBQjA5N3ljRU5sWUNEQ05CWE1xd3FEei9aajhQc3RETytMSWFpcWI5ZkJuaWxZQk93em5EUjhCZzduWmk3WXVURHI2Uko5WGdzQWFZZGdzQmVYdUh0UHlKaUdYb1pkYi8vM1VtckQ0dCs4WFFlcE9sdUROd1ZLL2haSkxBOEgwUkh1QXZyRGRld2ZTREFxdXpncDRldHlYVUIwRVRORGoyQm52a2swYWozeHY4RDZnZU1ZZDNUYzBZMGVWWVh4WmtUVmU3YUFhOGFyUk50d2ZEazhDYkN1a2JzNTRhbFNIRUJ6QjNaNmNnR3pNc2xoeTBjaUQ0b1NHcDMydkM5MERZTkZZdUIxWGpma2ZQOEUvMG9MeEYvUDBCaWhjWitMNUUxTzlxdVVwRmhiSFJEOVVkMlNEa0NDUHRHMDRSc09tWWkwUjh5eHplNTVtc2VFSERIcE5SbnJMM3hCMjViTys1WlpaMHpHUEdvZ1JOQUIiLCJtYWMiOiJlZjcwMWE0NjY3NTg5MzJjYzMzMDY0NzQ2NDRmMTBmMjg2ZTNlYjc4N2E2YjM0YWY5MTgxZWZkMTE4YjFlYjQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJTNTQ5N0lvSkdvNGFkM3dJVm9kYWc9PSIsInZhbHVlIjoiclZMOG5ndm12cEJFb0QvTUVJeCtMeFRWWndsZFZMS2RiaW9yM2xwKzhUSjcxUWRnQkpyWml1czcwSjRmR3IwRTNrVENXQlE2N0JHWFBHUlc5YlBkbkpCOVRqVnh2bGNFbXhUYUs5RFJwZVRaR0QxMFhmOGdGSVc0eWl2cWZvTXN2NC9qSGZ2NDQxTHN5VjZvTnBteGxraHpTVWg3VEtjSk9SQll6SjVYNXA4UE8rN3pobHNuMVN6eWI5TXpsaG9wUy90UWhyRTA2M0hjdUNYOVZvNmJiSjlTMm1XQlRsZG1ZdGtyTk1zSCsrTlVmc1NZVHlWSkZYMCtaT2JKeE1mRFZWNmJZMjcweEdRZjVESFFDSFNNcEtZdEtLcnVLWVh3eVhmbTV1Vkl1eEhMMDZQb1JaMWxySVNUQWhVZzdzQmlPaEFEMnJnNVNhbGNFK3l1WDFlb21qNjJJV2l0bm9yeFlCZGZSTjc2NCtITzV1Rit6a1ZFUXoyL0Z4a0dlTVBjT3ZGbXJxUTdnVnRvNUZKa0djdmpUTTdTOXRDZ0FIN0k0UVZEK1pCUXZaZWRRWU5lVjVhVWFEU1JCTlQ4SENFSzJ5dlZ0VDEvbWpaY1NtYjZhaVJydEI1TjRQaysxeFhFNUdIZUtpOVE2MGVmWXcxMVA5RGZaQXRubVFWbWJuZVQiLCJtYWMiOiJhMzBjZmVkYTNkOTk5NzFiNGViNDc3MDYwNGVmMGY2ZTY5MWYzY2RlYzIwZWNmODQ0ZTVkZmQwMmEyMDI2OTQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220886320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-850803724 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850803724\", {\"maxDepth\":0})</script>\n"}}