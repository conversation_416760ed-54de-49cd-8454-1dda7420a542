{"__meta": {"id": "X4895dc9ff4fc9a9ea61c4e47021c5698", "datetime": "2025-06-08 13:04:28", "utime": **********.240639, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387866.976097, "end": **********.240669, "duration": 1.2645719051361084, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1749387866.976097, "relative_start": 0, "end": **********.08943, "relative_end": **********.08943, "duration": 1.113332986831665, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.08945, "relative_start": 1.1133527755737305, "end": **********.240672, "relative_end": 3.0994415283203125e-06, "duration": 0.15122222900390625, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43925456, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02961, "accumulated_duration_str": "29.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.183996, "duration": 0.02827, "duration_str": "28.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.475}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.221468, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 95.475, "width_percent": 4.525}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1601632364 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1601632364\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1696115474 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696115474\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-38422053 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-38422053\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2034259770 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBseUZuNStsZXM3VjRQTVRFSFMxUWc9PSIsInZhbHVlIjoiUFh3cVFObUgyNGIyWUlrR0FJNnBGdjN1ejR1TDRsR2xWQUtJdkIwMlUvZDlyTkwreEdFWHR5blRqUGRsQ0U3ZFhzWHdRNEgwOUU2QjE4cXNZVjg4Uk1oS1dKK1Y0TXdJS1R5d2RoaFlOR3N0a09UbHE2UjJ5VmRKTldCUmN5NUF5aDd6NGNwY2c4UGt4cVdVaXdLNlBDa0NreGV1blR5MnFKeGQra1dkd25LY213Sjl0NVRwaWFJa1VUTXVTK1AxdXNNNVNKSDRMMFV6UG9HNXJRN0VpS3djQ1JyblNHLzR4UlZObklVL0taRVcxYzJ2QTFvc0xUYmY4TjB5VFRsYndkUFUzUlo2Um12bzRTOUw5R1dxYm9RMUVNNC95R3FDLzBYVFh0Qks5QzNEWXZmQXJrUWVCK1poK2w0U3NlWlFIUTBhd0VuZHRoL082b3JLY212K3NJUVViOUZpdzQzcCthUFQxYWFtbmdZbWtSYzFmT1gzYi9KK0c5OXRaMEsrN0JoTUU1NytQUlJCTjNxVWw2UHdIQk5EdVZ3amtSc1dpeUlRa0t3dk9iSnFLSXZRSFEranpiTjhtMHJZQVFGZ0ZuL0xMdk9OQWNCNDBodGwyaWNrUnRnaDNBZmd4Y1hTRzdPbGJHWmxmVkM1K0I1RVl4RUQwVzhGcWY2TFJ6Rk4iLCJtYWMiOiI4MjRhZmM2NDQxMWZjYzgyYWE0ZWYyMTI3ODU2ZmFjMTczNTM1MzM5ZDkxM2VmODk5Zjc1YzYyMjRhOThlNzdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InZ4a1J0Q2dyR0h2QWpGTTkzNUhTd2c9PSIsInZhbHVlIjoiS0Z4Z2JVb2FvTjgvTENlaFZ2MVZjdVMyMlJrRlBkc0ZZazhNalI2VXlNWGIvTFA1aGt1VUd1UFFuNFNmK2VHSyszcjJZMWROWDltUG42WTBmVEtmUm5kWTVrOFJSSW1wUXBsQlpTdWNXa200WVVDMVg4UVFuWHNOczJwem1ORlVPTk1pSlI1eHlGWFZUZnNhamJCaE5idE9YQVkwMVBLY2FYVmxjTFI4WmFDdnBWMmpCbGsvZDkzbzBoaHdNSE9ORU1tUTBlWGtCUThyZGlXR1hzdXhiTEp5anRCZnBlOWxSYWVBbGRKOUxJWnMrVjZLcjIxWGJVUEFXZFczdENxQkhuZllXek8rUCs1ZzFZcHQvTjVDNWxFTUJSbkJBTVF3ejU4Y2tRcHB1T01wcVBSN2NnZ3dFZGtvN0I2eXVjYUFOcC80bFkzKzRlOG5DQmE3NU02L2I5WHd2WU1FYnJxcmJ0N0x3YzhjQ0tLWUlISnRYSU9weGhwK1ZDYkh2VDRoNyttRXBMcU0rNHNlaHF3c2RIbkNxQ0I2bkx0RGdGMDNiNWZhTnVDM2xnMmpqRTFBNXExRnJPN1BxL2t5Mld1TWgyMVpYdkt3eC9VU00vTjdvTWVqamxuSWNBWFE2RW1HNUR1RkQ5bEVUV2JKcFNVb2J2R0k2aXkwY2FaMFprSVciLCJtYWMiOiJjNjEwZjVhYTk1ZDgxMzQ1OTJkMzdiMTg3Y2RiMDk3N2M3MmI5YWJmODdiNzJjNTQ2NGIyNDVkMjdjNjhhYmVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034259770\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-584178992 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584178992\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1349769056 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:04:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVLRDFMWVNwT0JFSzdDa0xyU2RzZnc9PSIsInZhbHVlIjoiYU9EWWRqUDJlQVNNalE1Mmx1T05TV21zQlE4YktmTExmMms2ZHJGVDFFd01YcHZUaVBlekdxa3VvbEhxL2pNU2dGUkFIem0zR0FxZ3RvRUlmNUtuZmpLUTIzRVNKeXN5T2QwNWxhQjFJd2x4UUE1eGxiWVlDdUdLODZCVEYzQ1huSUprZlZVeXQ4M1JaY3hsc0wrUUIxY21WQUxkemFtbVh5YkxOL1R0MUFEMXBHN2c0UTBoOC9vZlJOQ29wbFlhUGRrZThscisvcmJJbGFpY045Tmg4NHU2SFNEWXVNNVAza3J1dWo3S0xyUU9GandJMFg5ZlVMWnB5RVlzU0tvOHhPSGZMWCtSem0vSHJvSVRFRUdRaEg0OXBoZVdxZWlvTFc3cmd2VFZUR1VnKytHSll2NjRyK3M0eVNPWUhmeUpJWXQ5SENzeWsrMUpSL09lQW1McU14VnFyZGxRbVhRV3EwcjRLMFl3dmNYYUhmcld5TUVxNFFDRGtJVWd1aXpQRUJmZ25taGFlYlhtOS9VL3pxZXlvY2lQalY3TVV6cmF0bEo2OHcyUWZaMjk0TjFXM2l0NnhEK2EwWEFIcklHTFBrcm92TmZMRExpalJiMHVjaGpHU0dBQ0VPa2VkMURRSkk5YXJ2WUNSSGk3d1pSOFBTdFFDSmp0QTh3R01GTHYiLCJtYWMiOiJhY2QzMWRmNTMyYjhiYTc5OWE0ZjA4NjkzMzM0YTA4MDI0OGY3ODhiM2RhNWRlYzdiYjgwZDQ2ODE4NzlhZTFlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNCRHhIMmxLdEhmYnhUcmR1YUZJZWc9PSIsInZhbHVlIjoiM3Z6aGtFMlZCU2QyK1M5ZTJ3WWEwZEJDVnlsbkNIekxISksvRkpGdHlXZkt6TGpMMDdvd0c2aU9BSFgrTnI3UlYwOUwwN3dQWUFYV01hNmdjQlR0cnRXR3J2ZkI3TkxjZDZJTk1IblFub3pwSEFRbzVYbGI0Ymt2M0RLMzBoZi9la2lsaE84aWZYcTdnK0M1cWhWaUlWU2hteDB4MXd1SFRxZkRuRUVDb2hEU2lQNXhEeFRBSWprSzBlZmNyeDZmYWtuczgvRXUwRUdIUDd3M2dCQVNoVzhFanpYd0NPdXdMeGtPSHd6cmlEVUFnNlQvZEdDWmQ3TmFYQXlXR1FBVllCQWpPZzgyV1E5bGhnc2NNZXlQbndrVmdBdDNjaUdQOW5FQm40SEpQZ1dMdFBJVzBwNHNySlFjZFhjcjlxZC91Z3dGMDBhNmkzTmQwVktDZzZaNWdCTEJrVG1UR3BiTUtnQnNqREplVitPdWRncldrTnZIL0o0NFcrb0xaQll5ZGpGQzBHMWtSVTV5NzUyVFhITzRyc3BVUGtldmg5UkhWUndFazRBZ2lyZEt3dkNPeElsOEJyVzNEWTBYVjlBOGlYL2pCL2pTQXkzWUFVcTU2YkhEejlyLzhJc1RkY0RmY0xSSG5DczZxdWtnSWpMSE4zSmlHQWoxVmE1aHBJRHkiLCJtYWMiOiI4YmUxZTY2OWVlMTNiNjhlMDNiYWIwYWVjZjQ4MjI2MGU1MWIxM2E2NzE0ZjZlMzhjZWEzNzk0MjkzMGU1NmIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVLRDFMWVNwT0JFSzdDa0xyU2RzZnc9PSIsInZhbHVlIjoiYU9EWWRqUDJlQVNNalE1Mmx1T05TV21zQlE4YktmTExmMms2ZHJGVDFFd01YcHZUaVBlekdxa3VvbEhxL2pNU2dGUkFIem0zR0FxZ3RvRUlmNUtuZmpLUTIzRVNKeXN5T2QwNWxhQjFJd2x4UUE1eGxiWVlDdUdLODZCVEYzQ1huSUprZlZVeXQ4M1JaY3hsc0wrUUIxY21WQUxkemFtbVh5YkxOL1R0MUFEMXBHN2c0UTBoOC9vZlJOQ29wbFlhUGRrZThscisvcmJJbGFpY045Tmg4NHU2SFNEWXVNNVAza3J1dWo3S0xyUU9GandJMFg5ZlVMWnB5RVlzU0tvOHhPSGZMWCtSem0vSHJvSVRFRUdRaEg0OXBoZVdxZWlvTFc3cmd2VFZUR1VnKytHSll2NjRyK3M0eVNPWUhmeUpJWXQ5SENzeWsrMUpSL09lQW1McU14VnFyZGxRbVhRV3EwcjRLMFl3dmNYYUhmcld5TUVxNFFDRGtJVWd1aXpQRUJmZ25taGFlYlhtOS9VL3pxZXlvY2lQalY3TVV6cmF0bEo2OHcyUWZaMjk0TjFXM2l0NnhEK2EwWEFIcklHTFBrcm92TmZMRExpalJiMHVjaGpHU0dBQ0VPa2VkMURRSkk5YXJ2WUNSSGk3d1pSOFBTdFFDSmp0QTh3R01GTHYiLCJtYWMiOiJhY2QzMWRmNTMyYjhiYTc5OWE0ZjA4NjkzMzM0YTA4MDI0OGY3ODhiM2RhNWRlYzdiYjgwZDQ2ODE4NzlhZTFlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNCRHhIMmxLdEhmYnhUcmR1YUZJZWc9PSIsInZhbHVlIjoiM3Z6aGtFMlZCU2QyK1M5ZTJ3WWEwZEJDVnlsbkNIekxISksvRkpGdHlXZkt6TGpMMDdvd0c2aU9BSFgrTnI3UlYwOUwwN3dQWUFYV01hNmdjQlR0cnRXR3J2ZkI3TkxjZDZJTk1IblFub3pwSEFRbzVYbGI0Ymt2M0RLMzBoZi9la2lsaE84aWZYcTdnK0M1cWhWaUlWU2hteDB4MXd1SFRxZkRuRUVDb2hEU2lQNXhEeFRBSWprSzBlZmNyeDZmYWtuczgvRXUwRUdIUDd3M2dCQVNoVzhFanpYd0NPdXdMeGtPSHd6cmlEVUFnNlQvZEdDWmQ3TmFYQXlXR1FBVllCQWpPZzgyV1E5bGhnc2NNZXlQbndrVmdBdDNjaUdQOW5FQm40SEpQZ1dMdFBJVzBwNHNySlFjZFhjcjlxZC91Z3dGMDBhNmkzTmQwVktDZzZaNWdCTEJrVG1UR3BiTUtnQnNqREplVitPdWRncldrTnZIL0o0NFcrb0xaQll5ZGpGQzBHMWtSVTV5NzUyVFhITzRyc3BVUGtldmg5UkhWUndFazRBZ2lyZEt3dkNPeElsOEJyVzNEWTBYVjlBOGlYL2pCL2pTQXkzWUFVcTU2YkhEejlyLzhJc1RkY0RmY0xSSG5DczZxdWtnSWpMSE4zSmlHQWoxVmE1aHBJRHkiLCJtYWMiOiI4YmUxZTY2OWVlMTNiNjhlMDNiYWIwYWVjZjQ4MjI2MGU1MWIxM2E2NzE0ZjZlMzhjZWEzNzk0MjkzMGU1NmIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349769056\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-371117372 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371117372\", {\"maxDepth\":0})</script>\n"}}