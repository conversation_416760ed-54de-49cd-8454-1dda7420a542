{"__meta": {"id": "X2167554b50a94594b6812c42ddc9117d", "datetime": "2025-06-08 13:32:28", "utime": **********.051001, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389546.580909, "end": **********.051046, "duration": 1.4701368808746338, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1749389546.580909, "relative_start": 0, "end": **********.766347, "relative_end": **********.766347, "duration": 1.1854379177093506, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.766367, "relative_start": 1.1854579448699951, "end": **********.051051, "relative_end": 5.0067901611328125e-06, "duration": 0.2846839427947998, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.020089999999999997, "accumulated_duration_str": "20.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8740568, "duration": 0.0141, "duration_str": "14.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.184}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.916143, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.184, "width_percent": 6.421}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.995192, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 76.605, "width_percent": 5.376}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.001157, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.981, "width_percent": 4.38}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.016385, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 86.361, "width_percent": 9.209}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.026517, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.57, "width_percent": 4.43}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-475802038 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475802038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.013599, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-251867099 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-251867099\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1427474423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1427474423\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-584758478 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-584758478\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1912222149 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhVakRGVXZ5YVNDcll0Tjk2SkFyVmc9PSIsInZhbHVlIjoiUy9GY0xFTzUxdDc2S2FrOC90cldYUzJvbTNtaDRINUNmaWZKWkhZV01Jb3V2bW9rK2xKT3FPK1lmYm80cGJFV2lRRVJXUmdWT2prOHloUkl3VFhxa0dXaVg4WlY5N2d0aGVzWDl6aXJOWkxKclhkbUVzUGZBZVFzT01CQ3dKZ3hNK0podmRTbzBjeVRBQi83QWxyNGo3ZHNkNVlEYUM4eGdBdTFqeGNnd0t2Nk5mNUpIaVA0OUc4RFB3WHVOOXJ0NXh4T3ZSYmNTeDNGWU5jdjB5aThQNzVrSXQxYkdpR21WTmwvRXlWaWJPQVpyK013UVVwZ2NkNkdoZ28wL2lwTndINm5rSWx5RXR0MWU4bzlRb0l1bVgrTHdWZmhYOGRuZzRrSXhlQU8zRlBGRnJndWFzRzdzZ1dtQkhBU0ZKcUVPOCtuLy8vMy9lVXdvM2NYZ2xvQ2ppTzFlTW9WMTJmcEJWVDVENThnQkFmRnAyMkVBNzVUUzNEVWE2WXJkU29rTXV2MVZqSFFZMGtPekMydjFuSms0aXNuMVFRS3E1OFNrSVVla3JFNFBNc3VETG5lUGxXcFdlT0VXaWVabU1pQyt3UmVmZzJYc2dQcGhsUkhJUEg4NVBwd0oyNnBvOU1Dd1FkUUdKd2hMczR2UDc1YlF4MjJ6RlE3VUNaQ1BaR2MiLCJtYWMiOiIxMjE2ZGQwYjA2MThiNDY3Nzc2NDZmZDdjMjJmZDk4YTIxZmI3NWU0ZmM2OTA4NzM5N2ViMGZkNGZmNTRhNDU3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRCVSt0WTR3dzdSZEFpZ1Zsd01ac3c9PSIsInZhbHVlIjoiRmVoWnZmS3U4MlllbE1oTnZ4RTh1bEljTVhjdzNhZmsxbmhZeFI3enFTRlpsbENuZkZxc1BuRTdUZExaSE9ZRDhYQW5tSDF1bWJQZU96YTZ4QmhCOEkvY21uK3FpWVVPa29kQjVqT3B6UXRHNmdzRjNNMVFlTGxEQU5pZk51N3VrTTQ3T1U5S0Y3cEZpQW1xclovOExhOUZqcnJ5RDI3OHl3NndvRlpmREMzMExqdHpOVzVBb1MzUXJuVm5ZR0xCSXlibWs5V212UzVtWlU1aXdjUWE5ZmF5L2NLd2JNLzJ6a3pBcjNwekpYc1VLdGdFSUZ1dDI0Z1FJdUJrRTlVV3pEVHZNWUVlamY3MG5icXIzc0tVOWd5bWhUY2ptR3dIbEZqQnljZlBwT2dwM2pVVDRsNE40RExpamlNSjk0UWwybXl1QTNUVElHNnQxQmJiN045L3ZrZm1odDlwbExVeVZYUmZobTVoVHY0YlNGdjA5YmR0bCtOUTczZWIyRHhoZG5aN1ZieVI3cFByYWJycFdCZ0cyZjlJYW45V0NDNG9OMlpENUM2cUMrb2t0QlNTUGpzdk5pOHRaVkFsUzAxWGU1NVdWbDBqc3ptTGhManRhWFpWY3ppdWYxeTJjTHVLN05aS0JTN1NqeHR4c3RkR1lxV3VtVVZKT21BTk9vcVciLCJtYWMiOiJmNjAzNzZmMTNjMDQ4NjM4ZDRiNTM4MTI5OTZhNTUwNjJjMTU4MGU0Nzc0NDlmZWVlMDM2MjFlYjExNmYxOGU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1912222149\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1795470076 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:32:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVmRFpMa3RYc1JsUU9kOEFwYm91Y1E9PSIsInZhbHVlIjoiaCtRcnJIaXdaQm5QVW9nNjg2NWFiZDhHSlZaOW5kQWxkWFNrc0EwamtwbUEzOGdhY25IbmV4d2h5NmFSb3NyUkM3L2wxVml3cjhoMW1iSE5YZzd5aG9OcUpWcXQrU2d5MzQ2aDZOUzJYb0JPWWZQc3pWaGExa0hGdnJwMm5NbzlsYnFsVUZ5cUpwcnp0R2VIQkdZM2hVT3VEeEFueUlaN210cnVvUU1wNENkLzlVUWdQV1hxbFIvRG1YaHB2T2hSckw4Qm5rL2tmelFLNk5Yek5QWkpzakhYUk1VM1NEMStNbjVoclRjSkxzY0RzVVR0YmpET0VERWdCSUZneWJBYkNlZ3YvVld2aHZYN1BCNWNJM2JReTVlWCtacWtzYkNZVHRlUExZS1lRK3RuYTBtNkd0Z1Bkc3dYajFBRVZ1VC92dVhUKzF0TWVzZitwMmVHZ2srQVRVMjJMWU9MZlc5NVNLOXQvYnF6RnRLZmhISWlLV0FWSzQ5Nit3WmVieVBlYUlWNG8yS3p5SVVHSzNnbDkrbDV1TzM4dXdSN204TnNjelJRZkdJd2hGQ1VVT3ArMk0rSit4STFpeko3ZGhaZzREeGVEaHcveDFGeW0yTVZoYkVaUnU2Mnl3ZXg5ZGRuVndYVzlzdEErYUR6UkxWYUZYbjZsZWRRWWF0VUU1alYiLCJtYWMiOiI5Y2MyODNmNmRlMmVlZWFlMDRlNjE0NjAzZjJmMmNhZWZjOWExOThmM2IxOGRlMjg4YTA4ODNiNzI3ZWM2NThiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBsK0V1c2tyayt6Qk9oaGNuNDg1REE9PSIsInZhbHVlIjoibnlySnVZZW4zKzMrd1lsUXlkWDFPVmczOG5YSGhGZVRUM3BIZkkwcGk0cmNnQVlYK09BVjlNUzBDQi9wYUF5OXkvVFpMck1CUlhqaUdiT2RHa1dtamtJTDRLcy83U3pPSldiN1I1S1I3Mk5DWHEyV2h0ZDJGNGtEOFR0aUpXa0NUcFhCQTFoZ1ZHamM3ME9qZXo0UVBjb3o5RFBYeWVxR3E1Q0pkN0hmZ1I1L0luMjlxc3dHL3oydE1pa3diM3VwVWRFcFNFeTVFd3g0TGRSYkoybC96UTJ4N29XSG5YdW05aVFBWVhrdkxLaXJTSlpUOEN2a1h2K2FQZU5QbGhRTFhjV2FPUm9kcXVrcHQrRmwrOG4xYkVPejRNdFlQQnNxWWtYQ2xVT0JvS1l6V0JqYnp4ODNtakppQWs1dnQraEpCcWc0b1Y5dGMvRDAyUkxLWDBMS1JIWEJWaE5KaFhjL3YyMFNlemdaMDVIMkgvWmNHWGVjbTMwak55REwwTGZ3cFhWTms0c0d2V2g0RmlqODgwVlhsTWxKRS8ycXVLT1ZCY000VDg1R0J2MVJBZEtMenplNUd5ZmRvT2tDcVpOOG1qQkV2MlBOM2xVU2RTeTRiZTRLUFBWUWVwS29lWXVlY1k0RHEyUisxNTMwZGdsaFJDYmI3NURJakU0bHRqOEIiLCJtYWMiOiJhNGQ3YTdjYjQ2ZTEwMmIwZmFkNGViZmMyYTRmNzk5YjQ5ZTQzYzI3OGVmZjcyNTEzODBiMjQxZTEyZWJiZDgxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVmRFpMa3RYc1JsUU9kOEFwYm91Y1E9PSIsInZhbHVlIjoiaCtRcnJIaXdaQm5QVW9nNjg2NWFiZDhHSlZaOW5kQWxkWFNrc0EwamtwbUEzOGdhY25IbmV4d2h5NmFSb3NyUkM3L2wxVml3cjhoMW1iSE5YZzd5aG9OcUpWcXQrU2d5MzQ2aDZOUzJYb0JPWWZQc3pWaGExa0hGdnJwMm5NbzlsYnFsVUZ5cUpwcnp0R2VIQkdZM2hVT3VEeEFueUlaN210cnVvUU1wNENkLzlVUWdQV1hxbFIvRG1YaHB2T2hSckw4Qm5rL2tmelFLNk5Yek5QWkpzakhYUk1VM1NEMStNbjVoclRjSkxzY0RzVVR0YmpET0VERWdCSUZneWJBYkNlZ3YvVld2aHZYN1BCNWNJM2JReTVlWCtacWtzYkNZVHRlUExZS1lRK3RuYTBtNkd0Z1Bkc3dYajFBRVZ1VC92dVhUKzF0TWVzZitwMmVHZ2srQVRVMjJMWU9MZlc5NVNLOXQvYnF6RnRLZmhISWlLV0FWSzQ5Nit3WmVieVBlYUlWNG8yS3p5SVVHSzNnbDkrbDV1TzM4dXdSN204TnNjelJRZkdJd2hGQ1VVT3ArMk0rSit4STFpeko3ZGhaZzREeGVEaHcveDFGeW0yTVZoYkVaUnU2Mnl3ZXg5ZGRuVndYVzlzdEErYUR6UkxWYUZYbjZsZWRRWWF0VUU1alYiLCJtYWMiOiI5Y2MyODNmNmRlMmVlZWFlMDRlNjE0NjAzZjJmMmNhZWZjOWExOThmM2IxOGRlMjg4YTA4ODNiNzI3ZWM2NThiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBsK0V1c2tyayt6Qk9oaGNuNDg1REE9PSIsInZhbHVlIjoibnlySnVZZW4zKzMrd1lsUXlkWDFPVmczOG5YSGhGZVRUM3BIZkkwcGk0cmNnQVlYK09BVjlNUzBDQi9wYUF5OXkvVFpMck1CUlhqaUdiT2RHa1dtamtJTDRLcy83U3pPSldiN1I1S1I3Mk5DWHEyV2h0ZDJGNGtEOFR0aUpXa0NUcFhCQTFoZ1ZHamM3ME9qZXo0UVBjb3o5RFBYeWVxR3E1Q0pkN0hmZ1I1L0luMjlxc3dHL3oydE1pa3diM3VwVWRFcFNFeTVFd3g0TGRSYkoybC96UTJ4N29XSG5YdW05aVFBWVhrdkxLaXJTSlpUOEN2a1h2K2FQZU5QbGhRTFhjV2FPUm9kcXVrcHQrRmwrOG4xYkVPejRNdFlQQnNxWWtYQ2xVT0JvS1l6V0JqYnp4ODNtakppQWs1dnQraEpCcWc0b1Y5dGMvRDAyUkxLWDBMS1JIWEJWaE5KaFhjL3YyMFNlemdaMDVIMkgvWmNHWGVjbTMwak55REwwTGZ3cFhWTms0c0d2V2g0RmlqODgwVlhsTWxKRS8ycXVLT1ZCY000VDg1R0J2MVJBZEtMenplNUd5ZmRvT2tDcVpOOG1qQkV2MlBOM2xVU2RTeTRiZTRLUFBWUWVwS29lWXVlY1k0RHEyUisxNTMwZGdsaFJDYmI3NURJakU0bHRqOEIiLCJtYWMiOiJhNGQ3YTdjYjQ2ZTEwMmIwZmFkNGViZmMyYTRmNzk5YjQ5ZTQzYzI3OGVmZjcyNTEzODBiMjQxZTEyZWJiZDgxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795470076\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1414994305 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414994305\", {\"maxDepth\":0})</script>\n"}}