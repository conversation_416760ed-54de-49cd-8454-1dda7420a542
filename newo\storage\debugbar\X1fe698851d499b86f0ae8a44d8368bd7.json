{"__meta": {"id": "X1fe698851d499b86f0ae8a44d8368bd7", "datetime": "2025-06-08 13:11:59", "utime": **********.548946, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388318.023302, "end": **********.548979, "duration": 1.525676965713501, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1749388318.023302, "relative_start": 0, "end": **********.365242, "relative_end": **********.365242, "duration": 1.341939926147461, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.36527, "relative_start": 1.3419678211212158, "end": **********.548983, "relative_end": 4.0531158447265625e-06, "duration": 0.18371319770812988, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024170000000000004, "accumulated_duration_str": "24.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.457832, "duration": 0.021920000000000002, "duration_str": "21.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.691}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5085092, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.691, "width_percent": 4.841}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.527013, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.532, "width_percent": 4.468}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-774451945 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-774451945\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1406596967 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1406596967\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-735673560 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735673560\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1657849714 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749388306480%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBIdmh2eG9wVVZ4TXhoUUI2TE5OckE9PSIsInZhbHVlIjoiTEFjNThydWNuelFBbFNtdWdSdFlVOVJ6RnYrdWJUQzVkMjlRbEx4STB3K0tFOW5kY3M4TjZYM3VaZUsvRjAxU1pDcmQ4UkpoalZoVkJMU25wL3VwN296S0Fxbm5PRXk0MWtqamowaTNpWk8vR1VRVU0wcVhQc3A1TUhqQWZIOU1LWmFrTjd4TXdZNmdCeGdKYmVuZkNJWXpQMW1rSEtLK1IrSXppdXBqMmtyQ2ZKdi9tdzl6N2RyMnNkOXM2TDk5c3JUeE5CTW5HUTRDenprTmhhQ0l2Q05ESFo0NGc1SzFNYlAxTFZRZ2FUR2ZhSmE2T3FkdFZCbFJmTTg2bUwwdWY0akpWeHR0UGFtT2dENExZc3M3YmoxLzdpb0FSc2FpbWdVWExjUXpUVkFpUDB1Qi8vci82V1NxM3VJWmtFai9XYVliZHIyb3h4NzRWTUF6SDRRNFA1NitvZ1ZFS1ZVcVJoZFhkVHVRUEdMVTRvRFRScHRyT01IT0EyODczS3I5Vk5TUmZDQmtkRmZnVE91L3hJL3hWbEJNZnZsK09lUVpkbjBPUFNYL0pyMU1QSXNXcG4wRVRLMWFCdFhHOU9vcWY0cHRYWXk3N01mc1Q2NTVYK0FtQ3IySDF4akJ6akJqMUsrUlJUcW5telk3c1FhRzdWWmVnM0JVUnlQcEtBOWoiLCJtYWMiOiI0Mjk5NWVjM2M2MDY5MzE0ZDYxZDQ3ZDdkNjc5MDEwNGNlZmNjMTcyYjc0NTM4NzA5MmQwYThlMGJhMzM5YzFiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imc5YTU4V0xKZzlHalJ5MXdlaXF6TWc9PSIsInZhbHVlIjoiU3R3Qk1Gb0V6TzE1TGI4V01zK3ZYbStodnAzbzFVaHEyR2sremVsbXVNVHovVG1CMmVPOVIrSEovSERWd1ZhZGcyTXpJMEpTSjlkUEo4N0tNNmJJRnFWRkFORkI2N1VwOEx3YmdrbWJDai9aa3BTczlEL2w1Yk13VzVuMEsxZDErVURzYXZVYUZZc3lzMDhLNGQ3dTZuaTBtdHp6eWhEZ09aOTFKbUpHOUd2WUNlSUt2Z1l4cCtseWt3UVlIbzlOYnJubUtOYm5zc1FsRkoxbFNueUZ0SDl6WEV0b1p0V2swWEQ1M2FwTk4rajlwVzdSS29oVFd0SVU0SmZqWlFBdTNIMVlJVFJ5ZTRoSkxYMEpnZEhnbFNRN2d1RTBuemNBMG84UWltOGFzRVZhajEyaUhhSEdCbEloaHYzMU0vSUR2dVVtOW81QW14QXJmc0I2ZEc2VUlLczlDc0cwejJpcTJBNHVUOXZHNnIwSFdQeDVzMmJkQVJSaCs5eno5S1lBNW9FcnlGNHp3SGdHS2ZpZmdXeEpZNFNiN0FsQnBqUDNOcUN4bUNVMmJYQ2xoRC85cnh3MHcwcWY5Z0FJeEc1cjV2Q0ladm1SOUp4QWszWDRyUUVhYTVmZHJ4NjBwdVJ2ZUJKMFdWY2t5R1YwdnNiY3BES2RCeWliM2NTcytsYWciLCJtYWMiOiJlNTcwMGZiOGMyNTdkMmM3ZTVmMjUzNmY3MjJhMjVmMzVkYTgyODNlMTAyOWY2MTlkYjhmYmExNzJlMGMyNzRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657849714\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1640708105 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640708105\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1495624314 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:11:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVqaFlZTFd6dFlWQjlNaVRPWUE2d3c9PSIsInZhbHVlIjoiYjhURW1oUi9jWlYzbEZQMTRWRjF3NVhBZkQ3UGU5ZVFPNTVlMnZmVm42TlFPKzBQSmlvb1VnakcyUVJXenQrR3pDN0JWZXEwU2xyYmFYRkxiSzN4TFUrWGJtQjNaMXVyVjF6V3puZDdEcXkyMjhjcTlvS1VZd1pVS3J2TTNzQ1d2UUNhLy93V0s3MVF1MzBFUmlHNVU5V3BzRG1HT3p1NWRsR2RXUU9WT05sa3B2amJla0wyem4xS0dhd3I5SDBNZU5vS1oxVi9nY24zNlc2RElOcUxGQW1ZbUQ2OTY1TnZvQWh2WGh0RTFhTndYTEFTUTJ3eDRvektFUlN0T2g4bDdDNlhKL2NrL0xFbnM1SmtQdWgzekxGTENxOVZUQklHeGUyUlUzL01saENEbHFTZjM3cVQ3SnJ3SDBvN0VSRmpINmsycDNaY1ZDYnNSYVhlMUhXMVVuMTB5OUhLRUNhOHgzYnBEeHN0M3dyNTBhN0JOSlNKamFaaktzbmpMRlZmUWx5QW1YeE9QWDdiNGt1VUpzSjlkajFpMy9wRktjRmZPditveWxIdlA4R0tCUDR5Ykxja0FXem9HUGx3QUhRekE3eUtiUzNjclAxREZydUp1VTNuQjZlck5GM2JRalc1TGQwRnJqMElqcGZTZ1NoRi9Hd3FKVFpoUzU4YWpKN0wiLCJtYWMiOiI2MzU4OTFiZTEzOTVkZTFiMTllZjQwOGQ5ZjMwNmFiNGYyZWQ0NzNjNWVkZDdjNmM2NGI2MTJjOTAxMzI0ZGVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:11:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNrQytVL2pOVldYY29hSjNXNEV1T2c9PSIsInZhbHVlIjoiUDdHMTRKRDJmaUtwNS8wS012SDFuVUNBTEJSQlN4dHhNREIwR05OMitTUEp0VzRwbEl1a0hPbHE2YkQ1N0JnWTV3cG5TZ3VGSGgwejQrTlYxRWtIUjVwMktXeXFzYURnZTZYT2JabVpuUU5YZG1IUFBWUlEvZ2JjWk9NTzlyR0xxTVQva0cxRkJTc1BDTDBGZ0lOb1YxZWZ4QzQ2d3p1NnVueFpNNktCVEdaTTBQdXFHQWI0TlkreVRIQzk5Zk1rTEIwcnFBb1NsRlBuTlZIVkVPQktETXJyekY4cHZ5anFTQWlhZTh3NFF4akN1N2ROeXFqalcyL3BHak5WeWxrZ1NCTGF4M1hSdEtDRmV4VGs3MUFlcDYvRGhZVWtKN2d1bWdxbUtZZG81bEh1MGg2TXJVV2lvMWpUZlNjYUpvcWhpS3BzU1RXT1FBUkhEV1ljK21lRklSVGlpa25vb3YrSUZGb3poeDEyMCtFQ1g4bmg5SmFuMjBVRzdJd0l3STdTUWptNGRVc1REa2JpR0pxdW5UeUtzVnFBSzVjS3RjS0lvdFl3cDExVjRzNGVQUjZsQVIrbUtWVUlTcW9ZblNuM2JvaE9Ha0tpVXJQY2p3L1NycUZTOTdwNVZhUDN4eC9EcExZODVRVHhMMitzTlFQNkY4NWl4Y2pFUzk2REV2WS8iLCJtYWMiOiIyMjgwMTQ2Y2VhNDFiMmI3YWIzMWVhZDg3OGNkOGFlNDY2YTJjOGUzNmQxYmMyNjEwNmUzNWFkMzVlNTIwYzRhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:11:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVqaFlZTFd6dFlWQjlNaVRPWUE2d3c9PSIsInZhbHVlIjoiYjhURW1oUi9jWlYzbEZQMTRWRjF3NVhBZkQ3UGU5ZVFPNTVlMnZmVm42TlFPKzBQSmlvb1VnakcyUVJXenQrR3pDN0JWZXEwU2xyYmFYRkxiSzN4TFUrWGJtQjNaMXVyVjF6V3puZDdEcXkyMjhjcTlvS1VZd1pVS3J2TTNzQ1d2UUNhLy93V0s3MVF1MzBFUmlHNVU5V3BzRG1HT3p1NWRsR2RXUU9WT05sa3B2amJla0wyem4xS0dhd3I5SDBNZU5vS1oxVi9nY24zNlc2RElOcUxGQW1ZbUQ2OTY1TnZvQWh2WGh0RTFhTndYTEFTUTJ3eDRvektFUlN0T2g4bDdDNlhKL2NrL0xFbnM1SmtQdWgzekxGTENxOVZUQklHeGUyUlUzL01saENEbHFTZjM3cVQ3SnJ3SDBvN0VSRmpINmsycDNaY1ZDYnNSYVhlMUhXMVVuMTB5OUhLRUNhOHgzYnBEeHN0M3dyNTBhN0JOSlNKamFaaktzbmpMRlZmUWx5QW1YeE9QWDdiNGt1VUpzSjlkajFpMy9wRktjRmZPditveWxIdlA4R0tCUDR5Ykxja0FXem9HUGx3QUhRekE3eUtiUzNjclAxREZydUp1VTNuQjZlck5GM2JRalc1TGQwRnJqMElqcGZTZ1NoRi9Hd3FKVFpoUzU4YWpKN0wiLCJtYWMiOiI2MzU4OTFiZTEzOTVkZTFiMTllZjQwOGQ5ZjMwNmFiNGYyZWQ0NzNjNWVkZDdjNmM2NGI2MTJjOTAxMzI0ZGVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:11:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNrQytVL2pOVldYY29hSjNXNEV1T2c9PSIsInZhbHVlIjoiUDdHMTRKRDJmaUtwNS8wS012SDFuVUNBTEJSQlN4dHhNREIwR05OMitTUEp0VzRwbEl1a0hPbHE2YkQ1N0JnWTV3cG5TZ3VGSGgwejQrTlYxRWtIUjVwMktXeXFzYURnZTZYT2JabVpuUU5YZG1IUFBWUlEvZ2JjWk9NTzlyR0xxTVQva0cxRkJTc1BDTDBGZ0lOb1YxZWZ4QzQ2d3p1NnVueFpNNktCVEdaTTBQdXFHQWI0TlkreVRIQzk5Zk1rTEIwcnFBb1NsRlBuTlZIVkVPQktETXJyekY4cHZ5anFTQWlhZTh3NFF4akN1N2ROeXFqalcyL3BHak5WeWxrZ1NCTGF4M1hSdEtDRmV4VGs3MUFlcDYvRGhZVWtKN2d1bWdxbUtZZG81bEh1MGg2TXJVV2lvMWpUZlNjYUpvcWhpS3BzU1RXT1FBUkhEV1ljK21lRklSVGlpa25vb3YrSUZGb3poeDEyMCtFQ1g4bmg5SmFuMjBVRzdJd0l3STdTUWptNGRVc1REa2JpR0pxdW5UeUtzVnFBSzVjS3RjS0lvdFl3cDExVjRzNGVQUjZsQVIrbUtWVUlTcW9ZblNuM2JvaE9Ha0tpVXJQY2p3L1NycUZTOTdwNVZhUDN4eC9EcExZODVRVHhMMitzTlFQNkY4NWl4Y2pFUzk2REV2WS8iLCJtYWMiOiIyMjgwMTQ2Y2VhNDFiMmI3YWIzMWVhZDg3OGNkOGFlNDY2YTJjOGUzNmQxYmMyNjEwNmUzNWFkMzVlNTIwYzRhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:11:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495624314\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1923974979 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923974979\", {\"maxDepth\":0})</script>\n"}}