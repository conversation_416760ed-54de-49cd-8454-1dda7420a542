{"__meta": {"id": "Xb77050ba728006d12a75c75a040ac702", "datetime": "2025-06-08 12:56:23", "utime": **********.606842, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387382.109114, "end": **********.606875, "duration": 1.4977610111236572, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1749387382.109114, "relative_start": 0, "end": **********.460716, "relative_end": **********.460716, "duration": 1.3516020774841309, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.46074, "relative_start": 1.3516261577606201, "end": **********.606879, "relative_end": 4.0531158447265625e-06, "duration": 0.14613890647888184, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00732, "accumulated_duration_str": "7.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.540627, "duration": 0.00474, "duration_str": "4.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.754}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.568264, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.754, "width_percent": 17.35}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5861628, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.104, "width_percent": 17.896}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2133907107 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2133907107\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1859134151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1859134151\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1374589448 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374589448\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1312087916 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387360042%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI5UDN1N2dvM1oveklqVUZNdXFKOHc9PSIsInZhbHVlIjoiRVc2WHl0Mms4bkVnZXlLQWd5TVYyOTcyZXVIcitHYklBRWJjLy9SSzFhcTl1czZwcDNVRE9heU5PU0tGcnEzaUEyaGZYT1VHczhOTHp0QmkwcW5URnY5ZFdJelN4Ujc2Vk9OVkdkNE96eUthNTBTenoxaUtQR3AwOWM2dlVpWHZjRmk1UEJsZnVQaXlSSTB1T3krQ0VoT09MSmdzSWFXYmVrRkFpVzE4WS9yaDBMcG03WCtmaHAyWXVUMVR1SnM1aWpZMVVPeEpsTU5LZ0k4K3J4QjdZKzF3aG5XR2owWjN6SHNBbGp0SFdRT0NmVSttbGNNR0o3WWhMeTFHMjRSUjhkeGpEREFWYkpSNmRDOW9hR1hOWnN6a3hNM09WOTV0d3Q5NzRXOVZ2Ykk1RERWMnVBVVJzUmRRamZDVENtclNDenhiUVcwdFFSWHYyOUlaenMwdXhBWGlYYjlVMGxIem5xODJ6VGVXL1Z0MkZCaWZLSC9DUFdOMW9WR1VvU2JreS9rT0VsNEFDUytCUWxzMGtJbkN5cFNnWGFPUHFDcTVrNFVHb0VCN3huK1FLbVNLQXllTDMzQldEdjM0bjkyeWJYYUdabitZTzJYTXpqV1EvWW1HbXY5bmlHY2JWMXJEK2ZHTUVhRm1rajBlaWNvb3FEOFcyQThPcnAxQk14anAiLCJtYWMiOiIwZjZmNDA0OTJkYjg4ZmFjNGIwMmVlMGY1NjM2Njg0MGQ4ZTY4ZDMzZDQ1YzZhYWQxM2ZiMDc1ZmY4MmRkYzEzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFUcFBkeWxxKzJhckluNmUzZzJ1UkE9PSIsInZhbHVlIjoiZUVJWDdoVDZqazR0KzM0cURTYndvTEdXQ2kwVlVLRDFEQW93ZTNCVEV5MXVBa3NmcmNpYW9OVnA1cGNGRCt5SUlVZ3RYT3JXNkVpeEpJc1BkclJXOWZRdkVEY0R5c2QxalZkVWhrWndiV0I5NURmVVI2Q2FOMGw2TGtIT2ZCck1jRC9naDR3VEY3SUVucG1UbnpueDZmaUlRUlQyNklqbndSOEIrM3R1TXNBWGJDWXB6a1JnVWYxNUNScjFtZ3I0ZjVreUhqWlJDcFZjUTl5cVRWQ1BQMDlkZFZaZ1V5Y3dKUHBWZUdRLzFMaG1GS0g4ZDNNa1M0RjZuRnU4ZllUYkR1RE9wUXcxbGwyaGFIRTMvY2R5VVprVjR4WVJyY29EakVmS3EvRVE2amkzaDFYNGVRTGtuSkVISlNSZzRhQzNDSUExRlNrRGlvcnNUMXQ2UGpNUis1RXRmTkxwRVlQMXhTTjVadVZ3ZVdaTGdZNlp5VVdQa1BsRzNqQlBjdllWM0VDeElqNUh6TnVFeTNWbXRxdWtMTWl3QjdzVVRJY0RPNjc5cmVOUUQ0QUFjK0thd3paYlE3K2dyQ1NSMHBJU01wUmFkaFVFekdsckFESmtpLzNjdTZLc0YvSTU1cTZLMERUSm1RM0c5RkJEUnUyY2UrVHJtUXBGVnc2NnNRdzMiLCJtYWMiOiJlZTIxMGQxMzVjZWIxN2Y0ZGVkMjMzY2E3NTIzNTliNDI4MTYxNWMwN2JiYmVlMjljYTQyMDBmYzM4MTMwNGYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312087916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1540623397 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540623397\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1798438590 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:56:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF1UnhydlRZSy94VHdUVXY3U1BkR1E9PSIsInZhbHVlIjoiWFd0bFZqOENHdythbFJoaU5SN0xEZllBT1V1bmNLU2lSZFMwQ2xsVnVOM08raTl5OTdyaWZvRlNBb3h4eFhuQ1Q1TDhMaTV2TjZGcnhMeU5Sa3EvQzZ1cHYzSmxVWmdhSExWTFN0bGQzNFlTVWQwZ3ZYbUtEeWJxckhPbjQ4L0tIM0dUMmcyazZxVUI5TDczbFIxSmUrNW93Snh5eGZIeW05SHk0UGNSZGpTQ1FldEgzUEtCWXBUSTNBdU9Rd1pWVUl2amdEWlhLdEh6SzJZNDFoSzBtdmVoMzB0a2JseUNSYzZMcTUwR0lpc2lqbmx4cUhoUE56UDBjc3FNL3JDNmgrNmJMdFFzK3grNzE1WDlFZVE4UStSU1czeFo2ZHdiOFZLVEgwY0dVYnd5KzZnbndJL21LelZ6TVhMcERSUndQZ2tORkRHR09MOUxuSlY0WkdvQ1I4cW8xVzk3aHlxa0lkVVR2YTRHQXQrOUVlRldyekxubVcyQkVtanVJa1BCTkk4dGprNUhqOERhMkc1bnBLd2ZSOXI4OHVxYVpRUlFuVmNmN3J1YWw1ZThGUnVlWlQwMzNFZTRWNFkwSGEvZ0F4SmtXRExjMUI1V0l4Rlc4d09RMGFzR2NJSERBTXl6ZkVOb0RyU2lQcHpEUnpCa3djeEhBd2lIcXJGZUpibHQiLCJtYWMiOiIxMmQ4YTc5NDVkOTJiMWUwNmE5OGYyZmRiNjRkNDFkOWQxYTFhOGEyYzJiZTYyZDJiNDAxMzViZjVmNzlhNmM4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImYxRld0ZkFKalhoU3NhQTRNU01DTlE9PSIsInZhbHVlIjoiVTZqSDdYMkpKck1vbVNSTGNLK2w1SmJCcnIzaCtLVXZhMmNqaXJCY254U2xRQllEanVld2dVZEZ4YTFlcXlwR1J6VVVCbjFMK1FwY3AvanFBZVR2MzhyM09qWFFCUmNzMlROU2E3R0VWVW1DaUl5RThJbW83aXpxbU1LTmg3SXR5OWR4L0FOeGM0UXZseUkyanVGUkRNc1p4djdQZVU3OHR2NWtYTTV5UTlsUDRhenkrY2w2WjYva3p3dGdDdzVCSWNqbFhvR0Jneis1MmJqVzFtMURGTllNbjZCbGZSRTJia3oxemt0YUZnTVlKMzFNUWpWNkhDaFVkMGNadUxYbGJsWFNMNlNKbjJnSkJQTEVkRWZ2UzZTNHYzMVJHdGxnLzBVdWlCMVNZMkRPK0FCZ05zeEU4cWpYeXdtZmR5cFRiMzBwUXRBZ09Gd1RlRVFLRDc4YUJ4WkN3c0JFMTltYW9WN0FWck9VdDRCOWYzZXhaRGpWdUxIQnZRbzNEblRLdDh6MXBwM3l1Q2FUK0F0dU5POW5WR1JqVnAxZmdxUjNvZW15emczR3JUOUdBT1VtVVNQcEJSLzE3SlZYVG5LS2hJVlYwbW5ZWjJUV0J5T01XZlNIRHJWT1k3dVFLenVUOUdLdFJmMWtlMzkzeEo2YUJrSEZjVVZOYUhlNkdBak8iLCJtYWMiOiJhOTQ0YjE2YTlkOTNlNDljYmNhYWQzY2NjNTFhZDI2MDJlOTJjZDBmNGIwZDY5NTQwNDc1ZmRjYmVlZmUyYjc1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF1UnhydlRZSy94VHdUVXY3U1BkR1E9PSIsInZhbHVlIjoiWFd0bFZqOENHdythbFJoaU5SN0xEZllBT1V1bmNLU2lSZFMwQ2xsVnVOM08raTl5OTdyaWZvRlNBb3h4eFhuQ1Q1TDhMaTV2TjZGcnhMeU5Sa3EvQzZ1cHYzSmxVWmdhSExWTFN0bGQzNFlTVWQwZ3ZYbUtEeWJxckhPbjQ4L0tIM0dUMmcyazZxVUI5TDczbFIxSmUrNW93Snh5eGZIeW05SHk0UGNSZGpTQ1FldEgzUEtCWXBUSTNBdU9Rd1pWVUl2amdEWlhLdEh6SzJZNDFoSzBtdmVoMzB0a2JseUNSYzZMcTUwR0lpc2lqbmx4cUhoUE56UDBjc3FNL3JDNmgrNmJMdFFzK3grNzE1WDlFZVE4UStSU1czeFo2ZHdiOFZLVEgwY0dVYnd5KzZnbndJL21LelZ6TVhMcERSUndQZ2tORkRHR09MOUxuSlY0WkdvQ1I4cW8xVzk3aHlxa0lkVVR2YTRHQXQrOUVlRldyekxubVcyQkVtanVJa1BCTkk4dGprNUhqOERhMkc1bnBLd2ZSOXI4OHVxYVpRUlFuVmNmN3J1YWw1ZThGUnVlWlQwMzNFZTRWNFkwSGEvZ0F4SmtXRExjMUI1V0l4Rlc4d09RMGFzR2NJSERBTXl6ZkVOb0RyU2lQcHpEUnpCa3djeEhBd2lIcXJGZUpibHQiLCJtYWMiOiIxMmQ4YTc5NDVkOTJiMWUwNmE5OGYyZmRiNjRkNDFkOWQxYTFhOGEyYzJiZTYyZDJiNDAxMzViZjVmNzlhNmM4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImYxRld0ZkFKalhoU3NhQTRNU01DTlE9PSIsInZhbHVlIjoiVTZqSDdYMkpKck1vbVNSTGNLK2w1SmJCcnIzaCtLVXZhMmNqaXJCY254U2xRQllEanVld2dVZEZ4YTFlcXlwR1J6VVVCbjFMK1FwY3AvanFBZVR2MzhyM09qWFFCUmNzMlROU2E3R0VWVW1DaUl5RThJbW83aXpxbU1LTmg3SXR5OWR4L0FOeGM0UXZseUkyanVGUkRNc1p4djdQZVU3OHR2NWtYTTV5UTlsUDRhenkrY2w2WjYva3p3dGdDdzVCSWNqbFhvR0Jneis1MmJqVzFtMURGTllNbjZCbGZSRTJia3oxemt0YUZnTVlKMzFNUWpWNkhDaFVkMGNadUxYbGJsWFNMNlNKbjJnSkJQTEVkRWZ2UzZTNHYzMVJHdGxnLzBVdWlCMVNZMkRPK0FCZ05zeEU4cWpYeXdtZmR5cFRiMzBwUXRBZ09Gd1RlRVFLRDc4YUJ4WkN3c0JFMTltYW9WN0FWck9VdDRCOWYzZXhaRGpWdUxIQnZRbzNEblRLdDh6MXBwM3l1Q2FUK0F0dU5POW5WR1JqVnAxZmdxUjNvZW15emczR3JUOUdBT1VtVVNQcEJSLzE3SlZYVG5LS2hJVlYwbW5ZWjJUV0J5T01XZlNIRHJWT1k3dVFLenVUOUdLdFJmMWtlMzkzeEo2YUJrSEZjVVZOYUhlNkdBak8iLCJtYWMiOiJhOTQ0YjE2YTlkOTNlNDljYmNhYWQzY2NjNTFhZDI2MDJlOTJjZDBmNGIwZDY5NTQwNDc1ZmRjYmVlZmUyYjc1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798438590\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1034529558 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034529558\", {\"maxDepth\":0})</script>\n"}}