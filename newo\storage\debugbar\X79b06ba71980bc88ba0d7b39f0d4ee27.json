{"__meta": {"id": "X79b06ba71980bc88ba0d7b39f0d4ee27", "datetime": "2025-06-08 13:00:41", "utime": **********.301844, "method": "POST", "uri": "/vender", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387639.837487, "end": **********.301875, "duration": 1.464388132095337, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749387639.837487, "relative_start": 0, "end": 1749387640.988286, "relative_end": 1749387640.988286, "duration": 1.150799036026001, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749387640.988306, "relative_start": 1.1508190631866455, "end": **********.301878, "relative_end": 2.86102294921875e-06, "duration": 0.3135719299316406, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51650272, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST vender", "middleware": "web, verified, auth, XSS, revalidate", "as": "vender.store", "controller": "App\\Http\\Controllers\\VenderController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=65\" onclick=\"\">app/Http/Controllers/VenderController.php:65-149</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.03789, "accumulated_duration_str": "37.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.072755, "duration": 0.02258, "duration_str": "22.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.594}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.121076, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.594, "width_percent": 3.088}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.173945, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 62.681, "width_percent": 2.982}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.181649, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 65.664, "width_percent": 2.85}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 89}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2249198, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "VenderController.php:89", "source": "app/Http/Controllers/VenderController.php:89", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=89", "ajax": false, "filename": "VenderController.php", "line": "89"}, "connection": "ty", "start_percent": 68.514, "width_percent": 3.009}, {"sql": "select count(*) as aggregate from `venders` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 399}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.233584, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:399", "source": "app/Models/User.php:399", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=399", "ajax": false, "filename": "User.php", "line": "399"}, "connection": "ty", "start_percent": 71.523, "width_percent": 3.827}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 91}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.243415, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "VenderController.php:91", "source": "app/Http/Controllers/VenderController.php:91", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=91", "ajax": false, "filename": "VenderController.php", "line": "91"}, "connection": "ty", "start_percent": 75.35, "width_percent": 3.51}, {"sql": "select `value` from `settings` where `name` = 'default_language' limit 1", "type": "query", "params": [], "bindings": ["default_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 92}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2509751, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "VenderController.php:92", "source": "app/Http/Controllers/VenderController.php:92", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=92", "ajax": false, "filename": "VenderController.php", "line": "92"}, "connection": "ty", "start_percent": 78.86, "width_percent": 7.337}, {"sql": "select * from `venders` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 258}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.26188, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "VenderController.php:258", "source": "app/Http/Controllers/VenderController.php:258", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=258", "ajax": false, "filename": "VenderController.php", "line": "258"}, "connection": "ty", "start_percent": 86.197, "width_percent": 3.457}, {"sql": "insert into `venders` (`vender_id`, `name`, `contact`, `email`, `tax_number`, `created_by`, `billing_name`, `billing_country`, `billing_state`, `billing_city`, `billing_phone`, `billing_zip`, `billing_address`, `shipping_name`, `shipping_country`, `shipping_state`, `shipping_city`, `shipping_phone`, `shipping_zip`, `shipping_address`, `lang`, `updated_at`, `created_at`) values (1, 'تجي', '', '', '', 15, '', '', '', '', '', '', '', '', '', '', '', '', '', '', 'en', '2025-06-08 13:00:41', '2025-06-08 13:00:41')", "type": "query", "params": [], "bindings": ["1", "تجي", "", "", "", "15", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "en", "2025-06-08 13:00:41", "2025-06-08 13:00:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.271288, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "VenderController.php:117", "source": "app/Http/Controllers/VenderController.php:117", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=117", "ajax": false, "filename": "VenderController.php", "line": "117"}, "connection": "ty", "start_percent": 89.654, "width_percent": 10.346}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1628432516 data-indent-pad=\"  \"><span class=sf-dump-note>create vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">create vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628432516\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.193903, "xdebug_link": null}]}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "<PERSON><PERSON><PERSON> successfully created.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/vender", "status_code": "<pre class=sf-dump id=sf-dump-1100313535 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1100313535\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-161073927 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-161073927\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-218617295 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1578;&#1580;&#1610;</span>\"\n  \"<span class=sf-dump-key>contact</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_number</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_zip</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_zip</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218617295\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">316</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387628836%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjMwRGoyS3k1b2NlQUVtRW5UcXNaa3c9PSIsInZhbHVlIjoiZmMralpoOTlDTStoRFNCdGY3NklVbURvM2x0QzNBYlFmQlQrTmJlKzNMWjZjM3poUkxVek0zNVBYRDFUUXVOQ1NjSW8rblZnSEY2UXpZRG02RjFOYkZaQW1BNmd6SnJHZzFFMGEyT0VhcjZ1ajJMRXpnNlJ0ZEJWNXduS1NXZVZnRUdmczhpR0lxUUdNMk12M3ZZOFJxcHVDRGNwMjZpeC80SWVGNlVUSjVOY1Q0MU1SYm1CaG16ZVl1Uy9PTHVoTFh1WWdUb2ZFMEx1Smx3T0lMQ20xQ25WTUhRWFdoYzUxS001aDl4TE9uVWsxNkJoc0lkTU9UVE9GeXJzODJqazY3RUMyaVVPMmhTb0l5SEllYXJOa0ZWcnNzR2JjWDBPY2l2bGZnQXJrdktxMWx1S2FtZ2ZQbXFKeDl2VlA5cmYvVWlFRXdxN2g4SnJoMlphdlBvWXdqWjY5QVVNM1lRTUFqYU1heUtuM1BXbkNNTWVPMDErMHhsenBIWlZPNStRa2Q4Um9sR1ZZZnJCZVVaVG02UEpORExNc0NtWTByZXZ5ZWZZZnpnZ0xKMGhhdWZnczZnQ0IxZUdybE0yRXhCMVZ4N3ErcWs5OUpRazg2NzJ1NklhcURYVTZVMU51dGpmbWZiMlVud2VxbUpEMGtsZjUrUmVBcUgwWFJHc2R6ZkciLCJtYWMiOiIzZDJlNTlkYzkyYzAxOGMyMDVjMzViM2E3Yzg1NzMzZDE5MDg5MDQ3OGIzMjM5OWE3OWQ2MTU0NjI5NDlmMDA0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldlZkNrdGdkc20rUy9jSjlLRUhtL0E9PSIsInZhbHVlIjoidlBQVHJyV3ltSFFoUmtBd3Y5R1BLYU1yWTBJd01BSjN4b096Z1FYSFBkcGJCZFNyNlAzM3pTMFloZHJRQUp2Sm82SytRNFI4ejRGMHowdXJuNVZRbUsxcWF4c1A2SVltRXVKZFkxZW90UXNQSUQ1UGRVWmpkdU02alFBekExMUEza3N0eDF2R2l1Z25lS2tDcHNYTGpWTnRtRWVUWGtjYlBEYnd0SzAzSUNmcktWY3NHSnltOHhueC90WVpIeGxYazlJckg5U0F3REdvQnpONzJYdUJUZGZhcXdtQkZuZHBiMzJlYWRJRHVuS0xSNzFiSEFuVzA3RkVoWnFlaDhHbFlhQnMvenYyV2RHOTZwai9vQk53RkVZWi9nZC9sZEVZWkFOYStTczRoOEVnT0RiQUpCb211UWtObVhmSWdkcmZxcThDL201b1BLbkx3Y3FWUjJSMjQ1TXZTdkFqWGVhYXRKeUZ0SzFHbEw5VFdkd2RFckRHNGtmTFREQkl1eWxFc2JKd1dFUkwzY0NMSHBpNkQ1K1BOMmIrQ2k0VjFkeVp3MzJES21NL3lpV1lmUk9SNklhajg4aEQ4QUh0OFdoeTFiWG9pTk4vV1RVZ0duUWFwS2ZKcGhJUUVHZTQwTHNUTzNYVTAzTlpCcFRxSW82WkNZOHJMMjBkaDJPOW1qRTAiLCJtYWMiOiIxMzBkNTkyMTI4ZTkwZmM0MzEzNmViNzhkMGZmYWEwZDg4NGExNzRiOTE1Mjg3YWMxMjIxYmZhNjFjMjE5YmZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-867900848 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldJbmJoZm5rV1BBMEQ0MmtGY1RjUnc9PSIsInZhbHVlIjoidFYyUnBiZ0JjSHhMNDRWZzFYdWt5MmIvbWhwalhGNDZuaVdmbDlhRktrRm53WkFIMWFVbHNaVmg2QjlvK0V5TGJwbGx4U2VndDNMdE9vaUg1Rm03MTV2VHdtRnVOZHFscTBiRW1DRkJ5Z3JlTmlkZ2FqMkpuemhVMlhwRm9Nc2dlSmRiLy9OdVFEOHJTaFVpazdnUE8rMkFqS0dOUUpoelYxRzhDd2dyZ00vWnpMdU9Hc0hUSm5tUENmMFRoOFZDbkwrNll4cEtmWlFoeUtZZWF1RWx3dnNtYnlSRURqSWdKcXpPOTNoZExKcDlwMXgxWHlZSWFacUxoRXZtbDBFeStNMm85aXZmbWVNckhXZ1hkckhzRjhrSWoxcEp6SU9VV3RyRm1Ra3VGWVlETy9VVmdrZDVLdXNQWkdqWDZ4TGFBMmUwRGFXNnZrd2U0T0EvdG55N0VVcGpSM3l5MnZTQTY1YUZ2cjdiSE45NjBuWSs2Q0h0RkQ4UEpabk8yQVhXaUNpTGVwdEFnb0hYam4reDFFU20yTWE5cWkzQ1FpSUIvZmp1MmozT0JZR2t3OWRVT3pvdHgyNTlIazV3M2hyVW5HK0xXYmhRV1ZvbmhiUHF6amkxK0VIWExjTUdOazNDTVo5aFpXeWFOa1k4aFcyLzNVN3g5K0R4dHZYTUszTDYiLCJtYWMiOiJjOGFiNDBkMjg1NzQ4ZGVlMTEyYjBlNmViYTFkYzEwODcxZmEwODhkNWQ4OGU4NzE5ZGRlZmM2OGVhODk3MjlkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNkMmhUcWxJZ21vVzhJeVF1ZHpyR2c9PSIsInZhbHVlIjoiUy9VTTlCT2dCOTdrajdidkRsSmZCWStLTVlqdkt5aHVxTTdhbkpQUFBOVUZEdWZqamNydHVTNEFkUFNqVDdQMyt1ai9xU1RoVTZKSWxObzNQS0NwamtHOXg1b2w4a1QxRW1maW9hS1BvWmQ5Z1NjSDRaVTdBam1kRVBMK2R0SGNFemJORldjRXVyZHZsdXJTTG83ZFMyQWN1bkVnQXdlNStLUXlYNk9xM0hrVm9tNGZGeHVHRFpwS3NmSTlPdVZ3Mk9EZVoyQ2dhNWJjYXVlSWpsbGFhK21iaHlJSFppRE4rNEZxeUtyci9XU01nZlRLSzVic3BRMHR4OWZPaTRKSmYxZUQyZG1qczNIVzY1dGFaYVhFQ2JZUTMwRktpaDNCaDNlMEtzbVV3OWs4RlVsKzlvbDk0OUs1K1lweEgyUEkwYy9NSnFqL0RvZGgwRFhDNmtoZm9sbU00eGVRaG96YUJ4MFBvZTVXY29wWWxMd1lrUDR0OVZSaW9kbHlONEZSbXRXWnNZbkZBY1ZyR2pVMWRLWG8xRVB4YkZCQUhORzQ1ZFFaQXlkUUxLZEM5SDJTVHdYN0puVWo1cjJxVXJSeTdVaHdrVFdJTXl6emVFT2Z1R3h2M2ZkbzFITHdQQTM4bnRrUGtLQjUrYUhVUGFuVktwY3BYVk83VktqVFkzWE4iLCJtYWMiOiI3Yzc0MDc4MjNhMDhmYTA0ZGM0ZWM2MGVlNTdjZjg5NmZkNzkzZjQwMjgxOWQxM2FiODdiZGM2ZjE1MjQ4NTkwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldJbmJoZm5rV1BBMEQ0MmtGY1RjUnc9PSIsInZhbHVlIjoidFYyUnBiZ0JjSHhMNDRWZzFYdWt5MmIvbWhwalhGNDZuaVdmbDlhRktrRm53WkFIMWFVbHNaVmg2QjlvK0V5TGJwbGx4U2VndDNMdE9vaUg1Rm03MTV2VHdtRnVOZHFscTBiRW1DRkJ5Z3JlTmlkZ2FqMkpuemhVMlhwRm9Nc2dlSmRiLy9OdVFEOHJTaFVpazdnUE8rMkFqS0dOUUpoelYxRzhDd2dyZ00vWnpMdU9Hc0hUSm5tUENmMFRoOFZDbkwrNll4cEtmWlFoeUtZZWF1RWx3dnNtYnlSRURqSWdKcXpPOTNoZExKcDlwMXgxWHlZSWFacUxoRXZtbDBFeStNMm85aXZmbWVNckhXZ1hkckhzRjhrSWoxcEp6SU9VV3RyRm1Ra3VGWVlETy9VVmdrZDVLdXNQWkdqWDZ4TGFBMmUwRGFXNnZrd2U0T0EvdG55N0VVcGpSM3l5MnZTQTY1YUZ2cjdiSE45NjBuWSs2Q0h0RkQ4UEpabk8yQVhXaUNpTGVwdEFnb0hYam4reDFFU20yTWE5cWkzQ1FpSUIvZmp1MmozT0JZR2t3OWRVT3pvdHgyNTlIazV3M2hyVW5HK0xXYmhRV1ZvbmhiUHF6amkxK0VIWExjTUdOazNDTVo5aFpXeWFOa1k4aFcyLzNVN3g5K0R4dHZYTUszTDYiLCJtYWMiOiJjOGFiNDBkMjg1NzQ4ZGVlMTEyYjBlNmViYTFkYzEwODcxZmEwODhkNWQ4OGU4NzE5ZGRlZmM2OGVhODk3MjlkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNkMmhUcWxJZ21vVzhJeVF1ZHpyR2c9PSIsInZhbHVlIjoiUy9VTTlCT2dCOTdrajdidkRsSmZCWStLTVlqdkt5aHVxTTdhbkpQUFBOVUZEdWZqamNydHVTNEFkUFNqVDdQMyt1ai9xU1RoVTZKSWxObzNQS0NwamtHOXg1b2w4a1QxRW1maW9hS1BvWmQ5Z1NjSDRaVTdBam1kRVBMK2R0SGNFemJORldjRXVyZHZsdXJTTG83ZFMyQWN1bkVnQXdlNStLUXlYNk9xM0hrVm9tNGZGeHVHRFpwS3NmSTlPdVZ3Mk9EZVoyQ2dhNWJjYXVlSWpsbGFhK21iaHlJSFppRE4rNEZxeUtyci9XU01nZlRLSzVic3BRMHR4OWZPaTRKSmYxZUQyZG1qczNIVzY1dGFaYVhFQ2JZUTMwRktpaDNCaDNlMEtzbVV3OWs4RlVsKzlvbDk0OUs1K1lweEgyUEkwYy9NSnFqL0RvZGgwRFhDNmtoZm9sbU00eGVRaG96YUJ4MFBvZTVXY29wWWxMd1lrUDR0OVZSaW9kbHlONEZSbXRXWnNZbkZBY1ZyR2pVMWRLWG8xRVB4YkZCQUhORzQ1ZFFaQXlkUUxLZEM5SDJTVHdYN0puVWo1cjJxVXJSeTdVaHdrVFdJTXl6emVFT2Z1R3h2M2ZkbzFITHdQQTM4bnRrUGtLQjUrYUhVUGFuVktwY3BYVk83VktqVFkzWE4iLCJtYWMiOiI3Yzc0MDc4MjNhMDhmYTA0ZGM0ZWM2MGVlNTdjZjg5NmZkNzkzZjQwMjgxOWQxM2FiODdiZGM2ZjE1MjQ4NTkwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867900848\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1118124735 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Vendor successfully created.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118124735\", {\"maxDepth\":0})</script>\n"}}