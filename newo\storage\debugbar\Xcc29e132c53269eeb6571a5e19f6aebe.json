{"__meta": {"id": "Xcc29e132c53269eeb6571a5e19f6aebe", "datetime": "2025-06-08 13:27:24", "utime": **********.312683, "method": "GET", "uri": "/pos-financial-record/opening-balance", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[13:27:24] LOG.info: Opening Balance Request Started {\n    \"user_id\": 16,\n    \"warehouse_id\": 8,\n    \"is_sale_session_new\": 1,\n    \"has_manage_pos_permission\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.266639, "xdebug_link": null, "collector": "log"}, {"message": "[13:27:24] LOG.info: Returning opening balance view", "message_html": null, "is_string": false, "label": "info", "time": **********.270347, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749389242.842406, "end": **********.312732, "duration": 1.4703259468078613, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1749389242.842406, "relative_start": 0, "end": **********.027589, "relative_end": **********.027589, "duration": 1.185183048248291, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.02761, "relative_start": 1.185204029083252, "end": **********.312736, "relative_end": 4.0531158447265625e-06, "duration": 0.2851259708404541, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52201968, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.287969, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record/opening-balance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@opinningBalace", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=262\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:262-323</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00842, "accumulated_duration_str": "8.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.135138, "duration": 0.00545, "duration_str": "5.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.727}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.170935, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.727, "width_percent": 12.945}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2453258, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 77.672, "width_percent": 11.283}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.252769, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.955, "width_percent": 11.045}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1738618320 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738618320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.264678, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-489883414 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-489883414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.269897, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos-financial-record/opening-balance", "status_code": "<pre class=sf-dump id=sf-dump-42110788 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-42110788\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1685876710 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1685876710\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389240484%7C22%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhjV2dKVytsb2tMa0R6MHRTdmUzQ3c9PSIsInZhbHVlIjoiQmlndGhsMWFqOGpNb1ZTSGRGMXRUb1AreGh2WFMvQ1FIUDRjSkdqQUVpOUtCSm1vVkUxUlhXQWdtM2x2djNlaTdWVmcyZmliMGl4Z1FEcDdHVUtIRURGa1NEd0IzRUQzWGc1MC9rNDVRR01JckVOZFVFcGZqNXVJUUYrTDZoa0FFTDk5Qi8rbzllckg4bDl6eXdqUWFMcTlRczdCKy8yTGpzc2JIZHNiVWxEOUFJcTd4MVlWc1dKbFJTMHNEZnVvNEwrMVY1MjBFQXFwdnZMT2k3L2Urd3U4WkxRRzRwc0I4eFNnakVyU0ViQTVYNjE4OTIrTno0OXI5SVdKbWt5bnlPdmYwcVgrZUpiaDBMWUNDTEZWSzhkMHdYRGtCU3B5Z3VqbUhjNnR4YTZSVWdxWDcrU2pGZVd4YUVJcUc5Q29SeUlyS1FWV0ozK25Lbm9lb05YOEJRdFlvb2Z2RHZhaW8ybGp5STVQcTgzSDVYa1lYYi91c1A2S0dFWWpHWGdxWVhvRW81SXp4QU16eFRXWDlKUUE3UUwxZDQxb0JPSlVoaTBockE4ZE44cGhmSVFsZXVPbkZLYU9BS3hXUWtLVEJYcDM5ZzJkSFhUQ05saUNCaGZkMStSdWZpcXg4V21rdFlIUjY1WjV3MmZ5VFM1eGMrdzB1cEp3TGVoVHRVQXQiLCJtYWMiOiI5NTkwMzg1NDQ3NTJjYzY5NDk3MjE0ZjY4NzZjMTdhMzFmOTEwMGE5NjU5M2I5NTRlY2VkODAzM2Q3Zjg2NzZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJNNFpzank5Q3ZzaktTRWpFTzhwblE9PSIsInZhbHVlIjoiSGZNb3Jic1F4R0NQUTJ2c3hUMVlubkF4UmFVOEZhNWlMNFR6WmhMcmszakZ6V3NyTVVYcmxRRzBrMGQvUitMOVo0L2Q0aE1ydXpiSHJtSEVpSzdDZlF3d1BmQW5rT3FFWTRIdTRqeHJGQ2NWOUE2KzhUcURGVTh5dWlteVVBWlZ1ZCs4bmlQY3BRWTQrNGIvWHdBQ1BMVGZKTStvZEJWejVaS2ExS0l4S3RNTGtBaEEvalZCR3VHZk8xY25tTFd4djhLcWpNWEVKY0ZMQ3lScmJnN1dGQngyRVM2K2M1Q0k3L3F3Vy9UOEgzaUJ1dzQ2K2N5WUJQM3NManJ4WEIzWXFLZEFxTElSTVJOb1lRLy9rRXdBMUJGdXl4bFB2RVJqUVRjOHk3aVpDZGJnSzFGNSsxbWx1MEpmSkdaa2Rvc1A5TUhpYjRoNWt4dGtrZjBUVmNWVENDQjczSWFuVW9tMWhaTWRKdWRMd3lqb3pMbDkvNHEvSDJTaFJIMUh4eGZHS2hJWmc1bG1uSVpJZDY2U25mTDZkQ0k3ejRDT2ZjS09RSFZIMW5mblIrZTB5K3prYnppMytJbUs2WGRNSGZ5K25NWGtFbHErQ2lZR2FTcityeS92Ykl6aWtWeFhkVlhad0ovcG55a1grcUN6alRyOHhHaHN1bWduV2dZSXJKYlkiLCJtYWMiOiI0NGMzZGMwZmExY2RmNDdmNGY1NDE0NDBhMTc5MTMxZWRmYjM1MzE2MDU5ODVlMmVjZjQzYmQ4OTFiMWNmZWFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1530901 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-17894275 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjljTzZINGlyNklGQTg1SXQrQURUMGc9PSIsInZhbHVlIjoiNklYM1ZoU0JFOW5WT1pGRjA2T29WclpmQ1I5SXhBY2hOQnpkc0Rtcng1TmNWR0M5UkxEaWI2RVFGeFlwcmM0NWtvZE5WWnNzWDdTYldSZkdqeW93TkNpUkxHZnI5QjUvZS9iWXdsaFQweTVFWUVYYmlqeUxIRTN4RjVKazA0dHZ4S21DVmprVEE3L2JnSHN1YVN2Qkt6cGpYbzFremxadjZseWw5cVg3YVBQRnBCYWJzUVNDNUVNQ1NRL0cyemZOTEFaTmFzY1Q1eGJaK1owWC90ZENuR3ppQVNTd1VSRVYrYjhuVVpQb2ViUTlKT2svMmphUEZubGkralVHT3NyVktCcWxMN25PRXh4cjliYnZxb3ZnRlIzNndKekV2YnpPWmorSkNHWHFPejg2TW5VUnR1ak5XRHV5QnNTWTlwTzBqSnd6cFQzVDFkUGI0dlBnb3NWMStXbWRNdGQwWHBLR1RIOWFYenZZVXgrYlhYY1k0a2s3T0NpVTZGU0JsdGoyQ3g1NjZkckJ1T1ZMYko1Mjhta3VoSmtrL09SdVdJeFRnSnozK3JDTm9EcWZhODhJSE8vRk83QzI5MDI1RHJ4L1paS3hIVFJuN2RSN0U5NUNlU0R1UFY3TklucC84RExONEhxcXRGbXdLcWpJTWttd1B0Qyt0M3poSGxJWHM0ZnEiLCJtYWMiOiI5ZjQ5ZGQzMTcwZTAwM2IxYTcxNDczMGFkMDUxY2QwNjIxODU5ZTAwZmRhZTUzYWZhZTg2MTBkZmJlZDAzZDRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjM4Zm9HNE9RZjg5Sjh0WFBwSEpROFE9PSIsInZhbHVlIjoiSlRhWU1qeGRTMTIrb29vbzRiMkFvYnFMcXlXdGFpRFg4OHRxTFllNjlMKzUrVVl6dWN3djJMY3lQcWVmZ01manVDSmxwdUNUalRSS1BFWFBha01BbjlRRExkTWxLaDJidmF2WHBNTnI3WlB1Q0xBalZvL3FKTVpCU3lUWlNEdC92RWtHN2FzY0xpRURHTisyUEVlN2tJekpvc2Rkci91amhaM1RaTDl3NEJDRWk1TDZjOU1NM0VmSUY5VVFWenNSYzNNRVExK21TY3dvclkzTUNvUTlEWWlRd29lem41OVFPSWJObDlzQVJvOEVMRVRURGpUSzFGWFpOZzRPcTlPNXlYQjVwTWxJdUZHUXY2UGY1d1kwWE5JUUg1b2poZnlGZkpMMmxGTTV0a2dFdzVzVktIVjRYangxdW52WC9pcFRTQ1hwQXdCa29ZN1RCakQ4UEVuYkpWQzl0VW1pZVJBVVNoSkdFdEJXK3pkb1RtS1FqZUhvUXJNT0s2Nzl4UEt6bllsVjV3dllEU2dtOENFZ1JmYjlBVzhwdjBvcEVSYjR2ZHFoUFpreVdCcTM1VDh1d0xyTmI5N3NXM0YrTXM4K0pJdm5sV1RvRkRUc2x0bWRZQUFKamlXek1xcFRNN0dHUnFnVUhBN0ZlU2NrTTVsaHlTMG9wK295RzBqMWJGM3oiLCJtYWMiOiJlOGQyN2U3YTY4MWUyMzZkY2ZkOTMyNzgwOGE2Nzk0N2MyNTBlMWJhZmIyMTlkMDI3NzUyZjQ5MjExZTg2ZDgyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjljTzZINGlyNklGQTg1SXQrQURUMGc9PSIsInZhbHVlIjoiNklYM1ZoU0JFOW5WT1pGRjA2T29WclpmQ1I5SXhBY2hOQnpkc0Rtcng1TmNWR0M5UkxEaWI2RVFGeFlwcmM0NWtvZE5WWnNzWDdTYldSZkdqeW93TkNpUkxHZnI5QjUvZS9iWXdsaFQweTVFWUVYYmlqeUxIRTN4RjVKazA0dHZ4S21DVmprVEE3L2JnSHN1YVN2Qkt6cGpYbzFremxadjZseWw5cVg3YVBQRnBCYWJzUVNDNUVNQ1NRL0cyemZOTEFaTmFzY1Q1eGJaK1owWC90ZENuR3ppQVNTd1VSRVYrYjhuVVpQb2ViUTlKT2svMmphUEZubGkralVHT3NyVktCcWxMN25PRXh4cjliYnZxb3ZnRlIzNndKekV2YnpPWmorSkNHWHFPejg2TW5VUnR1ak5XRHV5QnNTWTlwTzBqSnd6cFQzVDFkUGI0dlBnb3NWMStXbWRNdGQwWHBLR1RIOWFYenZZVXgrYlhYY1k0a2s3T0NpVTZGU0JsdGoyQ3g1NjZkckJ1T1ZMYko1Mjhta3VoSmtrL09SdVdJeFRnSnozK3JDTm9EcWZhODhJSE8vRk83QzI5MDI1RHJ4L1paS3hIVFJuN2RSN0U5NUNlU0R1UFY3TklucC84RExONEhxcXRGbXdLcWpJTWttd1B0Qyt0M3poSGxJWHM0ZnEiLCJtYWMiOiI5ZjQ5ZGQzMTcwZTAwM2IxYTcxNDczMGFkMDUxY2QwNjIxODU5ZTAwZmRhZTUzYWZhZTg2MTBkZmJlZDAzZDRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjM4Zm9HNE9RZjg5Sjh0WFBwSEpROFE9PSIsInZhbHVlIjoiSlRhWU1qeGRTMTIrb29vbzRiMkFvYnFMcXlXdGFpRFg4OHRxTFllNjlMKzUrVVl6dWN3djJMY3lQcWVmZ01manVDSmxwdUNUalRSS1BFWFBha01BbjlRRExkTWxLaDJidmF2WHBNTnI3WlB1Q0xBalZvL3FKTVpCU3lUWlNEdC92RWtHN2FzY0xpRURHTisyUEVlN2tJekpvc2Rkci91amhaM1RaTDl3NEJDRWk1TDZjOU1NM0VmSUY5VVFWenNSYzNNRVExK21TY3dvclkzTUNvUTlEWWlRd29lem41OVFPSWJObDlzQVJvOEVMRVRURGpUSzFGWFpOZzRPcTlPNXlYQjVwTWxJdUZHUXY2UGY1d1kwWE5JUUg1b2poZnlGZkpMMmxGTTV0a2dFdzVzVktIVjRYangxdW52WC9pcFRTQ1hwQXdCa29ZN1RCakQ4UEVuYkpWQzl0VW1pZVJBVVNoSkdFdEJXK3pkb1RtS1FqZUhvUXJNT0s2Nzl4UEt6bllsVjV3dllEU2dtOENFZ1JmYjlBVzhwdjBvcEVSYjR2ZHFoUFpreVdCcTM1VDh1d0xyTmI5N3NXM0YrTXM4K0pJdm5sV1RvRkRUc2x0bWRZQUFKamlXek1xcFRNN0dHUnFnVUhBN0ZlU2NrTTVsaHlTMG9wK295RzBqMWJGM3oiLCJtYWMiOiJlOGQyN2U3YTY4MWUyMzZkY2ZkOTMyNzgwOGE2Nzk0N2MyNTBlMWJhZmIyMTlkMDI3NzUyZjQ5MjExZTg2ZDgyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17894275\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1835081055 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835081055\", {\"maxDepth\":0})</script>\n"}}