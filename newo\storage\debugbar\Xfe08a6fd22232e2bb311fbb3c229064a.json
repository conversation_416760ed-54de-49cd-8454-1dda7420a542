{"__meta": {"id": "Xfe08a6fd22232e2bb311fbb3c229064a", "datetime": "2025-06-08 13:00:45", "utime": 1749387645.05103, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387643.729836, "end": 1749387645.05107, "duration": 1.3212339878082275, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749387643.729836, "relative_start": 0, "end": **********.896965, "relative_end": **********.896965, "duration": 1.1671290397644043, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.896987, "relative_start": 1.1671509742736816, "end": 1749387645.051075, "relative_end": 5.0067901611328125e-06, "duration": 0.15408802032470703, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45038520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00769, "accumulated_duration_str": "7.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9763749, "duration": 0.0053, "duration_str": "5.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.921}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749387645.007644, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.921, "width_percent": 11.704}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749387645.027149, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.624, "width_percent": 19.376}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1447205560 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1447205560\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-16120587 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-16120587\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-75371334 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75371334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1748321901 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387628836%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRVZVhBR2xjUDUwQ2tyU0VaU3BkS3c9PSIsInZhbHVlIjoiSTdiaVZMOUJ4VEFVcVA0TXozR2ZMck5vMWRSZFA3VmkwOElscTRTRU45TysrVGZLQmd5NXZselVBdU8zUEZMODZVV2ozaG93ZjdwQ2x0OUZtQnhWSGdQNi9NSGo2RWNLdFVhYmw3VlBMVE56MXlmQWRuc0JBcU5RT2wyL3E2ZldhTVlTOG8zNUJudHdZMVZVSkk2bloyZnhtUzhteVZEM3daKzNMRkVSNmZGVlpBUkpsVzIweXFwSXhkT0F4aEorQzY0blRtZUtpVTFxcFJJRTlkcnBBcFhaa0xlVTdlYlprNFZWU2FRT0VjWHNGSTJVV2hoV3V6cnJYSFNyQko4K2N2QmZqUzNGeGMwSkZnK1ErMndsYXg0QWdkME5Ud24ram1wT2VmWmRxMDVmVDJnaWx3NDYwN0pyU3JqOHdXVklCUmFLdmxyU1dSakNLd2djWWVFQzluVURGR25BRWJ5N2JKKzVKRTVPRW1RRnc1Y3FyWG9tWTc5bk5GNWhqRHV3SlFzN1Z3RVpZbm15TU5ZQUtPRFRSRzVKNzZGYTRlMDJSaFV5OHIxU3JKeng3V0tMT01LSDFFWXd4S2ttOW43a1drekZwWksrMHlOaHJWcVZqUWpJVVc1UFBlQUtiN2hxWjRrczJpWTg0ZWJLb0pQa2VxdmNOV3pMYnFWTm95NzEiLCJtYWMiOiI1MzA2MjkwMjQ4ZmRjNTEyN2MzNGU0MjMwMTg5YmU0ODBhMTQ0NjhkN2ViNjlmMjM5MzA1MmVkNjY0ZDA3ZmUzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdkWFhoTjQ3VE5JZ3V4cUtVcXQzTnc9PSIsInZhbHVlIjoieXJabnRwL3VyNTcxMkc4NG51eDBObGk0elYzTUY3d3RGOGwzQnZGUEw4UC9IQVBHNjhsdGYrejRYTFJYYlFHRXY1OGhFV2ZXcmgrSmxTYzFIbGczWnA3RTRxSC9ZQVk4TTFUV0pzbXdCVFlYL29EQlpFc0JlMi9Lc054U1RKaWNWMDYyNU9lb3JsQWRhNm5GOUl4bXlJVWExWjArRXJVNkJjdDhzYmZrbHhyblNCNXArZkRZbldXL21vWEFtZ20yaVkvaUU2OFdTK1hWekhyVVFXQTVCTVYybTdibmdVRkNDcUJIWEN1encxQjdyYWlScHFxc2ZjVnFvSmVET1gyMThtRW9GL2NBV2RLNTJkdmpBQ20zM2JkM0xYZXlGdUNoa2V0T0d3VHF3VFBDMlZ5M3BjYStmdEhneHRyK1lHdXhDZ1FZNUdFOFJIYVBIb1lIWFZ2RldaOGEzS2xqNVM5RGpQcUgydTIyelp2ODRGWjkzYVIrQ0FyWWZZNkpOdjBqZElxNDBDa083dzdtSVljbkdmcU5UVTh0Q1JhdjhRaFhJWDQ2YnI5WVlHZXBWSjliejE2Y0VXWXljNG9HMnMySHBYZTc1OFo2dWpGMUJRZ29xeVFZblBxWVN3Vnl5QXppSllqWTZLY01haVVZSmZlTFJodlkzdERmaDlPNnpSUlIiLCJtYWMiOiJmOWEwYmNlZTY1MGQ4NWY1MTMwOTQ1MWIzODQzOTVlMTIwZGYyMTdhZDFiOWQ3YWJjMTkyOTFjOTdjNWYwYTRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748321901\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2002200413 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002200413\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-653382947 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkthdTl4d3hmTE5LRmkvbHpoUWw2Vmc9PSIsInZhbHVlIjoidU5vZUFVZld1Qkt0dVErazBWd2lpVWZhWmFod0dPQncyMk8xZXozU2xVSUxZZEMzaGNVVGI5czJRQXZBV09BZ0FWUmFyd1JxazhacXdKRzBFYW96Z2xFRXBuS0lNdmtjUmhhV0YzU1UvdlFiL0ZUanZDZ1c0cE5qVE1abmx3L1V5SHFBRmg2Z016UVQ5TzFKb0g2WFB3VTRyS3VvcllObnUxRlNmMTUyQVlBMVh3TGljQWYxUWhnaktaSWgwRVpDYUN6V3Z1MWZ3VXNDNlRxaCtFV2pzVldpczZHbm15QTV0cUwrTnIzRUh3M003ajBQWUpLVnNTUGF4Mmo3a3FvblZBR3p2K0pmbUJaUGdwV0I0bFNOS2dSczg0cGJJNWxlcEovSVNqSG94SmRab0duVUVHNEx2RHB6cTlEU1UzSzkxVUF4a0o1dXpIZ2pVdVN2dGxMVkx4QXdJbnhzRlorTWpqNTcrNzVoOEw1Ky84c2tkM0lldXRpT3JOUVBGelRDUDBjM28yVjlSeWZYLy9TaGd3cTdzTHkrSkZGNXV5b3pUK3JJajJyQjlLY0xZb3FPK0xyYjdyUWdQeTJjbUYxcHNpeEV2bGU4NVJuUmdhcVY2RXk0bkkrQmRGVzFveDFXNWQ2TlVDUGlEYkVtRlJLMzVqcUVneXJyYXJvb0hmWTAiLCJtYWMiOiJmNWZhYmE5YjNhOGNjOTQ3YjlmN2I1ZjgxNjUwNTg5ZmZiMWE4NDliYmQ5NTBkN2NmNGNmOGQ0NzcyMGIxOTM4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik8xYnZVRnNKQUdleFNDNEFVNStrS0E9PSIsInZhbHVlIjoiSWs0dlpkVEpLUjJCZ1V4d2Ixd1FVOW5ZL29Kd1NFbWNHQTVMUytGcC84UmFZa29kQStwdW9vY0I1L3o3alhpaEpGWmluREY3UFRyVHRNVHRzSXh6RFRpeVQ4amo5dFllVnd0ZVo0ZFRmWjhGWHZlaWpsdXQwZi9xTVcwWHJrOXNsYWhtaUVxajFiUmtUbzR3T3RJWHBrYktvbDYzVWJBcVRHRVFPYkh5N2MzQWdmektyblVWVTkvbThaSGd2dWlYdm9aaWhyVWY4NExOM0dXNjUrU0hZdXRTQXVCRkovOVYrU3IyWitta3NpNTI3MlRnbXRTSW1Ga1ptaWxENlI1YUoyc2F2SnkyaUljOW9YLzFvckxucjFZNThLSVBENXVDd1ptdGFEUU5OeTdpRlF4RUhON0lIWXg5amVyZUdtVHp2S3Jwd0t3cW1Ea3c2VXlPdzV6YXdqaEt5dlhUdGt2TFQ4MU9xTmtWNWQwSVIydGZjNGZ3NzJSa28xNmU4elgvSGtqbkxmamVQMWhZUm1ndGhVQUtKUVN6OExCQXZrYU1XZE5INWRlN2NuVUlPUjhxR3FUay9UWGFhbHYyMk41S0FQUFVoZEZtZXBxOHVaZDllK1dxcDY3RjZlWHpKT0JzWWRQb0NMak5UWmN5dWZzeWRzZ0p2TGxPUG1BOCtPYUkiLCJtYWMiOiI1MDNiODU0NGNlNmI1NDg2NDE0ZDhkMmM3YzA2MTY2MjY3OTQ1MmRlZmU5NGEyYmNlZjc1YTM2MzllM2Y0NDJmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkthdTl4d3hmTE5LRmkvbHpoUWw2Vmc9PSIsInZhbHVlIjoidU5vZUFVZld1Qkt0dVErazBWd2lpVWZhWmFod0dPQncyMk8xZXozU2xVSUxZZEMzaGNVVGI5czJRQXZBV09BZ0FWUmFyd1JxazhacXdKRzBFYW96Z2xFRXBuS0lNdmtjUmhhV0YzU1UvdlFiL0ZUanZDZ1c0cE5qVE1abmx3L1V5SHFBRmg2Z016UVQ5TzFKb0g2WFB3VTRyS3VvcllObnUxRlNmMTUyQVlBMVh3TGljQWYxUWhnaktaSWgwRVpDYUN6V3Z1MWZ3VXNDNlRxaCtFV2pzVldpczZHbm15QTV0cUwrTnIzRUh3M003ajBQWUpLVnNTUGF4Mmo3a3FvblZBR3p2K0pmbUJaUGdwV0I0bFNOS2dSczg0cGJJNWxlcEovSVNqSG94SmRab0duVUVHNEx2RHB6cTlEU1UzSzkxVUF4a0o1dXpIZ2pVdVN2dGxMVkx4QXdJbnhzRlorTWpqNTcrNzVoOEw1Ky84c2tkM0lldXRpT3JOUVBGelRDUDBjM28yVjlSeWZYLy9TaGd3cTdzTHkrSkZGNXV5b3pUK3JJajJyQjlLY0xZb3FPK0xyYjdyUWdQeTJjbUYxcHNpeEV2bGU4NVJuUmdhcVY2RXk0bkkrQmRGVzFveDFXNWQ2TlVDUGlEYkVtRlJLMzVqcUVneXJyYXJvb0hmWTAiLCJtYWMiOiJmNWZhYmE5YjNhOGNjOTQ3YjlmN2I1ZjgxNjUwNTg5ZmZiMWE4NDliYmQ5NTBkN2NmNGNmOGQ0NzcyMGIxOTM4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik8xYnZVRnNKQUdleFNDNEFVNStrS0E9PSIsInZhbHVlIjoiSWs0dlpkVEpLUjJCZ1V4d2Ixd1FVOW5ZL29Kd1NFbWNHQTVMUytGcC84UmFZa29kQStwdW9vY0I1L3o3alhpaEpGWmluREY3UFRyVHRNVHRzSXh6RFRpeVQ4amo5dFllVnd0ZVo0ZFRmWjhGWHZlaWpsdXQwZi9xTVcwWHJrOXNsYWhtaUVxajFiUmtUbzR3T3RJWHBrYktvbDYzVWJBcVRHRVFPYkh5N2MzQWdmektyblVWVTkvbThaSGd2dWlYdm9aaWhyVWY4NExOM0dXNjUrU0hZdXRTQXVCRkovOVYrU3IyWitta3NpNTI3MlRnbXRTSW1Ga1ptaWxENlI1YUoyc2F2SnkyaUljOW9YLzFvckxucjFZNThLSVBENXVDd1ptdGFEUU5OeTdpRlF4RUhON0lIWXg5amVyZUdtVHp2S3Jwd0t3cW1Ea3c2VXlPdzV6YXdqaEt5dlhUdGt2TFQ4MU9xTmtWNWQwSVIydGZjNGZ3NzJSa28xNmU4elgvSGtqbkxmamVQMWhZUm1ndGhVQUtKUVN6OExCQXZrYU1XZE5INWRlN2NuVUlPUjhxR3FUay9UWGFhbHYyMk41S0FQUFVoZEZtZXBxOHVaZDllK1dxcDY3RjZlWHpKT0JzWWRQb0NMak5UWmN5dWZzeWRzZ0p2TGxPUG1BOCtPYUkiLCJtYWMiOiI1MDNiODU0NGNlNmI1NDg2NDE0ZDhkMmM3YzA2MTY2MjY3OTQ1MmRlZmU5NGEyYmNlZjc1YTM2MzllM2Y0NDJmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653382947\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-445960358 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445960358\", {\"maxDepth\":0})</script>\n"}}