{"__meta": {"id": "X0ba776966810d94ff2b7df7ec91bcc64", "datetime": "2025-06-08 13:26:46", "utime": **********.24622, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389204.950824, "end": **********.246251, "duration": 1.2954270839691162, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1749389204.950824, "relative_start": 0, "end": **********.106166, "relative_end": **********.106166, "duration": 1.1553418636322021, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.106187, "relative_start": 1.1553630828857422, "end": **********.246254, "relative_end": 2.86102294921875e-06, "duration": 0.14006686210632324, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43933360, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01218, "accumulated_duration_str": "12.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.205928, "duration": 0.01102, "duration_str": "11.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.476}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.226825, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 90.476, "width_percent": 9.524}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-491868322 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-491868322\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1405083010 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405083010\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-437099707 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-437099707\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndKbGJpQ2lRWEpaRFVzYy9HdXRJdlE9PSIsInZhbHVlIjoibTZVbEpTSVRwRCs3SGtFalFrZlNMOGlmNkZCWGVRMFBKSDFxTkhzZ213WVFoK2x2RmJKODRuZ2NOSTljZTZoTHhHdzdXbHRZT0lNeW9jQkdSa3k0UDVWanRDYWtsWVdEUXVGUlJ0U3cxb1VzanQxRXphaGd0dnB4U0VPZnpYY2Foc2ZWNSsxQkRiUEh6d0RuZUppVHVDV0lLZ0JLRFpjOFZDU1JOQytPZXB6SmxrR0dhQ2Fva0NXTDdxdWZ3N1FNNjZlcGs2Wi9hMWJDNmhWdVdKNzB1bTI2QmVsQStYalluc2JYeXBEMndPQjVyQXg1UFlENDFITjZ6WUZrNERtanpDeTdDN2FlWW0rdDY5b3dsL3hUcWZhSnhWRGc0T2hVR0RxMk5BZjB0eEdOeEJDRDl0MWNEVGk5YUxXcFpnUXJtazZKaFBnaWNpTG5SSDJrQkZCTERZcUlSazc3TCt5S0xFcTZGRnB4U2FPVm5McjVXZE80YjNWbmdpMDRuVU1sWVZsdlpVeVEybnhLOG5kTThhQXU4QVMra2R4ZVJCeDVWd3pSQzRpd0IwQ01UKzdIbzNuZzJsZFpTNmJobGVoNEZnekVZWjhNU21HUG1nQmc4ZGpBbzloWnVHRk1EM05QN1JITFIyRFVTQmxNRmJRekpnV05JQXZIdk1tdWJNSEEiLCJtYWMiOiJmYjhkY2FhMDc5MzYyMzAwNDVkZTE2MjUxNDMyNTA1MzlkYzE0YjEwMDNiMWQxMmM2MTgwMjAzY2E1NTg1NzI5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkcvbjBYUjN4Ym5BVE9rZ1I0RVA1MHc9PSIsInZhbHVlIjoiMjBxY0d3bmJ2SGZFYzY5OUllb2o3SldZOUFIaDJaWmJlZVdrVnVjTjNoR1hyMzhVaEtKVFpkc2J5K3F2S09MUzRzc2l2VmtNSmRxNGcwdmEwWkV2d2lNYWpFUWt4SWhWVExwcmNtVkFwL3JvTjJVdHJrb1NwY1BRb2llQlhqTDBiUm1KWXUrWUtXYTlETWVrV3JKdllDWVcyMnpiRXVST2Z6NGNCRTRYVDJ3c1JtOXpSbWtTSzNiNFRlUmE0VDRsUlVhd0ZvMFVwUE1PMW1pZXI3OUZGWUZKbW9kWUFqR1U3ZkdNY0o1bmVURm5DdGNMQ0JQNzF1L2Q4elBidTBuVGg5Y2tUdUNjVklMMTM3NFJzSWh1ZUNFaFdQRDExWHhOcFJ3ZDB3SVlwdS9DbnpCeC9OQnhicjA2ZjNSY24rQTM0bDBSTVFyV1dzWm5pVS9xNTJjcGRLS1U4c1VldklzcS9TTWNKZGF1a0YrSlJnVHJKWnU4QldPalU2eDhkc29EOXZOZkY5UWxXb1VWYWM2VEZEdDdycnlzNkFwMWdYVVR6aGlrTGZZeDZMcWp2TlROVVVTNEpaNDEvVWx0eE9zMHJiZ2VzNUhyNWgwKzJoMDdGZXQ3UFdGNmNUdUpVMGtYSDV5S2E5dEFzWDNWSlQxd1d6ZEl1d01NV3lkSkxubm4iLCJtYWMiOiJjODViODczNzMwZWY5MGM3NTMyOGQ0MmMxNDY0ODQxNDJkZjhlMDBkMjY3ZjljNjRiOTgxN2E0ODg3ZTQxYzM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1782603513 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:26:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlZbXVNL2U5UkQxeENLNUdGek83MUE9PSIsInZhbHVlIjoiWk9HcGEyd1JvcFlKTW52NlVScHBlUEYxUVpPZ0kreDNZWFNYaDQzNENTZ3Qxd3ZQWWhiQW1tMmpIbTNHNFJ1VEkyUitSWmpMWGprbmpob0FaazlhVElMb0NOTVB4RzlHUjNJTkhCWmpIektENVllRnhMdnhSVmFMSis3aThLYjdiN1greEp2c2VHbkd5MndjNUE1M0ZjbnhsNVpJZjU4em5xbUQxSHFpcHVIa3pKU0ZrSisrcVExS004Rms2Mk5ZSjlvZEpHZVlTUUFhalpaUlQvSDJZbDVIK29GNHpHWXJLWjVpMUNsQ3pPcHp6U3VCSFErN3lrVzZWdTVZTXRWb204TUxoOXVaSGIwZ3JvdW52djhIRnp1a3hmRFl3clltOGg5NGZoY3FKcGtxVHkzZDJtQUwyakpmbVpRODQ4U25lWENTNm9CUm5RamdOQkNQNEJQZGN6bzNTaTNscWlXbTNONU9GUUg4Z3JDc01velV1ZkFaN1l5SDZzTU42TnUzNVYzYjZVWDVpT1l4V1YwZEx1YVkyY1dIRkhFb0hLMGY4TzAyc0NYRi8rSU8rVHFjYWFUMUZuWTFTKzBBV3hRWWI5TUt1UXczUi9XSm1KaTVOdWx6V0NxUUZMN1dSOUU0Qk1NWjlQTkx3eWxyU284cERRTXNJcEZHQnlvS1ZRZlgiLCJtYWMiOiI1YjliYzRmMDMyOWU5Mjc2MTM1NjNmOWQ0M2JjZDBjZTBhZjY1YzQ3ZTIzY2VlM2JhNzdlZWVlN2E1MTU1NDVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijhkdk1saU82bDBVZmVoRnY1dXBtOUE9PSIsInZhbHVlIjoiemE1eHY4b09RdlJkb0l1VFJIdDJNZ21iL05wOUZHRTRhUFR0Z1U4ejdXelA2Zmx6UUx6N3lMZ1FzcldMYmVxVzBtczB3YXBCUUVHekdvem5DWTJVbkZNcy9vSmRLTEN4Q0hnck9zQ2NGK2c5VmdOVmV0czA4NEVaUlQrZklEV3pIUG16aDdYN3I3NWxVQTBzblNDclYxU3BvdCtGWU9CTTd6elFDdU8wK3Y4TjF2YlZaaTJzY1ZkUVd5d3h1Zjh2eUJsVlkvNFdOcm1MamxjQkdtS2k5RVgzbHZpL0Y0eDJQRzlENmFMQUFNZlFlODVObEwwOGo4UTVOalZtN0djdjJ6ckcrdHRxSlZjK01Ha1NWUVhGb2tsV2pWNDFtTG00VjBCclNWYlR0ZmF2cVNCSUpCc3JsckplZDVIMjJlT1VHclVUZFl1bGp6WURYWTcyM21aam93TmpXcHEyMlZtSlJMbG0yWUJIOWp1eEtUWFNRVFFBeFJ3OFBiWTYvV2hheDU1Uy9QWm5vbDUxN1JhbTJVbGlweWEraExoOW1CTW1kN09RSUZXSmNjbmo0QWl5VlZEU3lUYWhucHVnLys4aUkrN1piTXNHWVl0d0ttWWdKYlJXRDJSU3l3UDIxd0tLZDBmNUFwZnVDd1I3R2pFbDVWUmozdVkxdVhNSUxiNVEiLCJtYWMiOiI4NzhmNGY2MjJhOWZmNGU4NDhlYzUwZDEyOGE1MmQ4N2MyYWQ5N2FhODllOGFiYWM0OWEzYjM3MDdlZTVmNmNjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlZbXVNL2U5UkQxeENLNUdGek83MUE9PSIsInZhbHVlIjoiWk9HcGEyd1JvcFlKTW52NlVScHBlUEYxUVpPZ0kreDNZWFNYaDQzNENTZ3Qxd3ZQWWhiQW1tMmpIbTNHNFJ1VEkyUitSWmpMWGprbmpob0FaazlhVElMb0NOTVB4RzlHUjNJTkhCWmpIektENVllRnhMdnhSVmFMSis3aThLYjdiN1greEp2c2VHbkd5MndjNUE1M0ZjbnhsNVpJZjU4em5xbUQxSHFpcHVIa3pKU0ZrSisrcVExS004Rms2Mk5ZSjlvZEpHZVlTUUFhalpaUlQvSDJZbDVIK29GNHpHWXJLWjVpMUNsQ3pPcHp6U3VCSFErN3lrVzZWdTVZTXRWb204TUxoOXVaSGIwZ3JvdW52djhIRnp1a3hmRFl3clltOGg5NGZoY3FKcGtxVHkzZDJtQUwyakpmbVpRODQ4U25lWENTNm9CUm5RamdOQkNQNEJQZGN6bzNTaTNscWlXbTNONU9GUUg4Z3JDc01velV1ZkFaN1l5SDZzTU42TnUzNVYzYjZVWDVpT1l4V1YwZEx1YVkyY1dIRkhFb0hLMGY4TzAyc0NYRi8rSU8rVHFjYWFUMUZuWTFTKzBBV3hRWWI5TUt1UXczUi9XSm1KaTVOdWx6V0NxUUZMN1dSOUU0Qk1NWjlQTkx3eWxyU284cERRTXNJcEZHQnlvS1ZRZlgiLCJtYWMiOiI1YjliYzRmMDMyOWU5Mjc2MTM1NjNmOWQ0M2JjZDBjZTBhZjY1YzQ3ZTIzY2VlM2JhNzdlZWVlN2E1MTU1NDVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijhkdk1saU82bDBVZmVoRnY1dXBtOUE9PSIsInZhbHVlIjoiemE1eHY4b09RdlJkb0l1VFJIdDJNZ21iL05wOUZHRTRhUFR0Z1U4ejdXelA2Zmx6UUx6N3lMZ1FzcldMYmVxVzBtczB3YXBCUUVHekdvem5DWTJVbkZNcy9vSmRLTEN4Q0hnck9zQ2NGK2c5VmdOVmV0czA4NEVaUlQrZklEV3pIUG16aDdYN3I3NWxVQTBzblNDclYxU3BvdCtGWU9CTTd6elFDdU8wK3Y4TjF2YlZaaTJzY1ZkUVd5d3h1Zjh2eUJsVlkvNFdOcm1MamxjQkdtS2k5RVgzbHZpL0Y0eDJQRzlENmFMQUFNZlFlODVObEwwOGo4UTVOalZtN0djdjJ6ckcrdHRxSlZjK01Ha1NWUVhGb2tsV2pWNDFtTG00VjBCclNWYlR0ZmF2cVNCSUpCc3JsckplZDVIMjJlT1VHclVUZFl1bGp6WURYWTcyM21aam93TmpXcHEyMlZtSlJMbG0yWUJIOWp1eEtUWFNRVFFBeFJ3OFBiWTYvV2hheDU1Uy9QWm5vbDUxN1JhbTJVbGlweWEraExoOW1CTW1kN09RSUZXSmNjbmo0QWl5VlZEU3lUYWhucHVnLys4aUkrN1piTXNHWVl0d0ttWWdKYlJXRDJSU3l3UDIxd0tLZDBmNUFwZnVDd1I3R2pFbDVWUmozdVkxdVhNSUxiNVEiLCJtYWMiOiI4NzhmNGY2MjJhOWZmNGU4NDhlYzUwZDEyOGE1MmQ4N2MyYWQ5N2FhODllOGFiYWM0OWEzYjM3MDdlZTVmNmNjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782603513\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-762243353 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762243353\", {\"maxDepth\":0})</script>\n"}}