{"__meta": {"id": "Xe94990d600538d37392af2f4c7cf8acc", "datetime": "2025-06-08 12:54:04", "utime": **********.763415, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387243.260445, "end": **********.763452, "duration": 1.503006935119629, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1749387243.260445, "relative_start": 0, "end": **********.583362, "relative_end": **********.583362, "duration": 1.3229169845581055, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.583387, "relative_start": 1.322941780090332, "end": **********.763456, "relative_end": 4.0531158447265625e-06, "duration": 0.1800692081451416, "duration_str": "180ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43389088, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00882, "accumulated_duration_str": "8.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.722367, "duration": 0.00882, "duration_str": "8.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ppHWJDQH68Bkr0QGWhSDdRuFTgW2E2h9dgdihchk", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1908128339 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908128339\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-676929429 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-676929429\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-87358663 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"259 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345888290%7C59%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87358663\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-525293997 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525293997\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-43882413 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRXK3poSytKTWlPdUJQaE9QdVhMbnc9PSIsInZhbHVlIjoiTm9SUzNkMUIweEV3US9TZlBFQi9laVJ1QnRoTnRhZ3pPOUJnTUlwUENzdkpDMGozanJBZmhlVTIydzFxVmRDMnRLRFpWQVJmUVZXTWpUems3UHF2MWxESGFnc0N0QS9JOEdZcmpMSUIrV0gyZjBMYlFHenFISHVGYTlqMDF6YXJrVE1WekQ1RERCZlZzd1BQTTAzMUtwd0s5RjBIMU0yRUk4eVAvVDI2SlpxQVNuNENJWWJ4SjQybmthQ056emRHKzFqeGtaVlQvc2M4R1JtUndkZzdlc2h0QmI3NXJYcy83RUhjVlZDS3dJYS85YmxQdzZkN2hmWjZxT3dWWGViNkt0Ny9jQnVSTDlTY25vUG9DTGNZMTFnZGRuSGx5Z2Q4TzJXMDZhYXBPY2RoNC8wb1p2VUVpV2lGOGQvelVQKyt1a3UrNDFpeEQ3OHNFTVBBUGRReE9HeHMvR015eVBSYllLV3d0R29yZkovbHBUczlJQm9kaXZJOXU4TStCd0JXV2xGWElVbXpnZzNyNCtma1JzNXJhd1FwcjVZZXRWV1kwa3hOWlVDeDZmaFVoYVBFRll5dGFXN08zVnRjMHFadkVodUVNVTk0QU8vR3VMUC9BOGY0NG5oOFFkOW5NOGlLSmdhbmdQWkM1bllZaHZjWDgzQ1I1bFNHeS9JSmdpbG0iLCJtYWMiOiJmMGZiYzkwZjYxNjZhZTA1OWFjMzU1OTgxZjc4OTU2ZTgzZWUyYmFmNWRmNGY5YzI2NmZjOTY4NTI5YmIxZDliIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitHd1RPNVRrTTZnTnlCak5JOFBFWUE9PSIsInZhbHVlIjoiNnVKOTdkTjVTc1VGalhlOWpZdjVzQm9mZE10bUd4TVM5VkRSd0pTWlYzbGV0M0FsNnR5VlRmQ0drUTZ2ejJLQ1g0aWN5RkdmVkpwSTR2QWx1L3k4UEd6cXRreTMxYVRNSEZoS0E2b1lEanF6dUpDRUhCekJpV29YZWlDc1VBc3FlbkxZWGxFTVF0cytFQTFDTTN5clF1K0hicnBLcXVsYmd0Qlp1T3NSbWhILy9qajc4TkI2RW1GWE54aENWR25IYm5yMXQvdysyNDJ6OGQvTzVsQ0Y4QldpRjBYSytnR1hvOU5xL2d6QzF2VGlNd2t4SUxjUkptcnBEckErSmZVdnNERFNjc3M5QjBtYzcrRkczb2RteEhIZ3dGSzFMbVBkOTlvelc1ck5vOFpzOFpMZDRsa1lEUXl1cWlGVU9id1dKZVJjU1RFNFhPTGR0Y1hydTRvRWJJVTdpeklJdjdIZFAvMXBzcVc0U1VWN3pCNVRCWFZ3T1RBa2FxNXRhaHlQdEZkZmhwSi9wbEllbVJ1d3MrRXk2VFBkaXFOa2dISFRud1Y2dmNWQmtOOTRZa0JhazZ6Tm8wZTRBdnZBUDdQbWU0aGVRa2R6VDYxWkwxYUhISU1haDlxRVpqOXpXQy84V09WWGxxVUQ1Qk0yTFplaVg3dkdwazRXZlJ5WDFQdEQiLCJtYWMiOiJjMTIyNzcwNjA2MmEzNGZjY2I3MGQ3MWQ1ZTdhMjAxMjVkNzgzZDU3Yzg3MjE0NDVlNDU2YWUzN2ZmMjEyNjgwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRXK3poSytKTWlPdUJQaE9QdVhMbnc9PSIsInZhbHVlIjoiTm9SUzNkMUIweEV3US9TZlBFQi9laVJ1QnRoTnRhZ3pPOUJnTUlwUENzdkpDMGozanJBZmhlVTIydzFxVmRDMnRLRFpWQVJmUVZXTWpUems3UHF2MWxESGFnc0N0QS9JOEdZcmpMSUIrV0gyZjBMYlFHenFISHVGYTlqMDF6YXJrVE1WekQ1RERCZlZzd1BQTTAzMUtwd0s5RjBIMU0yRUk4eVAvVDI2SlpxQVNuNENJWWJ4SjQybmthQ056emRHKzFqeGtaVlQvc2M4R1JtUndkZzdlc2h0QmI3NXJYcy83RUhjVlZDS3dJYS85YmxQdzZkN2hmWjZxT3dWWGViNkt0Ny9jQnVSTDlTY25vUG9DTGNZMTFnZGRuSGx5Z2Q4TzJXMDZhYXBPY2RoNC8wb1p2VUVpV2lGOGQvelVQKyt1a3UrNDFpeEQ3OHNFTVBBUGRReE9HeHMvR015eVBSYllLV3d0R29yZkovbHBUczlJQm9kaXZJOXU4TStCd0JXV2xGWElVbXpnZzNyNCtma1JzNXJhd1FwcjVZZXRWV1kwa3hOWlVDeDZmaFVoYVBFRll5dGFXN08zVnRjMHFadkVodUVNVTk0QU8vR3VMUC9BOGY0NG5oOFFkOW5NOGlLSmdhbmdQWkM1bllZaHZjWDgzQ1I1bFNHeS9JSmdpbG0iLCJtYWMiOiJmMGZiYzkwZjYxNjZhZTA1OWFjMzU1OTgxZjc4OTU2ZTgzZWUyYmFmNWRmNGY5YzI2NmZjOTY4NTI5YmIxZDliIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitHd1RPNVRrTTZnTnlCak5JOFBFWUE9PSIsInZhbHVlIjoiNnVKOTdkTjVTc1VGalhlOWpZdjVzQm9mZE10bUd4TVM5VkRSd0pTWlYzbGV0M0FsNnR5VlRmQ0drUTZ2ejJLQ1g0aWN5RkdmVkpwSTR2QWx1L3k4UEd6cXRreTMxYVRNSEZoS0E2b1lEanF6dUpDRUhCekJpV29YZWlDc1VBc3FlbkxZWGxFTVF0cytFQTFDTTN5clF1K0hicnBLcXVsYmd0Qlp1T3NSbWhILy9qajc4TkI2RW1GWE54aENWR25IYm5yMXQvdysyNDJ6OGQvTzVsQ0Y4QldpRjBYSytnR1hvOU5xL2d6QzF2VGlNd2t4SUxjUkptcnBEckErSmZVdnNERFNjc3M5QjBtYzcrRkczb2RteEhIZ3dGSzFMbVBkOTlvelc1ck5vOFpzOFpMZDRsa1lEUXl1cWlGVU9id1dKZVJjU1RFNFhPTGR0Y1hydTRvRWJJVTdpeklJdjdIZFAvMXBzcVc0U1VWN3pCNVRCWFZ3T1RBa2FxNXRhaHlQdEZkZmhwSi9wbEllbVJ1d3MrRXk2VFBkaXFOa2dISFRud1Y2dmNWQmtOOTRZa0JhazZ6Tm8wZTRBdnZBUDdQbWU0aGVRa2R6VDYxWkwxYUhISU1haDlxRVpqOXpXQy84V09WWGxxVUQ1Qk0yTFplaVg3dkdwazRXZlJ5WDFQdEQiLCJtYWMiOiJjMTIyNzcwNjA2MmEzNGZjY2I3MGQ3MWQ1ZTdhMjAxMjVkNzgzZDU3Yzg3MjE0NDVlNDU2YWUzN2ZmMjEyNjgwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43882413\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1602787414 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ppHWJDQH68Bkr0QGWhSDdRuFTgW2E2h9dgdihchk</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602787414\", {\"maxDepth\":0})</script>\n"}}