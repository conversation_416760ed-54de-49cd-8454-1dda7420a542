{"__meta": {"id": "X160b8d7727c95ab8840b6514db76d278", "datetime": "2025-06-08 14:41:12", "utime": **********.715681, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.125974, "end": **********.715704, "duration": 0.5897300243377686, "duration_str": "590ms", "measures": [{"label": "Booting", "start": **********.125974, "relative_start": 0, "end": **********.636017, "relative_end": **********.636017, "duration": 0.5100431442260742, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.636028, "relative_start": 0.5100541114807129, "end": **********.715707, "relative_end": 3.0994415283203125e-06, "duration": 0.07967901229858398, "duration_str": "79.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01617, "accumulated_duration_str": "16.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6746051, "duration": 0.01523, "duration_str": "15.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.187}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.702432, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.187, "width_percent": 5.813}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-324563864 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-324563864\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1687809212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1687809212\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1075028725 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075028725\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1851179559 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749393346722%7C61%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpwMTkwZHcvNXBKbURtSWwyQ0lTWWc9PSIsInZhbHVlIjoiNXNNNUwyWDBHSlRtVHFiZjVJSmoyRFdHZ1RqM3lwNmNVY1hSYjRJbEt6Tkd6alkyL09SYUlGejlYK1V3MXhzb09RNGFMNHVGdkY3blUzenFnN0gxdXNtK3dmdzluSnA5Q1BhRXZQejdMdUFrOFJhWk1TQ0lDZW5Scm9pUyt6Z0wyYXNyOU5UNHZhTkprRzNrTncwV1g2OFBxS2xjTTJZWThXcXdFN1p6V3NIWVRZZUs4ek5mbUlKd0hxZVhKMDdhK2x4WEFvQTRCSVhCUjlHWjdGdERVOFRpMjBVaUt3OHg2a1czVGwxTzZ4aDZoUXBJRjdTMzFTWTBGSjlQZkhkZmE0OHUrSXJYN25yeHp2c2RuNEx0R2c5Y1dBWFRRYUpKTEdwdG5RUWZ0M1FjU24xRUJmRS9VdWhweFgwaVNBWWtXYnUyQ2wwMkxBdFNxbjBpQmk0ZHBOWXBDcmQwSWZLWjRuVVhMY1dmVkloYlJpaEMyVUNoSzd4bEJWRFVRU2FwelFvZTBhMjRxVGtvWHhnQUhEOUVsZVp5OStZMkxqbmxaR1V4MTBXT1F6Z3ZlNVM0YnQ0N1NCNHhKTlNuWExTK0lwclYvQkkxR3hlVTBhdWdjUEh2N2RTaEE0YVNLdllEOWdQUlUxeGZnZzlLUm9YYVN3bm9tNFY0S3RvTDdKNDEiLCJtYWMiOiJkOTUwYzYyNmZkMGM0NmRmZjgxZDYyMDExMTc0ZTEyZjE4M2QxMGNjN2MxOTZiNjA0OWVjYWQ0YjdiOGJjYTNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlY0OUpiQzFVVlhhcE51QWFoVE5VR2c9PSIsInZhbHVlIjoiSWF5L1NtdVkwV05lMXFhck1Qc1kzOHhtZEU3RFFYMnlnanMvak5wbmVCQlFvVng2clZWQ2hKS0xIWUwzam5KTFAvY3VIYk5OMXIwYXZ5TFZ6QVRlVFNLVDBkUm42SEtpRU5sdExWV1hMTGsrZnlHZHVmek5hQTFvbTVrdlNybk1FN01UNlBXUlpEZzNpc0dPd3BJeDZmSVE0OFZkS2hkOWd1aXNrWDBOSDFQT21oZnRhZEdCOFBzOXZoWDZJRHdMdW85MDUwemIySURpWWJ2VmZYM0RoR05VbmFUcEcrdlpsS05wK1RXaEdoUCtYdUU3WUNZak83VU9xcXlDOEdwWHczZTkweHVRbnc3bzFleS8vbCs3cHNmdUxVd3drZGw4U1NFQTY0UEZpUk02bWVDeE1wdlNZcFRkc3ZGTGFxV1R4UmF3T1ZOSHJrZi84UDF1SDM3VHlUbDkrM3ZjTjMxTnYySTJrcWZnM3VDMW5ENE85b3dZcXRtSnZhemRrNU1lNlZ3Y3NYOFVpOW1xSWhCZVlpT2NXMlRadDVqVHI2U2ZSVFpXTERTUFR1N1d0K0w3dW04b2ZOMlExUmNPVFRHR1FVRzRsWW5CRXArT2w3RFpyM2pkK2VWYklNRmFEQnhDTXBVdjhadXpSc2dmbS9HaFR6a21xelFKdDNoN2tKTUgiLCJtYWMiOiJmNmVkODFkZmVkYTg5NDkwYjJhMjAzNTA0NmRiNTgxMjc3ZDMyNzQwNGM1NDVlMmJlOWU0ODQxOTYyNmM5ZGZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851179559\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-795377271 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795377271\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-239802004 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:41:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlCOS9CRXZQRytmVUhvZkZkbWRJSWc9PSIsInZhbHVlIjoiR3BaVW1hd2dxeVRFaEt3ODd3YXNZMEJPcWNaMXR0V3pURFE5UnBRVkZNR1Y5RWVPUGlFWmlSSE0vRmRMLzZrVnBLUmorMFpEQjFaOHpJcDBFOGRwWEJTeVA0NXhabDRJME9qWWx3TDBveHJYZFhJRDgwU05YZW9WNUJoVVl6VmNKN3N3RE1OUmVWditTL05TWlFhMlNORkRSZGxKRzRNRXhkd09pczRETWZ5QWtyZ2dTcnUwZENuRVA4U3VFUWZuVWR2TUVoNis0Z1NJcTBzd2xKLzg5SXQ2QkI5Mi95eDlSTURzSXJCbE02SjFIY3hxazFPNnpjTjdnT2xCWEROcEUzYUhhVWVYcGxMNGNKUU9qQmZkTjQ5RzBEQXUzM1pRUVZPUnRCMXdqYlQ1Tk1xODVkQzN1ZVhSSDk4a2UrbmFrTXl6ZXpibGNJSHJlVDAvTEdabXFucUs0TXdQUFNicXdWZk9YWEpPU0NRYXplUlRBTjB5SC9TbUJYc2lVYjVneDhadjJJRDJHSm9TVytZbTQ0VEEvcnhvNmVaOXRkR0RHY0hWMDQ3Z0tUeVlqckkyQmFoNDBjcEMvOTZWYnVnZWhqTGJiSXhqcmRCZjE1MWNRY2R1VXgrQlhZN0ErTXgrZXloZnB5aXVydnV5YUlxZ3lRMTlURmloYTB1SHJKOEoiLCJtYWMiOiIyZWZhNWEyNzY1Y2QzZTdkZTg2ZjNlMzc1ZjY1OGFlOGY0M2IxMGU4YjgzYzFmYjc3ODIzYzI3YTk5MjVjYmI5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:41:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imdmd1pCMVd4SERyU2ZnVmtJT1hyU0E9PSIsInZhbHVlIjoiRUZZcU1PSDJQNHJvWWF5Sjlpd3UzQjNid201OE5DdEdqa3NFQ2djZ2dwL2crMW9qSE5ZUFlCRTRYZG1zV05PemJiSFZ5djdmd3ppUGlMd3BwVjAwTW9MSWtVMElWeHJDQ3ZweThseXg5Ymp0UzJXc2Mwa0ZHZVd6T0ZXVWM5V2k5bFhTd3lZZWxPS1FmV1d6eUd3enN2VDV6bXphTXo2WDFMKzFVQkpyMXdCRGdMWkU5Nk5wbmxNaTdsdXpSR2drTWUwdXNhR2xQTGk3RUxqdk4rYi9rL1paUDR3Nzl3N2g1L0J5L2tjclRaNDVpN1pFdWl0YXV0alU2TnlHUnBubFRuWnREZjZvN2YzcEJKTTMwQjRZVmtycTBVV09RQk4vRGUycHNVZFhpWlBZUThJM1c4RE8rb09FTDdzSkExT3RqUXZNSFFicU50eE1wejlFWU5OMGpxZmNDYWpWY2N5NUl6N2NxdjlxRHJjb0U0TmIvZHZ3VFBCUUsxT09hQUs4NDZBNWdabDJ5Si9zMWpKODRNTVpPL3RzaFVITm51VFYzc1lCUStaK3dVSms4NGJrRnRiVEVGUzZDUEZ2WXFmZXFjUnN0ZTczWHZJRUs5QzV1SkpIcjU4dnNWY0NPMzJiNWpFOWkybUpSUGtEL3A3SEw0NThFbDcyZFdCdWExZmIiLCJtYWMiOiIyNTMxNWQwYmZjN2FiZDRlZDhjZTYxODA1ODE4OGU1ZjZiM2NiMzk0YzNiZmI4OWRjZTc2MGYwNzc4MTA3ZDY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:41:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlCOS9CRXZQRytmVUhvZkZkbWRJSWc9PSIsInZhbHVlIjoiR3BaVW1hd2dxeVRFaEt3ODd3YXNZMEJPcWNaMXR0V3pURFE5UnBRVkZNR1Y5RWVPUGlFWmlSSE0vRmRMLzZrVnBLUmorMFpEQjFaOHpJcDBFOGRwWEJTeVA0NXhabDRJME9qWWx3TDBveHJYZFhJRDgwU05YZW9WNUJoVVl6VmNKN3N3RE1OUmVWditTL05TWlFhMlNORkRSZGxKRzRNRXhkd09pczRETWZ5QWtyZ2dTcnUwZENuRVA4U3VFUWZuVWR2TUVoNis0Z1NJcTBzd2xKLzg5SXQ2QkI5Mi95eDlSTURzSXJCbE02SjFIY3hxazFPNnpjTjdnT2xCWEROcEUzYUhhVWVYcGxMNGNKUU9qQmZkTjQ5RzBEQXUzM1pRUVZPUnRCMXdqYlQ1Tk1xODVkQzN1ZVhSSDk4a2UrbmFrTXl6ZXpibGNJSHJlVDAvTEdabXFucUs0TXdQUFNicXdWZk9YWEpPU0NRYXplUlRBTjB5SC9TbUJYc2lVYjVneDhadjJJRDJHSm9TVytZbTQ0VEEvcnhvNmVaOXRkR0RHY0hWMDQ3Z0tUeVlqckkyQmFoNDBjcEMvOTZWYnVnZWhqTGJiSXhqcmRCZjE1MWNRY2R1VXgrQlhZN0ErTXgrZXloZnB5aXVydnV5YUlxZ3lRMTlURmloYTB1SHJKOEoiLCJtYWMiOiIyZWZhNWEyNzY1Y2QzZTdkZTg2ZjNlMzc1ZjY1OGFlOGY0M2IxMGU4YjgzYzFmYjc3ODIzYzI3YTk5MjVjYmI5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:41:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imdmd1pCMVd4SERyU2ZnVmtJT1hyU0E9PSIsInZhbHVlIjoiRUZZcU1PSDJQNHJvWWF5Sjlpd3UzQjNid201OE5DdEdqa3NFQ2djZ2dwL2crMW9qSE5ZUFlCRTRYZG1zV05PemJiSFZ5djdmd3ppUGlMd3BwVjAwTW9MSWtVMElWeHJDQ3ZweThseXg5Ymp0UzJXc2Mwa0ZHZVd6T0ZXVWM5V2k5bFhTd3lZZWxPS1FmV1d6eUd3enN2VDV6bXphTXo2WDFMKzFVQkpyMXdCRGdMWkU5Nk5wbmxNaTdsdXpSR2drTWUwdXNhR2xQTGk3RUxqdk4rYi9rL1paUDR3Nzl3N2g1L0J5L2tjclRaNDVpN1pFdWl0YXV0alU2TnlHUnBubFRuWnREZjZvN2YzcEJKTTMwQjRZVmtycTBVV09RQk4vRGUycHNVZFhpWlBZUThJM1c4RE8rb09FTDdzSkExT3RqUXZNSFFicU50eE1wejlFWU5OMGpxZmNDYWpWY2N5NUl6N2NxdjlxRHJjb0U0TmIvZHZ3VFBCUUsxT09hQUs4NDZBNWdabDJ5Si9zMWpKODRNTVpPL3RzaFVITm51VFYzc1lCUStaK3dVSms4NGJrRnRiVEVGUzZDUEZ2WXFmZXFjUnN0ZTczWHZJRUs5QzV1SkpIcjU4dnNWY0NPMzJiNWpFOWkybUpSUGtEL3A3SEw0NThFbDcyZFdCdWExZmIiLCJtYWMiOiIyNTMxNWQwYmZjN2FiZDRlZDhjZTYxODA1ODE4OGU1ZjZiM2NiMzk0YzNiZmI4OWRjZTc2MGYwNzc4MTA3ZDY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:41:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239802004\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1705182867 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705182867\", {\"maxDepth\":0})</script>\n"}}