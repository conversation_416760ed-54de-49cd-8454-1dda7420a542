{"__meta": {"id": "X2567ab099f8a868c7c3cf784efde7d07", "datetime": "2025-06-08 13:26:55", "utime": **********.259648, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389213.944437, "end": **********.259677, "duration": 1.3152399063110352, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749389213.944437, "relative_start": 0, "end": **********.128877, "relative_end": **********.128877, "duration": 1.1844398975372314, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.128899, "relative_start": 1.184462070465088, "end": **********.25968, "relative_end": 3.0994415283203125e-06, "duration": 0.13078093528747559, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43932600, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.005979999999999999, "accumulated_duration_str": "5.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2211158, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.268}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2396212, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 80.268, "width_percent": 19.732}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-595481325 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-595481325\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-739431744 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739431744\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1202501869 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1202501869\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1740158664 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im9qeW53VTZvUkoyQWxRVXFhNGwvRlE9PSIsInZhbHVlIjoiV00zR2RZUkhaTWtDVnJYUGlCTWVGSHBuZlhSeXNSQVdjakxlc29qVnh1MDRPZTIyTEJ1RUc5VUFPMzVySEQ3RWdkMFgzTFZ1aTNEVVQydk0rMnNhYVUvb09ldDhYSXpMY3ZjV0R4Qms4SU1rMXNkcnpTcEErRng5ZS9mZnBsVUZER1Jxd0V0M3lQVEk5eDJvNGZubGJ2T1dYZDlVeVkrdUxQc1lLd0J1ZmsrOVh4aEhHdFd2b1cySDNBaW13bTE5UFo5YWRVdlFzS3pyeUtiaFROaGlFdVhINVZMcDI5RnNQTUtBTmllRmRLMmxMU1o5MWVxVFNEajJjQWtaQUlxR3J4NGQ5c3M1OWl5Y1lNN0dpTGFtZUd6eVdBU3RLeVVuK1B4VkNSU0pZWXNGWFhtdXloTlRtbDlJNDVIQjZnbmFwaTlTWXZ6MTJ0K3BPSG03bGh5REhPM3ZMWUs5a2U1clpJNGxZK0pHelprSkZJSEZEcWcyNmJ2QlNmcGRWaE1SeWE2NmorSGJuQ1JjV05Kc1NjaTNtRlBpR3JDVHh0eVBVZ3B3OFpLUit4L0Fha3U0V0w2dklncVI1dEwxU0xpNzYvSkdKR1pDdmI4QXh5K0dDRW1kaWFoUGw5TCsyRUp0cVk0T2hudUEySndJcmd1SVZtMGl5UHdkT0pBb0t1U00iLCJtYWMiOiIyZGEyNDc0ZjdhM2MxOGZkZmYzNmQwNWJkOTQyZmJkNzE2ODZjMWVkMjk5Y2U4Yzc5Mzk4NGUzYzM3ZDZjMjVmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik8vWWYrWTczSFZtVDRkdWdwZWk2S2c9PSIsInZhbHVlIjoiWXdaRW9FUjY4NWp2cmlzbS9sd3gxa0NIYVVSbjhVc2FDeUlHdUgxYUJaWjVpbk5lTmlDTk96amFKMkhvU3lDdHd2SitQMjlVazArTWFyOVBCZGJaeVVTQ1NSc2VSQjdtVkNQUWppYm5GYWRaQldVWVFJLzZkZXJ1eXRkSUVKZ2JDUFFzVjlvU3NDZzhxZFlBUEM2Y1RQQWx6V2tha2s1MDMvald1TWdNYm5wVnYzSlVGcVhSM3hyRmZBY1pFNEIxV1p5ZUhJZ3VGbTFrNEdTTkpqelYyeEhCZTFtMGtsTGFnZ1RRL2pmQTNRODVLR1JLaXNSSDQzallrRmM0L0lNZWtuQTVENzdxbUx2c3p4bEhoSGtOakFsNmZxNndyTXpZeWRBUXpnV0JGZGRTUGtYME5pYkoweUxMdTJPWlEwenp0VVZDalZCZ2hVNXZmRElheXovWDd1MXRqQWpwR3dXWFptMGZpVUNPMEhhQm53RS9xRjlFT082UFdjYlR5Y0E5RHh1MXNXTmRJS3NtNTN6VWFLdVVPMjVxamJ5SDloZ1h0MmZuVlJSZGMybEhWbzE1VkJlMUIwcS9tNWJwTFNLa2NJazArcVRabnRxSC9zdXZiS1JMSGFTMCtZM2ozTmIzZEhOdXZSaWlGcWhGTjlWMFJBVnRNdHVnVXhSczZNSFIiLCJtYWMiOiJiNTQ5NDI0Yzc3NWE2NmQxNzJlYzg4OTA0MDVlNmI5ODMyZWU2MzE3ZmY4OWZhZjc1MmY4ZjIwMjRkOGEyZmYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740158664\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1426678693 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426678693\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-669336560 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:26:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBqNGlQM0ZlRjVBcDFLT2Zqc1BUMlE9PSIsInZhbHVlIjoiY20yZVRvdlRtK21DZEo5VGRDNm11NjBlVU9sV3UxMklQU3pQVkR0UmxIcGxpTVU0b2x1L2ZnOU1Ec2cveEFCdGxTd2VBWWRMQ3V4aENDWUVwM01hS29PZTV6OXNBblRSZHBTelQzb3ZKWC9oanpQbU5HSnRVbmRrNkFIc0J0bGNwdEg3aWZxcEpVQXhIVTU5REp3eGZxbi9hb25pUUhRY0VpcEhnYTBLeUFWNWk0Nit4eUNSS3Z0VGxBYVBNT2dtc0g2amJldWllSmRNYXcra09McllzRDJGbFRTaWNzU2pOM05PNnRoTnhTYjRjdHRmMVBIUVZWM21xenlMT2g2L0pCcGdZUmx5SlVBRHhIK29xT3V6eTNFbVhyMWFRVWVQVTFpYWJnSEl4b1JXU0s5b2RCSThTMUdPMzRudk12RVRUS2ZiaEVMb2VKU0dCdzludzhMdFNvZzNUMG1QTElHRlZpaXZIQ1hTWmNDOEJJTzNWbHEyZjRhWDh5RC9adERNdzRmejErOXAzSlg0MWYwRDlPZ3Ewd1JsTEx2RWsxYzB2Ylp0NFN3VlQxVmFKOWlqYkJPTG9qN0dPWmFuZjRGR2EvK1U3eFFoUUVRakJNYnNUY1NsVTFJb0dGK0pYQnJlM0duUGoyMlhGK1loblduVVBHYzFiOUxjS0E0aFVwVGMiLCJtYWMiOiJlOTczMGM5NzQwMGQ1N2I1ZjNiYmExZGJhNmYzNDRiNThhYWU5ZTZhNjUwOTRhNjA1ODZjNGEyZjFiMzBhNzM2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9uL0ZkWnBubnMrd0VGek9WWXZ4VEE9PSIsInZhbHVlIjoiWW1wNXlybENhK1Zqd3dONWZGNm9jTVNqYTF1ZDRzSDBvcjVDWjRicm5xVlNYN3FBSy9qNmo2emZkMkNmTVBQTHBnZXhIdE5zbTV3V2Y2dm81QmdkY2dINHV1NithQjBkTDVPSlRTNmd6dFRSOXBkVXE3cVdMTitpMXkwbFB4ZjladUhHWkhaNjF0cTZmQmE5Q2NuZFpFck1Mbzg3cnp3RHVyY09HVGVva3pNSkZJTWhqQ2ZFZTBFRlAzbm0wQ296VDdodktWRWlsaEpmOFRZVTB0L0JERlErN3RYWnUxUnhOczFhckNsYkx2VlpiYVB1Qlh0bUFHNlh1Wm9Pei9hcU1IaWpuckFTMS9IZUQyL3JsY2M3UEZCb2dldEJOd1VoeTFRN3MwM0NZYzIxUEdaOXBLNldNbThxM3JuM0U1R1J1TXhnUG5VazUyekhzSHBubW9ONFNUTXZkRzFocnlyTjZjSkErNVJqQ09KR3NISnovSmhPdUVrQWJwbEtIMWo3b3J1V3ZveVN6R0VNT0NxeEtjSllUSXlyaHMyNXlMNkU0QjBlVVh0UmsrSWFnSWRKeC9Kell2ams0NXR6ZVNZT0hPRmZlaW04UWtDaVlIaTc4QjVjVTFoUTJ0K3ZaNTh4SDI1c3hnQVBucUxYWHk3dlAzUzVzL3JOeGJzd0ZGeWUiLCJtYWMiOiI0OWYzOGVhYzUwMTg2N2FiOGFjMWM3ZTJmYTQyM2E3YWM4NGUwZGVhMTU0Zjc0MjIyYzBiNGQ2NmJmMDEyMTk1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBqNGlQM0ZlRjVBcDFLT2Zqc1BUMlE9PSIsInZhbHVlIjoiY20yZVRvdlRtK21DZEo5VGRDNm11NjBlVU9sV3UxMklQU3pQVkR0UmxIcGxpTVU0b2x1L2ZnOU1Ec2cveEFCdGxTd2VBWWRMQ3V4aENDWUVwM01hS29PZTV6OXNBblRSZHBTelQzb3ZKWC9oanpQbU5HSnRVbmRrNkFIc0J0bGNwdEg3aWZxcEpVQXhIVTU5REp3eGZxbi9hb25pUUhRY0VpcEhnYTBLeUFWNWk0Nit4eUNSS3Z0VGxBYVBNT2dtc0g2amJldWllSmRNYXcra09McllzRDJGbFRTaWNzU2pOM05PNnRoTnhTYjRjdHRmMVBIUVZWM21xenlMT2g2L0pCcGdZUmx5SlVBRHhIK29xT3V6eTNFbVhyMWFRVWVQVTFpYWJnSEl4b1JXU0s5b2RCSThTMUdPMzRudk12RVRUS2ZiaEVMb2VKU0dCdzludzhMdFNvZzNUMG1QTElHRlZpaXZIQ1hTWmNDOEJJTzNWbHEyZjRhWDh5RC9adERNdzRmejErOXAzSlg0MWYwRDlPZ3Ewd1JsTEx2RWsxYzB2Ylp0NFN3VlQxVmFKOWlqYkJPTG9qN0dPWmFuZjRGR2EvK1U3eFFoUUVRakJNYnNUY1NsVTFJb0dGK0pYQnJlM0duUGoyMlhGK1loblduVVBHYzFiOUxjS0E0aFVwVGMiLCJtYWMiOiJlOTczMGM5NzQwMGQ1N2I1ZjNiYmExZGJhNmYzNDRiNThhYWU5ZTZhNjUwOTRhNjA1ODZjNGEyZjFiMzBhNzM2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9uL0ZkWnBubnMrd0VGek9WWXZ4VEE9PSIsInZhbHVlIjoiWW1wNXlybENhK1Zqd3dONWZGNm9jTVNqYTF1ZDRzSDBvcjVDWjRicm5xVlNYN3FBSy9qNmo2emZkMkNmTVBQTHBnZXhIdE5zbTV3V2Y2dm81QmdkY2dINHV1NithQjBkTDVPSlRTNmd6dFRSOXBkVXE3cVdMTitpMXkwbFB4ZjladUhHWkhaNjF0cTZmQmE5Q2NuZFpFck1Mbzg3cnp3RHVyY09HVGVva3pNSkZJTWhqQ2ZFZTBFRlAzbm0wQ296VDdodktWRWlsaEpmOFRZVTB0L0JERlErN3RYWnUxUnhOczFhckNsYkx2VlpiYVB1Qlh0bUFHNlh1Wm9Pei9hcU1IaWpuckFTMS9IZUQyL3JsY2M3UEZCb2dldEJOd1VoeTFRN3MwM0NZYzIxUEdaOXBLNldNbThxM3JuM0U1R1J1TXhnUG5VazUyekhzSHBubW9ONFNUTXZkRzFocnlyTjZjSkErNVJqQ09KR3NISnovSmhPdUVrQWJwbEtIMWo3b3J1V3ZveVN6R0VNT0NxeEtjSllUSXlyaHMyNXlMNkU0QjBlVVh0UmsrSWFnSWRKeC9Kell2ams0NXR6ZVNZT0hPRmZlaW04UWtDaVlIaTc4QjVjVTFoUTJ0K3ZaNTh4SDI1c3hnQVBucUxYWHk3dlAzUzVzL3JOeGJzd0ZGeWUiLCJtYWMiOiI0OWYzOGVhYzUwMTg2N2FiOGFjMWM3ZTJmYTQyM2E3YWM4NGUwZGVhMTU0Zjc0MjIyYzBiNGQ2NmJmMDEyMTk1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669336560\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1241923055 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1241923055\", {\"maxDepth\":0})</script>\n"}}