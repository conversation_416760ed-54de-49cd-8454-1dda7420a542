{"__meta": {"id": "X05122bc1cf83a2af327dbac7183d4fb6", "datetime": "2025-06-08 13:19:48", "utime": **********.225706, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388786.805309, "end": **********.225737, "duration": 1.4204280376434326, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1749388786.805309, "relative_start": 0, "end": 1749388787.9484, "relative_end": 1749388787.9484, "duration": 1.1430909633636475, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749388787.948418, "relative_start": 1.14310884475708, "end": **********.22574, "relative_end": 2.86102294921875e-06, "duration": 0.27732205390930176, "duration_str": "277ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48125208, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03704, "accumulated_duration_str": "37.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.062159, "duration": 0.02859, "duration_str": "28.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.187}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.116823, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.187, "width_percent": 5.103}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.171865, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 82.289, "width_percent": 4.617}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.178407, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.906, "width_percent": 4.32}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.194314, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 91.226, "width_percent": 5.562}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2036371, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.787, "width_percent": 3.213}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-318645842 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318645842\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.191452, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1960970799 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1960970799\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1311563013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1311563013\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1143359432 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1143359432\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1735246887 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijl2VFBSMFRYc2dyVm4xc3R1WTRxTFE9PSIsInZhbHVlIjoiSExxM0h4bEdkaVZ1cVJVQkp6anBDT005UnIvVVZWcWgwNSt2ak5FN0VCZi9Bdlk3VHZwZ1cxTmtmWGNWM0FXRUl4cGRxQy9ZVXZlcTMzZHNXTEJyY29semRBRjVndENHTlhEdGVHeXpqeE13ckM0OFJIcndSVUhkR25sTzRzMlhhd2VuYUMzRWRoUmlZcUpZYndnQUY3MEsycjVqT1Q1cGcrQlBIeFM1cFRhdk9tMlpCd0gwZE9ZckMxQmloSzlha3MrVm5PY3B2alM5c0NHQnM2RXFMNWlNQkp3RnUyZldlRHhreVpzMkdOS3A1dmcwSEhyUHJtVUs0dndpVzlZU3A5WUY0VGFQb0tlejdyYW5oajR1ZFp0NmNtN3M3OUtlOHIvd3ovTmhyTDdNN3ZQUVdvcW5VN3FwQ0k4ZXdacnJpSGxYeGY0ME1meUtlaDhHcmdIYktTS2tjREEycyt6dzlKM21zeVRqUExGMUhPNTVmQ0lzK0xoZE1ScjU2N3J3UVNKZVpJVWJxNmNQV1hsNStjMVFjYkJtMzVVMFUwaGd6WFNoM1hKQk9XTXBaOXI3bkFjVXhwN0Z1SkJpTVBrV3hlWXVWZFNIMjA4QlVFYWV5NStqekNiRTdtY0VYRm1QQzBLSi81TEVnQXFxMEFONlI2c2laQjMweTFibWhFdFciLCJtYWMiOiIwYjBlNzRmNzJkOGNiOTg1ZmY0Njk0ZGVmZWVlY2RkZDQ3NzU1OWNiYjlkZTRkNzc4MjJmNDZlMzZlNjc0ODNjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkM3NnRGYlRPNTdnb3dPR0g1RzB1M1E9PSIsInZhbHVlIjoiZXdYSlRxOHBWRnlEUDZaUGx6bFZublBmeFFraDl5eDZYWm9oUjZ1YUMwNmFFUENsR09EQ0xUSCtObFkyWVpLRnJOQkwyVFpZWVUwNzV6WUt0R1AyVWJJK1FkaG5QNEtGWFA5VFlKSkdiRVc5SjZoT1l6bUp3cnJja0ZjZFl3ZmtuSWhVZHgzRDJha2NNMlU1QW1RWUtGLzRaZlk5SWZVN1I0UG1XM1k2UUdxYjBrYi9wY1JJNWpPa3hva0NVTzFGV0NRbVRTUjhBTGx3N1BzSUlNSC95N0FhS2lmNkxJVVp5Wm1TYUNReVBkM1U2SmtZS043KzZtYVB6R05ZeXRGazV6NzBRdmxsakZrblJGVTNJUzVpMkt4SkVTcGFwTk9QZHdINms2NTd2cmNhbDlHbGlYTmhYZUNqYnpSTUtnZGVDbGh2WkttV2NseCtPRzBEV1BWRUdDVVpJSDUyL3g0N29ucE1STTQ0VUpZNU1DcVh6MWxZVHhkWk1VeDRtYnNjK0FNRXNtSGI5V1V0aENOYTZhVzZUWW1Hd2ZXRVBVQW9JcVFYUkNNaXdmS0YyRmo4UmxuaWZvbDdHbENkZ2hoUDMzR1RnNGpxbk9LSUNHcjkwMWVnOThjL3FpSDBFMGhuTDl3V0RodCtZMjc1QksyRWFobXFKc21CdHlYR3J6R0wiLCJtYWMiOiIyODcwNTllYzk0YmYwYjlhNTY3YmEyMDI4NDI5MTE5NDkzMWM3NTM1MjU3MjdiMTQ0NDIwNWMwNjU4NTlmMThhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735246887\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-623020146 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623020146\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1897022353 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpGYW5vMElZem5Gcm9qcTZ2OXB4d2c9PSIsInZhbHVlIjoiL0JRTExDVjNKRFJxV3VHV2dKbFE1aVJJTWpkdS81N2g0TFBRVjFTc3M5ci9hN2pGM3pDVnFzMjJvN1Z2TzBaNmVBTDBsRmx6OXdid25zbGlseGVUazh2dE5tdVg2em8xSTlrRmRPek1QV05OVThsVnAyOXVvRlBnRzBjVVFyV0E1ajArYkxnbzg3czYyUTZpTlVtc0VSOVNrWlpjMlUyWENnRjRvTHlzc21hV3ZyUTRXakpnZzhEd242Y05kNUIyTEdJQld2YXdaR1ZnN1dtRGMzR28vVnpHQWdQRjRrUFNuZHc5TWYra3dDQkRQdjFiMzBqZU5lWVB2Y1BlVFV6M2tZMi9MVXhScGk1ZnJ3enNhTnBwTy9lUUYzK1RwNWhwaFkrNklEY0ZBN1dXVHFRTDlkZEE0U3NNYmo3OFJkUEFMelFnQnpNeVREQ3Zmc0dMajRlRWpld1ZMYUJxai9mdUlEVlZsNW5RSUVxNlA2dzNHWGQvN0FQOGZheWx6OU1mUm9MSkRoSjBpTmd0WUgrYmo0UGtPMFlHUm5oWEhvWmladS92WS9KQTErV2dsY1NnMVg2ZmgvaklMQlN2eXF4Vm9NR21GWnBTb00vV0FjNUMzek16Sm5PZENsRlgyL2VwT3hubHc4alE3NDZhaUdQTC9xUURrRUpsYTh1bHNYa3kiLCJtYWMiOiJlODhmNjRkZTU3NjI2YjJhMjIwYTkxZDE0NTVmOGZlNzkzNTNjMDE5ZGNlZDcwYTk0OTU3MTE1ODdkM2FkZmQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImgxdTg0VzFtc1FrWmJFclM1U3NxOFE9PSIsInZhbHVlIjoiMU9TNzRKV3ErRFJQTVdpdWlUa3BiTkVid1U0K3gra05hTWpNcDg4Y2R3L3cwZzNaT2RNL09CeTZjcTkyb01BWWh6Z3Y3WHdCYU0rNzB4bXZDTWUwL2x6UTNGN0hUMzJDb1RDL3NJZUZWclpTeXBNM3orMXIvK0tnRmhHQnB6RnNBdzdTQjJFbjE1ZVh5SFNsVU1HUVM1N2pzVklib1p6RjhsRFJ6c1FqQ3hsM1NwUUVHZGE1YUtCM3VNOTBMcUxCOE1rTHc2M1NnTFozY0hmN29BMHpIMVVDazhDcGlpWGNxcG1DSmREWnk2NHpORjhQN20vQWx0Z0JSVUl5M2JWdTRFRnc1bWdHVEV2WWMwc2JKMTZsSk9pbTU3OFYwU21Ga3NtMnZoNjhKWGM0TDE3bVJ4eXBFdm1wWEFQbm9UbHNKRU5rS1dkZ0ViVExmdU9YSEc4cXArM0I3bVN1ZTZ4eEtCZVN1Sm1HOFhPVzdCci8vM3BxMHVUUmdyNzVBQ3ZxTjB4eUFXTk5xTUxvbm1uZU1SM3dMVEpYejhYV09xNnJPVUlaUlNwL0U1aG04R1huZUFXUFVGd0dRMkp5RXZsK2VSUkY1bnplWEdEd0dURWQzR1RqSUVQVW5FV2xrZVoxc0tlVkZ0RGtzMjdaaEJqazN3c0Z5MVZQclRaV3hPNlIiLCJtYWMiOiIyOTU3MGZlMmIzMDIzOWE2ZTI2NDI4NGQxNjkyOTE3ZDYxYzQ5NmMzZGVlNTI3MmFlMWI5YmQ5MjQwYTI3OGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpGYW5vMElZem5Gcm9qcTZ2OXB4d2c9PSIsInZhbHVlIjoiL0JRTExDVjNKRFJxV3VHV2dKbFE1aVJJTWpkdS81N2g0TFBRVjFTc3M5ci9hN2pGM3pDVnFzMjJvN1Z2TzBaNmVBTDBsRmx6OXdid25zbGlseGVUazh2dE5tdVg2em8xSTlrRmRPek1QV05OVThsVnAyOXVvRlBnRzBjVVFyV0E1ajArYkxnbzg3czYyUTZpTlVtc0VSOVNrWlpjMlUyWENnRjRvTHlzc21hV3ZyUTRXakpnZzhEd242Y05kNUIyTEdJQld2YXdaR1ZnN1dtRGMzR28vVnpHQWdQRjRrUFNuZHc5TWYra3dDQkRQdjFiMzBqZU5lWVB2Y1BlVFV6M2tZMi9MVXhScGk1ZnJ3enNhTnBwTy9lUUYzK1RwNWhwaFkrNklEY0ZBN1dXVHFRTDlkZEE0U3NNYmo3OFJkUEFMelFnQnpNeVREQ3Zmc0dMajRlRWpld1ZMYUJxai9mdUlEVlZsNW5RSUVxNlA2dzNHWGQvN0FQOGZheWx6OU1mUm9MSkRoSjBpTmd0WUgrYmo0UGtPMFlHUm5oWEhvWmladS92WS9KQTErV2dsY1NnMVg2ZmgvaklMQlN2eXF4Vm9NR21GWnBTb00vV0FjNUMzek16Sm5PZENsRlgyL2VwT3hubHc4alE3NDZhaUdQTC9xUURrRUpsYTh1bHNYa3kiLCJtYWMiOiJlODhmNjRkZTU3NjI2YjJhMjIwYTkxZDE0NTVmOGZlNzkzNTNjMDE5ZGNlZDcwYTk0OTU3MTE1ODdkM2FkZmQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImgxdTg0VzFtc1FrWmJFclM1U3NxOFE9PSIsInZhbHVlIjoiMU9TNzRKV3ErRFJQTVdpdWlUa3BiTkVid1U0K3gra05hTWpNcDg4Y2R3L3cwZzNaT2RNL09CeTZjcTkyb01BWWh6Z3Y3WHdCYU0rNzB4bXZDTWUwL2x6UTNGN0hUMzJDb1RDL3NJZUZWclpTeXBNM3orMXIvK0tnRmhHQnB6RnNBdzdTQjJFbjE1ZVh5SFNsVU1HUVM1N2pzVklib1p6RjhsRFJ6c1FqQ3hsM1NwUUVHZGE1YUtCM3VNOTBMcUxCOE1rTHc2M1NnTFozY0hmN29BMHpIMVVDazhDcGlpWGNxcG1DSmREWnk2NHpORjhQN20vQWx0Z0JSVUl5M2JWdTRFRnc1bWdHVEV2WWMwc2JKMTZsSk9pbTU3OFYwU21Ga3NtMnZoNjhKWGM0TDE3bVJ4eXBFdm1wWEFQbm9UbHNKRU5rS1dkZ0ViVExmdU9YSEc4cXArM0I3bVN1ZTZ4eEtCZVN1Sm1HOFhPVzdCci8vM3BxMHVUUmdyNzVBQ3ZxTjB4eUFXTk5xTUxvbm1uZU1SM3dMVEpYejhYV09xNnJPVUlaUlNwL0U1aG04R1huZUFXUFVGd0dRMkp5RXZsK2VSUkY1bnplWEdEd0dURWQzR1RqSUVQVW5FV2xrZVoxc0tlVkZ0RGtzMjdaaEJqazN3c0Z5MVZQclRaV3hPNlIiLCJtYWMiOiIyOTU3MGZlMmIzMDIzOWE2ZTI2NDI4NGQxNjkyOTE3ZDYxYzQ5NmMzZGVlNTI3MmFlMWI5YmQ5MjQwYTI3OGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897022353\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1625383070 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625383070\", {\"maxDepth\":0})</script>\n"}}