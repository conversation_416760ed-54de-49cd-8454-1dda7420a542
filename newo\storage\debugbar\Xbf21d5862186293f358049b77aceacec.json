{"__meta": {"id": "Xbf21d5862186293f358049b77aceacec", "datetime": "2025-06-08 13:00:47", "utime": **********.781858, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387646.455706, "end": **********.781892, "duration": 1.326186180114746, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749387646.455706, "relative_start": 0, "end": **********.597345, "relative_end": **********.597345, "duration": 1.141639232635498, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.597365, "relative_start": 1.1416590213775635, "end": **********.781896, "relative_end": 4.0531158447265625e-06, "duration": 0.18453121185302734, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45577456, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0203, "accumulated_duration_str": "20.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.693723, "duration": 0.0179, "duration_str": "17.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.177}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.736686, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.177, "width_percent": 5.123}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7559202, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.3, "width_percent": 6.7}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/create\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-124497920 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-124497920\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2070982622 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2070982622\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-399431929 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399431929\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-527860454 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387610576%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJZejVnaVk2djl2MkJqdThBcXd5U0E9PSIsInZhbHVlIjoicXVBckZncC8vN0Z3MUl5WTY3OFdTWmdMajFSRWV1dzV1TmhTcWpXZXBsVUY1NFppWHVMcG9EWmE3M2ZabHdiQnNBVnhQdkY1UGN1WTlHNUVLZGl0cVNCWjZ5OCttZlFUSk1sMG9pOXk2aGwzZUtmako0di9mbHFYZGhyYk1JSWM0YVhrYStJa3psNWF5RHNDVXdsVllTbk15Tm50UUFKaEswbENSVEh1SERvWWEwSEhOTE1MalpmWm9Wem9YMzFpQ3FZZHZORStnVEF2UUp6aXQxNHZnSVozRzhVMEdGUTI3cnJQMmpiMXAzYVlJUFJ2SjR2K20rbjlKT2Z5VjJQUjZlcFVYQnFDYU9MaFVFVjlMWGpRTmczK0lRRTRZeEhFSThiUTcrSjA5S25GNkliZ2pnRkpFMGRiOGdFQ2VucERrUUQ0ZHRjZklvYytVcThYRTBoaUJCUGVhVVJJclFMWkpjekVKcXZqdlEvSEt5V01LcURpL2YxQUsrVDZiTTNHeUJYb3E5OW9pRzhVWVhyNmtlTmpiVGtZYVB0YUNvM0hNYkxDaTNTem9SenBBazFPa3FhYStaSXBmTHJvcjNjQzVEbGI5WGk5dVpEQ25wUW5lNndhZHByT1FCRDJXME43VnNJMXRod3NnMTRGSllHaHIzMHIyRXBRSWpIcVhrZkQiLCJtYWMiOiI4MzE0NjlmODVjODI0YjY1MTE3ZDllNjkyZmM2NTRkODNlMjlkZTM1YjdlMDMwZDgzNmJjNjM1MWY2ZmIxOTBhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNuWEJNREFpa3puRWdiLzJMVm8wanc9PSIsInZhbHVlIjoiYkc3OXNSWjZhZUlYclI0c09GWUxRREEvT2pCcHBwbmRQem1LbC9KSnFyTWZNQWg0Zkx1a25uK2hkZ21JaWxvMUVHOFRyYWlvNkZWOVJLdFdENnFVQjcyT3YzUWJSTWxyaFF4RU16V1JzNEhaQzZRZllvSC9xd1RIMWxSTlpBWmMrK0lsc3J3ZFU2RFJFa1ZUeFNmaGFHczY2OW13L0sxMTdMSFhnd0lPUVY5SG9Jb3JBSng1dXp5YkthbUZoNU1VS3g4blNFVS9XOGFzWUpKcVJxSWZ2a3g1USs2YTVaL3d5NkRBdXZjYnVQZzJVRHM4QWkwVUlQUDVCeDVBWGc4WFErYWtSeEdUeDF2ZTZVb0ZkdEpYZ2RiZS82WXIzbkpPZTdQTWwzMFhML085akJwQk55eWtyTmN5R2ZiU01VOVNHRUswTHM1QkhPS1dPNHhpLzlYeWVHVFNLa1VGelpmaDNxSHRROFN0UUJmcjVDWEcydVdUWFlrK1F3TlJyNW9udXdyK2RSNTlZVjRPc2MvRXY3YWFPaWNxVkw4aEpOTUtpUUlEUWhtVnhtTlFYODlUMFpkTllwdHg0N3hQK2RGcU5DWHI1MkVWdWVpRjE5L2dEUWpuQUoxakhaVkVqOWZVTGNsOGgxK2VHQjFkSElod3JmVGY1ak1Udk1VWFFaV1MiLCJtYWMiOiI5YTBkNjAyN2UyMmYxMmY3YjgyODNjMjhlODRlYTJkYjQwYzI0ZjQxMWYwMDY1ZWNkMmU3MDQxNTBiMDhmYmIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527860454\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2114673366 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114673366\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJtbktlOHlGczNMaEFQdDhWU0IxK2c9PSIsInZhbHVlIjoiZHZVQnpsWm5NSWgxTkFhWjd4NmRERCtDOFovZDYxNXlnMUtSWi9kNGhRYkJ4dGI1VUlnMjdRWEgzYXpJTU03N1NVaGU2WFV2M25XYVYzLzFrRkU2bkNySk92U2JQa2RuY0xvTGgvWlgyN1RWZ0hFWEZZL3pJUFUwcjlsaU01VmlxR0o2UzducGtDSXBycEk0aTdHc0hDZU16UjBpNWdZTlNQaXFJc1pRUGxrRFRoT3RyYWtqQTRXQkVwdW9iVWd1bExOYTRycnZyVEtLK2R4MExnMGpaMXlMWHNHTm50blZWUzBBQkpZVm9ta3VaNmxpSk1IWEIveTM0UWVTS0JmNFRacmMxK0N3bWRQNkNVVVFoOElrMkNJTUFIT05Eb0xZV1BOMC9NWGZlVFlTTDg3K1R1azVPY2UzeVVJSjdZVVBuR05YanJudTg1NHFPYldJblVTT0NXZ0JaQmdXVlV0THZjSlNuaDN4NFl0QkVwVThLR2Qrak43N3UxNVZ6eC9wSzZOM05pUmQrdEpYOG52czlnU2pyUXZJd2sybnBKZnJmZmkycm9aT1dIbzFSMURNM21vV1RRdm1Pa3AxUGppOG52MWYrcmNjMEh6TTFvNG5tMzRscG03OFlJemdvblhWTlBSQnBFZEJ2SUFmNTRId1R0QkxjM0lCbW1uWFg2N2UiLCJtYWMiOiI4ZjcxMDAxMTNmMmY4OGQ4NWRhMTFkYTMzMmVlMzM3OWNhOGQyOWYxMzE5MzRjZTIwMzk3MDhkZWI2M2VmZjFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNaOUhiOENyMmpDMzkwNW81SXR3eGc9PSIsInZhbHVlIjoibC9MRkdQVVIxWVRUTXNRUmVmbzVjTzRrSHNpeDd5Z0xxRExZYlowVSt6NXg5SGorbDM4UU1VOFNVK2dBVjU5S1BvblJlTGI2Y0MvQlF1Z0RRRFlpWGdFMnYzVFp5azJEM0IyTmVCNUk4ZXhWYVJxNXpIYWdKMnhSZW4vYlhKdUlqNGM4VkFmSm1PZEg2bWw4V0dMYlhEMVJjT0JtQ2RaZnZlTVA2NDNJZ0tSb3owdkhTWDhIcFRNWnErOS9ta05QdlcvNDFPNjJrR29vQk5jeEVxZ1kwSFI3aVMzTXVUc2hkdDFhcHJXd2NlMlFYc09FQjdrOFNZdU5TSzQ5d05JUTl1ODFrWFJlVjkwSDluWldwazN3dlFZVytWMitoZnkvdEl4UEN4d3dNWE5ncjVjMWpZdnEvQ3p1dWtjN3lsMlBCbHFWWXdNdFFtZkJZZTJyNWF4RWV5azVtVTlPOGNrdThlNWNCUjU1cTBBa2p1MmFyMUM3aTBUVFo2OFowWjZURHh3R2s5c1BsY0Y4SUNOS3FLbm9MUEVaQ0h5UjhMUFR1bDFGdVpnVVRkTXlhb28wRjNrQW9Ydk9pNVk5WFY0c29jdjNmU1lHdjQrUHZiRkdMb25hNFEydURyVHhEbDQ0R2kwSjRYR0ZicmFBdEE1RE13dGlXWkdhazgwQzBaV28iLCJtYWMiOiI3NzM4YmFlOGViZjg5ZTFmNjZhYTI5MDdlZDM2YWUwNzU2NTVhNzUxNzk2ZTU3ODdmMzAwNDJjMGUyOTY5NjdhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJtbktlOHlGczNMaEFQdDhWU0IxK2c9PSIsInZhbHVlIjoiZHZVQnpsWm5NSWgxTkFhWjd4NmRERCtDOFovZDYxNXlnMUtSWi9kNGhRYkJ4dGI1VUlnMjdRWEgzYXpJTU03N1NVaGU2WFV2M25XYVYzLzFrRkU2bkNySk92U2JQa2RuY0xvTGgvWlgyN1RWZ0hFWEZZL3pJUFUwcjlsaU01VmlxR0o2UzducGtDSXBycEk0aTdHc0hDZU16UjBpNWdZTlNQaXFJc1pRUGxrRFRoT3RyYWtqQTRXQkVwdW9iVWd1bExOYTRycnZyVEtLK2R4MExnMGpaMXlMWHNHTm50blZWUzBBQkpZVm9ta3VaNmxpSk1IWEIveTM0UWVTS0JmNFRacmMxK0N3bWRQNkNVVVFoOElrMkNJTUFIT05Eb0xZV1BOMC9NWGZlVFlTTDg3K1R1azVPY2UzeVVJSjdZVVBuR05YanJudTg1NHFPYldJblVTT0NXZ0JaQmdXVlV0THZjSlNuaDN4NFl0QkVwVThLR2Qrak43N3UxNVZ6eC9wSzZOM05pUmQrdEpYOG52czlnU2pyUXZJd2sybnBKZnJmZmkycm9aT1dIbzFSMURNM21vV1RRdm1Pa3AxUGppOG52MWYrcmNjMEh6TTFvNG5tMzRscG03OFlJemdvblhWTlBSQnBFZEJ2SUFmNTRId1R0QkxjM0lCbW1uWFg2N2UiLCJtYWMiOiI4ZjcxMDAxMTNmMmY4OGQ4NWRhMTFkYTMzMmVlMzM3OWNhOGQyOWYxMzE5MzRjZTIwMzk3MDhkZWI2M2VmZjFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNaOUhiOENyMmpDMzkwNW81SXR3eGc9PSIsInZhbHVlIjoibC9MRkdQVVIxWVRUTXNRUmVmbzVjTzRrSHNpeDd5Z0xxRExZYlowVSt6NXg5SGorbDM4UU1VOFNVK2dBVjU5S1BvblJlTGI2Y0MvQlF1Z0RRRFlpWGdFMnYzVFp5azJEM0IyTmVCNUk4ZXhWYVJxNXpIYWdKMnhSZW4vYlhKdUlqNGM4VkFmSm1PZEg2bWw4V0dMYlhEMVJjT0JtQ2RaZnZlTVA2NDNJZ0tSb3owdkhTWDhIcFRNWnErOS9ta05QdlcvNDFPNjJrR29vQk5jeEVxZ1kwSFI3aVMzTXVUc2hkdDFhcHJXd2NlMlFYc09FQjdrOFNZdU5TSzQ5d05JUTl1ODFrWFJlVjkwSDluWldwazN3dlFZVytWMitoZnkvdEl4UEN4d3dNWE5ncjVjMWpZdnEvQ3p1dWtjN3lsMlBCbHFWWXdNdFFtZkJZZTJyNWF4RWV5azVtVTlPOGNrdThlNWNCUjU1cTBBa2p1MmFyMUM3aTBUVFo2OFowWjZURHh3R2s5c1BsY0Y4SUNOS3FLbm9MUEVaQ0h5UjhMUFR1bDFGdVpnVVRkTXlhb28wRjNrQW9Ydk9pNVk5WFY0c29jdjNmU1lHdjQrUHZiRkdMb25hNFEydURyVHhEbDQ0R2kwSjRYR0ZicmFBdEE1RE13dGlXWkdhazgwQzBaV28iLCJtYWMiOiI3NzM4YmFlOGViZjg5ZTFmNjZhYTI5MDdlZDM2YWUwNzU2NTVhNzUxNzk2ZTU3ODdmMzAwNDJjMGUyOTY5NjdhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}