{"__meta": {"id": "X63f15f401ccffc660fd67015a7c89382", "datetime": "2025-06-08 12:55:39", "utime": **********.604567, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387338.190639, "end": **********.604615, "duration": 1.4139759540557861, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749387338.190639, "relative_start": 0, "end": **********.419603, "relative_end": **********.419603, "duration": 1.22896409034729, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.419632, "relative_start": 1.2289929389953613, "end": **********.60462, "relative_end": 5.0067901611328125e-06, "duration": 0.18498802185058594, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45038568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00741, "accumulated_duration_str": "7.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.522569, "duration": 0.00524, "duration_str": "5.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.715}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.556309, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.715, "width_percent": 13.63}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.577785, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.345, "width_percent": 15.655}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387279672%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9EUXVYcHR1cE5aT3V1UGZxVWlFbEE9PSIsInZhbHVlIjoiUmY0NGFDSmtZQWdPeGFpMVI2RE1RS2FzdTY5V0xuVXAwV0FDOTVmTU00NXRwL3Q1Q0kyVGVKMiszQnRZWFFtOHNhSk5tcTlxbDVrUDlkMWVDWTUzN3llRVdyL3MwU0NZN09VV3hueDV4OHdIckFzMnFZVTg3enZxbG9HbWpIZnNsMWRQMUZocnA5VXMwdXE1MTFTbGluSmFHNUZWMndJd20xMmYvWXhnRUkrbWJFbDhiQjNRaUUzVGdPMWEzT2cvNWlXYjNtSDQxM1RHT1ZTRnlYSmFJTUJQN3RUMXlTSExJeFN2TmpyRERoTTdnWlFQM2dWVUJpbi9rcHJPU2J4dm16Y1FyVExORUVXemk1Q0t2UUZoTzRXdHpzbmUwSEVreG5aQkhtcTl3ZXgrK0dnTEUvdklaRGRPWFozMDRJZlk2R3BiU1B6UEhHZDB0dS85V0MxZXpxMTZLc0JxQkRNTkdqcjNRUUtnaTl6Mjlia1EzV2QwOE9ibGIvOXdBeWNaQ00wTUVoRGRQR0tQNkhXTDRhbUZRZHBiSTVvaVdMT3IrdnA1SmRHRzcvQVBlNWlldTM1czZCOVJxNERoRnFUWG9DeXBVZGZiWTkvd0h2cXdhanVJMEM5NW5Ea2h3eUVvWWVwb09CaFdZWHhFcE1pYmt5akdLWit3STcrRCs1S1oiLCJtYWMiOiI2MWM4YTU1ZDRhYjI4ZjUyNzIyZTc5YjIzNzUzY2Y1ZWRiNTE5MTRlODQ0NWQ2NTQwYTIwNGZjNDIzNzdjZWE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktPTUZHRlVFWTlDd0VaUGFCNWhTeVE9PSIsInZhbHVlIjoiM3Ird3dVVkFEZWhodmNHdlY1LzEzdWNiY0NHL2o1VGx1NE5TcWxBVjFjRjlRTU9iVURIUlZwL1ZZMGp4RkZmMEtZMGgzRUZUMWFxSTZSbHo4NUlDUFp6SWNpbTZGY3ZYaHU0dzRPWnRmNlR5ZWxtRHFycnlWSkQ4L2RJMkZ5TTRsRUVpeUF2Q0N4S2xYeldaUHQzOUxFbTVQVU0rVVFWSWt5UGtBckRqdTdGRFZnWUc3bWY0Z3JXTDFSa2ZZNHF3SXAxdmh3RktCaFQvWTZGRXprNWVISVhmVmpEM2ViQ0t1YTN6UHZuMUdjUXNwOHFzaFNKUXRsQ0tDMnpmQUFTTkVlZjg1dFJucGRpMm4zK3lnWVpuZEJGbmJJeXA5Sm00VHRaTmhqNkIxQ213NkVoU0M3S0U1T1JGd1cwSTIwa3FJVDdQeVZWUjl0UjJNVGtCK01ZZEw5THpUYmZjSFl0STQyOXQwenJOcVRuSDBDL0JYZitFNjQya1hGbmR5QitIZzJYZVIzVm1samwxQncwUktQd1g4S2s0dzAvVTkrNGVabkhEUWNOOTRrNW5tUGtjNDhhV01zQ0tlS2QwSkczWk51dmMxVkdJMEQ0MFNLYnRPeEpld29XUWF0UitrRnExZ295am1qeTZCMFhMbGpPaU8zNVJ2cVcwcDZwb1FiYW8iLCJtYWMiOiJkYmEwMGYyYmRhZjM2MWQyMzA1Yjk5NGQ2ZTI5M2IwOTQxMmE3NzgxOWRhZWIzOGJiNjVmNjIxNTQzNjAzZmNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-739912980 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:55:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9MT0hGakFqa2RkVGRsbFhKTUN6aFE9PSIsInZhbHVlIjoiNUdOaUdPb1dzUTd4YkkzWkwwSmhoZXhmbTkya1hlUW8rOUY2Rkp6QVZzQXhHSWIxSlA3cUhrSVNNWFViKzBJSmduT0U2L1dnMVVkLys0ZGFuKzRBZDBvQlhzZDV2aWJ6T0FOUFdLN0duNWJ4UURnNWZVTVp4MTR5dWd2ZzhrdklDRHY3SWxhMnhMcGJkclQ4aEV6TlJmWHJIejJEVzdLbHgzQ0Y4TGdabkhLc3lZdTRoTlpGL3FhYUxERDFka0ZZaW8zbzNxMW5Fczk5Vmw1UE5PYW1JZy85UEhMQVdZOGJtVzgyUEY3d1VabzYxRjZWa3FQTEI1Q0pZdVRrdURQRjFBK0lsL1pYc0JBZ2l1V3NoQUpybTgyYURzcXdGNXZsVExpdnk4cEVUTUdwd0J5eHJYdy8wc1crZUdzQXFnTHNkUVNCS1pxb1VTTjQ1NU40OUlpeExFemxxK2x6NCtqYVhzNTkvY1N4Si82OFhBZGlvRTI3d3hTbWFpRnU3RkFLUU12dGRiazJkbzJVaGpEcTNtM3NPYUtrUlYyVmJ1eHJvZTl5dEdlcmxmWEtrSzRhb3hKSVE0NnNBQmtqTmtVc0RpQlEzOTRSbGtabi9qUktlcEd2dTRpOHJQNkJObGcwcW9qbVlnQnFIUTVFZ1AySjRNWU5ienVVOTAxSWtHdUUiLCJtYWMiOiI1YTgyZjVmYmE1YzA2YTIyNmExMzA3YjQzNmFhNTQ5YmYwNjQ5YzdmNWU5NGM3MThlMWQxOGJmYTRjM2FkYWQzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:55:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpIRzFid1ZPMTRHUFdnOGwxWW12YkE9PSIsInZhbHVlIjoiZC9WdVNLZlMvSXZuN3dBVFBuMHVwL3FYc1R4UGhadENmVW9oVG1ybEswb2JnbjJrK2xXUnJiZ1hsUmZMd0R4VUErNlhTOWRhRm1XSmhlN1UzRVl6bEhmTlQ5OEJhQjZUYmlWYjlxMC92Rlg4V1ZtckVOdVRQTGE2d0ZnYysyWSsyR2VXcWxNdHk3REZaMmpJK1YrWnlyajVqN2QzNTJvSDlWaHF5UlhmZk5Oa243QWxjTGNJUTJhajFieFZUcTRZRlRySU9YMVlnVkNBUUxNeXFUb29YK2xQanRxc3VsV1N3RXMxTFplbVJEKzdkWFBKd1VWWHBYMUYyc0NwMmdGV25McmMxbDhEeEVRK3c3ZG9UWlhPeGxPdVE5WTBPdERETUM0YWo5RVJLMnYreG5EU2hMUWRnLytCY2pWajFlRE9SS3JPc0p1OWNyN0RyakpyZFdBWE8xRVc0bDJ5WStoS1grYUFaNVFTT3RhcS92YTVYeG5acUNCVmxJWHRwVHpzL0Y0MmpRcWVDWDgxN2k3ZEJJdVN5ZkxsZGdPcW1QS3RNMnV2aHlGL0xSVng4UDhaSEpXYitQMDZDL1NtU0FMVE9kaWNac2k4UWpJMGZUaldJY3J0eENLeEtGSUY5ZVF2MUs4b2J6WWNRTUlCTjBkVTkzNERJM1JlRS9GY3NyZkYiLCJtYWMiOiI4YmZiZDhiMjI5NDQxMjE4NDIwZDVmNzhjMzRlMjM5OTczOTUxM2E2YTQ4MTA4NjdhYzMwNWI2NTU1NTExNmJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:55:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9MT0hGakFqa2RkVGRsbFhKTUN6aFE9PSIsInZhbHVlIjoiNUdOaUdPb1dzUTd4YkkzWkwwSmhoZXhmbTkya1hlUW8rOUY2Rkp6QVZzQXhHSWIxSlA3cUhrSVNNWFViKzBJSmduT0U2L1dnMVVkLys0ZGFuKzRBZDBvQlhzZDV2aWJ6T0FOUFdLN0duNWJ4UURnNWZVTVp4MTR5dWd2ZzhrdklDRHY3SWxhMnhMcGJkclQ4aEV6TlJmWHJIejJEVzdLbHgzQ0Y4TGdabkhLc3lZdTRoTlpGL3FhYUxERDFka0ZZaW8zbzNxMW5Fczk5Vmw1UE5PYW1JZy85UEhMQVdZOGJtVzgyUEY3d1VabzYxRjZWa3FQTEI1Q0pZdVRrdURQRjFBK0lsL1pYc0JBZ2l1V3NoQUpybTgyYURzcXdGNXZsVExpdnk4cEVUTUdwd0J5eHJYdy8wc1crZUdzQXFnTHNkUVNCS1pxb1VTTjQ1NU40OUlpeExFemxxK2x6NCtqYVhzNTkvY1N4Si82OFhBZGlvRTI3d3hTbWFpRnU3RkFLUU12dGRiazJkbzJVaGpEcTNtM3NPYUtrUlYyVmJ1eHJvZTl5dEdlcmxmWEtrSzRhb3hKSVE0NnNBQmtqTmtVc0RpQlEzOTRSbGtabi9qUktlcEd2dTRpOHJQNkJObGcwcW9qbVlnQnFIUTVFZ1AySjRNWU5ienVVOTAxSWtHdUUiLCJtYWMiOiI1YTgyZjVmYmE1YzA2YTIyNmExMzA3YjQzNmFhNTQ5YmYwNjQ5YzdmNWU5NGM3MThlMWQxOGJmYTRjM2FkYWQzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:55:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpIRzFid1ZPMTRHUFdnOGwxWW12YkE9PSIsInZhbHVlIjoiZC9WdVNLZlMvSXZuN3dBVFBuMHVwL3FYc1R4UGhadENmVW9oVG1ybEswb2JnbjJrK2xXUnJiZ1hsUmZMd0R4VUErNlhTOWRhRm1XSmhlN1UzRVl6bEhmTlQ5OEJhQjZUYmlWYjlxMC92Rlg4V1ZtckVOdVRQTGE2d0ZnYysyWSsyR2VXcWxNdHk3REZaMmpJK1YrWnlyajVqN2QzNTJvSDlWaHF5UlhmZk5Oa243QWxjTGNJUTJhajFieFZUcTRZRlRySU9YMVlnVkNBUUxNeXFUb29YK2xQanRxc3VsV1N3RXMxTFplbVJEKzdkWFBKd1VWWHBYMUYyc0NwMmdGV25McmMxbDhEeEVRK3c3ZG9UWlhPeGxPdVE5WTBPdERETUM0YWo5RVJLMnYreG5EU2hMUWRnLytCY2pWajFlRE9SS3JPc0p1OWNyN0RyakpyZFdBWE8xRVc0bDJ5WStoS1grYUFaNVFTT3RhcS92YTVYeG5acUNCVmxJWHRwVHpzL0Y0MmpRcWVDWDgxN2k3ZEJJdVN5ZkxsZGdPcW1QS3RNMnV2aHlGL0xSVng4UDhaSEpXYitQMDZDL1NtU0FMVE9kaWNac2k4UWpJMGZUaldJY3J0eENLeEtGSUY5ZVF2MUs4b2J6WWNRTUlCTjBkVTkzNERJM1JlRS9GY3NyZkYiLCJtYWMiOiI4YmZiZDhiMjI5NDQxMjE4NDIwZDVmNzhjMzRlMjM5OTczOTUxM2E2YTQ4MTA4NjdhYzMwNWI2NTU1NTExNmJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:55:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739912980\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2496219 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2496219\", {\"maxDepth\":0})</script>\n"}}