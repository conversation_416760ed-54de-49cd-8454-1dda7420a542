{"__meta": {"id": "X9f789e28ad1912594c1a83d14f9f8224", "datetime": "2025-06-08 12:59:55", "utime": 1749387595.056371, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387593.681557, "end": 1749387595.056407, "duration": 1.3748500347137451, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749387593.681557, "relative_start": 0, "end": **********.875164, "relative_end": **********.875164, "duration": 1.1936070919036865, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875183, "relative_start": 1.1936261653900146, "end": 1749387595.056411, "relative_end": 4.0531158447265625e-06, "duration": 0.1812279224395752, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01587, "accumulated_duration_str": "15.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.960688, "duration": 0.00451, "duration_str": "4.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 28.418}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.997957, "duration": 0.01001, "duration_str": "10.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 28.418, "width_percent": 63.075}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749387595.028969, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.493, "width_percent": 8.507}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2124683923 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387586430%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJ6bTVhMUlWN00xbEVINUhNU1k4cHc9PSIsInZhbHVlIjoiNDhxTkgwMmQvd0pBMisycGxXTFdITEt4b1VyUUhEWG95WndoWmNRck52allveTczU1FvMGN4ZzRROXRhMkprMkFDZzZVY1lBQWhwSWwvWU1pbVJyTGxEQjU1OFJvYXpza2djaFNuSlhyeEVGOEh6VVNxbElQZkdQb2tlRk14N01IS0c3bngxVE1qSEo4eElhZDBUUkE2M0xzVEVldGh1TjloVFQyWktEeVcwNWM0Y0h5NW9vcTJrUDhYZ1lnQU8rQWRmQnZXSDdkOUIwTlpuQWhxQVg3aFpUYTdhQ3o2NndFbHdLd3hTcUdwOCszV1V3VTZVZ3MvYytzVU9PZjBPdlNPS0VlQ3BjRk1DMHVFR2Q0enRZWUk1aUpTRXZUQWd5WmVpQW5mZHdCdG4yOXppQWlNNVRlc3NBVXNMVStsWDB4Vjc4aVFyOVd2eWZLTFAvUitaUWV4SE04TitPT0sveVZVbDVRb241d1luUktUd2tUZjNDQzFZUFZpTU82QmptMUREeTdTUkI3NzRoZC9VTjdBa3p0ODQvK1Z3aTVTOTV5TUg4YzhWeXUzUklLWlZKc3ltZTBWdXphcmNEclNEeFkwTFdWdXpzL2E2ZjVVazF2WVUxRWJUdmpyZE1HcHZPQ0ZpYmlBTlpEbzdoeGR1ODZaenc0Y0orM3FKS2J0VGgiLCJtYWMiOiI4ZWFlYjRlZjA2ZmQ5MjRiYjExOGI3N2Y0OWI3ZDQwZGIwMTNhNTExZmNmZDg0YWRkYzE1NWU3MjA4MDgyMGM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InErWnlZUzhHOEJjS0ZKSnVBeVpJZVE9PSIsInZhbHVlIjoiK3pQSTNZVVJ6K1hIMmJwU1F3MGRSaDMyTmptakVhb3VOSlZBdklpVWJNT29EUHJxdVVzcFBoMUZkaG96V243UW9YbUZoWWhxQndHK0Mra3pvMWxLRWxkQ0pWMnl2VDJNL0l1Z1dBSlFPeWtMSnhHVjZUNEl1NnlOdkhTWUV3VnRiU21UaC9yaUMrNmJObXFIUzgrM0dmSDg1NHNkSDVvdWVoRWhuSXZSOGxEcU43bVU5bklKcFBEVzlqaUdJWWRqeDBEazNIZjJNRXlaaEFWWDZIQ3FuTTYwSXZrWVRvR2J3SFhNMkpiMG9kNGxlMGhGa2hnZHJKNkZYMTdpbWVzcHg1aml2SjhtQVNFQzQzU0hENHNtWWZvUS9TQkVRclNFWlVOK1cyRVdGTHJqd01GNXFLd3NiMzV0bER0T0ZtK243cE0vSTF3d2pjaUFBdHV6RUxQQU1OMEw2OEdXaXRqMkliYTAycUF2N0pVK0hBY3doa0UvSXFGRVRLbDQ3WlhhUGFVMGUxSUgvcUxwTFQzMS9TaC9Pc0tSZ3hQanExVWcxZWZvZ29GYnhLdHdDZ3dtRUQxTWFYNXJpbi83NXFGcGVnS2w4dEp4S3ZYRjhYV2xtOXI2ZTJtbEtGVmVhQlA4YWtEN0NubVFTOE8ydnlYaTd5NnQrOWdHeGFuUmlySVkiLCJtYWMiOiJkZjdmMGE4ZjA2ZWVhOTEyMDY2NjI3MWM2OWE5MTJjZGY1YjUzZmRlMmEyNzcxNWRkNjU2YTIyNGYzYzFjYjRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124683923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-159065045 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159065045\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-277653767 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:59:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inl5V0hQTkxQMXNIdUMycTRrVUR2WXc9PSIsInZhbHVlIjoidVorZ3oraC9GVEJ5a0hVdFlYTEFjbnBxZWNuMEVZV3RKWksxdWo3M0puaTlOTFJnVHhYQnQyL3liMXpjRnVPelRQQ0hKY29VYnJsR1FMYmFiKy8wbUZpMDFqWXh2UmVhK0lzd1RrZEdxZ21JTzliQnRjRWNmMFpLZzdYZ0dDeHZZa3JySjBkd0JMcTlDSjNqYUpyQ2lXbmw3RlNoNTJuVEs1VTVPRml4SUpZK1lHbloydnFwT3BTQzFONUVxNlMxVVRrSHo4bU9Scjl5cWY5dDh3Z3ZnTlF5UmNtZk5GSHlMTFJRdUo5NEVrLythMm9YVDdYSm4zTW5rNy8ycnhoS2gzMllqQzhiZXR6WHoxdG81cWtmeE4xMGVRMXhMMG1pVXhHcmhpenhYaEFrRnVHTmphRVNObFVGYllzQWY5Y3gxM05lQWxZM1JlcUhJdFUwSFh5aFIybGZNRjdmQXVXOVRjUmhReFJlejNQTm1ubERMK1pFaXRNMmJsZzBiZnU0QTRrcWRjaFZCS3hhd1RwRDRDcWt1VWxXQkF1V1JSNSsyc3FwOWRuZ2JBeEx4YXFsUFA4R01ZSlB0aTN2WVM5dkFtM2U1WFh4MmJBKzVnd3FGaVY4Yi9mSzFQbldRZmcveUlqV2FvWG8ycmFtUnVmVlQ2RmduazNiaks4WW1aTDMiLCJtYWMiOiJiYmQwYWVmZGNhN2RkNzljMjZkMWVmMTZjZmJlNmRjZjA2MzdkYTM2YTBiODlhZDlhNmY5ZmNjOGQ5YjBiNDVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:59:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlnTFdJZlZjY3lnMEl4dHp4T3c5dHc9PSIsInZhbHVlIjoic0N4VWNQTzVhZ084SStlRktSUytBMTdyOXNtdnlsV3g3N0ZUNkZQYmVSNHFGS2Nmb0N1Q0pyTUVwWGNBUXNUdU5WNEJPMVJBTHoxL1J4c0duSjVaNmNJT0M0UjNKbm9NRFMwT3FpbkY5NmVCeWU1OHNHK2FvcWRkaEQ4cU1lK3k1NUU0azYyK3RCSmlVWWdNTUtjTE9hMEI0ZzdIdUN5VHRlK0RxQkxRWm9zSGp2TThIM2VxYjNHWWQzbE9DdmQrTmduOWxXNlNFZUZjSEF4RVBTYk1BZnZjTGIrTTN3ZW1sZGQwbi9GWkprK0dPek42YWxvNDdkZXdlQ1pJZzlIZktrV2dmMGhjT2ZsSVliN0VIZDNURzhnTlU4NDRCOVBDK1pmNWpFdVBDTzlFMmpGSUYrMGVYTkxIekRDU0F0NURQL3FkRlBiZmxBalJKSmF4MVpyTlluaTVYWnRmQ1YzYm5uT0hVbGh4Z3dkVmJLcklheXpYNjI5UkVYRlJFQUZIc3V4MCtpWVJpVFpOTFA5ZHdrSVNMdzYyR25uOUtmY0dqL2hzVGlOeXAzaWdFbTZqdStIMzBoRUE5WWVnV3ZaUjlwWWxxTU16Y0FzWWlNaEZiNFprcjFEWWlsUUcrMlRXRUFwWDZnM0htOUdpMWdTMVJVYnZNTmVSdlZLRkM0YlkiLCJtYWMiOiJmMTk2YWIwNWEyN2VlNGFmMjkzZjhmYzYzZDI5NjFmMmNjYTllNWJkOTA1MGIyMThhNDgxNzRiNjA4NjMzZmQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:59:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inl5V0hQTkxQMXNIdUMycTRrVUR2WXc9PSIsInZhbHVlIjoidVorZ3oraC9GVEJ5a0hVdFlYTEFjbnBxZWNuMEVZV3RKWksxdWo3M0puaTlOTFJnVHhYQnQyL3liMXpjRnVPelRQQ0hKY29VYnJsR1FMYmFiKy8wbUZpMDFqWXh2UmVhK0lzd1RrZEdxZ21JTzliQnRjRWNmMFpLZzdYZ0dDeHZZa3JySjBkd0JMcTlDSjNqYUpyQ2lXbmw3RlNoNTJuVEs1VTVPRml4SUpZK1lHbloydnFwT3BTQzFONUVxNlMxVVRrSHo4bU9Scjl5cWY5dDh3Z3ZnTlF5UmNtZk5GSHlMTFJRdUo5NEVrLythMm9YVDdYSm4zTW5rNy8ycnhoS2gzMllqQzhiZXR6WHoxdG81cWtmeE4xMGVRMXhMMG1pVXhHcmhpenhYaEFrRnVHTmphRVNObFVGYllzQWY5Y3gxM05lQWxZM1JlcUhJdFUwSFh5aFIybGZNRjdmQXVXOVRjUmhReFJlejNQTm1ubERMK1pFaXRNMmJsZzBiZnU0QTRrcWRjaFZCS3hhd1RwRDRDcWt1VWxXQkF1V1JSNSsyc3FwOWRuZ2JBeEx4YXFsUFA4R01ZSlB0aTN2WVM5dkFtM2U1WFh4MmJBKzVnd3FGaVY4Yi9mSzFQbldRZmcveUlqV2FvWG8ycmFtUnVmVlQ2RmduazNiaks4WW1aTDMiLCJtYWMiOiJiYmQwYWVmZGNhN2RkNzljMjZkMWVmMTZjZmJlNmRjZjA2MzdkYTM2YTBiODlhZDlhNmY5ZmNjOGQ5YjBiNDVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:59:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlnTFdJZlZjY3lnMEl4dHp4T3c5dHc9PSIsInZhbHVlIjoic0N4VWNQTzVhZ084SStlRktSUytBMTdyOXNtdnlsV3g3N0ZUNkZQYmVSNHFGS2Nmb0N1Q0pyTUVwWGNBUXNUdU5WNEJPMVJBTHoxL1J4c0duSjVaNmNJT0M0UjNKbm9NRFMwT3FpbkY5NmVCeWU1OHNHK2FvcWRkaEQ4cU1lK3k1NUU0azYyK3RCSmlVWWdNTUtjTE9hMEI0ZzdIdUN5VHRlK0RxQkxRWm9zSGp2TThIM2VxYjNHWWQzbE9DdmQrTmduOWxXNlNFZUZjSEF4RVBTYk1BZnZjTGIrTTN3ZW1sZGQwbi9GWkprK0dPek42YWxvNDdkZXdlQ1pJZzlIZktrV2dmMGhjT2ZsSVliN0VIZDNURzhnTlU4NDRCOVBDK1pmNWpFdVBDTzlFMmpGSUYrMGVYTkxIekRDU0F0NURQL3FkRlBiZmxBalJKSmF4MVpyTlluaTVYWnRmQ1YzYm5uT0hVbGh4Z3dkVmJLcklheXpYNjI5UkVYRlJFQUZIc3V4MCtpWVJpVFpOTFA5ZHdrSVNMdzYyR25uOUtmY0dqL2hzVGlOeXAzaWdFbTZqdStIMzBoRUE5WWVnV3ZaUjlwWWxxTU16Y0FzWWlNaEZiNFprcjFEWWlsUUcrMlRXRUFwWDZnM0htOUdpMWdTMVJVYnZNTmVSdlZLRkM0YlkiLCJtYWMiOiJmMTk2YWIwNWEyN2VlNGFmMjkzZjhmYzYzZDI5NjFmMmNjYTllNWJkOTA1MGIyMThhNDgxNzRiNjA4NjMzZmQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:59:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277653767\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-10******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10********\", {\"maxDepth\":0})</script>\n"}}