{"__meta": {"id": "Xe7343ad48fb4c53f6f0332d4e4295b21", "datetime": "2025-06-08 13:01:14", "utime": **********.294247, "method": "GET", "uri": "/receipt-order-warehouse-products?warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387672.83564, "end": **********.294292, "duration": 1.4586520195007324, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749387672.83564, "relative_start": 0, "end": **********.035229, "relative_end": **********.035229, "duration": 1.1995890140533447, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.035256, "relative_start": 1.1996159553527832, "end": **********.294297, "relative_end": 5.0067901611328125e-06, "duration": 0.25904107093811035, "duration_str": "259ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46009488, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET receipt-order-warehouse-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptOrderController@getWarehouseProducts", "namespace": null, "prefix": "", "where": [], "as": "receipt.order.warehouse.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=106\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:106-144</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.020989999999999998, "accumulated_duration_str": "20.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.180219, "duration": 0.013359999999999999, "duration_str": "13.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.649}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.227037, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.649, "width_percent": 8.528}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2394109, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:121", "source": "app/Http/Controllers/ReceiptOrderController.php:121", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=121", "ajax": false, "filename": "ReceiptOrderController.php", "line": "121"}, "connection": "ty", "start_percent": 72.177, "width_percent": 9.576}, {"sql": "select * from `product_services` where `product_services`.`id` in (3, 5)", "type": "query", "params": [], "bindings": ["3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.259421, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:121", "source": "app/Http/Controllers/ReceiptOrderController.php:121", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=121", "ajax": false, "filename": "ReceiptOrderController.php", "line": "121"}, "connection": "ty", "start_percent": 81.753, "width_percent": 10.815}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.267448, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:125", "source": "app/Http/Controllers/ReceiptOrderController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=125", "ajax": false, "filename": "ReceiptOrderController.php", "line": "125"}, "connection": "ty", "start_percent": 92.568, "width_percent": 7.432}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/receipt-order-warehouse-products", "status_code": "<pre class=sf-dump id=sf-dump-1404501637 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1404501637\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1034587405 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034587405\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1445475777 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1445475777\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387646954%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im16SFZhcmc4RU04SzNrOS9rM29laWc9PSIsInZhbHVlIjoidjhraktzL0dKV3g1ak9KY0NNTjNZZkJQamk1VnFkdTJWMU9SUURMWFNGOHBHMHlBcjBvWG02aUtjdXQ1SDRUYkltNE5FZjEyMDdEMWRVcmNPeE83Sk8rTDcxN24rbTNZb0xZT3RnSnNGQnc3MEpydHBzOElXdHhwdEJ3SnlwaWlxN296cXdGSzQ4VkpjVFRhNXhEemJWZFFMSzBBelZoVyttbEtRVU9SVmJGM1F0b3ZSNXRndHlHcVFBY2d1VW1MVk1OcmNBQXhDSW1abEVaMkNhNGFqWGgvdGxzbHM0MnZwV0JKc0lQL1gzS2FabFI4M3Q3d0NkeldjWmxpQlZIbzJiM2YvbVdUUml2Y01aNVkxZjdQYXpvNC83RnV2bWgwMWJGNThzcStCRzRnOUJObmVaQStTaVpRaUtMOWNMTEQyUlVHQlAraDV2VXg1NVpzQ3d2eEJhKzdNWkU3QXF6Qy9GdXFwNHVtcFNPbmlKUUgwOEVESVg2M0k5SGlhVmVDbjkyYXlGTkVvR0Y5WWlFblBOaTc5RnM0UG1jS0h6SnpQUnJsOGhPYUNYOUlMbHA3S1dYdnk2eCtraFZnWFExaVEzb0hYMHpzTi9oajIzemF0R1FPR3JHUjFsTDZxQjhodW1LQlBGTjJTSXdMNmtUWkRvTFg4YlJSZi9YbDVEZE8iLCJtYWMiOiI2NDY2OWI0ZTdkOTgyZjlkNWQ4ZjVjYjU5MjFhMDMzY2RmNDM1NjYxNzQ0YmI4ZTkyYzJiN2ZlM2Y0Y2I4MTBhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im1hQkp3QlBXWEw3b3hoRHNIYmpJSWc9PSIsInZhbHVlIjoiVjlVS3hWOEo2NW9RMnp5YklDNDRuWE80R01rb2x0OG1WR3ZmeG91aXNKK0ZsR2xIdHlFYW5kTzRYcnlSaElxQ3h4dkdEK0Zobm5VOUgyMWduZHNaQm5wNzFhR250RzZLbDNRRTNYK2lCVUxWSlgzMUd5YUUrN1R3SW50K0cwOEU3MnpDZ0xMMkhQellmcWRJNUxiWlkwMis5TUEyRGxQWkNFTE1pTUc4aWhiMEhZRVMzZVhsMTNSdDl4V3RMOHpTQ0lvekJ4am0wek1xcGJJM2JvZUtuVXd0Ujd5OVliSHBqblpGRUV3TkhoZktReDBRTWtPdW0xSUplUkY2ZjlVQVFtYjgzWTVxWGl0MVk2b1RUMGpyelNnUVBmaGlITnhsS1BOOUhwWmJvTG1VNlhOWC9Yakp2QWVCOEwrL09yMkV4eUVuV25FNTJwMFN1YStlWnpKdTBoYnpDNDJhVFhsZnFCdWRRcVEySm93MTBjb2FxRm50VGJyUlVRck1zY2ZxNFU4NVc1bjU2ZHpnbjhGVkpCSFhLc09VWE1kYjJwbDhIUGxKaE93alFVT2JMMVdnbG0razZQcHA5QUJSNGovSXFzYlpNOEdXUklpRnZXNUZ6VVM1WFFqakc1WmpqRU9keDRxamJnRm9tOFQ4bCs0NnFNSUxqQU82b2UvbmdwUVQiLCJtYWMiOiI4ZWJiYTAzZDdhYTg5YjExYzk3M2UxMDk3NmVkY2M5OTVmY2U5Y2Q3MGRlMjk5NWNlN2M2Mjk3NzkxZjZlYjlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-908816348 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:01:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJEUHhYbjYrY3JyOXZibldlZzBIYXc9PSIsInZhbHVlIjoid2lQN0MzYXRrcU0wME84SjZSMHNvdVZTeExYVCsyY1dPVVN0TTIvYksyZ1BnRm1KZlhRSmVFdUJydEJNRTRCNk1TbDM5dE1SUE9jUi9vaWVXTjM2TERoMFpDL0RpNlZlVWJQN1pJMWY2ZkVQcWI0ZDZkWHFTcWFmVXdkcjNIN0l3L1lWeVJ4ZDVvQTluRmhkZExwNkRUbCtWeGM3ZU1RVUZvaHVnZ05wUFVPM1RhL3hBWllPR1ROSUxZejFhUllZczdDMDZCcGJuVTdqb1ZkRExjMjloN1hvYUhYU1llb0M0R3JENDRhQVR1YjVtMmsva09aTGlUeU5qcHd3MkhBOTRHcmRJRkxWSzJEZTNhUndoek5LdThrOHZFZTJRejZkU2p6QitjN3NicjNPdjRuZ2RuVlFYamI3RXorNU5CenoxZlNTUkZpNzBickpZZFdiQ0xJZmlJWERjNktDS3lIV1N1K0pSZmYrRE9BeEx5cUhCRTh6STNxVjZiLzBEbXUrMFV5TGpycHRGaE5rcitmT0NTajd3aTRnZjU4MENCdVJsUUZpM3ppUlBuTVVYL3NpWVZBWHpycURZZXBOdG5Dc0NyQWVvOU1CbENoS09HeHNWaVJOTWxKRDh6TUJQTTUyN0JVYTV6UnVOZ3A4WDgyYUdXdjVTRDBrakM4WmFza1EiLCJtYWMiOiJiZjJjZGU2ZjQ2MTdhMzg1N2NlMjkxNThjN2ZjMGIzZDc3OTUxMTQwNjFjM2NiMGYyMmJkODc1ZGZlZDM4NWVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:01:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZyYTBjVk1jTU54NWZnMVVKdmNsUWc9PSIsInZhbHVlIjoiWmY3MDVQaGRnTURyc0VpM0t6WVF0UExRQXhiQUJLbHpMWlhzQzhaSk14NEx3T2xWMGJObkVhQXRIclRsVlM5VjR2YVd1Q3c2elR4TXZDL3FJS2Z3aEZxTWhoWGE1UEdYVG1CeEgvWVBWeVNQQVNVeHU0bjROV1F3UXIwL1dvR1greEkxb25vREtGN3RFODJVYkxHZkxENlFZUU9hNFd4UlVEMGhPbS8vTzFTcWVkdS92Vk9hdjVnd09yTjRTbUxwd3g5d1hXSG5ENjZsL3BGNTc3bzlNaFUzbkQ0RVd1Z2YxYTVJbXE3enE0OVlvWGZSWitWZmRyclRLbkJVdFRhcm9vYWhIL2Vxay9wNTAvN1FWRXNYcEJJSHo1aGkza1N0UEltUjUwNEh2dmo0dC9DNGZGbWhMUmgzbzJ3VTBaRFc1bld4ODJ4UXozOVRVZzNzdjZBR1VwMU9lN0p3TEJDVXFZT3YvallTdjNrcXhXY0poZCtyc09NYjBkSXFXTVlKOHdTYnBhZXRsWVBNRFEycGhvVisydXI4NklGeDBsZHJ1alFpWHdKamNTTkNCLzk0VWhUNGcvQmJRWnZJNW9xamNVK0tWMDdzblZLVmNMZGR4SldXZlpKZ1VCbkV4YTlKdGRSeUl0Sy80dG5icW5iT09YdGNSWHV0aFlIWkJ5OTQiLCJtYWMiOiI4MjliYjYyZWIzMTI0ZWMwOTg0MmZhOTYyMDc0ODAzODdjYmE0MWU4MjRjNjI3MTAzNjE5YjZkOWY5NDA5MTkxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:01:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJEUHhYbjYrY3JyOXZibldlZzBIYXc9PSIsInZhbHVlIjoid2lQN0MzYXRrcU0wME84SjZSMHNvdVZTeExYVCsyY1dPVVN0TTIvYksyZ1BnRm1KZlhRSmVFdUJydEJNRTRCNk1TbDM5dE1SUE9jUi9vaWVXTjM2TERoMFpDL0RpNlZlVWJQN1pJMWY2ZkVQcWI0ZDZkWHFTcWFmVXdkcjNIN0l3L1lWeVJ4ZDVvQTluRmhkZExwNkRUbCtWeGM3ZU1RVUZvaHVnZ05wUFVPM1RhL3hBWllPR1ROSUxZejFhUllZczdDMDZCcGJuVTdqb1ZkRExjMjloN1hvYUhYU1llb0M0R3JENDRhQVR1YjVtMmsva09aTGlUeU5qcHd3MkhBOTRHcmRJRkxWSzJEZTNhUndoek5LdThrOHZFZTJRejZkU2p6QitjN3NicjNPdjRuZ2RuVlFYamI3RXorNU5CenoxZlNTUkZpNzBickpZZFdiQ0xJZmlJWERjNktDS3lIV1N1K0pSZmYrRE9BeEx5cUhCRTh6STNxVjZiLzBEbXUrMFV5TGpycHRGaE5rcitmT0NTajd3aTRnZjU4MENCdVJsUUZpM3ppUlBuTVVYL3NpWVZBWHpycURZZXBOdG5Dc0NyQWVvOU1CbENoS09HeHNWaVJOTWxKRDh6TUJQTTUyN0JVYTV6UnVOZ3A4WDgyYUdXdjVTRDBrakM4WmFza1EiLCJtYWMiOiJiZjJjZGU2ZjQ2MTdhMzg1N2NlMjkxNThjN2ZjMGIzZDc3OTUxMTQwNjFjM2NiMGYyMmJkODc1ZGZlZDM4NWVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:01:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZyYTBjVk1jTU54NWZnMVVKdmNsUWc9PSIsInZhbHVlIjoiWmY3MDVQaGRnTURyc0VpM0t6WVF0UExRQXhiQUJLbHpMWlhzQzhaSk14NEx3T2xWMGJObkVhQXRIclRsVlM5VjR2YVd1Q3c2elR4TXZDL3FJS2Z3aEZxTWhoWGE1UEdYVG1CeEgvWVBWeVNQQVNVeHU0bjROV1F3UXIwL1dvR1greEkxb25vREtGN3RFODJVYkxHZkxENlFZUU9hNFd4UlVEMGhPbS8vTzFTcWVkdS92Vk9hdjVnd09yTjRTbUxwd3g5d1hXSG5ENjZsL3BGNTc3bzlNaFUzbkQ0RVd1Z2YxYTVJbXE3enE0OVlvWGZSWitWZmRyclRLbkJVdFRhcm9vYWhIL2Vxay9wNTAvN1FWRXNYcEJJSHo1aGkza1N0UEltUjUwNEh2dmo0dC9DNGZGbWhMUmgzbzJ3VTBaRFc1bld4ODJ4UXozOVRVZzNzdjZBR1VwMU9lN0p3TEJDVXFZT3YvallTdjNrcXhXY0poZCtyc09NYjBkSXFXTVlKOHdTYnBhZXRsWVBNRFEycGhvVisydXI4NklGeDBsZHJ1alFpWHdKamNTTkNCLzk0VWhUNGcvQmJRWnZJNW9xamNVK0tWMDdzblZLVmNMZGR4SldXZlpKZ1VCbkV4YTlKdGRSeUl0Sy80dG5icW5iT09YdGNSWHV0aFlIWkJ5OTQiLCJtYWMiOiI4MjliYjYyZWIzMTI0ZWMwOTg0MmZhOTYyMDc0ODAzODdjYmE0MWU4MjRjNjI3MTAzNjE5YjZkOWY5NDA5MTkxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:01:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908816348\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1837706158 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837706158\", {\"maxDepth\":0})</script>\n"}}