{"__meta": {"id": "X5ac165c1bc320913bbdd4f90df279d70", "datetime": "2025-06-08 12:58:17", "utime": **********.820419, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387496.508556, "end": **********.82046, "duration": 1.3119041919708252, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749387496.508556, "relative_start": 0, "end": **********.636929, "relative_end": **********.636929, "duration": 1.128373146057129, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.636947, "relative_start": 1.1283910274505615, "end": **********.820465, "relative_end": 5.0067901611328125e-06, "duration": 0.1835181713104248, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45592296, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02144, "accumulated_duration_str": "21.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.723285, "duration": 0.01932, "duration_str": "19.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.112}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.769866, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.112, "width_percent": 5.131}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.789509, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.243, "width_percent": 4.757}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1035871958 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1035871958\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-989961069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-989961069\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-197717514 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197717514\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-541084342 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387487766%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpHbVJMaVZudHk0MW5PeXpJL0NaMlE9PSIsInZhbHVlIjoiQjUxclpEeDhpQWwxeWdCNUlHTWVQMTJ4S3pNNi9UZlZjRHQvOGtYZ2RpcWwwSzRYNHpvV0FZR0RIcUxaaWkzSDZySDdaR0lkeDhzM1hTQi9VSjlTWExlUHJRaDhjQytsOTBaMmVWOWt2dkRxNWt3aFBQZG45MDdtR2dxRCtUVWFpckZ3ckpZbFBKMFExc3Zzdm5mbHU5K1FjSDJLM1Ridy9kNXdoNTErcWYwUDBESGw2S2swQXZULzljYktsUDhQejZJcXFTZjJhK21DcVJQRmlLNWZlWEp1ZWFieGJjUzBZajV4TFdjeWdYWHk4TzBSdTRFaXBZV0pETHpvNU5PMW92NHlldTNrN3lCUUZ0aFg0ZEQ1NU5YZFdSaFRsN2lIaEtlV2IwUUdLMmErU1NLRVNPKzB3dHlDQnlBOGxiQ0FpYkxkT1BtRUlHOXhlRGNHdS9EcnJ1TWNQYnZvTnR6ZnplWHliYjA4NEQ0NXZncE40K2s4dWhMNDlPUWpJSGd4ckFYeW1oU0Qvd09xMkdZWGZsTk14c3k2SmhJZ29RRnN6MWJTVjRwRGZoVllOakVRaFBObGpzVkdmSmhoSjJMZ3ZNT1dnY0JmaXo5aGoxU3FzWnJyUEcwR0YreGt3Z3g1ZjhUQUlVSVYrSDhSa1h0VkxWTEdSY0tOVHNkMnVES2giLCJtYWMiOiI0ZTM5NTdiZDNkYTBjOWZiNWU0YmE1MmVmYmU1ZjFhNmMxNGNlMDZiZmQ1NTQxZjNlMWNlNDExY2Y5MjZhZDRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpOSnd2VU5hTEwydEJ5TDFnaXJKa1E9PSIsInZhbHVlIjoiMm9OK0ZxeVl2R25IbHZ4N0w3cTR2MXNZc0pDamlzWFVuOVN5T3NsdEczTDczYUJQeTNSMS9jeHNJZTNxM2x1TllQaWhsaFE3Q29pNmx3eHF4aGovWUh5akJIVDNkc0ZOVWc3ZWFKQ1ZzR3NndHdpZEx6cHp6by81M1BRSnF0d2dKbGMwNFgrQzdOdUp2b2NSMlN4WUtMVjVUb0hLcjVnQW1nSXNxNndlbUJaVnV4RVBIYk9FQTE4bXgwL05EdmRxU3pML09rU0Z6Y05Dc0UzelJ2WGxqb21Ea082WWpsVkZDZ1hNN0RUMWI3UVBFanJhZUtybXY0OVZZQ1NqdlFlODRPMEJDQjhRbDEvekRUUnM3Q1hpekFMZWpDZ0xEZ2pFc1Z4czNjRXBYNlhLZGZ4ckpxQ0ZsQ0ZhSjlmRUYvUWxtK0RGT0x0MktuQUtHRGVnTnBRRXdzMVlsNFowSjYrbDRqdHBGVUVUV3NOR09lT2JESVMxL3dTZE1Cckw2eElqbkRFMWwyYmZpV2JrNzl5eGxFYzBZMDFIenVQblNrMUk3Q0NtUmkrM28rNDN1K0ZvV095MzhOdWhwdEpuZkVKek1PbzBXSnR1aHplWUVrWG5HQmJVZmtQWG5wbHNRd0k5dHo3dzN0MEFLbDAyVGNVV1Q0K3pKMXhJV0JCTW1oWTEiLCJtYWMiOiIxYjViNjlmNzZlOWJiMDJhZGU4NThjYjliMmUwYjM3NjBhMjg2YzIwZGY4NTNiYjdiOTBhZDQzYjIyMDRlMGIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541084342\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-961257661 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961257661\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-83145441 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:58:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVENy9IWVA3OHprcHJ2UDRUNWhlMlE9PSIsInZhbHVlIjoiSm5VdTNYM0RZSkRYcjJnaXBPdUhXNGFCUWgxOW5PLytKa0VtSXE0UGZuS3FoMUY4MEFhUjh5UVJqSC9ydHVuYnVJSTR0bXUzRXdQV3MwVlh4bVgydWVHWEVjVVhZTlpibUR3dE85bmo1VU1hT2dYaUlXZEdwTkhsN2VmVmFrbUpwL3QvcllCVmNaVUR4YmZJbHU1RlZHd2dPTGl2cVZRRnpXZTd1enFMdUQyRHNrc0tBMDI1cDluSkxBTDBQa3JNbmdkR2llbm5CVFdlTHpjWEF6VGIycGhSRDNtTUNnQWUwaE9RY3J2bGh5eGk1ZUVjYmFtcTRodEd3Z0dzTE9KNU1Cd2lPS3FqaXhjWCtJay9HWk1qdHl4UFRNNkhWQ1k0YVdZS3hFWDdyR3RXNk9sNFMxTUVDQzlZRHVDcnFCSDFlYTVtOFM1RUxsbHhWa3NmdFNXbXFTRzFXNGEvaEI4YWxEWCtNYmlJZ25PZzQwcU1ET3hQN1A3MkhHaldzdVM1MGp3VHE4UUpNYnBHMFNYNzh4VkdSRTR1M2h1QjZXQjFCQmczUXpJVTh6RGhWM2ozV3J0QWR4ZjJ2L3QxRnlMR3k0WHRYL0JnUXlCZElBTnd0OFhtb1RSMjNKNGNoN2pWNHU1YTd1OFFoMXFZYUhHRHNtd2g3UjY5a0hOWG0xangiLCJtYWMiOiI0MmM4MDI1NGZmZDBhNTVlNGM2NTlmMGEzZmJkOWRmY2M4NWEwZTg1MzkyOWM5MzMxZjQ0Y2RjMjA5OWFkYWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik4zL0FON2p2UU53VEdlUDRod1M5Mmc9PSIsInZhbHVlIjoiSlpUdSthQXpJK203RkFqZHZlcVZGTmNtd2w3eHZmY2NIZkw0ZDJxU216RDVIcStDMW12SkpTc2hQLytNdkFvTE1Jc3VRMGx6MlBQTGtOd1pFY0FCV0FJMHcyemd4M3ZqOVhUSHZFakhsRHo4Q2hmMzl6ODNkcDBZbVd2bENZVHBpeDhHenpLVGZ3WjdyNGZmSTVjUy9NOGtrQ1BBaU5YMjlRWVRCYlNaSWlJaS9QWXVmV0d2eWZ3UHFXeTYxbjRWc1ZSQ3U2R3dUTFY0NTl4bnppeStFVHhWbDEyUjJ1Qy9LOVJZOEZ1dHlwYVEzY1QxQU4reDlCSjJEREwyY0NkZk5RVFFUN2oyc0tZUVRYbU1VVGtvMnljaDJiMGtJaDJ3QVMyWGZFQ045VEJHUHBjUjJLOWdEaGxNeG1TbjdsNGljNnRiTGZMTkxPcEh3QnNkT3c1cTUwOEw4SExrWHpMTUR1QWZTWjVYRnVPc2dCMEYvRlBIQTRFeXhwSm1WNVUxS3VmdWlNSUYxR2NkKzBMMk5pWkxRNjhmMm5abjJPT3lwVk5hclZHcGtTMUFmOEFtZ3hOTEFOMGFwZ01tZVhFU204T2h3Y3YrK0E4am1lYXJESXZSNVJaY1o2RGRZT0J1c09UdFFHMFhkSnErWDQrUmo5MEYyN3diZXI2bVJYM1kiLCJtYWMiOiJkZmYzZTJiZDUyNTMzMDk5NWViNTdjMzQyMGEwZTcwMmE5YTYyY2FhMzNiZTBlZWRhY2Y3MTNlMWY0OWIwYjhkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVENy9IWVA3OHprcHJ2UDRUNWhlMlE9PSIsInZhbHVlIjoiSm5VdTNYM0RZSkRYcjJnaXBPdUhXNGFCUWgxOW5PLytKa0VtSXE0UGZuS3FoMUY4MEFhUjh5UVJqSC9ydHVuYnVJSTR0bXUzRXdQV3MwVlh4bVgydWVHWEVjVVhZTlpibUR3dE85bmo1VU1hT2dYaUlXZEdwTkhsN2VmVmFrbUpwL3QvcllCVmNaVUR4YmZJbHU1RlZHd2dPTGl2cVZRRnpXZTd1enFMdUQyRHNrc0tBMDI1cDluSkxBTDBQa3JNbmdkR2llbm5CVFdlTHpjWEF6VGIycGhSRDNtTUNnQWUwaE9RY3J2bGh5eGk1ZUVjYmFtcTRodEd3Z0dzTE9KNU1Cd2lPS3FqaXhjWCtJay9HWk1qdHl4UFRNNkhWQ1k0YVdZS3hFWDdyR3RXNk9sNFMxTUVDQzlZRHVDcnFCSDFlYTVtOFM1RUxsbHhWa3NmdFNXbXFTRzFXNGEvaEI4YWxEWCtNYmlJZ25PZzQwcU1ET3hQN1A3MkhHaldzdVM1MGp3VHE4UUpNYnBHMFNYNzh4VkdSRTR1M2h1QjZXQjFCQmczUXpJVTh6RGhWM2ozV3J0QWR4ZjJ2L3QxRnlMR3k0WHRYL0JnUXlCZElBTnd0OFhtb1RSMjNKNGNoN2pWNHU1YTd1OFFoMXFZYUhHRHNtd2g3UjY5a0hOWG0xangiLCJtYWMiOiI0MmM4MDI1NGZmZDBhNTVlNGM2NTlmMGEzZmJkOWRmY2M4NWEwZTg1MzkyOWM5MzMxZjQ0Y2RjMjA5OWFkYWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik4zL0FON2p2UU53VEdlUDRod1M5Mmc9PSIsInZhbHVlIjoiSlpUdSthQXpJK203RkFqZHZlcVZGTmNtd2w3eHZmY2NIZkw0ZDJxU216RDVIcStDMW12SkpTc2hQLytNdkFvTE1Jc3VRMGx6MlBQTGtOd1pFY0FCV0FJMHcyemd4M3ZqOVhUSHZFakhsRHo4Q2hmMzl6ODNkcDBZbVd2bENZVHBpeDhHenpLVGZ3WjdyNGZmSTVjUy9NOGtrQ1BBaU5YMjlRWVRCYlNaSWlJaS9QWXVmV0d2eWZ3UHFXeTYxbjRWc1ZSQ3U2R3dUTFY0NTl4bnppeStFVHhWbDEyUjJ1Qy9LOVJZOEZ1dHlwYVEzY1QxQU4reDlCSjJEREwyY0NkZk5RVFFUN2oyc0tZUVRYbU1VVGtvMnljaDJiMGtJaDJ3QVMyWGZFQ045VEJHUHBjUjJLOWdEaGxNeG1TbjdsNGljNnRiTGZMTkxPcEh3QnNkT3c1cTUwOEw4SExrWHpMTUR1QWZTWjVYRnVPc2dCMEYvRlBIQTRFeXhwSm1WNVUxS3VmdWlNSUYxR2NkKzBMMk5pWkxRNjhmMm5abjJPT3lwVk5hclZHcGtTMUFmOEFtZ3hOTEFOMGFwZ01tZVhFU204T2h3Y3YrK0E4am1lYXJESXZSNVJaY1o2RGRZT0J1c09UdFFHMFhkSnErWDQrUmo5MEYyN3diZXI2bVJYM1kiLCJtYWMiOiJkZmYzZTJiZDUyNTMzMDk5NWViNTdjMzQyMGEwZTcwMmE5YTYyY2FhMzNiZTBlZWRhY2Y3MTNlMWY0OWIwYjhkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83145441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-255397974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255397974\", {\"maxDepth\":0})</script>\n"}}