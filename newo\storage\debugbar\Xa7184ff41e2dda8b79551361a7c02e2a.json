{"__meta": {"id": "Xa7184ff41e2dda8b79551361a7c02e2a", "datetime": "2025-06-08 13:05:10", "utime": 1749387910.055672, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387908.68769, "end": 1749387910.055708, "duration": 1.3680179119110107, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749387908.68769, "relative_start": 0, "end": **********.864633, "relative_end": **********.864633, "duration": 1.176943063735962, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.864659, "relative_start": 1.176969051361084, "end": 1749387910.055712, "relative_end": 4.0531158447265625e-06, "duration": 0.19105291366577148, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021720000000000003, "accumulated_duration_str": "21.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.950265, "duration": 0.01914, "duration_str": "19.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.122}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.996073, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.122, "width_percent": 4.972}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749387910.0170622, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.094, "width_percent": 6.906}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-87209181 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik14Vm1iTnZCV1lhcEwvZ1pVNmt2aEE9PSIsInZhbHVlIjoiaTM4YWhKaWJOakgrQ0FNV2ZIZ1FuZHo4cUhNYjBlUGVJRVk3Z09OWkpYL3dzODl1Z2l0K3dYM1pPUE4zcGpZWHBiU0RseGRIdTdFMUtNMDY5bENwRTA5ZVg0WUEzVnJQaDd4RjlNaUNtYnNmeUpkVWV1K04rL3N6WEhKYXV0a1I2NHlnU2lnZUw4bXk5V1U5aS94RFFzU3BMeEZSSVpZM0hlRU9uZzVqZ0ZLNERoMmpteGx2Z0VLUEFCaTA4YmwrN29jS3ljSGh1cURNU041TUNLTUpCa2EwVC9oSTNYZTBjSVlKai81U3FuOHV6cVZ0TGw1VHJWUXo2c01QZnQvVDlJTElGb04xU2V6Rkpkc0dIVCtSejZGNzltL1JwL0k0MFFEZDF6THpoRDQvNlV0MGpyR28xVE9yQlp6RUFvZHV3UUNWT2RCeXBPM3htSkJXZDZLZTkzSUxzbmNOQVRraCt1ZG43V2NIdW0wbnpCVUVBd3FISTFKbmFwcWhRSXpuY21UTEVLMWVYNlovRlhtVEtUZ01mNGVzbUVQOTl6Rno5NEdjZ2IrRDZRMHhoMjUwaWM3aHQyVXIzdlQxSERVZXVFbDJsNi85NzEwMnBrRXU2WkhyMllQMXVpSlBBeGhVbnB5MWlqVWEvQXp2SWVHRy9OVExuZklNaWRMSy9Ob1giLCJtYWMiOiI5ZWUxMzljMjVlYzM1MTAzOTU3Yjc1MWYwMzkyZDlhNTk3YWZlY2NjMTQxYjI5N2IyYzkyOTFmNGNlOTM5M2QyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFMeHVTOUlxWkNQT1JOL1RDWGRBVGc9PSIsInZhbHVlIjoic0pmc01uUW4zQ2FTZkRKSDl1T0pvTWZCN0I3NzlqQWhJNlZiUVF3QmZKNHhEN0xIc09MVmZBTWNNblhvSFF3TURkUDBSUTNqcDFzR21hL0VEOEpQc2pBcWJrdHdISWdPTHlQZng5QldZU1Q3WTNNbnA2T3BMVW9keURRc212MDZ6c0tqL3pUMnE1b1Y0US8xL2dLK3FEdlJneXNjN1VDd3oxOFMwSGJwWEdXN3VlbjhzVTNodHRzbHhYN2dPUGJWMGV1Uk43SHJUTFVqaTNoT3NiNlhIbE9LNFNlREY2YzhySGg5QTluck5xQ01obGlXQXVLSzlqOURaZnA3RFl3VkxTV1Z6aFR6eFVaVTJmSjhyY3JBaG9nOTBxaUR1US9tNUVXa0xaTXdzK0VZTzE5d3hyRnhNOXUrSGNXVzM1UWViT0NaQlNEdGlWdmpPcENIcGpmVlZXYkNUNnZxb3JWdU1RY05kT2llei8vOVNIRlZIQjBtVzQ2Z0JLRDVMTFRIRUxMN0ZKeFp5bFhxYTNjVlpKdTVuelpvMVZMUDN3VERhNG54bXcwUnNmL3dVUGYyWnpRK2htOVZ4RDRSUXRQY3NKNUt4Vk1OenRTeVpIK3JRMHcvWnhVcHJuNHhjOVNZOERJd0RxcXZqNkJOaXdIZ05idE44UWJscDM1ZlUxZkciLCJtYWMiOiJmNjU3ZDM5MWEwNTVjY2M4NDVhZjU1NTkwNWU0NWU0MTk4NjIxNWQwNzM4YzI4OTJkZGM2MjdjNDE2YzVkMjA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87209181\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2077634352 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077634352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1806746865 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink0c3RBell6TTl5Q3IzMjlobzVSQlE9PSIsInZhbHVlIjoiNDBJSUd3UXRKY1dHK0ZwOHFReU80NitkdE1ZazJDZ3FGZW9UNFhkNzg5amVOWkNGNHljc1JDUTZGMkw4VlFkd0FUM1RneFlpMmJJYktCbzgwZjEzUzFHaDEyM3dUWmNCV3FPTlNYMndDRXhCSFp0b0hMcEVjZUZ6c3ZWS0RuUE8xaFp1bmtreFFpQitTQVNINWZvcHM3cjF6R1BCdlpTWkphZXZVREgwWDZIRVliQkd3eVM2Q0tQazhSNHErMTJPTVMwRzJhUkRia1M1L0dId2pTLzBvYXB0NGd5ZFREZlY4Y1J6VDY2Q2g5LzJSWERVOVN2ZE1jYTNZZ3B5ekRKS0NDdXBTK2tiazR4T3dMd2YxRnZaWnFkRmpSNEpMODdaVlVXclBGcTV5UlpaeCttWERyQ2FBSTJMRFpnS0VpY2l6ZlJHWE15eEVYM2ZUa2VkUmI3NXhaT253bTdtV3RvSWxQU3VMYmVtNE16a3F5YkI2YnNXVEFMRmRpNE5iZkZodmk5dEt5MVRVdjR5Mm9oMURzVkpoRzhWSTBRQ1FOTVVZRjVyeXN6UDVkanNoeXdLYnlkZnRZWTRUUytZc0hSZHkzOHl6OENoL25pcWp6bVYvMENyTWhJdzBCelZWcGxGNVN6UFFpQzU3UDBSOVVKZU5wL2NxTXZDcEtWK3RZRngiLCJtYWMiOiJiMDUxZTIwMjQ0Y2I4MGJkODExZjhjYWE4ZmNhODlkMjU1MjExOGJiZDFiOWNiODI4NzY3MWVhOGRjNzNlYjhjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjR3LzdTYW10ZnlSclI5TXM4bDNoV2c9PSIsInZhbHVlIjoiekp4b3N0UEJvc0VTdDJjU0RIWFM4YjV6NDBZM3pKdi9tNFcweU9GSFNQV3dONTJLeXE3QXlHOWt3OHNRY2FmZGJLNVdTVkZueXdVNGNKSXR0TTdoaERhVG5adEsxc21jbjVJNFhyOHZnTlhORFg1YldSdWlXQUVGVGhFa3Z5R2pZTTVidjVtYWx2aCtESE9wK25KTXVWWWV6V0JRME5SWjJBK2RlVmNtbFlyWVBpRllrUG9RMDV1bE1iVGlGQUxPcVNsVHFpYTdva3laQTVoWHpRM0NNM3IyMWVIa3NERzJlYUZNYmUvWC8wbUhXajRhQVZmeTFFY2NYVVJ0Wm04eVpqRDZHL1hhaWcvNW55bEtRQWV6aithVml0RGUrMVdnMHZoSk03YWZLM0N4VFNYSGt4Q2h2aHdqUGtrODJ4Y2RmZHl4cWNQeHN4Z1RncVZROGdwUFZ2RUtaMmkvbDNnb2lkY2IwM3NmeE9MS2pEYkhCaENHMmhWdEZFblF3clJ2aGh3YzNuaGd0UFlqdGtrdTZ6TXFyUGg5NndKT2lCUExMOVVaS0tlRWxBNGdDUDJlUHBSYW0rUTNGNHVlMGJvalVzVEVDdWpCWTV0NHNNalRYU2lMTlRWRm15d1UzMHZjNmNuQk5rdmdyRWtpT2Y5RVlLbzB5Rk5PenJvQnlkSTYiLCJtYWMiOiI3ZDE3MzY5MDBjMzI2ZDViM2ZlNGU4Nzc3NmMxMDk0MTllNTMyNzliYjNjZDIxMGMwMjUyMWNlZGM2OTc4OGRmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink0c3RBell6TTl5Q3IzMjlobzVSQlE9PSIsInZhbHVlIjoiNDBJSUd3UXRKY1dHK0ZwOHFReU80NitkdE1ZazJDZ3FGZW9UNFhkNzg5amVOWkNGNHljc1JDUTZGMkw4VlFkd0FUM1RneFlpMmJJYktCbzgwZjEzUzFHaDEyM3dUWmNCV3FPTlNYMndDRXhCSFp0b0hMcEVjZUZ6c3ZWS0RuUE8xaFp1bmtreFFpQitTQVNINWZvcHM3cjF6R1BCdlpTWkphZXZVREgwWDZIRVliQkd3eVM2Q0tQazhSNHErMTJPTVMwRzJhUkRia1M1L0dId2pTLzBvYXB0NGd5ZFREZlY4Y1J6VDY2Q2g5LzJSWERVOVN2ZE1jYTNZZ3B5ekRKS0NDdXBTK2tiazR4T3dMd2YxRnZaWnFkRmpSNEpMODdaVlVXclBGcTV5UlpaeCttWERyQ2FBSTJMRFpnS0VpY2l6ZlJHWE15eEVYM2ZUa2VkUmI3NXhaT253bTdtV3RvSWxQU3VMYmVtNE16a3F5YkI2YnNXVEFMRmRpNE5iZkZodmk5dEt5MVRVdjR5Mm9oMURzVkpoRzhWSTBRQ1FOTVVZRjVyeXN6UDVkanNoeXdLYnlkZnRZWTRUUytZc0hSZHkzOHl6OENoL25pcWp6bVYvMENyTWhJdzBCelZWcGxGNVN6UFFpQzU3UDBSOVVKZU5wL2NxTXZDcEtWK3RZRngiLCJtYWMiOiJiMDUxZTIwMjQ0Y2I4MGJkODExZjhjYWE4ZmNhODlkMjU1MjExOGJiZDFiOWNiODI4NzY3MWVhOGRjNzNlYjhjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjR3LzdTYW10ZnlSclI5TXM4bDNoV2c9PSIsInZhbHVlIjoiekp4b3N0UEJvc0VTdDJjU0RIWFM4YjV6NDBZM3pKdi9tNFcweU9GSFNQV3dONTJLeXE3QXlHOWt3OHNRY2FmZGJLNVdTVkZueXdVNGNKSXR0TTdoaERhVG5adEsxc21jbjVJNFhyOHZnTlhORFg1YldSdWlXQUVGVGhFa3Z5R2pZTTVidjVtYWx2aCtESE9wK25KTXVWWWV6V0JRME5SWjJBK2RlVmNtbFlyWVBpRllrUG9RMDV1bE1iVGlGQUxPcVNsVHFpYTdva3laQTVoWHpRM0NNM3IyMWVIa3NERzJlYUZNYmUvWC8wbUhXajRhQVZmeTFFY2NYVVJ0Wm04eVpqRDZHL1hhaWcvNW55bEtRQWV6aithVml0RGUrMVdnMHZoSk03YWZLM0N4VFNYSGt4Q2h2aHdqUGtrODJ4Y2RmZHl4cWNQeHN4Z1RncVZROGdwUFZ2RUtaMmkvbDNnb2lkY2IwM3NmeE9MS2pEYkhCaENHMmhWdEZFblF3clJ2aGh3YzNuaGd0UFlqdGtrdTZ6TXFyUGg5NndKT2lCUExMOVVaS0tlRWxBNGdDUDJlUHBSYW0rUTNGNHVlMGJvalVzVEVDdWpCWTV0NHNNalRYU2lMTlRWRm15d1UzMHZjNmNuQk5rdmdyRWtpT2Y5RVlLbzB5Rk5PenJvQnlkSTYiLCJtYWMiOiI3ZDE3MzY5MDBjMzI2ZDViM2ZlNGU4Nzc3NmMxMDk0MTllNTMyNzliYjNjZDIxMGMwMjUyMWNlZGM2OTc4OGRmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806746865\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-12******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12********\", {\"maxDepth\":0})</script>\n"}}