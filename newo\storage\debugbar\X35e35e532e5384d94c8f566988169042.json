{"__meta": {"id": "X35e35e532e5384d94c8f566988169042", "datetime": "2025-06-08 13:00:47", "utime": **********.764535, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387646.462838, "end": **********.764564, "duration": 1.3017261028289795, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1749387646.462838, "relative_start": 0, "end": **********.592772, "relative_end": **********.592772, "duration": 1.1299340724945068, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.592797, "relative_start": 1.1299591064453125, "end": **********.764575, "relative_end": 1.0967254638671875e-05, "duration": 0.17177796363830566, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579872, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006880000000000001, "accumulated_duration_str": "6.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.686772, "duration": 0.004900000000000001, "duration_str": "4.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.221}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.717906, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.221, "width_percent": 15.116}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7375948, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.337, "width_percent": 13.663}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/create\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-702588680 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-702588680\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-614735108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-614735108\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1456031636 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1456031636\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1656646748 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387610576%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJZejVnaVk2djl2MkJqdThBcXd5U0E9PSIsInZhbHVlIjoicXVBckZncC8vN0Z3MUl5WTY3OFdTWmdMajFSRWV1dzV1TmhTcWpXZXBsVUY1NFppWHVMcG9EWmE3M2ZabHdiQnNBVnhQdkY1UGN1WTlHNUVLZGl0cVNCWjZ5OCttZlFUSk1sMG9pOXk2aGwzZUtmako0di9mbHFYZGhyYk1JSWM0YVhrYStJa3psNWF5RHNDVXdsVllTbk15Tm50UUFKaEswbENSVEh1SERvWWEwSEhOTE1MalpmWm9Wem9YMzFpQ3FZZHZORStnVEF2UUp6aXQxNHZnSVozRzhVMEdGUTI3cnJQMmpiMXAzYVlJUFJ2SjR2K20rbjlKT2Z5VjJQUjZlcFVYQnFDYU9MaFVFVjlMWGpRTmczK0lRRTRZeEhFSThiUTcrSjA5S25GNkliZ2pnRkpFMGRiOGdFQ2VucERrUUQ0ZHRjZklvYytVcThYRTBoaUJCUGVhVVJJclFMWkpjekVKcXZqdlEvSEt5V01LcURpL2YxQUsrVDZiTTNHeUJYb3E5OW9pRzhVWVhyNmtlTmpiVGtZYVB0YUNvM0hNYkxDaTNTem9SenBBazFPa3FhYStaSXBmTHJvcjNjQzVEbGI5WGk5dVpEQ25wUW5lNndhZHByT1FCRDJXME43VnNJMXRod3NnMTRGSllHaHIzMHIyRXBRSWpIcVhrZkQiLCJtYWMiOiI4MzE0NjlmODVjODI0YjY1MTE3ZDllNjkyZmM2NTRkODNlMjlkZTM1YjdlMDMwZDgzNmJjNjM1MWY2ZmIxOTBhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNuWEJNREFpa3puRWdiLzJMVm8wanc9PSIsInZhbHVlIjoiYkc3OXNSWjZhZUlYclI0c09GWUxRREEvT2pCcHBwbmRQem1LbC9KSnFyTWZNQWg0Zkx1a25uK2hkZ21JaWxvMUVHOFRyYWlvNkZWOVJLdFdENnFVQjcyT3YzUWJSTWxyaFF4RU16V1JzNEhaQzZRZllvSC9xd1RIMWxSTlpBWmMrK0lsc3J3ZFU2RFJFa1ZUeFNmaGFHczY2OW13L0sxMTdMSFhnd0lPUVY5SG9Jb3JBSng1dXp5YkthbUZoNU1VS3g4blNFVS9XOGFzWUpKcVJxSWZ2a3g1USs2YTVaL3d5NkRBdXZjYnVQZzJVRHM4QWkwVUlQUDVCeDVBWGc4WFErYWtSeEdUeDF2ZTZVb0ZkdEpYZ2RiZS82WXIzbkpPZTdQTWwzMFhML085akJwQk55eWtyTmN5R2ZiU01VOVNHRUswTHM1QkhPS1dPNHhpLzlYeWVHVFNLa1VGelpmaDNxSHRROFN0UUJmcjVDWEcydVdUWFlrK1F3TlJyNW9udXdyK2RSNTlZVjRPc2MvRXY3YWFPaWNxVkw4aEpOTUtpUUlEUWhtVnhtTlFYODlUMFpkTllwdHg0N3hQK2RGcU5DWHI1MkVWdWVpRjE5L2dEUWpuQUoxakhaVkVqOWZVTGNsOGgxK2VHQjFkSElod3JmVGY1ak1Udk1VWFFaV1MiLCJtYWMiOiI5YTBkNjAyN2UyMmYxMmY3YjgyODNjMjhlODRlYTJkYjQwYzI0ZjQxMWYwMDY1ZWNkMmU3MDQxNTBiMDhmYmIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656646748\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1951363509 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951363509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1935774456 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9qV29DMzdVMWFZbGNCRER4clY1U2c9PSIsInZhbHVlIjoiUTFGRi9Qend2N3YzVFhMSjFnT3JPNDlZVm9SZklVY0I0OGxFWkdrb1RJQ01QUHJyZlBtVFM4MVpibjVob05GSThqUXpyNUkvSVI1Mm45MnRtL0YzMUJIMFhCdXBUVTZBeXNlVFB0dTdLOG1FZExjM1pUclowOVhIbXJBelhURXdvZG5kSXpmK20rT0NYL25nd1laKzFKZXBqajZrVncySTVkeTEvL3hScGZZT2VtV1NGUUM5T2ZDWmo2VHJ4SWN3L08zSHdTTGZqZ0pWbG85ZFY5WU4vY2l5WFEvYndmR2d3R3BHd2VyT1ZVa2ZJKzliakNLVnZCbWZLakFiMjZMa2JyenAyZWp2M2w5UkRjd1VyTUVldEQxb0JvSHdUVEdDNFh2RFNUaWlKMjFrcU1vU1FpdXZXSk9zeW1SNnRsV1BPTHVNSU9GQkZOQW5SRk5QZWJMWlo1NEoveTUyS01iK1lQNm1HUDBUWGkrTUxDV1JsT1FZd1NHRHBpUTBnME8yUnFLWXZwSHo2SktBRkp4YldIVWhybS9FMU1DaW1TbGI3QjF0K0Z3T2NxTjdDYTRhSG5uNjM5TEZqRG5DOXhUWThIdnF6Nkc5UnJOc1l4K0ZUUHBGQ0pNRUJIV3JXNGVRelU0K1FtbEhkM0dsUjdtZllZamZhbElsR085dHM0VSsiLCJtYWMiOiJmNWNlOTg2MjBjZTY5OTkxZWRkZWMyYWM3MzJjZTA4ODFmZGMzYjk4NDUxZGI0MmE1NGUyNzUxYTcwNWQ3MDhlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZscEhERFVIMHBBTldRZ3RDNXIwYUE9PSIsInZhbHVlIjoiMzB2WXA5bUpCbHpWRThOWm0vWjQza1dTbDJmMHRUbFFQaHFYbHQ3TXBWMGNueHZXTnVtL3R5RHlSaDFKZVBqR05mZlh4Y1Z5S3BsenIxR0xZdFBmRHprZHQ0TmtGc1YxS3hIclZtclFVMHhCOEplcDBXZC9RU1dZK2ZrNEdLQXIreC9YQ0VlaTZudy8wdXQ1MEtIY2NGTWtveDhySlBaMFVyRDNQTVdaaEhmSzBlSjVad2hsMy9RajhCM0NGRTFNek9UUTJXM2ZsRlhQbjI3R0s3c0IxN280Y3IzK3gzemhNbjJ0N3pqVm51Y1pXb0xlcVQvN3V6ODZMWkZqNTIzMzJtS2twZWdFQm9NY3E5UlYrK0tFU2duMXZFdHdJWDF4c2lobjRldnRxMHIxbGt1cXBxSUlIRlJ5NVhMOFlvWVlYaEtUUi9qT1hHcTNSR0ttUHZaUkJPcmF3eGVUTEY2V2U2SGVhaHE0YVZ0Lzl5eEN4NEM5TXlVMVdIZ3Jlb3dhY2t0MExyVzhEenN6Nkk1ZXluaFk2NE9LK1Z6bXp4OFhaOHFjdG9lekpMenl3WmFxR0h4ai9wYXFYVVpmbnZpQ3NVci90RS95TFFYUHQ3TU1VMW5JR29yYk5Zdjk0NWx4TDZrL1dXYTFnbmVGTnhuQ2pwc0NTa1ZUdS9lTnUyR1IiLCJtYWMiOiIwYTVhZDYwZjdkZDZjMTEwM2YzY2VjODA3NDQ3MWZjYTAwMWUwM2I0ZDZkOGM3YWFlY2MyMzIzMjAwMzdlMjc1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9qV29DMzdVMWFZbGNCRER4clY1U2c9PSIsInZhbHVlIjoiUTFGRi9Qend2N3YzVFhMSjFnT3JPNDlZVm9SZklVY0I0OGxFWkdrb1RJQ01QUHJyZlBtVFM4MVpibjVob05GSThqUXpyNUkvSVI1Mm45MnRtL0YzMUJIMFhCdXBUVTZBeXNlVFB0dTdLOG1FZExjM1pUclowOVhIbXJBelhURXdvZG5kSXpmK20rT0NYL25nd1laKzFKZXBqajZrVncySTVkeTEvL3hScGZZT2VtV1NGUUM5T2ZDWmo2VHJ4SWN3L08zSHdTTGZqZ0pWbG85ZFY5WU4vY2l5WFEvYndmR2d3R3BHd2VyT1ZVa2ZJKzliakNLVnZCbWZLakFiMjZMa2JyenAyZWp2M2w5UkRjd1VyTUVldEQxb0JvSHdUVEdDNFh2RFNUaWlKMjFrcU1vU1FpdXZXSk9zeW1SNnRsV1BPTHVNSU9GQkZOQW5SRk5QZWJMWlo1NEoveTUyS01iK1lQNm1HUDBUWGkrTUxDV1JsT1FZd1NHRHBpUTBnME8yUnFLWXZwSHo2SktBRkp4YldIVWhybS9FMU1DaW1TbGI3QjF0K0Z3T2NxTjdDYTRhSG5uNjM5TEZqRG5DOXhUWThIdnF6Nkc5UnJOc1l4K0ZUUHBGQ0pNRUJIV3JXNGVRelU0K1FtbEhkM0dsUjdtZllZamZhbElsR085dHM0VSsiLCJtYWMiOiJmNWNlOTg2MjBjZTY5OTkxZWRkZWMyYWM3MzJjZTA4ODFmZGMzYjk4NDUxZGI0MmE1NGUyNzUxYTcwNWQ3MDhlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZscEhERFVIMHBBTldRZ3RDNXIwYUE9PSIsInZhbHVlIjoiMzB2WXA5bUpCbHpWRThOWm0vWjQza1dTbDJmMHRUbFFQaHFYbHQ3TXBWMGNueHZXTnVtL3R5RHlSaDFKZVBqR05mZlh4Y1Z5S3BsenIxR0xZdFBmRHprZHQ0TmtGc1YxS3hIclZtclFVMHhCOEplcDBXZC9RU1dZK2ZrNEdLQXIreC9YQ0VlaTZudy8wdXQ1MEtIY2NGTWtveDhySlBaMFVyRDNQTVdaaEhmSzBlSjVad2hsMy9RajhCM0NGRTFNek9UUTJXM2ZsRlhQbjI3R0s3c0IxN280Y3IzK3gzemhNbjJ0N3pqVm51Y1pXb0xlcVQvN3V6ODZMWkZqNTIzMzJtS2twZWdFQm9NY3E5UlYrK0tFU2duMXZFdHdJWDF4c2lobjRldnRxMHIxbGt1cXBxSUlIRlJ5NVhMOFlvWVlYaEtUUi9qT1hHcTNSR0ttUHZaUkJPcmF3eGVUTEY2V2U2SGVhaHE0YVZ0Lzl5eEN4NEM5TXlVMVdIZ3Jlb3dhY2t0MExyVzhEenN6Nkk1ZXluaFk2NE9LK1Z6bXp4OFhaOHFjdG9lekpMenl3WmFxR0h4ai9wYXFYVVpmbnZpQ3NVci90RS95TFFYUHQ3TU1VMW5JR29yYk5Zdjk0NWx4TDZrL1dXYTFnbmVGTnhuQ2pwc0NTa1ZUdS9lTnUyR1IiLCJtYWMiOiIwYTVhZDYwZjdkZDZjMTEwM2YzY2VjODA3NDQ3MWZjYTAwMWUwM2I0ZDZkOGM3YWFlY2MyMzIzMjAwMzdlMjc1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935774456\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2015508404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015508404\", {\"maxDepth\":0})</script>\n"}}