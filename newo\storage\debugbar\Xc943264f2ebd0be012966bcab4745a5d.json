{"__meta": {"id": "Xc943264f2ebd0be012966bcab4745a5d", "datetime": "2025-06-08 13:35:50", "utime": **********.195984, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389748.818915, "end": **********.196033, "duration": 1.3771181106567383, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749389748.818915, "relative_start": 0, "end": **********.030605, "relative_end": **********.030605, "duration": 1.2116901874542236, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.030631, "relative_start": 1.2117161750793457, "end": **********.196039, "relative_end": 5.9604644775390625e-06, "duration": 0.16540789604187012, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45293256, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01684, "accumulated_duration_str": "16.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.11987, "duration": 0.01558, "duration_str": "15.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.518}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.167616, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.518, "width_percent": 7.482}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-127617374 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-127617374\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-90871518 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-90871518\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1257667461 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257667461\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-721022708 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkR0cGNzWDBLRW5PR3FzMzFZSFo4YXc9PSIsInZhbHVlIjoiTDdNNzh3OHVPU1lrYnJJQkFpZGtZclV2eW80MVVrM3Rnek5pdG5HbzVtYnlJZXhMM1F4enpIbEhoaTFLMlFCWEdWTiszU0l5R2JPV1dXaXhQZzJ4c21qbnhKbUdkNHBFME45Ykt3cHJmc0xSWGwwRk9EbnpWOEhWMlJyNWJsdDBha2thTUJSSzRFdlp3dk5nNW53V3ErS0hCR3l5WE9PeC8rL1BiVzZydmZVbFBvdEtJdFJ1WFdQZ0o4Tnd4Vk91N0RhMm1pbHZmTGdONm1VemZCQ0tzamVIU1RQeDYvZU9MOWZxYjVLZm55S3lvZTZTT09pZEZxQTNudy9SamMyaWtkZklEb0VuMWFXTWE3WTFMRUJmK1BqMkJnb05RSG4rM3Vzbjh3WHhkekwzcWJlZnNrNDUyRTdnVzhkWit6YlNrRUxLbm5wUll2eEwwUEhxUDJMcTNReGpLUEpnamFUMmdJdU8zQkNWR2RsK3JZS0xDT2FndEMxUmJMdHhEb2JjSmlUTzNPSWxrWSswZHhkNzA3cnl0bkdEY1VIREV1R3hCcHkrRWtBQWxWSWJuV0ZNbnRiWVBFUWtFdmE0K01XVER4TndwdGhHRmxmNFlGa1BNYWk5Y2cxQ1NEUTkvUUN6MUhnQzZjaWFacEx2azVVcS9abEFFUnVwU2gwclg2MWYiLCJtYWMiOiJhYzY4NTkwZTg3OWRkMzczMzQyNzM1ZWRlMGQwNTg5ODg3ZTY2NGJmYTczYWJkOTU5YWE1MjIyNTQyMzZlYjM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNBRUxSM0ZBaEY0WTRnS3Y4M0ZkclE9PSIsInZhbHVlIjoiMGNaaUpzZ1Y1UWZRWlNMSWJLb2ZPVUZLWktuMy9BYmhEbTFoTEtZWHp5NnNpdW1PeHByUTBRTVNFMlVDV3crTHovQVVVYy9ucTRudWxPTStOeUJ5ZldoV3N0MkJvNDI1VUxjemQxWWZoQmIxclQxeXlkNCtPNFJjYjdscFoydnFNL2tFZEdud082MC9LY0NtS3ZLd2FhQVhkb3pWOFVjeDdsaXAzaGQ3NUl2cGVkc3h3QlR1aUQ3cGRmS2RBaFV4VVdmMTNuOGt2bjFHd1lqVFRuZEZQVVNWT3JqVmg4RG1RT0gwYk5GM1J5bnBuSWNreHZNcUM2UStNQUw4bEJ6SXhtb3c5UEpZNDkrU2RadThTTzF0UUVTc251NHJTVGJiQTFBUkFtSm81TmFKM3N6eC9GazgrVkJUbk01TlcxcFUxYU03eDMzZmx5TU8zK3NGYlBZOVlKWUVsdnUzME90dVJqb0pCdktpRitVWVoyZm9JejFibjVPSVlzZ3doMGx1c1EvVXgzMHRMKzltbCtFb1pRTnpjK3I5QW5JdU9UVk1BaVRzZnMvYm93YUN5WUJCY3BRVFhQMEF4Y09SNUx3bkgreFNyQnNrUWhBN2pjeUNsNElMcnRlaGM3UXNNTFVrcWFVN0lmS2xzZStYcUUzaEV4Wk1hZjRPd1lhYzJnUk8iLCJtYWMiOiI5NzQxNDYxZmE4YTVjZTQxYzg3MWIwMGM4ZjY0ODU5MTA2Mzg2ZjdmYjIzY2JiMjg3ZWQyMjM1ZDJkNzAzMDY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-721022708\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-84500166 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84500166\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1152682601 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:35:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpucXkxRFI1V1ZlTGdIZ1NDdzJDakE9PSIsInZhbHVlIjoicit5dWpHSHdHejArUVZGTEVReTc5dVQ0ZGFzdDl4N2RiU1hMSU9maWduN0drZnJlTTFreXZEdkZSaTh5c2hVWFQ5SmpIUVAwK0NmcHZrREdVOWV6bG5TZVd4Q0UxaFZNY1ZNSVNzck1sdDZMRVkwbGR0aW1mdm1PS05lV1J0YmRCMTBZZ2F2OTl5UWh0NnE1N2xrWUVnQ1RqaUdjNy9hSWVlT1JoYTQ4Sm5TVW8zellsRFJOenB2dW9EY25GQysrOVpNdnlidW9kbm5hNmZzK0dveFVZSjZnZEtldEdDRkQxbzBjSFJybm1QaW5iOXdlUi9CNVVEQlFMelhiSzZMQnloWTZGYTFGVEtucVlBOXppYXFMWXpLNHBTck9MK0g2aVNEN05HTlR2Slhad2p0VU9tOXVHTnAxdzRRWVFOZGh5UFlpZ1pnajQ5UHpXYjc5ZEdQcjNCUVNxZlViMHRqcFZmQVNDdG83elNqNWdXY3NjQUN1UjhjWEJ0dms0cWFXZGJkbDdkL043VWZ1cC9SbjNuSWdYUTJSOGlPMWVkV1k0K3ZEeFdlQzFyZ0IwTzhVTWlybVFoemxzdno3cE1uamxET1RsN0ZYS3NqZUNxSW1EVi9PWmN0Z3VXYlByQXRsUWh2ZmkrLzhEMFp5WVhCYlZMcy9wYjRDRzkzbS9MUksiLCJtYWMiOiJmOTI0OTBjOWE0ZmRhYWY5ZmE3OGNlNTE2ZjBiMTkwMzllYzU1NWUwNWMxNzQ2MGM4YzBjYzdlMWU5ZjUzZjA1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZpT2RTLy9VUFNUb3JSOFF1ZEc2Umc9PSIsInZhbHVlIjoia0FpOEIvL0l0bm9MRDBHS2h4RHljSDg4QlNPclRyWVJrMjk2RURncmN0TjNyWkk4RXZCbjNEUXhqNTNLWUZ3UnpLM29HZ2o5enRhcEplUTR1eGZIdFQza0VXT1JZbTRST0ordUhkcTYxY1prTUpXMXpDLzk3Vi9aWlh0VzZvUkVPUk1mQk9XR2Z0c2xySXZaSjFxaVJDQmhhU2V4ZjA1OURIeER1aE9sSVdwUGZLQlg0NStod3BUMkFVeFA0SksrMVhEOFQvbmZ6aVNEcEtwUlhocUhyUlBwV3E3ditZME80dHEvV3lBdHU4QjFnUFp0TXBTeTZ1NGx4UlhLcm15WEpCZ0RYNDE3RFhtaUJsTXBoVXkvZHFmeFB4VDZXZ0ZIUXdic05DZVJUdlVSODR2TDhaSk1Gb2c1TUVtWnA0Z3p6Q0lqRHlpdzBkZlBUcHNLaFpoVjlIRmxPbDVVNnhjUjhIUWNFeUZ3dStwSUIweXFEREJKaFlTbU81c0JqVTVnZW5zN0ZDZmRmVk5lSSs4SUR0cjB5Y2RML0V1UGlUUW9hSVhldkVYTlBFNjdUTkZIOWEzUjRucWpiaE5JdFR5U2IvM0I1K05ZY3Y4aTAwa0Z4a0dORUViQ2xwQnNxSkJteTFYTTVNdXRMUGViRkdwVW5HVGt1NGxYYU1NVVZvRG4iLCJtYWMiOiI2MDMzYWI2N2Q4YzQzODMxNTdjNzE3ODA3NTE5MjZlNzdkMDdlZGM5MjJjYjNhZWNhNjI4YzI4OTQ5N2M3NWYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpucXkxRFI1V1ZlTGdIZ1NDdzJDakE9PSIsInZhbHVlIjoicit5dWpHSHdHejArUVZGTEVReTc5dVQ0ZGFzdDl4N2RiU1hMSU9maWduN0drZnJlTTFreXZEdkZSaTh5c2hVWFQ5SmpIUVAwK0NmcHZrREdVOWV6bG5TZVd4Q0UxaFZNY1ZNSVNzck1sdDZMRVkwbGR0aW1mdm1PS05lV1J0YmRCMTBZZ2F2OTl5UWh0NnE1N2xrWUVnQ1RqaUdjNy9hSWVlT1JoYTQ4Sm5TVW8zellsRFJOenB2dW9EY25GQysrOVpNdnlidW9kbm5hNmZzK0dveFVZSjZnZEtldEdDRkQxbzBjSFJybm1QaW5iOXdlUi9CNVVEQlFMelhiSzZMQnloWTZGYTFGVEtucVlBOXppYXFMWXpLNHBTck9MK0g2aVNEN05HTlR2Slhad2p0VU9tOXVHTnAxdzRRWVFOZGh5UFlpZ1pnajQ5UHpXYjc5ZEdQcjNCUVNxZlViMHRqcFZmQVNDdG83elNqNWdXY3NjQUN1UjhjWEJ0dms0cWFXZGJkbDdkL043VWZ1cC9SbjNuSWdYUTJSOGlPMWVkV1k0K3ZEeFdlQzFyZ0IwTzhVTWlybVFoemxzdno3cE1uamxET1RsN0ZYS3NqZUNxSW1EVi9PWmN0Z3VXYlByQXRsUWh2ZmkrLzhEMFp5WVhCYlZMcy9wYjRDRzkzbS9MUksiLCJtYWMiOiJmOTI0OTBjOWE0ZmRhYWY5ZmE3OGNlNTE2ZjBiMTkwMzllYzU1NWUwNWMxNzQ2MGM4YzBjYzdlMWU5ZjUzZjA1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZpT2RTLy9VUFNUb3JSOFF1ZEc2Umc9PSIsInZhbHVlIjoia0FpOEIvL0l0bm9MRDBHS2h4RHljSDg4QlNPclRyWVJrMjk2RURncmN0TjNyWkk4RXZCbjNEUXhqNTNLWUZ3UnpLM29HZ2o5enRhcEplUTR1eGZIdFQza0VXT1JZbTRST0ordUhkcTYxY1prTUpXMXpDLzk3Vi9aWlh0VzZvUkVPUk1mQk9XR2Z0c2xySXZaSjFxaVJDQmhhU2V4ZjA1OURIeER1aE9sSVdwUGZLQlg0NStod3BUMkFVeFA0SksrMVhEOFQvbmZ6aVNEcEtwUlhocUhyUlBwV3E3ditZME80dHEvV3lBdHU4QjFnUFp0TXBTeTZ1NGx4UlhLcm15WEpCZ0RYNDE3RFhtaUJsTXBoVXkvZHFmeFB4VDZXZ0ZIUXdic05DZVJUdlVSODR2TDhaSk1Gb2c1TUVtWnA0Z3p6Q0lqRHlpdzBkZlBUcHNLaFpoVjlIRmxPbDVVNnhjUjhIUWNFeUZ3dStwSUIweXFEREJKaFlTbU81c0JqVTVnZW5zN0ZDZmRmVk5lSSs4SUR0cjB5Y2RML0V1UGlUUW9hSVhldkVYTlBFNjdUTkZIOWEzUjRucWpiaE5JdFR5U2IvM0I1K05ZY3Y4aTAwa0Z4a0dORUViQ2xwQnNxSkJteTFYTTVNdXRMUGViRkdwVW5HVGt1NGxYYU1NVVZvRG4iLCJtYWMiOiI2MDMzYWI2N2Q4YzQzODMxNTdjNzE3ODA3NTE5MjZlNzdkMDdlZGM5MjJjYjNhZWNhNjI4YzI4OTQ5N2M3NWYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152682601\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1171795151 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171795151\", {\"maxDepth\":0})</script>\n"}}