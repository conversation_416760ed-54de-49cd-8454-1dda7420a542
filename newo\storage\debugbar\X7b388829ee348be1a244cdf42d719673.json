{"__meta": {"id": "X7b388829ee348be1a244cdf42d719673", "datetime": "2025-06-08 15:09:39", "utime": **********.804282, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.265716, "end": **********.804303, "duration": 0.5385868549346924, "duration_str": "539ms", "measures": [{"label": "Booting", "start": **********.265716, "relative_start": 0, "end": **********.727707, "relative_end": **********.727707, "duration": 0.4619908332824707, "duration_str": "462ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.72772, "relative_start": 0.4620039463043213, "end": **********.804305, "relative_end": 2.1457672119140625e-06, "duration": 0.07658505439758301, "duration_str": "76.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45184904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013359999999999999, "accumulated_duration_str": "13.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.766224, "duration": 0.01219, "duration_str": "12.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.243}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.789989, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.243, "width_percent": 4.566}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.794752, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.808, "width_percent": 4.192}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2143145029 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2143145029\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-10411115 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10411115\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-788546173 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-788546173\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1348648738 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBDdlhrMGtoWVBqbTNnM3RnVEN1RHc9PSIsInZhbHVlIjoiTFJzaXRibjJqeFhiM2s3MlFzTzFXRTZhNmxRYmNNb1pzSUJNeHRXaTFSVFJMdlcrQlUyRk42OXRGQVQ1Mk1FZDgzSk9GV0ltMHh0S2d3U3luUmkyOVpNaU5HOG54RXpMcXVRWXBraXZHV2dCQjhYSit4STBCcFJSckxDVnFtb1MwOUc5UUFqWmpSODEzelhZckdsQWxKem94eExoN2pMK0lHQW9HeTVKbWVIYzBoVFhJVmdvYUtDTndKcmk4dDBLY1UxcjBzUUlGK3VyUHVOdlh4WjU1a0NES3hxd3hMOGp0UFdlbkd3ejMwN2xVU0lVMTAvU1dPVlNRcTFtT29FN2tCMkllWmQ4V29QT29raWNqbjlUanJJdFFHK1JFUDN1a2tTaG1ZNjU2N0RTSWRTNGtYdzJ2bVc0R0lvby81TEdkcmZpSnNEWjN1b3BnblVJZE5qbWcvZjVlcnFWTExHVzNZc2hvZVFBT0IwMkxUd2xEQUtMaWxRNjcwUjVFL3ZLbk9QcTkySzA1OU5rTHVPbWVUN3YyMXIwa0VmQVZncFdhVjdXRDdzLytkTHpRUVVYTnVyTk9GZVFpTHRuekRKZ1BvUWNDd1FnRDJkOFQ0alpZWENaSUsweTdMSFZNTmJNelZtbjBSYjRySU5OcXVvalV2YVpuN1h0NVBZY0x6QlgiLCJtYWMiOiI3Yzg1NDgwYjc3NzUzN2I4NGNhN2RhYWM0MzY3MzVlOTViMTZkMTY5MDVjMzFlZWY3MDFhYTlkNjQ3NDY5NTRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InIzb1J0bEowZ2MrYURlZFJOK3hEZ3c9PSIsInZhbHVlIjoib0M4K1BHY0JONkNLZWZ4SHBLVFE5cWp4WTczWUJVa2NBSEdVUHlqcS9DeEVPTXhZR3FmMEVXWlNaYTF2R3hVaWE0bk1iOThvMTB3YWMrUmI1NHl3VDNWVWtzbmNGZFZuSjIwNjRqWWVKdWkyY1pKVDRPWkRGbER4REFIWnVrT01LeU96c2ZVSmczNG1NQVR3akZ5eHJlRDlmNjVsRHpUMi8vUUt2ekN1bm9SWFZIeWErNFNPS0VLZm9LQ25oSmdJY0xubXhkaDFoL2ZHMEgwcGp5emNlMzZsR0c1SkhnK0ZPdE9ZMXVCWEEyR3FpSTFlclhyV2lSZ0dET21UL0ZyMHY5M0lQV2lmcUMzSkx3LzRMeWd6bjVkMmNobmFLOHVKVm9zeitIcTBpdDdNWENWWm9kUVdvelMxY0FjUHhFRDBCeHJLNkNFUCtMd09DTEI5YXVQaS9QaGJySW5pTXdlYjhWRTJvcXJwK3U5cVpTd050MGxpcmxybytocitEcTJZV0UxZ0NLdmVLVVR2WVNWTjdpd2N4ZFR3cUJPMHd2Wkl5RW53dFNHQzdySU05Y3JwaUNJbzhtMjVKY1loSTkzWFE2Q2VGRVRGbUNBOFYyVi9Vb28vVVRnS1h2cHlGNkMwMzU2cTFtNDVqNFFyU0pHMzY5bmlGdEx4UmpyZ01CbmoiLCJtYWMiOiJkNDA1ODU5OWQ0ZmQ5MDhmMWQ3MjEyZTViY2E0YTNmYThiNzBhMGFjZDA4NzQwMGY2OTBlZmMxODY4NzNiNThmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348648738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1649541372 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1649541372\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-890588375 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxhR0ZvbExQaVRzbU5KbHEvL2FTc1E9PSIsInZhbHVlIjoibXZIaWNOTmlCcWpNOHRPaDhZZVJacmwyTzRhU1NaekRBdHRidVp4SUQ3L0VZNnArSEhkY1lGQkczQ2I3L1BtWm04ZExianEyamtZOEpPUTkrdWJ2ZWg5b3JRTVIrQUdjMlJueGVhVTBRV284emdoYkh3eVNvTDcrSHZRejk2amZxbWNwOTJKQlRoV0hCY2JQMUZkTTF3aWZFTHN4akh1bzNFUENaSjBwMklPdHVCNUEyZklPQXl6bjNXRWJWOUd4ZC9NQ3VUajE2VVVqVXdWU2ZHWHlTWVc4ZVk0NGtyQVF3REt2czBTWFpzUGdPdXJGL20zUDFWeUFFYUd4cEV0Zm81ZVRtUlNIT09pZW9NUjkxMS83NnZmTkRrbDFiMEZlUXNnS3RzTkcwSlRxbk1OMUorQzNZbTNxcWdtZ2xKSFpzMzhlako0YitQNm9mUStscEVGVG4remtRbHV5TDV6U0dCVEkxTFJWWGdIYkozNVhnVm5QTVRSd2ZYanVRZm10N3lSK01raEJlcEF1MWxnc2RzMnREbkxtY2NvVmFJeFFjWEw3WXlBdVVOcUsxdEFtc0NsN24wbE0xOVc0Wks5OE4yZjR1Vkx4WkNadW04eGs2UDBNL0FaZFA4YlhhTDlyWVlQcDBwWDZTMkVBUW5XSXRKbldZWnIvNER4QUhnVmEiLCJtYWMiOiJhMTYxMGU5ODNjYzcyYmJlNWU0ZGExN2Q2ZDhiOWU1N2FkYmE0OWQ0ZTU2NzRiMTZkNzBkN2U0ZjljODM5YTFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikx0ZFR2N3VMVnJTOEtmZHp2VUVIY3c9PSIsInZhbHVlIjoickhRUXRIMTNTYjh6OWJiMnB0UW1pQU05dlhrNTBqNVFHZGUzSExYYWMwa3R0WXhwRFZpcG9zcWpRbXQvZDluSkEwSUd5UHdJbjBqQ2dHcXVtb0txaHJPcFVvYU51NnBQVUtiN081ZjJTaEUvc2Z0bnhJSXIybmtUMzJvZFFWaWtvbEZyZ1Bad2xMNWZZV29TdDc1VUp0eDRDS3RrRnRTUzRSeGIrTWZnZmVMMXMwL3lHT0F1cFllSFNSR2RxQjYzNVpyNVQrRlpIa3JsSkpwNmp5dklkaG1UVjcxRVhjcUhsazZ5VUg1RGd4ZEtpM0p5U3l3ZTN1YUh0NkY3a0grM3VpcHVCUnpQQlN1cnRORUtqNndJTkZPZ1JhRFl0RmhRTzVzWExQeUx5NGwxb2F5OWIybzNIUHVJc0hyNDJqdyt5VGZFMHdidEdlWFcxWkhQdTF1amVqZkRacUhBMXVHbGkyZjU0Ty92ZmpwSkxJbHpNQjQycnZtVU9jKzUrOGJudkdvZ1l1SVc1UU5OTzFJU0R4c2toM0MrTVc2WE1xSUJEWU9CTW9mZWU1eWtucklBbEVuRmpCVVdab2hTdlRlSkZ5S3JQM3dBUFU4VjJEODg4TDhwV3lKL0I5bW4xb2IvUkw5WHB4emVwOWNyTjBzTUo5cml4cEhJTFJMcmttcVoiLCJtYWMiOiJjM2NiYjM3ZjFhZWVkOGFlNWQ4MTJjZjdmZmEwYzE1YTcwMjk4NTllNWJlOGQ0ZDRiOWJlMzBjZGZlMTk0OGJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxhR0ZvbExQaVRzbU5KbHEvL2FTc1E9PSIsInZhbHVlIjoibXZIaWNOTmlCcWpNOHRPaDhZZVJacmwyTzRhU1NaekRBdHRidVp4SUQ3L0VZNnArSEhkY1lGQkczQ2I3L1BtWm04ZExianEyamtZOEpPUTkrdWJ2ZWg5b3JRTVIrQUdjMlJueGVhVTBRV284emdoYkh3eVNvTDcrSHZRejk2amZxbWNwOTJKQlRoV0hCY2JQMUZkTTF3aWZFTHN4akh1bzNFUENaSjBwMklPdHVCNUEyZklPQXl6bjNXRWJWOUd4ZC9NQ3VUajE2VVVqVXdWU2ZHWHlTWVc4ZVk0NGtyQVF3REt2czBTWFpzUGdPdXJGL20zUDFWeUFFYUd4cEV0Zm81ZVRtUlNIT09pZW9NUjkxMS83NnZmTkRrbDFiMEZlUXNnS3RzTkcwSlRxbk1OMUorQzNZbTNxcWdtZ2xKSFpzMzhlako0YitQNm9mUStscEVGVG4remtRbHV5TDV6U0dCVEkxTFJWWGdIYkozNVhnVm5QTVRSd2ZYanVRZm10N3lSK01raEJlcEF1MWxnc2RzMnREbkxtY2NvVmFJeFFjWEw3WXlBdVVOcUsxdEFtc0NsN24wbE0xOVc0Wks5OE4yZjR1Vkx4WkNadW04eGs2UDBNL0FaZFA4YlhhTDlyWVlQcDBwWDZTMkVBUW5XSXRKbldZWnIvNER4QUhnVmEiLCJtYWMiOiJhMTYxMGU5ODNjYzcyYmJlNWU0ZGExN2Q2ZDhiOWU1N2FkYmE0OWQ0ZTU2NzRiMTZkNzBkN2U0ZjljODM5YTFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikx0ZFR2N3VMVnJTOEtmZHp2VUVIY3c9PSIsInZhbHVlIjoickhRUXRIMTNTYjh6OWJiMnB0UW1pQU05dlhrNTBqNVFHZGUzSExYYWMwa3R0WXhwRFZpcG9zcWpRbXQvZDluSkEwSUd5UHdJbjBqQ2dHcXVtb0txaHJPcFVvYU51NnBQVUtiN081ZjJTaEUvc2Z0bnhJSXIybmtUMzJvZFFWaWtvbEZyZ1Bad2xMNWZZV29TdDc1VUp0eDRDS3RrRnRTUzRSeGIrTWZnZmVMMXMwL3lHT0F1cFllSFNSR2RxQjYzNVpyNVQrRlpIa3JsSkpwNmp5dklkaG1UVjcxRVhjcUhsazZ5VUg1RGd4ZEtpM0p5U3l3ZTN1YUh0NkY3a0grM3VpcHVCUnpQQlN1cnRORUtqNndJTkZPZ1JhRFl0RmhRTzVzWExQeUx5NGwxb2F5OWIybzNIUHVJc0hyNDJqdyt5VGZFMHdidEdlWFcxWkhQdTF1amVqZkRacUhBMXVHbGkyZjU0Ty92ZmpwSkxJbHpNQjQycnZtVU9jKzUrOGJudkdvZ1l1SVc1UU5OTzFJU0R4c2toM0MrTVc2WE1xSUJEWU9CTW9mZWU1eWtucklBbEVuRmpCVVdab2hTdlRlSkZ5S3JQM3dBUFU4VjJEODg4TDhwV3lKL0I5bW4xb2IvUkw5WHB4emVwOWNyTjBzTUo5cml4cEhJTFJMcmttcVoiLCJtYWMiOiJjM2NiYjM3ZjFhZWVkOGFlNWQ4MTJjZjdmZmEwYzE1YTcwMjk4NTllNWJlOGQ0ZDRiOWJlMzBjZGZlMTk0OGJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890588375\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-797955747 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797955747\", {\"maxDepth\":0})</script>\n"}}