{"__meta": {"id": "X3b5ea8e84be9bbe26325944c2596fa51", "datetime": "2025-06-08 12:57:21", "utime": **********.147848, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387439.24969, "end": **********.147889, "duration": 1.8981988430023193, "duration_str": "1.9s", "measures": [{"label": "Booting", "start": 1749387439.24969, "relative_start": 0, "end": **********.006077, "relative_end": **********.006077, "duration": 1.7563869953155518, "duration_str": "1.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.006103, "relative_start": 1.7564129829406738, "end": **********.147894, "relative_end": 5.0067901611328125e-06, "duration": 0.14179086685180664, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43388440, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.006679999999999999, "accumulated_duration_str": "6.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1127, "duration": 0.006679999999999999, "duration_str": "6.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ToM9MzFHjrqchhleuo7Qv68shFdHDviRp2ZwOxFx", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-48430942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-48430942\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-621287888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-621287888\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1555602197 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"137 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfw2%7C0%7C1961</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555602197\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-499359784 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499359784\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1770592905 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:57:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI0V0ZuT3p0SU8xQXFEaHdYbCtnSkE9PSIsInZhbHVlIjoia291Q1F3aG1LYjJpN0hyQVNYSDh4YlZlY003YzBYdVExTnorcEdQNWlQNi9McElQSERscjNzS2lramYvYytNclJCMy9qTy9tSXpEeGRUYXpKTktScGhxVXBELzNyOCs1c2c3dTBrU29jMWpZQjZ2U2F3dGZhQTMvTG5mZGx5NGdESzFtTDZ1c2tZd0hoMXUvcUJZOEdlbENZWFZFU2ZtTjNFbmVHVWxwTUJJMGxqeEZkalRHOWllZ1NyS0o1M0VLZnQxL1VlUjEzTnFpK1NMUzBSY0dqRDBscldtcTZqVi9heE9QZm9qa3VlTGc3WmZHeEhLTzFBMHhnMUJES1d6Z1lvQncwNWxYckJFVUlxbC9wdUdIc2IyYVV1RVltcDBuU3c5RmxwVEcyanJWd2NkMmpLcnVvVHhJU21FMnppY2wyU0wvWVBmY1NiVTFQOC83bU1TazlrbE00bFFsTC94ci80WnA0bmxFbXFQMFdRTTVUZjM1cEVPaTBKQ3ludXZOS1R0NTY3NWQ4TjM0czdVV0U3SzFJTTNWUi9LMTNxR29CYkxLSVdaMVV0clp5SkJNTHBwNm5oZy9MWDQ1cHlWTndrY3JWbnptQlp4YVh0UzVHZXNHWmEvRnJrMFd6QzVYZGZWZmxZclRWUzZxVWJkUGV2OXVFQlJoci9XUzlkRVoiLCJtYWMiOiIxNzJlNDIzNzZhODQ4ZDg0YmFlN2ExMjBmZDgzNzA2Y2E2MjhlMDI1Y2RjNDdkN2UyNDBkNWQ2MTNmYjU0ZTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:57:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitGV0dQYTEra0cvdExtclltWXNJaVE9PSIsInZhbHVlIjoiZWU5aDVLQ2xZdngwVEFtOTJZRlhwdjI0Z1ZhT01wTDB2ajRBZFppMG1kNmxxVjIvZW5jbE5iUVJiSXVEM3lCeE1oaG9hdlNXYmJ0QkMzb3ZUNFc0L2RwYmRaN3NraGNsZ0FUaWNnQjZYYmlHRkNjb2cyT1h1NE50MFhLalZJaHIvVDNYOENxQ1g0SkRvQ3daNDdDTzlLM1JzQUhzNlZhQjQ3a2hZZ1o1KzRWQ0w5RXNYV1lyM3h2cjVJVlI2RytnVUZyRDlzVFYxRlpwTmsxYlM4WnkzNzQrZ095VGZ5bmo0L1Q1UzB1OHJoQzNlRFBQZGRlZlhibVo1Umllb25lWkNvTFc4L2JzZGNEQys4L3l6ZTRSdHMyUGdLd3NWTC9KOWJvcU9GRG0xZ0xXdUVLbktoV0d6RStaMUl1WnpnVTJTY1N2MHFoazJ2ZklSRWVMSFJZb2hmUlRWNXZvUHk3czZRNTB3eW5NNWp0Q1pyVnIyNjRYY2xFT0xvZEQ2Z1lEaldTaldVU0ZXc05iRWhMUkh5dDdDYmNrSC9DWjdJbGVvZmh5R3Uzdlc3Q3VvMU9qSHJpV1RvcUJuRktiaFhaSXhydzMrUWhEb3NoSmZ2RTNGcEZ5djZKZjdFdXVkNHRBcnkwbjBlekFVZUw0eFA0NmpxWld0L25XL0pHWnUzSGkiLCJtYWMiOiJkOTA3ZmNlYmE4YjExNTA5Yjc3NmIyYTg2NWYyMzljYjQ4NDhjNGUyOTY5OWVjZmQ4Njk1NzU1NDljMThlZTRmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:57:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI0V0ZuT3p0SU8xQXFEaHdYbCtnSkE9PSIsInZhbHVlIjoia291Q1F3aG1LYjJpN0hyQVNYSDh4YlZlY003YzBYdVExTnorcEdQNWlQNi9McElQSERscjNzS2lramYvYytNclJCMy9qTy9tSXpEeGRUYXpKTktScGhxVXBELzNyOCs1c2c3dTBrU29jMWpZQjZ2U2F3dGZhQTMvTG5mZGx5NGdESzFtTDZ1c2tZd0hoMXUvcUJZOEdlbENZWFZFU2ZtTjNFbmVHVWxwTUJJMGxqeEZkalRHOWllZ1NyS0o1M0VLZnQxL1VlUjEzTnFpK1NMUzBSY0dqRDBscldtcTZqVi9heE9QZm9qa3VlTGc3WmZHeEhLTzFBMHhnMUJES1d6Z1lvQncwNWxYckJFVUlxbC9wdUdIc2IyYVV1RVltcDBuU3c5RmxwVEcyanJWd2NkMmpLcnVvVHhJU21FMnppY2wyU0wvWVBmY1NiVTFQOC83bU1TazlrbE00bFFsTC94ci80WnA0bmxFbXFQMFdRTTVUZjM1cEVPaTBKQ3ludXZOS1R0NTY3NWQ4TjM0czdVV0U3SzFJTTNWUi9LMTNxR29CYkxLSVdaMVV0clp5SkJNTHBwNm5oZy9MWDQ1cHlWTndrY3JWbnptQlp4YVh0UzVHZXNHWmEvRnJrMFd6QzVYZGZWZmxZclRWUzZxVWJkUGV2OXVFQlJoci9XUzlkRVoiLCJtYWMiOiIxNzJlNDIzNzZhODQ4ZDg0YmFlN2ExMjBmZDgzNzA2Y2E2MjhlMDI1Y2RjNDdkN2UyNDBkNWQ2MTNmYjU0ZTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:57:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitGV0dQYTEra0cvdExtclltWXNJaVE9PSIsInZhbHVlIjoiZWU5aDVLQ2xZdngwVEFtOTJZRlhwdjI0Z1ZhT01wTDB2ajRBZFppMG1kNmxxVjIvZW5jbE5iUVJiSXVEM3lCeE1oaG9hdlNXYmJ0QkMzb3ZUNFc0L2RwYmRaN3NraGNsZ0FUaWNnQjZYYmlHRkNjb2cyT1h1NE50MFhLalZJaHIvVDNYOENxQ1g0SkRvQ3daNDdDTzlLM1JzQUhzNlZhQjQ3a2hZZ1o1KzRWQ0w5RXNYV1lyM3h2cjVJVlI2RytnVUZyRDlzVFYxRlpwTmsxYlM4WnkzNzQrZ095VGZ5bmo0L1Q1UzB1OHJoQzNlRFBQZGRlZlhibVo1Umllb25lWkNvTFc4L2JzZGNEQys4L3l6ZTRSdHMyUGdLd3NWTC9KOWJvcU9GRG0xZ0xXdUVLbktoV0d6RStaMUl1WnpnVTJTY1N2MHFoazJ2ZklSRWVMSFJZb2hmUlRWNXZvUHk3czZRNTB3eW5NNWp0Q1pyVnIyNjRYY2xFT0xvZEQ2Z1lEaldTaldVU0ZXc05iRWhMUkh5dDdDYmNrSC9DWjdJbGVvZmh5R3Uzdlc3Q3VvMU9qSHJpV1RvcUJuRktiaFhaSXhydzMrUWhEb3NoSmZ2RTNGcEZ5djZKZjdFdXVkNHRBcnkwbjBlekFVZUw0eFA0NmpxWld0L25XL0pHWnUzSGkiLCJtYWMiOiJkOTA3ZmNlYmE4YjExNTA5Yjc3NmIyYTg2NWYyMzljYjQ4NDhjNGUyOTY5OWVjZmQ4Njk1NzU1NDljMThlZTRmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:57:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770592905\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-195107118 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ToM9MzFHjrqchhleuo7Qv68shFdHDviRp2ZwOxFx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195107118\", {\"maxDepth\":0})</script>\n"}}