{"__meta": {"id": "Xb571940abc5af7deaccda2926e08945c", "datetime": "2025-06-08 13:44:54", "utime": **********.086184, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390292.761868, "end": **********.086218, "duration": 1.324350118637085, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749390292.761868, "relative_start": 0, "end": 1749390293.91798, "relative_end": 1749390293.91798, "duration": 1.1561119556427002, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749390293.918005, "relative_start": 1.1561369895935059, "end": **********.086222, "relative_end": 3.814697265625e-06, "duration": 0.16821694374084473, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45293000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.024750000000000005, "accumulated_duration_str": "24.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0137088, "duration": 0.023420000000000003, "duration_str": "23.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.626}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.065166, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.626, "width_percent": 5.374}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-2008372252 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2008372252\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-386372651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-386372651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1263921197 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263921197\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1295392263 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFic2dNaGwzWDNHVnNSc21vOTJiMlE9PSIsInZhbHVlIjoicjVTWG1FMEpnUzVObCtLQlkxYldvT1Zod3ppckhJYkp6ajFZYWJNYU5YRFlMWllYTWl3T2dSeldmVjkzUlVBUEJUbmNUTVV6L1BxekxCWnEzK1J5Z3NaWmdjWkpRMFpLSGJEMjVDbkdQOHUyVW80YmVNejk0c1N0NTExSmJ1MSswVjNWTEdLdVUvQVVMYkY5TWg5R2NYSkJ1TGVmakZiQkJETGpmNWdLRlN3WEljdk5MQ0lHckNlNVlVWkYwNmwwU2tTYmdkNzZMcS9TU2h0Z3lhbFhDSmx5SlNwOG1UeldVUHRtbTFxMllxNjF6Y2tjS28rQ01FTmtuMXVxVXc3SXZCdHdQUnJDSVVxaTNNMEEyWkZ5WTN4Q0dnayt0bTJWdXVUR29uUE5kWkZZL3dLQmF0WE1lNHFoVmJwYUZQZjNRRWN6WkpXUllHRktkcklTN2szREdhWHM3anRqVzhiVkRpQ3VrYlp0UlQrZVBRRGhmSEw0NnBsTG1zNlVZZFFXSmk1MjZIL29GejlTa1Z1RmxwYkdkZkYwSnFFb1Z6d3BwSzJPSHZFSTVjakFjU1ZpejlrTGxIa1B2aGtUb3J5aDJ5U2ZsQlRCekpCYVMzdnlJblB1bGN5Zk5abTZCSjVzeGZoVUl6N29Mcndxd0VLZHEyQmU0ZzhnMmtYQ3BNTmIiLCJtYWMiOiJhODI0MTZhNWY0YTRkYzdlMjQ5ZTQ4NTFmNzgwYzBlN2M1OTE0OGY1Y2I1NjQ5ZmQ4ZDZmZjNhYmU1MzFiNTU3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkttcG1CUndiMDNZWnl6dGtQR0VtSXc9PSIsInZhbHVlIjoiU0lwOVVqQXNLVlJ5bW5BMUp6bnQ5eXhRQSs4N0g2YUFIb1ozSDFPekt3SFkwV3FPNG43QXRJM2xaQVR1cUNibWFQcTVVU1AvYkEyWmkrOTAwR3JtKzRmd21aMkViRXIwOENDYXp2RUtKeldSdFVUcnZZMkRHY1l0MEdoMlpSUFZ4aityUU1ZTkNkc2liZjd4NHJhWDFmQU9VemRWZysreVgvVmxzQ3VmaDF2R0NrQmNnd0hjdkRDeXJ1UEpPL2ZZYVE1OE55N0RqMVdENWN1d0ZVdEgzNUY4UlJNMUJ5eTVHY0hpRmZ4RHNZejlyV0J5TEs5RFloUTFzVkZxdWJGNzVWVmFmTXNJNm5uekRDZUpGU0J0MUk2T1VFRHlTcHVNaTE0QUN0N2tOaHJUbDk1WkRYWVdlNElmRm9zeUttOVhUQXFvZXREZXpKS3FBUGY4VU1mMWJ5T1dxY3pSUnIrcXZ6dnVZcFFpbFc1Wlhydkl5OGRYV2UwVXV3aENBU2trTHlNc2l0WE5qSmE4SDdGblJnVlIyYjhyMVpEUnpzeVplZTJtMmJ3eWlqM3RVWHdLOHowQjhFM2hZNnIwSnJRUjA5c1F6MDNjekhMVEY3MVF0c01zU1BDcUtaNHhCRWorcEVhekwzeEJVR2I2VlBwT29jUnJ2RzU2dklab0MwQjkiLCJtYWMiOiJjMjJkMzc3MTcyY2NiY2Y4MmI5ZDkwNjc0NGEwNzY3ZWFhZWI4NjI3ZTEwN2RjMDYyNzdhZWNlYzQ3MGU4OTNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295392263\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-209275717 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209275717\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1246748497 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldKRHFUZjg4OHhxL2h4TEhaTzY1bUE9PSIsInZhbHVlIjoiUFh6YUQ4R3ZrUDIvNHExWFp5eFpRNDF0ZjYwdTMrbzVLYkxESUM1YkNhK2xsTnNSekRzMkdNdXhRSGpCYkRQd1pnODNEajkrQWZTTTA2Q241Y2tIY2JwZFVCVjM2Z29LcE1vVFQ5ZDRCY1Y4QkFoMjN0VWFMbzg1UVNZRFZkK0NOcGhWV2dZR2ZGZGcra0VqMk9iV3RrZjFUMjlaK0NNV1djb2pPKzlPWGMrL1BnWjEwdkNwbHlteHZVcVBYRmw5dStrMVh4MXJUbmo3MzlGOWZ6V1hWaHZEV1kvaGRic1lpZ20zYjJibGgzeGZyOVllbE5BRDNvdWw4aUxvK0pNT0xGeUtCQmVVaHF4RkhaTmxEWkFqYkZzM01VdUtSMUhHVzEzNWdqTVFqdk45R2hjeC8xQTZXb3drTlBvNjYyZS9MYWRsNFl4TW1HdWUzT2pyNlVHcjBuaTgrb3ZSdlYydEZYNlpGcWhUSnkwU1pyd3JOWU92V1ozVCtYdWFxUmFtNmVKWEk1aFgxQWRrbHltd0tsZU90WStCT2VlZXJMbmthNnYzS3BrTHRKWE9QeXBuRHJZWUlGNEx0Vy9HVkdQVXMvUFpxQy9nK0ZaRHBhZVNqY3RndkQzOUFQQVJORWgwYkh2STMxVitjSnhIRG9KRUhWeTY0a1NqUCtZb2NuUUciLCJtYWMiOiIzYjRjYWEzODhmYTFjYmE3MThhNThmNTk5YjE3ZDFiZjA4MmM3YTE2ZGM5MzNmYzM5MTFiZjZlMGI2MWZlY2RmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVXOWJyV05kKzlEM3dWcFBmQ0YvOEE9PSIsInZhbHVlIjoiVCtvOXllNU03QnlJQnpURy9sYytnMU5yWkNvNEFoS29IcVBGOXVzQjY3WWJqY1g1SHIybVZMU1Z4UmJ3NEwwNEwyK2htdFMwRnJKYVV3VDYxMVEwTEJBN2VYL2xWVlAxTk9uQ3FTclYyRER4aUJSdUtlbTVBZlZtMmpJOUZNcDZiU0QyVTNKRDNKRk5wT1hoZmRnWGROVnVNb2p1VjBDMWhzeUFGMUhFS2ZQM0lFQllYdkJ5d0hTdmQ1STJRdUJoVkw4a3p5QWRaRlNVYlFOOUxGZEd2ZjVvR3R3NVNJbTR3b3E1bjBkbEJNbllyaFp1TFdSb2I5Z3ZsRm8rNHVCWW43dVlQVXNsT0tZVDdMSVVEbStlUENyWkpyUWU0cCtCVGRtZElheVNFdUVaQVEzOVN5bnNtT3R3U2lmK3VnSVhuM1luMWY3NnNCclY4VW90RmVVcG0wSXFJSjJWNHFweWxEc2tMUEVWSHFUSzhLU2JmSG85UkcyWlpkQnF2SEZWZ1NSWGsyVjJhcGY0cEVRRnk1TGZZckV0NzcrVy9NdVFLWll1MC9BSVVtMkxrM1JIN2hWKzZaUWFRMjRFNzZMN1RJaG1GTjk3SDRFbVNRTEMxY3JUSjIzQndpTitobmozQmpZTGFNNkxkNTU4akdxSmxBRktLbjI2MXJTRzROZDkiLCJtYWMiOiI1NGU4NWQxNDMwYWU3NzAxNmY0NmQ5YThlYWFjMTYwOTc3MmQ3N2ZkM2UyNzhkYmMxOGYwNDkxMTRiYTg5NzVkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldKRHFUZjg4OHhxL2h4TEhaTzY1bUE9PSIsInZhbHVlIjoiUFh6YUQ4R3ZrUDIvNHExWFp5eFpRNDF0ZjYwdTMrbzVLYkxESUM1YkNhK2xsTnNSekRzMkdNdXhRSGpCYkRQd1pnODNEajkrQWZTTTA2Q241Y2tIY2JwZFVCVjM2Z29LcE1vVFQ5ZDRCY1Y4QkFoMjN0VWFMbzg1UVNZRFZkK0NOcGhWV2dZR2ZGZGcra0VqMk9iV3RrZjFUMjlaK0NNV1djb2pPKzlPWGMrL1BnWjEwdkNwbHlteHZVcVBYRmw5dStrMVh4MXJUbmo3MzlGOWZ6V1hWaHZEV1kvaGRic1lpZ20zYjJibGgzeGZyOVllbE5BRDNvdWw4aUxvK0pNT0xGeUtCQmVVaHF4RkhaTmxEWkFqYkZzM01VdUtSMUhHVzEzNWdqTVFqdk45R2hjeC8xQTZXb3drTlBvNjYyZS9MYWRsNFl4TW1HdWUzT2pyNlVHcjBuaTgrb3ZSdlYydEZYNlpGcWhUSnkwU1pyd3JOWU92V1ozVCtYdWFxUmFtNmVKWEk1aFgxQWRrbHltd0tsZU90WStCT2VlZXJMbmthNnYzS3BrTHRKWE9QeXBuRHJZWUlGNEx0Vy9HVkdQVXMvUFpxQy9nK0ZaRHBhZVNqY3RndkQzOUFQQVJORWgwYkh2STMxVitjSnhIRG9KRUhWeTY0a1NqUCtZb2NuUUciLCJtYWMiOiIzYjRjYWEzODhmYTFjYmE3MThhNThmNTk5YjE3ZDFiZjA4MmM3YTE2ZGM5MzNmYzM5MTFiZjZlMGI2MWZlY2RmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVXOWJyV05kKzlEM3dWcFBmQ0YvOEE9PSIsInZhbHVlIjoiVCtvOXllNU03QnlJQnpURy9sYytnMU5yWkNvNEFoS29IcVBGOXVzQjY3WWJqY1g1SHIybVZMU1Z4UmJ3NEwwNEwyK2htdFMwRnJKYVV3VDYxMVEwTEJBN2VYL2xWVlAxTk9uQ3FTclYyRER4aUJSdUtlbTVBZlZtMmpJOUZNcDZiU0QyVTNKRDNKRk5wT1hoZmRnWGROVnVNb2p1VjBDMWhzeUFGMUhFS2ZQM0lFQllYdkJ5d0hTdmQ1STJRdUJoVkw4a3p5QWRaRlNVYlFOOUxGZEd2ZjVvR3R3NVNJbTR3b3E1bjBkbEJNbllyaFp1TFdSb2I5Z3ZsRm8rNHVCWW43dVlQVXNsT0tZVDdMSVVEbStlUENyWkpyUWU0cCtCVGRtZElheVNFdUVaQVEzOVN5bnNtT3R3U2lmK3VnSVhuM1luMWY3NnNCclY4VW90RmVVcG0wSXFJSjJWNHFweWxEc2tMUEVWSHFUSzhLU2JmSG85UkcyWlpkQnF2SEZWZ1NSWGsyVjJhcGY0cEVRRnk1TGZZckV0NzcrVy9NdVFLWll1MC9BSVVtMkxrM1JIN2hWKzZaUWFRMjRFNzZMN1RJaG1GTjk3SDRFbVNRTEMxY3JUSjIzQndpTitobmozQmpZTGFNNkxkNTU4akdxSmxBRktLbjI2MXJTRzROZDkiLCJtYWMiOiI1NGU4NWQxNDMwYWU3NzAxNmY0NmQ5YThlYWFjMTYwOTc3MmQ3N2ZkM2UyNzhkYmMxOGYwNDkxMTRiYTg5NzVkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246748497\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1320744455 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320744455\", {\"maxDepth\":0})</script>\n"}}