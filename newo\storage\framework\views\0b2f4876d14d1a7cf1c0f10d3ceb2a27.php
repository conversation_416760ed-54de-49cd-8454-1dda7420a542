<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>لصاقات الأسعار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        
        .products-container {
            width: 100%;
        }

        .product-label {
            border: 1px solid #000;
            padding: 8px;
            width: 250px;
            height: 100px;
            text-align: center;
            background: white;
            margin: 5px;
            page-break-inside: avoid;
            position: relative;
            display: inline-block;
            vertical-align: top;
        }

        .company-logo {
            position: absolute;
            top: 10px;
            right: 10px;
            max-width: 50px;
            max-height: 50px;
        }

        .product-name {
            font-weight: bold;
            font-size: 11px;
            margin-bottom: 5px;
            color: #000;
            margin-top: 5px;
        }

        .barcode-container {
            margin: 5px 0;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-price {
            font-size: 14px;
            font-weight: bold;
            color: #d32f2f;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2><?php echo e($companyData['company_name'] ?? 'اسم الشركة'); ?></h2>
        <h3>لصاقات الأسعار والباركود</h3>
        <p><?php echo e(date('Y-m-d')); ?></p>
    </div>

    <div class="products-container">
        <?php if($productServices && $productServices->count() > 0): ?>
            <?php $__currentLoopData = $productServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="product-label">
                    <!-- شعار الشركة -->
                    <div style="position: absolute; top: 5px; right: 5px; width: 30px; height: 30px; border: 1px solid #ccc; font-size: 8px; text-align: center; line-height: 30px;">
                        LOGO
                    </div>

                    <!-- اسم المنتج -->
                    <div class="product-name"><?php echo e($product->name ?? 'منتج غير محدد'); ?></div>

                    <!-- الباركود -->
                    <div class="barcode-container">
                        <?php if(!empty($product->barcode_html)): ?>
                            <?php echo $product->barcode_html; ?>

                        <?php else: ?>
                            <div style="border: 1px solid #000; padding: 5px; font-family: monospace; font-size: 10px;">
                                <?php echo e($product->sku ?? 'N/A'); ?>

                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- السعر -->
                    <div class="product-price">
                        <?php
                            $price = $product->sale_price ?? 0;
                            $formattedPrice = number_format($price, 2) . ' ر.س';
                        ?>
                        <?php echo e($formattedPrice); ?>

                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <div style="text-align: center; padding: 50px; width: 100%;">
                <h3>لا توجد منتجات متاحة</h3>
                <p>يرجى إضافة منتجات أولاً</p>
            </div>
        <?php endif; ?>
    </div>

    <div style="margin-top: 30px; text-align: center; border-top: 1px solid #ccc; padding-top: 10px;">
        <p>عدد المنتجات: <?php echo e($productServices ? $productServices->count() : 0); ?> منتج</p>
    </div>
</body>
</html>
<?php /**PATH C:\laragon\www\to\newo\resources\views/pos/barcode_pdf_simple.blade.php ENDPATH**/ ?>