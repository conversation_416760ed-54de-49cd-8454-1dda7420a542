{"__meta": {"id": "X8ab280602a78fa282093d2e9ac6b40d3", "datetime": "2025-06-08 13:00:27", "utime": **********.661567, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387626.343501, "end": **********.661611, "duration": 1.3181099891662598, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749387626.343501, "relative_start": 0, "end": **********.492977, "relative_end": **********.492977, "duration": 1.1494758129119873, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.493002, "relative_start": 1.149500846862793, "end": **********.661616, "relative_end": 5.0067901611328125e-06, "duration": 0.16861414909362793, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01921, "accumulated_duration_str": "19.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5790088, "duration": 0.01691, "duration_str": "16.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.027}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6212242, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.027, "width_percent": 6.195}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.639451, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.222, "width_percent": 5.778}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1348740553 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1348740553\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2004166872 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2004166872\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1104313591 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104313591\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387623459%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdEOXJtT3NXazM0L09WYXJxMm5hRmc9PSIsInZhbHVlIjoiaG1UNFVUcGRSKzFoYWJ1NVVucEl2b3lTTTByelZxQ1A5NXFUL3d0Z3JTenFxRU9WeGxnRzRrOVgxbVFxYmJ6a3F2RytCdGFjMDdWVS85b3RId3d0Yld5TytQZmU0MjUvODJ4Y3VabFpsUGxtWndsVUxhbm9YVHB0RFBOeVRNT0REQjFsYkFxR1NheUJtSTM3b2UvaTRESDFjRjZQUGRla1lUb1FGSVAyc3JkQlZrWU5jd2NJWC9peVhoVnh2SzkrNzhWSk5keXdqTWZjQk02OVpCb09hSExjWXhTTUhmcXNkVzVLY0VkR21xZlMrZkpFcXVVbEJrNDFHSmJ5VlZSR29lUE54S1B6dHFhbzBPNEU2emt1VkNlcGp4ZHo5dzJjWlJZYlVlVTR4Q1FJMnFVM1BVcC9Jc0V3SkN1UytlQjJFMGM1WjhPTXZKeHVqNUZFSXZmTkFuUmJKMWxvcFp5eXZYdklGYk9rU0tGTzNVbDNVSDBKNmxFd1AyRkNPSW15RndORFh5N25RUjUzeldpcFRQU1pZclYrNWwrVWdDRm1rNmI3QkxwZjFOWlpsWHkwcG1ua3hqazNaNjl3T3pGTkh1cVJWK3ZDTGFKQUdOYWJsRXZUNVlYUjdQS1pMOGNoQmZnNnpDWjNaMGZtVjNpVVlENGltN2ZtclAwa0Q3YUEiLCJtYWMiOiI0ZjczZWQ0MzRlODNjYWIwMWFjM2JjMzNlZTJkNWJmMDBmNGNkM2M1OWVkN2EyNWNmMjYyMjlhYmM5MWY2YmUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpFZndEZ1ZuT3NOUWtWWFk5bFYyRkE9PSIsInZhbHVlIjoia3Q0emxabHlHNCs1TEpwbXJ2N29rNlJERFd5R2ExTEtrb3RKRmFRbnJNOGRJYjlQRWJrMXoyaDZEa01mVUNnRlJEOEdhaFJ6YUQ1aWNjcGpBQjZqYnlIQldWT2RINGJXYkxycWQyWnhWN2JhZlV2MHBYQ0xXaUxpZWFFcjZLV0tlak9vSXBmM1o1WVREVGJ0eW92cFhOTUMwUU9EMVdZSW9pWEdMREhvMWJvem0vcXFScElmVzNQcmIrbUZDM1VIQXpYRHYxMDBpRUxQVVk3UzhoaHVOdEo2UnZpcXFBWk5PWHlTWHE1VFluV3U3QUs2NlB6WVN6WEhOdSt5YUludmVRVmh0U2RqM01ENURpYlI2aDRzc0pmMlVPelZ3YXRKa3BaUktYQkNST2hCUlFzaXpLUHRJbUNNN005SDVUTHVER0VSa0ppWThLbzJTWDFXUWh2dHRCUWFwZ05jWnRjWmJ3OUhQb29Bck5YRlIrS3hSK2FNNk5KUm5IVy9Oa1FHMks5ZFljdkdjNFJITTgvcXdid2tYM3M0cTczZmhzbzNjc3pLNnZRQTFsL1FRUGs4RzJ3ZW5VYisxVzRzdVMzWVBiMkpUSUpGU0ROT1ZMTnAzQUNGMXYwbTJxdnl2Y3RBWEVzUUEya0dBcStpTlhxRlNrUlV6Q2pEZmR1Q3NXb3YiLCJtYWMiOiJlNGYyMGZkY2RiZTRiZmQ1MmYwMzUwYmM4ZGRiYjc5ZDdmNWFkMDg2MDk0Zjg4YzNmYzJmODgwYTUxNzEwZjE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1809404134 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9nMGZUTnJmUDFvOXVuVG5Bd3ZkOVE9PSIsInZhbHVlIjoieVh5TGJjVFZmTW41TDB4alZTWko5SWdnK1l0ZVdKMnYzRUVsREUzQXpiUHVTY0hhdVJicGxTL1FQRlRVczc5TUVSamR3Vzg0WDJESHdvOWxBY1BrdGFjZUVjdjhtYjNnb29majhnUWtSQzFQZ05HMnJET2xqT3h1MzlzbGZWM0NJcmhrcHcvL3ZrUGdrU25zcHJmR1ZPbUJLakdCdkt5QThCUG9LRVdGQmM5STdYeitodFUxRHlGTUJXQmhLVFZaSHg4SDFJNmVIMVhiS3U5WWZkd2FzNWRFdjlWR1pKbEdpR25NaC9sNmo1cThjY3NnWThZNjFkS2FLYXpSOTNRUnVxMGJ0K0haQWthSXB6T05lemhwSjA3VDlUdVJDbjZyYUtWc2ZHNFEvbDgvblJxMllsOGVubnRtdGpBQ3dCbVR1RVF0UEJRY0hQSUxiWDNsODhPQm0xTFFhTEgrOThKOXhJQXdnSW1vTzdOcVVGQisrY05ETXpIMCtqR1ovRFJVUzdUTi9RYzBlSEVBemVweGxOUDNJTlI3MlZsbzNOOUUrZkJsSmUzZVc1UFNnYUNPUEFqUFd2WDllVFpteHpCbE5zZGFrTmRHM0JsQUQ5ODcydTQ1cktEQUlRd2R1NzcxOWQwazROTEFhUnhodm5IUmc0YW9DaVltRWkwY3VsdzQiLCJtYWMiOiI0NGY1YTJlNDUwNmJmYTg1OTk5NzlhNDAwYzcxZTU4ZDQyYWQyZDllMWVmMTE5MWY4NzdhNDMyMDEzZTg4MGI5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllXOU05OTJmcmwyMGRrMElqZ2xIL3c9PSIsInZhbHVlIjoiYy96SnA5c1BacVhJWkpLRTFhY2lEMURFY3QwcHc0ZEphbTRpVlFwTDFxelJ3TDdITmRBQUh6MUFCSjZVb0ZnM05ZaUsxUldMcDVOSmxrM2U3UVF5dlRXUXNYeXpyV0R6a2pPMlNkNVZUaEJMS09jdmZCS1VTSVV1bHkwMmN2SUFJQTlCSUVRc1dBNW4rMFg0Rm9lQ3Nxcmk0bHFZaHRTbThGR0daZGNjOXkxcVUwb1BMUlg3T3BGRVM4aUZYSERTNlRWTUhybGkxdFpOejFCbUlpSnVMMitkMElJb2VOSmlDRk5iU040VVF6SVBnRmllQ05sZmhTcE0wUDFRNlBFei8rRVhhbmdFY0ExMU5aSzRkTk5tOUNBemI0N1BPbnI1Q3hGNWJyeFdOZUtVbkhXNVhMOXZ1eDdPVm1lYUxGMXQvdnZWYmhVRUduQmZsUVRJYURiUm1qbnk4TmFhRDJFNmM5SkVDbkdOb0g5ZUlFTWVKVEVnZjlGNHR3Q2RIaUlkK3phMEFFWEZFRWQ1N0FuY2I4dlVkYTA0NGI5bWN2S2VaMkw5WUlrSkFjRWpxSHJBQldqc3VHb2s0Z3JIMmI4aHo5M2pXcGJJUGVoemZJSjVXVzIvR2IxYVdtYTBqck9JRVlod0JvODBwdm1OeVp6dnBnditaWUZMQVF3RU5VQWEiLCJtYWMiOiJkYWQyZjZlMTZiNWE0MGM2MzFmNWUwOGE2NGZiZDdhMDhmZjIzMzlmOWZhMWE3NmZkZDQzYmVkY2NlOWZlMWM5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9nMGZUTnJmUDFvOXVuVG5Bd3ZkOVE9PSIsInZhbHVlIjoieVh5TGJjVFZmTW41TDB4alZTWko5SWdnK1l0ZVdKMnYzRUVsREUzQXpiUHVTY0hhdVJicGxTL1FQRlRVczc5TUVSamR3Vzg0WDJESHdvOWxBY1BrdGFjZUVjdjhtYjNnb29majhnUWtSQzFQZ05HMnJET2xqT3h1MzlzbGZWM0NJcmhrcHcvL3ZrUGdrU25zcHJmR1ZPbUJLakdCdkt5QThCUG9LRVdGQmM5STdYeitodFUxRHlGTUJXQmhLVFZaSHg4SDFJNmVIMVhiS3U5WWZkd2FzNWRFdjlWR1pKbEdpR25NaC9sNmo1cThjY3NnWThZNjFkS2FLYXpSOTNRUnVxMGJ0K0haQWthSXB6T05lemhwSjA3VDlUdVJDbjZyYUtWc2ZHNFEvbDgvblJxMllsOGVubnRtdGpBQ3dCbVR1RVF0UEJRY0hQSUxiWDNsODhPQm0xTFFhTEgrOThKOXhJQXdnSW1vTzdOcVVGQisrY05ETXpIMCtqR1ovRFJVUzdUTi9RYzBlSEVBemVweGxOUDNJTlI3MlZsbzNOOUUrZkJsSmUzZVc1UFNnYUNPUEFqUFd2WDllVFpteHpCbE5zZGFrTmRHM0JsQUQ5ODcydTQ1cktEQUlRd2R1NzcxOWQwazROTEFhUnhodm5IUmc0YW9DaVltRWkwY3VsdzQiLCJtYWMiOiI0NGY1YTJlNDUwNmJmYTg1OTk5NzlhNDAwYzcxZTU4ZDQyYWQyZDllMWVmMTE5MWY4NzdhNDMyMDEzZTg4MGI5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllXOU05OTJmcmwyMGRrMElqZ2xIL3c9PSIsInZhbHVlIjoiYy96SnA5c1BacVhJWkpLRTFhY2lEMURFY3QwcHc0ZEphbTRpVlFwTDFxelJ3TDdITmRBQUh6MUFCSjZVb0ZnM05ZaUsxUldMcDVOSmxrM2U3UVF5dlRXUXNYeXpyV0R6a2pPMlNkNVZUaEJMS09jdmZCS1VTSVV1bHkwMmN2SUFJQTlCSUVRc1dBNW4rMFg0Rm9lQ3Nxcmk0bHFZaHRTbThGR0daZGNjOXkxcVUwb1BMUlg3T3BGRVM4aUZYSERTNlRWTUhybGkxdFpOejFCbUlpSnVMMitkMElJb2VOSmlDRk5iU040VVF6SVBnRmllQ05sZmhTcE0wUDFRNlBFei8rRVhhbmdFY0ExMU5aSzRkTk5tOUNBemI0N1BPbnI1Q3hGNWJyeFdOZUtVbkhXNVhMOXZ1eDdPVm1lYUxGMXQvdnZWYmhVRUduQmZsUVRJYURiUm1qbnk4TmFhRDJFNmM5SkVDbkdOb0g5ZUlFTWVKVEVnZjlGNHR3Q2RIaUlkK3phMEFFWEZFRWQ1N0FuY2I4dlVkYTA0NGI5bWN2S2VaMkw5WUlrSkFjRWpxSHJBQldqc3VHb2s0Z3JIMmI4aHo5M2pXcGJJUGVoemZJSjVXVzIvR2IxYVdtYTBqck9JRVlod0JvODBwdm1OeVp6dnBnditaWUZMQVF3RU5VQWEiLCJtYWMiOiJkYWQyZjZlMTZiNWE0MGM2MzFmNWUwOGE2NGZiZDdhMDhmZjIzMzlmOWZhMWE3NmZkZDQzYmVkY2NlOWZlMWM5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809404134\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-114198447 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114198447\", {\"maxDepth\":0})</script>\n"}}