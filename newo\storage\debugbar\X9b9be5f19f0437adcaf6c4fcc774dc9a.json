{"__meta": {"id": "X9b9be5f19f0437adcaf6c4fcc774dc9a", "datetime": "2025-06-08 13:05:47", "utime": **********.173063, "method": "GET", "uri": "/add-to-cart/5/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387945.746932, "end": **********.173105, "duration": 1.4261729717254639, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749387945.746932, "relative_start": 0, "end": **********.875837, "relative_end": **********.875837, "duration": 1.1289050579071045, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875864, "relative_start": 1.128931999206543, "end": **********.17311, "relative_end": 5.0067901611328125e-06, "duration": 0.29724597930908203, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53622528, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.022119999999999997, "accumulated_duration_str": "22.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.982754, "duration": 0.01574, "duration_str": "15.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.157}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0250862, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.157, "width_percent": 6.193}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.0794618, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 77.351, "width_percent": 5.922}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.087755, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.273, "width_percent": 5.696}, {"sql": "select * from `product_services` where `product_services`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.10389, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 88.969, "width_percent": 5.515}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 5 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.118166, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 94.485, "width_percent": 5.515}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1274342452 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274342452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101338, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/5/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1153363414 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1153363414\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2005281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2005281\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2137292439 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFEejJmQ0pwS0F4OWpvWjJsSE9hdVE9PSIsInZhbHVlIjoiTHF3MHpjbnY4NWU5SFVueGVHMVJVRE9vQzkzV1YyRm55L2xzaVBlZEFuY0N1YUNSK2piNUx3em0wOUVpNzdwSWE3cVl1RmdpL3orbkdvREc2ZEFCdzAxSGJpSitQZ21CbDJ1M2ROd0pIQmowU1crYkJGY285NExlMU5UajdpUHNHMVVZS2ZvMWNvNFpsTVJVdWQ4NEYwejJTVGtmVHlRNmlCQkZMREFBcjVpNkY5UVVseU9kL0xNYlBTN2VMdU1EaDNzWllkWGR6Q3dlU0xrYVF4Z2FueXFXTVRXT0NSK1c0VHNtV0xUeXMwZ2ZCalFPUTA5V1EwMXdOOFE3ZUpBWGljZVFWQktSVTd5U1h5aFhaQjY5YTF3T0krdTV6UVJoNitVZi9RQ3FzTUoxM3FiR0czUDF3N2pqR0FQYlBkUDJwb0c2Y2lPRnNRdUpPeCtYWUxhZmlVUXlNTWZiRzROLy9jcVhVZ1VUSVJvOXNKRDI5R0pzb0thZGxrcmZLT283SjN0VnZoVjQzaW1kV3lFd0FMWDJmRExtWUEvQXRINXNibjgwMmxLYmZMd2xabVloUVRTN0cyRlJtREtMUTVMcEdVN1l5Nk5BZFkraDRPdlJyakh5UW9XUjRnZU9hSlQySjF4Y0JpNjUwUThEMzBFMXZ4aWIwSnliWDZZYm5KK1oiLCJtYWMiOiIxYjMwYWRlYjJjZWMyMmQ1YmJhMjNhZDBmMDRkZjI0NDBjNGQ1ZDU4YThlZGU0NGRjMDU4NWU4YmE5ZDAzNjM1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdMeGZGZTFMTjRXdXFSd0N4OGVTNGc9PSIsInZhbHVlIjoiKzk4ZE9UZS9YTnNQbWNQY3h1SGtDQk8wTzdzQUFoZkJOcFIwREtNcHJHaFY1dFZIWllXaWhjeEF4SzFWQkMyb2FRRkNvMXl0VWlsZGV3YUlOd1AvQU5PWDZnNXJpKy9tbENUd1NKOGxuS2h1QVZjdlB3L21JTnVxU2Z3NDcxTGFXbktHczF0Y3BhSUo4b0VSdndWYUFHUTJwazMzT3E2c2VYRHhzb2pjQmd3UDhqb00yU1lOb1hMbTc4TVBBUHJ4RGliWTNNcTJQbW5UWERjOER6NTZpeHg0S2gxTFl3aDNTOEJUVXJZR0x5cTdZOWZibHdxR1NxSHZFVnA4dkEvcUFCUGtOZEtkZ3h6eVYrWVdtYmdBMU55bDA0azJtaCtsNWUyeFRUWDI3ZWR3K2JaKzk1Rzd5djd4eWZUaHRVTlRaS2xncDdSTmZqTjRmYndkWlh0SXFzc3BIRGZQdFhIaW1sb2NPM3RTWTQvYzE1cFJMcUtmNVEwdFUrUUpLMHpLQmNSakppaW9RZ01jcTEyYlZZT2FQVVhxRXlNby9iTE1oN0M0bUQ5TnlmOFB1MDBReUU5ekpDL1BNQnI5anlLSlhyUzV2L003TEFCWllMT1JTT3gwd2lnbXZZakxiZTY5MGcwM2pzRmhFTHRydnlaSS9rWVpzTHlRMStMdkY2dlciLCJtYWMiOiI1OWU0ZjcxNGFhMzQ4OGNjYTBjYzI2YzlkODgyNjc0YjRiNjM3YjFhZTVlOWE2NzkwMjllYWU4YTIwMTJmM2M2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137292439\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1765030004 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1765030004\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1084546803 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZGU1RRd3h4Y05wZXdaVnkyRnkzQkE9PSIsInZhbHVlIjoiaHlQcE9RU29GMlQ5bmo4bjBacWxrd0xXK2VHLytybUllU002SjBnc1BjaSs0cFowVW9CaFM2S3FkZnNDVHlxSmNQdW9VdEFKSUxDSFBzTGRYNXpCU1ZYQVhDQU1GRXNGV3ZObkRtbnBXaFhCcy9IbGtCTXo2QW9PMjlucnFaVG44c2dnV2FVYmxySlRITjBRdlNZNHluQzVUc25uS0g3RElvdWQ1UHZaSlJmaGpuWmFDYUtKYnRtY2tGdURCUG9BQ0FhbG5QUUNuWG93Yyt4NmlrNEJnemF3RTZkL29id0x4M2tqNElmVGRINE9ieU1zWWFYdmdORmQ3eCtTTUwwTkFJWGhSVmdJZTBvbC8yUGxJK1BxSkFvcGkwTnREdk1kTGVsc1NoU0ZuakE3YTVmTW1zd3YxZFk2YTZ2UFVQRFRydTZtWFFzM0Z1RTBhTTVMdVhUVjA0YkpjMU5xNWkveU13dkhaWHpid00vNjZzYkc1UlVqeVBJTUwrUTcrMk10ZHk1a1FjbnJKeGVsK1UvNytTZ3J0MzBiVmZWVm9CQWtMRHp4Z1BPU29oNWtsR2FURzRWU2hoVWVkYVgwNm93SzZIV2hJU1owUXM5cDdibmJiRlZ5NUtYbzRmeWlqNWpvTk1Zamd5cll6V3paTzhBU2lwbGZlM0w0bW9OWHc1WjUiLCJtYWMiOiIyMDhiMjNiY2FjN2VmNjU1YTI1YzAzZTllNmM5YzIwOTE4MjAwYTE5Y2Y0YzU3OTA5ZGJkYTQ2NzgzZjA5NjMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZLVStMYS84dVgzc3JmdExwMlBaVUE9PSIsInZhbHVlIjoicnJSbXRhcWd6aWdHUm9UYW8vamkxdGpWdVlUMHV1MXpjc2dORzhmdmNpazJxOThpczBoUDVwUWdsQ0JxVHFBQnAvS1FkY0pwZDFaU0R0Ukl4ZCtiSGNSclZNRDZ6SU93dml0UXFJSlhXdklvcXRWZ3NiZlpsWW1hZ1VUZUJZaks0ZUxoUHh5a2ZoaTU0Zk8vTmlaenRsNjZnR3NlSVFFWjIvU1RXTUd6V1BUYm16QUV3UGNtYWtuMWJWSVBLVWxoTmJTa1ZaT3VuOFk5R3g4SGNTbzFHMmtIa09MOGEvVUp3QTRLZitERmI3NUY4N3lFUWJPd0xZblJrZm1iaTk1TE9yQndNOTJaMWw5MVJZeW5FclFHSEZTOG54VkpPcU9JcHg4MVF4eXc4SGlGQlJ3NHhmNEJRenVDQUVnRFdRdk9lUHdKRFZaWHh2S0JaUWtpZ2txWnBuV0poVDUySEZxbE9ZUkJnZEMxdUFxY2NiZjh5K04wNzdqTExFZ3cxM0xkWEF4am9XVm95eHA4M3o4TlU2RnJUcDNKVng4UW5LbXk5WmdOT054bWtKU2FmRDhNVUVEem1JL0dOWlRiRjFuM0p5VjVUeHlCZnZuSlRZaEkzOWtUd215UVB5YWJzWGRZeFcvdXZ5amlNWEV0aFZSazRrd1FUaWViK2lVQVhSSlAiLCJtYWMiOiJjNGFjZGEwMmFkZWI5MDU5OTIwMzMyNzQzNDI1NzUyZDZlZjQ0ZDBmMzA0MzdmZTIxNzNmYzdkYmRiY2M1ZjJmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZGU1RRd3h4Y05wZXdaVnkyRnkzQkE9PSIsInZhbHVlIjoiaHlQcE9RU29GMlQ5bmo4bjBacWxrd0xXK2VHLytybUllU002SjBnc1BjaSs0cFowVW9CaFM2S3FkZnNDVHlxSmNQdW9VdEFKSUxDSFBzTGRYNXpCU1ZYQVhDQU1GRXNGV3ZObkRtbnBXaFhCcy9IbGtCTXo2QW9PMjlucnFaVG44c2dnV2FVYmxySlRITjBRdlNZNHluQzVUc25uS0g3RElvdWQ1UHZaSlJmaGpuWmFDYUtKYnRtY2tGdURCUG9BQ0FhbG5QUUNuWG93Yyt4NmlrNEJnemF3RTZkL29id0x4M2tqNElmVGRINE9ieU1zWWFYdmdORmQ3eCtTTUwwTkFJWGhSVmdJZTBvbC8yUGxJK1BxSkFvcGkwTnREdk1kTGVsc1NoU0ZuakE3YTVmTW1zd3YxZFk2YTZ2UFVQRFRydTZtWFFzM0Z1RTBhTTVMdVhUVjA0YkpjMU5xNWkveU13dkhaWHpid00vNjZzYkc1UlVqeVBJTUwrUTcrMk10ZHk1a1FjbnJKeGVsK1UvNytTZ3J0MzBiVmZWVm9CQWtMRHp4Z1BPU29oNWtsR2FURzRWU2hoVWVkYVgwNm93SzZIV2hJU1owUXM5cDdibmJiRlZ5NUtYbzRmeWlqNWpvTk1Zamd5cll6V3paTzhBU2lwbGZlM0w0bW9OWHc1WjUiLCJtYWMiOiIyMDhiMjNiY2FjN2VmNjU1YTI1YzAzZTllNmM5YzIwOTE4MjAwYTE5Y2Y0YzU3OTA5ZGJkYTQ2NzgzZjA5NjMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZLVStMYS84dVgzc3JmdExwMlBaVUE9PSIsInZhbHVlIjoicnJSbXRhcWd6aWdHUm9UYW8vamkxdGpWdVlUMHV1MXpjc2dORzhmdmNpazJxOThpczBoUDVwUWdsQ0JxVHFBQnAvS1FkY0pwZDFaU0R0Ukl4ZCtiSGNSclZNRDZ6SU93dml0UXFJSlhXdklvcXRWZ3NiZlpsWW1hZ1VUZUJZaks0ZUxoUHh5a2ZoaTU0Zk8vTmlaenRsNjZnR3NlSVFFWjIvU1RXTUd6V1BUYm16QUV3UGNtYWtuMWJWSVBLVWxoTmJTa1ZaT3VuOFk5R3g4SGNTbzFHMmtIa09MOGEvVUp3QTRLZitERmI3NUY4N3lFUWJPd0xZblJrZm1iaTk1TE9yQndNOTJaMWw5MVJZeW5FclFHSEZTOG54VkpPcU9JcHg4MVF4eXc4SGlGQlJ3NHhmNEJRenVDQUVnRFdRdk9lUHdKRFZaWHh2S0JaUWtpZ2txWnBuV0poVDUySEZxbE9ZUkJnZEMxdUFxY2NiZjh5K04wNzdqTExFZ3cxM0xkWEF4am9XVm95eHA4M3o4TlU2RnJUcDNKVng4UW5LbXk5WmdOT054bWtKU2FmRDhNVUVEem1JL0dOWlRiRjFuM0p5VjVUeHlCZnZuSlRZaEkzOWtUd215UVB5YWJzWGRZeFcvdXZ5amlNWEV0aFZSazRrd1FUaWViK2lVQVhSSlAiLCJtYWMiOiJjNGFjZGEwMmFkZWI5MDU5OTIwMzMyNzQzNDI1NzUyZDZlZjQ0ZDBmMzA0MzdmZTIxNzNmYzdkYmRiY2M1ZjJmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084546803\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1675908618 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675908618\", {\"maxDepth\":0})</script>\n"}}