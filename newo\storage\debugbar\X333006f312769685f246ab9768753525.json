{"__meta": {"id": "X333006f312769685f246ab9768753525", "datetime": "2025-06-08 13:05:10", "utime": 1749387910.023682, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387908.68473, "end": 1749387910.023726, "duration": 1.3389959335327148, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749387908.68473, "relative_start": 0, "end": **********.853286, "relative_end": **********.853286, "duration": 1.1685559749603271, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.853308, "relative_start": 1.1685779094696045, "end": 1749387910.023731, "relative_end": 5.0067901611328125e-06, "duration": 0.17042303085327148, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45577712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00714, "accumulated_duration_str": "7.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.94071, "duration": 0.00483, "duration_str": "4.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.647}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9719748, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.647, "width_percent": 18.347}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9898221, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.994, "width_percent": 14.006}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-434188131 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik14Vm1iTnZCV1lhcEwvZ1pVNmt2aEE9PSIsInZhbHVlIjoiaTM4YWhKaWJOakgrQ0FNV2ZIZ1FuZHo4cUhNYjBlUGVJRVk3Z09OWkpYL3dzODl1Z2l0K3dYM1pPUE4zcGpZWHBiU0RseGRIdTdFMUtNMDY5bENwRTA5ZVg0WUEzVnJQaDd4RjlNaUNtYnNmeUpkVWV1K04rL3N6WEhKYXV0a1I2NHlnU2lnZUw4bXk5V1U5aS94RFFzU3BMeEZSSVpZM0hlRU9uZzVqZ0ZLNERoMmpteGx2Z0VLUEFCaTA4YmwrN29jS3ljSGh1cURNU041TUNLTUpCa2EwVC9oSTNYZTBjSVlKai81U3FuOHV6cVZ0TGw1VHJWUXo2c01QZnQvVDlJTElGb04xU2V6Rkpkc0dIVCtSejZGNzltL1JwL0k0MFFEZDF6THpoRDQvNlV0MGpyR28xVE9yQlp6RUFvZHV3UUNWT2RCeXBPM3htSkJXZDZLZTkzSUxzbmNOQVRraCt1ZG43V2NIdW0wbnpCVUVBd3FISTFKbmFwcWhRSXpuY21UTEVLMWVYNlovRlhtVEtUZ01mNGVzbUVQOTl6Rno5NEdjZ2IrRDZRMHhoMjUwaWM3aHQyVXIzdlQxSERVZXVFbDJsNi85NzEwMnBrRXU2WkhyMllQMXVpSlBBeGhVbnB5MWlqVWEvQXp2SWVHRy9OVExuZklNaWRMSy9Ob1giLCJtYWMiOiI5ZWUxMzljMjVlYzM1MTAzOTU3Yjc1MWYwMzkyZDlhNTk3YWZlY2NjMTQxYjI5N2IyYzkyOTFmNGNlOTM5M2QyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFMeHVTOUlxWkNQT1JOL1RDWGRBVGc9PSIsInZhbHVlIjoic0pmc01uUW4zQ2FTZkRKSDl1T0pvTWZCN0I3NzlqQWhJNlZiUVF3QmZKNHhEN0xIc09MVmZBTWNNblhvSFF3TURkUDBSUTNqcDFzR21hL0VEOEpQc2pBcWJrdHdISWdPTHlQZng5QldZU1Q3WTNNbnA2T3BMVW9keURRc212MDZ6c0tqL3pUMnE1b1Y0US8xL2dLK3FEdlJneXNjN1VDd3oxOFMwSGJwWEdXN3VlbjhzVTNodHRzbHhYN2dPUGJWMGV1Uk43SHJUTFVqaTNoT3NiNlhIbE9LNFNlREY2YzhySGg5QTluck5xQ01obGlXQXVLSzlqOURaZnA3RFl3VkxTV1Z6aFR6eFVaVTJmSjhyY3JBaG9nOTBxaUR1US9tNUVXa0xaTXdzK0VZTzE5d3hyRnhNOXUrSGNXVzM1UWViT0NaQlNEdGlWdmpPcENIcGpmVlZXYkNUNnZxb3JWdU1RY05kT2llei8vOVNIRlZIQjBtVzQ2Z0JLRDVMTFRIRUxMN0ZKeFp5bFhxYTNjVlpKdTVuelpvMVZMUDN3VERhNG54bXcwUnNmL3dVUGYyWnpRK2htOVZ4RDRSUXRQY3NKNUt4Vk1OenRTeVpIK3JRMHcvWnhVcHJuNHhjOVNZOERJd0RxcXZqNkJOaXdIZ05idE44UWJscDM1ZlUxZkciLCJtYWMiOiJmNjU3ZDM5MWEwNTVjY2M4NDVhZjU1NTkwNWU0NWU0MTk4NjIxNWQwNzM4YzI4OTJkZGM2MjdjNDE2YzVkMjA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434188131\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-415417522 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415417522\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1911451699 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik82bDF0K1lvVjVxV0l1SUl3a1BoL3c9PSIsInZhbHVlIjoiNE5pVWZCTjAvOU9WeGpLdmM0Szh5R3ZyQ3I0d0xwTHhnbTRlVExNeHJ5Y2E3M0VlQXo1dzEyNlpJWHJXU3FMd0t0aGVGeDBhbUxkazZ1V2krUFBrZVpyVlJVRmZtV1V0Z2tMRjYzNjU2emQ5TG16eUp4NmdFZ3d3NzhRaG1md0FuNFFSV1N4MVVRRzJhK0xpRGxkUDBJckllOGpkVzQwRWQxamFJcWNnRC9JaElJbEcxTGplT20zN0VTVW1mQWJOK09SQ3pUVzFOd1phOHpRM1ZySFlVT1RGRHNFT0ZNQjFjRDRPanRWTmg5Vyt4Rlhkd0lJRHp0MDV1bTV0L1pZbkloZEJvZ05kaGRsdUkwTnE4c3dZU3o0ZnhuRDlHdzdTWXJsYjBOdVBhNmdzYzZVckZnYVBRQ2M0VUJxSmkrYjRkWkxxOS9Yd1g3QUdFK3Blb3RKZ00rQVF4S1RhUC8vY2JVcERTME05WERxUnhEVVlsNDl5NHAyY2kzaWF0K21jNVE1dnFXL1pla3FhV3VMSDA2dmZPbjBHK1JReklneGYwZ1dxenpnK3VnNk1GdEhSODVRV1RBS0MxMW9ja081WVpqYlVNbDRQa2o0YkJML09ONEptdFlxZUpJWnJkOWs0eEY5VVl2UzhPQnphTXR5KzErNFJOUWgrbjVYb3IrUEsiLCJtYWMiOiI3MDA0NDFlM2RlYzU4OTM2YmU0NjkwMGVkYTEwZmRkNjc3YzI3ZmQ5M2YwZWVlYzQzYjQzOTY5MzU1MDlkMjliIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9ydFpQYTdUMXJrRTg1aThQWWtNdmc9PSIsInZhbHVlIjoiUUpwdWhDN0c0bk9uL2dYOTNrSXNGMU12Lzd6SVJjbE9sUy9aUUpObi9iN1FSOHdaQlUyVEUrN0RCNVhpcnNpdlRUakpaeURjemFUVG9UWUptalZUTDJGRDZaSCsyeFhwcWVtUVNuT0cxZkJXcU82V2kyMVJuVGdKMC9UdS9oa3RaRGxrNjFybEZtNExyOVZJTGFBeXFkU05uMmVoT20xVVFyR0M4dnpzK25LYkdnSDFzdEFJYlBveTBiQ092NnR6VXdVQ3RCc0J0OWZqTkpBVjRVaFdXejBsZWlRWnUzU2QzUGJqS05xVFpjaCt6V2lkS2ZnMWhUejNnMVRMaTBkSnZnOFcxTXlPakFMSVpBRTlRUFFDaTZnS0dHRDljL3FFVFcvOUN4c251dHFkQnFxOW4zQUhWUlMrdHhuVjVyT0NXL0Jqck0yeE54aWdCYXl3UXVMQ3p3dkZnWktNUlpPT1J0U3p3SnpRWUxLZGN2Ynh1QUMwVTJjYkxUQ2lDNWVPdkw3ZlNua1ZjdGYwMVgzTzZJSUFLMHFDL0JqUXlVWFk5S282cVRDdjY1c1pEU2hDN0thQzhNL1o0dEpSREVNZC9UUXV4bldqd1E4ak5qa0pXY0F3KzhlVUlxMzZLQkF3SU9OVFhYWThneVoxM0VjSE9XZzhPTkJmR1M0MW12ZHIiLCJtYWMiOiI0YTBmNzdlZmMwMDg4MmM1YjhhYWQ3NjYyNTI2MzY1YWU5M2VjNzNkMGVkMDhmNTQzZWExOTI3YTE1ZTkxYWYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik82bDF0K1lvVjVxV0l1SUl3a1BoL3c9PSIsInZhbHVlIjoiNE5pVWZCTjAvOU9WeGpLdmM0Szh5R3ZyQ3I0d0xwTHhnbTRlVExNeHJ5Y2E3M0VlQXo1dzEyNlpJWHJXU3FMd0t0aGVGeDBhbUxkazZ1V2krUFBrZVpyVlJVRmZtV1V0Z2tMRjYzNjU2emQ5TG16eUp4NmdFZ3d3NzhRaG1md0FuNFFSV1N4MVVRRzJhK0xpRGxkUDBJckllOGpkVzQwRWQxamFJcWNnRC9JaElJbEcxTGplT20zN0VTVW1mQWJOK09SQ3pUVzFOd1phOHpRM1ZySFlVT1RGRHNFT0ZNQjFjRDRPanRWTmg5Vyt4Rlhkd0lJRHp0MDV1bTV0L1pZbkloZEJvZ05kaGRsdUkwTnE4c3dZU3o0ZnhuRDlHdzdTWXJsYjBOdVBhNmdzYzZVckZnYVBRQ2M0VUJxSmkrYjRkWkxxOS9Yd1g3QUdFK3Blb3RKZ00rQVF4S1RhUC8vY2JVcERTME05WERxUnhEVVlsNDl5NHAyY2kzaWF0K21jNVE1dnFXL1pla3FhV3VMSDA2dmZPbjBHK1JReklneGYwZ1dxenpnK3VnNk1GdEhSODVRV1RBS0MxMW9ja081WVpqYlVNbDRQa2o0YkJML09ONEptdFlxZUpJWnJkOWs0eEY5VVl2UzhPQnphTXR5KzErNFJOUWgrbjVYb3IrUEsiLCJtYWMiOiI3MDA0NDFlM2RlYzU4OTM2YmU0NjkwMGVkYTEwZmRkNjc3YzI3ZmQ5M2YwZWVlYzQzYjQzOTY5MzU1MDlkMjliIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9ydFpQYTdUMXJrRTg1aThQWWtNdmc9PSIsInZhbHVlIjoiUUpwdWhDN0c0bk9uL2dYOTNrSXNGMU12Lzd6SVJjbE9sUy9aUUpObi9iN1FSOHdaQlUyVEUrN0RCNVhpcnNpdlRUakpaeURjemFUVG9UWUptalZUTDJGRDZaSCsyeFhwcWVtUVNuT0cxZkJXcU82V2kyMVJuVGdKMC9UdS9oa3RaRGxrNjFybEZtNExyOVZJTGFBeXFkU05uMmVoT20xVVFyR0M4dnpzK25LYkdnSDFzdEFJYlBveTBiQ092NnR6VXdVQ3RCc0J0OWZqTkpBVjRVaFdXejBsZWlRWnUzU2QzUGJqS05xVFpjaCt6V2lkS2ZnMWhUejNnMVRMaTBkSnZnOFcxTXlPakFMSVpBRTlRUFFDaTZnS0dHRDljL3FFVFcvOUN4c251dHFkQnFxOW4zQUhWUlMrdHhuVjVyT0NXL0Jqck0yeE54aWdCYXl3UXVMQ3p3dkZnWktNUlpPT1J0U3p3SnpRWUxLZGN2Ynh1QUMwVTJjYkxUQ2lDNWVPdkw3ZlNua1ZjdGYwMVgzTzZJSUFLMHFDL0JqUXlVWFk5S282cVRDdjY1c1pEU2hDN0thQzhNL1o0dEpSREVNZC9UUXV4bldqd1E4ak5qa0pXY0F3KzhlVUlxMzZLQkF3SU9OVFhYWThneVoxM0VjSE9XZzhPTkJmR1M0MW12ZHIiLCJtYWMiOiI0YTBmNzdlZmMwMDg4MmM1YjhhYWQ3NjYyNTI2MzY1YWU5M2VjNzNkMGVkMDhmNTQzZWExOTI3YTE1ZTkxYWYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911451699\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}