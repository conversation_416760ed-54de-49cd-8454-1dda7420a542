{"__meta": {"id": "X4232039067231b531a3f227c498a629a", "datetime": "2025-06-08 13:30:50", "utime": **********.909423, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389449.617468, "end": **********.909454, "duration": 1.2919859886169434, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749389449.617468, "relative_start": 0, "end": **********.673472, "relative_end": **********.673472, "duration": 1.0560038089752197, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.673493, "relative_start": 1.0560247898101807, "end": **********.909457, "relative_end": 2.86102294921875e-06, "duration": 0.23596405982971191, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131096, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.013759999999999998, "accumulated_duration_str": "13.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7758489, "duration": 0.00618, "duration_str": "6.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 44.913}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.806952, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 44.913, "width_percent": 8.43}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.856425, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 53.343, "width_percent": 12.5}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8629801, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 65.843, "width_percent": 11.192}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.877695, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 77.035, "width_percent": 15.044}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.888377, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 92.078, "width_percent": 7.922}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1178948148 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178948148\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.875122, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1186753186 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1186753186\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2076615308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2076615308\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1706429420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1706429420\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1697687638 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpLcktuKzFBK1FReCtxZ3o2c1VHSHc9PSIsInZhbHVlIjoiK1Y3WXEvN3R2WUc0WnE1bmk1Slo4b1BMZEhuR3BjNkNHNlBKTjU2OUdFVzF1a2FBcGtpREd1THlweXB2L0YwY1NaOW42Qno0SmMwT1lqM1hUcG5jWGYydUZQRTlLeXh6dUVkZWhtTWo0TzFzUU13dTJxdDU4SnZDT0o2UDNvb2NrT2lLcHhDY3plOHZiQzhwQmxuQkg2d2VMbmdQdFVhK0ltaFc3UnM5RzJjellMbVBPZjNBUVp6ekVjejF0UlB5TW05N2ZTaTlqdnViTG9FWFA3UndpZm1xcks3R3ZlVGc4d3c4bW9iZGFaSm14bkdUaE54bXpSZnN0V3ZMeXdtYmRMWWZpRVZDMG14cFJqNXR4OExCY2tzSWZLRlVLQUswQzhvdVk5bkJGRDMrdVJEUzJuUElqQWZodTIzQVRUSU1iQm9VNWs5TVp3RDRVdTNOOFQvRWFmczJnMGwwaVRWZUp0aSs2ZmhudnIzN1FwWGpQckJXRFVRNjhKMUR2OEpiT2VTeG9ZcEk1VUhQTm9WRUlyamc2R0FBRGpkSGxObkhtZEd4dEFEM1BYRmk0TmJaNTJpaytGZ1pyc1VEM2xMZzk2UnljQVY4QWRLeHk0U1lUYVg2NUFTY0xWeG5XbTRjVXpvY1laSi9Gc24wd2dVUjZ5emJtTnBQZWxOTFN4UFEiLCJtYWMiOiI0OTY5ZmUxZGFmNzFiODM3Y2JlNDVhMWIzZGY5YWE3NjVjY2MwMWFjOTdhNzZiYWY2OWFlNDY1ZjhlYTcxMzcyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllUaVkyTzM4Z0dVSUV6VE1DeXEzb3c9PSIsInZhbHVlIjoiSjhra1NSK25ZZDhXZmJMbURZVDZrR1dtVlZnTE1YWHhBR2NIYmNxUndMbWk2dSs0TUZER3JYMnY5Nzl0NjhRcmc0aVNwdGlzUW9VZzR5N0JTM21PQkN1eHlqcWRGR1IzSFhpUkRhaU5na0ZYdHBsMWY5aHZ6cW5zRi95VitnK1Vkd2RRTDFvaXQxL1J2ZmtGRFZsTDM2Znl3ajhESGtOVXg3QmtzV3A1d3ZrY0tFd2ZJREovZTZkTnExRjlBZXdWdGpqdlpQSXRRYXp2Z3h1cDJjMGVQQTJYSHlFUmlnTHlqQzByYVFaNnZzRzRtYk04MkI5NkFjREI4bnJWSysxaHRvdUpDTHMzRU5oQmlyZWlZV2xsOXNVd1kwY3JDV1kwQllTQ1JQdW5jT0N3UUVhai8yMWtqNy9YdkVkelROVlFteFpxbUF4eXNnOFQ2TWJFRUlsdS9MRW1ETy9VR0lQUVp0akthK3NnN0YxaHV1WWFyQ0t4c2syekhubk83TWJ1bVB5T25LN01iVlN4enpVMU1BL0pEckZBRjZVQjlIYVRyemN1MVdDa0lJdVJNOThhMzdpRmhjUzBpejhPeUNpS1gyVWh2RUc5RnF0NmZxYTBYU1lEUzFVbDFEbGE5d1gvU3pIZ0lWTnFsdnF2anl1d01ocjYxbTJaY3JTVTd2VnYiLCJtYWMiOiIyMjhhOTI1YjM4ODI4ZDVjNjU2YzgxNjkwNDQ4NmZlODk0NWUwYWMyZjRiMWZmYmRiZWY5MmRmYmE4N2JiNDgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697687638\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1492839557 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492839557\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-957584781 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:30:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpnWkxrTWQzeHRYQ25vdW1LMGlGd3c9PSIsInZhbHVlIjoiKzB5cFRibFJJbVJUbGZndHRzRVpBakZoTzN3T25xOEREMFZxcWc1NFJReGJveWsxbkh1MWtXdlptL3RWSnBRS1JUUnNUSDMxelJxMTdDanJaMHgyZENacFNHdW9FUmJFT3NweUZHUklQV1JCSGw0c0ZmUlV6dS82eTEvdXNzYlg2M3RmSHQ4Y0FSMXlnRlVjVEM1eE42Q0N4UCtFaFBLUXVGSTNqejV3U0VmbGZKeHgzaE9JVm5KR2VqckZ4Z1pjckg5NjhqM3RhNTdZdlF2NGYzMWFwdWh1M0xVbVFnNkxEM3ZUY1JDUHczOVo1SGRBR2d2TnY4Rk53Y1FuTlFjWVVYS2thT25nVFYyT3FGWUNoZzRnYSt3RmRSb0JOMEFrc0dBdVEyS0FDV1YrV1E0cENONHcvdVo5WWEvVEdXRmM2K2FXcUI2dFJTNmQxRU5MOE9NTmlHNWxYbUs2dHp3bW9uejU0SmQ3NzVqWW90MldERFpMU05HdDZsaFplbEhaRnFTMHh4eEd4UUlwVjJ6NTJaOTdmU2t5OWV6aC9kanlXaFJlTHJoV2c1ZTBkTmRiQ1Y1RHN6OGE3cGtSd2c3WmsvZXRBZnB3MFYxV2ZUeTJsWGY1eUdVaVMvODFTaGh1TEQwd3ZQVFU0UENmWitaN0kyVDdJSElMUXFLc1BmRCsiLCJtYWMiOiJhOGEzMWVmMmQyMzZiZDE3MGM3NzQ1YmZjMzVjMGFmZjBhYWRiNWEzZTk2Mzk4MzYwMDlkMmViNTM4OTFhOGY4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:30:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVMaFJBQ1dmTjIxUWZDeDVXcWJxcUE9PSIsInZhbHVlIjoiWlkxbFVaeFJFVFBTM3RMcHZqaDdNNTd4YUIwbEJtL0M0dlB4VXhjd2kraUs4enRGa2FDVk9hbTZpclh0bEhwalppdWtabFFiVHpEdWtvdE5zQjUwaXhVQVhqcEd0T2NRN3pzS25XQk4zdS81ZjFiNjZ5aW12Mk5GeWk5N0dweW1MMW5pQzNXdlFHZUVyMTRZK0RiUW1CKzYwNjZWL1czNmszYjlrZ29Xb0ZNL0xqMWY4cEhyVm0welloNUI0NmpIVnRTS0lvM2M5Wkd3bnRyWUs0bklHTk9wM0JmSWhNMzhNMy90MDhjdzlFRXRPTVhCaFFxVmI4dTEwUWVnZkd5Wkh6M2hwRHFXWmE3K1c0T2Nyb3dPeWluMWNoWW04enEwODVJZ1h3QXp2Q0kzZU9kYUE5N0FVUjVOSkwwUm1EakpaVWhudDlleDNxV3BsUU9ZdFA4L1owUnluYUI1RVNNTG1QY1pkWWl3OE9SeUU4Mis1dHFGWjE1MFNIYmw5LzB0RmpVR3lnRjNHY1RyMzBIS2lKSVlNNURUejVHaEFPcmFUbEh1cEJPQU91VXhid3VaUXR3UU04ZzRlTUxJUGVWL1Q1R2RuSEI1NTMwYXFWVURMdE15czlXS0FHOEVHRThZTUdlb2NRcDFNbW15a2JtbWliRU5XV3hzcjZaVGg4V2giLCJtYWMiOiI1MWI4NGQwNzdlY2I4NzViODUxY2Q2MGZkNjVjOTgxZDBlOGQ3ODk1MDlkYWViZGQ2YjE3M2QyMzZlZDFjMzc3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:30:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpnWkxrTWQzeHRYQ25vdW1LMGlGd3c9PSIsInZhbHVlIjoiKzB5cFRibFJJbVJUbGZndHRzRVpBakZoTzN3T25xOEREMFZxcWc1NFJReGJveWsxbkh1MWtXdlptL3RWSnBRS1JUUnNUSDMxelJxMTdDanJaMHgyZENacFNHdW9FUmJFT3NweUZHUklQV1JCSGw0c0ZmUlV6dS82eTEvdXNzYlg2M3RmSHQ4Y0FSMXlnRlVjVEM1eE42Q0N4UCtFaFBLUXVGSTNqejV3U0VmbGZKeHgzaE9JVm5KR2VqckZ4Z1pjckg5NjhqM3RhNTdZdlF2NGYzMWFwdWh1M0xVbVFnNkxEM3ZUY1JDUHczOVo1SGRBR2d2TnY4Rk53Y1FuTlFjWVVYS2thT25nVFYyT3FGWUNoZzRnYSt3RmRSb0JOMEFrc0dBdVEyS0FDV1YrV1E0cENONHcvdVo5WWEvVEdXRmM2K2FXcUI2dFJTNmQxRU5MOE9NTmlHNWxYbUs2dHp3bW9uejU0SmQ3NzVqWW90MldERFpMU05HdDZsaFplbEhaRnFTMHh4eEd4UUlwVjJ6NTJaOTdmU2t5OWV6aC9kanlXaFJlTHJoV2c1ZTBkTmRiQ1Y1RHN6OGE3cGtSd2c3WmsvZXRBZnB3MFYxV2ZUeTJsWGY1eUdVaVMvODFTaGh1TEQwd3ZQVFU0UENmWitaN0kyVDdJSElMUXFLc1BmRCsiLCJtYWMiOiJhOGEzMWVmMmQyMzZiZDE3MGM3NzQ1YmZjMzVjMGFmZjBhYWRiNWEzZTk2Mzk4MzYwMDlkMmViNTM4OTFhOGY4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:30:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVMaFJBQ1dmTjIxUWZDeDVXcWJxcUE9PSIsInZhbHVlIjoiWlkxbFVaeFJFVFBTM3RMcHZqaDdNNTd4YUIwbEJtL0M0dlB4VXhjd2kraUs4enRGa2FDVk9hbTZpclh0bEhwalppdWtabFFiVHpEdWtvdE5zQjUwaXhVQVhqcEd0T2NRN3pzS25XQk4zdS81ZjFiNjZ5aW12Mk5GeWk5N0dweW1MMW5pQzNXdlFHZUVyMTRZK0RiUW1CKzYwNjZWL1czNmszYjlrZ29Xb0ZNL0xqMWY4cEhyVm0welloNUI0NmpIVnRTS0lvM2M5Wkd3bnRyWUs0bklHTk9wM0JmSWhNMzhNMy90MDhjdzlFRXRPTVhCaFFxVmI4dTEwUWVnZkd5Wkh6M2hwRHFXWmE3K1c0T2Nyb3dPeWluMWNoWW04enEwODVJZ1h3QXp2Q0kzZU9kYUE5N0FVUjVOSkwwUm1EakpaVWhudDlleDNxV3BsUU9ZdFA4L1owUnluYUI1RVNNTG1QY1pkWWl3OE9SeUU4Mis1dHFGWjE1MFNIYmw5LzB0RmpVR3lnRjNHY1RyMzBIS2lKSVlNNURUejVHaEFPcmFUbEh1cEJPQU91VXhid3VaUXR3UU04ZzRlTUxJUGVWL1Q1R2RuSEI1NTMwYXFWVURMdE15czlXS0FHOEVHRThZTUdlb2NRcDFNbW15a2JtbWliRU5XV3hzcjZaVGg4V2giLCJtYWMiOiI1MWI4NGQwNzdlY2I4NzViODUxY2Q2MGZkNjVjOTgxZDBlOGQ3ODk1MDlkYWViZGQ2YjE3M2QyMzZlZDFjMzc3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:30:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-957584781\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-328205490 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328205490\", {\"maxDepth\":0})</script>\n"}}