{"__meta": {"id": "Xab90395b4fcee5cdc64ed42192615323", "datetime": "2025-06-08 13:30:58", "utime": **********.901204, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389457.668235, "end": **********.901246, "duration": 1.23301100730896, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1749389457.668235, "relative_start": 0, "end": **********.766876, "relative_end": **********.766876, "duration": 1.0986409187316895, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.7669, "relative_start": 1.0986649990081787, "end": **********.90125, "relative_end": 3.814697265625e-06, "duration": 0.13434982299804688, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43922408, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0069500000000000004, "accumulated_duration_str": "6.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.865728, "duration": 0.005690000000000001, "duration_str": "5.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.871}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8811479, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 81.871, "width_percent": 18.129}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1292350920 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1292350920\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-352296943 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352296943\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1531227620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1531227620\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZ3bnpQZ0F3VWhCUEVwWVFjVFppYlE9PSIsInZhbHVlIjoiUmdZVitybUtRUUI1UEpsY3E1UElGREtaUFpjTStUTERnZzlYYmxFTGFTNDEwWkNiNkgxVWtFZWl1Mks4bXpFa3Vwb2t4TExuQXhRcmJIM1FmdSsvOWFMclJlZ0JaeHFUaENVaVgwYnh2RzlLOFFDRllZQ01xZE4xZWNWUHgxQWhwREV0Qmx2L3NYWXF6Smpnb0UveFpReXhuNXZieXpISVRqK2NpcEo5UkVLejlSV1crUjdBWG9kOWFGWXI3b1VZbndXTktVcTA5dStDYkhKRTBybGJ5eHZIbU4zOVpWNHZjbWNGM0FjMlVnS1pvQm1Da1FuWXF3SWxESzE1bVFZMTVlY3UzQmhMMjBWcTNwOXFGTnZDUW10dTR0bDllNGJ0YlZXa1ZaZTdieWdCUG1LcURYTmxZdngvdlBVR2pVN29MQ0Z5TURIRkF1bXdONFc0YkZYaWcvRGt3Zzg5QlViSG9jZDYxUlJhZHpTQ095VzNVTHZMb1NuZnlySWF0UVFpaGpwVmY5c1p0SGw5TlpETThyUmNUZFV3TU1BZndYMS9uV0ltVWNzMXl3VS9mYmtSVjU0azkyWFdBOHNCeDVWUS9jVUJlbVpuRXBnbGdPUmxRVVRjNUpubXRNcFZaUWVGVTM0TnVHNWVaNXIzSndSem9KRjk0V2xPTGkwcS9iTDAiLCJtYWMiOiI0M2RjY2FjMjgwNDM4MTYyOGNkYTJkNDdlNTJkZTg3OGU0ZTU5MmMwZTZiYmM2ODU4ZjM5YjgyMDM3NDMzNzExIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpicHA5emFhYVVkaE1wcW5tS3JwRlE9PSIsInZhbHVlIjoiaUFNYmMyWDNYYlBOZjRoL1laZm9XQmtzNnhWNHdDdUFlei91amI1Y3dSZ0RCbkxRUTlLLzVHejkwa1Q1T3I3OW1aZ2hqZ0Q5b21KNHE3WXdDY0J5WU1iR2pncERvaks0VkhWUUh6R28vMWtMRkR3OXJLSW96SmFEcDY3Q0xlWCtma1VtS0w1MHZ5UkZLUU1laDVnSDBEV3JRVHRSQTJWQWdxZEtVeWJSazVyNG82MGZIMmdCN0lkNWllSnZDRUlyZFlqWEw5VmxMRjdraFA5Sll5eWVSVG0zWEc3T0FVMGlUblJqN2ExNTgvM0hLZWxsRnk4eVE2M2h4Si85TytySlBZOFhkcEhpWjBlUkFZSmx1bnp5MWhWbG5FaW9nZ0UyUlRyWDZYWlR1blRidnR5NFpwd1l3MWFmamVoMXBPOXVlMlFOYWJpZTAyWGpSTHVibjV3WFIvb0pvS3I4TytzZDhHaERxSXhFQWp2SlhrL05vazVPWURQMnhrVnM4dWpuQTZuQ2J1RkFIckdEbDZpOTRhTkUyckUyZFdkQW45QXVrN3JiRHhmU1o0RlhJVWE2N0lPa1JOVGVjdzhKSTIvWTBlR3ZFc2lrNTBVL3dlN2xEcUFoZTcxM0dma1NNUkJQSklHeTI3MHJEOHBaRGd2cyt1Mk5KTjVlYzRoYnB4K2kiLCJtYWMiOiIyOTAyNWUzNjNmZmJkMmI0YjdhOTg1ODVmN2M1MWY1MTlkNjhjYjM2OTAwODA2ZjllM2M0NjMwYTUxYWM1N2QxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2016399143 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016399143\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1355603722 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:30:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik94bFkzU2ZDU1FOY2NtazU2dmp2M0E9PSIsInZhbHVlIjoidmFVeEYwZkpZK1lheGg0MDlZZlpoT2tNUWIveFJEKzB2cFlkeHR4YUJBejFGU0d2RkE2YWFyV3ZnTkxKNCtXMUs4UHAycXlVcG9ma1J1eE9HK2U3dWRuMzJjK2k2SHc0QUF0V1R2SmcyT0JoSDNIODRGZWF2cENjcmtQWTdSNGVsRDVJTkxLV3ZKOWlnemlaemZnS3JSZ2ZjakhkWlBCQjJQRG1LV0Q5YjBYblpQU0pNTDhad1YyK09aQmQzM2ZkVE5jZkozdEZWMmJkL3NoTnAwc3d0TFcrdlB2YTFGZjRaZzJUUWJvaCtkd0ttWFVVeWN6YThheisxcjY0S24wSG44VHFxT3B5MWdJeklYUjAzRU9BaGVGaXVDOCtQQzNTMU4vNnBzSS9yQVhqM0RGc1VxODJ1aE9neWpSNE1Saks3QlUxdUFuLzc2NHZid0l4YWdqTmZDMlMwZVc3c2Q1RmFHQU9rRTFUcVBkbGhoK3Z6bUU1eFIycHF5SDI4dzdFc0lZQ1pEY28rYWdkelNweDlmV2tPZ3lZMVY5cHFXTHJXRjJvZUlwM3R1SU1FK2QyVTY1bGtQQ0JkUGRtK2N2KzlMU1kvOWZzalZCT0t5L2U2YjVaQmdsVmZabnF2VjByNDRpbW5kRlVXL25iS1dscDRqOVg5bE1na2dGc0lrTVkiLCJtYWMiOiI2MjVjODUxNmY0ZWRiMWJkZGQyZWQzNTFiMzQwZGE0ZmQ4YzQxZDU2ZjkwYjg3YTEyMjk4OTU1Mjg3MTdkYzNhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:30:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJmc1U1aDUzMEw0amY3WThkZmNRY1E9PSIsInZhbHVlIjoiYmZBZlYvVUlnbEtsenVYVXBJcHROR2xZZ0lPcU5PSE5CM2xYZVlkUUFsWndUbng3SXpiK0FnYWtRSUxKNUh5bXhkOVBYQlRla21hSnYvQjhPZGpVZUNFcytvRXNGcHRsUGNCV1YrU3J3eHZ3WlRGOTVjY2FTNUlpMjBEMnFtRjZnOEllN3BnOE5Ea2dDZTRpem1sT3I5VXZvUlYzWGd6Mk15RlhWYmRyeWFySDhjZ3VGakI1cnAyR0NhdktqTXhlZW1IOVNqVVZwb2huREdMNjZzaGFhK2NjMG5RZ09rV2FDZ3kxdmhBSzRDdEdZQjhpaTNKNENURW5hUzBScnVaSVBJaC9tZk9CVXlnb2xScXoxTVc0QWgvNVNPeXRkOUxqT01Dd0o5UHUyLzI2cXBFTVZ3RG1SZjNESmMwL3lNOFp3eVlJcEhERTJGa0JSV2pINFBGRjlISlA4ajFpdmJJRmY5bE1YL2R2WXg5VnFmVEFlcXBwS3V1K296MGVramw1a0puQTlDQXJYRWMva2tUVXc5NlVleExBM25EKzN3YU41YXdEOHpWdWx1bVVVeG56bUxEYk05aU5DaVdCUjhZbmU1MGN0S2NRUHZjQjNyYzlsTklnNjdiWEFmbWJuMXQxaDN3LzZENGZyZTJOT0ZpZFUvY3kxS2Z4eWQxRTBDOHMiLCJtYWMiOiI2YWNjOTM2ZmJmNjMzNDQ5YWM2NzU2NDAxNjhlYTNjNTQxYWE5ZmRhOTlhOTc1ODE5OGIwNzhjYjE2ZGIzZTZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:30:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik94bFkzU2ZDU1FOY2NtazU2dmp2M0E9PSIsInZhbHVlIjoidmFVeEYwZkpZK1lheGg0MDlZZlpoT2tNUWIveFJEKzB2cFlkeHR4YUJBejFGU0d2RkE2YWFyV3ZnTkxKNCtXMUs4UHAycXlVcG9ma1J1eE9HK2U3dWRuMzJjK2k2SHc0QUF0V1R2SmcyT0JoSDNIODRGZWF2cENjcmtQWTdSNGVsRDVJTkxLV3ZKOWlnemlaemZnS3JSZ2ZjakhkWlBCQjJQRG1LV0Q5YjBYblpQU0pNTDhad1YyK09aQmQzM2ZkVE5jZkozdEZWMmJkL3NoTnAwc3d0TFcrdlB2YTFGZjRaZzJUUWJvaCtkd0ttWFVVeWN6YThheisxcjY0S24wSG44VHFxT3B5MWdJeklYUjAzRU9BaGVGaXVDOCtQQzNTMU4vNnBzSS9yQVhqM0RGc1VxODJ1aE9neWpSNE1Saks3QlUxdUFuLzc2NHZid0l4YWdqTmZDMlMwZVc3c2Q1RmFHQU9rRTFUcVBkbGhoK3Z6bUU1eFIycHF5SDI4dzdFc0lZQ1pEY28rYWdkelNweDlmV2tPZ3lZMVY5cHFXTHJXRjJvZUlwM3R1SU1FK2QyVTY1bGtQQ0JkUGRtK2N2KzlMU1kvOWZzalZCT0t5L2U2YjVaQmdsVmZabnF2VjByNDRpbW5kRlVXL25iS1dscDRqOVg5bE1na2dGc0lrTVkiLCJtYWMiOiI2MjVjODUxNmY0ZWRiMWJkZGQyZWQzNTFiMzQwZGE0ZmQ4YzQxZDU2ZjkwYjg3YTEyMjk4OTU1Mjg3MTdkYzNhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:30:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJmc1U1aDUzMEw0amY3WThkZmNRY1E9PSIsInZhbHVlIjoiYmZBZlYvVUlnbEtsenVYVXBJcHROR2xZZ0lPcU5PSE5CM2xYZVlkUUFsWndUbng3SXpiK0FnYWtRSUxKNUh5bXhkOVBYQlRla21hSnYvQjhPZGpVZUNFcytvRXNGcHRsUGNCV1YrU3J3eHZ3WlRGOTVjY2FTNUlpMjBEMnFtRjZnOEllN3BnOE5Ea2dDZTRpem1sT3I5VXZvUlYzWGd6Mk15RlhWYmRyeWFySDhjZ3VGakI1cnAyR0NhdktqTXhlZW1IOVNqVVZwb2huREdMNjZzaGFhK2NjMG5RZ09rV2FDZ3kxdmhBSzRDdEdZQjhpaTNKNENURW5hUzBScnVaSVBJaC9tZk9CVXlnb2xScXoxTVc0QWgvNVNPeXRkOUxqT01Dd0o5UHUyLzI2cXBFTVZ3RG1SZjNESmMwL3lNOFp3eVlJcEhERTJGa0JSV2pINFBGRjlISlA4ajFpdmJJRmY5bE1YL2R2WXg5VnFmVEFlcXBwS3V1K296MGVramw1a0puQTlDQXJYRWMva2tUVXc5NlVleExBM25EKzN3YU41YXdEOHpWdWx1bVVVeG56bUxEYk05aU5DaVdCUjhZbmU1MGN0S2NRUHZjQjNyYzlsTklnNjdiWEFmbWJuMXQxaDN3LzZENGZyZTJOT0ZpZFUvY3kxS2Z4eWQxRTBDOHMiLCJtYWMiOiI2YWNjOTM2ZmJmNjMzNDQ5YWM2NzU2NDAxNjhlYTNjNTQxYWE5ZmRhOTlhOTc1ODE5OGIwNzhjYjE2ZGIzZTZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:30:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355603722\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-405609178 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405609178\", {\"maxDepth\":0})</script>\n"}}