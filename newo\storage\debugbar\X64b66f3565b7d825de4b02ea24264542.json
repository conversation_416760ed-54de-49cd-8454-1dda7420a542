{"__meta": {"id": "X64b66f3565b7d825de4b02ea24264542", "datetime": "2025-06-08 13:27:54", "utime": **********.322379, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389273.065376, "end": **********.322429, "duration": 1.2570528984069824, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1749389273.065376, "relative_start": 0, "end": **********.158938, "relative_end": **********.158938, "duration": 1.0935618877410889, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.15897, "relative_start": 1.0935940742492676, "end": **********.322434, "relative_end": 5.0067901611328125e-06, "duration": 0.16346383094787598, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45186992, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014360000000000001, "accumulated_duration_str": "14.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.250841, "duration": 0.01183, "duration_str": "11.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.382}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.289939, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.382, "width_percent": 9.958}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.300775, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 92.34, "width_percent": 7.66}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-66571169 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-66571169\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1340485126 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340485126\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1476179113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1476179113\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxKOWxnUW1tQlQ3akVhY1ArNlB5L2c9PSIsInZhbHVlIjoia0J5czQ2a2xLY0w2eEVqandxY2syV3MrWEU0UUJkZFRqcE9sZFVYR3VRbEFLTGx2ZUdJaDRackhONFZxbFNNRzJsaGR0RkhxR1ZnK1dnd1EzRmVzYjF5ZnQ3YW1sR2x1bldwTWgvSFcwZVpzMzZjdHN4V2FzR1k1Smd1TkFwSllPQStGRXgrTll6c1J3M3hwRjJKVVc5Y0doK0VuR0xCRjJnbmI5YnRMOUpLcmRmMERETTFzVTNZaC8xdzhZVTJWd05hMFR4TGdVT2I3RTluRERFdnRxRW1wUlFPbzhwUkp6a21icjVRRmxZUG5GbGN2U2VtdGhSSytFbGpUYW5EVFVpLy9jbiswZmR3b1dBa0MvdEhHTTFxYm5qQmlQL0M2Ym9LamY3SzMwV3lTL01UN1cxSWF4YkMwUCtxRUpGbk1UYWlVSlhZbVVkc2VPYnZJdUxxZEYwOFJjNlpyS2pqeVNxNjRCVkNWSjNJbStDMFdZNXhuSllqenM3YytwVVpvU1picHNsa2w0VlMrYzhqTkdMOFdNNDNqU3BVTkdhcjhycnZ1d3QxSXJOcCt6Z2s5Ym9QL0ZjcTF2cnpZaG54dXhta0RKVjUvSitNZG03dEdDV1ExVWpoQ3FCVEpoa1h2dVF0enRzTGZOU2liaHE5L2Zvb3QxVHQzZmNUbjZyZFQiLCJtYWMiOiJhNzZiNThkOTkwNjI3ZGUwMDkzYTYzNGFmN2M2ZjFmZTI4MjA1NDZkZDUyMjY4NTYwMGQ1YzgwYmE3NzM1OTRiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im84cS9xeG5Rb0xvUGhGYnZCZ05pbGc9PSIsInZhbHVlIjoiNlNJVk4rZFhTT3NVZ0ZnWWp5ZDgwTzhpdjQ4TjBIWDlzcWc4c1luYkFDR0YrRlg1NlBoek5NTURoeW5LNTFhWEtuMVZubGprNEVPcHZXekpMK3IwMkdsLzNVODdyazg5V0k5Z3l0STZzNUFhdmpFRkovS0tmZlN6RTdsWVZSUHNQQURtWTVMdmkwV3Fmc1d4aUJ3M3Y1QlRsVjIyemRuTnN5Vk0rM3NVZFdFc1Bvak5xZ1FCT3lFelJoRXYzK0szYkxGMFFKT2VtVndCYkpTaVZGNWN5eVhsTS9acnQwcWxRaUYzdXA4RXRwa3F4N3E3R0c0bG9SQnp5V1luNHlUOVdUK2JzalZoN1ZwL0Y3ditlVEZ1bndlRWlEOTU4TytsN3hTc3YwRk4wQmZ5RFJBY2pjcEoxWlFUbWp6aUFhRUZzaEpsckdvWkZ6Z1JSUGljbTN0RVZiRFRMaUJVeWcreG9YSEoxZkJLaEN4d3VCVWVPS25aMDc2QW9tbTNnbi81Ynp2aXozRUkweHdkQkxENGNYc1ZqRHZpdGloTlBtbFdHYWZoOHBUUnZmK09RSU9hRi9TSjJ1SVZNYWxrc0VqZnREQm4yM05IV0xxbmEzWjhSUkpzeEkwQnNwWmtVeEhCWUFPVDRRMmdvUG1sQ1RuTnA0Y1Y1ZGZHa3JoVnBHb1oiLCJtYWMiOiIxMGQ5OTU1ZmIxMmQ5OTIzMjJlNDQ4OTU2Yzk1NWE5MjMxNjAwMjI3NDllMjYyZmNmOGVjNDVjZTFiNzE4NGM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1196939835 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196939835\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-281159819 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNnWVUyeEEyL1NBM3o2SlR4NjRobXc9PSIsInZhbHVlIjoiYXVRTmpLTXZPWGVxT3lvN1BpT0VMWGhZMkl3WGg5ZEplbTg5azFsVytuVHg3bGUzN2F5VmY0Z0xCTHNLQlM4Zi80YW92eHBqQTRKTldlTXZuWC9rTFZORnJ5WmowdDRBaGRWSTlkWkNTS0lEM05JN1hOUDdia0hDWXREQnNtRDVxQkNGK21qSUlYWGVEMGJiSmF2NkR5MkEvbUtyQUE5SHA4WXppTm8rRTZJb2xRcTF6OFBKY0w3cjVoSWd3S25jUlFCMTExYVloMHQvTHFjOU5idlkrUzlzZUlRZ0l2bUoyWFhINnFDTnl1NUdyQ0FMdjB0K0dRY2VkN2h0U0NlSmZ1UGovK25jM1lEanFXQ0ZKYzBTemlQallsTWtiQmJMcDN0NzA2VjdEcXMwSU02Qm5VS3ZLUTRGZzVJazV1bkREZlZwV3JwUFIzbjkwRzVSUmZMMUMzbDlzcWRGVm5RellsUWJpamNsNFhlV1EydmFrcVBqNVcxTG83dVJROXgwR2R4WkY3RnVDTmVqY0NnU3J2dUZHNnhnMnZ3NFNXVXRWY3FESkJmRkk2UGk0Tzdmbks2TUFwTWVOUmQrSEFJSGtTaXZQZk8zZXF1Qk5NU0huQXk2eERtcU1uUENLdERrWitsaFhiOTNQZnltTUFvTEdJcHh3cmVxaGNkVzhqVmoiLCJtYWMiOiI3MmE4YWE5ZjYwMmQxMzYxZTliYTA5Yzk5NTlmNDg4ODEwNGE0ZGU5ZTMwMDk5MjRkNWEzZGI0MDhhNTZhZWYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJQbVNWeFlmQW9MZFE0bHdzUWRNVEE9PSIsInZhbHVlIjoiMXU3UHFkNUV4aTNnTEthNGMxSGVZZTBYbjZENmF5VzlzWDhvNGRPc05OdVNmaHRuQ2toaE83bmJqd2F1R2tFVUloaVhxV3FBbXlVK0VKNm5MT2ZIZmN4eFR5eHdCWmxEbURaVkZUSDl5Y0JWRVBEaXlRdXRRNXBnL0ppWTBXRlM1SWk2YTczeUVRTlNzS28vMG8rNG9NUTJxMmY4dFpLK0I0RmxjWXNzV2Izb29uclJEQ09UVEN3RVNiQy94WU5aelFxaU5DNXBNN2xZRVlQWlZYL3BOcnRDZWJ3VUdUQituNWttU3l1Sjd0ZmR4ZFFiT1hxQU1PakRiQ3JpelluVTR5aFRGQzBudlZ4a1UwUE5weHN4TXdlRksycG9MbE4rNlM0MHJkN2tCRmxLeWFTdnRXYWF1NHF2ZEV2dVlFRUV1Rm15T1FEWE1vZ3UvQVgrQysyNlJtUC9pS2gyMG96MVJJaGNJUFdqdGNqM2F5Y2JQbVVmVEl2TGhoL1RvWTNhMUNLSC9tbkpEQVJuYi83R3gxdGVaa1BBR00rZDlFanNxK2ZkMTlkVGF5ZDJKempFQ0toR1M0YzRpTHRxRDZnQkE3WHk2TlJLV3lTT3hxT1lhMHE2cmM1cUY0d2JWY2dHVVFFeGdSTW1FWjNMM2xBQVVhSGFSTjQzelQ5SUVpM0UiLCJtYWMiOiIyNTg5ZmQ5M2NiY2FhZTFkOWU2ODk0NTBiM2E4ZTNlOWNiOTY3YjQ2NmNjODdiYzI1NmYwNGFlN2YxNTUzYjI4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNnWVUyeEEyL1NBM3o2SlR4NjRobXc9PSIsInZhbHVlIjoiYXVRTmpLTXZPWGVxT3lvN1BpT0VMWGhZMkl3WGg5ZEplbTg5azFsVytuVHg3bGUzN2F5VmY0Z0xCTHNLQlM4Zi80YW92eHBqQTRKTldlTXZuWC9rTFZORnJ5WmowdDRBaGRWSTlkWkNTS0lEM05JN1hOUDdia0hDWXREQnNtRDVxQkNGK21qSUlYWGVEMGJiSmF2NkR5MkEvbUtyQUE5SHA4WXppTm8rRTZJb2xRcTF6OFBKY0w3cjVoSWd3S25jUlFCMTExYVloMHQvTHFjOU5idlkrUzlzZUlRZ0l2bUoyWFhINnFDTnl1NUdyQ0FMdjB0K0dRY2VkN2h0U0NlSmZ1UGovK25jM1lEanFXQ0ZKYzBTemlQallsTWtiQmJMcDN0NzA2VjdEcXMwSU02Qm5VS3ZLUTRGZzVJazV1bkREZlZwV3JwUFIzbjkwRzVSUmZMMUMzbDlzcWRGVm5RellsUWJpamNsNFhlV1EydmFrcVBqNVcxTG83dVJROXgwR2R4WkY3RnVDTmVqY0NnU3J2dUZHNnhnMnZ3NFNXVXRWY3FESkJmRkk2UGk0Tzdmbks2TUFwTWVOUmQrSEFJSGtTaXZQZk8zZXF1Qk5NU0huQXk2eERtcU1uUENLdERrWitsaFhiOTNQZnltTUFvTEdJcHh3cmVxaGNkVzhqVmoiLCJtYWMiOiI3MmE4YWE5ZjYwMmQxMzYxZTliYTA5Yzk5NTlmNDg4ODEwNGE0ZGU5ZTMwMDk5MjRkNWEzZGI0MDhhNTZhZWYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJQbVNWeFlmQW9MZFE0bHdzUWRNVEE9PSIsInZhbHVlIjoiMXU3UHFkNUV4aTNnTEthNGMxSGVZZTBYbjZENmF5VzlzWDhvNGRPc05OdVNmaHRuQ2toaE83bmJqd2F1R2tFVUloaVhxV3FBbXlVK0VKNm5MT2ZIZmN4eFR5eHdCWmxEbURaVkZUSDl5Y0JWRVBEaXlRdXRRNXBnL0ppWTBXRlM1SWk2YTczeUVRTlNzS28vMG8rNG9NUTJxMmY4dFpLK0I0RmxjWXNzV2Izb29uclJEQ09UVEN3RVNiQy94WU5aelFxaU5DNXBNN2xZRVlQWlZYL3BOcnRDZWJ3VUdUQituNWttU3l1Sjd0ZmR4ZFFiT1hxQU1PakRiQ3JpelluVTR5aFRGQzBudlZ4a1UwUE5weHN4TXdlRksycG9MbE4rNlM0MHJkN2tCRmxLeWFTdnRXYWF1NHF2ZEV2dVlFRUV1Rm15T1FEWE1vZ3UvQVgrQysyNlJtUC9pS2gyMG96MVJJaGNJUFdqdGNqM2F5Y2JQbVVmVEl2TGhoL1RvWTNhMUNLSC9tbkpEQVJuYi83R3gxdGVaa1BBR00rZDlFanNxK2ZkMTlkVGF5ZDJKempFQ0toR1M0YzRpTHRxRDZnQkE3WHk2TlJLV3lTT3hxT1lhMHE2cmM1cUY0d2JWY2dHVVFFeGdSTW1FWjNMM2xBQVVhSGFSTjQzelQ5SUVpM0UiLCJtYWMiOiIyNTg5ZmQ5M2NiY2FhZTFkOWU2ODk0NTBiM2E4ZTNlOWNiOTY3YjQ2NmNjODdiYzI1NmYwNGFlN2YxNTUzYjI4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281159819\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}