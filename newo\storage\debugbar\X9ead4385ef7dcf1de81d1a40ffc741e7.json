{"__meta": {"id": "X9ead4385ef7dcf1de81d1a40ffc741e7", "datetime": "2025-06-08 14:57:39", "utime": **********.161759, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394658.447218, "end": **********.161787, "duration": 0.714569091796875, "duration_str": "715ms", "measures": [{"label": "Booting", "start": 1749394658.447218, "relative_start": 0, "end": **********.058485, "relative_end": **********.058485, "duration": 0.61126708984375, "duration_str": "611ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.058501, "relative_start": 0.6112830638885498, "end": **********.16179, "relative_end": 2.86102294921875e-06, "duration": 0.10328888893127441, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45604448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01749, "accumulated_duration_str": "17.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.097804, "duration": 0.01595, "duration_str": "15.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.195}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1323578, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.195, "width_percent": 4.345}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1453469, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.54, "width_percent": 4.46}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1150372715 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1150372715\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-815364521 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394651112%7C68%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpMRWlicy9LeVFJYnBCeDczM1FQS1E9PSIsInZhbHVlIjoiT2huT1lXMzAyN1BzVWRaQTdHRVc1MVhBSkxRNThIMWNISTFIVndQdW14YnNhc29vOC8vRU9TZHJpQmxLTmdxQzFoYytBaXVJRk1EZUJkdW9HdFgxR2x1K3JuZU1mcHd6N0I0anhwa2xmUWhqWHQwTDhrRE5vNlloaCtmcER2UEh2b3FEdkc3T2svQm1VYmpJSUx4ZnQvYWdCT3pmRDRqNDFDWVZNb3ByVnYwTHh5UExNdTFFRmlNWlFncGdNaG9LSFJzQzdFZGdxY09iQkd4ZGl1YkhqS2k0QWJ2UDRDWUN3RDQvdWFuREtYakhMcndadTV3aDduZk8xVHEyZTRvUU1mR0h1eXp1b3N3Z2dTTGJEUWRkSXE2UzhITWpFY1JlYkI3d3VsV1BGVElQMWV6WTBoQTVGaTY3enhld3QyT24xUEV4Zk1KdlZsSjFQM1hJQzl3QkFLZXBJMkJPU3lVZXBuS0dpcy9QL0pkbXNOa2R6aS9OSTJiQVpuWDJWeUFkMEdHaWFVT0J1WjlqNHVnaEZLZDhxSWhOblBQNWVhK0NYcTFSL2IySVVDVVhZTWRjZi9vcm1VNXQzMDd3bG9JdE51MXJMZXBhRDJuT1lRQTdDY3RQK0JQaEJPbEpRRHJRTHo2aDRyc0hpcHZyNGYrMjNadXpZTWZJNWd6MWRWNHQiLCJtYWMiOiIwMWU1YzkyODk4YTNkYTg5NDUxNDM2M2M4NWY3OTYwY2JlNDc2NmUwY2IwYzc1Mjk4YjkzN2M1ODU0YWI4ZjViIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkVRVy9HbWFPQS9qOEtjbzVGakNVZWc9PSIsInZhbHVlIjoiNEdxdURhK2l3bGhZeGNDWnlOS2pwQ1lJQktpYTBjSUIyT0hJOVljdVovNllPZjRyK2dQTUEzNlNyT29CTU0xdG9Id09DcEx3Y0RBUVJWRVExV0ptOWZUd3ZGM0VkdGNJQU03aC9Mb1cwU05lN1gyR0Voc2hxRHlmTHNVQTI4eUQra3NUam5vK0QyM2UxNkZVK3BzbGtGZW9teFMxREVtSjQyTm5WaTBtcVhPUGltZFBBOHp2RnFKRmtyQ3BxVFMzeUllZklUYUY1TVkyQUo0UlBGNk5XWmhoMEN2cG00UGtyQVpTR3E1M2p4RHNRYTRMWSttRXhXeW4zRERLUUJRYTFvZ3FwOFl2NzVOZ2FiVDRTZmF6WWhWOXNpRjlGbGIyeFpUK2lFaE5WZVlKTVhLQVlOQXRvQkNqajJXOEZsYW5XVHdjUElFQ01tQVZmQkE1K2o2OWFYaER5MnNIQkp5QVE5dzlaT2hSbVdyMTl0NURvckpSUHRIeHdGV21zVCt2eUpJN2lXelhHQ0JGQlRFS1hmaWhpRk9IYXBIVHJ5OVJOUjdFWkpaSFJGU3Ard0pjL29wNDRJSUR6WnBXVkxkM3daWWZzcUIwNnkwc05MNFlPWFhJWlFwK2JNbVhMRU5lbmlSTVhPYlFRTzFZTGNyK3pLTERUODB2T052OHVFeDUiLCJtYWMiOiI5Y2IyODZhY2E0NGM3NGUzZWEwN2VhNTMxZjVmMzRiYWE3ZTFiNDcwNTRmNGRkMzU1YTkxZWE0NzczM2M1ZWU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815364521\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-579577898 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579577898\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1800530023 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZ1cFRoRU1uUkZpMHBOaXhTU3lTdGc9PSIsInZhbHVlIjoibUphdXh0T2F5SjhFenQ5RmxlZzdQaVVnTVVWRUZLbHRRdEFibTZ4RENoeDBIanV1eHlNUUJpNm5kVVZBQ2ZyanNzYVdkWko5UlRHRHkwOHNZOWFsYVVhZ0tiL1huV2ZjN2xRUzZzdUh6NFlWZTdqRDZ5U01kcktwdFRtbnU4RmtPTFJjK0pJdWd5VnhyZHhTdU8xby90T0VBWVk3T29mRXV4NmVGMTdZTnQ4QnVCM2c2SEE5ZDJnSG5jZGdIOEZIUUN4b0piZnRDWXM0UWpUU3hFTys5Y0ZQWXJJVFVvVVlUbUR4Rm5Rd0ovQ1VCb01EMUtkUTRBM0tCWlBtTXFrQjlselY5aHpFUzJTTUtScDVlUWZDclVSaWFtVkI0cUpSZkFia0psbzl0R0x5NHN0UXd6TnBoVVUrV045U3U2MWpQVU9WNmEyeWtxWVQvdmM5Z0ZtMmFIK3dDa0lncklWZXhSNnZ3MHJKenVBZDNYeUJPYjhERlQweVNyS0M5NzljN3Q5Z1RHUHVzbCt1cUVqdEdBOE9JSENpcEJYb0RudVhDY2ZSeEh3Qmx6ZVdJdkhFUWFUQ0RmVnh2d1U3cHlGOVBSUmlRVnIxK1FtTFBrZnB2ODBVZEgrZ3VRUFNoOW1LNk5HRUJ2QkgvQUpuelNuYUhVK054UzFkdFpON0p4ZTciLCJtYWMiOiIwMGNhMTdmNTc0MjY5OWQ2OTA4NmIzYWMyNDBiNmY2MWY1ZjU3MjMxN2JkMTJiMjdlM2UyMDNhNTAyMzI2MDEzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1BMEdKdXFPSXI2d0Q1NlUydHI5VFE9PSIsInZhbHVlIjoiTXBxemtkYUFHbFZLd2EyRmNvazdYMmtjMTMzSW5VazV5RkVPQlQvMVZiZDF5VjhyMjFIN1h6K2NrZVZodmFuemNOTldPNjFDbTE2aXBJbFM4NUthOFMwWVFRT014M3I4RUEzRjNCczUzUUdNdktsTTNZZE5xcnUrK1psSWFIOStmOFkzUk9hV3MvM2ZuK2tNWVk1SW5TOEFScWdMNGV2ODFuTFAySmVGWHE1Q3NTSWxEeHo0ZXBrNDVVbHR1NkU4emRhYXRZblRUT3ZnUnBGbUxCN0FmUGV4OTdnTE01ckdVNDhSOU15NnJwR1lkalRCcGRkVlNYcTZVOU1UOHFwWGliaTQ1TEVUa1RRZjUwZjd2bVBueGpWVWRNTUhnQmdQN1NFSFJiWllXaE0rV0YyZ0ZFaEgrOTRXYTQ4UnlKSGdTVTNTVktsT2FVWGM5VEs2UlJ5aGZUeGxOeS9YVVpQVTZNcWVsUG4vcGkwQVgyT3FzWjJTVG8zV0E3SDlKSVRmd0J5TGYxOW1DWkh3Y2ljaDY2ZmZ4Wm9rNDFIZ1VmQTlnNy93a1VrSWJmamZEYWwrQmhoYnNGREdCYzQ3amRJSyt2anRQMyswd3VSUHlUR3RCNE95N1k0YU90ZnZYNGxVWXB2MCtaOGJWR05QYzVKUm1vVFlkendsVStSZFZTRFciLCJtYWMiOiIyOWI1MWZlNTJiNGViZThmMjc0Yzc4NDY0Njc2OGMyZDAyYjAyNjllMzE1NTEyZDc4ODFhNzJjYmQ3NTY4NzA0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZ1cFRoRU1uUkZpMHBOaXhTU3lTdGc9PSIsInZhbHVlIjoibUphdXh0T2F5SjhFenQ5RmxlZzdQaVVnTVVWRUZLbHRRdEFibTZ4RENoeDBIanV1eHlNUUJpNm5kVVZBQ2ZyanNzYVdkWko5UlRHRHkwOHNZOWFsYVVhZ0tiL1huV2ZjN2xRUzZzdUh6NFlWZTdqRDZ5U01kcktwdFRtbnU4RmtPTFJjK0pJdWd5VnhyZHhTdU8xby90T0VBWVk3T29mRXV4NmVGMTdZTnQ4QnVCM2c2SEE5ZDJnSG5jZGdIOEZIUUN4b0piZnRDWXM0UWpUU3hFTys5Y0ZQWXJJVFVvVVlUbUR4Rm5Rd0ovQ1VCb01EMUtkUTRBM0tCWlBtTXFrQjlselY5aHpFUzJTTUtScDVlUWZDclVSaWFtVkI0cUpSZkFia0psbzl0R0x5NHN0UXd6TnBoVVUrV045U3U2MWpQVU9WNmEyeWtxWVQvdmM5Z0ZtMmFIK3dDa0lncklWZXhSNnZ3MHJKenVBZDNYeUJPYjhERlQweVNyS0M5NzljN3Q5Z1RHUHVzbCt1cUVqdEdBOE9JSENpcEJYb0RudVhDY2ZSeEh3Qmx6ZVdJdkhFUWFUQ0RmVnh2d1U3cHlGOVBSUmlRVnIxK1FtTFBrZnB2ODBVZEgrZ3VRUFNoOW1LNk5HRUJ2QkgvQUpuelNuYUhVK054UzFkdFpON0p4ZTciLCJtYWMiOiIwMGNhMTdmNTc0MjY5OWQ2OTA4NmIzYWMyNDBiNmY2MWY1ZjU3MjMxN2JkMTJiMjdlM2UyMDNhNTAyMzI2MDEzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1BMEdKdXFPSXI2d0Q1NlUydHI5VFE9PSIsInZhbHVlIjoiTXBxemtkYUFHbFZLd2EyRmNvazdYMmtjMTMzSW5VazV5RkVPQlQvMVZiZDF5VjhyMjFIN1h6K2NrZVZodmFuemNOTldPNjFDbTE2aXBJbFM4NUthOFMwWVFRT014M3I4RUEzRjNCczUzUUdNdktsTTNZZE5xcnUrK1psSWFIOStmOFkzUk9hV3MvM2ZuK2tNWVk1SW5TOEFScWdMNGV2ODFuTFAySmVGWHE1Q3NTSWxEeHo0ZXBrNDVVbHR1NkU4emRhYXRZblRUT3ZnUnBGbUxCN0FmUGV4OTdnTE01ckdVNDhSOU15NnJwR1lkalRCcGRkVlNYcTZVOU1UOHFwWGliaTQ1TEVUa1RRZjUwZjd2bVBueGpWVWRNTUhnQmdQN1NFSFJiWllXaE0rV0YyZ0ZFaEgrOTRXYTQ4UnlKSGdTVTNTVktsT2FVWGM5VEs2UlJ5aGZUeGxOeS9YVVpQVTZNcWVsUG4vcGkwQVgyT3FzWjJTVG8zV0E3SDlKSVRmd0J5TGYxOW1DWkh3Y2ljaDY2ZmZ4Wm9rNDFIZ1VmQTlnNy93a1VrSWJmamZEYWwrQmhoYnNGREdCYzQ3amRJSyt2anRQMyswd3VSUHlUR3RCNE95N1k0YU90ZnZYNGxVWXB2MCtaOGJWR05QYzVKUm1vVFlkendsVStSZFZTRFciLCJtYWMiOiIyOWI1MWZlNTJiNGViZThmMjc0Yzc4NDY0Njc2OGMyZDAyYjAyNjllMzE1NTEyZDc4ODFhNzJjYmQ3NTY4NzA0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800530023\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}