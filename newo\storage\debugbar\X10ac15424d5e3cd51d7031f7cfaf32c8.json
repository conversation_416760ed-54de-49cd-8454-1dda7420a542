{"__meta": {"id": "X10ac15424d5e3cd51d7031f7cfaf32c8", "datetime": "2025-06-08 13:27:11", "utime": **********.510406, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389230.098139, "end": **********.510434, "duration": 1.412294864654541, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749389230.098139, "relative_start": 0, "end": **********.349607, "relative_end": **********.349607, "duration": 1.2514679431915283, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.349628, "relative_start": 1.2514889240264893, "end": **********.510437, "relative_end": 3.0994415283203125e-06, "duration": 0.16080904006958008, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45396120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020910000000000005, "accumulated_duration_str": "20.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4353871, "duration": 0.018760000000000002, "duration_str": "18.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.718}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.482796, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.718, "width_percent": 5.165}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.49164, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 94.883, "width_percent": 5.117}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1949070778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1949070778\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1919513050 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919513050\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1163464419 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjgvRHdWWG5XRHR5SUR4YWNOWE84Tmc9PSIsInZhbHVlIjoieElNdDFMOVlrTFhja3U0Ynd5cWpXNHpXNHUrUUdtdmwvVWpOT1NOQmp5Wlh4clAxU2FiUDFzMkIxYkt0MjBsOFJGZGdGMnhza2tUM1VkRStpM1RsMHdpM0pyenByRnZCQndHSGRyNEhncmFhVzhWK3pabTdZUHA2QWxwSnVqQW5udVJuSThGZDRpMkV1WmlvV0hieXJFUzVBMjdVWFBScFMybUsvZ2dBaE1jcWFmK0JWTFE0Z1VIa3E2NzJVR0xaWmJPZ1IwWDhwM05kc2NvSDh3czdWcGpUUjRoaXQxUmw2RFQrZzdyTGlsaGVieEdJaXp1L0NTaWFTSlVJSWJzVlU1SzdEZWh2b3pEaWxsWTI5ekVSdFV6MDRZK3dJV1J4bC9OM0VEZ0tiZVc0Vlcrbjk1WlNFK0NFZmY5c01YWGFDd0UvdDdJamhWS3A3Tk9kVEZDYi9hZWFqMUZGNk9mQ2ZGTVNMY2VUcDR3QkpGc2Y5cEFBeFZlWkl5TnNrSlErQktVS3RHaFNuYXN2SkdZOStwMmN5dlFhWEJLQ3pReU5lTHFKb0pFU1E5ZHJhR2ZNVzFoeDBHekIxcEw4SGpjUkQwR1RyenI0S29qS1ZUSVlmRlFBVU9taFNLWWhrbFRIYThCUjFteU5zUzNyMyt3d2RVazloeklaZk10aWdSaUciLCJtYWMiOiJiMWM0YzdiM2YxNjk3MjZkYmJiNDUzYzA5MjUxNTFkZmVjZWQ1NTdhYjM0NzRhMmIyM2YwMDk3NmFjMGQ2MWQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik95eXgxdWdVYzB5UVJ0VWNKOG03ZGc9PSIsInZhbHVlIjoiVlppczl2WTJKT1NOTXg1emRjaHpQVXNCdi92K2VyRlo1RkRKQmxrZVNGTW5pMG1qS3VuNFJDbEN2M08ycUxxZ0xNMkU2dEdNMm9KYTg2dnVsZTZXRlpycld2UWRkTGpJcTNkNXNLUGZiaFUvYlVzMXdMVEM0QnN0Y2VHRmVNZVF6Z2YraEVBaUNmYlZwYk9wbnJFcE1GalpaaTVBVWovcmtPdEd0SVJYM2gzUXhrNkRTSEpGWjFuVzF3ZmtacE0xRVI5VTVGb0xWY0NjdTBiTTNXV0FsT08zSGdlVFdibmhjWXU5VHlleTJnYmp5MEdwOWxFSC8zUmNYOXZKeWQ4K0VUeVB6dUJqS05RY3hKZkxXWGRmL2g0eElsdXg2aVpYb0ZHWHpZMjBhUXVmek1KZkJlSW1VclZRVVBKQkY4NzBlYmtVdE5NYU0reVlQWGZWVC9WNXJRMjhaem9iN1JGYlJvMDJMTlBqYzFFeC9XQ1lOaUgwUzMrSnhpYmhHaU1USW8ySGhET3BLd0ZZTVJXbmx4TlZjcHBDMUhEbThtcUR3c0VhQmluNlZhOWhWUmtqYjNVcDJCREJKakZ0QlFUUFVkWm5aOGV1VEZHK2lxcjVrdXVhVytZSVVIaVhmMHZXY2RxOU41dlNkN0FyMDlHZ05NYzN5cHlab21SSWo5S00iLCJtYWMiOiI1MDZjMGU2OGI4MDJlMjRjZWVhM2I1ODk2NjFmNzkzYWU0ZmQ4ODE2ZmE4NjBlMDA2MDI2Zjk1Y2YwZWYzOTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163464419\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-86082889 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86082889\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-17758552 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkwvSnY2L2hrSFhsMFF2cnN0M2RFNVE9PSIsInZhbHVlIjoic0VhQkFmeXBONWoyZmk5b0FuMHhMcWpnU0JXNEtWY0FObzdkN2lRVWROc3BZVkVLY2daeTJidHRTNm1idFREUE4rRy9VOElLSm9pWk13d2hOeGorYTM2L21ld3h6aHVMcDI2aEpubitaelREbWZlazg0RC92a1hFdWcyamNZRzZLTTQ0ampFOTE1RG83dnNVVXVBMWU5b29NNnp2cE13NzdnMHYyWElKTm1FZkljQ05MYW4vZkNEa01WMFpvczBKZjBwUmFaL1dMMkZqMkpSamJhWjVrYUUways3WjNvSG1SMGN3LzJBdUd5ZFFabXJOaWhyK1NrZ1lWbTRQQ3hsSnpOYkVzQitPYzRuUVFJVEJwL2FWUGNlVTk0Q3VCNnBVNzBzUFlKVWlPZ2VGTzFFWnVQWlNHV1ZvaldRc1VnT280NVF4ZXNCMDZqQmZuQWExTCtVS0h4TllHSXpjVkN5YzFUNVM4NkN3NmxqY3hpOUR4d1Y1MUN1b3JnQ1lEOXdybGg5MDZqZ3lGSmNqVTJKYWl5NWVCR0NkV0V4dGxSQ20xSWNkV08reWd3UmRNYmpHbGpjajhCTXRtQmhabzZGYWlaVlR4WHNHWmpvK3crSUJQV1M1bnRsaHVMV01aZzVQOHRrTXlIZE15OVRrK1NHOXYwdXJMeUUwcmtRRnVjd2wiLCJtYWMiOiIwNTBkNTllZThlNjFhMDVhMWQ1N2M5NGZlMTRkZjNhMWI4M2I0MjA5ZTZjYWI4YjAyZDFiZGViNTAxZDMzMzYxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZrLzVJYmdnYms1Vit3M1RGSGg0cUE9PSIsInZhbHVlIjoiRXZqWDZTWVBhcjhxdGlRcHQrSjNnZGJwRGZ2N3ozZS9QR0VNbytLNXJoeWdmRy9OWDRHQnBGclVqem9kUFNoSmg1c0FKWTZMZFovalRwZy9VUDNIRGhYOWxrQjVXZDZCbXVnWGVUSnhWandJcGY2VkR3SXJ3YXJLek00Uk9mRWpXSll1SzJjR2xSSzZzLzRhWHA4UEpqaWxWYVZIcjVMVlNsR09XRmtMa2JXQUFsRkxwVW92UHFGNjhHQkYwUXdCeENrcWNta0pLcHhiTFh6eC9OaWpTVEFBKysxNnBIU3dIa3VIOHAybVp2bFA0VWowblF1a01YKytyd0FZSk9FZFdLc01qamQvMVFGZ09JbzVKTFA5Yy9kUXZacWRYQ01hUThvNmI3RENaMFBoOHYyazJBcnRBdDVONFRsSXdvdkhJRXZSUHJ1aWQyRSthUVNEZjRySUJYNHRVc1QvRzcyMGpHZVJ5dEpzR0FXNXgvd2d5WFFCbXpQSXZtRzliZE0yeTRRS1FEMlpROEVRVmpxbXhNd3pEeWtxS0pYN2FwYkdGZWhVS2plMy8yVVg1UFlCd0tLSkRIdjlvQ1N1RWFYTFFiWEU1TzBZTzRRS2NOS3hYcXJwczlJb1JWR2wrUDhiR2czcHNESnBCT1hxa2NyVHI2MGhRMzR1NmJ1Yzdmbk0iLCJtYWMiOiJiYjE4YmE3ZWFkNTVmOGNiMDlmYzAyYzU0ZjhjOTk0MWJiN2QzODI0OTVmZDQ3NzIyZTkwYWUyNGI4ZGM1NGJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkwvSnY2L2hrSFhsMFF2cnN0M2RFNVE9PSIsInZhbHVlIjoic0VhQkFmeXBONWoyZmk5b0FuMHhMcWpnU0JXNEtWY0FObzdkN2lRVWROc3BZVkVLY2daeTJidHRTNm1idFREUE4rRy9VOElLSm9pWk13d2hOeGorYTM2L21ld3h6aHVMcDI2aEpubitaelREbWZlazg0RC92a1hFdWcyamNZRzZLTTQ0ampFOTE1RG83dnNVVXVBMWU5b29NNnp2cE13NzdnMHYyWElKTm1FZkljQ05MYW4vZkNEa01WMFpvczBKZjBwUmFaL1dMMkZqMkpSamJhWjVrYUUways3WjNvSG1SMGN3LzJBdUd5ZFFabXJOaWhyK1NrZ1lWbTRQQ3hsSnpOYkVzQitPYzRuUVFJVEJwL2FWUGNlVTk0Q3VCNnBVNzBzUFlKVWlPZ2VGTzFFWnVQWlNHV1ZvaldRc1VnT280NVF4ZXNCMDZqQmZuQWExTCtVS0h4TllHSXpjVkN5YzFUNVM4NkN3NmxqY3hpOUR4d1Y1MUN1b3JnQ1lEOXdybGg5MDZqZ3lGSmNqVTJKYWl5NWVCR0NkV0V4dGxSQ20xSWNkV08reWd3UmRNYmpHbGpjajhCTXRtQmhabzZGYWlaVlR4WHNHWmpvK3crSUJQV1M1bnRsaHVMV01aZzVQOHRrTXlIZE15OVRrK1NHOXYwdXJMeUUwcmtRRnVjd2wiLCJtYWMiOiIwNTBkNTllZThlNjFhMDVhMWQ1N2M5NGZlMTRkZjNhMWI4M2I0MjA5ZTZjYWI4YjAyZDFiZGViNTAxZDMzMzYxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZrLzVJYmdnYms1Vit3M1RGSGg0cUE9PSIsInZhbHVlIjoiRXZqWDZTWVBhcjhxdGlRcHQrSjNnZGJwRGZ2N3ozZS9QR0VNbytLNXJoeWdmRy9OWDRHQnBGclVqem9kUFNoSmg1c0FKWTZMZFovalRwZy9VUDNIRGhYOWxrQjVXZDZCbXVnWGVUSnhWandJcGY2VkR3SXJ3YXJLek00Uk9mRWpXSll1SzJjR2xSSzZzLzRhWHA4UEpqaWxWYVZIcjVMVlNsR09XRmtMa2JXQUFsRkxwVW92UHFGNjhHQkYwUXdCeENrcWNta0pLcHhiTFh6eC9OaWpTVEFBKysxNnBIU3dIa3VIOHAybVp2bFA0VWowblF1a01YKytyd0FZSk9FZFdLc01qamQvMVFGZ09JbzVKTFA5Yy9kUXZacWRYQ01hUThvNmI3RENaMFBoOHYyazJBcnRBdDVONFRsSXdvdkhJRXZSUHJ1aWQyRSthUVNEZjRySUJYNHRVc1QvRzcyMGpHZVJ5dEpzR0FXNXgvd2d5WFFCbXpQSXZtRzliZE0yeTRRS1FEMlpROEVRVmpxbXhNd3pEeWtxS0pYN2FwYkdGZWhVS2plMy8yVVg1UFlCd0tLSkRIdjlvQ1N1RWFYTFFiWEU1TzBZTzRRS2NOS3hYcXJwczlJb1JWR2wrUDhiR2czcHNESnBCT1hxa2NyVHI2MGhRMzR1NmJ1Yzdmbk0iLCJtYWMiOiJiYjE4YmE3ZWFkNTVmOGNiMDlmYzAyYzU0ZjhjOTk0MWJiN2QzODI0OTVmZDQ3NzIyZTkwYWUyNGI4ZGM1NGJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17758552\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}