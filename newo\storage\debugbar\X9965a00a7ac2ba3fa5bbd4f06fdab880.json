{"__meta": {"id": "X9965a00a7ac2ba3fa5bbd4f06fdab880", "datetime": "2025-06-08 13:35:10", "utime": **********.789396, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389709.469176, "end": **********.789431, "duration": 1.3202550411224365, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749389709.469176, "relative_start": 0, "end": **********.545907, "relative_end": **********.545907, "duration": 1.0767309665679932, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.545934, "relative_start": 1.0767579078674316, "end": **********.789434, "relative_end": 2.86102294921875e-06, "duration": 0.2434999942779541, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131096, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02147, "accumulated_duration_str": "21.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.648319, "duration": 0.01459, "duration_str": "14.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.955}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6868799, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.955, "width_percent": 5.123}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.734823, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 73.079, "width_percent": 6.428}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.741171, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.506, "width_percent": 6.055}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.754734, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 85.561, "width_percent": 9.735}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.763915, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.296, "width_percent": 4.704}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1465607322 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465607322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752049, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1447702203 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1447702203\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2099973620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2099973620\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1802699914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1802699914\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-25081327 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNMYWFVTUp6bmVsay9LYlZGVkk1a3c9PSIsInZhbHVlIjoiT0QzZkk3VG9KL3BVeFlWS2tGMWgrT3BZSWpvR1lsdTd2R0lNWVFuYloxdmlaUnVZaGdHNHNDYzRPdjBlalZMNXRvNGVQMjJiZkpkQ2dNL09zWWN0aW5zU24wMHlDQXdQYjMwOVlHVmZlRVFha0RXU3JESThBSHd0dHdDTzluQUc3aFpMZ0JmRGZkbjR5RkpSNDlpZUVYLzB0VkRsNmRjSVRWU3JtRUlUbE10eHgxSWJoa0MvTzBHSHdyZ0RiU092SDM4VVdBVTkyQjFvRzdlWmgzSGphRnJYRGl3QXRRdmZnSkdjc1FyWk1YYzdBZGZDL3pvc1FWTVBlMXJhS29Nc3dGdGs1eWkxZWlmNldWUEhRQ2pkaExycU4wQkd1RGluWlZidnRjOU9kV2MwL2ZXRDBrN0dFWis4ZzJ1dHRXc1pIbVIybmlzdG1sUGMvbnNwZ2tRWTUwcGNDUno3STFBT2RGRXBnODFxWWpXZXN5ZVZnWURERWlmR0JPSm12dk5qamREZjJRUWoxanVMU1RIQ0VoTkxaTzVwQ0N4UG80VlFlNlFFUVhjSTVUQVJCeXFIV0NKUkt4S1NPMEV1Zi9KeFlLVGtxRzN1MkFhMXR4T2pFa1FRZ2dIUHQyVlplSE9BVjdlOVVGT1pySGRSQnVwTnBEbzcraGVvWkJMSjJaN1MiLCJtYWMiOiJiZGY2MWFlZjJlNzY3NDUyMWFiMWVjNmRiMGM2ZWY3OTg5NTQ3ZGVjMjY5OTNjYWVhYWZiYzYzOGY3ZDJiN2ExIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllGZHhNMnMrOUN6ejB5UVVZRGxkTmc9PSIsInZhbHVlIjoiZmlQM3VOWDE0djJqMHl6M0hhWjhHZEw3VEp5aGVpekk0RTJhSlQ2NjQ0MEdxWDRaUkE0a2NoSXNzMmc0dmNYTW9naHVGZDg0VE9Oa1ZHM05aWWppYWdLaHlzVE9jS2VGVDgvTGRRcjhDZGdWZHlTbnFzTEgxNU5GQ25DR0pxaUVVMGFlcnE4aEtqSVBwVmtyWlA2YVZ5RW44dHlLbW83YjJEQ3REUWFlVXc3WGZEbG1uN0pYdmY2M1Y4bjNXeUxzZVhEWUxuWlJJeUM5Z3hneUcxclI0YmxsQitGM21NL0drSllpM2YyN1NqTzFhU1NjNDY5ZXgrcVd6TjZsODN4dkFZM1FJdU42Ui9jQmdwcitEUCtCdHRCd0xDS2JoUE9kQy82REtIS09mLys5L1VPMFlFSkJkYnN4MEE1eW1Qa3dLMnFQbTV0dGNGV0ZKcHVrODJtcW9HQ2QyMEh6UWJDdU1qOVVqaXJXUjIwbTVtcHhIMXN2U3hMek8yUEFSL3d3MFhJQnNwV1lwc2d0YUJ5cXd1cWEyckJLWEFqQmRoQjVOenJRQk41c2JoSkI2bHVXUXNLbjdzQ0JxSTV6RHlmSmc3TjNTU3M0eWNaS2pwOFVxRjZLVEFrTDBqN3pBY0NSWWJsa1JTRHpKY20zaStRYitMUWJrVnhnUWZ4ZVVWaFQiLCJtYWMiOiI1NzFkYTkxMTQwNWE1ZGYwYTgxZTQ2OTVmYTQ4YmVlODE2Mjg5ZjhlZTAzZWI5ODE2MjU3Y2Y4NGQ1YWNmZDc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25081327\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1482775514 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482775514\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-656041801 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:35:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9IVTdzRjFaQXRSRFNVQmFiSnRadVE9PSIsInZhbHVlIjoiSGp4aWdQZnRPbklGRGYwT0d4QWxteDNHM0ZlcU9aUExYTkxneTFSNkphQXp0bkdIdy93aHdWckNWWTBqR01xN0MrSDBZNVNqU2U1Y3VsYzlLbU41UEc5d0luYndWRCtkQ0k2Zk5VaytwSm1PSUJEQ2FScHBIMEdVZ1BCbFJpL2RvdWxIcEl2QndLSlNhWmFNTDlZcDlDY0hYbk5xcjAxVkNsU3hQbXZSLzQ1K2RHS2tVdE9odEFPWjl2T2xZR3VBK2VxdjhMM2k0dGJ0cnRBK2hGenNNOGRrdFVMenZha0l1NG1VVUVOdEJMMHpuQmVualNSdks0L01rWVRuWUlaV2toNjRwRFVVbHpQTzczdktNeVQrcnExUko0d3hoNUlKK2ZySDltenJUemU4d3dMaVFCbEtCWklJUGh4QmhZcW9EYXZwTmozVDJaemd3K1VyeVFnRjZzNHJaVWdSREtTWjRxanIrUDQ3YlNJNkVnWE1XK1JLc3NteWtEUkdJL3Ntc3NrTFRxN0FVN1ZTdHRsei8xMHJ4aUQ5ek5LSmVpQWNMZ1MzZXdOcW5hZW1mM2FLbEMveEd3OUFwdFUzY2RQeTI3cmtnbmoydDd5SlRJNUFuMExmTThLMERYaGJ6M3lPRWV4RCsxenVMRVlNUDlLUk9ueHB0N2RkK0MzVFV0MWEiLCJtYWMiOiI0YzY1MDAyMDIyM2M4YWM3NTlhYjM3MjM2YTkxN2FmODcxYjBlODYxODQ0NjI0NDQ2MTgyMTJhYWNhNTdjOTQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijl6REVIN3UzOUQyOHdpMW9OSndtNXc9PSIsInZhbHVlIjoiMCswNXZvZlV2L2Q0TE1CQng0aVpnYUpLQjBaZ2pmOWZ3QkNhTGlvaFRKSkpZUXpxM3BSNDJFRk9sY3BhcmEycEkxTDF6L1VpYUFOdjd0Wm9uMUcvdkVCT2NTU25HcGJYRTB5VXphMHNlZW4waEhZV1RPcVpJemlscVJ6WG5KZGNKNGxBZVlTalFKS3c5RUhyQTdPK1VNSnJuVnlyNWN5blN2RTQ2SjZqOEhhWDdON0xEUkR3OTd1ZDFuMC9BZ3R1Y2pnMGp5bWdmcW44K2hMOUxKdThiZVpqZFdXTy9wN3ExRTRtS1ZzUWVLaHZJSFJOVzUyUmVFUHN3clFFd3pPUHhrd3dCWm1GMksvSE9qbXl4U3YrRWh2VTN0N3BYd2xEOGNpUEIvdzRTbEIrSjBpS3hWMWtLRmIvTkM1Z1NTZHU2N21MWmp1ZUs3TVh0WjBjZzQ5WWdSQ0pnM0YrdDl6T2ZZb2l6S0JOSkw3cUhlMDZ1S0c2ZzAvSHdiVDVRbTEya0lGWmpVdmVXSjl4cmx0S0FtdDQyQzEzVWxHUWpIZGFDdXI4QVVsSFo3dUZtd2JyMEZnay9oeTI0S2hqR2hhSjdDQjZBNE5xSUxSMG5wVlZpSHlyVlEvNDZCV3pPeVdmZWl6ZkQ4dHFINkcwY0Uxc2F3N1EvUlhqWHphdFNyc1IiLCJtYWMiOiIzNjllNWQ0Mjg5YzAwNDFmNmI3Yzk3YmVhZjZkNzk4YmYyY2FjMGQ4YWY3OGFjZmUzODhjOTU1Mjc4YjcwNDQzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9IVTdzRjFaQXRSRFNVQmFiSnRadVE9PSIsInZhbHVlIjoiSGp4aWdQZnRPbklGRGYwT0d4QWxteDNHM0ZlcU9aUExYTkxneTFSNkphQXp0bkdIdy93aHdWckNWWTBqR01xN0MrSDBZNVNqU2U1Y3VsYzlLbU41UEc5d0luYndWRCtkQ0k2Zk5VaytwSm1PSUJEQ2FScHBIMEdVZ1BCbFJpL2RvdWxIcEl2QndLSlNhWmFNTDlZcDlDY0hYbk5xcjAxVkNsU3hQbXZSLzQ1K2RHS2tVdE9odEFPWjl2T2xZR3VBK2VxdjhMM2k0dGJ0cnRBK2hGenNNOGRrdFVMenZha0l1NG1VVUVOdEJMMHpuQmVualNSdks0L01rWVRuWUlaV2toNjRwRFVVbHpQTzczdktNeVQrcnExUko0d3hoNUlKK2ZySDltenJUemU4d3dMaVFCbEtCWklJUGh4QmhZcW9EYXZwTmozVDJaemd3K1VyeVFnRjZzNHJaVWdSREtTWjRxanIrUDQ3YlNJNkVnWE1XK1JLc3NteWtEUkdJL3Ntc3NrTFRxN0FVN1ZTdHRsei8xMHJ4aUQ5ek5LSmVpQWNMZ1MzZXdOcW5hZW1mM2FLbEMveEd3OUFwdFUzY2RQeTI3cmtnbmoydDd5SlRJNUFuMExmTThLMERYaGJ6M3lPRWV4RCsxenVMRVlNUDlLUk9ueHB0N2RkK0MzVFV0MWEiLCJtYWMiOiI0YzY1MDAyMDIyM2M4YWM3NTlhYjM3MjM2YTkxN2FmODcxYjBlODYxODQ0NjI0NDQ2MTgyMTJhYWNhNTdjOTQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijl6REVIN3UzOUQyOHdpMW9OSndtNXc9PSIsInZhbHVlIjoiMCswNXZvZlV2L2Q0TE1CQng0aVpnYUpLQjBaZ2pmOWZ3QkNhTGlvaFRKSkpZUXpxM3BSNDJFRk9sY3BhcmEycEkxTDF6L1VpYUFOdjd0Wm9uMUcvdkVCT2NTU25HcGJYRTB5VXphMHNlZW4waEhZV1RPcVpJemlscVJ6WG5KZGNKNGxBZVlTalFKS3c5RUhyQTdPK1VNSnJuVnlyNWN5blN2RTQ2SjZqOEhhWDdON0xEUkR3OTd1ZDFuMC9BZ3R1Y2pnMGp5bWdmcW44K2hMOUxKdThiZVpqZFdXTy9wN3ExRTRtS1ZzUWVLaHZJSFJOVzUyUmVFUHN3clFFd3pPUHhrd3dCWm1GMksvSE9qbXl4U3YrRWh2VTN0N3BYd2xEOGNpUEIvdzRTbEIrSjBpS3hWMWtLRmIvTkM1Z1NTZHU2N21MWmp1ZUs3TVh0WjBjZzQ5WWdSQ0pnM0YrdDl6T2ZZb2l6S0JOSkw3cUhlMDZ1S0c2ZzAvSHdiVDVRbTEya0lGWmpVdmVXSjl4cmx0S0FtdDQyQzEzVWxHUWpIZGFDdXI4QVVsSFo3dUZtd2JyMEZnay9oeTI0S2hqR2hhSjdDQjZBNE5xSUxSMG5wVlZpSHlyVlEvNDZCV3pPeVdmZWl6ZkQ4dHFINkcwY0Uxc2F3N1EvUlhqWHphdFNyc1IiLCJtYWMiOiIzNjllNWQ0Mjg5YzAwNDFmNmI3Yzk3YmVhZjZkNzk4YmYyY2FjMGQ4YWY3OGFjZmUzODhjOTU1Mjc4YjcwNDQzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656041801\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-938744492 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938744492\", {\"maxDepth\":0})</script>\n"}}