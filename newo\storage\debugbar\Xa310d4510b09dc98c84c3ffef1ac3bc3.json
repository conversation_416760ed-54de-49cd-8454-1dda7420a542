{"__meta": {"id": "Xa310d4510b09dc98c84c3ffef1ac3bc3", "datetime": "2025-06-08 13:30:55", "utime": **********.523845, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389454.119544, "end": **********.523874, "duration": 1.4043300151824951, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749389454.119544, "relative_start": 0, "end": **********.341154, "relative_end": **********.341154, "duration": 1.2216100692749023, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.341191, "relative_start": 1.221647024154663, "end": **********.523878, "relative_end": 4.0531158447265625e-06, "duration": 0.18268704414367676, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02682, "accumulated_duration_str": "26.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.444904, "duration": 0.02555, "duration_str": "25.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.265}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.503252, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.265, "width_percent": 4.735}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-699961804 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-699961804\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1895393753 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1895393753\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-701032300 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701032300\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-657747884 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpnWkxrTWQzeHRYQ25vdW1LMGlGd3c9PSIsInZhbHVlIjoiKzB5cFRibFJJbVJUbGZndHRzRVpBakZoTzN3T25xOEREMFZxcWc1NFJReGJveWsxbkh1MWtXdlptL3RWSnBRS1JUUnNUSDMxelJxMTdDanJaMHgyZENacFNHdW9FUmJFT3NweUZHUklQV1JCSGw0c0ZmUlV6dS82eTEvdXNzYlg2M3RmSHQ4Y0FSMXlnRlVjVEM1eE42Q0N4UCtFaFBLUXVGSTNqejV3U0VmbGZKeHgzaE9JVm5KR2VqckZ4Z1pjckg5NjhqM3RhNTdZdlF2NGYzMWFwdWh1M0xVbVFnNkxEM3ZUY1JDUHczOVo1SGRBR2d2TnY4Rk53Y1FuTlFjWVVYS2thT25nVFYyT3FGWUNoZzRnYSt3RmRSb0JOMEFrc0dBdVEyS0FDV1YrV1E0cENONHcvdVo5WWEvVEdXRmM2K2FXcUI2dFJTNmQxRU5MOE9NTmlHNWxYbUs2dHp3bW9uejU0SmQ3NzVqWW90MldERFpMU05HdDZsaFplbEhaRnFTMHh4eEd4UUlwVjJ6NTJaOTdmU2t5OWV6aC9kanlXaFJlTHJoV2c1ZTBkTmRiQ1Y1RHN6OGE3cGtSd2c3WmsvZXRBZnB3MFYxV2ZUeTJsWGY1eUdVaVMvODFTaGh1TEQwd3ZQVFU0UENmWitaN0kyVDdJSElMUXFLc1BmRCsiLCJtYWMiOiJhOGEzMWVmMmQyMzZiZDE3MGM3NzQ1YmZjMzVjMGFmZjBhYWRiNWEzZTk2Mzk4MzYwMDlkMmViNTM4OTFhOGY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkVMaFJBQ1dmTjIxUWZDeDVXcWJxcUE9PSIsInZhbHVlIjoiWlkxbFVaeFJFVFBTM3RMcHZqaDdNNTd4YUIwbEJtL0M0dlB4VXhjd2kraUs4enRGa2FDVk9hbTZpclh0bEhwalppdWtabFFiVHpEdWtvdE5zQjUwaXhVQVhqcEd0T2NRN3pzS25XQk4zdS81ZjFiNjZ5aW12Mk5GeWk5N0dweW1MMW5pQzNXdlFHZUVyMTRZK0RiUW1CKzYwNjZWL1czNmszYjlrZ29Xb0ZNL0xqMWY4cEhyVm0welloNUI0NmpIVnRTS0lvM2M5Wkd3bnRyWUs0bklHTk9wM0JmSWhNMzhNMy90MDhjdzlFRXRPTVhCaFFxVmI4dTEwUWVnZkd5Wkh6M2hwRHFXWmE3K1c0T2Nyb3dPeWluMWNoWW04enEwODVJZ1h3QXp2Q0kzZU9kYUE5N0FVUjVOSkwwUm1EakpaVWhudDlleDNxV3BsUU9ZdFA4L1owUnluYUI1RVNNTG1QY1pkWWl3OE9SeUU4Mis1dHFGWjE1MFNIYmw5LzB0RmpVR3lnRjNHY1RyMzBIS2lKSVlNNURUejVHaEFPcmFUbEh1cEJPQU91VXhid3VaUXR3UU04ZzRlTUxJUGVWL1Q1R2RuSEI1NTMwYXFWVURMdE15czlXS0FHOEVHRThZTUdlb2NRcDFNbW15a2JtbWliRU5XV3hzcjZaVGg4V2giLCJtYWMiOiI1MWI4NGQwNzdlY2I4NzViODUxY2Q2MGZkNjVjOTgxZDBlOGQ3ODk1MDlkYWViZGQ2YjE3M2QyMzZlZDFjMzc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657747884\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1641665206 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641665206\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1005983204 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:30:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks0Unp3Q2x0endhT0FURWdUNmlBOVE9PSIsInZhbHVlIjoiclNqNEJCMFNmaUxXNnpLbm0zZ3VGRWlHMHgyTDJ2R3BoS2JzdXBFcjBGRXNyMEZ1Z2E1RGdYalpxYmdhSG9VaGVJN01lbDZJZ01KZ3V6SWQyVDZLL2ZQTEY2enZmMkZ1YUVkUlZjS0RjT1lOR2tXT1RjY08rUVNoWlFTdTlMUlR0a3hzMEEwK0hGTUtBMWFTQkI4cExKZUpoSXRsRlFpQUxFQnR6ZGhIa0FkcU9CZDdZbHVLcFJOWExZUUxkaVFXS2JTUFBLY3doRVphQ1gzRnRvQURhSmN6anpEajlKZ1UyZlM1VDgrWFFtTFhZMWoza1Y3MXFZaE5NMG9ZYk9wbkltTC9PVjF0TkRudkV5MXF2MGxGRzgyeG5aQXdjSG9mQzhMQUsvVkxVUHpXOXZ2cGM4Wlltck15Yjkzb2IvWEFVZi9pVmlDVVRScjNabWQveHU0cGN5SjNEWmZmSzZDenRiMmdSSzZreDdzMmw0VUNqZW53eVg5QUt2aW1XbUdVUjIwU2QyQ1k3QmJIVXV4K1RWK1d3d1hPSHpwcWdOazZZdW5IUExRc3dSc2NTNk0weUJ1QzRpb0ovc05BSCtFazFnaDk1M3owRWdzdjJEQm1qakJiVklHbHk3VlJiblhSZjk4Tmk2akxjaTYrREJWeFp5eVRBTUZyN2x5bWV0dnAiLCJtYWMiOiIxNDg1ODcyNmI5MTMyYWI2YThlZjY5NjkxOWY3YjA3ZjQwNjgxOGIyMDkwOTllOWRjOTQxMjZhZDk0YzY3ZjViIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:30:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1FZVg0eXRBQUhSSkV0NEJ6YmFRdEE9PSIsInZhbHVlIjoiTGhoUGhBNU5TWWtvb1RUOEVIb3VZZFcxOTBZd0orNERBcmJ2VHhzMWZQSWVqRmNxWHN5UWs5SG9yaWdZWm41WEFZTTUweXJ1MUlEbW16L0tpeUpISHNKMmUrakNIT1FJSlFTMjNrNk1lSkd6ZTMrWlFqa3VCSWFyZSsydlEyNU5uMGgyQWVGRnV4TVdGOXR6STRReUdpM24zTHZtLzBHS1BEekxVVlBQQm15Sk9SYmdKci9lMlgxbHJHTnlSWkcwaXBtL28ybUMwbUhlNXhXK2pGTVlDZUhqRDhjR2NsVDh5T1JSdGx4VkhBMUU2RGFkNExXcnE0VUZqdWVhakNFUHJMbTlSa1lOWmpoNVV5bGVYY1FrUU5IQTN6T3ZDTnlGQjN2bDZmNTczMzRiZWtrVFI1NVkzVHZTVzZYbVlTWHV0ZXdXcVJrMW9VWW9XNHlGUnZjZVNYbUxnZm5XOUU5NzhEalRXVHl0bGdISkxCY2NTWXFWcm9PUzdkeEZ6WUoyZG55TWR0SUQwUUZFSVB3c1V1REJXaTlLUUt1ZkdwVkxab2pDMWxkeG5PVnlnS3JvMkcycllmOTFtQkZENURZQkE5bUd3UDIzNlMweTRKMC9VblAxMGVpTjloeGM0RTF5SktNZ2dWdi96NWFOYkNpMjdOVWNqRkxHTDI5VW40a3IiLCJtYWMiOiJkZWY4YzUxOTFjNWNjMWU4NWZhNTU3ZmQzNjI3ZDUwNDk1Y2M1NjVkNWUwNTVmMTY4ZDU2N2U2N2QyZmI4YjlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:30:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks0Unp3Q2x0endhT0FURWdUNmlBOVE9PSIsInZhbHVlIjoiclNqNEJCMFNmaUxXNnpLbm0zZ3VGRWlHMHgyTDJ2R3BoS2JzdXBFcjBGRXNyMEZ1Z2E1RGdYalpxYmdhSG9VaGVJN01lbDZJZ01KZ3V6SWQyVDZLL2ZQTEY2enZmMkZ1YUVkUlZjS0RjT1lOR2tXT1RjY08rUVNoWlFTdTlMUlR0a3hzMEEwK0hGTUtBMWFTQkI4cExKZUpoSXRsRlFpQUxFQnR6ZGhIa0FkcU9CZDdZbHVLcFJOWExZUUxkaVFXS2JTUFBLY3doRVphQ1gzRnRvQURhSmN6anpEajlKZ1UyZlM1VDgrWFFtTFhZMWoza1Y3MXFZaE5NMG9ZYk9wbkltTC9PVjF0TkRudkV5MXF2MGxGRzgyeG5aQXdjSG9mQzhMQUsvVkxVUHpXOXZ2cGM4Wlltck15Yjkzb2IvWEFVZi9pVmlDVVRScjNabWQveHU0cGN5SjNEWmZmSzZDenRiMmdSSzZreDdzMmw0VUNqZW53eVg5QUt2aW1XbUdVUjIwU2QyQ1k3QmJIVXV4K1RWK1d3d1hPSHpwcWdOazZZdW5IUExRc3dSc2NTNk0weUJ1QzRpb0ovc05BSCtFazFnaDk1M3owRWdzdjJEQm1qakJiVklHbHk3VlJiblhSZjk4Tmk2akxjaTYrREJWeFp5eVRBTUZyN2x5bWV0dnAiLCJtYWMiOiIxNDg1ODcyNmI5MTMyYWI2YThlZjY5NjkxOWY3YjA3ZjQwNjgxOGIyMDkwOTllOWRjOTQxMjZhZDk0YzY3ZjViIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:30:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1FZVg0eXRBQUhSSkV0NEJ6YmFRdEE9PSIsInZhbHVlIjoiTGhoUGhBNU5TWWtvb1RUOEVIb3VZZFcxOTBZd0orNERBcmJ2VHhzMWZQSWVqRmNxWHN5UWs5SG9yaWdZWm41WEFZTTUweXJ1MUlEbW16L0tpeUpISHNKMmUrakNIT1FJSlFTMjNrNk1lSkd6ZTMrWlFqa3VCSWFyZSsydlEyNU5uMGgyQWVGRnV4TVdGOXR6STRReUdpM24zTHZtLzBHS1BEekxVVlBQQm15Sk9SYmdKci9lMlgxbHJHTnlSWkcwaXBtL28ybUMwbUhlNXhXK2pGTVlDZUhqRDhjR2NsVDh5T1JSdGx4VkhBMUU2RGFkNExXcnE0VUZqdWVhakNFUHJMbTlSa1lOWmpoNVV5bGVYY1FrUU5IQTN6T3ZDTnlGQjN2bDZmNTczMzRiZWtrVFI1NVkzVHZTVzZYbVlTWHV0ZXdXcVJrMW9VWW9XNHlGUnZjZVNYbUxnZm5XOUU5NzhEalRXVHl0bGdISkxCY2NTWXFWcm9PUzdkeEZ6WUoyZG55TWR0SUQwUUZFSVB3c1V1REJXaTlLUUt1ZkdwVkxab2pDMWxkeG5PVnlnS3JvMkcycllmOTFtQkZENURZQkE5bUd3UDIzNlMweTRKMC9VblAxMGVpTjloeGM0RTF5SktNZ2dWdi96NWFOYkNpMjdOVWNqRkxHTDI5VW40a3IiLCJtYWMiOiJkZWY4YzUxOTFjNWNjMWU4NWZhNTU3ZmQzNjI3ZDUwNDk1Y2M1NjVkNWUwNTVmMTY4ZDU2N2U2N2QyZmI4YjlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:30:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005983204\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2128462654 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128462654\", {\"maxDepth\":0})</script>\n"}}