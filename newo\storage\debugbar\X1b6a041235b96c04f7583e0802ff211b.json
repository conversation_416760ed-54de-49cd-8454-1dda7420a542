{"__meta": {"id": "X1b6a041235b96c04f7583e0802ff211b", "datetime": "2025-06-08 12:59:55", "utime": 1749387595.014269, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387593.67419, "end": 1749387595.014299, "duration": 1.340108871459961, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749387593.67419, "relative_start": 0, "end": **********.842151, "relative_end": **********.842151, "duration": 1.1679608821868896, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.84217, "relative_start": 1.1679799556732178, "end": 1749387595.014303, "relative_end": 4.0531158447265625e-06, "duration": 0.1721329689025879, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45384656, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0063100000000000005, "accumulated_duration_str": "6.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.940227, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.048}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.977496, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.048, "width_percent": 16.799}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.987603, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 86.846, "width_percent": 13.154}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-239484257 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387586430%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJ6bTVhMUlWN00xbEVINUhNU1k4cHc9PSIsInZhbHVlIjoiNDhxTkgwMmQvd0pBMisycGxXTFdITEt4b1VyUUhEWG95WndoWmNRck52allveTczU1FvMGN4ZzRROXRhMkprMkFDZzZVY1lBQWhwSWwvWU1pbVJyTGxEQjU1OFJvYXpza2djaFNuSlhyeEVGOEh6VVNxbElQZkdQb2tlRk14N01IS0c3bngxVE1qSEo4eElhZDBUUkE2M0xzVEVldGh1TjloVFQyWktEeVcwNWM0Y0h5NW9vcTJrUDhYZ1lnQU8rQWRmQnZXSDdkOUIwTlpuQWhxQVg3aFpUYTdhQ3o2NndFbHdLd3hTcUdwOCszV1V3VTZVZ3MvYytzVU9PZjBPdlNPS0VlQ3BjRk1DMHVFR2Q0enRZWUk1aUpTRXZUQWd5WmVpQW5mZHdCdG4yOXppQWlNNVRlc3NBVXNMVStsWDB4Vjc4aVFyOVd2eWZLTFAvUitaUWV4SE04TitPT0sveVZVbDVRb241d1luUktUd2tUZjNDQzFZUFZpTU82QmptMUREeTdTUkI3NzRoZC9VTjdBa3p0ODQvK1Z3aTVTOTV5TUg4YzhWeXUzUklLWlZKc3ltZTBWdXphcmNEclNEeFkwTFdWdXpzL2E2ZjVVazF2WVUxRWJUdmpyZE1HcHZPQ0ZpYmlBTlpEbzdoeGR1ODZaenc0Y0orM3FKS2J0VGgiLCJtYWMiOiI4ZWFlYjRlZjA2ZmQ5MjRiYjExOGI3N2Y0OWI3ZDQwZGIwMTNhNTExZmNmZDg0YWRkYzE1NWU3MjA4MDgyMGM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InErWnlZUzhHOEJjS0ZKSnVBeVpJZVE9PSIsInZhbHVlIjoiK3pQSTNZVVJ6K1hIMmJwU1F3MGRSaDMyTmptakVhb3VOSlZBdklpVWJNT29EUHJxdVVzcFBoMUZkaG96V243UW9YbUZoWWhxQndHK0Mra3pvMWxLRWxkQ0pWMnl2VDJNL0l1Z1dBSlFPeWtMSnhHVjZUNEl1NnlOdkhTWUV3VnRiU21UaC9yaUMrNmJObXFIUzgrM0dmSDg1NHNkSDVvdWVoRWhuSXZSOGxEcU43bVU5bklKcFBEVzlqaUdJWWRqeDBEazNIZjJNRXlaaEFWWDZIQ3FuTTYwSXZrWVRvR2J3SFhNMkpiMG9kNGxlMGhGa2hnZHJKNkZYMTdpbWVzcHg1aml2SjhtQVNFQzQzU0hENHNtWWZvUS9TQkVRclNFWlVOK1cyRVdGTHJqd01GNXFLd3NiMzV0bER0T0ZtK243cE0vSTF3d2pjaUFBdHV6RUxQQU1OMEw2OEdXaXRqMkliYTAycUF2N0pVK0hBY3doa0UvSXFGRVRLbDQ3WlhhUGFVMGUxSUgvcUxwTFQzMS9TaC9Pc0tSZ3hQanExVWcxZWZvZ29GYnhLdHdDZ3dtRUQxTWFYNXJpbi83NXFGcGVnS2w4dEp4S3ZYRjhYV2xtOXI2ZTJtbEtGVmVhQlA4YWtEN0NubVFTOE8ydnlYaTd5NnQrOWdHeGFuUmlySVkiLCJtYWMiOiJkZjdmMGE4ZjA2ZWVhOTEyMDY2NjI3MWM2OWE5MTJjZGY1YjUzZmRlMmEyNzcxNWRkNjU2YTIyNGYzYzFjYjRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239484257\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1229370485 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229370485\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-495026440 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:59:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik0xUGdyY1M4OXZHQUd1YzRoV1pxM3c9PSIsInZhbHVlIjoiZldlR3dwMXRERlMxcURhbUxWWTdYNHF4aFlOKzBKY2ZWU0JBOWQ2Uys0R0NuRVducjlZeEVXRE1lSkJoYVFOVlRPKytPczdHU1BsRUZVQXlZWk8zWHUyM2UzZVZ1L0NUU3g4WVZ0YnNocjA1aHFlZVJ2SU5XSkRkUkN4V2FvTlVGMkd0WkhZRUU2SmdBTHU1RnFCbzVjODd1R1I2bDdtSmNIV3haamFCckErREZaTlhtZ2lNbWR5dWJoazJtbEg1ME5DaGRJVkpianIxNSswakRrMXJOQjUzY0VOZUx6c0xUUkNyNnRuaDIrUTFZOUt5ZFdDRHp2UjhpMGUzMmljVDJ5MnVzVGZUbENBemwveTZ6OUczUnVOK2RsNWRaSXVvR1VpejcySnp0bU9yc1NpVU14VkhQYzY2VUFDUmZkTytjRnVuQnpGN0NJeDYrbU9qNWlOK3BlS05GaVNGa0NBQ2V5S21NUkNkMkxMMCttR3E5TWZ1MVQ0L016alpTaTVLVWZvTCtMNGwvcllaNDBjS1hhblpST3dIM2IrSjRhL1JnNDZFYkVnd3dnaDNoNGJ2U3VxT1QzTXpJUlVXVmJRWkxhanZkekFxUFdvYnN1bGdILzB0aG80SWtoYjhJRGR5QjBXNnFJNmE2cnhkTGpWL3NUVks5RmZoM1FtVG1ML0ciLCJtYWMiOiIyM2RlZDJlMmE4MTg1ODkxMGM3ZWRmYjJlMjAwNjBhMzU3NmFlNmY0ZTlmYzE3ZTE2YTdmY2U3YWRmN2NmZDZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:59:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJseVJvZlJLYUdZeFhUanVuaUx2dGc9PSIsInZhbHVlIjoiQ3hyRnNxak13TGN0WmRiT3E1bUdNSWZFdUFyYUN1aTh4VVUyRlc1T2VFVkFTMFBOa0NDOHpETDVzS1gzLzdFbjdiSFQrb0dzWmllL3d1L0FoSnNrNFlpS253UkRPeWE5RWZwTVVaV3dMNnB3RmdsUUZ5UU5QRC9DcllOUFdSeXFEWkdVb2RNdnhBYnFyZDZVU1JtZzQxZ1dIdFZzVUVVaFo2eHhQWjI1ckk4cEtaVUpDaDFWdWRHem80MktSY3JSdC9LR3QzSlU1ZUc4bnVhWmx2dGNHYmFWZmpBZ285TjBvM3ZpMlRvbmNDYWo4WkIwQkFaL3ZIdk5IalcvUk8xR0VNWVJCaUsyR0lBOHJIUGNKUlVuYllxTmwyNTg5cDRkd2JlcVRsMUxJN1VTZmMwaTM1akJjekdOcHZXOTJUUy8zZlVFWU9hTk15ejU0WGIvbVN3SkFaa2NldmJmSGZDbWErdDV4bHlGK2xxaVdTWTU4Y2VWdzliSEo3b3Fvd0RhVThsOGVUaHNIYnNyVy9aUlVURFNVaGtTbUplRk43SXFRNGJIN1Y3V0JFVnNUYzRITGNrL0VXMEtDdEtDMnloK3psYzY4QjgxNzZqYmJxY0FmWi9KVVZTTFFlRkZCNjgxaFFQZW5YbVFoSnZYT3djN3hKR2l6SlIzRzRINnU1SVciLCJtYWMiOiI1OTBmOWY4ZjlkYzBhZWEzYmZhZTE4NGYxMDQ4YTlkOTJiYWIxMjdmYjVmZTNiN2I4M2IwZGNlZmM5YzQ4Y2FhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:59:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik0xUGdyY1M4OXZHQUd1YzRoV1pxM3c9PSIsInZhbHVlIjoiZldlR3dwMXRERlMxcURhbUxWWTdYNHF4aFlOKzBKY2ZWU0JBOWQ2Uys0R0NuRVducjlZeEVXRE1lSkJoYVFOVlRPKytPczdHU1BsRUZVQXlZWk8zWHUyM2UzZVZ1L0NUU3g4WVZ0YnNocjA1aHFlZVJ2SU5XSkRkUkN4V2FvTlVGMkd0WkhZRUU2SmdBTHU1RnFCbzVjODd1R1I2bDdtSmNIV3haamFCckErREZaTlhtZ2lNbWR5dWJoazJtbEg1ME5DaGRJVkpianIxNSswakRrMXJOQjUzY0VOZUx6c0xUUkNyNnRuaDIrUTFZOUt5ZFdDRHp2UjhpMGUzMmljVDJ5MnVzVGZUbENBemwveTZ6OUczUnVOK2RsNWRaSXVvR1VpejcySnp0bU9yc1NpVU14VkhQYzY2VUFDUmZkTytjRnVuQnpGN0NJeDYrbU9qNWlOK3BlS05GaVNGa0NBQ2V5S21NUkNkMkxMMCttR3E5TWZ1MVQ0L016alpTaTVLVWZvTCtMNGwvcllaNDBjS1hhblpST3dIM2IrSjRhL1JnNDZFYkVnd3dnaDNoNGJ2U3VxT1QzTXpJUlVXVmJRWkxhanZkekFxUFdvYnN1bGdILzB0aG80SWtoYjhJRGR5QjBXNnFJNmE2cnhkTGpWL3NUVks5RmZoM1FtVG1ML0ciLCJtYWMiOiIyM2RlZDJlMmE4MTg1ODkxMGM3ZWRmYjJlMjAwNjBhMzU3NmFlNmY0ZTlmYzE3ZTE2YTdmY2U3YWRmN2NmZDZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:59:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJseVJvZlJLYUdZeFhUanVuaUx2dGc9PSIsInZhbHVlIjoiQ3hyRnNxak13TGN0WmRiT3E1bUdNSWZFdUFyYUN1aTh4VVUyRlc1T2VFVkFTMFBOa0NDOHpETDVzS1gzLzdFbjdiSFQrb0dzWmllL3d1L0FoSnNrNFlpS253UkRPeWE5RWZwTVVaV3dMNnB3RmdsUUZ5UU5QRC9DcllOUFdSeXFEWkdVb2RNdnhBYnFyZDZVU1JtZzQxZ1dIdFZzVUVVaFo2eHhQWjI1ckk4cEtaVUpDaDFWdWRHem80MktSY3JSdC9LR3QzSlU1ZUc4bnVhWmx2dGNHYmFWZmpBZ285TjBvM3ZpMlRvbmNDYWo4WkIwQkFaL3ZIdk5IalcvUk8xR0VNWVJCaUsyR0lBOHJIUGNKUlVuYllxTmwyNTg5cDRkd2JlcVRsMUxJN1VTZmMwaTM1akJjekdOcHZXOTJUUy8zZlVFWU9hTk15ejU0WGIvbVN3SkFaa2NldmJmSGZDbWErdDV4bHlGK2xxaVdTWTU4Y2VWdzliSEo3b3Fvd0RhVThsOGVUaHNIYnNyVy9aUlVURFNVaGtTbUplRk43SXFRNGJIN1Y3V0JFVnNUYzRITGNrL0VXMEtDdEtDMnloK3psYzY4QjgxNzZqYmJxY0FmWi9KVVZTTFFlRkZCNjgxaFFQZW5YbVFoSnZYT3djN3hKR2l6SlIzRzRINnU1SVciLCJtYWMiOiI1OTBmOWY4ZjlkYzBhZWEzYmZhZTE4NGYxMDQ4YTlkOTJiYWIxMjdmYjVmZTNiN2I4M2IwZGNlZmM5YzQ4Y2FhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:59:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495026440\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-20******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20********\", {\"maxDepth\":0})</script>\n"}}