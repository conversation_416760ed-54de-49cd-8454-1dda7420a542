{"__meta": {"id": "X578e38ccdc87ec7dcf9cbaa9ec3fe9f1", "datetime": "2025-06-08 12:54:06", "utime": **********.438048, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387244.780741, "end": **********.438095, "duration": 1.6573541164398193, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1749387244.780741, "relative_start": 0, "end": **********.127933, "relative_end": **********.127933, "duration": 1.3471920490264893, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.127962, "relative_start": 1.3472211360931396, "end": **********.438102, "relative_end": 6.9141387939453125e-06, "duration": 0.31013989448547363, "duration_str": "310ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45978456, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.29014, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.317022, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.395791, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.409706, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.045540000000000004, "accumulated_duration_str": "45.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.202619, "duration": 0.02266, "duration_str": "22.66ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 49.758}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.234247, "duration": 0.01342, "duration_str": "13.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 49.758, "width_percent": 29.469}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2568, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 79.227, "width_percent": 2.262}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.292737, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 81.489, "width_percent": 2.877}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3200939, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 84.365, "width_percent": 2.833}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3562522, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 87.198, "width_percent": 2.569}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3688788, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 89.767, "width_percent": 2.481}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.377336, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 92.249, "width_percent": 3.755}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.4005718, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.004, "width_percent": 3.996}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ppHWJDQH68Bkr0QGWhSDdRuFTgW2E2h9dgdihchk", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1779088272 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1779088272\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-934980347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-934980347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1371985479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1371985479\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1170838661 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345888290%7C59%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRXK3poSytKTWlPdUJQaE9QdVhMbnc9PSIsInZhbHVlIjoiTm9SUzNkMUIweEV3US9TZlBFQi9laVJ1QnRoTnRhZ3pPOUJnTUlwUENzdkpDMGozanJBZmhlVTIydzFxVmRDMnRLRFpWQVJmUVZXTWpUems3UHF2MWxESGFnc0N0QS9JOEdZcmpMSUIrV0gyZjBMYlFHenFISHVGYTlqMDF6YXJrVE1WekQ1RERCZlZzd1BQTTAzMUtwd0s5RjBIMU0yRUk4eVAvVDI2SlpxQVNuNENJWWJ4SjQybmthQ056emRHKzFqeGtaVlQvc2M4R1JtUndkZzdlc2h0QmI3NXJYcy83RUhjVlZDS3dJYS85YmxQdzZkN2hmWjZxT3dWWGViNkt0Ny9jQnVSTDlTY25vUG9DTGNZMTFnZGRuSGx5Z2Q4TzJXMDZhYXBPY2RoNC8wb1p2VUVpV2lGOGQvelVQKyt1a3UrNDFpeEQ3OHNFTVBBUGRReE9HeHMvR015eVBSYllLV3d0R29yZkovbHBUczlJQm9kaXZJOXU4TStCd0JXV2xGWElVbXpnZzNyNCtma1JzNXJhd1FwcjVZZXRWV1kwa3hOWlVDeDZmaFVoYVBFRll5dGFXN08zVnRjMHFadkVodUVNVTk0QU8vR3VMUC9BOGY0NG5oOFFkOW5NOGlLSmdhbmdQWkM1bllZaHZjWDgzQ1I1bFNHeS9JSmdpbG0iLCJtYWMiOiJmMGZiYzkwZjYxNjZhZTA1OWFjMzU1OTgxZjc4OTU2ZTgzZWUyYmFmNWRmNGY5YzI2NmZjOTY4NTI5YmIxZDliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitHd1RPNVRrTTZnTnlCak5JOFBFWUE9PSIsInZhbHVlIjoiNnVKOTdkTjVTc1VGalhlOWpZdjVzQm9mZE10bUd4TVM5VkRSd0pTWlYzbGV0M0FsNnR5VlRmQ0drUTZ2ejJLQ1g0aWN5RkdmVkpwSTR2QWx1L3k4UEd6cXRreTMxYVRNSEZoS0E2b1lEanF6dUpDRUhCekJpV29YZWlDc1VBc3FlbkxZWGxFTVF0cytFQTFDTTN5clF1K0hicnBLcXVsYmd0Qlp1T3NSbWhILy9qajc4TkI2RW1GWE54aENWR25IYm5yMXQvdysyNDJ6OGQvTzVsQ0Y4QldpRjBYSytnR1hvOU5xL2d6QzF2VGlNd2t4SUxjUkptcnBEckErSmZVdnNERFNjc3M5QjBtYzcrRkczb2RteEhIZ3dGSzFMbVBkOTlvelc1ck5vOFpzOFpMZDRsa1lEUXl1cWlGVU9id1dKZVJjU1RFNFhPTGR0Y1hydTRvRWJJVTdpeklJdjdIZFAvMXBzcVc0U1VWN3pCNVRCWFZ3T1RBa2FxNXRhaHlQdEZkZmhwSi9wbEllbVJ1d3MrRXk2VFBkaXFOa2dISFRud1Y2dmNWQmtOOTRZa0JhazZ6Tm8wZTRBdnZBUDdQbWU0aGVRa2R6VDYxWkwxYUhISU1haDlxRVpqOXpXQy84V09WWGxxVUQ1Qk0yTFplaVg3dkdwazRXZlJ5WDFQdEQiLCJtYWMiOiJjMTIyNzcwNjA2MmEzNGZjY2I3MGQ3MWQ1ZTdhMjAxMjVkNzgzZDU3Yzg3MjE0NDVlNDU2YWUzN2ZmMjEyNjgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170838661\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-616096578 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ppHWJDQH68Bkr0QGWhSDdRuFTgW2E2h9dgdihchk</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">f5AkRSJ6znJ9Q9GYajnCIMsKHfSywmwrgEPMB2Vn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616096578\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2033223031 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRoNVVUNVFyclVCeXZ2RDBFVTliRHc9PSIsInZhbHVlIjoiTzlqVnZ1cDZqU2hUYkFSTUxSdEVJR1pTdldGTm5xQURzSEt6TU9EUmJ4c3JwT0RCL094VG9uS0V1MndJL29HaXdHb3dLUVByTXdXeFlpRFlnaWZscEhDZU5HejlYV1IrZlorNS9xU1p2V1VtMXQ1UTd3TXoyRE1ieHJnQ0NKcmwvRVEwaHBkb1QyeWtFRFNobjl4cDExd0hGYTVKYlVUeTRucFFKN3l0ZlY1Z0gzazZDNHVtTVdqNE5kZ1h1Skx4OUhMRytySVhCYjcwUGJSWmVuMU1PYTd6UU5XM2ZJQ2g0MktDR0V1Vlo4Nk04bTByZ0loTjV2dktEVnYzRmNLYmxiSUoxUDNmbzdoMlpkMVdJV1dZOVEySFBKNkpSV0tCV2Z3TGI1ODE5Vko4M2hMR1NjTUVzOWdINVJjSUJ2eTVnOFExQVdidFJVZlM2RHZVVlBKWENJSnNocGtkSEhna01Vemp1K2VLcVltQ3VzN25jaGxhenhXdmJoZkJGdnk4a1dJcUNET0lvTEtrZDlRM0VIQWh4R2hnd1hTa0tQUlFEQVh0MlBQenlvY09GbWdtZ09OdGh0WmFXQjhLMjR1aGUxYlBRV05aS0J0R3ZBd0dVbEEybTIyTUh1N3ZmYmVoT00wV2xlNmRLNEdQdklIQnF1TldXSEFiS0syd2RWUzQiLCJtYWMiOiI3MzdhMTdjMzM3NmU1ODJmOTAyOTBkZmUxZDAxMWU5YjZmODA1MmQyZDNiMDAxYjMzYmU3NGRiM2Y2N2RmOGJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ill5NWE4S1FjelBkL2dqQzg4UWRnS2c9PSIsInZhbHVlIjoiR3laYTMwRjFjTGY1c1JZRzh3OFRaWmRmQVJlNG52eG5kS09uNEtLMUhLQ3NDa3ExRXVtM1FBTEtGdnMrTis5NzlrR20valNHUE1WeTZyRDhUWVVJSE5nQktzcmtMN1BYUUlDcjFMUk1yUWQ5eXpNbmhIUjU4ZzBLRVhFNFg1RXd2ME9vS1YrMUdOTFVHdk9uQUcrY1hIdGMxbDFUVzdEUFFiYzFTMEFtNFF6MFdIN3RlT0NRWXNHWVhqNXRlbGVZeUI0THhQM1NlaUxmU3UzSWJqQVlybkNhelNjeDE1SllDNXhMcUJvb1p6T0NuZEYzK3MyV0xXTTNlNFBhZXJXMGFJZ0JtdjM1UndxY3B4UGNBaEpGdWFRVGZyWk5ZK0FESVhZVkw4N3Q1R0EzaDdzUlpmUUxMemxYdkpSd2hydVUyeUl0K3RNUXRpSlhYREM0SlJsbzZZT2Ywa3Q1YkJra3g0NHExVXlLdytPTDNpUGtDbG9KYk9QVzFGb1l6RHozUFVUeDJoWXJPNHVMY2dXcll6eXVaNDRLY2E2dzVzT0xWSnZwY0tNZjIzTU1qSUxMbzhjaktrTHZNd1FrUGg5TWpBb2EwV1NOUHo2RnNEODdnd1M5WklWVzFZd2ZsRnl5UVJrckhmaEE0VDFUa1V3ZFlvQ3FVc0NPU3R4UWtZaXUiLCJtYWMiOiJiMjMxMzNhMWE1MGU1ZTdmNTJmMTc0NzBiYTM3NjRkZDlkNWJjNWM0MTRhOWJlYjcxOGNlYzYzNzIxZDUyZTg0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRoNVVUNVFyclVCeXZ2RDBFVTliRHc9PSIsInZhbHVlIjoiTzlqVnZ1cDZqU2hUYkFSTUxSdEVJR1pTdldGTm5xQURzSEt6TU9EUmJ4c3JwT0RCL094VG9uS0V1MndJL29HaXdHb3dLUVByTXdXeFlpRFlnaWZscEhDZU5HejlYV1IrZlorNS9xU1p2V1VtMXQ1UTd3TXoyRE1ieHJnQ0NKcmwvRVEwaHBkb1QyeWtFRFNobjl4cDExd0hGYTVKYlVUeTRucFFKN3l0ZlY1Z0gzazZDNHVtTVdqNE5kZ1h1Skx4OUhMRytySVhCYjcwUGJSWmVuMU1PYTd6UU5XM2ZJQ2g0MktDR0V1Vlo4Nk04bTByZ0loTjV2dktEVnYzRmNLYmxiSUoxUDNmbzdoMlpkMVdJV1dZOVEySFBKNkpSV0tCV2Z3TGI1ODE5Vko4M2hMR1NjTUVzOWdINVJjSUJ2eTVnOFExQVdidFJVZlM2RHZVVlBKWENJSnNocGtkSEhna01Vemp1K2VLcVltQ3VzN25jaGxhenhXdmJoZkJGdnk4a1dJcUNET0lvTEtrZDlRM0VIQWh4R2hnd1hTa0tQUlFEQVh0MlBQenlvY09GbWdtZ09OdGh0WmFXQjhLMjR1aGUxYlBRV05aS0J0R3ZBd0dVbEEybTIyTUh1N3ZmYmVoT00wV2xlNmRLNEdQdklIQnF1TldXSEFiS0syd2RWUzQiLCJtYWMiOiI3MzdhMTdjMzM3NmU1ODJmOTAyOTBkZmUxZDAxMWU5YjZmODA1MmQyZDNiMDAxYjMzYmU3NGRiM2Y2N2RmOGJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ill5NWE4S1FjelBkL2dqQzg4UWRnS2c9PSIsInZhbHVlIjoiR3laYTMwRjFjTGY1c1JZRzh3OFRaWmRmQVJlNG52eG5kS09uNEtLMUhLQ3NDa3ExRXVtM1FBTEtGdnMrTis5NzlrR20valNHUE1WeTZyRDhUWVVJSE5nQktzcmtMN1BYUUlDcjFMUk1yUWQ5eXpNbmhIUjU4ZzBLRVhFNFg1RXd2ME9vS1YrMUdOTFVHdk9uQUcrY1hIdGMxbDFUVzdEUFFiYzFTMEFtNFF6MFdIN3RlT0NRWXNHWVhqNXRlbGVZeUI0THhQM1NlaUxmU3UzSWJqQVlybkNhelNjeDE1SllDNXhMcUJvb1p6T0NuZEYzK3MyV0xXTTNlNFBhZXJXMGFJZ0JtdjM1UndxY3B4UGNBaEpGdWFRVGZyWk5ZK0FESVhZVkw4N3Q1R0EzaDdzUlpmUUxMemxYdkpSd2hydVUyeUl0K3RNUXRpSlhYREM0SlJsbzZZT2Ywa3Q1YkJra3g0NHExVXlLdytPTDNpUGtDbG9KYk9QVzFGb1l6RHozUFVUeDJoWXJPNHVMY2dXcll6eXVaNDRLY2E2dzVzT0xWSnZwY0tNZjIzTU1qSUxMbzhjaktrTHZNd1FrUGg5TWpBb2EwV1NOUHo2RnNEODdnd1M5WklWVzFZd2ZsRnl5UVJrckhmaEE0VDFUa1V3ZFlvQ3FVc0NPU3R4UWtZaXUiLCJtYWMiOiJiMjMxMzNhMWE1MGU1ZTdmNTJmMTc0NzBiYTM3NjRkZDlkNWJjNWM0MTRhOWJlYjcxOGNlYzYzNzIxZDUyZTg0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033223031\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-701840682 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ppHWJDQH68Bkr0QGWhSDdRuFTgW2E2h9dgdihchk</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701840682\", {\"maxDepth\":0})</script>\n"}}