{"__meta": {"id": "X6e4d6486fba79f86782a0aa0bc6ac751", "datetime": "2025-06-08 14:57:06", "utime": **********.060447, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394625.372619, "end": **********.060473, "duration": 0.6878540515899658, "duration_str": "688ms", "measures": [{"label": "Booting", "start": 1749394625.372619, "relative_start": 0, "end": 1749394625.985971, "relative_end": 1749394625.985971, "duration": 0.6133520603179932, "duration_str": "613ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749394625.985984, "relative_start": 0.6133651733398438, "end": **********.060476, "relative_end": 3.0994415283203125e-06, "duration": 0.07449197769165039, "duration_str": "74.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43933360, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01739, "accumulated_duration_str": "17.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.026895, "duration": 0.016829999999999998, "duration_str": "16.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.78}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.049732, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 96.78, "width_percent": 3.22}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1830627281 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1830627281\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1195907245 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195907245\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-808684366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-808684366\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394379618%7C66%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtmaFlFQTc1MmtTam12VmxZZjk5ZFE9PSIsInZhbHVlIjoiSnExK0JrcWJDMDJsMnQ0SlN3R0VHdFJRK2pEV1k5MlM4TGZYeUdUOVVUem9NVUpSZVN4R1BzUzFIVXVUUitjMGZQZHh5TE9zQWNrc29IMHRzZHI3NmdkMUpVWnBGRGUyL1ppN3U0S2wrcCswRytyVEtGTVBaZXRWeG9FTnRXRnpiTmdHZ1JFVUdaRHk4dGdMdjl6MmlBOGlvQ2dGNThyTW1idnpENytxd2hPM2M1a0FLbDVQVndnYUlhUmVoVUxTT2hFR1NqRk55TlRSa3VhSFhSRUFMSlh5MzJwQ2IrMVU0ellCRXVZcU1UY3h0aXZsSlAySVd0cG1Xa0p3NDJ2dlFrRzRRbEM5TXF4dGN6QVJiRGNSRTlRUmwyeUZ1dFYxU0V4aXY0V240aEZSKzlSd0hrSXdPelFTajB0WElBWWpjYzhWTHJqWGtERzdXT2JPd3h4QUMxQjBROXpRK0hpdHhSVzZsNDdaSVFsUStmQjlGVzFHVzQvcG1JbExtM0VwR05nTmd4bU1BYVM4ZmZKMEY5MG5waGVCYjRqbFBRZFdBc01kdzhzNng5K1hNQ3R2VnZzYzRFSFpEakRBU2lFVFRSNytJNUFQTWwybVRHWGFaTzV4WGhJZ3pIb3N0aE1URGZLRWd3QnoyMTA4ZDN3TksvZUM0bTVXZGVyZ1R4dDEiLCJtYWMiOiI5MmE0OTA0MzEyN2IzODkxNTE2ZTMzYzVjOGE0Y2RiODg1YTU1MWY3ZjBiZWMxZDdkNjYwODJkZTczNWYzNjM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRJMm85OTdjckVnN01DeWFjRHR2blE9PSIsInZhbHVlIjoiWWliNG9MMjg2aVJJL2NPSkt0Sk1EUER6T0Y0dDR4NjVHWUNSNlE3eGhPUktzK2sreTZ1Sms4MDlpdzdMOE5qNk5aTE40RlNhczJYRXVkOUlWVm1FOXhBQ2UyTW1ORjI3Y1VsNHFaaGFFaTVKY2Yxbnh0Y0hPVFQ0YjNNMG1aM0ZXbzlkTUM1OThSR0VObkNsTmQxK2g1SVBIZTREZ3BvSklsOFB1RWc1djlBbVNGZWgzejVUdXlJcmZaUENJc0plbVRVVEpaMXphWkRwUHpNREhEOVRGcmJleDZnTTBvKzRNM01yT3gxb3RYTEJNb3JJMXRIUkJuVjFrQnErcit2OFlibnNiRHBzWFlNMDNaMTJPK1RXQjJOSis4d1Z1MDk5M0JCN2RkOGhOSHBDT1o1Y2RrdkxBc3VHbVp2ek4rWHMzK05KT2ZVKzF5dzh0OXlBa1hXanRISlFSZjZaNjgxaW4rdklGL1FQVDZrNFBHZFZvd1U5bUFwTGxqUytlaE1GdlhrY3NZUXV2V1IvRGVsVVZxeTVNVlJEVkx3dGl3YzhWUVhiSEVabWhCNTg1anJreWEvNDkreW5SM21YNTRGdUpVeE5ubHdqMzlGMEN0NWFQV3JVeS9xWHFvbDRISHpLYjF2MGx4dE1SZHlZKzZVRmlGQlRYMFZmRXRBOFBZWEIiLCJtYWMiOiJiM2VkNDMzMjk4NjYwNDJmYjRjNGFiMWY3MmY4ZjUyMjBmOGI4NWQ2MDBiNDVmMzQ2OTAxMGI3ZDAxYzdiNDk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1019895325 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019895325\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-740003293 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5FaDhOaFdFS2ozUGJ0bnVldzgvcGc9PSIsInZhbHVlIjoiQWhZLzRuRmlXMjA2LzlaQllqdHJMN2V0aHdwS3FPNk93RnowRUN4TmpYYWUyZ0U1ZENCa1k3QnJ5UHRKQnY4QmRVNVEwMnFrN3dWYWF5cEQvM0hLQ1F3RFppMno0QllpeEZZRytZNUcycGs2eVBZRWxjamV6NjYyNzdVR0t6RkNkOFZjWDkyYzJ5MjFuem8vbTZjTE5zUjVzWitHUU45aHFXalZ0c1A5RWlNYkpQMmgyWjVLMXRJenZZbUg1cFBnVDl5MmxNZUQxb2lSNXZjTTEyWDNoOVAweXh6Qk1ZNTFtR25ZN0xWNXkxNmJvWU1OSEI1ZXFqSTJWbFJMeG54bnF5TU1VZ3JwKzViVFdmUW5KdnlncUhLSllEWWNjQnF6cWRaaWxhbnZseDRQMktRN3pVQ1FYNnJ6WG8zeXlBZ2ZLQWh6UFlmemhTdEtjRGRXRlcwVDU0c1YxQVBHMERySlhZelExMHp3SS9MSmY2eFZjd2tnbzc1emdFVXlzWHpaeS9rQlg4SitwTEZBeDVhSGJnbW1laDllMTVDakdRU1NkWjBIZlY1a0VZWldjR1h6c3hjZkNNSzl2N1Z2UExKWnN6bFk1L085dkVnQ3hNUUVrK0hBU2x6NkZvaFhZTnA0NGdVb3hLY2UvYTh0eWRUSlRDZ0ZpQks5dExQdVNTOXIiLCJtYWMiOiIzMDgyNDE1ZTAyMGYxNjVjMGI2YjJjMDUxZGI4YmFmYmM2OTExNjAxMzZlM2E2ZTg1MTUwODYyMWViZDhhZjY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpiWVZKaERiMWZHQkJFSnhCdXpvOGc9PSIsInZhbHVlIjoiL0pPdXVmTTlDSmpRWU1LREZldlVQN3NFZlZFYXBqMlI1dFkxem45S0N5MW1ydEhYZC8vSHhqZTVSaTJObWk3VGtMS2x5d2djOTVnUHJEVlNxbWpQYy95SVJGYWp2ZnpWVWxnRWMyTmJnakFucXhCbmp6aUhRT2JpRGViVTNGL3ZzUHNsMXIxc3NnM3lTR0p6ZGZIeWF2RDR6b2xabENocHJ4YjVNaGtFVmFPQlVrMUlCYjgxVU8xdy9wOVk4MWlIZ0JOOVFycGRQQTRzM2hQRUJIWTBuNUF1STBvdVMvaGJIVVhFaDJiM2R1MGMwQU82UkNLTnpkTkRWNW5SRWZjOTRLWmlnY1YvaDN2cnhIVytWVCt4UmVpeGtIQWI0OWRZV3RSR3UyZkNMajFKQlRxOUo1bFlSajcwcDlvR2E2a0N1T3F1dFlmZW5jMmdLcDJ4UTZ2anZjL0cyUjFwRytST24xVXhONzl2SGJFUkkvbFYzd2dhWTRnbTdlUFUydDZ6VXBRekhUbXpQZ0NKTWsrMnRvWHExc3dIZGRrUEczMVBvdUlRUEUzQjJyOC80bnVoYVQrZUovbDM1SlRUU0RPOGkrVCszTko4WmJPZ2hwSzRVVEVpQlpwOVhiQTI3dEZPbDVhV1JMM3FEcTlPbUVyUmw1NndDOUM0SlFZUmhoaVMiLCJtYWMiOiIxN2Q3ZTk5MDZlY2FhNTY4NWRkNGU5ZTc5MTQwMmYzN2FiOGIxMzE0ZDNlYmNmNjhmZDU1M2I0NjhmYWQyZWUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5FaDhOaFdFS2ozUGJ0bnVldzgvcGc9PSIsInZhbHVlIjoiQWhZLzRuRmlXMjA2LzlaQllqdHJMN2V0aHdwS3FPNk93RnowRUN4TmpYYWUyZ0U1ZENCa1k3QnJ5UHRKQnY4QmRVNVEwMnFrN3dWYWF5cEQvM0hLQ1F3RFppMno0QllpeEZZRytZNUcycGs2eVBZRWxjamV6NjYyNzdVR0t6RkNkOFZjWDkyYzJ5MjFuem8vbTZjTE5zUjVzWitHUU45aHFXalZ0c1A5RWlNYkpQMmgyWjVLMXRJenZZbUg1cFBnVDl5MmxNZUQxb2lSNXZjTTEyWDNoOVAweXh6Qk1ZNTFtR25ZN0xWNXkxNmJvWU1OSEI1ZXFqSTJWbFJMeG54bnF5TU1VZ3JwKzViVFdmUW5KdnlncUhLSllEWWNjQnF6cWRaaWxhbnZseDRQMktRN3pVQ1FYNnJ6WG8zeXlBZ2ZLQWh6UFlmemhTdEtjRGRXRlcwVDU0c1YxQVBHMERySlhZelExMHp3SS9MSmY2eFZjd2tnbzc1emdFVXlzWHpaeS9rQlg4SitwTEZBeDVhSGJnbW1laDllMTVDakdRU1NkWjBIZlY1a0VZWldjR1h6c3hjZkNNSzl2N1Z2UExKWnN6bFk1L085dkVnQ3hNUUVrK0hBU2x6NkZvaFhZTnA0NGdVb3hLY2UvYTh0eWRUSlRDZ0ZpQks5dExQdVNTOXIiLCJtYWMiOiIzMDgyNDE1ZTAyMGYxNjVjMGI2YjJjMDUxZGI4YmFmYmM2OTExNjAxMzZlM2E2ZTg1MTUwODYyMWViZDhhZjY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpiWVZKaERiMWZHQkJFSnhCdXpvOGc9PSIsInZhbHVlIjoiL0pPdXVmTTlDSmpRWU1LREZldlVQN3NFZlZFYXBqMlI1dFkxem45S0N5MW1ydEhYZC8vSHhqZTVSaTJObWk3VGtMS2x5d2djOTVnUHJEVlNxbWpQYy95SVJGYWp2ZnpWVWxnRWMyTmJnakFucXhCbmp6aUhRT2JpRGViVTNGL3ZzUHNsMXIxc3NnM3lTR0p6ZGZIeWF2RDR6b2xabENocHJ4YjVNaGtFVmFPQlVrMUlCYjgxVU8xdy9wOVk4MWlIZ0JOOVFycGRQQTRzM2hQRUJIWTBuNUF1STBvdVMvaGJIVVhFaDJiM2R1MGMwQU82UkNLTnpkTkRWNW5SRWZjOTRLWmlnY1YvaDN2cnhIVytWVCt4UmVpeGtIQWI0OWRZV3RSR3UyZkNMajFKQlRxOUo1bFlSajcwcDlvR2E2a0N1T3F1dFlmZW5jMmdLcDJ4UTZ2anZjL0cyUjFwRytST24xVXhONzl2SGJFUkkvbFYzd2dhWTRnbTdlUFUydDZ6VXBRekhUbXpQZ0NKTWsrMnRvWHExc3dIZGRrUEczMVBvdUlRUEUzQjJyOC80bnVoYVQrZUovbDM1SlRUU0RPOGkrVCszTko4WmJPZ2hwSzRVVEVpQlpwOVhiQTI3dEZPbDVhV1JMM3FEcTlPbUVyUmw1NndDOUM0SlFZUmhoaVMiLCJtYWMiOiIxN2Q3ZTk5MDZlY2FhNTY4NWRkNGU5ZTc5MTQwMmYzN2FiOGIxMzE0ZDNlYmNmNjhmZDU1M2I0NjhmYWQyZWUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740003293\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-239954854 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239954854\", {\"maxDepth\":0})</script>\n"}}