{"__meta": {"id": "Xe085218061666d144488269a5ce378ff", "datetime": "2025-06-08 13:00:29", "utime": **********.62996, "method": "GET", "uri": "/vender/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387628.215724, "end": **********.62999, "duration": 1.4142661094665527, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749387628.215724, "relative_start": 0, "end": **********.343482, "relative_end": **********.343482, "duration": 1.1277580261230469, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.343509, "relative_start": 1.1277849674224854, "end": **********.629993, "relative_end": 2.86102294921875e-06, "duration": 0.2864840030670166, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51442072, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x vender.create", "param_count": null, "params": [], "start": **********.590984, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/vender/create.blade.phpvender.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fvender%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "vender.create"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.619234, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}, {"name": "1x components.mobile", "param_count": null, "params": [], "start": **********.622763, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/mobile.blade.phpcomponents.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.mobile"}]}, "route": {"uri": "GET vender/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "vender.create", "controller": "App\\Http\\Controllers\\VenderController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=50\" onclick=\"\">app/Http/Controllers/VenderController.php:50-62</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.03236, "accumulated_duration_str": "32.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4309928, "duration": 0.02637, "duration_str": "26.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.489}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4813669, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.489, "width_percent": 3.368}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.534262, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.858, "width_percent": 4.759}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.542947, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.617, "width_percent": 5.315}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'vendor'", "type": "query", "params": [], "bindings": ["15", "vendor"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\VenderController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.557385, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "VenderController.php:54", "source": "app/Http/Controllers/VenderController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=54", "ajax": false, "filename": "VenderController.php", "line": "54"}, "connection": "ty", "start_percent": 94.932, "width_percent": 5.068}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-12705507 data-indent-pad=\"  \"><span class=sf-dump-note>create vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">create vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12705507\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.555378, "xdebug_link": null}]}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/vender/create", "status_code": "<pre class=sf-dump id=sf-dump-706166384 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-706166384\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-349584529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-349584529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1558527791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1558527791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1775609040 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387626736%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9nMGZUTnJmUDFvOXVuVG5Bd3ZkOVE9PSIsInZhbHVlIjoieVh5TGJjVFZmTW41TDB4alZTWko5SWdnK1l0ZVdKMnYzRUVsREUzQXpiUHVTY0hhdVJicGxTL1FQRlRVczc5TUVSamR3Vzg0WDJESHdvOWxBY1BrdGFjZUVjdjhtYjNnb29majhnUWtSQzFQZ05HMnJET2xqT3h1MzlzbGZWM0NJcmhrcHcvL3ZrUGdrU25zcHJmR1ZPbUJLakdCdkt5QThCUG9LRVdGQmM5STdYeitodFUxRHlGTUJXQmhLVFZaSHg4SDFJNmVIMVhiS3U5WWZkd2FzNWRFdjlWR1pKbEdpR25NaC9sNmo1cThjY3NnWThZNjFkS2FLYXpSOTNRUnVxMGJ0K0haQWthSXB6T05lemhwSjA3VDlUdVJDbjZyYUtWc2ZHNFEvbDgvblJxMllsOGVubnRtdGpBQ3dCbVR1RVF0UEJRY0hQSUxiWDNsODhPQm0xTFFhTEgrOThKOXhJQXdnSW1vTzdOcVVGQisrY05ETXpIMCtqR1ovRFJVUzdUTi9RYzBlSEVBemVweGxOUDNJTlI3MlZsbzNOOUUrZkJsSmUzZVc1UFNnYUNPUEFqUFd2WDllVFpteHpCbE5zZGFrTmRHM0JsQUQ5ODcydTQ1cktEQUlRd2R1NzcxOWQwazROTEFhUnhodm5IUmc0YW9DaVltRWkwY3VsdzQiLCJtYWMiOiI0NGY1YTJlNDUwNmJmYTg1OTk5NzlhNDAwYzcxZTU4ZDQyYWQyZDllMWVmMTE5MWY4NzdhNDMyMDEzZTg4MGI5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllXOU05OTJmcmwyMGRrMElqZ2xIL3c9PSIsInZhbHVlIjoiYy96SnA5c1BacVhJWkpLRTFhY2lEMURFY3QwcHc0ZEphbTRpVlFwTDFxelJ3TDdITmRBQUh6MUFCSjZVb0ZnM05ZaUsxUldMcDVOSmxrM2U3UVF5dlRXUXNYeXpyV0R6a2pPMlNkNVZUaEJMS09jdmZCS1VTSVV1bHkwMmN2SUFJQTlCSUVRc1dBNW4rMFg0Rm9lQ3Nxcmk0bHFZaHRTbThGR0daZGNjOXkxcVUwb1BMUlg3T3BGRVM4aUZYSERTNlRWTUhybGkxdFpOejFCbUlpSnVMMitkMElJb2VOSmlDRk5iU040VVF6SVBnRmllQ05sZmhTcE0wUDFRNlBFei8rRVhhbmdFY0ExMU5aSzRkTk5tOUNBemI0N1BPbnI1Q3hGNWJyeFdOZUtVbkhXNVhMOXZ1eDdPVm1lYUxGMXQvdnZWYmhVRUduQmZsUVRJYURiUm1qbnk4TmFhRDJFNmM5SkVDbkdOb0g5ZUlFTWVKVEVnZjlGNHR3Q2RIaUlkK3phMEFFWEZFRWQ1N0FuY2I4dlVkYTA0NGI5bWN2S2VaMkw5WUlrSkFjRWpxSHJBQldqc3VHb2s0Z3JIMmI4aHo5M2pXcGJJUGVoemZJSjVXVzIvR2IxYVdtYTBqck9JRVlod0JvODBwdm1OeVp6dnBnditaWUZMQVF3RU5VQWEiLCJtYWMiOiJkYWQyZjZlMTZiNWE0MGM2MzFmNWUwOGE2NGZiZDdhMDhmZjIzMzlmOWZhMWE3NmZkZDQzYmVkY2NlOWZlMWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775609040\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1268229896 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268229896\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-292699305 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZaV0ErR3ZoNlJQdFhLZFJ6a21xdGc9PSIsInZhbHVlIjoiMmE3UjBxTHQwZW0rRXM4M2JEZlNQWmtTbXFTd1IzOWJ3dkdWZ2R3bURwSGp1K2J1dEh0alZhaS9iSElvczVIWWhBWDVVTVppL2ZKY09ENXVNUmpEcnMxQlNNZ0JBMFZWR3hIbVVGTWp1Zk9Da04wcVlMYzFjRjA3Z3RhV3VJZkRYcG5Xdk9hQUNuWEtubERYc3hRNHFJaVllbEFtSStsSHdNYm8wN25oSDFqaHRNVkhrQm9peW1YdE5GNEU0SGJnUTR5M1E0aUtlMUhFYmFwc09yZHA3UjNIVTZKUHoxa0ZTanhFNFBrRGVoZzlWdklzNVgzKzRlS1ZvL09pQU4vSlNURzdKTmJFZnRjOTErRURCbXFDbzNkRE8yU045b1pYajM2ckladVRyK1J0TWpTRmpXbGk5YVBrZkhQNTNJeWVvK1dsa1Rxay9ucGZBeWcvdzd6YVU0MXNhdzNtR1NpU2lTSENYd0J3WEQwRmJleDh4OVMzRU1icmpyVnBjekl6bmZBZnpLcitmcVc2ZEpzRXluTVhlNWdZYWhNSlJ2VlEzK0Q0ZzB3WTBoMzBDa2t6d0trM21DQU5qSU1VaG1YVU5hTVR3YXpRVEdTZ0UvTjJGVjFkZjVFVHZxWEplV0crUEFqQjlmSnZBRDV4a1ZDRlpsMmdGWnVnUGJ0eUxiM3AiLCJtYWMiOiI5NjI3MTZiMTY2MjM4ODIzYzhjMDhjNWM1NzY3NGUxMzcwOTE5NDY1MDU0MmMzMzQ3OGZkZWViOTk1MzJkZjhkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFrTVdvaHAwY2Q3REZSSExPWjlJMkE9PSIsInZhbHVlIjoiN2xhWEpmTW9icjIvQW00enZoaUFORDVTYW9WeTMwRFdKT0kvT0Q2Q09KNjBXNmExdWlJMEFkakxubDByalVwaEhUZVV0SVZVYnc4OCtqbGt4QjZLZFMxQTZYa0RsSTBsR3k2OGdmbUgzblE0SkMvRUsrL29weEtFcXJIOFlCR0FFY1JHRUxlV3ArUTJOellpa3BwTzVycTNqVUM1dC9iemtMQ09YUmd0dHZxbG9GQnpXR25ZMjVZbDN6cWM3VWJ2NEF6RHMvNktNRXpKVUFIbHJyNzV4bWNzYU1RcGxFbkRRVWxKeVhScWxVVGJxYWxOa29CVUJPdlRSeEZyQkhCQjdVZW5xcnJSSi8wUTFGRmtkUGUzdTJCQng2R09xTGVOWXRPdVZ3c3hWaWFSNTZPbktDaUxTSnh4c3dmZy92RlFmZXNwYncvYk9zL1RYMUlYaUZMUUJrMVlVVFpHWmFVSkVkcktlM1NpVTROR0RwT2tLd3VONzA1ZW5obVZjcmtoNk9rdzF1VkwxbmpmaUZST2xnbzhWWjZKVkdwRldQSlFsUmlEQmVpbVlPcHpMeHI1bHhlb3Jmc1N2QjU5S2R4bEtzaEJSUTJBOHV6Z2xNREpwbmVSRllMM1A0cEFQcTFTamRvTGljREtLZFl0bmZ4OVVpN3hoSTRiU3o3ZFQ4eWkiLCJtYWMiOiIyNDRkZjdmNGYzMzJiMDI5NzhmMTBhODQ4MmViOWE3NjExMWI3MGFlZGM2YzZkODMyNTljNWM3NWVkYzQzYmUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZaV0ErR3ZoNlJQdFhLZFJ6a21xdGc9PSIsInZhbHVlIjoiMmE3UjBxTHQwZW0rRXM4M2JEZlNQWmtTbXFTd1IzOWJ3dkdWZ2R3bURwSGp1K2J1dEh0alZhaS9iSElvczVIWWhBWDVVTVppL2ZKY09ENXVNUmpEcnMxQlNNZ0JBMFZWR3hIbVVGTWp1Zk9Da04wcVlMYzFjRjA3Z3RhV3VJZkRYcG5Xdk9hQUNuWEtubERYc3hRNHFJaVllbEFtSStsSHdNYm8wN25oSDFqaHRNVkhrQm9peW1YdE5GNEU0SGJnUTR5M1E0aUtlMUhFYmFwc09yZHA3UjNIVTZKUHoxa0ZTanhFNFBrRGVoZzlWdklzNVgzKzRlS1ZvL09pQU4vSlNURzdKTmJFZnRjOTErRURCbXFDbzNkRE8yU045b1pYajM2ckladVRyK1J0TWpTRmpXbGk5YVBrZkhQNTNJeWVvK1dsa1Rxay9ucGZBeWcvdzd6YVU0MXNhdzNtR1NpU2lTSENYd0J3WEQwRmJleDh4OVMzRU1icmpyVnBjekl6bmZBZnpLcitmcVc2ZEpzRXluTVhlNWdZYWhNSlJ2VlEzK0Q0ZzB3WTBoMzBDa2t6d0trM21DQU5qSU1VaG1YVU5hTVR3YXpRVEdTZ0UvTjJGVjFkZjVFVHZxWEplV0crUEFqQjlmSnZBRDV4a1ZDRlpsMmdGWnVnUGJ0eUxiM3AiLCJtYWMiOiI5NjI3MTZiMTY2MjM4ODIzYzhjMDhjNWM1NzY3NGUxMzcwOTE5NDY1MDU0MmMzMzQ3OGZkZWViOTk1MzJkZjhkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFrTVdvaHAwY2Q3REZSSExPWjlJMkE9PSIsInZhbHVlIjoiN2xhWEpmTW9icjIvQW00enZoaUFORDVTYW9WeTMwRFdKT0kvT0Q2Q09KNjBXNmExdWlJMEFkakxubDByalVwaEhUZVV0SVZVYnc4OCtqbGt4QjZLZFMxQTZYa0RsSTBsR3k2OGdmbUgzblE0SkMvRUsrL29weEtFcXJIOFlCR0FFY1JHRUxlV3ArUTJOellpa3BwTzVycTNqVUM1dC9iemtMQ09YUmd0dHZxbG9GQnpXR25ZMjVZbDN6cWM3VWJ2NEF6RHMvNktNRXpKVUFIbHJyNzV4bWNzYU1RcGxFbkRRVWxKeVhScWxVVGJxYWxOa29CVUJPdlRSeEZyQkhCQjdVZW5xcnJSSi8wUTFGRmtkUGUzdTJCQng2R09xTGVOWXRPdVZ3c3hWaWFSNTZPbktDaUxTSnh4c3dmZy92RlFmZXNwYncvYk9zL1RYMUlYaUZMUUJrMVlVVFpHWmFVSkVkcktlM1NpVTROR0RwT2tLd3VONzA1ZW5obVZjcmtoNk9rdzF1VkwxbmpmaUZST2xnbzhWWjZKVkdwRldQSlFsUmlEQmVpbVlPcHpMeHI1bHhlb3Jmc1N2QjU5S2R4bEtzaEJSUTJBOHV6Z2xNREpwbmVSRllMM1A0cEFQcTFTamRvTGljREtLZFl0bmZ4OVVpN3hoSTRiU3o3ZFQ4eWkiLCJtYWMiOiIyNDRkZjdmNGYzMzJiMDI5NzhmMTBhODQ4MmViOWE3NjExMWI3MGFlZGM2YzZkODMyNTljNWM3NWVkYzQzYmUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292699305\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-565772865 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565772865\", {\"maxDepth\":0})</script>\n"}}