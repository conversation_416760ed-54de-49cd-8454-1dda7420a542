{"__meta": {"id": "X79dc1053333ad7b42929947cebba65f0", "datetime": "2025-06-08 14:57:31", "utime": **********.496729, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394650.753823, "end": **********.49675, "duration": 0.742927074432373, "duration_str": "743ms", "measures": [{"label": "Booting", "start": 1749394650.753823, "relative_start": 0, "end": **********.391468, "relative_end": **********.391468, "duration": 0.6376450061798096, "duration_str": "638ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.391481, "relative_start": 0.637657880783081, "end": **********.496752, "relative_end": 1.9073486328125e-06, "duration": 0.1052711009979248, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45396880, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02127, "accumulated_duration_str": "21.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.44349, "duration": 0.019370000000000002, "duration_str": "19.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.067}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4790502, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.067, "width_percent": 4.607}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.485033, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 95.675, "width_percent": 4.325}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1683686009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1683686009\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1164341651 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164341651\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2109622761 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394643965%7C67%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikg3ZzZWckcwblR2QXY4UGRVWFYvQnc9PSIsInZhbHVlIjoiOHljVnpOOWxJb3d4ZUhLWVpLSlZ0NmwxZDRzYUVGYjl0SVlSbk4wamRFOGxBbWUyYlc1Vitnc3VjVWw3SDJmb29OVWJNbWxZYU91dlRLQVQxNFdLWk02R1RXTDhDaERRVFBtZkJuRi9SZUgzV3ZZVEJaSU9ScVpCZjlMRnk0bkhWbzdHK3RzanRrWmFmN3NvQ0psaGRmaUxBbVp2S2trMlUzNEtzS1dyby9MV3I4ZXdUNHRzdTJSaW5nWElZTlVJeGo2emxvbDBwa0N4TmgwWHo4ZXFxSXdwWDR2Nk82bURZbHM2cEpmelVvV214dHJIUnZEVXVlMlJLSHF3L01JYVFJeENyZDFpWmIrWVh0SW1JSGpBRE8rOVpwa2ZmSVdUKzFpd3IvV1plNnpSTUpIZkN3V1J0WnpIVEd1L203VGF0V1J4SDgwV3F5YUcvdUNPM1hQTGduMEp2cDFJL3VQZ0Q3R1B1YXhrUEgraTFSVk9ZbVhjeFluQ2k5RXRLZDZoaGZvNjNJVHRoK2NyaHIzOURlTGxlQlA2Z1FMSXpiU05MRHNUejhNVEF0emUwcUdnOXpZVFVFSTNUcWdUWnh1TEFpTVlic1pyQmF2cm5HN0gvdzc4MktjdENvYU14WVVUWmxaM21xU0prVmVRbGR1Q2MyWXVkZC9JT2xQT2ptbEMiLCJtYWMiOiI4NTI5YzA1OTkxYjQ2OWQ5MTgxNTQ5MjU2ODlhZTI5YmMwM2ZmOTA3MzkyODhjZjc0ZjEyOTY4NmE3NjA5MjBiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndNbGVJelhOMjRCd0p3SGp1cVZMOWc9PSIsInZhbHVlIjoiMStaSWs4NHBNR3BBdXptVWVHTmdneHZZRE1Qa2dKOTl2UUEvenE1YmowSVNxUnpEaHIxZFNlRWt5Z0JLVS9SRGV4cUdxMGNzSWpwWjBnRnJmT1Fqd0ZiNGhUZDFjWXJKTlc3WTZteFhhZlpvamxEaDZMaTVvL24vRms5Qkp3UWcrMytEWjIyaUlQenVpTkxac0UvQmVTeWpYMFBEdFhrODgxOTJEd2xoWWVDZnhvYmQ3NFR6Vk9MU0tTei9RdzRjR25BRFpIb096MlFiUnQ0Yk5vRC80RElDeUsxV0hST2ptRFVJSGlPS1l2VUFGTEdaQlFWRFp1Q1lnczc4alQzYWJIK3NDSDUxRkZVY2tEV1ExWjhPVHNLSktERy9PdVM1STNVOXBVQkJlWWVHUGl2b3FyL2t4bk10L3NpZ3RITmorOVVjTzFnd3JnY0MxS1VDZUhwQmtNVUh3cHZTSjZBYTAyVEE5UFp1THBZb0cyTVJ6RFZXNWN4T0YvcUgrMkFqeHNlQ2M4T0c1MEdGYkVuK1Z3bDk0UjlNTVFNL3RFN3E2SU4ydEkwUG4xUUV6UVI2UnZ4UE05c0dBVDJYcExhTFBFTEN4Y0lIajRnV0NySWpqbkNZZlBSZVJXa3p1WHZQdXpNa1ExL0xtWW8rK0haTExLNWROZ2IxaHlnNzA5TjIiLCJtYWMiOiI0YzQwZjM5MDU5YTFhNDA5OGU4NWY4YTUwZmQzMTgxMmFhOTZiY2I4NGNkMzkwNjE1MGEyNDBhZGE1ZGNhOTRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109622761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1243460885 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243460885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-654913989 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndFeTZNcVo0S0JEL09KMjE4WkJ1TVE9PSIsInZhbHVlIjoicDNZRFBxcEx5S3J4RkNSaENUUDhicFJkT25lSUtYdGlqTTlkRE1hZU1iY1J0WVFqdkpZK3RhOGdGVEZzNEU1K3c1UFZjTHJNaG85c2tKVkxiV1hSc3pmK0h3WlVab0JaYTFnS2FycERvNS9PYXpDaE1nR2lVQUZIenlLbWY5RU4yWTZYak0vWHk2d2VQZUJZZlBlMlNWazNuZmRPVDRHbTZTZW9XY2pOYitDUUYvcVNxc1llYnBBN2VQV3p2U01ZUFpIT3c4N2lNUnhjMjN5Mm5LMmhIMzJLNVFHN0hhRmo3c0JseUZtdFRGNWFkaW9xWFF2TGlGeXdqZC9QTkJOQTNodWxCbzUxSE85ZDh2Q2lodWlJZE5PUmxOaDFqOHk4cklEaWxaRkJZQVkyRFc0NXNXQktEZEtIOTNnVlZTb3MxS1JuTGo3NmdLVk16RHJ0dHpNU0Z1aWIzS2FheXBmQS9DNS9HS1Bvbk5URjZPSU5PVytwNzlUd09LZ0tPaFJpQXMwdWJDT0hpZzQzc2t5QjBuS2tMbkZXSElCVU81VUxaczhTKytBdHlwckt6TVJrZUVVRUZ1aUdFZmpXc1hIbVRPK25yVGsrc0NRbVV4aG9uOXFhMHdjSTlEVWVXRW9tdEY0cFkzTGE2K3BUclg5dmhqT05sSnNlVmR6SUhONU0iLCJtYWMiOiJjODEyZGJkMzY5NWJkMmRiMjk2ZDYxZjdkYjRlODIxN2I0ZTAxMmViNTMzMWZlNmE4YWMxYmRmNGZkNzc4ZDU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVsaXdnc1hGT1NhcjhKUlB6T1ZpRGc9PSIsInZhbHVlIjoianpGazZ5VVZpeENEQTJwUlJDODVQQkhZWWZsaWl2TTVJRFBGcGp4OXVaSFd4dGs1SEZiRUIwcytFWkRjS3pxK1RFY3FSUExTNG5ZcE1DZUQxeVRvdzlVMUFKMTBIQlNHdzYrbk54a1N1L3NSZkF3elBZbTNUM1hLVEZScU1CTW9hY2FndGNZU3ljNUpQVnVKcy9id2RKbnZNTzlyWmlGVXQrTk9VRytZazNNTHY2c0FaR1prempNOHluZGp0NEp3clRMUWxSbWRET0crUWl1UUkyMUdKd2Z0RngxWjhJeXpBS1ZJdVNHZ2VScFBnamtpaWs0bGpia2JzNzdZT1pGeWt5MUErWGoxWTVQd3dNMElBYkpPSW9TZG5FVk1TcHJqSzNxZTlCOWRsd0M3T0JVOEsyWGZZV00yWG1sREh2MUxHaXlOUHRkQytoNWRRSTVLQ0NzNXhTZXdXNkI2RWNnOWdYaTNuSElpMVUzUmprY2lpTzJEZVI2WkhvK3R3NklJTFpyZ0R5S1psRjUvZnRKRFBUeDNxVmYxRE5Hc2xDdnVnN2VnYktyWFY1clJqTEI5TzVnS1hlc01USmJzbzNsTi9kalgwdk1FVGpaTzhwYXRYVzE3T3BVSTNNOEtSMS9zN21Jc3lYYW40L085TFpYdHhaVlhOTnRFcEZBMXR4NDIiLCJtYWMiOiJkMjI0ZGVlYjIxMzczODdiMGRlZmQ4MjMzOTZhNDgyZWI0ODNhM2YzY2Q1NTJiNGNkMTI5ZWZiZWRkNWNlMjlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndFeTZNcVo0S0JEL09KMjE4WkJ1TVE9PSIsInZhbHVlIjoicDNZRFBxcEx5S3J4RkNSaENUUDhicFJkT25lSUtYdGlqTTlkRE1hZU1iY1J0WVFqdkpZK3RhOGdGVEZzNEU1K3c1UFZjTHJNaG85c2tKVkxiV1hSc3pmK0h3WlVab0JaYTFnS2FycERvNS9PYXpDaE1nR2lVQUZIenlLbWY5RU4yWTZYak0vWHk2d2VQZUJZZlBlMlNWazNuZmRPVDRHbTZTZW9XY2pOYitDUUYvcVNxc1llYnBBN2VQV3p2U01ZUFpIT3c4N2lNUnhjMjN5Mm5LMmhIMzJLNVFHN0hhRmo3c0JseUZtdFRGNWFkaW9xWFF2TGlGeXdqZC9QTkJOQTNodWxCbzUxSE85ZDh2Q2lodWlJZE5PUmxOaDFqOHk4cklEaWxaRkJZQVkyRFc0NXNXQktEZEtIOTNnVlZTb3MxS1JuTGo3NmdLVk16RHJ0dHpNU0Z1aWIzS2FheXBmQS9DNS9HS1Bvbk5URjZPSU5PVytwNzlUd09LZ0tPaFJpQXMwdWJDT0hpZzQzc2t5QjBuS2tMbkZXSElCVU81VUxaczhTKytBdHlwckt6TVJrZUVVRUZ1aUdFZmpXc1hIbVRPK25yVGsrc0NRbVV4aG9uOXFhMHdjSTlEVWVXRW9tdEY0cFkzTGE2K3BUclg5dmhqT05sSnNlVmR6SUhONU0iLCJtYWMiOiJjODEyZGJkMzY5NWJkMmRiMjk2ZDYxZjdkYjRlODIxN2I0ZTAxMmViNTMzMWZlNmE4YWMxYmRmNGZkNzc4ZDU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVsaXdnc1hGT1NhcjhKUlB6T1ZpRGc9PSIsInZhbHVlIjoianpGazZ5VVZpeENEQTJwUlJDODVQQkhZWWZsaWl2TTVJRFBGcGp4OXVaSFd4dGs1SEZiRUIwcytFWkRjS3pxK1RFY3FSUExTNG5ZcE1DZUQxeVRvdzlVMUFKMTBIQlNHdzYrbk54a1N1L3NSZkF3elBZbTNUM1hLVEZScU1CTW9hY2FndGNZU3ljNUpQVnVKcy9id2RKbnZNTzlyWmlGVXQrTk9VRytZazNNTHY2c0FaR1prempNOHluZGp0NEp3clRMUWxSbWRET0crUWl1UUkyMUdKd2Z0RngxWjhJeXpBS1ZJdVNHZ2VScFBnamtpaWs0bGpia2JzNzdZT1pGeWt5MUErWGoxWTVQd3dNMElBYkpPSW9TZG5FVk1TcHJqSzNxZTlCOWRsd0M3T0JVOEsyWGZZV00yWG1sREh2MUxHaXlOUHRkQytoNWRRSTVLQ0NzNXhTZXdXNkI2RWNnOWdYaTNuSElpMVUzUmprY2lpTzJEZVI2WkhvK3R3NklJTFpyZ0R5S1psRjUvZnRKRFBUeDNxVmYxRE5Hc2xDdnVnN2VnYktyWFY1clJqTEI5TzVnS1hlc01USmJzbzNsTi9kalgwdk1FVGpaTzhwYXRYVzE3T3BVSTNNOEtSMS9zN21Jc3lYYW40L085TFpYdHhaVlhOTnRFcEZBMXR4NDIiLCJtYWMiOiJkMjI0ZGVlYjIxMzczODdiMGRlZmQ4MjMzOTZhNDgyZWI0ODNhM2YzY2Q1NTJiNGNkMTI5ZWZiZWRkNWNlMjlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654913989\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}