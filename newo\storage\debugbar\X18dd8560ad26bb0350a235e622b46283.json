{"__meta": {"id": "X18dd8560ad26bb0350a235e622b46283", "datetime": "2025-06-08 14:57:56", "utime": **********.194795, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394675.555397, "end": **********.19482, "duration": 0.6394228935241699, "duration_str": "639ms", "measures": [{"label": "Booting", "start": 1749394675.555397, "relative_start": 0, "end": **********.095884, "relative_end": **********.095884, "duration": 0.5404870510101318, "duration_str": "540ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.095897, "relative_start": 0.5404999256134033, "end": **********.194824, "relative_end": 4.0531158447265625e-06, "duration": 0.09892702102661133, "duration_str": "98.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45589840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02283, "accumulated_duration_str": "22.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.132112, "duration": 0.0212, "duration_str": "21.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.86}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.169712, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.86, "width_percent": 3.679}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.181316, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.54, "width_percent": 3.46}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2074760174 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2074760174\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1420210840 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420210840\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394658854%7C69%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJCU2RMbklXSklwTGw4TVo5S2R1eHc9PSIsInZhbHVlIjoiY2hSRXZNbTBobnNJbGJqK0RkeWo5OUVMczZFcSszdmhnc1RjTjdnWCtGeGQrd29sUWhzb242ZTNXVHZJUklSWjFPMkxhQzJjOGV6a1MyS3oxZkkvRzVPWkVLNHpxdkNDdTcwUkZYMmNqUXJJMmY4MmdkdjFQczU5dkRoN1NJUTN1SDZVT3pRTmxpeXpWN0ViK0tScmVOTDNIb1ZiMjlnVmtYM2UvVlFuZjhxcFFtanVGOE9kOWgxRFlrbkpHUHRlY3dsTytuUWhiWkpTbi96RW8vdDJtak9ramtaMlRXd1R5R09tcjhKRkVnSFc2NU00enpMVEk0NTN2S3hJVnpuQVZMZkIxL3F1R3RKS2Q2QjlqUEUvVUZNMkJZNkVvdHIwRDBBejZUVnR4TFFDVHlGaEtlWnNnK3p0VHIzNEgxVGEyYmUwS3p1SVpnQlJidThFWFlWQnJwZkJCRUIvWW13MWYzSXlVWmhWcVhkSFNnalQraFkrdFlKTjlGOHVRRlNqZDNGTmFRazE3ajk4dVpXOUlyOHkvTVhqNitRWE5hWXVBaklLWDVJYUtmMjYxY1JiY1N0M1dkYlAzcXlYQlEvcUIwNzJHZHhpaXRoTjRIZkE1UmllSEdsSndhNUVqU0d5V3FrbUlaQ01ITlFUdnV0ZU0vdmpkcklDMnlnU1QzL0EiLCJtYWMiOiJjYjFkM2YxMGNhYzU5MzBkYzVkODE5MjZlMTIxYTViOTc4NzdlMzJmNWU0YjU0MzM0MzE4ZDMwM2I1MGJhYjhkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im13c3FLQTJGZ0ExblQ5eFNZUkg1NXc9PSIsInZhbHVlIjoiUWVFL2V6UElSVzEwTXVYUzRkY3NlbTN2SmRVZXAzNkNNR1VYdDBZZW12bmNaYUc5cTJWK1lWYlEzdGZXVEYyckhQSk5vOEJFNElHMm1sZk92OTVDVzlaMWNTckJjOCtiNzlTdEd5UmNvS1k5bTVrT1BpcVR5R016cUhENVR6SWZ4RzVrd0lpaFZNWStOR0NyYjJnb0Jld3k4ekZJa3VnemRLOU8xN2xEQjBIYUlGdnAvT2RGVHMyZXhmSkt3M2YwV2R4Y2xCZWFwS3RMM3EyZ25ZVFVnVm9QdVdwb0pXZmdNKzk3Q3d2QU1PalFiRit6NVhwdXp4UFdXRmdQc1RVR0hoekpNVVFiNmVic0xoOEJVS0FMQnhQWCtqbUZ3ZGJVMGNia1RJc0VGTjlsV3Z5dmFSOUhWeThEcWhIRUk1RzlLcEs2VWMvaW1QQm4rRkFEZlZudHR0bHdtT0lqdkhsbmhTaXpjblU4dkIrVUwzY3l0bnlNQTVLOERBT0pIWE9OazBYV3R5dnVmN0FUc0h1RTlVNnkwR3FBVGxZOUtFTjAvdytYeGtudkZsdWhsTC9lRDFjUEdYdkd4VFJUYzliNjRKaHlNVXNTV0JIR1ZYaDBqb3dtYjNRYVZxc3E2ZW5xKy92cUlyeURRdW1nb3FZNmJwZExxQXZUU2EwM0VuaEEiLCJtYWMiOiIyYmFjYzA1ZTFlZWRlZDE5NDFiZWVmOTY2MjQwMzBjYjkxOGMzNTAyNjY0MTVmZmNmZGYxMzA2MWM2NzljYzVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-182406107 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNTeDMwdlNwakhNVWUvUkpwYldMaHc9PSIsInZhbHVlIjoiRGd4c3hCUWRQUmNqSXlIdDFTWFl1VkViY0NmK1RiUFd4MWtuVjgwZ0NrMnY3NHliTHFNTkUyN1pXbjBiTXIzZDQ0L3hxTFl6WUtkQ0Q4Z3FCVVRySUJLUnVKKzZtTWErU1o2Y0kwc3E1dUNjQ2hFS2poeTBaeVhCMkNQdGJKVlBncUlIOTl2QjQzbGNzN3IrQ3l1SXlYLzhSVy9UYzAzZkhSU3JMQVdWdUpxVXhXbHlSQysxSmdtbjF0ZWl2dnk0aEJqT2l2RExacFB5OGRVODlqT1hScXU3SFQ1Z3pDR0k5OVZ4bWVQZ2ZIOTRsd2JQV3RkeU5mYXR6K3lxUnhVQ3JqRlRFOC9kRi9mcldWODdiV2p3VFUxSi9nejlkSWFzQTc2ZDlhc1RNUHJpVVVVak82anozVE9ZaEV3QmphWUc2SVlYSEQwWFlMWDg0eXM4dFhmSlg5TStIeGV2SlJiZUt5YzdMOWtQZ2pqRlRsOFZab2JDQlJ4VjhZeFlYMUxqRFpvaDBRSnBxeWVJTFNwYkMzSnNRMUFRWktOaGs0bG0xa1libVN6dnFUQXJRWjFlMk44YkdoQ2NlTG80bzdrYjYxc0VVaHBKY0FpVW1MRmppZEh4a2VtRXFNYkpJQnZYdWhBYzVHcHd1RnJ2cHJxanN6VWpWMDRac2tnQWZ0ZkUiLCJtYWMiOiIzYTc4NDllNjNhMjcwYjRjYWIwZmExNjVhOGY5Y2NlZWZlN2JjNzEyNDFiZGQ2ZmNmNjJiNjVhZGRiZmQ1OTM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitpWUU2QVVuRkp6MkVWelNudkFvekE9PSIsInZhbHVlIjoiblVRSGwxd2FIUjZoc2lFclRMZVR5Ukc4TUF2SU10MG4wVWg3LytXNFpVaGY5dEw0RlhpNmFwSStoTkt4YnJxVmJQRkFpOFdveG9UMEFYUmFKQXNPN01xTm5jZTkrZ3QxSXFkT1h5ZERCdFZmblVmeUJkcGJmc1Bxbllrb0ZOcXRlaXc4TmQxYU9FVkJpcDlVSDcrSE54cVBlaG9Qc0JsWlpKMTBkR0tmL2V4QzQvWVNpa0Z5eUVWcHRhQTRXNFB3SVdSSndvaWFIUmZ2WEp6MkY5cEZwaTZHK1F4VllvRFV5c2s5M2RuWHZYeWtrbDRDSkwyUUVSWmpSUFA3RUd1S0RMdHg5MisyTkg0MlhKZ25HRXg3N0JvWDE0TlJGdWw5VmNhNjd3VUhONFVySndyU2FydVI4eTBOcCs2SHFSeGxjZTg0Y2RyS2hCNzFlS2xHU2ZrL1Rtam4vd0ZocGFISUZ3QUVkMC9ldzhyMzdrTEtjejB4S21odEJhNENsUGxEYUtWVUxhS2dwcUQrc2ZEeDZnMytoN3YxendjdnNjMElkRm9iVWNtVDBwdXZUUUdIbVMzcWdvYlc4WkpSUjVDeGVjVkpaZVpGM0FYVGlUQjdyMk5XYTR1SmplNzEzeFdRWk9DNEllZWhDRDdIcDhQa28wZHB5TWZXdU1yTm94U0oiLCJtYWMiOiI5OTA0N2YxZDQwNzJlMTdjMGZmZjNkMWIyMWYwMDJlMTkyOTM5MWUwODNhYWI5ODE4MDNjNjA3ODkyYjIwY2RjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNTeDMwdlNwakhNVWUvUkpwYldMaHc9PSIsInZhbHVlIjoiRGd4c3hCUWRQUmNqSXlIdDFTWFl1VkViY0NmK1RiUFd4MWtuVjgwZ0NrMnY3NHliTHFNTkUyN1pXbjBiTXIzZDQ0L3hxTFl6WUtkQ0Q4Z3FCVVRySUJLUnVKKzZtTWErU1o2Y0kwc3E1dUNjQ2hFS2poeTBaeVhCMkNQdGJKVlBncUlIOTl2QjQzbGNzN3IrQ3l1SXlYLzhSVy9UYzAzZkhSU3JMQVdWdUpxVXhXbHlSQysxSmdtbjF0ZWl2dnk0aEJqT2l2RExacFB5OGRVODlqT1hScXU3SFQ1Z3pDR0k5OVZ4bWVQZ2ZIOTRsd2JQV3RkeU5mYXR6K3lxUnhVQ3JqRlRFOC9kRi9mcldWODdiV2p3VFUxSi9nejlkSWFzQTc2ZDlhc1RNUHJpVVVVak82anozVE9ZaEV3QmphWUc2SVlYSEQwWFlMWDg0eXM4dFhmSlg5TStIeGV2SlJiZUt5YzdMOWtQZ2pqRlRsOFZab2JDQlJ4VjhZeFlYMUxqRFpvaDBRSnBxeWVJTFNwYkMzSnNRMUFRWktOaGs0bG0xa1libVN6dnFUQXJRWjFlMk44YkdoQ2NlTG80bzdrYjYxc0VVaHBKY0FpVW1MRmppZEh4a2VtRXFNYkpJQnZYdWhBYzVHcHd1RnJ2cHJxanN6VWpWMDRac2tnQWZ0ZkUiLCJtYWMiOiIzYTc4NDllNjNhMjcwYjRjYWIwZmExNjVhOGY5Y2NlZWZlN2JjNzEyNDFiZGQ2ZmNmNjJiNjVhZGRiZmQ1OTM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitpWUU2QVVuRkp6MkVWelNudkFvekE9PSIsInZhbHVlIjoiblVRSGwxd2FIUjZoc2lFclRMZVR5Ukc4TUF2SU10MG4wVWg3LytXNFpVaGY5dEw0RlhpNmFwSStoTkt4YnJxVmJQRkFpOFdveG9UMEFYUmFKQXNPN01xTm5jZTkrZ3QxSXFkT1h5ZERCdFZmblVmeUJkcGJmc1Bxbllrb0ZOcXRlaXc4TmQxYU9FVkJpcDlVSDcrSE54cVBlaG9Qc0JsWlpKMTBkR0tmL2V4QzQvWVNpa0Z5eUVWcHRhQTRXNFB3SVdSSndvaWFIUmZ2WEp6MkY5cEZwaTZHK1F4VllvRFV5c2s5M2RuWHZYeWtrbDRDSkwyUUVSWmpSUFA3RUd1S0RMdHg5MisyTkg0MlhKZ25HRXg3N0JvWDE0TlJGdWw5VmNhNjd3VUhONFVySndyU2FydVI4eTBOcCs2SHFSeGxjZTg0Y2RyS2hCNzFlS2xHU2ZrL1Rtam4vd0ZocGFISUZ3QUVkMC9ldzhyMzdrTEtjejB4S21odEJhNENsUGxEYUtWVUxhS2dwcUQrc2ZEeDZnMytoN3YxendjdnNjMElkRm9iVWNtVDBwdXZUUUdIbVMzcWdvYlc4WkpSUjVDeGVjVkpaZVpGM0FYVGlUQjdyMk5XYTR1SmplNzEzeFdRWk9DNEllZWhDRDdIcDhQa28wZHB5TWZXdU1yTm94U0oiLCJtYWMiOiI5OTA0N2YxZDQwNzJlMTdjMGZmZjNkMWIyMWYwMDJlMTkyOTM5MWUwODNhYWI5ODE4MDNjNjA3ODkyYjIwY2RjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182406107\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}