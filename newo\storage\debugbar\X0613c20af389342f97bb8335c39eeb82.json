{"__meta": {"id": "X0613c20af389342f97bb8335c39eeb82", "datetime": "2025-06-08 13:44:11", "utime": **********.088003, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390249.869874, "end": **********.088031, "duration": 1.2181570529937744, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": 1749390249.869874, "relative_start": 0, "end": 1749390250.936872, "relative_end": 1749390250.936872, "duration": 1.06699800491333, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749390250.936893, "relative_start": 1.067018985748291, "end": **********.088034, "relative_end": 2.86102294921875e-06, "duration": 0.15114092826843262, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45185160, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01432, "accumulated_duration_str": "14.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.022913, "duration": 0.01166, "duration_str": "11.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.425}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.059408, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.425, "width_percent": 8.659}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.068613, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 90.084, "width_percent": 9.916}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-702191017 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-702191017\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-331531467 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331531467\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-798911914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-798911914\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-785221400 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNVUHcrV1ZDWHpzR0dZTkRJYUY5TFE9PSIsInZhbHVlIjoiTDJiVDR3YmxBMTJLeFBaSEZOelhhL0wrZzVSMFRsUlhjeFVTVkRJUzQ5b2IxWkRZZ3hnTG5UUXNvRlJWUDBTTDlqWGRxMGViWkhRbFFreHF5bVJRemFkUEd3cEtCc3VJcmp4Skl0WTZCQzZOS0J0VWxkV3ZUSmhnTElMNVg4eUs4L1hZR2ZObFI1QzhaeXNlWHJZREVEVlNYZmFBN3FLbXo5dGpZR29mdG9qeGMzRkdQeXBYdDZ5UnZjdUM3TytBZjFnazNSSHZUWEY3RUZtVk9LTmtkWmJxUUNMdFZJa2NjbVIrMlBqeGZDMmFRa0ZFb2FBdGhoTG5ISmdXMHN6Sk9Yem9qWGUzVEtuaDlGNE50YkxoWGpDZytOWEpYZ1NQRUlsaVBQM0FhMXU1SjJmUGVNUXVVd0JRZjc1dnArTSsyT0kvbXgxd3JPY1J3Q1IxcEpzSW9NZmVxWTBReHJxeHRQd2wrK2x6OXhEYVZwTjN3N2FZQUpoaHhPQm9NQ2E3S3dIVTI1Q0t2dk1Kd0ZGbnFDd3Jab1VlYU93Z1BLcmNkN0JvM1BsUVorR20vRTNna1pIdkk1Y0pxRUZBZWtRcE5rOVY2eExJTFh3Zmozb0lmVkVmS3J2WU41ZG03OG01YTlISW03cmVaU3I3ODVWK0FSd0RYeC9va0dsZ0cxWkwiLCJtYWMiOiIzODA3N2I5YjhlZDhiZWUwZWRmNTBkZGUwZDY3MTI3YmM4Y2FiMDYzYmFlZTM0OTIzZmY4YzhjOTk5YjhmNmEzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklFQTRRTlplMDdoVzB2a01OTkIzRXc9PSIsInZhbHVlIjoicXk0LzJ6cEJxMnJzRjBiN2FDVkVJRldXSCtuT0xKRm56YkJma1dPU0I4Mk5ibUJESzhiQzI1bkNOSUhYdDkxM0wzWWMvK0p6dHV4UnVxd3M5WUxRRklpVEJHamtpMEgreFVHcVA0T2ptZXJwT3lkeGJBcTQ0TUtPbnk4RVJpckRHMllzdHhnK2phZ0djMGNpTXIwRGRVNUFRTFowUXhtaVBJQkVCaC9QcklHakR3Q2xlc2lVTzRmZmUvVlRiS3gxNEhuM29Rc3NldmJ2ZUtqYWdRYlJ2dXpoOWVnNHA0TE1VeG9WUWxjSGhQZXRPRUJmazlvRmVOejdBOVp2VWNqU1c5L25pYUZTN2VJQXVlRE40a1ZvbEp5SnVFYURqalh0M3JmdVNnY3hxUEpaMm1wWGFLSFg0ZnpxMDNMYUJNMmF4RUhieGpEZENDODNRa2Rhd20xR0laSkJoQ0tNMytMYjM3b3MrbWN1ZlZKQ25NRGtmVGFJTXJoMXVGZzJQK0JFZTk3NEhrY1FHWmxNR1BTNXV2YnNqTGdUa2dNaW4zazlwU0p5aGhvVnJCNmZiTU9QOGFVVXBpeERDclBQa2NyTmdPWFVpYjVRL1FNcUdTbTFDeER0NFdRcW5kR0RnNGJUTzBNeVM5emExRktlS1VCTmNzcmhWZnBDekpPcVQxWTIiLCJtYWMiOiI4NjA1OWY0YWVkZGRlNTBlYTJjOTNjNDYyZjA0ZTdlMTNiOWE2NzBhOGM2YjhkOTIyMTJkMTQ3Mjk5NWJiMDU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-785221400\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1689216406 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689216406\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-682078470 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhCSWQ4amJ3eWprZ05QSWtQTWVjMWc9PSIsInZhbHVlIjoiWDhKaXd5QmdxaXpqVEFCTUdFWjFXb0dGWHRNUmRPbnZkbW9oZ3VVdEhBRmo1TUZDcnhxcFBsUGR3SXZ2RUJOam1wbVhlcUtnRGZLeTA5NEVuZFlmU0hQUm16bzd2M1VLVE5XU1FERUJnVG4wV1B6YkI2cURCQWJzdjY1T1pybElMWVlpcDR4UW5xZmM5TjhzamMvbFd4MUxVd0FHZWNJOGh4RWQ5REVkVUNWUWtVL3dQczdETi9vZ0k4RnRza1Aydlh0WlVFblg3WjBicjZqbnpGWU9vY2ZyeVVObVcxamZObE1hNmNBLzVDL2JnVWxaV29mQnVvSnNsTEhsRHlzK1BkZ3J1K3lHRWN2ZEhoc3ZDZkQ0c0traTNSYXNuTzJSNW5WYk9jV2FYcEF5N2RHQm0wb0R1SXQvWmdvOHltWkNWeloyR25YeldwTjhNMVd4eG1LSkRoanVnV3N6SUlWYjc1Tko5TFpoKzk0a3Rkb0JPWitUY0toalArT09JUCtUWGhiUjI1WTRnSUd3aWJjUGRJaU1HVzhNQklZa2hScEFmcUswYWE2Y29zcW44OWpSRDUycmI4VFFxSG5LYUJUdTBqMUxYQVFLVGlJSmRxM0N2WmlEUXBIRS9QQjE1aEIwM1BDdnVCZGFJc3FxZlQvYnl2UkNkaFU0T0NjUzVtV3kiLCJtYWMiOiIyZGY1YmY1ZWUyOTgxOWJhZmE0ZDdiMGNhYmU2MzM5ODQyN2UwNDRkNmVmZTQzOWZhNzAwM2U2MTEzY2RkYzgxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlNWTZ3QjNKd0pFdWFLRWs0VUxYU2c9PSIsInZhbHVlIjoiSXgxMGdQdjd6L2Z1aXQraFdsOHVJbEVzMko5L3dzSnNUeVpkQUVwZm9vQVVVMTgvSUs2VlpiVnlxdENSSjIyOVVoR1hHYUdCcWdCQUhRMmZFRUxFQWw0d3A0dllHS0QxczdUTm5JdWZVL0tUdU5nUG9JaGduUDRnRktZUWQrYTlLRUVzcEdnWE1pZWRaSVlWeGxoL1hMM3FRYVVFQ1ZIL0FyMFAzbm1LdGJmL01sZkc3YmJ2cjhlVDJWcUR6Smg5Q2xnYzlPMVVjbk5yV0MyZkE1OEVRLzh3YVVOek5KbU4xZXA2S3NDY0gwZVowWFRCd3BXUmdlZ2kxaWE2eGo3RXk2QjVITkloQ2N1bmRCNlQvVm5STWt4MXV1WG12Tm42VHV4VGJsU000OTJMTkpoQU9CT1pyWE1ncjRxSVI4azNtcldaNUplemNqVDFLL1d3NFZvNkZxQnFGR01yWWF5eVFsMUFMRU9jTzNPVnJlKy9zQWI3azFDRVRNOGxEdEo1QlJ5UXJWSnNXTnFsYUluUzZzNUp5L3VUaVdiUmZYK1duRlhMa0YyVEEzWSt4ak1mOGZUdmg2OXU3QXRhY0RXSlVGUFlnMzY5eHA3elk3NjBKM1EwL3BSdVY1bmI0aFIxcUt2QTFlRWZXUXllSWgyWllOMWNxT0pNUHhGQzQ3ckUiLCJtYWMiOiIyMjNmMzcyNTM1MWRlNWI5MTVmNDRjYzAzNDI0Mzc3YzU3ZjhhNTM2NDZiMmUzOGJlZGFiMGFkMjliMjA5YWU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhCSWQ4amJ3eWprZ05QSWtQTWVjMWc9PSIsInZhbHVlIjoiWDhKaXd5QmdxaXpqVEFCTUdFWjFXb0dGWHRNUmRPbnZkbW9oZ3VVdEhBRmo1TUZDcnhxcFBsUGR3SXZ2RUJOam1wbVhlcUtnRGZLeTA5NEVuZFlmU0hQUm16bzd2M1VLVE5XU1FERUJnVG4wV1B6YkI2cURCQWJzdjY1T1pybElMWVlpcDR4UW5xZmM5TjhzamMvbFd4MUxVd0FHZWNJOGh4RWQ5REVkVUNWUWtVL3dQczdETi9vZ0k4RnRza1Aydlh0WlVFblg3WjBicjZqbnpGWU9vY2ZyeVVObVcxamZObE1hNmNBLzVDL2JnVWxaV29mQnVvSnNsTEhsRHlzK1BkZ3J1K3lHRWN2ZEhoc3ZDZkQ0c0traTNSYXNuTzJSNW5WYk9jV2FYcEF5N2RHQm0wb0R1SXQvWmdvOHltWkNWeloyR25YeldwTjhNMVd4eG1LSkRoanVnV3N6SUlWYjc1Tko5TFpoKzk0a3Rkb0JPWitUY0toalArT09JUCtUWGhiUjI1WTRnSUd3aWJjUGRJaU1HVzhNQklZa2hScEFmcUswYWE2Y29zcW44OWpSRDUycmI4VFFxSG5LYUJUdTBqMUxYQVFLVGlJSmRxM0N2WmlEUXBIRS9QQjE1aEIwM1BDdnVCZGFJc3FxZlQvYnl2UkNkaFU0T0NjUzVtV3kiLCJtYWMiOiIyZGY1YmY1ZWUyOTgxOWJhZmE0ZDdiMGNhYmU2MzM5ODQyN2UwNDRkNmVmZTQzOWZhNzAwM2U2MTEzY2RkYzgxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlNWTZ3QjNKd0pFdWFLRWs0VUxYU2c9PSIsInZhbHVlIjoiSXgxMGdQdjd6L2Z1aXQraFdsOHVJbEVzMko5L3dzSnNUeVpkQUVwZm9vQVVVMTgvSUs2VlpiVnlxdENSSjIyOVVoR1hHYUdCcWdCQUhRMmZFRUxFQWw0d3A0dllHS0QxczdUTm5JdWZVL0tUdU5nUG9JaGduUDRnRktZUWQrYTlLRUVzcEdnWE1pZWRaSVlWeGxoL1hMM3FRYVVFQ1ZIL0FyMFAzbm1LdGJmL01sZkc3YmJ2cjhlVDJWcUR6Smg5Q2xnYzlPMVVjbk5yV0MyZkE1OEVRLzh3YVVOek5KbU4xZXA2S3NDY0gwZVowWFRCd3BXUmdlZ2kxaWE2eGo3RXk2QjVITkloQ2N1bmRCNlQvVm5STWt4MXV1WG12Tm42VHV4VGJsU000OTJMTkpoQU9CT1pyWE1ncjRxSVI4azNtcldaNUplemNqVDFLL1d3NFZvNkZxQnFGR01yWWF5eVFsMUFMRU9jTzNPVnJlKy9zQWI3azFDRVRNOGxEdEo1QlJ5UXJWSnNXTnFsYUluUzZzNUp5L3VUaVdiUmZYK1duRlhMa0YyVEEzWSt4ak1mOGZUdmg2OXU3QXRhY0RXSlVGUFlnMzY5eHA3elk3NjBKM1EwL3BSdVY1bmI0aFIxcUt2QTFlRWZXUXllSWgyWllOMWNxT0pNUHhGQzQ3ckUiLCJtYWMiOiIyMjNmMzcyNTM1MWRlNWI5MTVmNDRjYzAzNDI0Mzc3YzU3ZjhhNTM2NDZiMmUzOGJlZGFiMGFkMjliMjA5YWU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682078470\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1963664382 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963664382\", {\"maxDepth\":0})</script>\n"}}