<?php

/**
 * ملف اختبار عملية إنشاء فاتورة التوصيل
 * يتحقق من ربط الجداول الثلاث وتسجيل المبلغ في delivery_cash
 */

echo "=== اختبار عملية إنشاء فاتورة التوصيل ===\n\n";

// 1. فحص الجداول المطلوبة
echo "1. فحص الجداول المطلوبة:\n";

$tables = [
    'pos' => 'جدول الفواتير الرئيسي',
    'pos_payments' => 'جدول مدفوعات الفواتير', 
    'pos_products' => 'جدول منتجات الفواتير',
    'financial_records' => 'جدول السجلات المالية'
];

foreach ($tables as $table => $description) {
    echo "   ✓ $table - $description\n";
}

echo "\n2. فحص الحقول المطلوبة:\n";

// فحص حقول جدول pos
echo "   جدول pos:\n";
$posFields = [
    'delivery_status' => 'حالة التوصيل (normal, delivery_pending, delivery_completed)',
    'cashier_id' => 'معرف الكاشير المسؤول',
    'shift_id' => 'معرف الوردية'
];

foreach ($posFields as $field => $description) {
    echo "     ✓ $field - $description\n";
}

// فحص حقول جدول pos_payments
echo "   جدول pos_payments:\n";
$paymentFields = [
    'payment_type' => 'نوع الدفع (pending للتوصيل)',
    'discount_amount' => 'المبلغ النهائي بعد الخصم',
    'cash_amount' => 'مبلغ النقد',
    'network_amount' => 'مبلغ الشبكة'
];

foreach ($paymentFields as $field => $description) {
    echo "     ✓ $field - $description\n";
}

// فحص حقول جدول financial_records
echo "   جدول financial_records:\n";
$financialFields = [
    'delivery_cash' => 'النقد لدى طلبات التوصيل (العجز)',
    'shift_id' => 'معرف الوردية',
    'current_cash' => 'النقد الحالي',
    'total_cash' => 'إجمالي النقد'
];

foreach ($financialFields as $field => $description) {
    echo "     ✓ $field - $description\n";
}

echo "\n3. فحص دالة storeDeliveryOrder:\n";

$controllerFile = 'app/Http/Controllers/PosController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    // فحص الخطوات المطلوبة
    $steps = [
        'إنشاء فاتورة جديدة' => 'new Pos()',
        'تعيين حالة التوصيل' => 'delivery_pending',
        'حفظ منتجات الفاتورة' => 'new PosProduct()',
        'إنشاء سجل دفع' => 'new PosPayment()',
        'تسجيل العجز' => 'recordDeliveryDeficit'
    ];
    
    foreach ($steps as $step => $code) {
        if (strpos($content, $code) !== false) {
            echo "   ✓ $step - موجود\n";
        } else {
            echo "   ❌ $step - غير موجود\n";
        }
    }
} else {
    echo "   ❌ ملف الكنترولر غير موجود\n";
}

echo "\n4. فحص دالة recordDeliveryDeficit:\n";

if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    $deficitSteps = [
        'البحث عن السجل المالي' => 'FinancialRecord::where',
        'إضافة المبلغ لـ delivery_cash' => 'delivery_cash +=',
        'إنشاء سجل جديد إذا لم يوجد' => 'new FinancialRecord()'
    ];
    
    foreach ($deficitSteps as $step => $code) {
        if (strpos($content, $code) !== false) {
            echo "   ✓ $step - موجود\n";
        } else {
            echo "   ❌ $step - غير موجود\n";
        }
    }
}

echo "\n5. فحص العلاقات في النماذج:\n";

// فحص نموذج Pos
$posModel = 'app/Models/Pos.php';
if (file_exists($posModel)) {
    $content = file_get_contents($posModel);
    
    $relations = [
        'علاقة مع PosPayment' => 'posPayment()',
        'علاقة مع المنتجات' => 'items()',
        'علاقة مع الكاشير' => 'cashier()'
    ];
    
    foreach ($relations as $relation => $method) {
        if (strpos($content, $method) !== false) {
            echo "   ✓ $relation - موجودة\n";
        } else {
            echo "   ❌ $relation - غير موجودة\n";
        }
    }
}

echo "\n6. فحص المسارات (Routes):\n";

$routesFile = 'routes/web.php';
if (file_exists($routesFile)) {
    $content = file_get_contents($routesFile);
    
    $routes = [
        'حفظ طلب التوصيل' => 'pos.store.delivery',
        'معالجة دفع التوصيل' => 'pos.process.delivery.payment'
    ];
    
    foreach ($routes as $route => $name) {
        if (strpos($content, $name) !== false) {
            echo "   ✓ $route - موجود\n";
        } else {
            echo "   ❌ $route - غير موجود\n";
        }
    }
}

echo "\n=== تدفق العملية المتوقع ===\n";

echo "
1. المستخدم يختار عميل لديه صلاحية التوصيل
2. يضغط على زر 'توصيل طلب'
3. يتم استدعاء دالة storeDeliveryOrder
4. إنشاء سجل في جدول pos:
   - pos_id: رقم الفاتورة
   - customer_id: معرف العميل
   - delivery_status: 'delivery_pending'
   - cashier_id: معرف الكاشير
   - shift_id: معرف الوردية
5. إنشاء سجلات في جدول pos_products:
   - pos_id: معرف الفاتورة
   - product_id: معرف المنتج
   - quantity: الكمية
   - price: السعر
   - total: المجموع
6. إنشاء سجل في جدول pos_payments:
   - pos_id: معرف الفاتورة
   - amount: المبلغ قبل الخصم
   - discount: قيمة الخصم
   - discount_amount: المبلغ النهائي
   - payment_type: 'pending'
7. تحديث جدول financial_records:
   - البحث عن السجل المالي للوردية
   - إضافة المبلغ إلى عمود delivery_cash
   - إنشاء سجل جديد إذا لم يوجد
8. إرجاع استجابة JSON مع تفاصيل الفاتورة
";

echo "\n=== نقاط الفحص المهمة ===\n";

echo "
✓ تأكد من وجود وردية مفتوحة قبل إنشاء الفاتورة
✓ التحقق من أن العميل لديه صلاحية التوصيل (is_delivery = 1)
✓ التحقق من وجود منتجات في السلة
✓ إنشاء رقم فاتورة فريد
✓ ربط الفاتورة بالوردية الحالية
✓ تسجيل المبلغ كعجز في delivery_cash
✓ خصم الكميات من المخزون
✓ مسح بيانات الجلسة بعد النجاح
";

echo "\n=== اختبار العملية ===\n";

echo "
لاختبار العملية:
1. افتح شاشة POS
2. اختر مستودع
3. اختر عميل لديه صلاحية التوصيل
4. أضف منتجات للسلة
5. اضغط زر 'توصيل طلب'
6. تحقق من:
   - إنشاء الفاتورة في جدول pos
   - إنشاء سجلات المنتجات في pos_products
   - إنشاء سجل الدفع في pos_payments
   - تحديث delivery_cash في financial_records
   - ظهور الفاتورة في POS Summary بحالة 'جاري توصيل الطلب'
";

echo "\n=== انتهى الاختبار ===\n";

?>
