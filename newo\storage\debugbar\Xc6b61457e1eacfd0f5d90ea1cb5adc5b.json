{"__meta": {"id": "Xc6b61457e1eacfd0f5d90ea1cb5adc5b", "datetime": "2025-06-08 13:27:32", "utime": **********.924522, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389251.541353, "end": **********.924551, "duration": 1.3831980228424072, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749389251.541353, "relative_start": 0, "end": **********.750526, "relative_end": **********.750526, "duration": 1.2091729640960693, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.750549, "relative_start": 1.2091960906982422, "end": **********.924554, "relative_end": 3.0994415283203125e-06, "duration": 0.17400503158569336, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45401560, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00824, "accumulated_duration_str": "8.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.861022, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.816}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.896978, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.816, "width_percent": 13.35}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9049048, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 86.165, "width_percent": 13.835}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1762658876 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1762658876\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1824141655 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1824141655\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1467684925 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467684925\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1840199849 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389243497%7C23%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InY4L0VocGE2WTBWR25SbnJocWZTWGc9PSIsInZhbHVlIjoiRTgySDZuak1TS0VoM2FGN3N5ZlNueklwWXhHdU4vU3RtZVZjKzlKWTA0YzhSeUk4MlJodTVZVVYxcFZYT1BjUzB6SDFhc014MFVxVTJnWm9US3QrdmxnNmE3aDcyUHNlYXBXZ1I3K1VLTlV2U2g0L1pnRmU1Z2swOHNGeXEzWGtJWlNUSDdGQzFCeG5NMUNyb1QrN0pUeXAxVFdHaG9VNFBBZU16aG1nSzZ6WGNnNFFFWW5MRW1hRFUycG93ZkFCT1BGaWxqM0pSS2l2WUxUZ3RXUldKaWRiQTM0c2doQitEelBGRUh4TTN1UW1UbWMzYS9vSDB3OVFVazdlSm5iUXEwYmZSdmI0TFRESnJZa1JRVWNRK1ltN3N4VnNwclpQVjhiT3ZweVdxa0dKKzYybGNsTWZuTnY5WGNxdEptUldSL2JET2lhN0VOdG9WbHhHa1VLTXZtSDJiWkhvdFVpRW5kVUFDRGZ0cXlWNG0rcFdTTDZIc0dyNXpxQWdzWFUxRlhCVUQydGduY2tkelppN2lHZUFZK3Q4WHRlcWZIejdZa3NQSTVkcjk1bThSYUNoUjcreVJrT3RIQ1AyQm5rQVl4bnpDeFZEbzFIUUhlbElwMGY1d1VnV1NyS3JVSHdWVlk4alRROVp3bFNiaUVWN0lOT1lqRDBTZW94VUxLQ3oiLCJtYWMiOiIxNjAxMDIyZTQ0OGViZDVjNjk1NzYyMWU0ZjM4ODJkNGI0ZTQ1MjJmMzE5MDRlODYzNTJjYjQ2NjhiNjdlYjczIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndDaGhiakh1UG1hQXU0eEppQXVxTVE9PSIsInZhbHVlIjoiVHovYkJER3MyUDViUWdESlJCUjlSa3NNV0VKYXhQSVYvQXZaSXFnNVBjckYvL1RnNWF4UGxzNGRWYXk4MllYNjdXNXVtSTJwZXpYbjJvOThRdnJOdWNUWnZSeEZYT1M4cVk0bjZQYmFwcVhseTFEUEFTY04wZThuOEZZRXQzUHRhTnFZZk9CaEVIQldVTUtuQk52dmFRMndMY1owV0xLaUVSRWg3Tm43VS82Y3RiR0d2WGpkM25Jc25taGhDVUxIWlNodlJXSWFTemlWV2dLQk02azdyNGYvS3VpYk5qS1ZWR1RQSmtzd1dmMjRnZ3R0MmpJZnhycHVVbVVuU1c4VHV0VEN1dlhvRHkrY2RsRkNhdFJXa2pWd2ZUR3hJZ2o0eUsxRnNURkxQNzZua1VlaEFtSEJTTVc3bEc0R3J5OTg4Y3YyYWJrVEV3TTh2TXhIdG9Ca1o4VnhnVnZaNExpNzVpWnRRbW0yQ1hmeEJRbFZYQXord204MGlkeTBLcVVsYUNXcTZqMGZtWG04OVBud1o4NmRGWXltRzBFWEQzUk5TbmVwaHFnZFNLc1JOd2pKakR5NkhxUlhvWXlKRmM4SGpNWDFDUnJyR0I3MjJUOGk0WW1waHZKQVpiUHhtSWlKdzkxQ251ZXUySzR0bFFhM3FsUWcyZGNTbHRIc3E3WVoiLCJtYWMiOiIxYjY1ZWExNDI2NzhmYjc2NGRkYjE4NzlhYTVkZjE0NGFmNGY0MjAzZWRkNWU3ZDQ2MTYwNjNlNGRjY2IyMDU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840199849\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1927718320 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927718320\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImljK1pSd1FCaGhPZlJkM2RTYW1BN0E9PSIsInZhbHVlIjoid0pibUsxNG9Ta2IyQnljZEgxN1ZVRkZwa2hkbkR2SGRnV2MwTnZEc3VveWZBcHpSS0hiT3pPMjkzNloraE5SVFRkU2tuWEpLWHlpQ2dBbkZ1cEJSY0RFcEVNazY1TmcyWFBsMWFQWmdNZDZUL29DL0d3SEVDZnl4K1AxdVRNTnJhK3FhQTk0bUxpSDFad2gyTTdKWTk2LzkzVzlCa3F1NHlnenJ6RVNJNVcrMFhPeXlGQ2tNOGM2RGVZWEZoTHB4dURWUllmMVkwbDM1dFRPTmZRcXFHNXNkM2pENjE2WXpkNGVqU25uTk1TUW9LM1FVUmdOR0VTYjdRQXBLc3I2czhJSHZIWUVzdlYzWHZyOFpsM3F6R3hvU3ZPTmYrV0VJSnJmSGNTb2RNVnEzWHVJdUFibUdRK3lJWkQzYW9JbkxyRWJPbzZ2c0hoaS9kMGgybC9CYlJUSDk5M0pVcy9yQ3pObzFXRk1zaFdHRVFETlBpVHpsQnRGZ0tCREQyTVJqL052RC9aY1JPU01EbWZJcm0wTFRENldmZ2VvZ1hnSzBqYnB1OHlEakcrK3EzRENrcmJ0SXdnUUZ6bmVkSEFvZW80MEZyaHBxOGdYcFZlem9IYVdpUmhhNnFkZzB6a3BvQlFIS0hPWlNyWUlPYy80T3F4L3JrWXdzTEtCb1hPLzciLCJtYWMiOiJjYTg2YWU2Mzk4YzIwMzcyNzc0MzJjMWRjOWQxY2QzZDBhYmI3NTkzZjU5MDUyODJhYjdlYTExNDY1YmUyOTZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklMVjg3dHVsMVdFb1lOcGl1dGY4NHc9PSIsInZhbHVlIjoibGlsWFFJTVY1bWgybXY3clgvRWRSajVOcHlzenBEZm0wb0NjV2JWRWxnbEJhTURFMG1QQjZhajZsVWNHWjRndzJRVTdIc0RSMVNOa3dVL1lEdTJVUHBxTG1yaElDYXVvajJqbDc1M2dYQUw2eEcyb1ZYYStCSkc5YjB6NlhBMzNxcWdFU2pLNzVKdS9FMlUwbzJldHYzSERrVEZQZ2dtZ3lnYzZmekZjTHo5M1pjZTNZMWRxYW02OXVXQTltaDNYMUI4QXZOSjZ4aDY2a0lvSGN0RTdpZG0yVzQ1aTMrdHNZS2lQZUEvelUvWmlVUXlRTXVGSHlxTUlkUURacEVlYnNPWE5ZTmdCa0pNY0MveG14YlgxcllOakRCb0Nib1hndGJxZzF5TzVha0RJUS9hakhUTUJBc3hPQ2dSY2pWWEU3bXFIODBTVVFSekVBN0RMTjAwYWpEaVUzQzdYVnMxTmdhRmoxZmhLVnJUNEc5dGdra0lHZHVtNGRycXY2MHFGdFNuZ1ZCYmhCR0ZvdEN3ZkpKSHhWM3JEbFU5RHhmMEk5NERhT0REZmVkcVd1T2JZNTRBL0ZmVzloNWFNUXQwQWdLbjNUTmo2aGl6RUxCQjRGMEwxcHlNY3lObWJ1bFZsdDJMN2dwdXBnZXphOHlYWnJOYkEzWGg2cHhtU2NVWlIiLCJtYWMiOiIwZjc2MDlkZmQ3ZGE3NWJiMmRkY2MyOGY4YWFhNTViZjc5NDZhMTg3ODQ4N2NmM2FiYTZjZGNmZjkwYzk0ZmFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImljK1pSd1FCaGhPZlJkM2RTYW1BN0E9PSIsInZhbHVlIjoid0pibUsxNG9Ta2IyQnljZEgxN1ZVRkZwa2hkbkR2SGRnV2MwTnZEc3VveWZBcHpSS0hiT3pPMjkzNloraE5SVFRkU2tuWEpLWHlpQ2dBbkZ1cEJSY0RFcEVNazY1TmcyWFBsMWFQWmdNZDZUL29DL0d3SEVDZnl4K1AxdVRNTnJhK3FhQTk0bUxpSDFad2gyTTdKWTk2LzkzVzlCa3F1NHlnenJ6RVNJNVcrMFhPeXlGQ2tNOGM2RGVZWEZoTHB4dURWUllmMVkwbDM1dFRPTmZRcXFHNXNkM2pENjE2WXpkNGVqU25uTk1TUW9LM1FVUmdOR0VTYjdRQXBLc3I2czhJSHZIWUVzdlYzWHZyOFpsM3F6R3hvU3ZPTmYrV0VJSnJmSGNTb2RNVnEzWHVJdUFibUdRK3lJWkQzYW9JbkxyRWJPbzZ2c0hoaS9kMGgybC9CYlJUSDk5M0pVcy9yQ3pObzFXRk1zaFdHRVFETlBpVHpsQnRGZ0tCREQyTVJqL052RC9aY1JPU01EbWZJcm0wTFRENldmZ2VvZ1hnSzBqYnB1OHlEakcrK3EzRENrcmJ0SXdnUUZ6bmVkSEFvZW80MEZyaHBxOGdYcFZlem9IYVdpUmhhNnFkZzB6a3BvQlFIS0hPWlNyWUlPYy80T3F4L3JrWXdzTEtCb1hPLzciLCJtYWMiOiJjYTg2YWU2Mzk4YzIwMzcyNzc0MzJjMWRjOWQxY2QzZDBhYmI3NTkzZjU5MDUyODJhYjdlYTExNDY1YmUyOTZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklMVjg3dHVsMVdFb1lOcGl1dGY4NHc9PSIsInZhbHVlIjoibGlsWFFJTVY1bWgybXY3clgvRWRSajVOcHlzenBEZm0wb0NjV2JWRWxnbEJhTURFMG1QQjZhajZsVWNHWjRndzJRVTdIc0RSMVNOa3dVL1lEdTJVUHBxTG1yaElDYXVvajJqbDc1M2dYQUw2eEcyb1ZYYStCSkc5YjB6NlhBMzNxcWdFU2pLNzVKdS9FMlUwbzJldHYzSERrVEZQZ2dtZ3lnYzZmekZjTHo5M1pjZTNZMWRxYW02OXVXQTltaDNYMUI4QXZOSjZ4aDY2a0lvSGN0RTdpZG0yVzQ1aTMrdHNZS2lQZUEvelUvWmlVUXlRTXVGSHlxTUlkUURacEVlYnNPWE5ZTmdCa0pNY0MveG14YlgxcllOakRCb0Nib1hndGJxZzF5TzVha0RJUS9hakhUTUJBc3hPQ2dSY2pWWEU3bXFIODBTVVFSekVBN0RMTjAwYWpEaVUzQzdYVnMxTmdhRmoxZmhLVnJUNEc5dGdra0lHZHVtNGRycXY2MHFGdFNuZ1ZCYmhCR0ZvdEN3ZkpKSHhWM3JEbFU5RHhmMEk5NERhT0REZmVkcVd1T2JZNTRBL0ZmVzloNWFNUXQwQWdLbjNUTmo2aGl6RUxCQjRGMEwxcHlNY3lObWJ1bFZsdDJMN2dwdXBnZXphOHlYWnJOYkEzWGg2cHhtU2NVWlIiLCJtYWMiOiIwZjc2MDlkZmQ3ZGE3NWJiMmRkY2MyOGY4YWFhNTViZjc5NDZhMTg3ODQ4N2NmM2FiYTZjZGNmZjkwYzk0ZmFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}