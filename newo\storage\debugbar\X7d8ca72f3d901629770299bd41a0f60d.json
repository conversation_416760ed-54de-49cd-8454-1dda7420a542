{"__meta": {"id": "X7d8ca72f3d901629770299bd41a0f60d", "datetime": "2025-06-08 13:05:05", "utime": **********.924217, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387904.646972, "end": **********.92425, "duration": 1.277277946472168, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1749387904.646972, "relative_start": 0, "end": **********.673918, "relative_end": **********.673918, "duration": 1.0269460678100586, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.673936, "relative_start": 1.0269639492034912, "end": **********.924253, "relative_end": 3.0994415283203125e-06, "duration": 0.2503170967102051, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48123448, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02408, "accumulated_duration_str": "24.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.765893, "duration": 0.0177, "duration_str": "17.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.505}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.809763, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.505, "width_percent": 5.066}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.871853, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 78.571, "width_percent": 5.606}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.878388, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.178, "width_percent": 5.316}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8929532, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 89.493, "width_percent": 6.312}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.901892, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.806, "width_percent": 4.194}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-991495567 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991495567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.890371, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-944275726 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-944275726\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-612590560 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-612590560\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-737166868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-737166868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-714113096 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNheFNrb0piYUhHMEJDREozdURyaVE9PSIsInZhbHVlIjoiRHppWk5sbXVQMWxjSFVRRFVQNmt6MTZCelgvNXFmYmxpVFVueEVRb003WlFwSVR3a00zekR2aEV1eDdkYXcxbzFrK0xVcVhjTmIwMkY1RFJkVCtJakQ5QWs2OW1GOUxXb1M5TnpPNmNrWkVqVS9keG44bHdDcnpwdUJhcXJGd3p5M1Zack5ZVGZ3WXZnZ0dEdHpKdGg2ZzlYSlVmMk9pTUsvd1Q3cFhsZ1N2ZnpMSHNxYnREVXFsTlRHTjcrRnRaTlVIWGUvT2JYZVM5MWhJeGRYMnRPMkdjbWdSVFA3RGdFTS9EdVBiSnhKTnNieisvZGh4eFpUVzdiQjd0YnpxbWcxdUNQZWhTVkhHMmNXYWtlWHZ5TTV5WVBCRnVwN285R3owYzB2a3o3cXZFRnRYT0xHc0NoS2lZRTJUcUFoOExuMmxUU0NzYWEvL0tlcTFLUi8wd2VNY2NGcFlKQk9jNEt0RGFMZUJJa1d2UjhlMU04NjgwQWNoTXBTRlNzc1dWL3pmS1lMQzVLVkNnVFBoQVh5dmEwZWV2Z3RXc1lwd0l4QkUrVE9ZTEU0MkZoL0h6bXJCNmJEWHZXRFpNYStLd0c5RkRhbG12ZXZWK1F2ZTRZUWMvcVF3M0wybmhxZjJ5dG1abkp1RTZCaHlYMFJFWHprUFYrVEtlTm9RaDdWQXIiLCJtYWMiOiJhM2EyNjEzZTMyZTExZjZhNzA3ZTY5MGFhOGRhZDgwYzNhYmJiMGQwOWZhYjViNWQ3ZGRmNjY1NzAzZjg5YmYxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRuc3BHY0MxNzRpWGtHWUhCODNxbFE9PSIsInZhbHVlIjoiY3k2aUxneTV5VDNsTjhtbS9BS09oZ0hkNVU5NVpPbk9XbXovTzIxcXJoYTRuU2JLVTF1bUJrZVloemZNM05VWFM1ajNRdVR6YlN1SVB1UUVkeE5KNUJOYng2TjZ2MXNNQmZMVjRDbTA4alIwSVRMb2ZjOW5IR0tsOVNOZUY3Y3NrMVdqT2xoNDlSVjZpS0hPNit2ekJkQmxuNkZ5TkkvMGMzMHplbDdXbk1YeW1lR2hwN2RmQWtuSllaVXBnZDFsSWsvQ2J5aGlZQ2hDRk9HeDVUSTB2aVVONW9GVlZyU3doT254ck5PTFoxTzFTK3hsRkRYVTVnOFJYUVNCdVE1VFUwb01VdnFIcjR4NXh6RTBtZS9Ud3BCcXA0ZnRyRHRGODBhSFpINDdrVUZqbHBZcTlzbTNGV3pqNEwvdytkWElFbzdiV0Nzc3dQVXkwSDYzMjFCeWd3bXNuSTZ4REFsY3BIUWpoSE9ZWjlRUDNxUE01SUphazRxM0FuL1l1dzcvdmF6ek9JWHl2Y3lWdXV5U2pTQUh1ZlpNRXp2VS9QeVVpRklFSGpveWg3dDVNaDQwUzY0NDl5aVFBZXZQVE5ha0tmVUN5KzFlRHBnYU1nVjlXOXY1OHdUQjY2L0hBemxZM2U2dXNNSVpVTTB4K3l1RndDS2IveU1CTlZvUU8wbUwiLCJtYWMiOiI1ZWFmMzhlNWI4OGU4NTkxMjhmNWNkNDkxN2ZmYTkwYzdhYjdmYzc3MTc0MmU1NTQ5ZjM2YzYxZmNkMDdjYTA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714113096\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-925290566 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925290566\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-685246449 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1SWFNuUjZFYVdIWXlZRmdDL245RUE9PSIsInZhbHVlIjoicXY1UjJwd0daSDBiYUtzVWYvQm1vN0NaNmx2b2pZbUtiM0ZkdlNjM3I3LzVIVmRaUkpSeFZhVXhJUnBacjNDK2tJTmdLQTRrQUJhTmRyVkkwT1NhS0RpQlBNbTdkZ1ZVMVo0QlpkVzZReGNQQnd4ODVxWDRyQ1VMeGpnSDBZOFFJVmhneml4T3JDaTlqc01scGZucGFOZUlveVBzdTdXS0lZTzRBMGtXcHUrYnFPdDdxQ2RCU010NUhpZHFFVW10aXE1OFNpb0tRdkZkU0F1bUkwUGQwYnpxcm5tMDhQOVAraDFzUmI2WlNkcjVwRCtHZm5GY1lvR3pYMTJRbjNXVVd4Wi8rNHREWU9oejMvaFlNL0g1dEhNOE90Sk9TUWxxNnRQS2w5aVVVV3BINjliU0FQTFZNOXdIVmphbW9PYnE3b1NwVlUybU94THhoNlFFMkpaMGRUUnplQlljeG1LQXhBVTJHektHUHNyaThWOEFCOFZlelpkcktkVjU5c1RZNDAzeGtwWHJiL0E3dGRFZTZ0d2xwcGphTFEyTVNZNnJDdVU2M0NiTDR4WGJxYmJ2V0RhWWhyVkRaNjE2ekNyVlpDZGhrSzBESEVvRVdoZ1FkZjRnVmJVZXJGWXVKeW1oYVQvWTRLZC92ZjJoZllyREdTaFc1M1YwWG1ya21scG8iLCJtYWMiOiJiZTU1ZmNiODIzZGE3Y2RhMWNhNjQ4MTM4Mzg4ODUyZmYwMzU0NGM4M2QyNDFlOGRkMmM2MTUwOGEyMjI4NGVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImdSN0hmNUFLT3hESU5Ld0FzYkZ4cHc9PSIsInZhbHVlIjoiWVhjcnBXZHlwa1VNM2t5OUhCN3BNaUdjMytCYmRpNDUzMXdXRm1XWFNJVnEyOHhrRnJqOFVOWGsyWmVIcFp3NWZOQnIvTnZ5MnZNTE4raFhOemltTkNYbFdDanhOQUphTm9NNTBzMktqM1JtMWVhLzQvSW1PUkxhaGJwcXlDQ2h4VDZ3WVNzdU56U0NXd1hGMWZBVTA3blgraXhHd0VBR1NwdURtZGduMUwxdnBUY2I3UVE1RG85RnhTU1c3VEFWeUxBVitiYlVnWFM5cHlmZGVFeHBNN2dqeDZscXBFcUxaSG5lY3dkWlkyVzJEOEtDNDkvRytveldiRWl2cFVYMHphSVk5RkxlaHBsUVk2dTh4SGQ2c2VnMzdPSnQxcEhsUkhaS20yTU1xZ2JKdWJvOXZmRHhNTnJpOWNyc2UzWVZnUHFISUJzc1JJbFRIQjU2UW1zUk1PSEpwclVMajJDdTlkb3ZzaXdaSC9CQ2ZWOWExTkhVSFJObWhqMC80V3Iyb0dLQWNkSlhJWmRiaUpobFlSL3BXVTd6WU5mREJ1TDJjZE9GcTZLeVQ1RDF2L2NRWlVjUDJMenlPei9XNmhoY20zbFNOcnBNVDU2RnFOcE9iMVloSG96NW5wR3JoQXRJVXZ2dTI5enRQQU5LRDV0ZlR2MWtUSUdka0hacFZVTGwiLCJtYWMiOiI1MjFmYzY4NTc1NWE0ZDYxNjExZWE5NzBkZGE0OGM5ZWU5NGM5ZDc1ZDZkM2UxOGZlNzc1MjQ5NTk0Yjk3MzcyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1SWFNuUjZFYVdIWXlZRmdDL245RUE9PSIsInZhbHVlIjoicXY1UjJwd0daSDBiYUtzVWYvQm1vN0NaNmx2b2pZbUtiM0ZkdlNjM3I3LzVIVmRaUkpSeFZhVXhJUnBacjNDK2tJTmdLQTRrQUJhTmRyVkkwT1NhS0RpQlBNbTdkZ1ZVMVo0QlpkVzZReGNQQnd4ODVxWDRyQ1VMeGpnSDBZOFFJVmhneml4T3JDaTlqc01scGZucGFOZUlveVBzdTdXS0lZTzRBMGtXcHUrYnFPdDdxQ2RCU010NUhpZHFFVW10aXE1OFNpb0tRdkZkU0F1bUkwUGQwYnpxcm5tMDhQOVAraDFzUmI2WlNkcjVwRCtHZm5GY1lvR3pYMTJRbjNXVVd4Wi8rNHREWU9oejMvaFlNL0g1dEhNOE90Sk9TUWxxNnRQS2w5aVVVV3BINjliU0FQTFZNOXdIVmphbW9PYnE3b1NwVlUybU94THhoNlFFMkpaMGRUUnplQlljeG1LQXhBVTJHektHUHNyaThWOEFCOFZlelpkcktkVjU5c1RZNDAzeGtwWHJiL0E3dGRFZTZ0d2xwcGphTFEyTVNZNnJDdVU2M0NiTDR4WGJxYmJ2V0RhWWhyVkRaNjE2ekNyVlpDZGhrSzBESEVvRVdoZ1FkZjRnVmJVZXJGWXVKeW1oYVQvWTRLZC92ZjJoZllyREdTaFc1M1YwWG1ya21scG8iLCJtYWMiOiJiZTU1ZmNiODIzZGE3Y2RhMWNhNjQ4MTM4Mzg4ODUyZmYwMzU0NGM4M2QyNDFlOGRkMmM2MTUwOGEyMjI4NGVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImdSN0hmNUFLT3hESU5Ld0FzYkZ4cHc9PSIsInZhbHVlIjoiWVhjcnBXZHlwa1VNM2t5OUhCN3BNaUdjMytCYmRpNDUzMXdXRm1XWFNJVnEyOHhrRnJqOFVOWGsyWmVIcFp3NWZOQnIvTnZ5MnZNTE4raFhOemltTkNYbFdDanhOQUphTm9NNTBzMktqM1JtMWVhLzQvSW1PUkxhaGJwcXlDQ2h4VDZ3WVNzdU56U0NXd1hGMWZBVTA3blgraXhHd0VBR1NwdURtZGduMUwxdnBUY2I3UVE1RG85RnhTU1c3VEFWeUxBVitiYlVnWFM5cHlmZGVFeHBNN2dqeDZscXBFcUxaSG5lY3dkWlkyVzJEOEtDNDkvRytveldiRWl2cFVYMHphSVk5RkxlaHBsUVk2dTh4SGQ2c2VnMzdPSnQxcEhsUkhaS20yTU1xZ2JKdWJvOXZmRHhNTnJpOWNyc2UzWVZnUHFISUJzc1JJbFRIQjU2UW1zUk1PSEpwclVMajJDdTlkb3ZzaXdaSC9CQ2ZWOWExTkhVSFJObWhqMC80V3Iyb0dLQWNkSlhJWmRiaUpobFlSL3BXVTd6WU5mREJ1TDJjZE9GcTZLeVQ1RDF2L2NRWlVjUDJMenlPei9XNmhoY20zbFNOcnBNVDU2RnFOcE9iMVloSG96NW5wR3JoQXRJVXZ2dTI5enRQQU5LRDV0ZlR2MWtUSUdka0hacFZVTGwiLCJtYWMiOiI1MjFmYzY4NTc1NWE0ZDYxNjExZWE5NzBkZGE0OGM5ZWU5NGM5ZDc1ZDZkM2UxOGZlNzc1MjQ5NTk0Yjk3MzcyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685246449\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-896224428 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896224428\", {\"maxDepth\":0})</script>\n"}}