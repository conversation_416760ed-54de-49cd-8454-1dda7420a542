{"__meta": {"id": "Xbd96ff0c857a0ac723031a45627e9320", "datetime": "2025-06-08 13:48:46", "utime": **********.059221, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390524.661761, "end": **********.059251, "duration": 1.3974900245666504, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749390524.661761, "relative_start": 0, "end": 1749390525.901144, "relative_end": 1749390525.901144, "duration": 1.2393829822540283, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749390525.901166, "relative_start": 1.2394049167633057, "end": **********.059254, "relative_end": 2.86102294921875e-06, "duration": 0.15808796882629395, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43933360, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01903, "accumulated_duration_str": "19.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.010808, "duration": 0.01809, "duration_str": "18.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.06}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.039069, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 95.06, "width_percent": 4.94}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-941796215 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-941796215\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1590252787 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590252787\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-929429992 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-929429992\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1404663441 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtGZTg5aGVLcm82SitLcHVkZURBMGc9PSIsInZhbHVlIjoiZS83cXlCSW45TktSNUNXT2lxT1dHVWp6RTNPU1M1aE9xV09tcHEvTTVoM2Z1ME9NOVJsMnJWbTV3ckhZcjRWczJ6YjdaNlVFSmoxbDZXbTAxdXBHcDM4aUk0V1pWbG9kRGNndEdJYi9oOGVYYWxuU1Z6cmMzanpKOEJ0LzhUeVc0K1BoY1NuK1FWczVyNFVGRnRBQjA5N3ljRU5sWUNEQ05CWE1xd3FEei9aajhQc3RETytMSWFpcWI5ZkJuaWxZQk93em5EUjhCZzduWmk3WXVURHI2Uko5WGdzQWFZZGdzQmVYdUh0UHlKaUdYb1pkYi8vM1VtckQ0dCs4WFFlcE9sdUROd1ZLL2haSkxBOEgwUkh1QXZyRGRld2ZTREFxdXpncDRldHlYVUIwRVRORGoyQm52a2swYWozeHY4RDZnZU1ZZDNUYzBZMGVWWVh4WmtUVmU3YUFhOGFyUk50d2ZEazhDYkN1a2JzNTRhbFNIRUJ6QjNaNmNnR3pNc2xoeTBjaUQ0b1NHcDMydkM5MERZTkZZdUIxWGpma2ZQOEUvMG9MeEYvUDBCaWhjWitMNUUxTzlxdVVwRmhiSFJEOVVkMlNEa0NDUHRHMDRSc09tWWkwUjh5eHplNTVtc2VFSERIcE5SbnJMM3hCMjViTys1WlpaMHpHUEdvZ1JOQUIiLCJtYWMiOiJlZjcwMWE0NjY3NTg5MzJjYzMzMDY0NzQ2NDRmMTBmMjg2ZTNlYjc4N2E2YjM0YWY5MTgxZWZkMTE4YjFlYjQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJTNTQ5N0lvSkdvNGFkM3dJVm9kYWc9PSIsInZhbHVlIjoiclZMOG5ndm12cEJFb0QvTUVJeCtMeFRWWndsZFZMS2RiaW9yM2xwKzhUSjcxUWRnQkpyWml1czcwSjRmR3IwRTNrVENXQlE2N0JHWFBHUlc5YlBkbkpCOVRqVnh2bGNFbXhUYUs5RFJwZVRaR0QxMFhmOGdGSVc0eWl2cWZvTXN2NC9qSGZ2NDQxTHN5VjZvTnBteGxraHpTVWg3VEtjSk9SQll6SjVYNXA4UE8rN3pobHNuMVN6eWI5TXpsaG9wUy90UWhyRTA2M0hjdUNYOVZvNmJiSjlTMm1XQlRsZG1ZdGtyTk1zSCsrTlVmc1NZVHlWSkZYMCtaT2JKeE1mRFZWNmJZMjcweEdRZjVESFFDSFNNcEtZdEtLcnVLWVh3eVhmbTV1Vkl1eEhMMDZQb1JaMWxySVNUQWhVZzdzQmlPaEFEMnJnNVNhbGNFK3l1WDFlb21qNjJJV2l0bm9yeFlCZGZSTjc2NCtITzV1Rit6a1ZFUXoyL0Z4a0dlTVBjT3ZGbXJxUTdnVnRvNUZKa0djdmpUTTdTOXRDZ0FIN0k0UVZEK1pCUXZaZWRRWU5lVjVhVWFEU1JCTlQ4SENFSzJ5dlZ0VDEvbWpaY1NtYjZhaVJydEI1TjRQaysxeFhFNUdIZUtpOVE2MGVmWXcxMVA5RGZaQXRubVFWbWJuZVQiLCJtYWMiOiJhMzBjZmVkYTNkOTk5NzFiNGViNDc3MDYwNGVmMGY2ZTY5MWYzY2RlYzIwZWNmODQ0ZTVkZmQwMmEyMDI2OTQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404663441\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-399587676 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399587676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-524246262 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:48:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkViZ0dqQmtmWDRHd3BxZktVU2M1N0E9PSIsInZhbHVlIjoiNVJJblRmYzdjNkk1RG5teVdwMjZGa1daQ01STVkybVFXa1F0MGU1aDlvdm5WanJRRHg5dWRsVU4vWlpaVGhRM05KT2tLR1pMQ1VSMVB1VXh3bHpYcERyaTZDQyttMVFtNEtYa21lNFlCY1F2L1k1cklmWTFwU2V2ODdZU3lQdk1TNkdDREdSVTRqTlFac2VNWnM5bUlJNUtpMGFuSDBRZVFLdVlucGo3WlhXUUI2MGh4d09wSDVyMHdKUTJkRitDK1R3c1p5TEtENlByQUJoanNKWnJJbjFzekJDZjNITWsxYXFqdUwrV09wYkZtMTBROWU4MWdXcTRQR3Z3K1RxVDk0UVRwcTlqbVVNNGRlbDdUZDNGaVNMcng2WEJjSC8wVGRkaFJEZzBHSlg5ZVNiL25LU1hnQTVBMjZQQ0Zmc2lUeTBPQzB1S1hKNmo2SHJFcWlRSWIwQlBjQXZQN3lJUEFha0dwQ1lOTk55Mjdvd2tJaE9rMzVQNm5vK2VaQk5wQ01mWVJ5QVFybEhpY2hpTG1hYUlUVFhjcDNrYXJod2dlUzZQYUtDMFNWNG1qSzI2L1VpV08zTldCOHVlME9QdHpvZ1dKNWp0b3B6NGsyaldaaWVLZVBXS0J2ZmgxYkNuUnhnMThybk5Kd1hPOHRPaFpkeGRPVmpqYVlhQVN1UnoiLCJtYWMiOiI4NDBkNzljMWFkYTIwM2Q3YTdkYWMxNGM2YmM4ZTYzNDE3MzNkNWIyZTQyZDczNzg4ZDRhY2RiZmIxNjE1NjEyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9OU1pGMnB2OHJqdWJjcVdTUysvMkE9PSIsInZhbHVlIjoibE4yMmFISGZwMldIczV2YU9zWmtjc09LM0x5OFdoOVlIVUNkTkJOUUJaenNPY2FUZ2JkS0IvVmtMY3pjMkV2Snd5eDFCcGw2WW9aNkxuTFdCVFNGY1R1V2VIQ2ZHNTN4TWt6cjlYV3p2MlRnV2I5STFVaCtlVjZrcEpUR1ZYeHBYUi9wdnJIT0hsT1ZYZUdOZTVxdVlRVGtHOUFlVWtOMnpVWmtNeE9KS0RCUmhrYWVnLzhqbGhQTHRtZDErTFhqRlJBRWFHLzNyVVF5NXhFL0R1WEwvOFdycW9sQlRDMWc4bXZVaFkvL21LeVNHWU9MajRhK1RURlgzdVluRlVmRGFUTmVQMWFmU2h1enNVVUNLWk5XZlF2cWtnR2hSNFJ4WkNjdS80WHlYekVZNVRzUnhVOHhwRXlwV2hreUFRbWpUdUZlR2lXY2wwYkRPTkVTQ0FzQlV5UFhXdm5LVEp1RkNmTTd0N2xoVlJub1gvdld3amF5OCt3Z2pId3NkT1NWaTdRdmY3Sk9OUXdMRTJuTlBmYXcrSkZiRWc5VTQwLzd5dUVENDNud1VRMVBIVmhUbnBobnNxRVlaNlpmbkRNTStoM0p0YzBxQVFFZTFjek1pZzh5dDRRblpRZGo1TUowaFJ6QzFOa2dKT1lFZURJeXRxL3pOREtEVVFONFhmV1YiLCJtYWMiOiIwOTlhNGUwYTM3ZTg0NDMzNjFiODdiZDAzN2I0ZDczZGJjYjgwZDhkMWMyMmUyNThlODE5YTMzMGYyYTgzOWZmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkViZ0dqQmtmWDRHd3BxZktVU2M1N0E9PSIsInZhbHVlIjoiNVJJblRmYzdjNkk1RG5teVdwMjZGa1daQ01STVkybVFXa1F0MGU1aDlvdm5WanJRRHg5dWRsVU4vWlpaVGhRM05KT2tLR1pMQ1VSMVB1VXh3bHpYcERyaTZDQyttMVFtNEtYa21lNFlCY1F2L1k1cklmWTFwU2V2ODdZU3lQdk1TNkdDREdSVTRqTlFac2VNWnM5bUlJNUtpMGFuSDBRZVFLdVlucGo3WlhXUUI2MGh4d09wSDVyMHdKUTJkRitDK1R3c1p5TEtENlByQUJoanNKWnJJbjFzekJDZjNITWsxYXFqdUwrV09wYkZtMTBROWU4MWdXcTRQR3Z3K1RxVDk0UVRwcTlqbVVNNGRlbDdUZDNGaVNMcng2WEJjSC8wVGRkaFJEZzBHSlg5ZVNiL25LU1hnQTVBMjZQQ0Zmc2lUeTBPQzB1S1hKNmo2SHJFcWlRSWIwQlBjQXZQN3lJUEFha0dwQ1lOTk55Mjdvd2tJaE9rMzVQNm5vK2VaQk5wQ01mWVJ5QVFybEhpY2hpTG1hYUlUVFhjcDNrYXJod2dlUzZQYUtDMFNWNG1qSzI2L1VpV08zTldCOHVlME9QdHpvZ1dKNWp0b3B6NGsyaldaaWVLZVBXS0J2ZmgxYkNuUnhnMThybk5Kd1hPOHRPaFpkeGRPVmpqYVlhQVN1UnoiLCJtYWMiOiI4NDBkNzljMWFkYTIwM2Q3YTdkYWMxNGM2YmM4ZTYzNDE3MzNkNWIyZTQyZDczNzg4ZDRhY2RiZmIxNjE1NjEyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9OU1pGMnB2OHJqdWJjcVdTUysvMkE9PSIsInZhbHVlIjoibE4yMmFISGZwMldIczV2YU9zWmtjc09LM0x5OFdoOVlIVUNkTkJOUUJaenNPY2FUZ2JkS0IvVmtMY3pjMkV2Snd5eDFCcGw2WW9aNkxuTFdCVFNGY1R1V2VIQ2ZHNTN4TWt6cjlYV3p2MlRnV2I5STFVaCtlVjZrcEpUR1ZYeHBYUi9wdnJIT0hsT1ZYZUdOZTVxdVlRVGtHOUFlVWtOMnpVWmtNeE9KS0RCUmhrYWVnLzhqbGhQTHRtZDErTFhqRlJBRWFHLzNyVVF5NXhFL0R1WEwvOFdycW9sQlRDMWc4bXZVaFkvL21LeVNHWU9MajRhK1RURlgzdVluRlVmRGFUTmVQMWFmU2h1enNVVUNLWk5XZlF2cWtnR2hSNFJ4WkNjdS80WHlYekVZNVRzUnhVOHhwRXlwV2hreUFRbWpUdUZlR2lXY2wwYkRPTkVTQ0FzQlV5UFhXdm5LVEp1RkNmTTd0N2xoVlJub1gvdld3amF5OCt3Z2pId3NkT1NWaTdRdmY3Sk9OUXdMRTJuTlBmYXcrSkZiRWc5VTQwLzd5dUVENDNud1VRMVBIVmhUbnBobnNxRVlaNlpmbkRNTStoM0p0YzBxQVFFZTFjek1pZzh5dDRRblpRZGo1TUowaFJ6QzFOa2dKT1lFZURJeXRxL3pOREtEVVFONFhmV1YiLCJtYWMiOiIwOTlhNGUwYTM3ZTg0NDMzNjFiODdiZDAzN2I0ZDczZGJjYjgwZDhkMWMyMmUyNThlODE5YTMzMGYyYTgzOWZmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524246262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1971358431 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971358431\", {\"maxDepth\":0})</script>\n"}}