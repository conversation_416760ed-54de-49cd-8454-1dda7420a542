{"__meta": {"id": "Xca541523d3968efd95c14600267729ad", "datetime": "2025-06-08 13:00:08", "utime": **********.473322, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387607.123556, "end": **********.473357, "duration": 1.3498010635375977, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1749387607.123556, "relative_start": 0, "end": **********.302736, "relative_end": **********.302736, "duration": 1.1791801452636719, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302755, "relative_start": 1.17919921875, "end": **********.473361, "relative_end": 4.0531158447265625e-06, "duration": 0.17060589790344238, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45577432, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009999999999999998, "accumulated_duration_str": "10ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.393937, "duration": 0.007549999999999999, "duration_str": "7.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.5}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.427286, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.5, "width_percent": 14.1}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.447749, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.6, "width_percent": 10.4}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1428108147 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1428108147\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-586359627 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-586359627\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-700306250 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700306250\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-208002264 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387594015%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlMzNjM1bmNrWnFQYTdMd0F3YTIyRVE9PSIsInZhbHVlIjoiWDE5cktLM2l1RDZlalg1L3M3eGRPMDVCV3BaN3RZMkZnUm5LTzNybDZRWW96VnZUYmc3clFQVFI5ZlBoSkYrQkE5OWtzeGVZTlN6WXh1aXRFN1lsZElWRUZ2amx4UllYbFdlaFpybHVyOGRNYnFhUWkxTXFjdG1oc3EzVSt5eDFDVzhqM2dCNkZlbzB1RWVwalRKZVVSSEZLQ2RMTy83MTlId2d2dDRYM1ZtcjIxRUlBZmpQK0t5LzdibkExeFAwbE1YV0xEM2Q0L2wzVnE5cEZZQld4T09kNW5DVzFzdjVtMG1KUDRVTXZ3NExjNjZmOEtaajZKRy9iU29xOGp4Q0VwUWhGc29ZNVBqd3h0Sjl1aVgzOEplclJoYjBzdWd0VDVkTUJZR0U0dmxxL2xLcGJBc0F3V3daeTJLSlcyOGhZNDJ5TVVMSU5VMjRFck5wYlpDU0ZaKy85cUxjb1RRTER5Zjdadmp5VExDd2RVMnZLa3Z2RzJwSVNLTGlJYUZzb1dnaS9rM0NVV0Ixdm41d1NpcGxQNkdxbFBITnpySHo3LzBDYkRuM0JmNkY1ZmlDdFkzZ24vUmhyNnR2OC8rZ1AxcUZiTktFbXVOa1EvdHhoUDFNNWFCNkk0MERNYXU3T2JTSFp4TVRLUEk4dmY3VE5ablhYNFZudzQ2OWZTY0giLCJtYWMiOiJhMmQ3MzhiMjgyMWI1OWJjZjY2NTdmMDEwNjEyZTYwMmM0YzE3MTEyMTMyODQ1NDdiYWY0ODYxNTIyMjMyN2I2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZ2TWlXc2RibDV4SDNUUlVrQzAzZGc9PSIsInZhbHVlIjoiR1dEMlFxR09xSkdyY1FPcFdPOE9LcTdrbzNXOEtwTE5CWm5IRGxKVVZPcmRqMC9uSFdqVUIvYWQ3M3VVdWh1aWFQbU1KbmdRYloraWtocjJDMHZTbzVtbmxmRG1RaUtZVzBFa3dRRnpneTNmUzBtQWtzakdNL0xOQTRFbDhnT2I5L0VhRnE3OTdYeW81YThYcnJ4VXd0aE5reWJPc1ovSUwrQnQ0Wm94UERHTkNGWTlnUi84Q1V0dU5LOWtCNmIyZSsySXIyVXhqS0xJbFlOUzJNYkU3NHE3L2FtOVd3RkM0MnJUVWJpS3FUSnpjbnhxU09OQitlUVdMV2w1NERzeHFKOStQNStaNTN2bXNNdytjRHdHZzhtN2d5S01ra3VFUDdlZTJSY0JZQ3Z1QWcxaUhzaVNHNTM0ZkY1RHQzQUh4RU9zYWlDczZoV1BCSGJja280UVhxV2d5RmtmWEJ6UktpRlA1VGZQU3ZjZzBiYVB5d3RTM2h6MGlKWjN0NEdLeCtsSExSdU02bXVReGZYeTNENlQwb01UMmhGUURROTRMcjJtVU5taE9NMVJ4ejVMQ0hLdTBGRkRKTWdmR2gyNjV5NFZST3IrOCtBek1lTGNSeWVWUGJHZHBPdG5KUXYrOE9WY0dTbnZJbkg2bjMwSWw3QjFBa3hHdEgyWWtpRUkiLCJtYWMiOiJiYzU1MmI5ZmEwMzMzNGM5ZThiYzAwOGVmMjgwZjUyODhkYzViMTkxNzRjMmJiZTQ0ZDk1ZGY1NTE3Yzk5YjI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208002264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-92648712 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92648712\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-883754258 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjV6N3FZajVMamVnQkt2RnpvSEEyd0E9PSIsInZhbHVlIjoiRTIvOE5rOEtZL1NKa2Q2cy9WS0lRTnVsSDNrT0JsdldhVFNvQmViMktHL0gwQzgzbDh1NU04dytmZ0I0NStyWG9RazRGMWRjbDd4N0tiWThaam94bjNwKzd1d28wa0YvMVJmcnIvUWtzVEd2TVp0dmdRb2FVdFpCSmlSU2h4dFhlaTR5akhJcnV6MG5oQ214cDNXMkJnenZWdEcxYzdhNXRoVExnOGtGclMvZ0RRRWZCQUlLVHhXMStCSUd4ZFliYitKUXVGQ0lYSjVYdVE3T1Y3c1hVSmlxM25Bb2FXZDFtZ2NZd1ovaTRad0VwZlhOSHNONVBmcnZpNktpaGp4WVcvSTRBeG9UeDZoSVJLWXdNb2FUV0NFRGtNclQ4T0puSmkxZ0ZCSEIwSmQ2WkZmeHBGTDFwclZ5MHRuakpleloyV1pUcDZNL3NlWkJGa1c0d2xFYjlsQ2xBZU5DMElOS3ZKeU15aHVHODZNSDFFS2ZSTVJCdTYrUVdRSlg4UytNYSsvZlFkZUJOZ3dCdmNmajlEOTg5MDdwbkQwbWV0aXExVmttaXFDcVBJc1lzNnlNWEVNalZPa2hwSWJOVmYzaU8wR1ZVTWQveFRuRGprYThnM2EvN1Y3MzYrbGVpU21TVnhISFNDbGdpMHhRb1hGWlF1ancwaUpqVXBBR084K1YiLCJtYWMiOiIwYzAzNjg2YTIyMTI4ZDQ2YjViYzBkZmQ4ZDg0NGUwNDdmYjJjZTY4MmU1NjA5MTk4MTg2NDczZjRiMzlmNDViIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InQrRElmc0lsUG9od0UrTFUwQmorNmc9PSIsInZhbHVlIjoiblRNamNQOUN6aHVoYmh1TVF4Z0tyMU9YMmh2bVdWQkJyVExEWG9YL2h4eXNzN1J1L0J5RCt3Rzd4MUttNjZMN1pNc3RkQ0l6bCtQVGR4QzV1UktPdUtmSjdPSU41aHBEbEoyTld4bS9EQXpUZ1dQUlZXVkF4R29EREkyRms2ZDlaNGNQN1VGenZUcng2RVNtNFFja2s2OTdBUjg3NXdCaDZKcTBHUkd1R3Fva3VBTWlwU3lVMDg4OURmZEYrVXhpZldjYThvdlpHcnlpZm43MjhzSE1sUjM2RXUyQVZqeWlYdXRKUGJIcFRoUjM4Q09qT282VUpXUXlwOENuZnpzdWZkaW5HTzN0d0dMOUtEQ0pGc3JCKzlUQk9nQndyYjhsMEJNVFRabkJYU1VjWmFIUXhHS3cwVlYrekh2OExjZktxN0xZK3R2dUgyc05ldjh4M1d0TjBGbXFCeWs4OUVTeXdZWmlUVjNGSFQ0UlRQMk51bHB4TS80bTl3a1FFZm9MaUV2YUxpaDhDYUxwSFN6cW1HQUZxbUcwcndsTXRodER2ZUFXcGM0KzdUR1IrZmNwclpCYlpIeTJRNjhNcCtiSEE5ZHJTR2hFUVc3RUlrV1lwZG1IVHpvT3hmSjNJT3VQSGhrSkRWcnQxYlpUK3lEZEhNYWRSdW82eXFZQTA3QTciLCJtYWMiOiI2NjIyZmI0OTc5MWVhYzJlMjAyMjM3MGIxNDY3NDlmYzY5NDU1Njk0NGZkZjJiZDUwNjM3Y2E2N2YyNTQwODgzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjV6N3FZajVMamVnQkt2RnpvSEEyd0E9PSIsInZhbHVlIjoiRTIvOE5rOEtZL1NKa2Q2cy9WS0lRTnVsSDNrT0JsdldhVFNvQmViMktHL0gwQzgzbDh1NU04dytmZ0I0NStyWG9RazRGMWRjbDd4N0tiWThaam94bjNwKzd1d28wa0YvMVJmcnIvUWtzVEd2TVp0dmdRb2FVdFpCSmlSU2h4dFhlaTR5akhJcnV6MG5oQ214cDNXMkJnenZWdEcxYzdhNXRoVExnOGtGclMvZ0RRRWZCQUlLVHhXMStCSUd4ZFliYitKUXVGQ0lYSjVYdVE3T1Y3c1hVSmlxM25Bb2FXZDFtZ2NZd1ovaTRad0VwZlhOSHNONVBmcnZpNktpaGp4WVcvSTRBeG9UeDZoSVJLWXdNb2FUV0NFRGtNclQ4T0puSmkxZ0ZCSEIwSmQ2WkZmeHBGTDFwclZ5MHRuakpleloyV1pUcDZNL3NlWkJGa1c0d2xFYjlsQ2xBZU5DMElOS3ZKeU15aHVHODZNSDFFS2ZSTVJCdTYrUVdRSlg4UytNYSsvZlFkZUJOZ3dCdmNmajlEOTg5MDdwbkQwbWV0aXExVmttaXFDcVBJc1lzNnlNWEVNalZPa2hwSWJOVmYzaU8wR1ZVTWQveFRuRGprYThnM2EvN1Y3MzYrbGVpU21TVnhISFNDbGdpMHhRb1hGWlF1ancwaUpqVXBBR084K1YiLCJtYWMiOiIwYzAzNjg2YTIyMTI4ZDQ2YjViYzBkZmQ4ZDg0NGUwNDdmYjJjZTY4MmU1NjA5MTk4MTg2NDczZjRiMzlmNDViIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InQrRElmc0lsUG9od0UrTFUwQmorNmc9PSIsInZhbHVlIjoiblRNamNQOUN6aHVoYmh1TVF4Z0tyMU9YMmh2bVdWQkJyVExEWG9YL2h4eXNzN1J1L0J5RCt3Rzd4MUttNjZMN1pNc3RkQ0l6bCtQVGR4QzV1UktPdUtmSjdPSU41aHBEbEoyTld4bS9EQXpUZ1dQUlZXVkF4R29EREkyRms2ZDlaNGNQN1VGenZUcng2RVNtNFFja2s2OTdBUjg3NXdCaDZKcTBHUkd1R3Fva3VBTWlwU3lVMDg4OURmZEYrVXhpZldjYThvdlpHcnlpZm43MjhzSE1sUjM2RXUyQVZqeWlYdXRKUGJIcFRoUjM4Q09qT282VUpXUXlwOENuZnpzdWZkaW5HTzN0d0dMOUtEQ0pGc3JCKzlUQk9nQndyYjhsMEJNVFRabkJYU1VjWmFIUXhHS3cwVlYrekh2OExjZktxN0xZK3R2dUgyc05ldjh4M1d0TjBGbXFCeWs4OUVTeXdZWmlUVjNGSFQ0UlRQMk51bHB4TS80bTl3a1FFZm9MaUV2YUxpaDhDYUxwSFN6cW1HQUZxbUcwcndsTXRodER2ZUFXcGM0KzdUR1IrZmNwclpCYlpIeTJRNjhNcCtiSEE5ZHJTR2hFUVc3RUlrV1lwZG1IVHpvT3hmSjNJT3VQSGhrSkRWcnQxYlpUK3lEZEhNYWRSdW82eXFZQTA3QTciLCJtYWMiOiI2NjIyZmI0OTc5MWVhYzJlMjAyMjM3MGIxNDY3NDlmYzY5NDU1Njk0NGZkZjJiZDUwNjM3Y2E2N2YyNTQwODgzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883754258\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1768109763 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768109763\", {\"maxDepth\":0})</script>\n"}}