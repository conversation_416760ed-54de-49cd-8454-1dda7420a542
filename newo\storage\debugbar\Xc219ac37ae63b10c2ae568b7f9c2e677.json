{"__meta": {"id": "Xc219ac37ae63b10c2ae568b7f9c2e677", "datetime": "2025-06-08 13:27:39", "utime": **********.174742, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389257.832991, "end": **********.174771, "duration": 1.341780185699463, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749389257.832991, "relative_start": 0, "end": **********.006528, "relative_end": **********.006528, "duration": 1.173537015914917, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.006552, "relative_start": 1.1735610961914062, "end": **********.174774, "relative_end": 2.86102294921875e-06, "duration": 0.16822195053100586, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45293000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02886, "accumulated_duration_str": "28.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.098567, "duration": 0.02768, "duration_str": "27.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.911}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.153479, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.911, "width_percent": 4.089}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-290298774 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-290298774\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1252952674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1252952674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-602686646 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602686646\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-703370488 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InM2eERaTHFsNC9xZkRrMDdwTDZTenc9PSIsInZhbHVlIjoiUzlKcmhKMnAwcmlIYnJKYVVRL2dBZ3VnNjJEVUdyQXRWUHBLSzJ3UWhscnhqZVBOUTJBN1hDcE11MlIxNkNZT2JLNnpOWmsvVWw5U25RdkZaaCtBYlMzYzZYVm5MRzczRHpHeDZVdWlVc3Rsck54SW1UVzJxQmM5WG1RK1RsanFrY3Q0b1pCQ01uMFc2aWk1Nk93eDFXU2wxcFhNYW44c1FBZkVJd0hFLy9wUGtxZEVpMkJONThKUXBuWCtLanYydHFOUjNDR0FWTThJZTNHYWZ2TjVkTHk0ajdKSEdrWG9DTE1qUFB2MjNXbGxYUjNaTVhTVTFjeFppaDlOSzlIUEZvSnRPR1NrdWMxTDhaZlNWbzFyUW1iUXhNMHJBZ1BiWENqMTZDbHZ4M0dIT2p5RjN3YnRCQTYzOVdrT3RDb2VMcFcrRDl5UDVwbjhna3RHUHZEQTdEYlQyUTlVZFliazBvQU4vUEQ2ZXpHWEx2a0xOay9uYUtwMHJScVJhM3lTeVgzK24xcVBpOVR4Y29zSlNUb0YrNDZqK0NKaDF2TkU1aVU5SW5mOFo5NEdobzhYSjJhTXpKOEpaZDBmb1UxN0grdHlRcUhncmZxZjFMTzExTlgxd1E0Q01HMEppbTFhamR0N1RYN2xXMVRVUHpqN2FlcnBGdThaRThLTnpxSHgiLCJtYWMiOiI2Y2Y3MTc3NDU2Y2FlOGYwMGYyMWM2YmNmMjEwOGRkMTRjODE3OGIwODdkNzdhMDRiMmUxZDlkOWRiNThjNDlhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRvUkNycng2M01YaXZ4OXk1VURKcFE9PSIsInZhbHVlIjoiYUN1S01VRkRMbEhRcXRYY3I1NURDSGFKczBVMnFkWHNBUmRJMk5kUjYvdGJ2VStXNFByeXRsK3NoTzVMd1hXQVFYODl5WVlValF4YlQySmtWWTNBVnlUdWlFVjhBU2ZaRlBBaXkxVmpudzZDOURlSG5NdWlKc2NVTVRrRDBsK1F3dHBPZzB6YS9iMDFNV2ZIMldUN0gxcnBlaWFjZnRDRkd6WHI5NjJZZDJkQTRKOU1zOTJBeEtZVER1UTB2cHlxNnZ4RWUzYkFDWkpQZy91cTdKdGtZQy9TSk9VZVlKS0gxYUlLWHFCSFEweVJaQXBRcnNtcHpkaTRyYjBwSS9zaFpwN0lMNEVxMUZkNG9JZU8yMUdYNFlaeEcwKzl0OE1GbHlGODQxeTFvK0tXcVk0ak91cHhyZFVIUFhERm5tZ3R1VTdzbkkvN1JhWVVMYWtubUJSeloxSEFhWFlURGhmT0VEUld3QjVlS1ZaSmE2NHlzeHZabjdvcFlud0VnSGpJeWthdGZ2eVM0UkpuMFFjOXVQTHoxajh6MXVqakduZnVFcE5JZEhNa2xwNFF1d2crOHZTbGMwZGZ3TnM1VitPTGFQRDFzRDh2MThKQkdLSlNld1VEYmlvRW9uSjF6c0p3ZjZFaWJ1VVVwbXJTVm5sbEhPMHBKM1p5S1htUWh4SDIiLCJtYWMiOiI5OWEyMmMyNTczZjg5YzgzMTA3MjU2ZTEzYmQyMzIxZDg0NDZmNWIzOGYzZTdhNmRiYTAzNmQ3ZTM5ZGVmNWJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703370488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1505085926 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505085926\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-461874731 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZJbW15ZFJadk1hVTNCWG53cDVnMlE9PSIsInZhbHVlIjoiaW4wTG5TNXlJcVpxWUlWU2NGZFVyZkNGaVBtdGE3eXhJcTBMK1lRUnp1MU02cEdpdGMxNndqdkUwcy9HL3pucGdZMkppNEhtSUprdVlZdjVWRHFxOE02YVZtck9qamZOZGltQ0dPMHlqYVVpZlJORW9ZRUNoVk10NU1Ea1ZBb2xRVGJsV3JmK1BVQW5ZY1FYcmFtWm1lWlp3RUt3eGVDbHM1VFFzRjRobEEwWDNsM0VEWEdta1FjWDR3SEs2a2hkbVNyNGo4bmFvaUNnWUZvdFJPUjcxZ0dVTE9Wd0lybG0vTEd1Wmx0cUxTdFd2Y3VGaTlFL0syUFEyZ0tIa3o4Vk95a0xOZ01hdk1iekFNUVk5dkQ3Qy92SUVuL3VyWDUzeDh0R1VIYUg3QmFIaHlLSW9JWUYwbzRGQm5hVjFMUmJPTjRvU1pjbEs5TXd1bmdoRVI0WmN1YzllR1k4WWZUOTlVV3RQUFR4a3RsOVBEWWxJbEh4M0Y4RVlwb3NjMFNKek8vWUJPT1NuMnk5Vkp2S3JBZWFXL2ViMStEK1E0b0l5cjUyQ251cVNjTEZQdmdqOVlaa3pjMkN3NUZrWFRDNXRuWlJIQys5RTZ0MXNUQkk2RVl1SVcwNmQ5WG5iR0t2WVlRQ3FsQ2QwdGlKMHRxcFhELzB2dlQzNmo0blYxbXUiLCJtYWMiOiJiMWZmNmEwMDcwY2I3ZTU1NGE1OWZhNTg1NDRiOGRmMWNlMzUzNjU2OTA4MTkzMmVlNjI4ZjM0M2YyNDcyNTJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjQ1b1F3Qkh1YWxVdnpLMlJWdk4xdHc9PSIsInZhbHVlIjoiWjNpK2plM0Y5NzlHRS8vQXIwMTIxdnV6UTd1V0toOEVKbU4rMFEyYUpWSm16NmxodnRSbTNYMFB1SDZvNWNScHgrbUwzbGtwajFRd1BrQ2pHTmdqYVFxKzY4N1oxdHJPcVlPdzNnV2FxN2F1SEM5clIxV2VkTUd6cVl4SCtEcFBtamJITitLeVFYbWRlOVZsbU9Ma2lyL2lYaDd1TzhxK3Z0WllmSlNlZitNOWpTTFRpYWxuWmd5SjIzekVsbmE2cXF1VUsxMWZHTTN1bld0cFpFUUVQWnV1WU9uOHhuWVZ2NkU3VWR5VVRwTzl0bUxSdExhRlRoc1VhcktXY3VOa1BUc1N5NTE0VjM5bGp0MEtoM084emd4NlpuZ3VPSlVaL0trV1N3VUYyU1NQTDN3dG1BOEREV2VvUUgwd3BvSTI2c3I1QUpEZk9VdzBPbktyc1IvNmRkN1ZqWDdQSlBwSHhyTXlaQStOdDZBQmlsZitvalB6aG1YUFpleXYzSFNnUFdTZ0pwbktwU3ljNm9wa2dwNm81aURweWFzUFJEZ2xqZytXc0pTL0VmbmpJazNBNFMvYUw5ZFZ6RFZlNzZGVU5nNGhZeVo1Qm1WbDhjYldURE1RRGV2ejBaTUhXYVlZeDNoR0RGZlJVRFByY0xYdnpmL2NRUEM0b09yUkNIK2kiLCJtYWMiOiJlOTQzNTk2Y2RjNzMwMTFmNDQzMjZhMDA1OTJlYmEwOGQwMDk3ZWE3YzAwMmI1ZWY0MzM0NzkyMTBkN2NiMDNlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZJbW15ZFJadk1hVTNCWG53cDVnMlE9PSIsInZhbHVlIjoiaW4wTG5TNXlJcVpxWUlWU2NGZFVyZkNGaVBtdGE3eXhJcTBMK1lRUnp1MU02cEdpdGMxNndqdkUwcy9HL3pucGdZMkppNEhtSUprdVlZdjVWRHFxOE02YVZtck9qamZOZGltQ0dPMHlqYVVpZlJORW9ZRUNoVk10NU1Ea1ZBb2xRVGJsV3JmK1BVQW5ZY1FYcmFtWm1lWlp3RUt3eGVDbHM1VFFzRjRobEEwWDNsM0VEWEdta1FjWDR3SEs2a2hkbVNyNGo4bmFvaUNnWUZvdFJPUjcxZ0dVTE9Wd0lybG0vTEd1Wmx0cUxTdFd2Y3VGaTlFL0syUFEyZ0tIa3o4Vk95a0xOZ01hdk1iekFNUVk5dkQ3Qy92SUVuL3VyWDUzeDh0R1VIYUg3QmFIaHlLSW9JWUYwbzRGQm5hVjFMUmJPTjRvU1pjbEs5TXd1bmdoRVI0WmN1YzllR1k4WWZUOTlVV3RQUFR4a3RsOVBEWWxJbEh4M0Y4RVlwb3NjMFNKek8vWUJPT1NuMnk5Vkp2S3JBZWFXL2ViMStEK1E0b0l5cjUyQ251cVNjTEZQdmdqOVlaa3pjMkN3NUZrWFRDNXRuWlJIQys5RTZ0MXNUQkk2RVl1SVcwNmQ5WG5iR0t2WVlRQ3FsQ2QwdGlKMHRxcFhELzB2dlQzNmo0blYxbXUiLCJtYWMiOiJiMWZmNmEwMDcwY2I3ZTU1NGE1OWZhNTg1NDRiOGRmMWNlMzUzNjU2OTA4MTkzMmVlNjI4ZjM0M2YyNDcyNTJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjQ1b1F3Qkh1YWxVdnpLMlJWdk4xdHc9PSIsInZhbHVlIjoiWjNpK2plM0Y5NzlHRS8vQXIwMTIxdnV6UTd1V0toOEVKbU4rMFEyYUpWSm16NmxodnRSbTNYMFB1SDZvNWNScHgrbUwzbGtwajFRd1BrQ2pHTmdqYVFxKzY4N1oxdHJPcVlPdzNnV2FxN2F1SEM5clIxV2VkTUd6cVl4SCtEcFBtamJITitLeVFYbWRlOVZsbU9Ma2lyL2lYaDd1TzhxK3Z0WllmSlNlZitNOWpTTFRpYWxuWmd5SjIzekVsbmE2cXF1VUsxMWZHTTN1bld0cFpFUUVQWnV1WU9uOHhuWVZ2NkU3VWR5VVRwTzl0bUxSdExhRlRoc1VhcktXY3VOa1BUc1N5NTE0VjM5bGp0MEtoM084emd4NlpuZ3VPSlVaL0trV1N3VUYyU1NQTDN3dG1BOEREV2VvUUgwd3BvSTI2c3I1QUpEZk9VdzBPbktyc1IvNmRkN1ZqWDdQSlBwSHhyTXlaQStOdDZBQmlsZitvalB6aG1YUFpleXYzSFNnUFdTZ0pwbktwU3ljNm9wa2dwNm81aURweWFzUFJEZ2xqZytXc0pTL0VmbmpJazNBNFMvYUw5ZFZ6RFZlNzZGVU5nNGhZeVo1Qm1WbDhjYldURE1RRGV2ejBaTUhXYVlZeDNoR0RGZlJVRFByY0xYdnpmL2NRUEM0b09yUkNIK2kiLCJtYWMiOiJlOTQzNTk2Y2RjNzMwMTFmNDQzMjZhMDA1OTJlYmEwOGQwMDk3ZWE3YzAwMmI1ZWY0MzM0NzkyMTBkN2NiMDNlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461874731\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1949936476 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949936476\", {\"maxDepth\":0})</script>\n"}}