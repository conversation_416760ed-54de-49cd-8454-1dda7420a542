{"__meta": {"id": "Xd323b0195297055bd36914c547cd2095", "datetime": "2025-06-08 14:46:29", "utime": **********.613759, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749393988.784094, "end": **********.613792, "duration": 0.829697847366333, "duration_str": "830ms", "measures": [{"label": "Booting", "start": 1749393988.784094, "relative_start": 0, "end": **********.516603, "relative_end": **********.516603, "duration": 0.7325088977813721, "duration_str": "733ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516619, "relative_start": 0.7325248718261719, "end": **********.613796, "relative_end": 4.0531158447265625e-06, "duration": 0.09717702865600586, "duration_str": "97.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00391, "accumulated_duration_str": "3.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5740812, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.028}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.597352, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.028, "width_percent": 20.972}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1388774104 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1388774104\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-342197082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-342197082\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-366612342 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-366612342\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1153742173 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749393346722%7C61%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBGR2lkNnh6WmxnWHY2NWFCVlo2Nmc9PSIsInZhbHVlIjoiL2NqVXNXU25oWWZjanl4RjA0dUZzY3Z0Tm9IcG4zV0ZvTVVTMy93K2VrZERYWEo3LzJQci83V21yZW0rZXUyRmxMdWw3NVEwVnFhNjRiMjdzZmZxcEFaMExGemg5NjIyMWhkeVZQbjhtbkowZVBGMHVOYVF6c2FIam9ZNDNKMWhpYlVIeVd2ZVBYVXptbWk2WE9DVmdxVWhJNER3WjBISWFQdjk0bG4rUUk1REFsOWVNamJOejg1OXQxclVRUVg0V1hCakNRZEU1UVRMZU1wdVVlODdwOHdyQjVOdE9ubnQxejV3OWREVmw5Y09JTzQ1TmFCTWNjS2F5T0tlaEZrd3FySWRBTEVYOG1Cdy9XQlU4TTNUMXJTMHRRWXZXejIvVkFRa2FWUXh2VTVLTFBsK3VyMVVjdlNSdEY4SVJ2SFY4SmF1dWM5NERRQXluZjBqYTlodE5uT1liTHYvSzNndE1PakxXVkJwNFpMUFFDZFgvQUQ4czN5NmZBWlFHdU0ycUtmTVdoclNSRjNYVFNFa0U0WDJrK0pUVG0vcHFTWldscVdrR2h4SWNjZjNzTDRGMjg4NHBzNXg3MVNCeTdMbGlRc0pNNjhlOXZ5OGtCenlCKzIrNnRhdHFxMGtZNGczWW1zbzF5KzRNY3Rya0gzdERPVnVObnJWblA4TUtTRVoiLCJtYWMiOiJmMGJmMGJlYzM0YmIxNzIzODQ3NmJjYTgyNmFjZDE1YjJhZmZlZTc0NGU2OWNlY2NmNDdkOGQ4ZGQ4YjYxMjgzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZ0REFxT1lhNUNkY25wdzA5Smg5dlE9PSIsInZhbHVlIjoiaWdPcTlMODhycUZIRWkrYm9aYXBGdWZxdnFTNHFDbDhyc2Q1Qkl4SlRvNEtyRHpaTnlPRVFSV055QlJLd2kvcnVtZGVIZ0ZQYkZjNmVuU2dQakZMdUFEM28rYVc3b2tubEpaU1UyY09Zc2tCOVBKeW5PZlFMTEdFbC9acTBFSmhzT2xVZ1Q1TkNOSnV0UWZDNTdFWEhxSUtBV0JxdG9iQm50VUZxTnBVdnZpTklXVmFOZGx6aGorWFVXK1g5d3FrNTJhZ1RnMEcxd3RHME9VbjZ4SFRocmZUdUhqS2dZTFowREdtdUlZclMwNkx1cDFGcGYwVEZIMW9sRTladHQzaVZhazExelZ3OUVKL3BEcVc0N2loRlNLYXdNU0R1K0kyaVdQdTdWdjYwWEUzRmtxMlhaaGlFemlQemFqWXB1Y3IwdnBwNGE3ZXZTQUtoMUFRT2tGbjQ5czlyYkszQktFOWFoZXJMenRsTUpCTitQL241ZGN0Z0MvT1JIcGE5RVJ0ZTA3b2xRNEtsdnZCb3I5L1A5TkRkNnpwMVFDS1BJVFREbFQ5TjNNNGh2cXluNHEvbHZXUDljcnZJVVZweEw0ZWNhTC94bnRHN3U0TmV6OXVyd2d0emhwbTRaRTA2dmRGZFRTdDYzYytpVDNIZkRLTjNCREJOaGtpcmRkdDk1UWEiLCJtYWMiOiJkMWViZGU1NmQwNzYzODc4NjkzY2I3MmQwMGYwYzA2ZDU5NTUyMDA3MWVjYWQyZTM4OTkxMGMwZmUxODNiYjllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1153742173\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-463591328 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463591328\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-125192397 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:46:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZwdEIvcEVGcUN6dnhEMUZTb1hrSEE9PSIsInZhbHVlIjoicm1pWjdEbGRNQWYzbWIwWGE1WFdtNXA1aGdRMDhQeG44eHJZQkRKOWhRL2IzbDVBVVR4aEZxQXI3eldhQklHY2hRNkNPbllZdTFLNlg3b3R1NVFOamdTRzcwU0Y0dDRQVEpRMG5vbmZ1ditycWJYVVMvd0cyVGZMU2U0MU1iTzRRODJvY1RDZHlLTWpSa1prZ0grNzRLREVFSi9oZTZveWtRMzN3MTFOSkR1OHI3ZUI4d3BTcUkyaXNzVGtYd3dvK0pacWpqdTRVUzhHUkhOdkFzdjJ6MGNqK3k2ZURhQXBYcDFqRGJwL3NhZFp1TUhNMUlIdjdaMlIxa0pwWEpLMytSS0dzWTMzVUxFeCt2WmI5TGxWYllRZ21QckpRUlpONnNzUmI3OGxyKzhidStaRitIaUZ4SlR0NWxOYndxYjAzc3B2eFNNbFhVajlvYysrMDVXZW5XZnhrQlJPek5odUhJNkF5QTRnSDlhMGR1TWtTYjVXLzZqK05HQWpuVUlWRHNmVjJBWVQ2ZlNrdWhsRWxOMkp5SzRQQXlrTGlab1UxdmM2S09PMWwyYUpZMmtOMlJaT09ObFJ0RmtCSUlCbWJ2bGxXeTBiYzd5UldqOE1wYmxIUys5UTdmd3hIRUUvNUxUakh4Wnh5THVwMVlzSHYwYmREZ1BlYjhET2FTdDMiLCJtYWMiOiIyNTcyMjRkYjI5YmEyOTRkNzIwOTY1OWI0ODhmYWJjMmM5ODU0MWI2MTIyMTc3YjY5ZmQ4M2YzN2YxMGNmMWY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBYaUJtYmJDQ2cwQW9mdE95Y0xSdWc9PSIsInZhbHVlIjoiRHpMcGxpeUZDdUx3blFLL3RnRVdsclpJc3YxS1l1R3JmTVY2VHA2aXAvVWhFSXRtRi9BM1JhSzhOclpGSjBEZTNCMHZWK1lUMHdaSWlNVHgwbWVrODBTWk1FMTQybkFFQXFNS0h1MHlYcVFqQlV6M242UHd6dmNXV0srM0RkUm8xczM4ZkVZY2N1YnEzOXNnNnNSSy9KTk1EYnBJTUdqOXZPL1p2bHN4aGdsOTNxT0lBYWVSSEVvdHJ2ZmlQN3NUelRwNUlROGVqUWRKTGdHWkx3Tk5iVU5Ld3hHcHVpL05VenNKTS9DMHE3TFViTVBjNTdrL0tMWW0rWVJyK3FFTXFrZ1NQQm1xYVYzeUE1dnhCcjRuNk9xQ1RIdHA4OHZ6Mk5zMVg0NjM4RDV5ZFpRZEZDS0pmSHlqaFVEdHZFc2NqNlhXSjF3WHhZWDhyT3JVaVRPcXJRQlRrbldJWS80dmhmWEVtNWREd1ZVS09kV0RTM0F3bzVxeUkyNUU4eFphVzVzVm9CQnFrWE9FMzJhcmF4UGg5ZG5seGZjaFFnRUpKWjJCLzZ1ejk4Yy9BeEw0b1RYem5aM2Q1SGN3aVVaT1laQVUray96emJWdzMyeHZJMXcvWUNkOEVOVnBqVTJIZDlGeFZ6alh3NWh6UXI5MHY0RzRRRzZ6N2JCMWp6K2giLCJtYWMiOiI1YzAxYThkYmUzODE0YzlkMWRiZDYwM2EyZGIzOGI1OTkxMDNkNzkzMDJmNGNkNmIwNjc1OThmMmRlZGZkYTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZwdEIvcEVGcUN6dnhEMUZTb1hrSEE9PSIsInZhbHVlIjoicm1pWjdEbGRNQWYzbWIwWGE1WFdtNXA1aGdRMDhQeG44eHJZQkRKOWhRL2IzbDVBVVR4aEZxQXI3eldhQklHY2hRNkNPbllZdTFLNlg3b3R1NVFOamdTRzcwU0Y0dDRQVEpRMG5vbmZ1ditycWJYVVMvd0cyVGZMU2U0MU1iTzRRODJvY1RDZHlLTWpSa1prZ0grNzRLREVFSi9oZTZveWtRMzN3MTFOSkR1OHI3ZUI4d3BTcUkyaXNzVGtYd3dvK0pacWpqdTRVUzhHUkhOdkFzdjJ6MGNqK3k2ZURhQXBYcDFqRGJwL3NhZFp1TUhNMUlIdjdaMlIxa0pwWEpLMytSS0dzWTMzVUxFeCt2WmI5TGxWYllRZ21QckpRUlpONnNzUmI3OGxyKzhidStaRitIaUZ4SlR0NWxOYndxYjAzc3B2eFNNbFhVajlvYysrMDVXZW5XZnhrQlJPek5odUhJNkF5QTRnSDlhMGR1TWtTYjVXLzZqK05HQWpuVUlWRHNmVjJBWVQ2ZlNrdWhsRWxOMkp5SzRQQXlrTGlab1UxdmM2S09PMWwyYUpZMmtOMlJaT09ObFJ0RmtCSUlCbWJ2bGxXeTBiYzd5UldqOE1wYmxIUys5UTdmd3hIRUUvNUxUakh4Wnh5THVwMVlzSHYwYmREZ1BlYjhET2FTdDMiLCJtYWMiOiIyNTcyMjRkYjI5YmEyOTRkNzIwOTY1OWI0ODhmYWJjMmM5ODU0MWI2MTIyMTc3YjY5ZmQ4M2YzN2YxMGNmMWY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBYaUJtYmJDQ2cwQW9mdE95Y0xSdWc9PSIsInZhbHVlIjoiRHpMcGxpeUZDdUx3blFLL3RnRVdsclpJc3YxS1l1R3JmTVY2VHA2aXAvVWhFSXRtRi9BM1JhSzhOclpGSjBEZTNCMHZWK1lUMHdaSWlNVHgwbWVrODBTWk1FMTQybkFFQXFNS0h1MHlYcVFqQlV6M242UHd6dmNXV0srM0RkUm8xczM4ZkVZY2N1YnEzOXNnNnNSSy9KTk1EYnBJTUdqOXZPL1p2bHN4aGdsOTNxT0lBYWVSSEVvdHJ2ZmlQN3NUelRwNUlROGVqUWRKTGdHWkx3Tk5iVU5Ld3hHcHVpL05VenNKTS9DMHE3TFViTVBjNTdrL0tMWW0rWVJyK3FFTXFrZ1NQQm1xYVYzeUE1dnhCcjRuNk9xQ1RIdHA4OHZ6Mk5zMVg0NjM4RDV5ZFpRZEZDS0pmSHlqaFVEdHZFc2NqNlhXSjF3WHhZWDhyT3JVaVRPcXJRQlRrbldJWS80dmhmWEVtNWREd1ZVS09kV0RTM0F3bzVxeUkyNUU4eFphVzVzVm9CQnFrWE9FMzJhcmF4UGg5ZG5seGZjaFFnRUpKWjJCLzZ1ejk4Yy9BeEw0b1RYem5aM2Q1SGN3aVVaT1laQVUray96emJWdzMyeHZJMXcvWUNkOEVOVnBqVTJIZDlGeFZ6alh3NWh6UXI5MHY0RzRRRzZ6N2JCMWp6K2giLCJtYWMiOiI1YzAxYThkYmUzODE0YzlkMWRiZDYwM2EyZGIzOGI1OTkxMDNkNzkzMDJmNGNkNmIwNjc1OThmMmRlZGZkYTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125192397\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-597337885 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597337885\", {\"maxDepth\":0})</script>\n"}}