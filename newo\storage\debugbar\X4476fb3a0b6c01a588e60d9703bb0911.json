{"__meta": {"id": "X4476fb3a0b6c01a588e60d9703bb0911", "datetime": "2025-06-08 13:00:11", "utime": **********.738019, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387610.240545, "end": **********.738057, "duration": 1.497511863708496, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1749387610.240545, "relative_start": 0, "end": **********.535861, "relative_end": **********.535861, "duration": 1.2953159809112549, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.535888, "relative_start": 1.2953429222106934, "end": **********.738062, "relative_end": 5.0067901611328125e-06, "duration": 0.20217394828796387, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00843, "accumulated_duration_str": "8.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.644732, "duration": 0.00623, "duration_str": "6.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.903}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.683925, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.903, "width_percent": 13.405}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.703995, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.307, "width_percent": 12.693}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-707900002 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-707900002\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-744994193 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-744994193\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1504204709 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504204709\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-141751194 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387607479%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNTNC9lUE1TNVJ2NmIvNXVYUjlGbEE9PSIsInZhbHVlIjoiUzBDamE4OVViYzJTbXhJMVNOYVN6dUFkVTFCNXo1cmszTk5BeVltNkxJdG5Qb0duNFRGb0F1Y0V6c2RodnVBRG1Bck9CNzZ2MVBUTmZjQ3k5UUZsLzNXVzBVRGtOOGdFZEpNTWVXRjFVRmYzcDVTcWpjWUpEUE5ib2xMQ2FYRzdORStLcnp1VGswQnhud2JsSlNrM0R2eVhmZ09JNVdCZ0xDRWU5RUUyUXJrRXJoUXlDSUVLT0tZNmVkdUh1bVRYZldCNm5ldlZ4bkV4YnUyR0QwTkZyVkFoU2Y2bXdJZGJxaGdlSlJsKzJJczNGZlV0SmFjZkdIREEwNjFDeEVwNkpHNDBia2ovU3hHaUpuMnVrdXM2d1dBQlEvWXE5UmUrVWcyUUQwYVlvWHlEdStydmY5dXZpeDhBd0kyVEx6YnROVmszZkQvbXVvWWFuN3Jkc1hWNnRjSlZDUHZsaEUwMThrZU9NWE1BQURVM2VxUlh4MlN1ZlFLak5tKzN6TURoUE5VV25VQVlmK1pGeWRmUWdGQ05MaDJHQXRQNkFvakQvVytBWm41SjVVOUxDNFVJR2NTVXdXTzdNc1E5WlJiOGx6QWw0YTgxMVpvNFZMMEdvU1JORHUvOW1sbThmZ3FiVWxLdkc2MHYxcnlzRkEyKzdmMUNqZjVRckQrcTB1T2kiLCJtYWMiOiI5ZjIxMjJlY2U0Njc3NDc2Y2YyY2ZlZjVkOThlNGRhYTEyMDNiNDgzZTQzZmZjNjM0MmViOWE2YjM0MGE3M2U4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImF2NGpla00zUHZ2cFQxMUZOZnN5SHc9PSIsInZhbHVlIjoiVlFHbE03Zi9CeDJpdkV0ZlBnVWNaQldWQjlTcXZlNFJLb0NQSE10RzYvN1VOeDNPV1lrZWxHM1BlV2lQd0V3ZGJGMVUzRGFCTFlsZi9vUTRic0ZEN21qTXdjeDZjNXdkVUpXZGVtamxUL21sZFVyQzVxY1FMRzEydlNwTlpSNW0zbXE3RWNBS0wzT3ptTUs2T3BoTCtxbXg1Z001KzIvYndUb2VpZHdVa3E4Wk1rSTcrbUlxZUkyOEcrazh0emFSa1o4dFhQeS9CMXErVDVYOEhhWmNKUHByTUlYK0tJOEFMN2RSSXhHY3MvQk5BR2hSNVdTbkExVDFMZ084Ymg2aEhLcFBlYzBHd1RVbGgvdnRRcENjYzhUMzA5NDhHTWhzOE14RER2WXcyQXBIL01XbmpUTDBhRG1KTk5KVy9BdFU3QnVDdnBCUW4vSEUyQ2tJR3U4MHdZRC9leDNDczc3ampIdXpueDY2QzBjb3dlcC9zVTVXZ0t0dm40aGRROEFlVHRZK0NMbU90RlhZd0w2bzVRazMrWlIvSm1yNUhxVUtBUjA0YWd2YllMR25BeC9PdmM5RVJtS1l1MHlyZXdGZitZZWExaEpYbGtyRHpXelFlMXRTQ0VndG9ldXVZc2V1RTVvZzhmYTdUaXdQc2VoVitXM2w5eUFheWM5dGRvMDYiLCJtYWMiOiI1ZjU5NDAxZTUwODZhMDM2Y2U3YzMxM2Q2Y2RkOTA2NWRmODQ3YjI0NmY5NmY5NTFmMDc1ZjQ3MjNmYmYyOTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141751194\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1895403401 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895403401\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-140452420 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:00:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd0bzExMlV2alhJZ2Noa0NCOTJnQ0E9PSIsInZhbHVlIjoidUhtTytMdFhBVlpIOTdDTEhHRkI4QWE5L1pxOVlNeFdjQXNwemFLcGU1NHQ2eGkwVHI4c1o0Y28zRGdyOWc5SlNtamJFd3psQTFVQWFtanhWQlZqMEZmQ1J5eVNLaGc4d0Y5WmREUmF6TU9jVzVEN2pwS1lYZlB0dnRNS1VBM0VUOUNwUjhmcURsYjI1dDlEM2RRYW1WU2g1Ynd0WGJ5Q2JseGdLSlNjdExJVnYzMktFWUFKSUY0bmxBL0RPbGs2bVhMbW51OTdKbTBicmRUWVA3b2Z2OEJmcTVuRkxmK2NTY083S0tiQ0ZiQWh6UmQ2cFFkSUR1b0RLMzBJR0VvT1VvTnR1TmNsMzA4TW5iZnNLRUlFMVJtd3Y5OW5qVFdSWDh1YVd6SHBYaGlRdGNicHdlNkhPY2s2Z2xzanZaTUFxbko0YzBGYnVwQTRkQ09yQXNkSXZ2QmdueTZZOW1vbENKQWNodEh1MFppd0dJK25PMnYyZGMzS3h2YVRRSlhMU09mTFN1dG1Zb3cvbW5mWTU0eG9RWmtmSVRFYW9zTUlWYktLUFNZTWM0bU9peWE1YmZ2aUR3V1FlRWcxcFRUNmFvZ1Z6SERVUnpMQ2RXTGxac2RJMUF5dHlaeHprSkZINDAyNDZNVzJIWW40REkrTFFERVNCSkZMakNYRHpMcTIiLCJtYWMiOiJhOGY4ZTk0NzEyYzY2OTVkODRkNmVjNGIxY2EwM2UwZTNhYzM2Yjk2MmM5NTI5NTIwYjhmNmYwMzgzNjBhYTRmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNPWndtdWRhRTFMUzVQK2t5ZEsyMmc9PSIsInZhbHVlIjoiY3ZSVzJLQjBjK01yWmovQTByOFpla3RpNEV0MGdQY2tiL3pRSkMyWkx3dTd5OEhEOHM4OHpZVVJzSW9sK0luMFhhZUtpRlh5VWpMeXpmbklmN2xoU1hvTlUrdmFRUEJxV2Rhc1VXRjdvU3BjOUZqUVljVkJVdGZmZDM2MjdZK3JUMVQ2cEdRdmZLVGJwdlk2SEVVZWVWU0p6WjRhNml2U2paQXNTb25YeDM2ZlorZnM5N0tpQmlaRmxqVFVDc3VQSHpEeUszcyswWDFqeXNRUjkxY0M1RFIxMXFIZ2NtTUYvQStJbWovQ0NxUVhVRWIwVEVtelljeFNLZlVYU0VXK0dCZmVQS0xCQmVHYmQ5MnFHdkpMOUxjM0V4MUtTbnUzK3RNdzN4V3B6S2pFamJoLzZERmo0N3B2YjIwa2tzeEZZQlRzZys2d0sycXNmQ21MSUtTYis5RUFWdkNnNmR5NkEyQzg4S0VLUW9GdU55T2k3eUtGR1lPbkRPcElvNkNkcENTVHNnQnRFTExBMThtYjNYeXE2WEgvTW1yVVE4OS82ZnBsT3Q4RFlTM2poMFl3VDFTYTZRdm52amdxVTQzbHRTOG9BTnUzRVZMWmtTQVJZTUJhVEVvcThnRHgwM2c3cGZJUTR6TVFSTEprVFF5NzZWT0NCU1pTQ0ZLT0tzeTEiLCJtYWMiOiIyYzg5ZDZmMjExMmQ1NTdiY2MwOTM0YTE1ZGIzNjczNmY3ZjI1MDM1NWY4ZGE5OTM3NjExNTA0ZWE4NzdkM2NmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:00:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd0bzExMlV2alhJZ2Noa0NCOTJnQ0E9PSIsInZhbHVlIjoidUhtTytMdFhBVlpIOTdDTEhHRkI4QWE5L1pxOVlNeFdjQXNwemFLcGU1NHQ2eGkwVHI4c1o0Y28zRGdyOWc5SlNtamJFd3psQTFVQWFtanhWQlZqMEZmQ1J5eVNLaGc4d0Y5WmREUmF6TU9jVzVEN2pwS1lYZlB0dnRNS1VBM0VUOUNwUjhmcURsYjI1dDlEM2RRYW1WU2g1Ynd0WGJ5Q2JseGdLSlNjdExJVnYzMktFWUFKSUY0bmxBL0RPbGs2bVhMbW51OTdKbTBicmRUWVA3b2Z2OEJmcTVuRkxmK2NTY083S0tiQ0ZiQWh6UmQ2cFFkSUR1b0RLMzBJR0VvT1VvTnR1TmNsMzA4TW5iZnNLRUlFMVJtd3Y5OW5qVFdSWDh1YVd6SHBYaGlRdGNicHdlNkhPY2s2Z2xzanZaTUFxbko0YzBGYnVwQTRkQ09yQXNkSXZ2QmdueTZZOW1vbENKQWNodEh1MFppd0dJK25PMnYyZGMzS3h2YVRRSlhMU09mTFN1dG1Zb3cvbW5mWTU0eG9RWmtmSVRFYW9zTUlWYktLUFNZTWM0bU9peWE1YmZ2aUR3V1FlRWcxcFRUNmFvZ1Z6SERVUnpMQ2RXTGxac2RJMUF5dHlaeHprSkZINDAyNDZNVzJIWW40REkrTFFERVNCSkZMakNYRHpMcTIiLCJtYWMiOiJhOGY4ZTk0NzEyYzY2OTVkODRkNmVjNGIxY2EwM2UwZTNhYzM2Yjk2MmM5NTI5NTIwYjhmNmYwMzgzNjBhYTRmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNPWndtdWRhRTFMUzVQK2t5ZEsyMmc9PSIsInZhbHVlIjoiY3ZSVzJLQjBjK01yWmovQTByOFpla3RpNEV0MGdQY2tiL3pRSkMyWkx3dTd5OEhEOHM4OHpZVVJzSW9sK0luMFhhZUtpRlh5VWpMeXpmbklmN2xoU1hvTlUrdmFRUEJxV2Rhc1VXRjdvU3BjOUZqUVljVkJVdGZmZDM2MjdZK3JUMVQ2cEdRdmZLVGJwdlk2SEVVZWVWU0p6WjRhNml2U2paQXNTb25YeDM2ZlorZnM5N0tpQmlaRmxqVFVDc3VQSHpEeUszcyswWDFqeXNRUjkxY0M1RFIxMXFIZ2NtTUYvQStJbWovQ0NxUVhVRWIwVEVtelljeFNLZlVYU0VXK0dCZmVQS0xCQmVHYmQ5MnFHdkpMOUxjM0V4MUtTbnUzK3RNdzN4V3B6S2pFamJoLzZERmo0N3B2YjIwa2tzeEZZQlRzZys2d0sycXNmQ21MSUtTYis5RUFWdkNnNmR5NkEyQzg4S0VLUW9GdU55T2k3eUtGR1lPbkRPcElvNkNkcENTVHNnQnRFTExBMThtYjNYeXE2WEgvTW1yVVE4OS82ZnBsT3Q4RFlTM2poMFl3VDFTYTZRdm52amdxVTQzbHRTOG9BTnUzRVZMWmtTQVJZTUJhVEVvcThnRHgwM2c3cGZJUTR6TVFSTEprVFF5NzZWT0NCU1pTQ0ZLT0tzeTEiLCJtYWMiOiIyYzg5ZDZmMjExMmQ1NTdiY2MwOTM0YTE1ZGIzNjczNmY3ZjI1MDM1NWY4ZGE5OTM3NjExNTA0ZWE4NzdkM2NmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:00:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140452420\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-652810456 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-652810456\", {\"maxDepth\":0})</script>\n"}}