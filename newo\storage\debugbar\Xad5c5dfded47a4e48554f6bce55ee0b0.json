{"__meta": {"id": "Xad5c5dfded47a4e48554f6bce55ee0b0", "datetime": "2025-06-08 13:07:02", "utime": **********.483115, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388020.922777, "end": **********.483152, "duration": 1.5603749752044678, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1749388020.922777, "relative_start": 0, "end": **********.267125, "relative_end": **********.267125, "duration": 1.3443479537963867, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.267146, "relative_start": 1.3443691730499268, "end": **********.483156, "relative_end": 4.0531158447265625e-06, "duration": 0.21600985527038574, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00797, "accumulated_duration_str": "7.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.38097, "duration": 0.00529, "duration_str": "5.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.374}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.424424, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.374, "width_percent": 16.437}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.450594, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.811, "width_percent": 17.189}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1938304024 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938304024\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1xcmwyNTRoUXdTVkJlV0VtVFBzY0E9PSIsInZhbHVlIjoiTU5SZ21ZTG91RWJ2ckUwZGRKU1BLNlR5bms2RUt6VUY3Y1dvRFZDc1dVR3ZqbXF0aTFkK3RRcUZZZTZsNWU1cjJ3NUVHV0hjVjNFbUFrMnpJTldzQ1ZBd254dWI3L2IwTHJzSWQrZ1hzU0FKdnM4ZzZOWWxwVXNyNE1IZWR2UDBraU1sU2h2QzBxWDhmTjZ6c2VrLzVLWU43eTdNOWxWQ0tUaFVaazRURFlJUnlIR1gvVm1uS1BFdGxkWWxLTVRMQ25Mc2RZMk4zTDdaVGpvVHZVdWpUdmdNMXlKYlNiUGFvTXUzQ0twVUlmL1pqNCtNSzVhSEFtOUZIakpUdlRWZFdGd2dZVTMyRXh5Nzg0bG4yQTlJT0tlNXQ5UDFXSjhrNms5SllRNmJndStiSGkvRFlvYTlhUzJSYTU5WEMrNDJGRnk0cDlsdzVSOVg3KzlGMDBuS0lVSHJLU3dyQ0xwUVhLdWNTaUg1bVd4N3QxL2RhMDduRVdPakxSbGZCQnNreUIyWE0xN0tCcDdEL3FQdmo3MzQ2SmdYZHRWWU9ud1BucUYwbGdBeHM0azhta2FIWXEvZTV0Y0x0aHBCVU9neXNFT1FkN3FjTTlLUGN5Q0J5V0kxU3l4Rk5aUnNjM1hPU254NEN4MUtLcjZxNHVXSExKK2owVjA5L3BBZTQra2EiLCJtYWMiOiI2ZDk1MTViYjYxYWU1ZDIwNGQ2ZDkwNDhiNTc4NTU1YzRlZWMxY2UyZTdkNmI5M2YyNWE2MzUyY2JmYWUxNGE0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdaOG51MlVuWWh0UEJwQ1V3QUFXeUE9PSIsInZhbHVlIjoiREtLVjNaV01lV0g1M2RMQURxd3NyRGJLa1BmRlZOSXFDOU5qSS8rOFlPUUNZUWllM1NyQm5UYklRbEQvZTNDQ2E3cGkwSkNueFdLS243VXMxa2xhNFlTUDBUblNUK3AxOHUzR2hDcXF3Wm5WZ3VhODlzaWEwSURRSVVZSllTc0hENmUrYlFyUExMKzhjNkNEYWc4VUw0RmpJS1pFekdvUEZqTUZwTWJaUjd0MGFYUC9jNzhWazRtY2pMR2ZqSkVJbVF1S292eFB2Z2twbFlQaGdLZ2ZMSlB5cHZhcXBWTk9PcHJiZW5IU2haVkZadGJrMGpxaWgxVzFzNllxV1Q4TnA2Z3FUWTg3RzdsOEtGeUxESnE3YWhMV01YUHFxcW1NOVVocy9NeHBmcms2T2FNY2NydEliVmE4VXpOVGp4eUFaSGphQWlqcGhMazFVbytHclBjY2ZaWTFQL2dFM0F1bzBkajRYTmxCRVQ4YlhaSDR2Tjk2NENLc0tNN1Bnb0U3dU9mS01rMDZaaHhZemgrRkZhL3d2cWVtYmp4S3hERDFhbTNwbHlnVmVVNzkxVEdJcHc2VlowZUN1N1B5a3ZLbHhlOHh3SlJQSGtVcmN4djZKd0JPcmtvVWRtRit6RnUrajVzYXNnaG9IMHVsbURLSDNiRjJsT0ZTZis2U05aWWkiLCJtYWMiOiJmM2FmOTU0N2ZlNDIzZGI2NmZiYmIwYTJjY2U2OTFlY2FhNTUyZGNkM2UyMTI2Nzg4NjlkOGJkOTc1YzM3ZDAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-916958911 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:07:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5waGtrVFJsRU5IUC9YWm9pdE5hTGc9PSIsInZhbHVlIjoiNC8vZ3JCRUg0c1lWa0xFOTRjblgrT3krVEQvWC9na1NRZHYzbkREUnB1dUhrdFZZTEkzUURsb1dRQWdTLzJSY3ozOFRTNUtMWDNqRzZZRHIyY1hYMkJ6eFl0dEw0cm9FMmxiMDRuWEJMSHlYSVJCaDBrb1oxam5kR1p3bEt2VXI2S25qa2xWTFRQSWdUZXNmaW50OEs0VHg2VUUreGtZemZ1NzJOaEZDZlJ3RUtnRGR3Tjg3TzduTDYvWU90SmcxeHFtRUlQWjRGVlhXa3U2TnFxZko0Ulk2VTdoZWJjYVhzWFhJdVJPT00rYkpnVUFYMWZBR0JqUWJPNU5ucjR3LzhXdmNrTUdMYXRqZmYvN1ZVNVFFOVRnMmNRTXF6UEVaSlBBbWVjU2Z0NGg1YWtPbnZkMEViZVBhRTB1T01hdjREck85YzFhQWZCOVFWdzZBcVBrc0xhZGxsbTUwZEpkVlluMW5PUUpzc3BuSzhXNUxvNzJHT2VXd3hDOVRmQmIvdXVMTHFQOGVLYnZUd3psalhJdm1GZElEYlNUMjhaQlc3NTNSdFhCQ0plTUh5RjlhUW00dHg1VTlsQ0RtT2drODRlclFXcFBhdjBOc3hZNzhqMTcxWEQrdURyelBqSVVzODZZTnhsdkxaMnBaR1FiZzMyS2VpdWhURm5wenJsZ04iLCJtYWMiOiI2NWYxZWM4ODQ0MzBmMmYwNjRiNzQ0OGEyMTQzZGFhMzUxMWZjMmUyZDY5OGM0NjRhNTI3MTQwZTk5NDc4ZGVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitXL1MxOFhHT0Z2RWpUUUNkUTFBRmc9PSIsInZhbHVlIjoiUXdrTEVuTkdlYW1oS1dpb0dudXk2dnI4d1oySVUzM0R6Wi9UOTF4T3FiK0lHNjFiWCthL1RweVdzczFjeFp3YjlxYi9uRVBjTG90bjZMNFZFSHl3WTM5cWNHc0ozemVSblc5UldIQSt4clcwYjVCOUVzYkIzeWZoU1hqSUs0TjB2TSt1NHRzQkhlUXdnQnVvQTg3RGg2UDdkNUEzQ1hmVUowRklDKzZnbDRhV215bWZCa1NMcm5MVTNibXordThHb2dlbCs4MTBTUHErdWIxUHlsYUlNWWN1Y1d2aTI4SDFtNllmVW9PT1BzUGVjd2dhWFdyenpYU3hEczdJcm1CZFN2OUplTTBsZ24xZGIxdGNnd2tnK0tuUVBqbE14Q0ZLWlpYTmg0a1Znd3N3YVNIMUY0QTUrQmhvTGR0Szlyb3dUcmh4Z3NwU0d5bkJNeHZpZmJWQjVGUzh1aUtuL3o1Nk5DSVY2RzBBS0JURVdmczc1bUlQdmJCUWVRRUd0TDNsR05ycFlFYzVoYjY0eWFwbGZoS2RPSlFtQkgyaWxHWEZxb2crbDFEWk9XWHRqa1MrKzdVRnEyZlc0YksrdVpmcldLbWVZZ3BMOVU1UElQRmZPTHdqVVZQYmg1UDhYcVFYRmZQU1M0TFdOMUVtZWEzeDYxOG9iUjBXMHJxUnJ6a1giLCJtYWMiOiJiYjc5ZmM3NjQzMTVlYWU1ZWMxMzBkMmIyMTVhYzg5M2JjNTU1YjgzZmUzYjgzZTAzYjM4MjExNjU3ZDczOWY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5waGtrVFJsRU5IUC9YWm9pdE5hTGc9PSIsInZhbHVlIjoiNC8vZ3JCRUg0c1lWa0xFOTRjblgrT3krVEQvWC9na1NRZHYzbkREUnB1dUhrdFZZTEkzUURsb1dRQWdTLzJSY3ozOFRTNUtMWDNqRzZZRHIyY1hYMkJ6eFl0dEw0cm9FMmxiMDRuWEJMSHlYSVJCaDBrb1oxam5kR1p3bEt2VXI2S25qa2xWTFRQSWdUZXNmaW50OEs0VHg2VUUreGtZemZ1NzJOaEZDZlJ3RUtnRGR3Tjg3TzduTDYvWU90SmcxeHFtRUlQWjRGVlhXa3U2TnFxZko0Ulk2VTdoZWJjYVhzWFhJdVJPT00rYkpnVUFYMWZBR0JqUWJPNU5ucjR3LzhXdmNrTUdMYXRqZmYvN1ZVNVFFOVRnMmNRTXF6UEVaSlBBbWVjU2Z0NGg1YWtPbnZkMEViZVBhRTB1T01hdjREck85YzFhQWZCOVFWdzZBcVBrc0xhZGxsbTUwZEpkVlluMW5PUUpzc3BuSzhXNUxvNzJHT2VXd3hDOVRmQmIvdXVMTHFQOGVLYnZUd3psalhJdm1GZElEYlNUMjhaQlc3NTNSdFhCQ0plTUh5RjlhUW00dHg1VTlsQ0RtT2drODRlclFXcFBhdjBOc3hZNzhqMTcxWEQrdURyelBqSVVzODZZTnhsdkxaMnBaR1FiZzMyS2VpdWhURm5wenJsZ04iLCJtYWMiOiI2NWYxZWM4ODQ0MzBmMmYwNjRiNzQ0OGEyMTQzZGFhMzUxMWZjMmUyZDY5OGM0NjRhNTI3MTQwZTk5NDc4ZGVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitXL1MxOFhHT0Z2RWpUUUNkUTFBRmc9PSIsInZhbHVlIjoiUXdrTEVuTkdlYW1oS1dpb0dudXk2dnI4d1oySVUzM0R6Wi9UOTF4T3FiK0lHNjFiWCthL1RweVdzczFjeFp3YjlxYi9uRVBjTG90bjZMNFZFSHl3WTM5cWNHc0ozemVSblc5UldIQSt4clcwYjVCOUVzYkIzeWZoU1hqSUs0TjB2TSt1NHRzQkhlUXdnQnVvQTg3RGg2UDdkNUEzQ1hmVUowRklDKzZnbDRhV215bWZCa1NMcm5MVTNibXordThHb2dlbCs4MTBTUHErdWIxUHlsYUlNWWN1Y1d2aTI4SDFtNllmVW9PT1BzUGVjd2dhWFdyenpYU3hEczdJcm1CZFN2OUplTTBsZ24xZGIxdGNnd2tnK0tuUVBqbE14Q0ZLWlpYTmg0a1Znd3N3YVNIMUY0QTUrQmhvTGR0Szlyb3dUcmh4Z3NwU0d5bkJNeHZpZmJWQjVGUzh1aUtuL3o1Nk5DSVY2RzBBS0JURVdmczc1bUlQdmJCUWVRRUd0TDNsR05ycFlFYzVoYjY0eWFwbGZoS2RPSlFtQkgyaWxHWEZxb2crbDFEWk9XWHRqa1MrKzdVRnEyZlc0YksrdVpmcldLbWVZZ3BMOVU1UElQRmZPTHdqVVZQYmg1UDhYcVFYRmZQU1M0TFdOMUVtZWEzeDYxOG9iUjBXMHJxUnJ6a1giLCJtYWMiOiJiYjc5ZmM3NjQzMTVlYWU1ZWMxMzBkMmIyMTVhYzg5M2JjNTU1YjgzZmUzYjgzZTAzYjM4MjExNjU3ZDczOWY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916958911\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-7******** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7********\", {\"maxDepth\":0})</script>\n"}}