{"__meta": {"id": "X482fe2799f05e43d1ccb96bf9e6d2e30", "datetime": "2025-06-08 13:19:49", "utime": **********.746479, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388788.44198, "end": **********.746525, "duration": 1.3045451641082764, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1749388788.44198, "relative_start": 0, "end": **********.594069, "relative_end": **********.594069, "duration": 1.1520891189575195, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.594089, "relative_start": 1.152109146118164, "end": **********.74653, "relative_end": 5.0067901611328125e-06, "duration": 0.15244102478027344, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45272248, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.007730000000000001, "accumulated_duration_str": "7.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.68855, "duration": 0.00623, "duration_str": "6.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.595}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7235491, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.595, "width_percent": 19.405}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1618837375 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1618837375\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2021109592 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2021109592\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1286048071 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286048071\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-840103711 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpGYW5vMElZem5Gcm9qcTZ2OXB4d2c9PSIsInZhbHVlIjoiL0JRTExDVjNKRFJxV3VHV2dKbFE1aVJJTWpkdS81N2g0TFBRVjFTc3M5ci9hN2pGM3pDVnFzMjJvN1Z2TzBaNmVBTDBsRmx6OXdid25zbGlseGVUazh2dE5tdVg2em8xSTlrRmRPek1QV05OVThsVnAyOXVvRlBnRzBjVVFyV0E1ajArYkxnbzg3czYyUTZpTlVtc0VSOVNrWlpjMlUyWENnRjRvTHlzc21hV3ZyUTRXakpnZzhEd242Y05kNUIyTEdJQld2YXdaR1ZnN1dtRGMzR28vVnpHQWdQRjRrUFNuZHc5TWYra3dDQkRQdjFiMzBqZU5lWVB2Y1BlVFV6M2tZMi9MVXhScGk1ZnJ3enNhTnBwTy9lUUYzK1RwNWhwaFkrNklEY0ZBN1dXVHFRTDlkZEE0U3NNYmo3OFJkUEFMelFnQnpNeVREQ3Zmc0dMajRlRWpld1ZMYUJxai9mdUlEVlZsNW5RSUVxNlA2dzNHWGQvN0FQOGZheWx6OU1mUm9MSkRoSjBpTmd0WUgrYmo0UGtPMFlHUm5oWEhvWmladS92WS9KQTErV2dsY1NnMVg2ZmgvaklMQlN2eXF4Vm9NR21GWnBTb00vV0FjNUMzek16Sm5PZENsRlgyL2VwT3hubHc4alE3NDZhaUdQTC9xUURrRUpsYTh1bHNYa3kiLCJtYWMiOiJlODhmNjRkZTU3NjI2YjJhMjIwYTkxZDE0NTVmOGZlNzkzNTNjMDE5ZGNlZDcwYTk0OTU3MTE1ODdkM2FkZmQwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImgxdTg0VzFtc1FrWmJFclM1U3NxOFE9PSIsInZhbHVlIjoiMU9TNzRKV3ErRFJQTVdpdWlUa3BiTkVid1U0K3gra05hTWpNcDg4Y2R3L3cwZzNaT2RNL09CeTZjcTkyb01BWWh6Z3Y3WHdCYU0rNzB4bXZDTWUwL2x6UTNGN0hUMzJDb1RDL3NJZUZWclpTeXBNM3orMXIvK0tnRmhHQnB6RnNBdzdTQjJFbjE1ZVh5SFNsVU1HUVM1N2pzVklib1p6RjhsRFJ6c1FqQ3hsM1NwUUVHZGE1YUtCM3VNOTBMcUxCOE1rTHc2M1NnTFozY0hmN29BMHpIMVVDazhDcGlpWGNxcG1DSmREWnk2NHpORjhQN20vQWx0Z0JSVUl5M2JWdTRFRnc1bWdHVEV2WWMwc2JKMTZsSk9pbTU3OFYwU21Ga3NtMnZoNjhKWGM0TDE3bVJ4eXBFdm1wWEFQbm9UbHNKRU5rS1dkZ0ViVExmdU9YSEc4cXArM0I3bVN1ZTZ4eEtCZVN1Sm1HOFhPVzdCci8vM3BxMHVUUmdyNzVBQ3ZxTjB4eUFXTk5xTUxvbm1uZU1SM3dMVEpYejhYV09xNnJPVUlaUlNwL0U1aG04R1huZUFXUFVGd0dRMkp5RXZsK2VSUkY1bnplWEdEd0dURWQzR1RqSUVQVW5FV2xrZVoxc0tlVkZ0RGtzMjdaaEJqazN3c0Z5MVZQclRaV3hPNlIiLCJtYWMiOiIyOTU3MGZlMmIzMDIzOWE2ZTI2NDI4NGQxNjkyOTE3ZDYxYzQ5NmMzZGVlNTI3MmFlMWI5YmQ5MjQwYTI3OGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840103711\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-857850485 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857850485\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-140188178 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ3M3p2eVg2TXF3U2VENmlETGw1T3c9PSIsInZhbHVlIjoiVUhmWThOeitmYkN2cXJLTzNNangyNXVaN0JtSXRmMEduNjJSM1JSOE80WUEvYkJLY2tOb2tCWVhpM3dIcFVJeXB3TFBtWjhOSTQxM3VCblNWS1hmZ00zZXdFL0lzNlNUR04rVVh1dk9USWdON2hOeUtTNDY4cm0rQUV5YW4vY1ByQ3lORzNzbEdjU3pudXNlcmQxOGZ2WmpvVWl5bUladzJ6Skx5eC8vSGFvck9aODJJK24vczBsWXVxT0cvZDB3a0hCQ01sanBjcVZpa1oyYTJEUGhZemsySk50YlVHQmVUUXRTa1p1Q1gyV0VxUFFqZVk0c3NoanBxcERlbG1wekJiRUlDNW1SYnBYZmpzNXlEWitkQjR4cVVuSFU5UmxHdi9aM1lYZmdXdUh0a2VRR0huVWpEN090TFZEV2RZUUM3OXpiSmR0aENDUk9BL3JJOVB6eldOdlZOZ0RXYjBLdEh1QXJZVmlMcG9IU2tOWDVvakt2blkyWlh4UDNhZ3BrMm5kVFliYzB4THVROU5NeUVjMkN4WFRWaGROVzJoZHlYa0FXUlovb1J1d0ZTRnpJYkRlbXhWQXBMRXdtRyt0OUZucU9nODc0U3lCTHdCOE9EeW9ZaW5VbTR1L0xnMXlLVFZJMzlhYjFYc0xEWkZSRmljUlV3WWhSMm91MWRjTFAiLCJtYWMiOiI2YWYwM2Q2MDA1MDdkMzViNzU2YzAzZjJiN2QzYzdkOTBlNzczMTgxNGE1NjAwZWQxOTJmYzdmNzM4NTI0NTlmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJtTCtmd3JDZW5vZzQ5Qzk0VDVvUUE9PSIsInZhbHVlIjoiUWJoeDdpQjBqUzYrbHJLOXRNOG16WXk0TUZmMVJERDJ6dnpuNS9BTU1ZT04wdndEWkxlMEYzbTZzOTcrU0EyamtuS1hKSFRESEJGZjY5cXRkeG90ckc2NjM3UVF0SUM2RFZIc1ZPK1hHc3JrMWpxMHcrNjF0bk1DT2RMWVVYbUxkK1ZVOXNENHFGa3ZaeDVMdFc4L1dDT3Z1VG5rK05nRkVTWXNTWFJWU0NMNStLT3ZDVTVTNEpvd0tkMjkvMTF2ODdlcmwxU3V3NUpHNkJ2RFBQZlJtN21ZckRtWWZ3MUJtTCtYbTVXWnA1cHUvdkltcTBrQkduVS8veVZ6TmFqK0c0YUxkQUZ5a3hCTEc2RnNRNWh3NmYxVnRqRFZTMjNWWFA5d0dSb3BIUDloUC94cHlURDAzU3M3bmMrejJhNzhUTWNQcW9iZzZsYVRVNFJpVmdER0UydHlsL3c4VkI1cTREUzlKTHF3R0ZFQnplVytOa2tVMkVGTU9RMVR2L3NwMUttQzhNdEkvaXVtdzZqQ21kcW1iUDBaTTJOcEtlUlg4eGwwL1kwMWxZY0hETGhmUG1QanhhMGhJTE82eGY3WVVYK0ExR0VVaUxUaFU1bVZZaWJHWmlwQ05DVCtsMVRmcFc3bjB1a0t3V0lwdjJtM2NkcUxnSFFOV3JoVnN5QlQiLCJtYWMiOiI1ZmZmYWY3YWMxZGE5MGRlYTg5NmE3ZDFiNjVkYjY5YjYxNjExMGQzMmQyOTdhODI0YjNlMjM4YmMxNmIyODk4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ3M3p2eVg2TXF3U2VENmlETGw1T3c9PSIsInZhbHVlIjoiVUhmWThOeitmYkN2cXJLTzNNangyNXVaN0JtSXRmMEduNjJSM1JSOE80WUEvYkJLY2tOb2tCWVhpM3dIcFVJeXB3TFBtWjhOSTQxM3VCblNWS1hmZ00zZXdFL0lzNlNUR04rVVh1dk9USWdON2hOeUtTNDY4cm0rQUV5YW4vY1ByQ3lORzNzbEdjU3pudXNlcmQxOGZ2WmpvVWl5bUladzJ6Skx5eC8vSGFvck9aODJJK24vczBsWXVxT0cvZDB3a0hCQ01sanBjcVZpa1oyYTJEUGhZemsySk50YlVHQmVUUXRTa1p1Q1gyV0VxUFFqZVk0c3NoanBxcERlbG1wekJiRUlDNW1SYnBYZmpzNXlEWitkQjR4cVVuSFU5UmxHdi9aM1lYZmdXdUh0a2VRR0huVWpEN090TFZEV2RZUUM3OXpiSmR0aENDUk9BL3JJOVB6eldOdlZOZ0RXYjBLdEh1QXJZVmlMcG9IU2tOWDVvakt2blkyWlh4UDNhZ3BrMm5kVFliYzB4THVROU5NeUVjMkN4WFRWaGROVzJoZHlYa0FXUlovb1J1d0ZTRnpJYkRlbXhWQXBMRXdtRyt0OUZucU9nODc0U3lCTHdCOE9EeW9ZaW5VbTR1L0xnMXlLVFZJMzlhYjFYc0xEWkZSRmljUlV3WWhSMm91MWRjTFAiLCJtYWMiOiI2YWYwM2Q2MDA1MDdkMzViNzU2YzAzZjJiN2QzYzdkOTBlNzczMTgxNGE1NjAwZWQxOTJmYzdmNzM4NTI0NTlmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJtTCtmd3JDZW5vZzQ5Qzk0VDVvUUE9PSIsInZhbHVlIjoiUWJoeDdpQjBqUzYrbHJLOXRNOG16WXk0TUZmMVJERDJ6dnpuNS9BTU1ZT04wdndEWkxlMEYzbTZzOTcrU0EyamtuS1hKSFRESEJGZjY5cXRkeG90ckc2NjM3UVF0SUM2RFZIc1ZPK1hHc3JrMWpxMHcrNjF0bk1DT2RMWVVYbUxkK1ZVOXNENHFGa3ZaeDVMdFc4L1dDT3Z1VG5rK05nRkVTWXNTWFJWU0NMNStLT3ZDVTVTNEpvd0tkMjkvMTF2ODdlcmwxU3V3NUpHNkJ2RFBQZlJtN21ZckRtWWZ3MUJtTCtYbTVXWnA1cHUvdkltcTBrQkduVS8veVZ6TmFqK0c0YUxkQUZ5a3hCTEc2RnNRNWh3NmYxVnRqRFZTMjNWWFA5d0dSb3BIUDloUC94cHlURDAzU3M3bmMrejJhNzhUTWNQcW9iZzZsYVRVNFJpVmdER0UydHlsL3c4VkI1cTREUzlKTHF3R0ZFQnplVytOa2tVMkVGTU9RMVR2L3NwMUttQzhNdEkvaXVtdzZqQ21kcW1iUDBaTTJOcEtlUlg4eGwwL1kwMWxZY0hETGhmUG1QanhhMGhJTE82eGY3WVVYK0ExR0VVaUxUaFU1bVZZaWJHWmlwQ05DVCtsMVRmcFc3bjB1a0t3V0lwdjJtM2NkcUxnSFFOV3JoVnN5QlQiLCJtYWMiOiI1ZmZmYWY3YWMxZGE5MGRlYTg5NmE3ZDFiNjVkYjY5YjYxNjExMGQzMmQyOTdhODI0YjNlMjM4YmMxNmIyODk4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140188178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1931744641 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931744641\", {\"maxDepth\":0})</script>\n"}}