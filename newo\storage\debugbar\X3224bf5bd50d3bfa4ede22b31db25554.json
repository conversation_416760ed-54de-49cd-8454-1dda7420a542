{"__meta": {"id": "X3224bf5bd50d3bfa4ede22b31db25554", "datetime": "2025-06-08 15:09:18", "utime": **********.900825, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.179514, "end": **********.90085, "duration": 0.7213361263275146, "duration_str": "721ms", "measures": [{"label": "Booting", "start": **********.179514, "relative_start": 0, "end": **********.827225, "relative_end": **********.827225, "duration": 0.6477110385894775, "duration_str": "648ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.827238, "relative_start": 0.6477241516113281, "end": **********.900854, "relative_end": 4.0531158447265625e-06, "duration": 0.07361602783203125, "duration_str": "73.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43918496, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015479999999999999, "accumulated_duration_str": "15.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.87135, "duration": 0.01478, "duration_str": "14.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.478}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.891044, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 95.478, "width_percent": 4.522}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-38960791 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-38960791\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-461558510 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461558510\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1305820957 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1305820957\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1812598585 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRoRVFHdm9kcFBQODFCMExiNERTL3c9PSIsInZhbHVlIjoibVJQQk1nUzVFa2tyN2poZ1ZiQSs4NmxMTzNNdGJoNVBuYVFJb0FCeW5LRTl4dEZVUkx2Y0EyTFFSV0g4cExRTTZvbGNmYUhlRzlib1Q5U2E4a3VYeVFUYjl2MzNENkRpRzZidlNVYmxLcXdTam1KY1VGVytYWjhHMFVyZmRxVk5lbmNPWUMxQkk1dW9pcEp5N0xITFZnb2Y1SXdyZVVsdTF0Q2VLNTRZMmtmazFhMHBLQVplTkpwYUdUSkhsWUwvY3dVYWlyTlExU1A3TGVwK3FmNDVHK09EbE8wNVJxdytnYytrNFVEK2pzT09KNzQzcU9CUXBzcm1vQmhRWTUxa1M0aVFRQ0xzaVBaY1pHK1V4S1dmYTgwTFBkT1Yrb2VHdlRBV0FjMXk1MW5NbUJxQU85eElLcWtGTXVJYzVELzNLZjJXRWxlT1NBK1c5ZnZObUdaM2dwVnF6WHlDcXBuVzZDajBJNzZVWFdoM1ZQTEJKM1Vmcnlxa3ZhbmlvRDFyd0NzbzgwRzJZbEN2Nm9tWVFaL0ZkK1FJa0VwTFRlRHhkRDlpdmFwS2N4RkQxajFwbjZRWHg1NzBOZVZhNmZrRFdNQlZvOXBHZWlqcnB4alYyekxyOXM1b3NsNjl0R0xTVko3ZlcvNFdlNEVDTDVSMGh1elpwZEw3a1VQb2xHUWMiLCJtYWMiOiI5NDc5OTZhMmNiNzY0NmNmMzgyOGM4ODlmMTJkZmE3YjlmYzUxZmU4YTc3ZDJmMTYzNjI1NmY3N2EwNGM2ZmZjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJ1N0E2bGdMUURlVEpnejUvYko5alE9PSIsInZhbHVlIjoiK3RrcUVmdDQ5QW5NYkJ4THV5eERjWDkvWmhTMHRwQ1RwbHc0aGxtT3l2dFZnOWYxcUZEdHp4eFpTVHJMaDRPc08vamJiREhFeWs1YU01NWt3VjBYanRmTkxvRlJnUnJpbVYyb09XbzJEeXptVHFVNVdMTys4eFI5TGJGOWRlajZPbmJ6dkd4U1pCVWs2OFFUaGxPWjZXTzdkckxzdWhBK04rUVkyanJDWmV3cnlsSDU2d3lGK1pnb05LNW9iRW1IeVprUzRFRzlvK2ZRaHMvSGJoQjAzMjR0R05aV0kwdFpGdVJxV2RseGd0dGcrSHVobjkvQzN0RkVFb1pRQVVrZjFYcHZBeEtrbFRpdFdwRFd6SEE4ZTZnSnVHSG15ME1PMTRRei94TWo3L0o0QnZRekVBak4wQllmNU90WmpjZm9DYkRWODNnVm8xMjNzL2wrWlVpM0dFZnhyamNXZmtsYjFMTmZhOFlLT1ZBcUI0THBTckVmeTVKZ3Z1VHoyWllsREpJYXFOUGpaS1JVNDRDaEVFVzVlVE14RkZzVEYybGtGUHdmL3F2ZW9mU2swS1I1cVVuZHhrN0tCUmV0NmZhb3VrNGM0TGRMU0dKaUdhUFd6dGRCMTZLYTY2Ty9VU0I5YjVndEwvdkxreUppc09wcVVLSjZFdWRaU0hhTm43WGsiLCJtYWMiOiI5Y2M5NzU2MDJlNDA4OTdhNDU3OGIxOGY2NzgzMmNhNjcxZDFkM2JlNTc0NTY3ODdkMDk2ZTY4MmExMmYwNzRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812598585\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1398078750 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBLZTlXMW4yZDI5ZXE0b000RDB4TVE9PSIsInZhbHVlIjoiUWYxdlNuNkRyNVMxVUVYZEt2ZHlnVlpzYjZVSGpLWEdHK2htMUpvbGVHWkFLRytxUGllbVowdnhLeUZFcDAxaUl4R3VIa1k1Z1FKK0VOMjZOTzFuR25mZWpEaXpVWEJWcUVYbW14Yzd0VHhBWFJsSFh2dXZRUVoyY05xd1gvK2Z0emdaelBMN2EvRS9YaUV5OUtwSk05VFMybGZ3ZDVsRVRjWXFjUGoxS25kWHE3bjNaWTZQRE1lWkNHeHpmOGw5VmplMjR6Znd4QXlOckd3bHhtSDNiQ2RvODkvaDNpM2xKd2RJVURvTkJ5V1FPeThyK3YzQkRwWEVrQWU0ejdQL1QzT1p3dTFjTm5yRkE2NEFsalBKTzZ1emZyQitHc2xibDZsMkFSeUtpVXlURFhOL0hPSS9Ob2hodDRkQ1Q5bDdzNENpYVk2MDk1K3RQSk5yZDA1Q3pjOTFqNHQ3cE5lbkhxVlJPNWJlcVpLT0c5MUhveElCcFJKRDNHNGFaNTU3RDBLU0gzR2NXeWI1dytWVWxjQjBuM1JSS0NweXhZbzVOVi9jdFdhMUVTVmcrTFIrenZHUlNDZXNDNkNMMFIvUHBUaDFJQ2VJMU51Und3NG9hQVc3ZDQ4TkcvY2NsY3VsWnJ6bm5tcHZNNnl1TWhpMlhpaUxrOXBqVERwZnM2L1IiLCJtYWMiOiI5MjE2MzU2NzliOWEzMmIzNjAwNTEyY2NiOTBlYmY5MTNjNTEzYTYzNmMxNDhmZmU4OGIwZTNiZTY2YmEwNmNmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZiQ2crTHQvNFRnWWs4VE9UVTBjSXc9PSIsInZhbHVlIjoieWhlaHRKOE9xeCtXUWEvVmxPNnVJMURuSWo1YUtXVUpta212dDUydU92ZUJCMDE0bjJqSzRtczc4aUR3Uk9BSnN2cGRWRDRkRUlVRUd3U2F1UW1yZEs5SmNKblVJSGJmYTB6eGlneXBaVFVBQmZob25ibU82clJzU3FMNXZsQ0UxbUlxQzZxblFNT2YrZlpmQm9PVWVwVXB2M2FlaWxoQzdGeTU3eUd3ZWNQb09YZUJ0UnRJby81WitWalhGSkVtcHBqZEdsZHBIcWFnNEVqVUpjN0hYSnViN2pWaklxZGNZeno2L2xRTlNzdHA3KzRWL2ZrVVIxQmVJelhONDBvYXZhM21Ya294YU4zTFl5anQyY2NUa3A4VG45RkQwTXVFS2Ywa3Y5QUtFSllTQ1ZaOU1LakowOTdhbW03U1ZMZ2VIaHNZZWJ4KzJoVjJIMjRRbXM3ekM0ZVZ2OGtReEhkTmZXZE9nMkk3WDlubWVNUWRhU1UzZUplakpqbHVMakJielpTYlBHTjF4aUFTUnE4T08xVU1uQzkyZk5KUkR3eTMwdTc1TWZJNkJYNlVkdFZCc2ppWThkS0VPeVFNM2tEeHFwVnNVbW9VUURnc2xzbFNrYzJFZXN1Wm16QmZmU20vS3FmdWk0eHRlaFBtZEc0NU56ZlpEZjR5TUFxcm9kTEoiLCJtYWMiOiI0MmIxYzA3Y2Q4MjViNjA5NTcxZDFkY2QwOGYwMzVjZjgyNDhmZWYxOTM1MjJkM2IyYjk1ZmM2YmU2N2U2YmNkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBLZTlXMW4yZDI5ZXE0b000RDB4TVE9PSIsInZhbHVlIjoiUWYxdlNuNkRyNVMxVUVYZEt2ZHlnVlpzYjZVSGpLWEdHK2htMUpvbGVHWkFLRytxUGllbVowdnhLeUZFcDAxaUl4R3VIa1k1Z1FKK0VOMjZOTzFuR25mZWpEaXpVWEJWcUVYbW14Yzd0VHhBWFJsSFh2dXZRUVoyY05xd1gvK2Z0emdaelBMN2EvRS9YaUV5OUtwSk05VFMybGZ3ZDVsRVRjWXFjUGoxS25kWHE3bjNaWTZQRE1lWkNHeHpmOGw5VmplMjR6Znd4QXlOckd3bHhtSDNiQ2RvODkvaDNpM2xKd2RJVURvTkJ5V1FPeThyK3YzQkRwWEVrQWU0ejdQL1QzT1p3dTFjTm5yRkE2NEFsalBKTzZ1emZyQitHc2xibDZsMkFSeUtpVXlURFhOL0hPSS9Ob2hodDRkQ1Q5bDdzNENpYVk2MDk1K3RQSk5yZDA1Q3pjOTFqNHQ3cE5lbkhxVlJPNWJlcVpLT0c5MUhveElCcFJKRDNHNGFaNTU3RDBLU0gzR2NXeWI1dytWVWxjQjBuM1JSS0NweXhZbzVOVi9jdFdhMUVTVmcrTFIrenZHUlNDZXNDNkNMMFIvUHBUaDFJQ2VJMU51Und3NG9hQVc3ZDQ4TkcvY2NsY3VsWnJ6bm5tcHZNNnl1TWhpMlhpaUxrOXBqVERwZnM2L1IiLCJtYWMiOiI5MjE2MzU2NzliOWEzMmIzNjAwNTEyY2NiOTBlYmY5MTNjNTEzYTYzNmMxNDhmZmU4OGIwZTNiZTY2YmEwNmNmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZiQ2crTHQvNFRnWWs4VE9UVTBjSXc9PSIsInZhbHVlIjoieWhlaHRKOE9xeCtXUWEvVmxPNnVJMURuSWo1YUtXVUpta212dDUydU92ZUJCMDE0bjJqSzRtczc4aUR3Uk9BSnN2cGRWRDRkRUlVRUd3U2F1UW1yZEs5SmNKblVJSGJmYTB6eGlneXBaVFVBQmZob25ibU82clJzU3FMNXZsQ0UxbUlxQzZxblFNT2YrZlpmQm9PVWVwVXB2M2FlaWxoQzdGeTU3eUd3ZWNQb09YZUJ0UnRJby81WitWalhGSkVtcHBqZEdsZHBIcWFnNEVqVUpjN0hYSnViN2pWaklxZGNZeno2L2xRTlNzdHA3KzRWL2ZrVVIxQmVJelhONDBvYXZhM21Ya294YU4zTFl5anQyY2NUa3A4VG45RkQwTXVFS2Ywa3Y5QUtFSllTQ1ZaOU1LakowOTdhbW03U1ZMZ2VIaHNZZWJ4KzJoVjJIMjRRbXM3ekM0ZVZ2OGtReEhkTmZXZE9nMkk3WDlubWVNUWRhU1UzZUplakpqbHVMakJielpTYlBHTjF4aUFTUnE4T08xVU1uQzkyZk5KUkR3eTMwdTc1TWZJNkJYNlVkdFZCc2ppWThkS0VPeVFNM2tEeHFwVnNVbW9VUURnc2xzbFNrYzJFZXN1Wm16QmZmU20vS3FmdWk0eHRlaFBtZEc0NU56ZlpEZjR5TUFxcm9kTEoiLCJtYWMiOiI0MmIxYzA3Y2Q4MjViNjA5NTcxZDFkY2QwOGYwMzVjZjgyNDhmZWYxOTM1MjJkM2IyYjk1ZmM2YmU2N2U2YmNkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398078750\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1997625493 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997625493\", {\"maxDepth\":0})</script>\n"}}