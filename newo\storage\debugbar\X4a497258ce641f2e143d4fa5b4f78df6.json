{"__meta": {"id": "X4a497258ce641f2e143d4fa5b4f78df6", "datetime": "2025-06-08 13:04:22", "utime": **********.111045, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387860.595679, "end": **********.111079, "duration": 1.5153999328613281, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749387860.595679, "relative_start": 0, "end": **********.813564, "relative_end": **********.813564, "duration": 1.2178850173950195, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.813587, "relative_start": 1.2179079055786133, "end": **********.111083, "relative_end": 4.0531158447265625e-06, "duration": 0.29749608039855957, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48123192, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01611, "accumulated_duration_str": "16.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.95049, "duration": 0.00835, "duration_str": "8.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 51.831}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.990993, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 51.831, "width_percent": 7.511}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.050797, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 59.342, "width_percent": 8.876}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.058048, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 68.218, "width_percent": 9.683}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.075215, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 77.902, "width_percent": 15.891}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.087153, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 93.793, "width_percent": 6.207}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-105749326 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105749326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.071733, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1541321335 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1541321335\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-495218672 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-495218672\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-590885912 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-590885912\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZidnRFQm50QTI1NjVIS2JqOXNjeEE9PSIsInZhbHVlIjoiZ0ZvL1RVT1pZNmhqa1I0Y3pIRExWbFdrcWZRYzdaOVZWR2RERG8rallrMWE4WVhwamkwd3hJM0REc09PT3hPRGlsanlNV1dtOFVTcmZjQmtZTzdNNWhrVHZ6OGRmRnowdittVXM5RUswZHQyUkVRMEw4WmpGb1lhbldtZnowWUdtNnBaZDl4TGZ2TlZEcDBOTlI0cEN1Vy9RZGJ3b1R1NW9UUVpTejNkaEZ3Nm9zNVZjTmZ4Z1Z3Q1I0TUttU2luUHNqaUw5WXdFVjVaa2pFTDU5SGFJVW1KMG9IcHFhWnRWaWEraEEzejZCUkhsT3ZLUnBsejRWOEZPUldlL3QwS3c1NFZCOTd4N0hKK0V1ZGlWbmthendPZEVPcTQzVGxhRlIwWitRV0tNcmNWWWxZSE05dGNFckRYY3M3RVN0UXNiQzJmM1R0WEEyd3ovb3ZQaGUyenZaaXhLTXovQWZ0aUdiWTlYM05ybi9RU1VQZFhZRThRRXBSSG10NUlpd1dyYlRpY2VLY2VBRURkQ3pTUGw0NXQwcGRLaStBYkxycnJwa3F1NFNYWWZXZmhhV1FzWlJZMnZxMWJTYnl5WERUdDVlOWJJeGJYUDZVTVFZUjJNZmxKMEVCTFpWR2swcFJGTDlCZWMrcHYxcEVtK1FET3F6dHdOdDIwTW1Za21BTFIiLCJtYWMiOiJmOWUzMjYxYzU3Y2NiYTExNjAxMTFkODNjZmFkZTEyODkzYTY4MTY2ZTE0NmQwZTMzN2Y3NDg2Nzg5OGRlODk5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik8veTE1NThpTkhpZEZWb3UrVlVnR1E9PSIsInZhbHVlIjoiOWxkSDJYQ2h0UGt4Z1BsYjF5UHZwVFJqa2dzREZUYXVaNFNZOWt1eE53NWhQMFVabWgwRXVDQ3VwWEFmNnFpZkdZMy9qR3JOQmw2WHkrTm5rMWYwYmpqQWFMcjZTSC9Sa280b1hOK3J3aEU2Q2hrOXlWWnpLaHJ0SEV5NkhLUU1aR2tZTlpFVUExV0NxYzFoT0JNaXFlSkZpa3k3cUxadExiYm1JY1gzdFNNQld2dm41NnlhWXJaK0N3eUdYMDVCYlRPb0s1S1cyRTdEZGk4c0wzeVhvNmhwVStFNGNlVDhITDRWZ2Z2LzdlZFcyVkV0YXN6WkdFa050eWdjcFZ3eXQweExPSkt2OTRvMW96RUJJK3NmaEltdXBhTVY1WG1DSTRQZHZQYWl5R1NZSlk0YjZ3aWlJSm16b1JmLzNybXRFdENFY1FtanAvOEZjeVl5aVI0Z0MzdkdRTGc4a2E5QzBDVEV1WHZIbGJyZWd5ajNIVFByUFZRWkF5djRkaExsdkIvM0Y2U1BkQWp4Y2dwR21icjFMa3BMNFpRU1pVZkc3SUVwb3FrQmdqYlV0ZFB4blhqeEVGWWt2VTUwVmRYYkdDVzBTUG0yQnpOQjFRd3crRmtKV29XZGNZNUxDYXZQejY0SEJqcEtETldjVW5jQzFZL2RTYnI1WUZRZ3NlTVkiLCJtYWMiOiI5MzJkYWI1MjM1NGZkODY4MGE5YjNlOGY3ZmQ4ZmFlYTJhOTJjN2NjZDFjNDI5MWM1MzhmNjdhNjY2NjAyNWJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1128612424 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128612424\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-726879170 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:04:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjY1VGhUUTgvQzdMcUp0dnpXYTE0aUE9PSIsInZhbHVlIjoiREU2VTVPUG1qYzdvYXhLM2JjVDhtRnZ2TmQwbm9KeXkydlJvMjc3ajM1RURDM0NVck5LQXZ0MkNHVURPQ0tuNEd4RWN3ZE5lR0pZM3Q0WElvN1k4MWJ0QXRrZXFISWxuZkpHOFZxcG5ERGFIOC9mdTdKb1JyNnA3Qy9QL1o4ZE1HenkwRW5sVTI1TVVQcjJGdUJIRFM2anZOU1B5QU1CSXEwMk9HQW5rT3p2THVsZnByMk0rL25XbTlNQnM3RzEzZkIrMFJSNnZtZHJnUm44S3B5dXlKQWVFWHFaQUlpMWJ5MlpWUGNWdnlhVFNSb01PVUZzbzJXZitPWU5oUGhYaHV1c1dTOEliWFhVVktGd3RvRjloWmxacnF5QUF4U0Z3ZzNDSVlMZGNadHc3dW82YkM5THJKVTZzdGJsQjBzRUVIeHp3QVhVN3ZDT1V2c1ZINDAzYlFxVm5zZVVHTWx3OGZ3YStqKzQzRXpFQVJoY3JqcCtYWlh5TkdxcGxPZU1CZmhKQ1ZNdnVRVE1yYW1DYVljcWZYcDR3eVBHNFZTS01xOTd0YUJ2aGJxdC91Q0Y0cjdzaitZUWxCTmlxWlNrRXVPL0RJcFE2S09KTHMzenZPMmtRVGVqTkZGbUFjNE5zR0VMUVhrWjEyeXc3NEVINU9CTzU3QTQyR1ZiS0I1bVgiLCJtYWMiOiI4MjUzZmExMmQxNTUxMzI1MDU1ZTk2OGEzMWQ2NTQ1NzAzNTNjNGU3YjM4MTIxYWZlMGI0OTAwZjU4YmFmYmE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndUYUFBUzRrb2xSSEhLTEE2SnpxSVE9PSIsInZhbHVlIjoiSk8rVEhIL1NDQXNNNGVzRjY1WVRXME0yd1VYS1FUY3ZWa1BtNld6VFRmRVZlRm42YUxNV3JhaWJEYWhOSitXY3pxL2lad0NCblBlWEhDZXliZU45emVnNXFwcHVIL0JJVmVZemUrTGpBVHRYWFkwUTQrTW5vQ21LT3Y3cGJtVHQ2bTg2Y2M4elFxOWlZUW4yYUpoZjhSdXU3Z0NBWERkMW5wbmE1bFdyN1JHUUxxOUNaOHFiTXIzRS9ROVdwUis4WThkNXhkRmx3QVhLc09KVzZxVjlDQ1lCdjJZRGVYUW9QdFZiZURGTU1IM0JkNG5qam9ydmoybkZzenhJMUtiZkM3a1pFcVR3M1ZWRWpFNGhrT3VPblFTdDlrYzdPNUY1aWdhNGp3WHZSN1FzOUJKRld1eW5iM3hvbWxmNGRqSlJuTGQ4OVhHZWlqYmFRMW02bDM2SUtOaGhVOW9LdGlEYlZ6c1NYQ1VFNStNZFo5Q1JEcHAveTQraVdyNTk2bUI3aUdOeVFKay9UZXRrN0U5eldpMDYyYWRaWkowT1JPUmlDQlZIN0ZyQk4rajB2NGxoVmtvQTZLS0duSGdNdE5vY2pNUlpKMUFJQ2lkVnEzaWplNVNGVXpTTWlEdWJWNUV4S2ViV1RvbVhGVTJyUHhWako5dkQxaXBFdCtCamk3OXEiLCJtYWMiOiIzYWY2NTgxMDFlODdiNjkxYjk3ZWI2YTQ5M2UzYjg0YjdlNmE5M2RhZWVhZjE1Mzg4ODMwMDkxMzY3YTU5ZGY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjY1VGhUUTgvQzdMcUp0dnpXYTE0aUE9PSIsInZhbHVlIjoiREU2VTVPUG1qYzdvYXhLM2JjVDhtRnZ2TmQwbm9KeXkydlJvMjc3ajM1RURDM0NVck5LQXZ0MkNHVURPQ0tuNEd4RWN3ZE5lR0pZM3Q0WElvN1k4MWJ0QXRrZXFISWxuZkpHOFZxcG5ERGFIOC9mdTdKb1JyNnA3Qy9QL1o4ZE1HenkwRW5sVTI1TVVQcjJGdUJIRFM2anZOU1B5QU1CSXEwMk9HQW5rT3p2THVsZnByMk0rL25XbTlNQnM3RzEzZkIrMFJSNnZtZHJnUm44S3B5dXlKQWVFWHFaQUlpMWJ5MlpWUGNWdnlhVFNSb01PVUZzbzJXZitPWU5oUGhYaHV1c1dTOEliWFhVVktGd3RvRjloWmxacnF5QUF4U0Z3ZzNDSVlMZGNadHc3dW82YkM5THJKVTZzdGJsQjBzRUVIeHp3QVhVN3ZDT1V2c1ZINDAzYlFxVm5zZVVHTWx3OGZ3YStqKzQzRXpFQVJoY3JqcCtYWlh5TkdxcGxPZU1CZmhKQ1ZNdnVRVE1yYW1DYVljcWZYcDR3eVBHNFZTS01xOTd0YUJ2aGJxdC91Q0Y0cjdzaitZUWxCTmlxWlNrRXVPL0RJcFE2S09KTHMzenZPMmtRVGVqTkZGbUFjNE5zR0VMUVhrWjEyeXc3NEVINU9CTzU3QTQyR1ZiS0I1bVgiLCJtYWMiOiI4MjUzZmExMmQxNTUxMzI1MDU1ZTk2OGEzMWQ2NTQ1NzAzNTNjNGU3YjM4MTIxYWZlMGI0OTAwZjU4YmFmYmE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndUYUFBUzRrb2xSSEhLTEE2SnpxSVE9PSIsInZhbHVlIjoiSk8rVEhIL1NDQXNNNGVzRjY1WVRXME0yd1VYS1FUY3ZWa1BtNld6VFRmRVZlRm42YUxNV3JhaWJEYWhOSitXY3pxL2lad0NCblBlWEhDZXliZU45emVnNXFwcHVIL0JJVmVZemUrTGpBVHRYWFkwUTQrTW5vQ21LT3Y3cGJtVHQ2bTg2Y2M4elFxOWlZUW4yYUpoZjhSdXU3Z0NBWERkMW5wbmE1bFdyN1JHUUxxOUNaOHFiTXIzRS9ROVdwUis4WThkNXhkRmx3QVhLc09KVzZxVjlDQ1lCdjJZRGVYUW9QdFZiZURGTU1IM0JkNG5qam9ydmoybkZzenhJMUtiZkM3a1pFcVR3M1ZWRWpFNGhrT3VPblFTdDlrYzdPNUY1aWdhNGp3WHZSN1FzOUJKRld1eW5iM3hvbWxmNGRqSlJuTGQ4OVhHZWlqYmFRMW02bDM2SUtOaGhVOW9LdGlEYlZ6c1NYQ1VFNStNZFo5Q1JEcHAveTQraVdyNTk2bUI3aUdOeVFKay9UZXRrN0U5eldpMDYyYWRaWkowT1JPUmlDQlZIN0ZyQk4rajB2NGxoVmtvQTZLS0duSGdNdE5vY2pNUlpKMUFJQ2lkVnEzaWplNVNGVXpTTWlEdWJWNUV4S2ViV1RvbVhGVTJyUHhWako5dkQxaXBFdCtCamk3OXEiLCJtYWMiOiIzYWY2NTgxMDFlODdiNjkxYjk3ZWI2YTQ5M2UzYjg0YjdlNmE5M2RhZWVhZjE1Mzg4ODMwMDkxMzY3YTU5ZGY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726879170\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1503302314 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503302314\", {\"maxDepth\":0})</script>\n"}}