{"__meta": {"id": "X7428b8b22c1594c9134e6e3fd749a237", "datetime": "2025-06-08 13:34:08", "utime": **********.166254, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389646.827267, "end": **********.166283, "duration": 1.3390159606933594, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749389646.827267, "relative_start": 0, "end": 1749389647.995828, "relative_end": 1749389647.995828, "duration": 1.1685609817504883, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749389647.995853, "relative_start": 1.168586015701294, "end": **********.166286, "relative_end": 3.0994415283203125e-06, "duration": 0.17043304443359375, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45602312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00873, "accumulated_duration_str": "8.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.09151, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.918}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.120034, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.918, "width_percent": 13.975}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.139355, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 77.892, "width_percent": 22.108}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1551821611 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1551821611\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2057518378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2057518378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1268327303 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268327303\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1429930915 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389582531%7C26%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFraTJKN1BxVmVvNTlYeXNMM0ZKMkE9PSIsInZhbHVlIjoiaER5bWsxQ0F1ZThnSWxjTVdXR0l2SjIzOHRlYXR1N29tVUU2SlJmb2FGTGhYcFJTS3kxNzZmc094TTBscGU4UHNxUXhkdHJGZEhvaGlSWm1OMERUaXE1TW5ENGxGbldqY2FlSlJEeEZPVEtFdlU3enkyUHc3clR1SDYraWMrUU90bHBXZ2hKczFGdTRYSGVkNzk5cEZRU1ZQV3JLem5NbDlYN3lQeU56RC9VaHhDWlBJNG5SdWRwSTg1ZmpjeVgwejZzQ3l5OGJQZWIyNWpMUmtoTklyRUxQQVFFOUp6RGowUW1hcm5BWkNVSnZpcXNCU2M4bm1PK2EvTkxNOG1lbjdaOXJ2L21HWUw2dS9uU0w1VkdLZHQ2SWRsRm1KQitnNVBmRVJDNDJ2dERETllnOWtXNWFNZ1pucFRRU2M4Y3RwMFNxUTMxMWJvWjNsRnowbUNTRE5QOWtiVi9mSzJWRXppWks5TzEva3h6bForTjE0V3dqZzVXSWI4SkV4eWhUOVlUUEk3TE5lNjh4azJhMUtPWFhCbWROdXJ3VXhmUENXUGYzTEo0OVhVQ2FWWGRnaXJlK2tYckVDMzBkRW1mcVVFM25vSC9GNFQ0bkpzVllwbmpxdmdHQWZvaGhRbnRvaU5GellrWXFpZkhqY1JocVlabUpsbENJaXljS3ZXQTUiLCJtYWMiOiIzMmFlMzkzMmJiZDc2Y2I4ZWIzMTZiMGQ1MGU0MDYyMDdlZjMyOGExMzYyZjFlYjkyMTczZWNiOWU4NmE5NGI1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilh1azhDVjB0ckwzN0Z4SjUzcnNaemc9PSIsInZhbHVlIjoiNVRMVzhJejdWTEl0bllySEdrY2Vmc0NldEc0ZklXZW5RYkphVWJpRDFadDlLa2VBUTZCNzh4eEVCZDNURXdmN3lQZUxRc2ZvL095dE9jZTJ5SDlFMWlmR1pBUGRlN2JqdHAwVDhCMGNEeWhvd2kwcHlOWjFucm8vWDV5K0tDSHRHbVdxVFRUTUl4c3hHS1J5WWxGamJYZFpZd05QdDYyTmtKSVFaSzJpMGRCZ0MvYm10TFExUnQxNHZESm55R0ZyZHF3YWgrZGp0MlY3R0hBNjJmTlRoR3dackFhUmVZQXVkTytXL3h0QWdWMUswTjczYVk4aHBPMGQ0YzlsZTZlMWw4WVMwbWFvMlVJT1kxTjQ0YVdJdWlJZlJOL0dWYnlBcGlRQk1IK1JTaWdNWHZDc2VBQTFjMzFXSjdGQTdDTy9FSUNLekJqYlNQNmxmN3A5bHhiZGJEcTlLYjhPUUJFRDFyeHJHR2xtUnZNUE5RNlZtOXc1L1ZuVER6WkZyVXZ5YnpzUHJOQ0U0MWV3UUx2S2JHS3VWQUU3Zkg4RHAycmVrSUhZZXpOQWdhbWF6RlZZOEQ5TDZaSDhBNFV5a0c4V0tFbHdGTWsrTzh5WlhRc2FaRlV4ZU9DWjB1SjVTbFZKWHhrcjNDcnF5YUladGJwNjZqdmVmM3FGUmZmelA2blkiLCJtYWMiOiI3OGMxZWU3MTJlN2U4YTc2YzI5MGFhNDQ2ZDNlMDI0MDhkMWQxMzM0N2FiMGE4MDc0NjgzNjlkMmU4MWRjYTQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429930915\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-513729415 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-513729415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1672058869 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRYcHpyeEt6Qm5IZytSZzNZOGRvNFE9PSIsInZhbHVlIjoiSSsxdGl1MjFqZlExbVkzeHpYTW4rQUZQOEFYS2x0QkhEenpCamNKYm82M3FzWFZBbzc4b0dCenBiUGcrMzlBSVg2b1lROXQzT1c1UHZjMXB1MEp4bFBNSVNqdWtOT2tPVVFzWVEwUDlxcE5sZjdzU0xmcEhaMVBEaUxsZXVKeHZvTzZBRFdFWTlYV1Z0MEYxaW51MFlPbzRvOUxLTXN5Zk80TFFBcTJQQ2t3OGRBa3pmWXdYZ29MYlFjRDEzYVFFV0VUbTRnVEFxME04d3ZWRDJueVMxNkpBTUd3WGJIdWY1OWt2OE1iZlR6Q0xQUXhKamsrMk5Id3gwamJYOXpmalRTclVSVTdEMTJTT3hvR3I0eDIxZGo5Q1NoT3ppRDdaTWg4WVA1NEMrcXpKMXdzRUM5YnhoMWZBNnhubG5kRXY0ZzF3Y3MyT2xwbWlUdzVDSUxSRnQrank3eUZrMUJmTlJFKzJ6SHoxMGVDUFBmZ2pSTUN4U3l2OTFCYzluOHpPaXE4ZWNzY0dPcXVJZUFIdDRzUWJ5Y3lyanhLR0k4QlVHa2tUV3RsMGlBRHE1Nmh6WnVoZXFJRFVReUZlS0cxMVRkNzgzWDJSMXpwSGtmM1pKNHpqS1RFTTRrUTVwTFhMaHk1eUhSQVllVHlTd2JlOEpuaWVFVFVobFQ4c0NFVjIiLCJtYWMiOiI1NTg2YTFmOGI4MTI1MzE3YzgyOTM0YTJmNGViYmIzZTYxZDg4YzBhOWI5OWRkZDRlMDRlMDAxMzMwMjllMDY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlmaVdraVNXc3M4emh3RjlhVkw0Vmc9PSIsInZhbHVlIjoiN3dROTg4VmV5RU0rQjdESWVqMm9KcmVWN2k0NnQ2NXNHck1nWGtyRis5OXVld0pKSjhRYXg5YjZnTkNyd3hJUmhnYUNwWGdubHdaUmd6Z2pJVkpNcURwTHE2cG9pN2ZpWDlYM0RSU28zNVA5NHVOYThuUWI0dHJQLzdkaU5ldmtPbkFoN285akJ4OEkwbEJrSmdUZk9HUDBXcEllM1BBZmNMZGR0S3pZS3BhVG1ZK0dZUWZlZUFTYk5veXh4ZEJndkJGVUpWVWFaMWpPenRkcy8vVFlOcDYrNE5HUmRXR0Nja2FVbTVrUTlMWmlWSEFOckRnblhLV1UxV0UvczJpeEFPVVNVaU9CaGJGbTEyZ2ZzaGx1UGRLZ2ZibWs3ODI2MHZxYkJWWmZHVDdqeHdLUG9YTWdtSi9NS1JEZlFhTW5pd2xIalladGFOaXYySktEeURhU2tJMzI2N1g2dU1DRHJ5TWRMdXNPMjRvWDhjZ1VxS0d0bjNlY0lqUkVOS2lmZlExN3FTMHp3QjVjajZJellTdk5wVGJsTmNweW9zWHJJY3ZlUVFEbkZDNGRpdmVZTVJzaGJ2WlVGMmtqdkEwVDZZWTVOdUtRN2I4aUx3djZtQjREcFR4R2JFTkY1YVJRYWc3Tmx2OXRTRWt4TTlWODQxRXNGNmxNam1PdTdCV1ciLCJtYWMiOiJhZDUyODQ2MmMxNzRiNDJjOGZkZDU0NWUwMjc1NGZhMzMwOTlkNmY0NjEwMTQ4MjhlMDNjZWRhYjdmODJmM2FmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRYcHpyeEt6Qm5IZytSZzNZOGRvNFE9PSIsInZhbHVlIjoiSSsxdGl1MjFqZlExbVkzeHpYTW4rQUZQOEFYS2x0QkhEenpCamNKYm82M3FzWFZBbzc4b0dCenBiUGcrMzlBSVg2b1lROXQzT1c1UHZjMXB1MEp4bFBNSVNqdWtOT2tPVVFzWVEwUDlxcE5sZjdzU0xmcEhaMVBEaUxsZXVKeHZvTzZBRFdFWTlYV1Z0MEYxaW51MFlPbzRvOUxLTXN5Zk80TFFBcTJQQ2t3OGRBa3pmWXdYZ29MYlFjRDEzYVFFV0VUbTRnVEFxME04d3ZWRDJueVMxNkpBTUd3WGJIdWY1OWt2OE1iZlR6Q0xQUXhKamsrMk5Id3gwamJYOXpmalRTclVSVTdEMTJTT3hvR3I0eDIxZGo5Q1NoT3ppRDdaTWg4WVA1NEMrcXpKMXdzRUM5YnhoMWZBNnhubG5kRXY0ZzF3Y3MyT2xwbWlUdzVDSUxSRnQrank3eUZrMUJmTlJFKzJ6SHoxMGVDUFBmZ2pSTUN4U3l2OTFCYzluOHpPaXE4ZWNzY0dPcXVJZUFIdDRzUWJ5Y3lyanhLR0k4QlVHa2tUV3RsMGlBRHE1Nmh6WnVoZXFJRFVReUZlS0cxMVRkNzgzWDJSMXpwSGtmM1pKNHpqS1RFTTRrUTVwTFhMaHk1eUhSQVllVHlTd2JlOEpuaWVFVFVobFQ4c0NFVjIiLCJtYWMiOiI1NTg2YTFmOGI4MTI1MzE3YzgyOTM0YTJmNGViYmIzZTYxZDg4YzBhOWI5OWRkZDRlMDRlMDAxMzMwMjllMDY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlmaVdraVNXc3M4emh3RjlhVkw0Vmc9PSIsInZhbHVlIjoiN3dROTg4VmV5RU0rQjdESWVqMm9KcmVWN2k0NnQ2NXNHck1nWGtyRis5OXVld0pKSjhRYXg5YjZnTkNyd3hJUmhnYUNwWGdubHdaUmd6Z2pJVkpNcURwTHE2cG9pN2ZpWDlYM0RSU28zNVA5NHVOYThuUWI0dHJQLzdkaU5ldmtPbkFoN285akJ4OEkwbEJrSmdUZk9HUDBXcEllM1BBZmNMZGR0S3pZS3BhVG1ZK0dZUWZlZUFTYk5veXh4ZEJndkJGVUpWVWFaMWpPenRkcy8vVFlOcDYrNE5HUmRXR0Nja2FVbTVrUTlMWmlWSEFOckRnblhLV1UxV0UvczJpeEFPVVNVaU9CaGJGbTEyZ2ZzaGx1UGRLZ2ZibWs3ODI2MHZxYkJWWmZHVDdqeHdLUG9YTWdtSi9NS1JEZlFhTW5pd2xIalladGFOaXYySktEeURhU2tJMzI2N1g2dU1DRHJ5TWRMdXNPMjRvWDhjZ1VxS0d0bjNlY0lqUkVOS2lmZlExN3FTMHp3QjVjajZJellTdk5wVGJsTmNweW9zWHJJY3ZlUVFEbkZDNGRpdmVZTVJzaGJ2WlVGMmtqdkEwVDZZWTVOdUtRN2I4aUx3djZtQjREcFR4R2JFTkY1YVJRYWc3Tmx2OXRTRWt4TTlWODQxRXNGNmxNam1PdTdCV1ciLCJtYWMiOiJhZDUyODQ2MmMxNzRiNDJjOGZkZDU0NWUwMjc1NGZhMzMwOTlkNmY0NjEwMTQ4MjhlMDNjZWRhYjdmODJmM2FmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672058869\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}