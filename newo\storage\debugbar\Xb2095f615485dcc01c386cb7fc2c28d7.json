{"__meta": {"id": "Xb2095f615485dcc01c386cb7fc2c28d7", "datetime": "2025-06-08 13:44:08", "utime": **********.367927, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390246.717061, "end": **********.367959, "duration": 1.6508979797363281, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1749390246.717061, "relative_start": 0, "end": **********.164859, "relative_end": **********.164859, "duration": 1.4477980136871338, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.164886, "relative_start": 1.4478249549865723, "end": **********.367963, "relative_end": 4.0531158447265625e-06, "duration": 0.20307707786560059, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02387, "accumulated_duration_str": "23.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.283582, "duration": 0.02266, "duration_str": "22.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.931}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3373141, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.931, "width_percent": 5.069}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1626352862 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1626352862\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-469394994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-469394994\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1909127589 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909127589\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1457317830 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdBcHQ0VUdzbEFPaDFUV3V1OTFva0E9PSIsInZhbHVlIjoiY1RtZnNSaFV5dTFUdGlwdVdyZWZ1cnpKbkVDWFRSUVcvRzNKNy9aUnRhUndGZ3l4Y3Y1ZC8wQlBDWXdPZ1RtRDI3ZjV0V0Rrak93N0htZHlrUCtzaHd6cVFhdjA5U25YUlZRSDNVVnI0cWdTN3JJb1FlUXUrc2cwZmw5NUw0MG90cndwenNLRU1rcDlZLzl4S0c2aDVwUm5jUkplbk5UQlUrSjJia1FjN3J4OGt6bTFYbDdzbkwxMkdUQ0xjRm42TWlGTEg1ajJsM2l1clY5TE40aU50bFB6SXdNTFFZYmxjMG4vT0QxVFp6L0pQaEFOTzh3L1JENTRsbFdSZDhYS3EzR0ZINW1ROVdRYmcvOXlTUEVRV2EzYUxrelAyMTRydVFRZzFmclljWWp3K0pub3FmUkZoTUxBYThNaThIOXBtNVMrYzRsSnZRTXpnM2NyYmRqS3kra2RHKzJtUHkwR0ZXd1F3NzFTVHZ2OHBVU24rRytFUEJoaEttMHlGYy9hazNESmpFZy9QUC9rL1E2U2JFNkRkODNhM1J2dURNMXNUUC9IS3Z1b3J3Z05NMUZ5VVlROE90WFRBTmpwTnZYUFpzQ0hSemMvTkltV3hzVmM2UTBkV0MwT3FtM3JDVXpKN2VkT1JCeW5jTE9DWS9xbDZIRnpwTDdpd2IxVzhaVFAiLCJtYWMiOiJhMzNjYjg0M2M0MDk1OGFlNTc3NWQxMTdiZGYzN2UxMjE1YjQ3NzY2YTJkMzY3MGM0ZDk4NjU2YWNjMGJmOTA1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlY1NkdHV0NhVlh0RUVmS3BmMHdjWEE9PSIsInZhbHVlIjoicUVSMlc5bmh4SVFreXIrS3BCOWdRRU1wSFE5UDFrbDc5NVVkdFJ1TTBBaEszYjE5Nk1leVcvakhQKzZ6N1dtV1JVb3hqUXRZMGdJWGtMd0ltcXRORFJiZVBaTVd1WjFlN0xoMmpXTWs1WC9iZjdwY2ZhS3R5a1JwUkc4ZDhYRGpUZmhYNFJNNDlzWHNqVmh4VmpNb2ZoRnM1YkRlbDFIL05sQmdhRkxBVzNOaVgwaHVGdlJsYzlmRW1MbktueDV3WTZObWxlWmRRYmRCWFE1em5xNTJ5cUFwRkYzNFdESmxYUXEyTVZMeDd3bnlIUVhlZ2prT09NY3djdDY2ZkZFL2owcU1udGNQV2NxclZYMEk3UEcyaDJYRFlHUnYrYUlIUkFpcGJSTjFvMGo0NS9HU3pLN2Ntallvais1Y0I4WmZFS1liemluajE3dW1jZEcwSHBtUjd2Z3Y0NGxxNU1rZkhzaTAwUEtBWEdPMk5aVHRmQy9tQll5VVphUjlMN1FkRmF0Y0k0aS9Hd3cwbjhQTzZJcEwyWEphdG9FbHdUa25Yc0x6VHMzNGJYc2pTK1dYTzY1WHB4Ny9hQlVxM3NxckZEdXJtTFlhMEtHZ1dyMHpuUnFJSjk3ZXNtSjd4ZVMxd3p0SVQ5TXdCTDI5Z28vT3RXK0N6Rll0TU5PY0ViL08iLCJtYWMiOiJkNmUwNzk4Nzc0M2YxN2I0MjYwZGU5M2RmNmVjMjZiYmE3NjYxN2M5MjA0ZTNlODkwZTUyMjYyYzgzYWY5M2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457317830\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1321737611 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321737611\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1805092645 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlV3TUhoMmNSTjF0SDBkRkg1Tnh3akE9PSIsInZhbHVlIjoicHAxZnJ2bVJocEsrV0w1L1dqZXd2c2RaR0dQVENWbEtqelU0KzEyMkk1bUl4cFJLdEt4ajNhaEJ1cDhOaFlEdThpOXVBQVNLL2prT0wwY2YyS005RUJibU84Rjl3a0xOYmIyMW5YNFFJUGFKNC9aSVNUbW1ONFcrV2E1TW9KVldzbEl3aGo2Q3Zra05QS1dzUWQ4WTEzKzAyNXJ3Rm5TZFpyUGY1ZDhCbFlEaTF4VkVMTzF0MkhCeDRFMGxiblpHeUJBZlRmZ1NzSm4zRWllUFRVbWdhSmhTYXRwUktzWGc2ZDRyUVFsV3lHeGRVN2tKbVJ0eGlDWGlNWmF1U3J1VEpuTmtQRWJhQmFnc2dYMjVKSjRPdHlHYmZVS0J4WENXbDZWTVMxbEpoOWdlWkMyaGlYQzY4LzdCRGdUVVhNWldTdVJWNUxBWkhKVkhlT3BjUTJ0S0dMRXVnWjB0bDNIRnZaUjZUbnA2eFRQa0kveEs2Mmw4dkRjTzM0V014UVowbXJKaks5SG1oYUVaa2JCbU9ncU9WcC9rWGdLb3dnVFZEb3VUTml1KzV4ZDdneTJMb3ljOEt0cjJsSjBJcG45b3VYQzMraWRmTWRiM0w1cTcxVGlHZDNleHlQTUI3K3BNbjhubXFweHNsNnNHUlU2S0ZkVGlJZGxSWHFsY3k0QjUiLCJtYWMiOiI3MWQzMDk3ZjNhMzQzMTIwOGU3ZWQ4YTJlMDYwZGMwZDM0MjA0NGY1NWNkMmExY2FkNmQ2NGY5NDViODM0YzIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdBdmVYRklYNy9UREJKRDNWdGxjWkE9PSIsInZhbHVlIjoibXVsNEg1T3Y2YWdPZXJLN1F3U1lxODgrYlNybjNpa2Y4N1BKOEtmejFYbUtscTNXQTRvb0lvR0JhZFZmcWpNSUdxVEh4b09Qc2tDWUEvNlJoUEp0bjFxYi9wUDljL05EN1NOV3VkcElQcDJRY04vRndLN3ZobGdSK1NLZDlzTnl4aitnRWxTQWg3NDVoaUVjM1BmTGZCUlowUXVrNTlsRzJWbWxyemQvTkt4Q1I3YmF2ZjUxVGI4R09XMG5MVGU5N3laNUl6bHZsRlB4aWJwdFBuRWF2TjhLU1N1dWNCMnV3Qm92M2xWTUN4V0J5dGg3SkNCMXpYSTcvNDl4d2lqVHorVHc4Z1NsMWt6MVVMYm1zbUc0UWVkcTB2TmwwQUcyWloya0I0Q2JnSng1L0VFV2YrOFFVTTBRNWIzaWJDS3ptN2JNNnZuRm1XTUpmTUJwdzI0bkdYYTQwT3E0Q3g4K3l2d1dIVmh3cXg4YmZjMmJ5SjVENmVwcFRSNmY5VE5oTUtkVFhKeTZrY0JYRW9pdXQ1enZ2b3AxMGRNRVZ6VGFXWlVGOWdINW9Tcno4MTlNcVcwbHRBUGQxQlo1d0VPMkRaOExhTEhGNmZXQksvMmFsVmZ1NEJWVGZHb1J1NENvalFVWXU5a0o3dDV2N2NUSVR6QWlYR0hHNzlOT1FwTC8iLCJtYWMiOiIzZWQxNDU3NGQxM2NhYzFkZDQ5MDRhOWMxNGUyNjVlZDZkYmU4MTQ1OTk1MjE1YTA1MmRlNGRjZTU2Y2JlNzVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlV3TUhoMmNSTjF0SDBkRkg1Tnh3akE9PSIsInZhbHVlIjoicHAxZnJ2bVJocEsrV0w1L1dqZXd2c2RaR0dQVENWbEtqelU0KzEyMkk1bUl4cFJLdEt4ajNhaEJ1cDhOaFlEdThpOXVBQVNLL2prT0wwY2YyS005RUJibU84Rjl3a0xOYmIyMW5YNFFJUGFKNC9aSVNUbW1ONFcrV2E1TW9KVldzbEl3aGo2Q3Zra05QS1dzUWQ4WTEzKzAyNXJ3Rm5TZFpyUGY1ZDhCbFlEaTF4VkVMTzF0MkhCeDRFMGxiblpHeUJBZlRmZ1NzSm4zRWllUFRVbWdhSmhTYXRwUktzWGc2ZDRyUVFsV3lHeGRVN2tKbVJ0eGlDWGlNWmF1U3J1VEpuTmtQRWJhQmFnc2dYMjVKSjRPdHlHYmZVS0J4WENXbDZWTVMxbEpoOWdlWkMyaGlYQzY4LzdCRGdUVVhNWldTdVJWNUxBWkhKVkhlT3BjUTJ0S0dMRXVnWjB0bDNIRnZaUjZUbnA2eFRQa0kveEs2Mmw4dkRjTzM0V014UVowbXJKaks5SG1oYUVaa2JCbU9ncU9WcC9rWGdLb3dnVFZEb3VUTml1KzV4ZDdneTJMb3ljOEt0cjJsSjBJcG45b3VYQzMraWRmTWRiM0w1cTcxVGlHZDNleHlQTUI3K3BNbjhubXFweHNsNnNHUlU2S0ZkVGlJZGxSWHFsY3k0QjUiLCJtYWMiOiI3MWQzMDk3ZjNhMzQzMTIwOGU3ZWQ4YTJlMDYwZGMwZDM0MjA0NGY1NWNkMmExY2FkNmQ2NGY5NDViODM0YzIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdBdmVYRklYNy9UREJKRDNWdGxjWkE9PSIsInZhbHVlIjoibXVsNEg1T3Y2YWdPZXJLN1F3U1lxODgrYlNybjNpa2Y4N1BKOEtmejFYbUtscTNXQTRvb0lvR0JhZFZmcWpNSUdxVEh4b09Qc2tDWUEvNlJoUEp0bjFxYi9wUDljL05EN1NOV3VkcElQcDJRY04vRndLN3ZobGdSK1NLZDlzTnl4aitnRWxTQWg3NDVoaUVjM1BmTGZCUlowUXVrNTlsRzJWbWxyemQvTkt4Q1I3YmF2ZjUxVGI4R09XMG5MVGU5N3laNUl6bHZsRlB4aWJwdFBuRWF2TjhLU1N1dWNCMnV3Qm92M2xWTUN4V0J5dGg3SkNCMXpYSTcvNDl4d2lqVHorVHc4Z1NsMWt6MVVMYm1zbUc0UWVkcTB2TmwwQUcyWloya0I0Q2JnSng1L0VFV2YrOFFVTTBRNWIzaWJDS3ptN2JNNnZuRm1XTUpmTUJwdzI0bkdYYTQwT3E0Q3g4K3l2d1dIVmh3cXg4YmZjMmJ5SjVENmVwcFRSNmY5VE5oTUtkVFhKeTZrY0JYRW9pdXQ1enZ2b3AxMGRNRVZ6VGFXWlVGOWdINW9Tcno4MTlNcVcwbHRBUGQxQlo1d0VPMkRaOExhTEhGNmZXQksvMmFsVmZ1NEJWVGZHb1J1NENvalFVWXU5a0o3dDV2N2NUSVR6QWlYR0hHNzlOT1FwTC8iLCJtYWMiOiIzZWQxNDU3NGQxM2NhYzFkZDQ5MDRhOWMxNGUyNjVlZDZkYmU4MTQ1OTk1MjE1YTA1MmRlNGRjZTU2Y2JlNzVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805092645\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1206062132 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206062132\", {\"maxDepth\":0})</script>\n"}}