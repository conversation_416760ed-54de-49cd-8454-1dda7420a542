{"__meta": {"id": "X4e09f8370f5d6793d44253eea07141ae", "datetime": "2025-06-08 13:27:17", "utime": **********.184087, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389235.816902, "end": **********.184117, "duration": 1.3672151565551758, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749389235.816902, "relative_start": 0, "end": **********.008569, "relative_end": **********.008569, "duration": 1.191667079925537, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.008589, "relative_start": 1.1916871070861816, "end": **********.184121, "relative_end": 3.814697265625e-06, "duration": 0.17553186416625977, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45587448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02935, "accumulated_duration_str": "29.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.086948, "duration": 0.02723, "duration_str": "27.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.777}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.139579, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.777, "width_percent": 3.646}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.158057, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.422, "width_percent": 3.578}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1052339338 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1052339338\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1232324615 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232324615\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1351246148 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389231048%7C20%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNYdmI5TEdxVGNyT2VGVUdBU0dZM3c9PSIsInZhbHVlIjoiNXZGOWozeWsyS3QrbTVBS2xpT3RjYUlXdFFZR1g0ZkJlVjJMSlB5UzU5bFZrVUtNV2R3cU82T3pMYmxpZGUrY2dNTWVEK0VrbE0zZGFmR01xM0ppd0IrQnV5U2ViNDNuZUxOb1Y3L1IxbjRsejF3NnZmbENhRGVWNnduKzZrRitzV2poZW1aM3d5YmFoRmpkUlhKbnI5Z004R2s3NW1OemVTNFR2VktBaXVxelZnMFRJd21ramhvNUZBZmZscWd1Nyt2Y3dEd1FGZGJ0bVptSUxVeUpTZ3VFU0xqd21RTHEyazROdmFxSnAzbXFOZGxhNUVIb2pJdGhxc2ZSRjhrQ1NDdzF3aWxHU2trSWMwYzZhOXBaYUkyZDdkRnVLUkhKM3NrRURIMlM1d1JsTVFwVHUvWnlnVVIvQnEwTEQ4Sm1HckVYWFIrTHJmUEM3ZFlRWmgyNUc4TlV2NlQ5akZlckRncDBtL3A4cGcwRFJMdVNWY0c2cDdwWUE5S2UyaDJMYmR4N1U0amcvUEw4UExUeWdFWURSWWxPNVlrTHRoVmU1ZVVkbExRQWxpN1JFQWpob2tZbXR0cXZENllHbnlZcHFiRlJTME1WZ1pqSG5HNXNReklzQnZWZ3UveDdhcG8yb0J4bHVGODNqK3p0cUhnUTVxaUpZZGZ3UTNBblJkdkMiLCJtYWMiOiJkZTc5NjBjZWE2ZTc3OTU2NTk0M2I3YWFjYzBmMDc4MDNmNjk0NWJlZTczZGYyNmZkNDUwMGVmZGNjOTY0YzI3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9qTlBmRm5hTHFPdktJc1ZnQU5KYVE9PSIsInZhbHVlIjoidFZaSm54Q3ZjbWU2WU05T04velRTTVBWVklick1Ra1A4LzNxeHhMUnFDU2hiZk5Mam5OYUZIbTlWTkhrdHNsOFJydVA3bW8zZWlkbTc5QXlxRzhiN3lGM2pmcm5SQmd0ZlZqSjA1M3kvS0p0bnNqNG44UnY5RkJFcU01bEpiQzZVTXI1OHRHSUx3NWs3Ym9FRkwxck1sZC8zS1BiNjN6b3MydzdKcnQ3VXB6VCtBMUU5OUpWSUQzZVdSZTZjNHZvSlJMSEpnbUJSZVFyM0hhczZGcHc1R2ZZOGR1dUh6WXJ3SSswVWV0YVEzTnJNbWNFanNYRzNLRmdrZWFLVEdBWFcyQlkreCsyNXE1c1ZDRTZyTm9YUmNKUFU4eEpzREVjVFZqNEVWRWh5aGZHalFUNXhTdmJmOHJDVGVmVUVYcUtFZFF5cVMySjc4Wm1HZTIzVDNOdXkvdzNheWIwSU5NY25LTUs2L2NJZTRpNGJFdk82eFQxNkFOSWdOc2UrNmpPODkxODhEOFNwQ1dZaktmRHM1ZThaRHBISll6UWhaNDNvbmpadnYxdGlIZEtvQU1pUE9CMXZqdnVveU5GM2Q4Mk1HZG5kR0poYVhTTlFJZEhUNGRJSzhJQ0p5Y05lSVlEaExDN1U1RXhQOEtYbFIyK2IvelBJdEE3Q2RFSXNtcGEiLCJtYWMiOiI4MmVmNzAzNTI4ZTRiZDE1OTA4NjBhOTk0YmUyYjk0NzQwMGFjODJhMWY1ZTY1ZjMwOTI3OGYyYjYwYWYxZTAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1351246148\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1300093544 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300093544\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1969734323 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:27:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjQyam1WejJDRlB0UzJxL29HejhSblE9PSIsInZhbHVlIjoid2M3L1hobm1ZeG51Z1MzV3dhN2M1RER5UEQ2ZzRsMGNMT1VJRU13bk16WS8zOERqM3E4SFdvTW5yYzdsK1g0dnU3NFBFbm50bWRSUVJheE9hUDRaZGdGYWN2NnkrcElrL1M3MWR1b080Ukg2K0RHZDJ3YWp6MkZMSWFYdFZjdzROL3ROR1IwcTFrcU81Z2ZFYzBUQ01HdFl0WFdDLzhLeHdQeTBpeWVmTnU5eklCbkc3U0kxU3Jyc25McHk4ZjlEVVNVb0x2dUdXZk4vbmg1cGl1cVpKai9BcmhKNTV2dE5GMmpCNXpTYnZFV0g5L0FOdW4wMmRMSG93T0ZwQUZ4bnYwb1Z5a3p1cEJiNlhORmRWb3pPM05DT204Qzl1OUQrNFBDZUlPcmtCcllkVzFwTDJoTHozdENiV2xyZkhrMXBHMFRBUFhuWVpNckpuc0lFcE9ZeFZTL1NCQWJXYnZtb1dVQWFUUDhLQ2dITEY5TGQzR1Q5M2c0N0d2cXZheDVZSUJpdUlEcWwzQktJRnNHWDFiWm5SL01zM1NjODhKY21kOWtBa2oxdFEwTDNrL1BnUnZ6RUdMbitQdHVQRHR1ak9ISk9XczljMUU5bWFlSDgxYnVLclpUTm4vRGZxVitoa2FiZlo0QXhXZHJFRlRWNnIyZHhqUnZURm1jeHhGL3AiLCJtYWMiOiIxM2U0ODYwMjkyOWExYTI3OTE1ZDE5NDc4YjA0YzE4YWJlODdkZGUxZWZkNDViM2YxNTY4Y2FmNTU3YjFkMWJiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllIVW5VYU9tbXFBT0dyMUZ6TW1waWc9PSIsInZhbHVlIjoiaDNoVzdKL3ZsTlNiRzJtNDBTa0hXR2RPZzRHNDFDWEhzZEhiQURMMEdNTFR2RU5UTGliRDQ2ckFQbjB6WVZGcmN3QVExYUlYRHE0N0szNDlrS0lKTW9acm5kNS82ckJYWjlCRXcwWDlSYmZwU3J3OWFkd2hUUFlYVXRmV1JyTFliOGlxbWdCWkpqU2FUWkdWQ1lvYjVyV2pzUlpLV0hDQkMxTGdJeTJLMU1uR0wzZzZpdUk3YkF0VXhtQWNsdTR4OEN0U3VXS0x0Ny9zc0ZZMmVGQUd4MGFoc3VNaVJqWU5MSUh3aFdyaHdqYzRvNEFiQ2Z2NWRyTFB1anAwK2pEYzBPNVdEcVEyMzdISXJibld0MnFIWmhlSmpNcWZxOEJ0NGtNb2phQXZ6SVpvUldlSUR6aXN3UXpjL0NLMlJOMTQ3UVZiNnBmK0VwL0Zua3VZNDEwUFFZRnA2VHZMLyt6L0FQbEJaQjY0OGtPbVd5N3JUS0hyZmN6eEpnbnlRZGdUdSt5ZjkyOUFYUmV1UGgrR2ZWMkNlSEVWbnMrbWJxZlgwVlUyL3Jqb3VkTGZjc1pYNWlHQzJiQjAxVHlUczBuZGRJMFFpVUVob3h4UktTdFpScWxkMHIybjVGL1Z4a1RHUlJQYTBINjNhRFl5Nmhsa1pGKy9iTmZkMVFlNHlxR3EiLCJtYWMiOiI3MDQ5Y2NmMDlmZjZjODU5ZjdjMzc5ZTdiNDcwZjJiMTFhNGE3Zjc4NjBhYmRkMzRjOTZkNTM5YzA5YjA1OWM3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:27:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjQyam1WejJDRlB0UzJxL29HejhSblE9PSIsInZhbHVlIjoid2M3L1hobm1ZeG51Z1MzV3dhN2M1RER5UEQ2ZzRsMGNMT1VJRU13bk16WS8zOERqM3E4SFdvTW5yYzdsK1g0dnU3NFBFbm50bWRSUVJheE9hUDRaZGdGYWN2NnkrcElrL1M3MWR1b080Ukg2K0RHZDJ3YWp6MkZMSWFYdFZjdzROL3ROR1IwcTFrcU81Z2ZFYzBUQ01HdFl0WFdDLzhLeHdQeTBpeWVmTnU5eklCbkc3U0kxU3Jyc25McHk4ZjlEVVNVb0x2dUdXZk4vbmg1cGl1cVpKai9BcmhKNTV2dE5GMmpCNXpTYnZFV0g5L0FOdW4wMmRMSG93T0ZwQUZ4bnYwb1Z5a3p1cEJiNlhORmRWb3pPM05DT204Qzl1OUQrNFBDZUlPcmtCcllkVzFwTDJoTHozdENiV2xyZkhrMXBHMFRBUFhuWVpNckpuc0lFcE9ZeFZTL1NCQWJXYnZtb1dVQWFUUDhLQ2dITEY5TGQzR1Q5M2c0N0d2cXZheDVZSUJpdUlEcWwzQktJRnNHWDFiWm5SL01zM1NjODhKY21kOWtBa2oxdFEwTDNrL1BnUnZ6RUdMbitQdHVQRHR1ak9ISk9XczljMUU5bWFlSDgxYnVLclpUTm4vRGZxVitoa2FiZlo0QXhXZHJFRlRWNnIyZHhqUnZURm1jeHhGL3AiLCJtYWMiOiIxM2U0ODYwMjkyOWExYTI3OTE1ZDE5NDc4YjA0YzE4YWJlODdkZGUxZWZkNDViM2YxNTY4Y2FmNTU3YjFkMWJiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllIVW5VYU9tbXFBT0dyMUZ6TW1waWc9PSIsInZhbHVlIjoiaDNoVzdKL3ZsTlNiRzJtNDBTa0hXR2RPZzRHNDFDWEhzZEhiQURMMEdNTFR2RU5UTGliRDQ2ckFQbjB6WVZGcmN3QVExYUlYRHE0N0szNDlrS0lKTW9acm5kNS82ckJYWjlCRXcwWDlSYmZwU3J3OWFkd2hUUFlYVXRmV1JyTFliOGlxbWdCWkpqU2FUWkdWQ1lvYjVyV2pzUlpLV0hDQkMxTGdJeTJLMU1uR0wzZzZpdUk3YkF0VXhtQWNsdTR4OEN0U3VXS0x0Ny9zc0ZZMmVGQUd4MGFoc3VNaVJqWU5MSUh3aFdyaHdqYzRvNEFiQ2Z2NWRyTFB1anAwK2pEYzBPNVdEcVEyMzdISXJibld0MnFIWmhlSmpNcWZxOEJ0NGtNb2phQXZ6SVpvUldlSUR6aXN3UXpjL0NLMlJOMTQ3UVZiNnBmK0VwL0Zua3VZNDEwUFFZRnA2VHZMLyt6L0FQbEJaQjY0OGtPbVd5N3JUS0hyZmN6eEpnbnlRZGdUdSt5ZjkyOUFYUmV1UGgrR2ZWMkNlSEVWbnMrbWJxZlgwVlUyL3Jqb3VkTGZjc1pYNWlHQzJiQjAxVHlUczBuZGRJMFFpVUVob3h4UktTdFpScWxkMHIybjVGL1Z4a1RHUlJQYTBINjNhRFl5Nmhsa1pGKy9iTmZkMVFlNHlxR3EiLCJtYWMiOiI3MDQ5Y2NmMDlmZjZjODU5ZjdjMzc5ZTdiNDcwZjJiMTFhNGE3Zjc4NjBhYmRkMzRjOTZkNTM5YzA5YjA1OWM3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:27:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969734323\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}