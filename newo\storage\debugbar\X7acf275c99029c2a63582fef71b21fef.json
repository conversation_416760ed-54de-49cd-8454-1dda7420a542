{"__meta": {"id": "X7acf275c99029c2a63582fef71b21fef", "datetime": "2025-06-08 13:04:53", "utime": **********.999818, "method": "GET", "uri": "/pos/9/thermal/print", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 77, "messages": [{"message": "[13:04:53] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": **********.926793, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.927861, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.928231, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.928635, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.928988, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.929362, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.929758, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.930146, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.930528, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.930816, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931108, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931395, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931697, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.931972, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932255, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932525, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.932803, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933093, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933374, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933654, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.933926, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934207, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934479, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.934767, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935039, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935334, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935619, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.935901, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936173, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.936461, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93679, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937227, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.937544, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.93783, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938103, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938386, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938658, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.938939, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939214, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939502, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 89.00000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.939908, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 92.40000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.940235, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 93.80000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.940509, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 96.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.940791, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 97.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.941064, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 100.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.941374, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 102.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.941657, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 105.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.941937, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 105.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.942211, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 108.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.942487, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 111.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.942769, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 114.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.94305, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 116.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.94333, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.943619, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 121.40000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.9439, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 124.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.944188, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 125.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.944468, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 127.20000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.944748, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 128.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.945093, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.945387, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 132.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.945663, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 136.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.945943, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 138.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.946218, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 141.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.946498, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 141.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.946769, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 145.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.947048, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 146.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.947328, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 151.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.94761, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 153.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.94788, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 156.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.948159, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 156.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.948431, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 158.40000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.948709, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.94899, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.94927, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.94955, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 160.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.949826, "xdebug_link": null, "collector": "log"}, {"message": "[13:04:53] LOG.warning: Implicit conversion from float 159.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.950099, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749387892.44872, "end": 1749387894.000036, "duration": 1.5513160228729248, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1749387892.44872, "relative_start": 0, "end": **********.64234, "relative_end": **********.64234, "duration": 1.193619966506958, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.64238, "relative_start": 1.193660020828247, "end": 1749387894.000039, "relative_end": 3.0994415283203125e-06, "duration": 0.35765910148620605, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52251040, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.thermal_print_clean", "param_count": null, "params": [], "start": **********.906618, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/thermal_print_clean.blade.phppos.thermal_print_clean", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fthermal_print_clean.blade.php&line=1", "ajax": false, "filename": "thermal_print_clean.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.thermal_print_clean"}]}, "route": {"uri": "GET pos/{id}/thermal/print", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@thermalPrint", "namespace": null, "prefix": "", "where": [], "as": "pos.thermal.print", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1680\" onclick=\"\">app/Http/Controllers/PosController.php:1680-1739</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03326, "accumulated_duration_str": "33.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.749493, "duration": 0.0213, "duration_str": "21.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.041}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7976708, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.041, "width_percent": 3.818}, {"sql": "select * from `pos` where `pos`.`id` = '9' limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.807334, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1682", "source": "app/Http/Controllers/PosController.php:1682", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1682", "ajax": false, "filename": "PosController.php", "line": "1682"}, "connection": "ty", "start_percent": 67.859, "width_percent": 4.42}, {"sql": "select * from `customers` where `customers`.`id` in (6)", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1682}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8214939, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1682", "source": "app/Http/Controllers/PosController.php:1682", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1682", "ajax": false, "filename": "PosController.php", "line": "1682"}, "connection": "ty", "start_percent": 72.279, "width_percent": 5.442}, {"sql": "select * from `warehouses` where `warehouses`.`id` in (8)", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1682}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.829669, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1682", "source": "app/Http/Controllers/PosController.php:1682", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1682", "ajax": false, "filename": "PosController.php", "line": "1682"}, "connection": "ty", "start_percent": 77.721, "width_percent": 4.57}, {"sql": "select * from `pos_products` where `pos_products`.`pos_id` in (9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1682}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8377419, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1682", "source": "app/Http/Controllers/PosController.php:1682", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1682", "ajax": false, "filename": "PosController.php", "line": "1682"}, "connection": "ty", "start_percent": 82.291, "width_percent": 4.39}, {"sql": "select * from `product_services` where `product_services`.`id` in (3, 5)", "type": "query", "params": [], "bindings": ["3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1682}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.84675, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1682", "source": "app/Http/Controllers/PosController.php:1682", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1682", "ajax": false, "filename": "PosController.php", "line": "1682"}, "connection": "ty", "start_percent": 86.681, "width_percent": 4.75}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1708}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8803, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 91.431, "width_percent": 4.059}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Pos.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Models/Pos.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Pos.php", "line": 144}, {"index": 21, "namespace": "view", "name": "pos.thermal_print_clean", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/thermal_print_clean.blade.php", "line": 269}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.951946, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "ty", "start_percent": 95.49, "width_percent": 4.51}]}, "models": {"data": {"App\\Models\\PosProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPosProduct.php&line=1", "ajax": false, "filename": "PosProduct.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/9/thermal/print\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos/9/thermal/print", "status_code": "<pre class=sf-dump id=sf-dump-959539223 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-959539223\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2088531460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2088531460\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1644367890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1644367890\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFRckxiK21ncHY0ZlU5REpiQ2pEbHc9PSIsInZhbHVlIjoiSy9VaFZzdU54QUlRbjlxdVRUMFd4WnZNdFV4MTczZkxEMHhnTHI3VUJIdEVqeHBMa3pvVUZaVmdOWmlTZEpHL3Q1ZkhIem5mQk5WTm9oYUdTcFNCQzVjTjhISUFlc21QeEU3cHVPdFo2OG1SeVptM0d3MFBFcS9QNGlwbzN4SGJjeEZ4aE9EY0lKT1N4Qi8yZlE3SXZTOUQ0N2lwdHpoU3p4Nk9QdzhtSkJjQ0NxUVNkUzg0YUk5VHdBNktlZHhYSUlCR0JzZGcvdEhOdWpyMDFOR3Y1bGlVOXhtU2pxNDdweHlxVEtQSDcrMHFGQTdjcVYrN1p3ODdPelhyMmhVQWxZdzBpSzh0TzNXRUx4YUxiNmd6dHAwS2krNGtBZXN3clZyc1dxK3NNT0prQVhGbEVOd0Y0Z1M4Y0Z1U0tIQW16VDFLeVdaVEFmTEdNZjVaSjJVdGx3UmJZRkxsSXFsOXJHRXlrRjNreXFsbmUzMnRoNFBPa010WG54TUk5Q3VuSVlUMXJwYjRCTUhSYjhMWGljcmlwTFNTcmRackFnKzQrT0VMc1N2THZmKzZUcHljZ2ZHTkNhVHZ6Sk4wc01aMW9CM3A1NFAwV1ZPR0gxaXl0eG5VcHJUTGJrTThUMVh5djRGeXpMQnFTbTlZQzI3emZaYjhWNXRUNjBvaG9sMHYiLCJtYWMiOiI5ZDQwYTJjYWNlNmZlMDFlN2NmYjE5YTVhODAxNTE4NWUwMzRlYTUzZWJkMTg1NmVlMGQ3OWJiMzM3MTcwMjE4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdRRVhnUUVCMzR0dkgySUhHeDF6Tnc9PSIsInZhbHVlIjoieGJjUkJWUVdDSGJLTkx3MzV3NVcrMWx5Nk54eXhjclJSeXZ3Um94Tmc5UkNMend0Rm56MUVQRHYxRTRWeGc1ME1qeUFVQjZvWERucEIwbHA3SE9hTHJxSFdaNGxZUy9Yc2V3YTAvNHI0RU9FZGJhTHRySzZGblpTNGJFZ2ZSdHVudXU3a2pNSEJ2TWFvb3JuK2R4UEYyNnUvM1pFdEZ5TDIxN1VVQzB0Ny9uL29CTWtIYVZldWM4MWVNMDJrVE41eFpHSThlR0s1TDE0cFR3NTJGRG4xdmtSaE1pbS9pZFBaOHNiTGhCbGZuYXN6Rm9kVGliaUNZQ3dBS2xWcTN2RWtoQlVMQTcyQ1crVHAvMXphMWF4aG5FdlVLRnFtQWxYK3NhT3NNb0hFOWxNZjZzVElnaElxMjRGOTV0STFRQ2lScTlVZG4vYkhGemgwZHA2NHJkTTYwZnpYV0p0MUxjUzhNeFNLSmp5QUQ3elBxK3pKd2M1R3dNelp6aFU2aFFKQ0pzdkhNUjNZRUtseWNtYlMxNTJoMjRRSFNRUisvWEJEZ0N5aElyRUx2UzlrUnFxcmxZYjNMZy8wbUZCQUtlcWs4TndnUzNpZjFodzdGeFIvQkRKaVhmQXNlbkp0RFRPZEhVcjhJTUxFWkEvc2hGLzh1QUVCSndBOTVFM01Obm4iLCJtYWMiOiI1M2E0ZTAyMDcwYWE1OTZlNzIxZWE5YmRhMjE0OGI4NmRjZDE1NWFjMDY3NDA3ZGJmM2YzNTkxYjZjMmEwM2U0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1669319440 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:04:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpBSDVhYWk1RW9lRWw4TmczUTBzc2c9PSIsInZhbHVlIjoicU1CU29JS3dPcmVZNWhoZ01BYWtLbGk2MEpMM0JTNW5RR3pvVXMvem4xOS9ncFY3YUtoQjJQMEVBOXhBdWlEZVVYdEk1S09MeHBPTXBiRUs1bzFZNlhhYWZYdDhOdUVZTFk5U1QrT1RpTU5ORnA1Nzk3azZWQXdkaTg0RnFnRWFSSStrc0VEaFJEMlZLQ1liTmNFdnJEelN2UlVCTkNTNHBoY1QzOHVFZjVFU0RSeGFsSGhFZk4zcTR4R3FmeU4yTkNmbE9PQkZqZzZ3RFJqK2lRRkh0OENwYW9jSkhLRXM4VlRycTJxbkRlY1cxYnZwdmd2aXZFWHozNHhqcW9SZ3ltOTlIYWhBVWw2TmpVZ3dZbWVMSjJNQkdGSHIwS0dGNzc2aW1vaTF3MW5UY1N3eURFaUFrbVpIN2Y3clJSOHl1WVBVaEc3VnNyNHdhMyt5dC9mT2NYRkEvcWpmNkY0ZXlvUzJhKzF6RDdTVUEweGg3dVNvMENDc1M5czJXRGYxQlJTNFJ4bEo3aWppZW9ZQXA4ZFBtYjJ4emRGbWR5aGpyWmMyTTJ1U1U4TEpoaDh0Qll3YktHMjVJemNNaG00dXgrY0xZbnptT21xSDh3SkIxS2h4REhBRk1yVFVZLzFDQ2VpUFUrRk1Nb0R2RjBiUmJ0dDltUmZkeWNMZjlYZ2EiLCJtYWMiOiI2ZTI0YWRhYTU0YjdmZDQzZThhZjExYjA0MmExZGJmMTliZGZjMjkxMWJmYTNiNWRlNzE3NjBjMzVmYTkyZWIwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:53 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFKMDFxUHBkZ0ZPZ1B0TGdVQUNzdHc9PSIsInZhbHVlIjoicC9UTmdHTWpyWmw4N1RKKzBSTHFRUGxBa0hmbjFFcnh5QkJ0cW5sZnNZTTdkckpXVGpTY296VGdEUTFFUUVCOWZYU3ZrdElpamtTMm9wQktqUjJMSGM3c2NhdC9ZRHVrS2xKMmE0ZW5PWHhQUUFLN0Q2YVg4ZWRScjhLUDhQb0hzbDNobFdDSmZDK0llaTJHVHA0Z052T21qUURuNW1LcGFyNC9uZUhrM2s0ays1MitaUGFicm1YR2ZpTzNJYXQ0Ym1NZXIvNFpwVjBmV0JKTHkwQ3BtS3JqbXBGRVJVMnE1VktjOEd4OGlPZFNkN3plck9HamdVa1dIRHNDWFNNNUhOS0RybXZ6dWY5WTQrMnZmNTd5NW1HR3p0MXZaRzVQcTY2MzZLZ2V3VVRqRlk2OUdhaW9YMUtzM1J3SWw1TEtTOEgxWEJLR21qQnU2WGdsOVNLMkJXVkpMeWlya2VhSk5vQkdLSGtDSkRIV1gwTHEwSTRvcE8vZmdCdlREaW5BQXJBbVdPSVp4SEQ2eEE5RkdTOW5NMWV1L0t5SEYrTjRBd1VMbmcwVC9DOVlpeEJCM3FOeXJ0OW9TVkxlOUdIRjZzK1ZtbW1HUm1VZm9vT3dNTVRPaURDWXZlZzJvWEN2QW00anhkYUNKUEVLdWVqcXpyQWRHNFRScWRwakNHVUsiLCJtYWMiOiJjMzFlMGFjZTUwY2M3NDg0ZDQwNTVlOGJjYWJhOGJlZjVjZGJlYmM5MWMwZGNmZGFjZjJiNjlhNGFiNmIxMmFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:53 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpBSDVhYWk1RW9lRWw4TmczUTBzc2c9PSIsInZhbHVlIjoicU1CU29JS3dPcmVZNWhoZ01BYWtLbGk2MEpMM0JTNW5RR3pvVXMvem4xOS9ncFY3YUtoQjJQMEVBOXhBdWlEZVVYdEk1S09MeHBPTXBiRUs1bzFZNlhhYWZYdDhOdUVZTFk5U1QrT1RpTU5ORnA1Nzk3azZWQXdkaTg0RnFnRWFSSStrc0VEaFJEMlZLQ1liTmNFdnJEelN2UlVCTkNTNHBoY1QzOHVFZjVFU0RSeGFsSGhFZk4zcTR4R3FmeU4yTkNmbE9PQkZqZzZ3RFJqK2lRRkh0OENwYW9jSkhLRXM4VlRycTJxbkRlY1cxYnZwdmd2aXZFWHozNHhqcW9SZ3ltOTlIYWhBVWw2TmpVZ3dZbWVMSjJNQkdGSHIwS0dGNzc2aW1vaTF3MW5UY1N3eURFaUFrbVpIN2Y3clJSOHl1WVBVaEc3VnNyNHdhMyt5dC9mT2NYRkEvcWpmNkY0ZXlvUzJhKzF6RDdTVUEweGg3dVNvMENDc1M5czJXRGYxQlJTNFJ4bEo3aWppZW9ZQXA4ZFBtYjJ4emRGbWR5aGpyWmMyTTJ1U1U4TEpoaDh0Qll3YktHMjVJemNNaG00dXgrY0xZbnptT21xSDh3SkIxS2h4REhBRk1yVFVZLzFDQ2VpUFUrRk1Nb0R2RjBiUmJ0dDltUmZkeWNMZjlYZ2EiLCJtYWMiOiI2ZTI0YWRhYTU0YjdmZDQzZThhZjExYjA0MmExZGJmMTliZGZjMjkxMWJmYTNiNWRlNzE3NjBjMzVmYTkyZWIwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFKMDFxUHBkZ0ZPZ1B0TGdVQUNzdHc9PSIsInZhbHVlIjoicC9UTmdHTWpyWmw4N1RKKzBSTHFRUGxBa0hmbjFFcnh5QkJ0cW5sZnNZTTdkckpXVGpTY296VGdEUTFFUUVCOWZYU3ZrdElpamtTMm9wQktqUjJMSGM3c2NhdC9ZRHVrS2xKMmE0ZW5PWHhQUUFLN0Q2YVg4ZWRScjhLUDhQb0hzbDNobFdDSmZDK0llaTJHVHA0Z052T21qUURuNW1LcGFyNC9uZUhrM2s0ays1MitaUGFicm1YR2ZpTzNJYXQ0Ym1NZXIvNFpwVjBmV0JKTHkwQ3BtS3JqbXBGRVJVMnE1VktjOEd4OGlPZFNkN3plck9HamdVa1dIRHNDWFNNNUhOS0RybXZ6dWY5WTQrMnZmNTd5NW1HR3p0MXZaRzVQcTY2MzZLZ2V3VVRqRlk2OUdhaW9YMUtzM1J3SWw1TEtTOEgxWEJLR21qQnU2WGdsOVNLMkJXVkpMeWlya2VhSk5vQkdLSGtDSkRIV1gwTHEwSTRvcE8vZmdCdlREaW5BQXJBbVdPSVp4SEQ2eEE5RkdTOW5NMWV1L0t5SEYrTjRBd1VMbmcwVC9DOVlpeEJCM3FOeXJ0OW9TVkxlOUdIRjZzK1ZtbW1HUm1VZm9vT3dNTVRPaURDWXZlZzJvWEN2QW00anhkYUNKUEVLdWVqcXpyQWRHNFRScWRwakNHVUsiLCJtYWMiOiJjMzFlMGFjZTUwY2M3NDg0ZDQwNTVlOGJjYWJhOGJlZjVjZGJlYmM5MWMwZGNmZGFjZjJiNjlhNGFiNmIxMmFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669319440\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1894495713 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost/pos/9/thermal/print</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894495713\", {\"maxDepth\":0})</script>\n"}}