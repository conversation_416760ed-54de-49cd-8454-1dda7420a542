{"__meta": {"id": "X639e4816335d937a7311832353171b05", "datetime": "2025-06-08 14:57:03", "utime": **********.753165, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394622.99332, "end": **********.753187, "duration": 0.7598669528961182, "duration_str": "760ms", "measures": [{"label": "Booting", "start": 1749394622.99332, "relative_start": 0, "end": **********.645776, "relative_end": **********.645776, "duration": 0.6524560451507568, "duration_str": "652ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.645792, "relative_start": 0.6524720191955566, "end": **********.75319, "relative_end": 3.0994415283203125e-06, "duration": 0.10739803314208984, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01621, "accumulated_duration_str": "16.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.70139, "duration": 0.01511, "duration_str": "15.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.214}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.736757, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.214, "width_percent": 6.786}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-789805752 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-789805752\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-634683265 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-634683265\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-964238612 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964238612\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-278058658 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394379618%7C66%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpDYi9Dc2pETG9nYnk5bjlSdnl2T0E9PSIsInZhbHVlIjoiK3Ztc2lXZUx5cWVFeTJrZUcwRWZGdmpMbkk3RUlZcDFkcFRLRXpoR2hyMFdReUtOSE1mQlZKb0RPaEJpcFF4Qk9sNnNTSS9vL0NwZjVkNmJNK3JpSVc5dk5tTWFPWXFWRC8xaGJFVjE3ajk2dnNlWWFJTWFmekFoU1plWTRUZU1PU1g3aUU0TXBjb3o5TmZMMjlaV3c2K2pjckc3OWdRZHVMTHc3OS8wdUtTbnZTdHcxRmFESWIzZG5wNktOaWR6Y0t2c2hudWNxZEFEeUtCQXFIMWIwUXhsR1p5R2FETExtL0gyM0w2bzh6NmQyUmU1WmIvRGdOTnY1NkJpVlhmQUNlSXJPMkVsMitHdi83RmM1YUNnWTA0WnJSMXVEV3BMYWllY0lXL2RBT1Y0eXdsc29mZVM2VHNFZTluUnkzT1REMVgvcEdYMFpPNS9QOWVMMkh2VCtaRC9uZVNMaitucVdMVnhSR3Q2cXlhNHh5VndiL1o3MFVWaWtsblRIREJUSEFiZFFmOXJRNFRXU280RTVIOWJWKy9BT2o4Tm96Rmp6b3NpdHVjRjNTb21wNjJrVHhNT3JGckxwSVk4NXA5aU1OUTA3YUF4S3NWTnZoL0RSazQ1d3JoRHBzdGM4R0lvRmRRWnJveUxRS2hWR3dxNURjS0RjdFNuZmdudHZ5MUYiLCJtYWMiOiIyYTQ2NmI2NWY3ODNjMzRlYTIwYzA4MjE0YjdjZjk0YjM3MmM3MmIzMjkzNTUwYzRhOWJmMTk0MjZiMzFkNzA5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjF5WlgxbUd2WFZWU3FncnpHNmYyS1E9PSIsInZhbHVlIjoib2RKejN6c0hBR3JtNy9SYnU4bDB5RkE5MmdVNmNHa1grSjM5MFkyaENwajBRcjU1eG5ja0JmQWxici8xYVVtT2t5TWpQTlZ5bXdHcjUzcnc1aWZNNjB2ZEtEWEUrcC9SR0wxNDlBd2pvS3d2UStaWnpPZWIxZ1F4MTRrZTNjTm1lc1VqRlk2aFlCaUJPNFU4bmk4WmRQRUNhanZmYkNtcVRQNnFiUTBtaTZ0V3UwbjdETE1xMUtOa3Y2N1VTKzRxSG80SFJaWHNGSkh1ZC91bFA0Q3hUcS9iMWFaV01VR3hMOGFQOGp6YzY2by9QZ3Jjd3VvaW5JdkN1bkgycC9hRjJ1STQzYjNsdXpSSjZRdkV4NXQ0Y090YUZGYXYyUWlpV2ZsTGM5N1NFVXZtYWdzbHZpYmxBR21iS0xsblBwV0YxNzhpQ3orMjNFVlVOY3YzMVFJMHZpU3dyM2Ftd21WSVNBY0docE1rditnU1g2c3BWRHR4aERSWnJUbVdJaUYwcVY4WEpCaWRucVFRNVJmdjNtcHdUd3ExdmlYclpLU0htL1k2dVVPQWt1TDlLajNxcVh6T09tWlRNN0N4Z0pnQXl6N2FpODJ0TmwwR1h0Qkgwei80OXhkb0VFVDh6YzBvS2sxS3c3TEN2Ni9CUW1EWVh5OXI5N21KNVZwSUthU2YiLCJtYWMiOiI2ZDgxNGE2YWE4ZGVmN2RlZWJjMTI5NzRlNGE3M2QyZTVmNTcxOGVhMTYwNzRhNDE5NDk2N2M5MjM0ODNkZjA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278058658\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-467172141 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467172141\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-815429232 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFxdnZmd2xFS1ZWdTRiakQ1clV2NHc9PSIsInZhbHVlIjoiWVQvS2U5cHllbEthTEg4NUNKWXlTVE9ZQUlIRUxmRUlqVlJ1UTUrRnAxQlRQby9CUUZhSXJYN1pMYUc2OHk3WnR6NUhobmhoRCtzZC9zbVdhUG5wUTBNaXpQUGl6TUJpbGNTK1lZSzkzRWJFVVZ1MkU5VmhBTmxwQ3hJZWJHcU1MTXZHV283MjYrZmQrS2pZRys0Y3BPbHpaQmJKQ2FLSDVwMG9MMTdZODZ4YXRvSVhzWGprQWpVSTdrdHVhblIrVEVFY2xuL0RFMXlTdU5XVEo5Y2VRQUYxeTE5aWZUaHVpVHVYakEyZHRCSWFhRFdjUnpqcnY2S3VDU2pUUjM1Z2l5VEFzUnZSQjVTWWhCNlNwT1lhN0Z6RkpIRjkxbnAvNkdCNy9IaVl0aHBmSE5NNGxETmd3NUZabEpnT0tvTG0wRCtyWU43WEpLcVdhb085Tkl3Wmh4ZGhGU0NKQnRTd3o3ZnNOVFZQM2N6NFVqcUJWZnFVaTN4RHM4bDBZUkJvT2JmSnIwY0VGc1crT1VtTGgrUmdYR0VHQ1hMeUJzUGVHc2dNOFljQ25ORUIrWTRrRTlJdE5Ld1Rmbnorb2czQ3VsTGxuMHJiWXZsbi82TmZIeTlEUkVkN2RKSDhpeVNYYVM2Y015bGNIckMzL1dOM2R0TUMxU3RoNHFIT2RDSTciLCJtYWMiOiI1NGRjNjFiZGNkMGM1NDkxMmZlYTg0YjliOTk3YjgxMGQ5MmI0Y2M3OTUxNjc2MjkyNWM2ZGE3MGEyZWM2ZmRiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InY4SzlmVHE5Z1BoRWFRNHNwckN6ZEE9PSIsInZhbHVlIjoidUFaSzBlNDUxMnFqSGpFZE1qdXpHbEpTVzFNUUFMSGJpZXk5MDRKRGpXRFhiYlREVDZnWStoTTEzYlJKUHBzd1IvSExNYUg0UzVHOE5yZ1psYU9VRXc5UzVwRVJ6a00xbkt1U25vcGhqTjhpVDdZTlJud3huZmlIN3A0TDFZZEdKcmZQRDlJR0FmTUZtQXFUejdPaDhlcTBqTEJBbnBlYXVmVXkxYWkyZUxKeFJ2RXNMU3M5RmZmTHdVM2FGTTBuamlMUDh0cTgyNjdvZ1lISWNLcjVwTGNuRGR1S3FTaVQ1L2JzM3hkUU9ZV2lURUgrSXEzSVhZaWVlV28zZ05QNWZseGRZQnJZQ2xtYjdZMFZKR3Jac2YrWkEzeCsxalo1YkRFQ3JKOHYyNVEzTUk4dTJKVWJlMXFlWkNJbVBRNjdSZXJXVmc1TEowSjBPTy9UcDhoeHRZUTRqdXR1M1ZJMnExZnVOS3FCL3NwL3JTZHFXSUlMenA2ak1wcXNEVHNiTkZ5aTdyd3VTZ1BkMVMrVUJQN1lJTVFla01jcUNKRDdZTzJkS3d3NGdycDhPazNPS2d0SVlwSkNQQ2lmWHk1Q0RscXlGaC9UZnJJWmhIcGg2QmNMMk5ValorSE9HYVpLNUJMMjJYRDBqWXJCUVN0dm9pQ1p3SFY0eGtiQ0tiR0QiLCJtYWMiOiIwNGNhMjgyNTllYTkzOWY5MjFlZWU1Yzg3MjM2MzNlMzk1ODcwZjYxMDU0Y2NjNzE3YjA2NTBlMDNiOGZjYjRmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFxdnZmd2xFS1ZWdTRiakQ1clV2NHc9PSIsInZhbHVlIjoiWVQvS2U5cHllbEthTEg4NUNKWXlTVE9ZQUlIRUxmRUlqVlJ1UTUrRnAxQlRQby9CUUZhSXJYN1pMYUc2OHk3WnR6NUhobmhoRCtzZC9zbVdhUG5wUTBNaXpQUGl6TUJpbGNTK1lZSzkzRWJFVVZ1MkU5VmhBTmxwQ3hJZWJHcU1MTXZHV283MjYrZmQrS2pZRys0Y3BPbHpaQmJKQ2FLSDVwMG9MMTdZODZ4YXRvSVhzWGprQWpVSTdrdHVhblIrVEVFY2xuL0RFMXlTdU5XVEo5Y2VRQUYxeTE5aWZUaHVpVHVYakEyZHRCSWFhRFdjUnpqcnY2S3VDU2pUUjM1Z2l5VEFzUnZSQjVTWWhCNlNwT1lhN0Z6RkpIRjkxbnAvNkdCNy9IaVl0aHBmSE5NNGxETmd3NUZabEpnT0tvTG0wRCtyWU43WEpLcVdhb085Tkl3Wmh4ZGhGU0NKQnRTd3o3ZnNOVFZQM2N6NFVqcUJWZnFVaTN4RHM4bDBZUkJvT2JmSnIwY0VGc1crT1VtTGgrUmdYR0VHQ1hMeUJzUGVHc2dNOFljQ25ORUIrWTRrRTlJdE5Ld1Rmbnorb2czQ3VsTGxuMHJiWXZsbi82TmZIeTlEUkVkN2RKSDhpeVNYYVM2Y015bGNIckMzL1dOM2R0TUMxU3RoNHFIT2RDSTciLCJtYWMiOiI1NGRjNjFiZGNkMGM1NDkxMmZlYTg0YjliOTk3YjgxMGQ5MmI0Y2M3OTUxNjc2MjkyNWM2ZGE3MGEyZWM2ZmRiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InY4SzlmVHE5Z1BoRWFRNHNwckN6ZEE9PSIsInZhbHVlIjoidUFaSzBlNDUxMnFqSGpFZE1qdXpHbEpTVzFNUUFMSGJpZXk5MDRKRGpXRFhiYlREVDZnWStoTTEzYlJKUHBzd1IvSExNYUg0UzVHOE5yZ1psYU9VRXc5UzVwRVJ6a00xbkt1U25vcGhqTjhpVDdZTlJud3huZmlIN3A0TDFZZEdKcmZQRDlJR0FmTUZtQXFUejdPaDhlcTBqTEJBbnBlYXVmVXkxYWkyZUxKeFJ2RXNMU3M5RmZmTHdVM2FGTTBuamlMUDh0cTgyNjdvZ1lISWNLcjVwTGNuRGR1S3FTaVQ1L2JzM3hkUU9ZV2lURUgrSXEzSVhZaWVlV28zZ05QNWZseGRZQnJZQ2xtYjdZMFZKR3Jac2YrWkEzeCsxalo1YkRFQ3JKOHYyNVEzTUk4dTJKVWJlMXFlWkNJbVBRNjdSZXJXVmc1TEowSjBPTy9UcDhoeHRZUTRqdXR1M1ZJMnExZnVOS3FCL3NwL3JTZHFXSUlMenA2ak1wcXNEVHNiTkZ5aTdyd3VTZ1BkMVMrVUJQN1lJTVFla01jcUNKRDdZTzJkS3d3NGdycDhPazNPS2d0SVlwSkNQQ2lmWHk1Q0RscXlGaC9UZnJJWmhIcGg2QmNMMk5ValorSE9HYVpLNUJMMjJYRDBqWXJCUVN0dm9pQ1p3SFY0eGtiQ0tiR0QiLCJtYWMiOiIwNGNhMjgyNTllYTkzOWY5MjFlZWU1Yzg3MjM2MzNlMzk1ODcwZjYxMDU0Y2NjNzE3YjA2NTBlMDNiOGZjYjRmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815429232\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-409831797 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409831797\", {\"maxDepth\":0})</script>\n"}}