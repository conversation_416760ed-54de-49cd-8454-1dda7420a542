{"__meta": {"id": "Xff5bcb5121eaa5aa88e2c9ae7db2390a", "datetime": "2025-06-08 12:54:43", "utime": **********.472337, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.055312, "end": **********.472372, "duration": 1.***************, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": **********.055312, "relative_start": 0, "end": **********.264121, "relative_end": **********.264121, "duration": 1.****************, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.26414, "relative_start": 1.****************, "end": **********.472376, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03188, "accumulated_duration_str": "31.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3445709, "duration": 0.02852, "duration_str": "28.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.46}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4103959, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.46, "width_percent": 3.545}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.444885, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.005, "width_percent": 6.995}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387279672%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkF5QW1rc1VEQlFQaG1YQk81VEFyY0E9PSIsInZhbHVlIjoiS3k4a2hGcmZzUGJHNXBnZGhKRHU5MXZ5bVhadURzUWRuL3RoU2VuWGJPVm5SeUlKa1F2VUhYQkpoNlZoRjdFUzJNbWhReHNXd1gzSDJhNTJMRFVLUkhXMnFDUmJyWEw1YjAvRzdLRm9Rc0l2SjM0UHJNclpFWGRLa0xsM2VPcGw2R1lhOFRQd0UzbnM2NHJpQS82ZUZ0OTNpcFd4aDB4Yk1jU1YxVk9WaGpoeGdyL0ZGUnd1bkYxZ0JlUkFlaE1CYy9NamFpNXZYZlh4NXg0RzNLS01kbTlRT0VMT0Q2dkV2Z0NaR1VUQTc5RmVuajdTTEVRMCtrenBxYmRzTVhqWFdpYUNhSk1iaVA1QjZnclVYck0xYTBGTzNaQWpORk1vYVp2RDNRTWNPTmZ2Q1UvNXR6aFZ0T05LWDhwWkx0NGFVWUxtbjk5NzFHcjBnRHZFd0VYSVFGOU5MNzNjWWV6TFJwK2s1bWNOS2VuOWk4K05VR0Irc0c2dTlTdE5iZlFJclFFdHFHZWZuY0syd3huRnZxRUV3aDVSSGFDV3hBQWIrSjRBamJjd0Via0ljYUlMRjVsenpDUE5JeEwxbkZmYWUxeWV0blhtUEczeG4xMjJsUlQ0Rmc1OWxYWDJ4cGhBVTh6Wm96c3JkUXRJU2FrdlB0YkdDbko1VlhJWERKWnkiLCJtYWMiOiIwZjlkNzVhMmY5NTVkNzc2ODQ0NDFmYTU4YjE1ZjJmOGZkYzk4ZGU5NzhjOGEwNjVlMzkxYTZjZmJhZDRjZmZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVVS1hSbkhyNG51bCtVMVMwbkNjUkE9PSIsInZhbHVlIjoiNVlXOURjWjVpVi9UQVNsSHR1KytVSUdaZGhnSzYvQ0lDa3B4dnYzVWpIdFlDMVhKQ1NKWlBjM1hCQ3pCVDBqRkt0eEdxdnVJVW92YUdMcHhtOE1WalFGeG5KbVFQaGpIMm1aN1NkWjVvTkExc1ZEUnNXaFhQbzdUU05HbkpxNElNMjl3cXVTenVFeDY3ZmxhRlE3bmk5L292aXhvUkhCYzlKUkpyVksyMnE4cWkzRE5ta3FQMTBna0g0aERoTVdMWFB2Rjl1SEtjTGpYWnB6cU4razd1enA1eVRDa1JIZ2RISWxEMko2b1RLUTdOYmdYNnNBR2RnUVVGVFFrajBNRngvUlBqaUxSZFRQQWpmVEZGdlM1Y29yY0ZaOFBDbWdRRjU1NVdMRkdTUVNzTXRpZFJjSUFVQVE3RWN2T1phQjc2ZUFqT0UvWXVUZWR0MXhMV0psWGVjRzZMckRUamJYSTZ0VFE1ZzgyZ0ErZVJaUm8vRVdqdGZuclRMQkJIYU5GS0xKS1FnU2ZMT0pNZ1Q5bmNpZXlVQ0tOSmROMzVQZnY0UUFOcndHaG9lTnc0RHQrUlZEQXd3TTFrUzF4UVdhMlFQdkdGRnB6a2tad0JCTFMycGt0cEJQeTdHM0lnTVBUNTY1ZEdZM3hTUU05ZnV3SFc3djVLVjdWV0lmQjR2MEciLCJtYWMiOiJhMTNiZmJhMmExMjIxMTgwODMyY2M4MjRhZDk0MTU4MDMxYWIxMWJhYjdlZmFmNzkxMTcxY2UwYmE1MmQ4ODU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2075685315 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlIK2NYcW1jQXREQmxGYXNWUmdsVHc9PSIsInZhbHVlIjoidVdGdDBjVkoxWU05cnFnWE85STVpSkxlcGI0M1dQZklsa1JWSmZqV21GUW5kUW5HNS9pNFNCLzFsVUpreHZ0aVhETlU3YzQwTGVxY0M3T0JqVVN4dXFjcjcxcUd3dlJ3SXZBSkM1SEZiK0FwdE1jeUp3emNrbDVrcU80ZWczL2d5eFJnMmdORWM4SjZsMG5HRnRPY1ZDd2lIQnZNR2E2SG9hOUIvT1RFeXN5U3pDNEZrcXU2Y3BXL3habThBQ0pGRS80WklCanB4eGhjTWthaFZyMHA3ck5uOEVTZXd4QmdHdEVjdlpHMDJuenVTM21aRHB3ME0xTng5aldTZUdiNTdWMDIvQmFPeEtKT0ZueEZTcEEyN0MyZFluTmtNck1GQytvMWg4ZkJxU2x5ZTF4akpwekd3ZUFwZk1EbHJQRTlOdE5yVUE0Wi94TE9GWmdBOEFGY0FwdUdtVXBjS0p5QTR3a2tGNGNTd0xxenNIZElPSDlmWE44YTRwbWhsbTJpbHd4OTRFM0VKOWd6WXJaM3JKYWpBczl3MnVXamtLK2owcWxWNks2L0hNNGV0eElVdjVscEVMamhZeDFPRWxFN2hzNTVSZ0h5YWhSclZlSmMxeG9LaEhRU3ZWNVZzWGs3R3lyV1VoRnI3MWU2OHRKUmxKblZLYXoveGpIOWZsaVgiLCJtYWMiOiI1ODc1YWFmNzFlYjhkODg5NjFhYTRlMGJiZjRjMjBkMzc2NjJiYmU5ZmMyZDc2Y2YxYzRjMjBhZTM3ODA0YmFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImUyY3BEQU9UQm4yMEkyK2o1ZHJza2c9PSIsInZhbHVlIjoiSmM5L3ZwZUhMUFBuYWJnbVR3djFFdDhmb2N0dm85aUhNSUJIZlE0R2FTL0xoaENXbkxrc0pCLy9wV3FCZTB1RjdJNjY5SkcxdnZmZjk2cjlvOGJJcU1SNWVjTzdnbElyUWNXYmZ3akhpd2RadTgrRlhDemcvbTJPeEx5YUdvaXU3NThKbTZhSS9UamVSRW5jSWxLeGlnMkdUL1V3VDZUSU5mUk9hZXZXcFhUMllXVzdLQmZRQTgralB0ZW93QnZvcDVySllJZ042Vm0vazhMd1hWaWJPZWh5c0VQN1BReFM2MFl5U0MxbXM0bStjRDFVeFZoR0RudFVkeUtCbmtpNGdRN0FQdjJrNFgrNEJ6Nzh3RUFaemd1QitsMlBwUnp5QnJmaWZMNitEcm03V1RLUWVlekMyTFVubHVvZWt6cW8rRUY3YmhtcEk2bll1ZGNac1d0STVXZDRxYkQ2SDdYVDZCaFJGd3Q3dWR3OTBoUEEvZE5qMVRYU2U1cytadW5aaE1JcnFWQzM5bjJzUHBCWEMxcXJwcUNSaisxVERzMmV3UFR3a3NlNWpkbWRQMC80YTVhY01objBPcW5PaXVvWm5rNUN6OXVvMVN1WWQ1YzZtK0V3YTNSVWUzZTVONVpIekptalBQMExETHkrYmtGdC9QUEdLVldZYnkwcG9RdTEiLCJtYWMiOiJmYjNiYWNjNjFlNTY1NTBhY2RmYTFkNWRlZWIzOTMyNDQ5NmExMDZkYTVmYzMwMTI3YTZkMGVjZTc4NTA3NzRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlIK2NYcW1jQXREQmxGYXNWUmdsVHc9PSIsInZhbHVlIjoidVdGdDBjVkoxWU05cnFnWE85STVpSkxlcGI0M1dQZklsa1JWSmZqV21GUW5kUW5HNS9pNFNCLzFsVUpreHZ0aVhETlU3YzQwTGVxY0M3T0JqVVN4dXFjcjcxcUd3dlJ3SXZBSkM1SEZiK0FwdE1jeUp3emNrbDVrcU80ZWczL2d5eFJnMmdORWM4SjZsMG5HRnRPY1ZDd2lIQnZNR2E2SG9hOUIvT1RFeXN5U3pDNEZrcXU2Y3BXL3habThBQ0pGRS80WklCanB4eGhjTWthaFZyMHA3ck5uOEVTZXd4QmdHdEVjdlpHMDJuenVTM21aRHB3ME0xTng5aldTZUdiNTdWMDIvQmFPeEtKT0ZueEZTcEEyN0MyZFluTmtNck1GQytvMWg4ZkJxU2x5ZTF4akpwekd3ZUFwZk1EbHJQRTlOdE5yVUE0Wi94TE9GWmdBOEFGY0FwdUdtVXBjS0p5QTR3a2tGNGNTd0xxenNIZElPSDlmWE44YTRwbWhsbTJpbHd4OTRFM0VKOWd6WXJaM3JKYWpBczl3MnVXamtLK2owcWxWNks2L0hNNGV0eElVdjVscEVMamhZeDFPRWxFN2hzNTVSZ0h5YWhSclZlSmMxeG9LaEhRU3ZWNVZzWGs3R3lyV1VoRnI3MWU2OHRKUmxKblZLYXoveGpIOWZsaVgiLCJtYWMiOiI1ODc1YWFmNzFlYjhkODg5NjFhYTRlMGJiZjRjMjBkMzc2NjJiYmU5ZmMyZDc2Y2YxYzRjMjBhZTM3ODA0YmFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImUyY3BEQU9UQm4yMEkyK2o1ZHJza2c9PSIsInZhbHVlIjoiSmM5L3ZwZUhMUFBuYWJnbVR3djFFdDhmb2N0dm85aUhNSUJIZlE0R2FTL0xoaENXbkxrc0pCLy9wV3FCZTB1RjdJNjY5SkcxdnZmZjk2cjlvOGJJcU1SNWVjTzdnbElyUWNXYmZ3akhpd2RadTgrRlhDemcvbTJPeEx5YUdvaXU3NThKbTZhSS9UamVSRW5jSWxLeGlnMkdUL1V3VDZUSU5mUk9hZXZXcFhUMllXVzdLQmZRQTgralB0ZW93QnZvcDVySllJZ042Vm0vazhMd1hWaWJPZWh5c0VQN1BReFM2MFl5U0MxbXM0bStjRDFVeFZoR0RudFVkeUtCbmtpNGdRN0FQdjJrNFgrNEJ6Nzh3RUFaemd1QitsMlBwUnp5QnJmaWZMNitEcm03V1RLUWVlekMyTFVubHVvZWt6cW8rRUY3YmhtcEk2bll1ZGNac1d0STVXZDRxYkQ2SDdYVDZCaFJGd3Q3dWR3OTBoUEEvZE5qMVRYU2U1cytadW5aaE1JcnFWQzM5bjJzUHBCWEMxcXJwcUNSaisxVERzMmV3UFR3a3NlNWpkbWRQMC80YTVhY01objBPcW5PaXVvWm5rNUN6OXVvMVN1WWQ1YzZtK0V3YTNSVWUzZTVONVpIekptalBQMExETHkrYmtGdC9QUEdLVldZYnkwcG9RdTEiLCJtYWMiOiJmYjNiYWNjNjFlNTY1NTBhY2RmYTFkNWRlZWIzOTMyNDQ5NmExMDZkYTVmYzMwMTI3YTZkMGVjZTc4NTA3NzRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075685315\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-402758550 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402758550\", {\"maxDepth\":0})</script>\n"}}