{"__meta": {"id": "X9c66269059eb053352f800f11cc4fc1c", "datetime": "2025-06-08 14:52:21", "utime": **********.801208, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.169612, "end": **********.80123, "duration": 0.6316180229187012, "duration_str": "632ms", "measures": [{"label": "Booting", "start": **********.169612, "relative_start": 0, "end": **********.647738, "relative_end": **********.647738, "duration": 0.47812604904174805, "duration_str": "478ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.64775, "relative_start": 0.4781379699707031, "end": **********.801232, "relative_end": 2.1457672119140625e-06, "duration": 0.15348219871520996, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53629488, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01426, "accumulated_duration_str": "14.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.700709, "duration": 0.00787, "duration_str": "7.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 55.189}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7245698, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 55.189, "width_percent": 7.854}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.755967, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 63.043, "width_percent": 13.675}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.762158, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 76.718, "width_percent": 10.168}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7721078, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 86.886, "width_percent": 5.4}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7787988, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 92.286, "width_percent": 7.714}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1228691671 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228691671\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770602, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1729315283 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1729315283\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394122823%7C63%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZBZGVETnRFRVpUOGlRNW83VFNIUkE9PSIsInZhbHVlIjoiMVZOVkp2ZmgzVStuZWRsd3NSRHAxcDdoZHk1ZWFPNWVmWVFXU1hmcGdDN0xyOWZBZTR1VUNZamc1eUp5U0xJMGFLN3lxZTVkcEYrUEJnSmxHa0ZJWE9pSld5NDIwZzdORTZGdWpXZlhrNFZIcUZvRTgrNlplR010cEVaajRkSkdzc01QUUU1ZW1HVzdCZnZ0M05kMmtUcm1IbnBLQk1SbTJQVjNGUVMzOHFBWVNhdDh3ajd2TGFtR1RNZHpYTTY4OXJyU1RZaDRIcy81MG51SXppTWxNUmRLYlNsWDJTeXBUQ0JablNrZ01TKzJBKzN6Z3FYRmxTNUtWclR1TWV0aWhZUFNENnl0ZGJGUFZHUFNoM2xtNjk1UWRIaFFEUkhjTHdGSWhRM3lSRitHMHIzTGhNUzQ4TzEzZGN4UHg0N205K3JMSjE3TXl6ZG51TG5lK2pYYll0VW91MVJML3pVM2czbiswRnFmajN4VDhzZUpZUFIxSW13NzIyaFB5RXVIMi92S2NGVDltdHhZMVNLYUE3M2E4MktiWnpEVitRRFlvTGovKy9pQjhreklsZHNFTTloZTJUZVdSRWtGV1B1ajFuNG9veDlTaHFpcHMrRlE5MWhKd05GbDA2RHFvSzlHUW9YNjh1akVGSGljd2l1c1RFN1prT05rNjZqWWF5RU0iLCJtYWMiOiJjNjQ1M2ZmZGQxMDQwYTBhMWM4ZjQ0YWNjM2VlMjQyNmYxYzYxNDNhYzdkNDYzMDY1YWEwNTZhN2MxNDBjNmMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJUM0ozMm9lc2dMWE1TeE45cElpRUE9PSIsInZhbHVlIjoiRU52MU9HQlNZRjdPa1N5WE8wSW9PQkVjWVdNSzJ1SHRKTCt1MjVMc0dITVNGQ285NnMvMkF3cDVBeDFEOGI3SEtpS2tsK2o3WkVRQ0FBN29ISmEyY1pLVnpiZlIra0llWWhCYmVoVzRLbjNGUmkrczB2QlVkb09NVllaNnFDL2dwUENHUENXQnRCQWtUTEUvZmJYVU9kV1R6QTZJb3JOb3FVU2lBZVpad1JlYWdoc0VmbUNvL3VWaC9VMU56S2VVUWFWRklIUGxqZGc5ME90SnludEtERnRPQVdHNmt4ZENPOEVsRWNRUG56T1lyQU5vdm0xWUpNWHdCMUE2a3c5U2ZoRW1mZU9RaWw1ckw5RERkbEE3Z0U0MjYxY0hWdEJycXpYYXJIMmVuYVRpcHdkMFBuMzMxbFBHYUh3YS81UXJ0Y2pMS2ZmTkFVUnZNdFB1ZUpnaEJuZC9GbEhRbW9sbnNEMHUrdlFLOHpSazZzZWZjM1JETlZnRHEwS01wUFppQ0c2Q1M0bWpDYVUwaWFXUXFGNHZlblcreE1MbVFyc0EwWnkxRllOYVpoOHBtKzgrTnk4MkNBdWttejdTV0hpMGk0dllKUXJOWHVZSzI5N0EzZjR2QWVvcTVjRjlWb0QvR2IrczZkdk80RzF3cnB3UEtWVGowSi90NU9PQitObEwiLCJtYWMiOiJkZDA3YTVhY2ZiOGJiOTEyMjVkZWFlOGMxOTczOGJkOGZiZTU2ZjQ1NDk2Mzc5ZWZiZDliNzY4MDZhMTJmMGRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-40393362 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40393362\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBEQVIzc0VBRllEZ1ZOTU1lRTR3MEE9PSIsInZhbHVlIjoiTkFkMUViWmQ0L1F0eFRFbWdQZmNML285SmxGZ09sRU4vMGM0ZXRRUDhWMVVsaDdBSm5tM3lvQjJXY01seG1TMGlUKzRkdE1FU3dmNVRWRW9rYnEwVmJRcEtPZXkvT3pVcFM1WVlMUEZScXd6ZERRRjl4QTNnYUZoZ2ZxMVFkMWdLaGk4Q1RjWjVxeUVpOG9WSE1zeDJWb21pbnBWWFMvb0VtTWVIbFhnODZ6V2trdFh0anI2N2ZxU2FNT29RbGE5SHdFeVBWaFFxaXFGbjJsd0FpdGg3V2E4TE1uSkU5OFBuU0xJTHpKUk5rKzYzM25kVHJyRnYveDNWTUU2ZUROaDI2UFBiNlhTL0ZPUUh4T0pWMjVzdUtqRzdrNjFaUncvaXQwRnJlMytjZGxqMW9jMUxOZ21lcGR4NXpyNjY0QjREdDRXK2pQUUh1a1RVeTdVcnhWaExGVGplNXRoQXJ5YWVQeHVodFhyNmIwUTRVNWE5dlpUeTM5NUVUZjZnZFRQRXlrd2RQeXRVMm9kQUJhKzlLNFVoUUNQYzJ5K0VOTHpsamNVS3lNb1hMRXRBZlN5NVBDemNIb0hXTzJaZjVsOG5IWVJTQzVQeTF0ZGdlY3R1TUNJK2tjdjFwY1lXQjFScHdiYW96Q0J3cGpQaDVTNjlDV214Tm1FTkUyUEVkV2oiLCJtYWMiOiI3ZmYzN2RhODg5MzM4NmViMDA5NmUwYTVkMTUzYzNmZTMzYWZmMDg0OGEwNDcyY2ZhZTExYWIxYzA5ZjZjMTZmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9qazkzYm9sQnhtVDNxeSsxYVZLR2c9PSIsInZhbHVlIjoiWTYwc2kzVzgrV3VNOEJTTTgvbWNBWWZ4amxZNUpaTmFhc2J3SzZOWU1sVE1PQnZrclZzbm1sZmJnVGxPbmJ6QnZtTktlWExnVDhiVUkrRUltU0VDZWs4NlFQdHRRV3F4ak5oSC8vU3FKN2w5eFhKWnRqa25KLzY4TTgwcE4yUW15TzVtRnBKc3FLVTJqMHduYkU4eVdGMFB0NUhFUDQ0bUlGTmt2Smgwck53d3dtajhsTlZJOEhMSkR5Qmx3aDZWaGY4Y2dUdURLYmxaV0VUb3FLcldPK1lOcGs0VWFEcUc3eStKRlFkcVdMSFpWQ29WbkdrM2JzamFKcmZtTXpuYlZrazk1R2gwUmpZWmFqT1l2MU9PdnU0WjNJSmZmZWZnUkRUT1d1ck55eFloVmxHNGJmOFJPM2QxTGhJUFA0U205emlNMW85ZXlnVjBHNGF2M3A1YlVpWWROenVLZ28xWDNhS2hpRUJxbHdiTjhFQXNGYlJEVFhxbTZ1QU9aKzlIT1UvSmZrdVBhQTRmeW1PckxGeVdHcCtGZldmWThFSGkvVzQ1RmsvYzAzRUxxZzVJQmhtQTkxWExoV3hrRTczOUdwdmNtcnJ1NXNNQ0t0NnZtSC8ydFlmYW1pcWZqMGpBcTU4UWJkRzJpdzBjSnlRamZOQWRZQUhDdVd5VXlFNkwiLCJtYWMiOiJhZTdkY2U3MWQxYTBiYjIxMmE1ZGZjNDI1Y2IxNjg5ZjFiNzFhNDdhNGQ2NGJlY2Y3ZWU1NTA4MmE3NTdlMjA3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBEQVIzc0VBRllEZ1ZOTU1lRTR3MEE9PSIsInZhbHVlIjoiTkFkMUViWmQ0L1F0eFRFbWdQZmNML285SmxGZ09sRU4vMGM0ZXRRUDhWMVVsaDdBSm5tM3lvQjJXY01seG1TMGlUKzRkdE1FU3dmNVRWRW9rYnEwVmJRcEtPZXkvT3pVcFM1WVlMUEZScXd6ZERRRjl4QTNnYUZoZ2ZxMVFkMWdLaGk4Q1RjWjVxeUVpOG9WSE1zeDJWb21pbnBWWFMvb0VtTWVIbFhnODZ6V2trdFh0anI2N2ZxU2FNT29RbGE5SHdFeVBWaFFxaXFGbjJsd0FpdGg3V2E4TE1uSkU5OFBuU0xJTHpKUk5rKzYzM25kVHJyRnYveDNWTUU2ZUROaDI2UFBiNlhTL0ZPUUh4T0pWMjVzdUtqRzdrNjFaUncvaXQwRnJlMytjZGxqMW9jMUxOZ21lcGR4NXpyNjY0QjREdDRXK2pQUUh1a1RVeTdVcnhWaExGVGplNXRoQXJ5YWVQeHVodFhyNmIwUTRVNWE5dlpUeTM5NUVUZjZnZFRQRXlrd2RQeXRVMm9kQUJhKzlLNFVoUUNQYzJ5K0VOTHpsamNVS3lNb1hMRXRBZlN5NVBDemNIb0hXTzJaZjVsOG5IWVJTQzVQeTF0ZGdlY3R1TUNJK2tjdjFwY1lXQjFScHdiYW96Q0J3cGpQaDVTNjlDV214Tm1FTkUyUEVkV2oiLCJtYWMiOiI3ZmYzN2RhODg5MzM4NmViMDA5NmUwYTVkMTUzYzNmZTMzYWZmMDg0OGEwNDcyY2ZhZTExYWIxYzA5ZjZjMTZmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9qazkzYm9sQnhtVDNxeSsxYVZLR2c9PSIsInZhbHVlIjoiWTYwc2kzVzgrV3VNOEJTTTgvbWNBWWZ4amxZNUpaTmFhc2J3SzZOWU1sVE1PQnZrclZzbm1sZmJnVGxPbmJ6QnZtTktlWExnVDhiVUkrRUltU0VDZWs4NlFQdHRRV3F4ak5oSC8vU3FKN2w5eFhKWnRqa25KLzY4TTgwcE4yUW15TzVtRnBKc3FLVTJqMHduYkU4eVdGMFB0NUhFUDQ0bUlGTmt2Smgwck53d3dtajhsTlZJOEhMSkR5Qmx3aDZWaGY4Y2dUdURLYmxaV0VUb3FLcldPK1lOcGs0VWFEcUc3eStKRlFkcVdMSFpWQ29WbkdrM2JzamFKcmZtTXpuYlZrazk1R2gwUmpZWmFqT1l2MU9PdnU0WjNJSmZmZWZnUkRUT1d1ck55eFloVmxHNGJmOFJPM2QxTGhJUFA0U205emlNMW85ZXlnVjBHNGF2M3A1YlVpWWROenVLZ28xWDNhS2hpRUJxbHdiTjhFQXNGYlJEVFhxbTZ1QU9aKzlIT1UvSmZrdVBhQTRmeW1PckxGeVdHcCtGZldmWThFSGkvVzQ1RmsvYzAzRUxxZzVJQmhtQTkxWExoV3hrRTczOUdwdmNtcnJ1NXNNQ0t0NnZtSC8ydFlmYW1pcWZqMGpBcTU4UWJkRzJpdzBjSnlRamZOQWRZQUhDdVd5VXlFNkwiLCJtYWMiOiJhZTdkY2U3MWQxYTBiYjIxMmE1ZGZjNDI1Y2IxNjg5ZjFiNzFhNDdhNGQ2NGJlY2Y3ZWU1NTA4MmE3NTdlMjA3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}