{"__meta": {"id": "Xc08c954e94fa0f2170d0749dabac8174", "datetime": "2025-06-08 14:52:57", "utime": **********.186048, "method": "POST", "uri": "/pos/process/delivery/payment", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[14:52:57] LOG.info: تم تحديث السجل المالي بعد تحصيل التوصيل {\n    \"pos_id\": 33,\n    \"amount\": \"32.00\",\n    \"new_delivery_cash\": 0,\n    \"new_current_cash\": 242\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.177074, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749394376.49091, "end": **********.186072, "duration": 0.6951620578765869, "duration_str": "695ms", "measures": [{"label": "Booting", "start": 1749394376.49091, "relative_start": 0, "end": **********.031934, "relative_end": **********.031934, "duration": 0.5410239696502686, "duration_str": "541ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.031953, "relative_start": 0.5410430431365967, "end": **********.186074, "relative_end": 1.9073486328125e-06, "duration": 0.15412092208862305, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48176440, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos/process/delivery/payment", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@processDeliveryPayment", "namespace": null, "prefix": "", "where": [], "as": "pos.process.delivery.payment", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2175\" onclick=\"\">app/Http/Controllers/PosController.php:2175-2238</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03617999999999999, "accumulated_duration_str": "36.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.076858, "duration": 0.02125, "duration_str": "21.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.734}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1097739, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.734, "width_percent": 1.686}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.133234, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 60.42, "width_percent": 1.935}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.137495, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 62.355, "width_percent": 2.211}, {"sql": "select * from `pos` where `pos`.`id` = '33' limit 1", "type": "query", "params": [], "bindings": ["33"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2183}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.144658, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2183", "source": "app/Http/Controllers/PosController.php:2183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2183", "ajax": false, "filename": "PosController.php", "line": "2183"}, "connection": "ty", "start_percent": 64.566, "width_percent": 1.493}, {"sql": "update `pos` set `delivery_status` = 'delivery_completed', `is_payment_set` = 1, `pos`.`updated_at` = '2025-06-08 14:52:57' where `id` = 33", "type": "query", "params": [], "bindings": ["delivery_completed", "1", "2025-06-08 14:52:57", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2196}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.14871, "duration": 0.00838, "duration_str": "8.38ms", "memory": 0, "memory_str": null, "filename": "PosController.php:2196", "source": "app/Http/Controllers/PosController.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2196", "ajax": false, "filename": "PosController.php", "line": "2196"}, "connection": "ty", "start_percent": 66.059, "width_percent": 23.162}, {"sql": "select * from `pos_payments` where `pos_payments`.`pos_id` = 33 and `pos_payments`.`pos_id` is not null limit 1", "type": "query", "params": [], "bindings": ["33"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.162381, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2199", "source": "app/Http/Controllers/PosController.php:2199", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2199", "ajax": false, "filename": "PosController.php", "line": "2199"}, "connection": "ty", "start_percent": 89.221, "width_percent": 1.99}, {"sql": "select * from `financial_records` where `shift_id` = 2 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1673648, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "PosController.php:2207", "source": "app/Http/Controllers/PosController.php:2207", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2207", "ajax": false, "filename": "PosController.php", "line": "2207"}, "connection": "ty", "start_percent": 91.211, "width_percent": 2.543}, {"sql": "update `financial_records` set `current_cash` = 242, `delivery_cash` = 0, `total_cash` = 296, `financial_records`.`updated_at` = '2025-06-08 14:52:57' where `id` = 2", "type": "query", "params": [], "bindings": ["242", "0", "296", "2025-06-08 14:52:57", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 2220}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.171385, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "PosController.php:2220", "source": "app/Http/Controllers/PosController.php:2220", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=2220", "ajax": false, "filename": "PosController.php", "line": "2220"}, "connection": "ty", "start_percent": 93.753, "width_percent": 6.247}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\PosPayment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPosPayment.php&line=1", "ajax": false, "filename": "PosPayment.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-333291004 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333291004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.143459, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos/process/delivery/payment", "status_code": "<pre class=sf-dump id=sf-dump-1298758497 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1298758497\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1172902600 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pos_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">33</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172902600\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-972502484 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394374407%7C65%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklkSlh3cktWL1FkeWNUUTlvUkdxdHc9PSIsInZhbHVlIjoiM2xxWURsUkorOEI3cm5UckxMenZXOHRCcy9ES0FiWks4U0QzZWduUTBMWlN2NUNyZlp0bjl4NmcwVGJEZ1BqRVZzRlFPdzVUVjdLZlUwS25xRWRSUlNYdXJCOFJWelF4MUY0VC9lSkhzY3BaeU42R2k0NTNLTVU0U25DNlh4Mkl0Mlg4ZU5HcVBPV244MnlQdVFNelpuMmlRVGpsMWU1L2NuTkdmT2k4UVB6MEpjL2x1cmxwem1NNW1HdXRMOERRNjZMc2ttaFpJMlZMVFUvU2dUdXR6SytOS1d3Qm9LQmh6UCtpWmp3V0lLeklYMFRZTmdkdk9PeHVpTmoyQWhOdkhXS0g2WnRxT0hFZ3dmM2p3ckVZdmVvK3Q1UUNxajBXT3JZbWdpK3Zick13R0YwLzlpZVJnTXJYYjZuTmRsT0ZhZ2NnTVd0K0hvUWtpTG95VW1DVjl4Q2RDYzhSUEtYMU1rM3ZTRXlCZVU5Rkd3UTNIYXl6TUo1ajFBaDdqdjBacTNMQUxvcWZPWE1hbzZxQW9GNXdVWTdNYVJmR08vQ1pIVS96OVA3OVd0QXkzUHd2S0c1K2M0UzIvUFM1YjhKdGxPV3BvUEt2bzZJOUs2bG1HcVFpaGk1OUVrb25RcGZEaXhwY0x0MTdPVUUvSzRPY1doOUd1NGR5VlhqODM4VEYiLCJtYWMiOiIyOGJjODc2ZTBjMjRmMjAwMmRkMThlM2Y3MjBkNWU5ZWQwNzJhMjZjMmFkMGE4NDk2MDBlYTg1NDkxYjVkZDNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitjUTg0RVpqVjdwNGErclpBOTIxcEE9PSIsInZhbHVlIjoiZm82NFhaZjJnb3FxbDcvVkdYdlk2dHl2b2dINjdsMzVSSTJudEliZDZBOWhsZitMcVoyckVMTGY0UUxqRE9vbHR1OVd5NzYvREphdVlVanBFSW9PaGE0d0NoQ3p4QkM4QWxkaU9UUTE2bXFCTE9obG01UXdYcFNMT2V4Uk4wcS9maWZxMzN0RzNoblBDT1BRRXpEYmZwamFwNndWSitSZXFuTUkyMlJkQ0xibEYyZnUwVlJwMDlKTDhheTFQWVF5d1RWK1laM0t1WDd5VVRVd0VXTHVCSUo0VGNvcUZMTDdYcGZvNnBVRVo5QlJzdDkrS1F4MG0ybjkxc0dKK1EyZUNaV1k5VFJ3bEVOdk9ObFhkQWtmMDBzbDh0YThUeW93NVVHaXc4a2o2UGR5OFY0ZEdka28ycFIrclNXQXpJS3NQZTVLR3ZkYXRnSXpWYmtSbC90SUQ4RUJVZzE3ZFkxSWJiS2ZwdFJDM2xnei9jdEU5OEUxeCtveHRFc1pBZjRkWGhsMjBrUU84RWpuQ1V4SXhWd1VBNm5mNWk3MXZZRUJQdXdWampUNm5Mbk9OaFhJY2dJa2laUjQ3cU9VWnprZy83VUo0RTFib2FuYnV1ODJqV0pqNUpZMnZnY0ZyTGFMRGU2bXJreDcwMnptSVRDc0ZaTE9sbkpUemFEeXlmcG4iLCJtYWMiOiJmNTEzMmVhNjJhMjMwYjJiMjgzNmRjZWQ3YTEyYWU4YmY3YTEzZTUwZGYyYjRhYzJlNjNjMzdjNWM5ZWYxMTcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-972502484\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-986282644 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986282644\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-751198195 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJwK0s3YVVsOEUzSlVpY3owZkJyN0E9PSIsInZhbHVlIjoiSDNmbTE4WXkzU1RRdXJYR1dJR2g2a2FzbmNjbENPeTVDeGRid0FFQ2p4M2M4aWxPYzVNYXhUTmQ2MWVjbnp2cjFZK0lEUVRNeHdoRWYweUNpZk1aaGZBVUpSeFdaejM0bVEzRFBjbFZ4dEU3dEJBakV3VE1pbmtQK3AxREY4RFQ4VFZQNEdJUDZSSnhjWTlwWmlTUEFic1c0emZ5c0c1OTd5ZkpjRnhBTWx2eGtDWGlBMHFzbUxSQlcxVksvMXd4TnRVZUZXOE0zQ00xcUg1ZXdxdlEwL1RDQzJJYkREN1dSTVJJY1ZpcTBoSGlBd0VrNG13NndvYTNKLzhMYk96K3o2cHN5VFdEck5ZcU1raDArdjZCTUlzN1JMdkZJdU4wTktxWnluUnJwak1NUkNUd1hudEdPL0dVWGY1WU14d0lqa1I2aUQvd0VBZ0llSERGTjU1S2pvako3Z3dlUkIrQ3VTamovVEJNMmpuNTV1KzU5ZU9GTlk3YVZQL2IvNGtnUjNZSnBSRndsbmp6c3V4YnByRnFNM0RHbjR2YW5lTDBWYTIwMTVqemEyc3ZrZDI3Mm1kSnE1dUdGU29mMWdZZ3d2SnZ3MVNib3F5L0ZWZC9ncTk0SUJYSGJqbmEwSEdDclgrZWZkb3FKbVBOb2pwQnRlNmhlMkFoZWtFMks4U3EiLCJtYWMiOiIzYjAwYzlhMDFhMjQyZWNiYjM4NjIyYTk3MmFiNmEzMjdmMGFkYzZlYjBmZDMyZTFlOWZmNGM5OTMwYmIzMTVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdVSGJaL3ArbWhDUnpDd1NQMjY1OWc9PSIsInZhbHVlIjoiSkJsY0xnSHJUMzZZd0MyK2Y4cGhQUklqQ003REJQci9FTVhBc2FxNEMvWnRVNUxIYms1Sm1QLzk1Z1g0cTI4VzQyZUMzQXc3ZWtwQVlnblhTTG13SjhxbkRuQ2RGOUdwaUdJUnhWSHUyUUkzeFlWVlJZYVpFd3lBUmM2RCtIOGdYK0ZaYkRPWUZUd3RJRG02dVhQT0MyR0RicmEwWmdRWGUrQ0ovWFUwUWVrRnNVNUdvV0xUdVJJcDE3K0E0bko3ZUxaT2ZsVzNKWkxPUTAzcU0yM0ZxZkUzRGxWOE5qYmtRWDJrUlhMZzZNYVlhamcxdWNSV3A1RURQWjJTQkYzejQ0bGNNdkRGRnIyUHVGZUdzS1E1MmFpWXZwMG9KN0w2UDFMS2o1Q1FlcXVrY01Nd0oxOWZ3a2ZRS0F4bDR4WWdKRlgzZUNTTmQ1Z0lqYUY1SGVDaTg0Q3lGNWRadEFPbDY1MEducHhKbGpGOUdtT3FSTU81RTRZZWF0N08wSU1HalJFZitnQ2RNZC9waWJLUzdNSHkzdjFNd3Y3ZVA2d2J0ckFuYUo0WTNuNUpacWVGVi9WbHNEbnRnZngwVVRtalhQN0RXMXJHd25zaFBIeVpSdExpYmpaQTU5WnJzOW1uT3R5ZTNqbklnUzh5d2RPZytZNkt0aHlSZkttdmVOaFciLCJtYWMiOiI1MjIyM2Y3YTIzMjM5OTBkMGFjYTljNjM5NDBiNjZkODBhMmVmMzllODM1ZDU3ZmE1NDRjY2ExMWQ1NTM3OTU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJwK0s3YVVsOEUzSlVpY3owZkJyN0E9PSIsInZhbHVlIjoiSDNmbTE4WXkzU1RRdXJYR1dJR2g2a2FzbmNjbENPeTVDeGRid0FFQ2p4M2M4aWxPYzVNYXhUTmQ2MWVjbnp2cjFZK0lEUVRNeHdoRWYweUNpZk1aaGZBVUpSeFdaejM0bVEzRFBjbFZ4dEU3dEJBakV3VE1pbmtQK3AxREY4RFQ4VFZQNEdJUDZSSnhjWTlwWmlTUEFic1c0emZ5c0c1OTd5ZkpjRnhBTWx2eGtDWGlBMHFzbUxSQlcxVksvMXd4TnRVZUZXOE0zQ00xcUg1ZXdxdlEwL1RDQzJJYkREN1dSTVJJY1ZpcTBoSGlBd0VrNG13NndvYTNKLzhMYk96K3o2cHN5VFdEck5ZcU1raDArdjZCTUlzN1JMdkZJdU4wTktxWnluUnJwak1NUkNUd1hudEdPL0dVWGY1WU14d0lqa1I2aUQvd0VBZ0llSERGTjU1S2pvako3Z3dlUkIrQ3VTamovVEJNMmpuNTV1KzU5ZU9GTlk3YVZQL2IvNGtnUjNZSnBSRndsbmp6c3V4YnByRnFNM0RHbjR2YW5lTDBWYTIwMTVqemEyc3ZrZDI3Mm1kSnE1dUdGU29mMWdZZ3d2SnZ3MVNib3F5L0ZWZC9ncTk0SUJYSGJqbmEwSEdDclgrZWZkb3FKbVBOb2pwQnRlNmhlMkFoZWtFMks4U3EiLCJtYWMiOiIzYjAwYzlhMDFhMjQyZWNiYjM4NjIyYTk3MmFiNmEzMjdmMGFkYzZlYjBmZDMyZTFlOWZmNGM5OTMwYmIzMTVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdVSGJaL3ArbWhDUnpDd1NQMjY1OWc9PSIsInZhbHVlIjoiSkJsY0xnSHJUMzZZd0MyK2Y4cGhQUklqQ003REJQci9FTVhBc2FxNEMvWnRVNUxIYms1Sm1QLzk1Z1g0cTI4VzQyZUMzQXc3ZWtwQVlnblhTTG13SjhxbkRuQ2RGOUdwaUdJUnhWSHUyUUkzeFlWVlJZYVpFd3lBUmM2RCtIOGdYK0ZaYkRPWUZUd3RJRG02dVhQT0MyR0RicmEwWmdRWGUrQ0ovWFUwUWVrRnNVNUdvV0xUdVJJcDE3K0E0bko3ZUxaT2ZsVzNKWkxPUTAzcU0yM0ZxZkUzRGxWOE5qYmtRWDJrUlhMZzZNYVlhamcxdWNSV3A1RURQWjJTQkYzejQ0bGNNdkRGRnIyUHVGZUdzS1E1MmFpWXZwMG9KN0w2UDFMS2o1Q1FlcXVrY01Nd0oxOWZ3a2ZRS0F4bDR4WWdKRlgzZUNTTmQ1Z0lqYUY1SGVDaTg0Q3lGNWRadEFPbDY1MEducHhKbGpGOUdtT3FSTU81RTRZZWF0N08wSU1HalJFZitnQ2RNZC9waWJLUzdNSHkzdjFNd3Y3ZVA2d2J0ckFuYUo0WTNuNUpacWVGVi9WbHNEbnRnZngwVVRtalhQN0RXMXJHd25zaFBIeVpSdExpYmpaQTU5WnJzOW1uT3R5ZTNqbklnUzh5d2RPZytZNkt0aHlSZkttdmVOaFciLCJtYWMiOiI1MjIyM2Y3YTIzMjM5OTBkMGFjYTljNjM5NDBiNjZkODBhMmVmMzllODM1ZDU3ZmE1NDRjY2ExMWQ1NTM3OTU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751198195\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}