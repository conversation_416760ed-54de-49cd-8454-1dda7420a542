{"__meta": {"id": "X1423cf866ebd041dea0ea3c30198ce64", "datetime": "2025-06-08 13:19:51", "utime": **********.975139, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388790.76989, "end": **********.97517, "duration": 1.20527982711792, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1749388790.76989, "relative_start": 0, "end": **********.831613, "relative_end": **********.831613, "duration": 1.061722993850708, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.831631, "relative_start": 1.0617408752441406, "end": **********.975174, "relative_end": 4.0531158447265625e-06, "duration": 0.14354300498962402, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45179016, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.008, "accumulated_duration_str": "8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.911501, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.125}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.94352, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.125, "width_percent": 14.75}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.953654, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 71.875, "width_percent": 28.125}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2143017898 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2143017898\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1565906854 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565906854\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1075930822 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1075930822\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1344700905 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ind3RHFCeGJyWmwwQkNTdWI2UFNrakE9PSIsInZhbHVlIjoiNE50a3czSDRKblNrVkdySTNZa2dDZUczQVhFYUp5Qk5XcDl2ZFl3UUVrVEt0cDZyUVlNUjNiUjhvS25SNjZRM3BIblRJaURBSXVobjc4TEVZQTgvNDd2dW1hb3ozb3dFZXEwMEltMDMvNks0M2cra3FFMDNUMzVRYzMyOFBUWUYvUVUzSHk5NWlQTC9xYVJ0UjlqWitsU29xK2U0SXJVZFdZeDRsTXdicFZTZkxrbFRrVTQvakVZbEEwTTQzdWJGY3JjZ2lrS3N1cnlia3VYblFJT3I3RDY3ejJrNTZFRHIrbGpJbnZRWUY5S0VMWFdrc1V3TktuMWZJbGM2d2UzOTdLRkhOdG5wRUR6ajBrT1ZyS3I3bGpNNkcvTlhFZEdhbVpXZCtZSGtBYkJxMVBLdytoSEU3Z21zYnlMMFVIR3BJRFdkU0NnVGp4bTBxZnBVWlhGU1BxSng0am0vM3VIR1JiaUlXWHFBdC82NEUyeHVHSkpURVQrM0lYRjBMNmlBY0prU2Z4QjdTSmFnK0ovTWNLRGZUYkh3UStvY0s4S09HV1ZSZGZaSmpxSE9yOVRMaG9PY3BNWTV5MlVhblBPUzNGc0NTYmFRTFhPZWJyTEpWYWxHRDlucUluZytWSmpOL3I1UHFOQ0o2NWl2bjhxa1NBc1hDS254QisrSEpBRUgiLCJtYWMiOiIxODBiZjExY2Y1OTcxNTJlZGE3N2FiZDBjOTVjYjQwNzdhZmJiY2VlZDBlMTMyYTIzMzU0Zjg3M2IwMTUyZDNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilh2MldIQ21JT2RkZkU5amowNUhJNHc9PSIsInZhbHVlIjoia1E2TFZFdXNZdkxnZElydTQ4RitoekxzbTFFVC8rNjlUZURxQUo1UFdxbHYxZTdPK05GTWhhaWNvczcxdkowNEgrTXFnY0VwYlNLc3VPNFhaMWI3QUR5N1RDY0dGcmRDMTkrR1dITGZGZjRORzhuWnpJYjgwZFB6SkN6eERnRkwxSUYrM2JnWmptTWpnZ0tHbmlKSHBzT0pBVlpGWDVkd2RyV2owWWVYUlhjei91TWN5dHZwVjZmTjU5VDZ1LzlzQzZzcVFYb3B0ZUluQlRSQ0hEVjIzRGdVbDB4UkZxVVI3dXhRdUdIWjhvTnpycHcyYndqV1Y1TGREQ0pSU3VNYlJpQ1B6R1cyaG5VR2FjY1d3QlhOcDhFSDNrd1NiMXFiU0pTQVNmdDRSR2dHMHI1N1M2NXRXVXEwTUsveHp3T2RGT3JLZWRoQitnK0s1UXluTmwzUTVTZENsc1oybElaeFQ5VU84NmJvZXNXbXhiOVFOSmpLNTVvZU1Xc2dIQlFMRHFaSGMxOXQrT29JRG1hdHUyS1lHM3BBbmNKL3VhRXNUUWlTVXdmZjdOTDI0NXg5VEZTYzl5cm1QTTJ6SjhxRm5YVVVmcm9SdVhPSXJxQ09jMWczVDN2TnRFWkcvYzd0V2NUbWMrQjZjU0lZZkpCRlNYRXozdFRnck1DYTJrN0kiLCJtYWMiOiJhY2JlZjBkYjg3ZWFjZWFjYjhjYzA4NWMyYzA0OWQzYTkwOWY5NmM4NGJjMjVjMjhjNDE4YTRhNTc0NjhhNzc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344700905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1287682421 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287682421\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZDTGo3dzZGUWJNb0E0NzhrSWRtZXc9PSIsInZhbHVlIjoiR2tQTFpzQ2s3NVdjOEdmYU53b2F5dnhJaDBlcDBMQ0Z6WTE4ZVZTMktsZFNVZlAxR0FVQ0ZhcWRsSjhSYVFrY1pFTjdFeVV0bDFJa1BOSjRRb1BETDJ2MXQxclBrbWcwK2tTT0FhSXI5MnlnUEhxOFZBMlBZN2U5TEUwY1U2ODB5SUhLQUxxNTgycWtVK1N0N3NKQnByRXoyUXhCaWM4Rkx5L0FSOU5wSHRxQnBKTC9Jc2FlNHAySHo2Qkc0TjA0WjdoNmEwaENDemVsWVMwb0pSaU1seVR6bXFMTXdWdlpJQjhvYjVQTUNQMXpFR3N4RVJnQlBuVmszSm1PY2NWQW56N2RaREhpNVRQSHdoYVVEakVlMGFOUnR6Q0FQOUQ4Ykw4SHZqa2JqT25EQVNXNU1lWUdrOGw2UDJHUHpyazZvWklpMkNsTDRDYXNtYlpmSW1VOXZtZkVUa1NRZFlaRWF0TFQ2ckJZYzI5WURpaVRmcmVTZks4Y3pJWDgyNDBsa1lmWWpyRDNmTzh5STZ0bHUwWUNVOC9QeU9Oc3hoTjJSYlNKeXl2Uk5NMmlncUJsa0hVSXozVUowTnZJdlU3K3loMzFNZG5WTnM3K0xuRlREYjdZY01rbDVYblM3dFVIVEtuWmFKN0pWS2RzZTNBNWltN1FmSjJORTVrYlFJdzYiLCJtYWMiOiJjOTFlYTkyZDhlYTYyM2Q5OTI5OGRmNjljMzk4YjI1NGY4NmJjMzI1Zjc2ZjcxMTQ5NjFmNTk1NTQ2ZGY2ZGU1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFXYldsckY2VXVlUytlT0syWkRaaWc9PSIsInZhbHVlIjoiMm4waFBLbzRZaU5Cdmt5TSs0OFdVVE1VNWFwMS9lTFZYdDNzdTV3dkR4ZGJXbmozcGRBU1NqNXd2NEtqOTNxaVZOcWJhUk1HR0NETWh5aVNIcGEzUURodGY5Q3FTdGtVUnpiOXdKRUFhNWszZzFjUVp1TnlGcGVmQ1NlNGc5NnBYemRBeVNDOU5nMTR1a0pYWDJBaUJKTzkwQ2x1WDJLRFZVS1l0TWI0amxpb1VEb3lNOU5HR1FZQmRhb1NnUWxuRTU4VE5aTURpU3JZQUh3WUovN1JSa044S09KNnJ1RkkwSHdCK2x1dnBYdVVhcHZLeVZyUDBrSi9QaTBhS3hIelN0TlozS2h0dnhUMHYwQk13M1E0eWdabGYzcjZybVJMREM5a2dLUnlydHV4cDNlVjhIUURQVmprZnBwV21wZkdRQWFpbElMbTdqbzRETW9UaGFOYVZWdU5DVjkzSGhaUmJ2THBNclRrS1FQdVI1Q0gyZmFNWmpLMVllYUtpVTF6em5KZlBoaXBaTklXcWFSZnRvbUhKeFQvUmlnUHk2Q2EwcjJiUTJVa3ZiUXpZSXU4ODVaaHJwc2p2MGppS3FoQ3EyRm5MS216aFZpaUJmVGw5cDJ2ekMvL3Z6MitCUjdNSW9kQjltYlBGNUZDNy8yakxqRytLOGN2aTliUFRrOFgiLCJtYWMiOiI1MTRhNjlkYTc3ZmFmNTAxNDljYzQwMTJiOWE2MGVjMzI4NzliZTk1OTRmMzIzODkyZTc1ZDI1YjAwYzdkYTY1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZDTGo3dzZGUWJNb0E0NzhrSWRtZXc9PSIsInZhbHVlIjoiR2tQTFpzQ2s3NVdjOEdmYU53b2F5dnhJaDBlcDBMQ0Z6WTE4ZVZTMktsZFNVZlAxR0FVQ0ZhcWRsSjhSYVFrY1pFTjdFeVV0bDFJa1BOSjRRb1BETDJ2MXQxclBrbWcwK2tTT0FhSXI5MnlnUEhxOFZBMlBZN2U5TEUwY1U2ODB5SUhLQUxxNTgycWtVK1N0N3NKQnByRXoyUXhCaWM4Rkx5L0FSOU5wSHRxQnBKTC9Jc2FlNHAySHo2Qkc0TjA0WjdoNmEwaENDemVsWVMwb0pSaU1seVR6bXFMTXdWdlpJQjhvYjVQTUNQMXpFR3N4RVJnQlBuVmszSm1PY2NWQW56N2RaREhpNVRQSHdoYVVEakVlMGFOUnR6Q0FQOUQ4Ykw4SHZqa2JqT25EQVNXNU1lWUdrOGw2UDJHUHpyazZvWklpMkNsTDRDYXNtYlpmSW1VOXZtZkVUa1NRZFlaRWF0TFQ2ckJZYzI5WURpaVRmcmVTZks4Y3pJWDgyNDBsa1lmWWpyRDNmTzh5STZ0bHUwWUNVOC9QeU9Oc3hoTjJSYlNKeXl2Uk5NMmlncUJsa0hVSXozVUowTnZJdlU3K3loMzFNZG5WTnM3K0xuRlREYjdZY01rbDVYblM3dFVIVEtuWmFKN0pWS2RzZTNBNWltN1FmSjJORTVrYlFJdzYiLCJtYWMiOiJjOTFlYTkyZDhlYTYyM2Q5OTI5OGRmNjljMzk4YjI1NGY4NmJjMzI1Zjc2ZjcxMTQ5NjFmNTk1NTQ2ZGY2ZGU1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFXYldsckY2VXVlUytlT0syWkRaaWc9PSIsInZhbHVlIjoiMm4waFBLbzRZaU5Cdmt5TSs0OFdVVE1VNWFwMS9lTFZYdDNzdTV3dkR4ZGJXbmozcGRBU1NqNXd2NEtqOTNxaVZOcWJhUk1HR0NETWh5aVNIcGEzUURodGY5Q3FTdGtVUnpiOXdKRUFhNWszZzFjUVp1TnlGcGVmQ1NlNGc5NnBYemRBeVNDOU5nMTR1a0pYWDJBaUJKTzkwQ2x1WDJLRFZVS1l0TWI0amxpb1VEb3lNOU5HR1FZQmRhb1NnUWxuRTU4VE5aTURpU3JZQUh3WUovN1JSa044S09KNnJ1RkkwSHdCK2x1dnBYdVVhcHZLeVZyUDBrSi9QaTBhS3hIelN0TlozS2h0dnhUMHYwQk13M1E0eWdabGYzcjZybVJMREM5a2dLUnlydHV4cDNlVjhIUURQVmprZnBwV21wZkdRQWFpbElMbTdqbzRETW9UaGFOYVZWdU5DVjkzSGhaUmJ2THBNclRrS1FQdVI1Q0gyZmFNWmpLMVllYUtpVTF6em5KZlBoaXBaTklXcWFSZnRvbUhKeFQvUmlnUHk2Q2EwcjJiUTJVa3ZiUXpZSXU4ODVaaHJwc2p2MGppS3FoQ3EyRm5MS216aFZpaUJmVGw5cDJ2ekMvL3Z6MitCUjdNSW9kQjltYlBGNUZDNy8yakxqRytLOGN2aTliUFRrOFgiLCJtYWMiOiI1MTRhNjlkYTc3ZmFmNTAxNDljYzQwMTJiOWE2MGVjMzI4NzliZTk1OTRmMzIzODkyZTc1ZDI1YjAwYzdkYTY1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}