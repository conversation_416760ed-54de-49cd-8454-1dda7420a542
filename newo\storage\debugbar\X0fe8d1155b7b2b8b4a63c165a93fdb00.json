{"__meta": {"id": "X0fe8d1155b7b2b8b4a63c165a93fdb00", "datetime": "2025-06-08 13:19:47", "utime": **********.146813, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388785.792128, "end": **********.146844, "duration": 1.3547158241271973, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1749388785.792128, "relative_start": 0, "end": 1749388786.998781, "relative_end": 1749388786.998781, "duration": 1.2066528797149658, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749388786.998806, "relative_start": 1.2066779136657715, "end": **********.146848, "relative_end": 4.0531158447265625e-06, "duration": 0.1480419635772705, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43912608, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0138, "accumulated_duration_str": "13.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0985022, "duration": 0.0124, "duration_str": "12.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.855}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.124881, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 89.855, "width_percent": 10.145}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1772208685 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1772208685\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-87764405 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87764405\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1083279158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1083279158\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhJcGRnVS9MZHcwR1VxYU5OZkRpdVE9PSIsInZhbHVlIjoiUm9tbG1DRi9ZRzlBODNjSkxxWjVDeGwxVVNoRlBjc2VwbWdsVDd5ODBTay9yd3RyM2MwTExHaWxUMURTMTE1bzBTT1pvZ1JHNDRrZjIwdm1UZDh3WDEzTFBucTViZTNLV3pqWE5kb3YrbCsrT2ZrMjVZUXFqeXJzclJrY1NMSjR6L2hVRkZpRVhpbXZNL1BsMFRmaDR3a3VxdmorSEtSS0VvZlQrSld2WTN3ZlVYbzJkZXhWZDFya05PcjNOQ3J6NFFFa3A3MzlvRTRKZkxSU1NwaG1VYVdlVkpKVnYwSlBpeCtrM2RLWjlLU3BIQmZnU2M1ZVFMbWNvTUpjUVVYcS9EL2tQb2VjcmF2NXQxZFBpODNldmdSZFFJV3YzSkNHVE1nMFBoRW93N1Y0ckF1R0dCSXRHeFg0TXpyVTJIYlU4b2dabEZoNFQ4S2tPZkl4S3pDRU9IeHBQU1BQeUFaTTkrV1lyaVRneDJaY0JTd0l5dTZ3bEUrRGVpQXZ6a3dVVU1USC9vMUxSWmFZeW50MUc2WWUzdFNiNXZrUitKQjE3eVNtbGdWM1BqdFlTZHNGbFppYVV0VmtBalpHZ21rTS9LOHhtVXpxSTR3eEhvaU1CLyt0cHBVQWZ0bFNnOUZ6Q2NlUS9sRzdwQ2dhb3BMVUZrRWYrbnFKRE5oalhHZFQiLCJtYWMiOiJlZWU4NjM3MjgzYjBhNzdmMDdmODkzZmY5MTMxNjE5ODI0YmQ5MWFlMjE0NzkwNGE5YmM4OGE1ZGNmNmVjZWE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilo3eWJNbHhLQkpXRHRtbE9TaHBRb0E9PSIsInZhbHVlIjoiL3lkRGlVd3lJRG96cFdpQXZBYy95VlpUR2tSVWJBWFo4ZFVsYTNvSVJvbFhHam40dlltbGwxcVdyQXBod1NKUFpqYkdhTGRZcjBMTnFGeS9OQVhXaUVZa2UwcVd1TjdCWnNHRkRYN0hlbDREczhzNXhjYkFoOWIwTnpPNkhpeHpyck5GMVZ0UHpTYkRhcXkyVjJpY1Z3ZTI3YzN3NlFrS0lTTDV2VjdSaGc5UHRMUVQwa0ZXZUEyOUVHQmZBZWw3Q0pFd0dUV2I0V25xaUhDekt5NHpJbUExUVYyWXlTVzhUUFMwZXJLd01UeS80eHpiKzVPTytxQUxjbEg4a21IQ0NSL2FZUysxeFZBZ0ZHbWpQQlVEL3VGa3lLR0dCVVBIUFhsaXJVVU12TDlTemw2OXZzTm9uamJkdVFsRmNaTFNkK0FyNVdEOUNjY2NERVE0OFhDUVlLcEdxV1k1akZuMEFwcTFkalFTc1h4U0VrYzlzeUpCeTJ2WkRsZEhiS2ptd2ZuVHdhR2lmK3RBUEZSNkk0RndUWnhpcDNNenpmeUVIOXYzUm5OSWZLb3VERkdqb2hub2VpaE4rb0tORE9oUzZ4UVhWRVRINGZteTJyMmQwZG0xTXFqbUNPRUQ3WVdNOU1XL2tRZlYrdE8wK3RjTGsxdHBNMWFEZGdPekhsQU8iLCJtYWMiOiJkMTY5OWJlZjgyOWRmMjRhZGI5ZTVlMDNjZDI4ZjdhZDJkN2IwNmZhNGU3NTBkY2M2MjI0OWIwMjZkYjc1YjE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-80293554 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80293554\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-382817514 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZkblM2OUxpZmhSdHpPYTV1eWFXMkE9PSIsInZhbHVlIjoiZGV3QmFEWEk3QXRGbFZtUDM2NzhhOEZrWDJjTGNFRUprMlpkSXJkZFhvSkxhb2FJK2c0ZlBhY3c1dHpwN1UyY3BKSEYrQzZMR2c3Y2NlVklDQUpZUnB3VERzUVY4cEhITlJFRmNERlpJZXcxcW0xT2N0V016WFdzdFB3WmVnN250ZjVBYVZtcjR6SkQ1Z2c5V1gvMkJCUmtZNU5TaWU2d2lCcW5VaHM0ODIwb1BmZjJjYjBMWXhIQTdma2I3VEZSK3hXVWtGYmE2NGhLZlFDRTQ0aEQ0RFYyVUcvQUJJY3h2SkJRcEVUT3VzTHh2dkdtaDJwUkRVV3FRd0FKSEh4YXgvMGd5Nzc0aXQ4QnZ3d2lTRFppaGdQc2gyTXdJbG1odnZOMkRvMUVrWlExelorMjJscjN5TEdFK29yMzJzMXVhdE9pZmFWK0prUXk3a0QxRG5vR0dJVGNVcVcxVDBSeEFOaVk4K1BXd1g2bE40bkp5T1hlaWRRMUpLNWdsZUtGQVNsb2gxZysyUE9lb01YOWYzWms2Mjg1d3FDTnNHWVJvL1J3Tk92Rm1EOGJDbkUwaTRZM1JqYTh4eXgwekRQSUhMa1h3aHNISGRyM01GcTZiTi9pTUFobGVQZEpOb1dBQWlTdzlLS3pTYktmVkRiVnl4TWtkem9XdTlkazR4cjEiLCJtYWMiOiJjOTAwMTdjOWIxMjdkNDY0NTMzMmYxNzIwOTAxNDcyNjlmMDljNTJjNWY0MTU0ZjVkZGQ0ZDQzYTRkZTE1ZWI5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNWdHRHMTFOOG0wL09UT2s1MVMvWkE9PSIsInZhbHVlIjoiWnpVaGV0Y3FEbWNtT0l6c1Vwd1RaYTZIc1JjRVNnOTFUVnRQYk12ME9VQkFVeDZhaHU4dXFaaWhpTG5QQnM1cXRFMUl3a0lMLzY2S21yZ0tnWmJMbjVMTC9hKzduNjB1UERYRXY5bFlFRWhRTGhzK09OMGw5Z2RYTDd4Y1h4R1ZlVnorcmVGMW5vbFNyblgrM0pSbzl0bHJZVUh2OERobUJwMVBTdW0yVUd1UXoyUjNreTN4bExwcHBGTnlJTzAvc2tjQVg3U2VERW1td0t3cFRKcXErNWUwSHlON1RUOUhnYkVXTTRZN2Z6UytqU1d0Mllma3pXcUFweVF5bndiWjI3Wkt1TGJ3emkrSjJ2eGwxZ1hzSFV0MkhuaVUwdFdjMzRCWFo3enBYeVdieUlXbUdpcU4wYzBDK3hIRjVoYTQwa3lrKy80NElYOFc1VEFENE02c1BqYjEvMDZaeVduZy93MUdhZTZWS1hxbXFZbFdqY3hSWm9RMVpBSEtBcmwydEJxcDRJenNjT2VpZndoQndwS1JhYkYwTldPeS82d053dUZFK1dDbUVwbGZEZVkrOEhQZWVLU2grN3djd0prT28zVGs1c2xlaENVNWpvckZ2UHpZUUdNdHU5ZlEwTmNDdGMxSlZyaEszY2hZZ29JMEhnVE42QUlsUVhPcFZGM0EiLCJtYWMiOiJmOGFiNzk0NTMzMzY3MGQ3MDdhMTQwY2E1ODNkNzhlNjUyMzVkOTZhN2I4MTZhNTdlZTQ4YmIzNTVkMzlkOTQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZkblM2OUxpZmhSdHpPYTV1eWFXMkE9PSIsInZhbHVlIjoiZGV3QmFEWEk3QXRGbFZtUDM2NzhhOEZrWDJjTGNFRUprMlpkSXJkZFhvSkxhb2FJK2c0ZlBhY3c1dHpwN1UyY3BKSEYrQzZMR2c3Y2NlVklDQUpZUnB3VERzUVY4cEhITlJFRmNERlpJZXcxcW0xT2N0V016WFdzdFB3WmVnN250ZjVBYVZtcjR6SkQ1Z2c5V1gvMkJCUmtZNU5TaWU2d2lCcW5VaHM0ODIwb1BmZjJjYjBMWXhIQTdma2I3VEZSK3hXVWtGYmE2NGhLZlFDRTQ0aEQ0RFYyVUcvQUJJY3h2SkJRcEVUT3VzTHh2dkdtaDJwUkRVV3FRd0FKSEh4YXgvMGd5Nzc0aXQ4QnZ3d2lTRFppaGdQc2gyTXdJbG1odnZOMkRvMUVrWlExelorMjJscjN5TEdFK29yMzJzMXVhdE9pZmFWK0prUXk3a0QxRG5vR0dJVGNVcVcxVDBSeEFOaVk4K1BXd1g2bE40bkp5T1hlaWRRMUpLNWdsZUtGQVNsb2gxZysyUE9lb01YOWYzWms2Mjg1d3FDTnNHWVJvL1J3Tk92Rm1EOGJDbkUwaTRZM1JqYTh4eXgwekRQSUhMa1h3aHNISGRyM01GcTZiTi9pTUFobGVQZEpOb1dBQWlTdzlLS3pTYktmVkRiVnl4TWtkem9XdTlkazR4cjEiLCJtYWMiOiJjOTAwMTdjOWIxMjdkNDY0NTMzMmYxNzIwOTAxNDcyNjlmMDljNTJjNWY0MTU0ZjVkZGQ0ZDQzYTRkZTE1ZWI5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNWdHRHMTFOOG0wL09UT2s1MVMvWkE9PSIsInZhbHVlIjoiWnpVaGV0Y3FEbWNtT0l6c1Vwd1RaYTZIc1JjRVNnOTFUVnRQYk12ME9VQkFVeDZhaHU4dXFaaWhpTG5QQnM1cXRFMUl3a0lMLzY2S21yZ0tnWmJMbjVMTC9hKzduNjB1UERYRXY5bFlFRWhRTGhzK09OMGw5Z2RYTDd4Y1h4R1ZlVnorcmVGMW5vbFNyblgrM0pSbzl0bHJZVUh2OERobUJwMVBTdW0yVUd1UXoyUjNreTN4bExwcHBGTnlJTzAvc2tjQVg3U2VERW1td0t3cFRKcXErNWUwSHlON1RUOUhnYkVXTTRZN2Z6UytqU1d0Mllma3pXcUFweVF5bndiWjI3Wkt1TGJ3emkrSjJ2eGwxZ1hzSFV0MkhuaVUwdFdjMzRCWFo3enBYeVdieUlXbUdpcU4wYzBDK3hIRjVoYTQwa3lrKy80NElYOFc1VEFENE02c1BqYjEvMDZaeVduZy93MUdhZTZWS1hxbXFZbFdqY3hSWm9RMVpBSEtBcmwydEJxcDRJenNjT2VpZndoQndwS1JhYkYwTldPeS82d053dUZFK1dDbUVwbGZEZVkrOEhQZWVLU2grN3djd0prT28zVGs1c2xlaENVNWpvckZ2UHpZUUdNdHU5ZlEwTmNDdGMxSlZyaEszY2hZZ29JMEhnVE42QUlsUVhPcFZGM0EiLCJtYWMiOiJmOGFiNzk0NTMzMzY3MGQ3MDdhMTQwY2E1ODNkNzhlNjUyMzVkOTZhN2I4MTZhNTdlZTQ4YmIzNTVkMzlkOTQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382817514\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1169543056 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169543056\", {\"maxDepth\":0})</script>\n"}}