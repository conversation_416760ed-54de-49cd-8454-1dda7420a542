{"__meta": {"id": "X8430d8e146b3ea93cd007dc48a1e4bbb", "datetime": "2025-06-08 14:14:53", "utime": **********.21682, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392091.510434, "end": **********.216851, "duration": 1.7064170837402344, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": 1749392091.510434, "relative_start": 0, "end": 1749392092.888338, "relative_end": 1749392092.888338, "duration": 1.3779041767120361, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749392092.88836, "relative_start": 1.3779261112213135, "end": **********.216854, "relative_end": 3.0994415283203125e-06, "duration": 0.3284940719604492, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53129936, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1320</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01613, "accumulated_duration_str": "16.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.019989, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 33.54}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.05783, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 33.54, "width_percent": 6.2}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.110183, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 39.74, "width_percent": 10.477}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.118648, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 50.217, "width_percent": 9.485}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.134654, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1236", "source": "app/Http/Controllers/ProductServiceController.php:1236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236", "ajax": false, "filename": "ProductServiceController.php", "line": "1236"}, "connection": "ty", "start_percent": 59.702, "width_percent": 8.927}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3, 5) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1431189, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 68.63, "width_percent": 12.461}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.157892, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 81.091, "width_percent": 8.06}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.167268, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 89.151, "width_percent": 4.588}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.196999, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 93.738, "width_percent": 6.262}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1752682153 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752682153\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.132006, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1003195743 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1003195743\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-248006990 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-248006990\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1478684981 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1478684981\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1753333275 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749391833855%7C52%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAwVVBVbmIwN2dzT1BjQ0JRTEtkL2c9PSIsInZhbHVlIjoiczFwc0lyc09Sb3lTNWVEUlh4TlcwNy8zMlN1alV0ZVhSR0JoMFN3MHlWbGVtMTk3bElCV3BOaUIxeTVIaWxqVTU4c3ZqbTBid2YzdDlmdGtNUnBROS9pbDJrUEVmeHNVSGlMbm5hWmJFUi82TENCWHEvdzNRa0cxa0xLUGkyOG8vd0dZU2l4Snd1enp0MWtSUlpyQmFnY1YvWHVENTVpTjBLQkxQWk9GRWlJZWRkc0lFL1orZmNzb2VPRUFDOWRaY0pJcG1qKzFOeTllY3I5eFp0aHJSckpXaTRPb2pCRXgzVkErTGN6Y1BPaCswOWpYZm1VOW51M3ZGZ2VYNitLU2tNa29nRFlnK2JCRFRzUTMvaVpsWHphTXBlSjFzTXQzNWFnREZBMFJiUWlWbGFQR3VZeW5RejR6emRXNDh0aDhKNEhqV29KYW5lbVdkV2VuK29iY01zTSsrZDhDVzhWRzlGWG9rUmhpN25VeS80UHpRSStMbHdJUWNFTzJKYVh3N3MwL0c4OGttTFluOXBVUno3b2F3QzZwTmtTdWxpb3cweE9paEZQV2RHN21qemtOYVVuUkRtQXNBR2h5QUNzWHFnWi9OdVB2bSswSGpnK0NtM1I2c2JhWUxiR2FSa0RpTjRQcURtaVJIdkg2OHlzS2YvYnVHTEFzWUpqMTdiKzIiLCJtYWMiOiJmMTU1OTAyMWM4N2IyOTFlZWI0ZjJjYjc0ODhkZDUwMDgwNmNmOGZmNGIxOTE3OWQ1YjI1NjE1NDYxMWE5OTc3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRaWVdLRXhRd3JVc3RBNm9kM2VXQXc9PSIsInZhbHVlIjoiMDc0OTZpbFhsRmduZms5LzI1ZXlCWTdPbGVTb2QrSWdKUmowM2E0eUs1MWcxOEdtK1ZyVmV2Q3VNcUkyc052NnFjNnlxSUROZEVtQzdRTGJLaEFRWkFpREdyM3prNDRpT1RGZmh1a2hSbXZ5aUlDSjZXQmVyMlQyY21sL0kxblpRSVZ6YklGemhpTGVad2FCWXk0OEE1citidTVLcXFuQXpnMmgvVHM1T2dHZXBOazI1bU1NQ1ZJNmV5d2kyRk1xZ0dSNGdmdjdZaXIzUmlTc0VuZkFscFN6YkNSbEFOZ3lBeGl5MlErTW9iRnQ2UHJncTFvbmUwWGJxb3dNNzBJckNUZmtoakJMVGp4dEZCbDVvMXFKVHp6Nm9pK2hVL0lTODE0MWJuQWV2SkdhNHJXYjl1bnd1QjU1K0h1VTVnRkNuTCswNVFXYlRNOVZxZ0NMTk5zSXNpMlBsVWdCVXhuYklGZ2srTVkwWENRczNBRG1sQ1lVbnRWNSsvdHh2eTFiZjgvamJ6aWpGUnFQTjc3M2E3UzZSMkNzL1ZXclZWcXdNVHNMcmFLVHJPYkYzOHFreDJqN2sydDUyZS9GRW5ZQkx0M1NKZmVzZWpLUk9VenRGSGFNOWFBa0NlVDVhVDI1UG9HMjNBSlBoRjBjQjlqZHNjaHZjajVkaCtsOW5FQ0wiLCJtYWMiOiJmZjRlZjdlYTkwMDgwYjFkZGY2MWRmMzZhODIxZjk2MWY0ZGJiOTRlYWIzOTBiZjBmMTNiZGY4MTBmOWJlZjhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1753333275\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1557946124 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557946124\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1366999674 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:14:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InV4bGl4VkVBQ0ZCalZ6c0lONXl2a0E9PSIsInZhbHVlIjoiS3ZtRzFhbm0rMFlRZ2IycnovY2Q3ajZhelN3aHA1R2hiR2NoR1pnRU1CL1c5QWVyT0NCSGNLVXpOTEZjU1RPUDVNNDY4Zm9pWGh0aWZsZFhoclpWWDNnZ0lqNEJmTWpxNHRMQ3phYjZzU1VoMnUyeUVUaFI0cFJ6cFg4a2Y5Z2h5ZXhMTFRsaVJCdXdDRElkSGVBN1kzRFQ0SjJPSGpDVlJyNVExeWRqQitCVnAwcUVVUWl1TzJwQ25qdzkyOEhRWndCZkg0eXFqQnJYc0ROa0dFeTZVNUEvdW83U3BWakRtbHVNdWlYV3V1aDlmRm5Hb0FiUGVwK2NoWVJYUFZKTDQ5UE1IU1B6TTYzek11T1VUNFkxNWtlR2hONzFnQjVCanpKVlVNUnN4T2ZpaXlLYWdOY05CMWloZU1wWHFWUzFzZDk5aVVXMnJxNzBCQSt4K0Jpb3FQRGgwRzdCNm5STFZjR0IxdEFxNDR0WDhOT3N0TXJCWk40MGIyZFhYNlBYMkc2WU93Qk1TUTc1SEtuTVA0MnVFV01XaGZTZFBmSGJxdGZXaitMU1EvMEkzYzBzaXBOdURXb2ZKM25ncnZDSjM5aWJYMytpT2lKY0w4Qlp4ajk0U0xHR2NCZ3dsSFJneDJZZkE2Q3JpVjJ6UnAvS0Zvd05TL2dSUS9tTDh6cjgiLCJtYWMiOiIwMzI1MjAzMzYxYjA2MDNiNmEyM2MxMGMxNjNkNTM1ZTVmMmJkOWNjNjEzZjM3ZThjOGM1NjQ0Njk1ZDlhYWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNwVjhBUktzUFh6VjQvbjdnUmM4L2c9PSIsInZhbHVlIjoiVXh4ZUJCdzJyM3ViUnRFT1hsUlBZR0ZOcjBvOENYOXVvaUJkZmNIRnczeVA5QXZ2VGc0TXRGZjBMVkpTRVhWczgya0hULzhYdTNvdG9nTmJ5YncxMWpNdi9RbFhSd0lXSTBmVWNtRXlZU1JVb2hWNWpkNzJ3eFZDVWhXQ2hWNTZ4UVFpRVh1cFd3UTh6cVZkb1haSDFkc1BKcWVhNUQrQWVYZklZVVE3TUxIL2pEUXlWTkw2emFqTlRyd0JON0F2VWU1NU5lcjE2a2oyZi9kcnNma1NZaTgvRzhrdVVXdndTd1oyRjdrcXo5aDVsL2ZrTUxwckFnSGZQUFR0bDhnZ1Y2a1dCNTFXR3lZYTYySmdxWU01b05LUmpTOExXdVh0dUE2ZWxLeG1ocWlsbmg3RWNic2JmSjN0M0JnZUpNK3JYUWl3cWlrRU9QSzRVOW1zc0xtUFRORzJPTytRT0JRa3QvWTU5SXRUcndJWHZrNGNIODZNK0ZycVZ4UjBBdnlwRmduc3RDRGJMK0FrSlJycWxZNUR2Vk1lMno5Rm5wUWFuemhCTGJTNmdoeXJiaHNrWWY2N2V4aTVxTXMvV2JGbHlNalMrVUlGaFpycmpPVjVHelQ3QVFvTkUyUk5pbTZaM1RCM0k5ZnBTTGpYZmp1TnJHYkVBVzNKUWErS040RTYiLCJtYWMiOiI5ODJiMjlhMzc4MTBkY2ExZGU4ZDY1Zjc1NmFmZWRjYjllNGZlMzlhMTNkNGFkMjBkY2U3YTIwZTM3Nzc0Yjk0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InV4bGl4VkVBQ0ZCalZ6c0lONXl2a0E9PSIsInZhbHVlIjoiS3ZtRzFhbm0rMFlRZ2IycnovY2Q3ajZhelN3aHA1R2hiR2NoR1pnRU1CL1c5QWVyT0NCSGNLVXpOTEZjU1RPUDVNNDY4Zm9pWGh0aWZsZFhoclpWWDNnZ0lqNEJmTWpxNHRMQ3phYjZzU1VoMnUyeUVUaFI0cFJ6cFg4a2Y5Z2h5ZXhMTFRsaVJCdXdDRElkSGVBN1kzRFQ0SjJPSGpDVlJyNVExeWRqQitCVnAwcUVVUWl1TzJwQ25qdzkyOEhRWndCZkg0eXFqQnJYc0ROa0dFeTZVNUEvdW83U3BWakRtbHVNdWlYV3V1aDlmRm5Hb0FiUGVwK2NoWVJYUFZKTDQ5UE1IU1B6TTYzek11T1VUNFkxNWtlR2hONzFnQjVCanpKVlVNUnN4T2ZpaXlLYWdOY05CMWloZU1wWHFWUzFzZDk5aVVXMnJxNzBCQSt4K0Jpb3FQRGgwRzdCNm5STFZjR0IxdEFxNDR0WDhOT3N0TXJCWk40MGIyZFhYNlBYMkc2WU93Qk1TUTc1SEtuTVA0MnVFV01XaGZTZFBmSGJxdGZXaitMU1EvMEkzYzBzaXBOdURXb2ZKM25ncnZDSjM5aWJYMytpT2lKY0w4Qlp4ajk0U0xHR2NCZ3dsSFJneDJZZkE2Q3JpVjJ6UnAvS0Zvd05TL2dSUS9tTDh6cjgiLCJtYWMiOiIwMzI1MjAzMzYxYjA2MDNiNmEyM2MxMGMxNjNkNTM1ZTVmMmJkOWNjNjEzZjM3ZThjOGM1NjQ0Njk1ZDlhYWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNwVjhBUktzUFh6VjQvbjdnUmM4L2c9PSIsInZhbHVlIjoiVXh4ZUJCdzJyM3ViUnRFT1hsUlBZR0ZOcjBvOENYOXVvaUJkZmNIRnczeVA5QXZ2VGc0TXRGZjBMVkpTRVhWczgya0hULzhYdTNvdG9nTmJ5YncxMWpNdi9RbFhSd0lXSTBmVWNtRXlZU1JVb2hWNWpkNzJ3eFZDVWhXQ2hWNTZ4UVFpRVh1cFd3UTh6cVZkb1haSDFkc1BKcWVhNUQrQWVYZklZVVE3TUxIL2pEUXlWTkw2emFqTlRyd0JON0F2VWU1NU5lcjE2a2oyZi9kcnNma1NZaTgvRzhrdVVXdndTd1oyRjdrcXo5aDVsL2ZrTUxwckFnSGZQUFR0bDhnZ1Y2a1dCNTFXR3lZYTYySmdxWU01b05LUmpTOExXdVh0dUE2ZWxLeG1ocWlsbmg3RWNic2JmSjN0M0JnZUpNK3JYUWl3cWlrRU9QSzRVOW1zc0xtUFRORzJPTytRT0JRa3QvWTU5SXRUcndJWHZrNGNIODZNK0ZycVZ4UjBBdnlwRmduc3RDRGJMK0FrSlJycWxZNUR2Vk1lMno5Rm5wUWFuemhCTGJTNmdoeXJiaHNrWWY2N2V4aTVxTXMvV2JGbHlNalMrVUlGaFpycmpPVjVHelQ3QVFvTkUyUk5pbTZaM1RCM0k5ZnBTTGpYZmp1TnJHYkVBVzNKUWErS040RTYiLCJtYWMiOiI5ODJiMjlhMzc4MTBkY2ExZGU4ZDY1Zjc1NmFmZWRjYjllNGZlMzlhMTNkNGFkMjBkY2U3YTIwZTM3Nzc0Yjk0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1366999674\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1647324157 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647324157\", {\"maxDepth\":0})</script>\n"}}