{"__meta": {"id": "X89dfd33805b22657b43cc411feb10e6c", "datetime": "2025-06-08 13:07:09", "utime": 1749388029.079684, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388027.738381, "end": 1749388029.079715, "duration": 1.3413341045379639, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749388027.738381, "relative_start": 0, "end": **********.883318, "relative_end": **********.883318, "duration": 1.1449370384216309, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.883336, "relative_start": 1.1449551582336426, "end": 1749388029.079718, "relative_end": 3.0994415283203125e-06, "duration": 0.1963820457458496, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45596544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03535, "accumulated_duration_str": "35.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.973715, "duration": 0.03257, "duration_str": "32.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.136}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749388029.032854, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.136, "width_percent": 4.47}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749388029.051469, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.605, "width_percent": 3.395}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1949427897 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1949427897\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1699501832 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1699501832\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1918921357 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918921357\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1142757946 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388021952%7C13%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5vcm1lWW05OTdrdGZpbXhPM3ZzRVE9PSIsInZhbHVlIjoidk5SZjU4cnFRb2FiRHdXK3B1ejFhWTlQWFdDaEFmUzdycHl1MFdpYXFxN296VlRZMDhBSVJkSnloS0xJNUZRMGh3dkd3eGxrM1Q2NDVMY3VoMk1LeExWbzl5MTRTTkY1dFRmZG9TeDMzTWM0TndTRGtyMlREdDd3RytQbUtFNDlPaWp3TjcvT1JOMVF3L0hOQzM3UWgrUGI0YUZhRFRuNVlKTjRKSG8vRmNtL2lzOE01aGlWRk5maUxZVXMxM2drOUJOcklQWGJ1NkdXdTB3Wlo5Tm9aREZlcEhYaVZmbG9jMFhIZ0ZHV1ZKUnRSdTVsOXdPWjdCTjJ2SmxsWXNGT1ZjVTRPZTEvR2VxZjZaM0dEL2V4SmlBUXpJVGVMZU5XTEdSbFRvOU5TWnZ6VUhBMFVMQUk3eFFQNjFKKzMydmQyYlBWWmgyc0hDMmpFYUVhU3JEYUQydjQ0M0xwaDNEcUg0Zldab2ROeHN3VmxMZTUrUXdzYUtSOCt2S2ZKNTBiclZPakZ4TTI4Z1RlejVkVC8zbFN5d1p3dTVxanA5aGNIeVVpc1pNRXlMeVBvQTlvVlo5OThCcjI1dytzb2pZK1hpL0swcjRpYy9QbVUyZjlqVzZrajA0OUpYT094aG1qakI5aE0yV1BwRkFrZ1lIMWxWUkoxdzJuZUNFR29Fc08iLCJtYWMiOiI0MmM2Yzk1ZTI5ZjdmNjhjMDBlOGZhZTBhM2RiODQwYzdhNmQ5MjIwZWVlMTQ0NmI0ZWYwYzIxZTNlMzBkZmFkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFGSk4yaUZmS0I4NTQwWUNySEZ4Mmc9PSIsInZhbHVlIjoiUTVwaFcvK2tMRzN3Y2NvQmlrWHp1WjlMc1FDcjZaOFlhbjVETkhMb2RPb3BGRDEybGo2VEJ4L3ZqQ0lQcmthMTQrRlhFUXcyNEF4bDV2MDB5bXNLdjM4MEJ2TUdxZkVWblNINFRvOURQM25oRktMcVJXRXdyVzEyZDFaTURvSVhzaVhnYmpObzQ4blJ4NEwxZWtKcFhubnBlZC9Fd29VOUFzTUh2akwwS0Vpa2xuTHFPcytEeXNadnFaS3M3Sndia1hXbzJzS2F6RkdsVVNkMldYTVd6RGpiWnJwR2N4bms1YXBJYzAxTlNDMlk2a1pPY1dTRzRLdzlLdGdYQmVtRndsSUlmNHhSUk15L2NCU3g2ZTJKQ2VKR3lwanVMRWZvTGdPZmVIV3JwU2RYMnJMcCt4Smh2UDgrTXVWQmFZek8ybVd2VWs2RTRObGQ3RUxCS2Zma0dSY3VPY0g2VGpIcTZPTVJRbXN2Q3FQS0NOQ1JpV252WjFuUDQwNVZJN0RwTnB4a09rRENlWThwU0RDUStwK1hDRlVpMFFQcHJ4T0dZeGt2MFBXM2RXR0M0NWdsNUR0cG9HaFhlay9GUnpVWUJzL0lSR1FWbnY5VzhKYTcyWHd2YU5raENvWWpPR0ZiT1JZeWlaN3FBbWpQQkE3YmZpdDVDZmI1NWlQOXhPaUsiLCJtYWMiOiIzYjA2NWIyOTU0OWMyNjFlNTNkMTQ5ZjRiMDE4YzMzNTEzZTM0MDY4ZDQzMzNlYzgxNWMwYTI1NGUzNjg4YjAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142757946\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-331080018 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331080018\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1228308079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:07:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdRNmJpNE9GcFJicHZuSi9aekNyWGc9PSIsInZhbHVlIjoiSEpRUXVCaUY1aHdLS1liWWE5OGdUR1U1VHIzWmhIUDhDREw0dUdNcFEyUE9UcVlLaG9IaHVKbmpNRkpJUXJ5RHNyOW1YMFRCam1UMnV2Z2pjbVUzcVkyaUloRWQzUkZIZW9SbUtrYVFNNlNNaFByZEYzMjFoc2x6K1FSR0JBR0xhdHpJVzVIUEdUaUZmTkt1Zk44NWVzODkyMXg4TmNONU9wUnBNNDFLL0ZZRTU0SXhCWjVqOSsrbUc3dTVrZ0JwQjFkVmFGMTI5bFpsY2F1LzhvU3k4RWdpbm45VmpuNC9LTWxLQWhZQlRSazMxdjZucEluczh5c00zM2xkbjNPdW14aS90aTdmZHRCMUx4M3pacXRCenorVUpMQ3JFQVlpSGRCZGc1eWRIeEp6QWQ4TTlrNTJMa2RtZ3Y5amNwTWsvNjlBNUlKNk9OMkk4RUxQSll1UmpMR3IzenJWTFdoQ3lQVjZmdWF1QnJleFJOL3VzMVN5YjFaakxHc3hITG9HVm1ucjc5MVJ6dittMm1udkd0b05adkVOMTQ3WmFabFlxMC9tQk8xRTcyaGFScHZsamUxZ1NaVTF3MGhaSldWK1ZOTkxJcm1DUHFSRG5rZUdCZ0ZES2JjY2VLNjhtTXppdS9XOTk0RFVuUGdGY1NMSTRlNTFtdXN3dHQ1cHV1KzUiLCJtYWMiOiJlNTFjYzNlMzVhNDMwNzVjN2QzZjBmOTNiYWRkOGU3ZGI5ZmRhNTM4MTU2Y2IyODA0M2RkODExM2FjOWVjOWUyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Inp3Ykw0QzdnbElzSURKQy9GZWJyR2c9PSIsInZhbHVlIjoiMS9lamNqZnJUUWlFRmJyaHRVZzZVVjJzYmRvNFlKcFRyanBERXg1czlPRnVyeTcvUXFTb25pZWZRZnQxMVhMdDN4V0puQVlRNTNZVkVyR2JaVTFMUWRERERqck1jY3k4TUV0SEtIMHdQUVVORmF1ZVA4NVZkcnYrVDg4SFRxUjdkMlpnQTVaQmpOeFM2VFpXUWdRRTFJaDJuMHZEVVVJaHUvRVhvZjRzUzlUallEWEVFTHQ5MVhSWndvVjZ3aUU1RTE4QmJvakpXa2REa1ZXNmVOa3c0eTRvOGV0QnZhYisyUXBlU2hmS2VvZll0Z0duL2xKQk01SWNsMkE2d0hpc1hKRSt3bnhxaithT1l0VFYxQ01LUEhldmhzZjNoeHNZd2dqV0JiRkdNMzJpUkJUQy9JaWFzbzhidHo2KzJVWU1PUGwxSXRNSkF1MVg3dms2MnBIam5PRFFmcmVINmtlTW1sYWtIWnJZem50WEp1UHh6SnEyM05ydTVQUzZwT2RiditEcUduTDVLUjNhWFlROTRWd0dnbWRvTWlKYmZzNlR0QUlKci9vOCtJb0lHUFNSOWJwdDhiUXhWZURxR0hhOHJOMHVoUTVsTENlUFFibUc0MzY0MnJrcjIxRlZMeHd5dVY0RlpZVFdrb3lvRnJRZ2p4WE1HcE9ldGJyaDVramMiLCJtYWMiOiI3YjBiNjE2NmMzZWQxY2QwZWQ3MmNlMzMzY2U5NWVlZTQwMWViOGY0MWQ0YzQwMWNhODRlY2I2Zjk2ZWJmODMyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdRNmJpNE9GcFJicHZuSi9aekNyWGc9PSIsInZhbHVlIjoiSEpRUXVCaUY1aHdLS1liWWE5OGdUR1U1VHIzWmhIUDhDREw0dUdNcFEyUE9UcVlLaG9IaHVKbmpNRkpJUXJ5RHNyOW1YMFRCam1UMnV2Z2pjbVUzcVkyaUloRWQzUkZIZW9SbUtrYVFNNlNNaFByZEYzMjFoc2x6K1FSR0JBR0xhdHpJVzVIUEdUaUZmTkt1Zk44NWVzODkyMXg4TmNONU9wUnBNNDFLL0ZZRTU0SXhCWjVqOSsrbUc3dTVrZ0JwQjFkVmFGMTI5bFpsY2F1LzhvU3k4RWdpbm45VmpuNC9LTWxLQWhZQlRSazMxdjZucEluczh5c00zM2xkbjNPdW14aS90aTdmZHRCMUx4M3pacXRCenorVUpMQ3JFQVlpSGRCZGc1eWRIeEp6QWQ4TTlrNTJMa2RtZ3Y5amNwTWsvNjlBNUlKNk9OMkk4RUxQSll1UmpMR3IzenJWTFdoQ3lQVjZmdWF1QnJleFJOL3VzMVN5YjFaakxHc3hITG9HVm1ucjc5MVJ6dittMm1udkd0b05adkVOMTQ3WmFabFlxMC9tQk8xRTcyaGFScHZsamUxZ1NaVTF3MGhaSldWK1ZOTkxJcm1DUHFSRG5rZUdCZ0ZES2JjY2VLNjhtTXppdS9XOTk0RFVuUGdGY1NMSTRlNTFtdXN3dHQ1cHV1KzUiLCJtYWMiOiJlNTFjYzNlMzVhNDMwNzVjN2QzZjBmOTNiYWRkOGU3ZGI5ZmRhNTM4MTU2Y2IyODA0M2RkODExM2FjOWVjOWUyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Inp3Ykw0QzdnbElzSURKQy9GZWJyR2c9PSIsInZhbHVlIjoiMS9lamNqZnJUUWlFRmJyaHRVZzZVVjJzYmRvNFlKcFRyanBERXg1czlPRnVyeTcvUXFTb25pZWZRZnQxMVhMdDN4V0puQVlRNTNZVkVyR2JaVTFMUWRERERqck1jY3k4TUV0SEtIMHdQUVVORmF1ZVA4NVZkcnYrVDg4SFRxUjdkMlpnQTVaQmpOeFM2VFpXUWdRRTFJaDJuMHZEVVVJaHUvRVhvZjRzUzlUallEWEVFTHQ5MVhSWndvVjZ3aUU1RTE4QmJvakpXa2REa1ZXNmVOa3c0eTRvOGV0QnZhYisyUXBlU2hmS2VvZll0Z0duL2xKQk01SWNsMkE2d0hpc1hKRSt3bnhxaithT1l0VFYxQ01LUEhldmhzZjNoeHNZd2dqV0JiRkdNMzJpUkJUQy9JaWFzbzhidHo2KzJVWU1PUGwxSXRNSkF1MVg3dms2MnBIam5PRFFmcmVINmtlTW1sYWtIWnJZem50WEp1UHh6SnEyM05ydTVQUzZwT2RiditEcUduTDVLUjNhWFlROTRWd0dnbWRvTWlKYmZzNlR0QUlKci9vOCtJb0lHUFNSOWJwdDhiUXhWZURxR0hhOHJOMHVoUTVsTENlUFFibUc0MzY0MnJrcjIxRlZMeHd5dVY0RlpZVFdrb3lvRnJRZ2p4WE1HcE9ldGJyaDVramMiLCJtYWMiOiI3YjBiNjE2NmMzZWQxY2QwZWQ3MmNlMzMzY2U5NWVlZTQwMWViOGY0MWQ0YzQwMWNhODRlY2I2Zjk2ZWJmODMyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228308079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1665864102 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665864102\", {\"maxDepth\":0})</script>\n"}}