{"__meta": {"id": "X71c8187ebe3f207e8f88d5a4a359fd7a", "datetime": "2025-06-08 13:34:43", "utime": **********.846779, "method": "GET", "uri": "/receipt-voucher/1/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389682.487644, "end": **********.846807, "duration": 1.3591630458831787, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749389682.487644, "relative_start": 0, "end": **********.629095, "relative_end": **********.629095, "duration": 1.141451120376587, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.629112, "relative_start": 1.1414680480957031, "end": **********.84681, "relative_end": 3.0994415283203125e-06, "duration": 0.2176980972290039, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46775648, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.receipt.popup", "param_count": null, "params": [], "start": **********.830128, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/voucher/receipt/popup.blade.phpvoucher.receipt.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fvoucher%2Freceipt%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.receipt.popup"}]}, "route": {"uri": "GET receipt-voucher/{id}/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptVoucherController@showConfirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "receipt.voucher.confirm.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=141\" onclick=\"\">app/Http/Controllers/ReceiptVoucherController.php:141-144</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02303, "accumulated_duration_str": "23.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.74298, "duration": 0.022019999999999998, "duration_str": "22.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.614}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.794529, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.614, "width_percent": 4.386}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-voucher/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/receipt-voucher/1/confirm", "status_code": "<pre class=sf-dump id=sf-dump-2125220430 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2125220430\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1396906903 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1396906903\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1437653916 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1GNk1mVnFnLzk3QU5jdjF6bkpJY3c9PSIsInZhbHVlIjoiekJiTTFlRGlwVU02QnJkdXMwTzVhYkYva3lNY0JyMGtYc1NMSUxoMHdnM2hDdC9Dbyt5NG9qUjFxTUo3bXlrMkNxTFEwUTMwcjg4K1ZjY3pKNXd5aGR6cVlqWXl1a0V0RFFvSjA3cEZjc3RKMDN5YlhTOUUwTlZnMW9Namdha1JhNG5qSGFqRzhTVyswSTBwU0FUaFV0OFFOZFZuS1hwS2J3cVdxeEU0d2l1QzBRaE9maGo4YjRObCt1ZGtCYnl4RXFzOVVQYlFUS1hackJkMklqaGNNR2Y3ckM1RFE5c0QwaXZDaDBHaW1mSi9yMkcxbk9MTk5XWnZXQkthS0tOQWd3alNqTXhvc1lvdzhLVi9NVVFZZXJJVzgxdHAzWmhPRk01aVpIRzNlZ3FVZGJVNXVLWTBLdC8rLzAyVWVTRVdsTk1rL096SnZOQWdTUmFWa1BRNzRtQ01CQkVnejNNcTVIVGJZNE1GQzBFWmhGTVdpVFNNQWRSdmFEU29FTWFJbTFDNWxPWHA5N3dEWUEzNFFYN1EvME90WFZlSW1zNFkxeGlheGRyd0l5aGJwTmFZZGtQMVE4ekZOQ05nRUZZUGo0bE1lWU1IZ1dEOUJIaHZmT1pQWTlnNXR6RHZuOHFZZHNaWFpxZjQ3WFZaNGppVW4xazJPZ3R5OU51eC9DbG4iLCJtYWMiOiIwYjhhOTlmMTQ5YjY3MjgwODlkZTQyODZmNjZhM2U5ZDc0ZDliY2U4MzY5NWY2NDBlN2Y2NTFmMDkxZWYwMGM2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZ3bzRiSmlxVy9jTUwzRk5pQnRFTGc9PSIsInZhbHVlIjoiNk1yS25ZU201T0NTaEpQTnRiL1UzblpFdzJnR1hQQU5mdXRpNlJPd2RvTVJncFZrM28wTGFJY096eWI0cE54YUFjS052bTJ4L2xEUUZYZlY0MU5pRkRXL0Qwd2o2Sk1UR09WMkVnUFZ6VURpRVBKcVp6UCtidWo3MUVUdUVYTFFpZVN5VDlnN3hxdk94R3liVU4rR1RwQjBmSWsxZnAxRDFveURXVFlQSXJMWk80aGF1WGtjVVhIY09mdXdlZlZiQVdxVnlEZDd3Wm1qMWhWSFBzMy8yMmwrQmFsTjBuUSttWVc5TUo2WFBTV2twUmtHbG4xTlhiZGFiZ3k4SCtTVDVVbWdoOEFZUHFOd1lxMHVDZUxNZXNxa003bXpLL3JXN28ydlBoMjdGbTdYcUhwNnBMVGlDalpTMlJLdUtKOVRka2tpV0F0dXNsRjJIUlRMdkJWMy9ib05nc0tMbGoxL1N1eVBNUE0vOHpGc0NrTHJCMnhEMTlrRVlMTktLTzlGajYrR0s4MXJIUW5BdHBWY0tMUy9pMWJmZnAvOGg2NTBmWHVCMXNOTXE0RHFaZ2JHdzF0WEJRZjdMTFJGUW1UZ0lGRWVvajBqVnI4em5uNXprdzE1K1F5Y3daV3NFbVAxZ2NSdTEvZ2xxQlh2WjAyQWhaVlExQkd5TjBvcnB3YzgiLCJtYWMiOiJjZWQwZTU1YzZmZTlkYzdmYTU3NzAwZTQwMTUzMDE0ZDhiNzgyNjE5OTBmMWZhMGY1NDJhMjU5YWM5MjY0MTQ0IiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389682168%7C30%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437653916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-220622549 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220622549\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-325846211 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InREcWZjZzdKY2RWb2VTYzdNSDBEREE9PSIsInZhbHVlIjoiOGpEQUN1eElEUEt4QmlwNWJVZXJ3UDN0M2g0NVMyMnNYVDRRZjFjNmF0UFRmRUtLWjd3U2F5SGo1TndwTk5vQkFrc28rbEd3SlhHdG1TU1FXTkh3dU8ybFgzNVlGVGxIMmlkVlR4WXEwd3BaQlVVUG9kVUNtZXR3VlhvaFY0UTY1ZU1NYVBPdnVSK2h2ZldvdWdOUnZJc1BSWDJXU3l2dGc1czMwQmRUYndVSDQwQjhZNmFsTEYyb2Vxb2QwZFp3b2ZBcWRtd2ZpVVpaNkJYWm50WGJWOFNRWjU0UzRkL2NpUzlocUE2VDJvMGZ5c2dYTityUkJUbFBvUnVQTWVkamtnQ0JTYmI3ZHdZVkV2bTNScVBpUFBxUTlndDFPTlVVNTQrUzdEc3NJVzJmZXVmbVNZNTJJYWsxaGFSZjFhb2pQQXh2bFp2Z3pIKzR0ZW9NMk1uYk5IMWNibmJXcWtEeUJyWUNGbWpVcnVWd0dEUk1LbUVScWNtRVBIcnhZREphenpGSmJqaERDdjRmYWJVYTM4ZTlIR25tUUw2TDFVTTM5UXBJcTBCeWpFVnhYMzhhanBtVTVrWE9ib0pBNk1Eem1Zc29mdTlUd1VudHdpVG9tZ1hBN29RcUd3MG1VQXBGMU8ycVdyV2FCYm5ocjFwMUJXVU55bjE2cndZaSt4TnciLCJtYWMiOiJlODc3YTdkOWJjZTIzNWNiNjIxMTdmYTNjNDY2ODg1YTg5Njk4Zjk4NmFiNWRhYjU3ZTJhNDgzYjM0ZDk2NjU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ims1V0grOUY1aWdiMUlEYmw0QWNYNHc9PSIsInZhbHVlIjoidWhRMDlqTDZMZTlwdXhWSTdVbHVnWkxpYXZ6ZlFCTXYyb1RCeGd3ZnFKa3JiRUliYkd0b08vWjcxaGxPR25nMUJJakNjRHg3SzF6d2p0OTM1R1Exai83NGhiSGhzekV6cGZUQlQyMG9PS1lQL0p4R2FTRFZ6NFBHcmJvYUsvZTRXYXdESXJkZk95YTVwTEdLRGtWSWk4L2g4NXJYNmZUZS9FWHdRM3pIR1N5TStrZ21Lci9KWUNsR0FBVld3Q3lkRlMrTnM3TjY5RDlkRTQ1UzVaeWtIWDFQcG96eUliU3pEQzY5SlJyMTlnV2pDNUE3WS9QRXhNZXYwZzM1ZkV1K3NaeCtQTGtqdlV0SjNlQnJKUGtpUHdKUmUrL3RCVXFqMFJEd01hdVo4dDQzdUxOZWZ3NlUxVStrSGZCVi9kc1NYd201TEtzWm5UYXBqU0MxNEJqdGhyclRnM21QQWJQajFRYjFzUXduLzF5dmFzampUV0w5RzdhTUJ0Nk14WjZaeVhXZldpSzgreGRjeVZWQjIvbEFWb1NPclo4eEp5MlZRcGNEZlBqYUFUZWdtVWtVSDFJQWRzWjc5T0M3aEFhclh3ZnVQMG1GOW5Tdk0yNm4rNHZXT0pja1RwNXMyWm9ic3NGWmVvQ2Q0MTlHQWMvZ3JTSkVva2x1ODRveElNeTQiLCJtYWMiOiJiNjA4MTEyNDIxN2Y5MjNjNDliMTBkZDNhOTVmNzc1YmY2NDc5YjgxNTUyMGViMDhkNjc5Mjg5OWVkZjY0MGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InREcWZjZzdKY2RWb2VTYzdNSDBEREE9PSIsInZhbHVlIjoiOGpEQUN1eElEUEt4QmlwNWJVZXJ3UDN0M2g0NVMyMnNYVDRRZjFjNmF0UFRmRUtLWjd3U2F5SGo1TndwTk5vQkFrc28rbEd3SlhHdG1TU1FXTkh3dU8ybFgzNVlGVGxIMmlkVlR4WXEwd3BaQlVVUG9kVUNtZXR3VlhvaFY0UTY1ZU1NYVBPdnVSK2h2ZldvdWdOUnZJc1BSWDJXU3l2dGc1czMwQmRUYndVSDQwQjhZNmFsTEYyb2Vxb2QwZFp3b2ZBcWRtd2ZpVVpaNkJYWm50WGJWOFNRWjU0UzRkL2NpUzlocUE2VDJvMGZ5c2dYTityUkJUbFBvUnVQTWVkamtnQ0JTYmI3ZHdZVkV2bTNScVBpUFBxUTlndDFPTlVVNTQrUzdEc3NJVzJmZXVmbVNZNTJJYWsxaGFSZjFhb2pQQXh2bFp2Z3pIKzR0ZW9NMk1uYk5IMWNibmJXcWtEeUJyWUNGbWpVcnVWd0dEUk1LbUVScWNtRVBIcnhZREphenpGSmJqaERDdjRmYWJVYTM4ZTlIR25tUUw2TDFVTTM5UXBJcTBCeWpFVnhYMzhhanBtVTVrWE9ib0pBNk1Eem1Zc29mdTlUd1VudHdpVG9tZ1hBN29RcUd3MG1VQXBGMU8ycVdyV2FCYm5ocjFwMUJXVU55bjE2cndZaSt4TnciLCJtYWMiOiJlODc3YTdkOWJjZTIzNWNiNjIxMTdmYTNjNDY2ODg1YTg5Njk4Zjk4NmFiNWRhYjU3ZTJhNDgzYjM0ZDk2NjU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ims1V0grOUY1aWdiMUlEYmw0QWNYNHc9PSIsInZhbHVlIjoidWhRMDlqTDZMZTlwdXhWSTdVbHVnWkxpYXZ6ZlFCTXYyb1RCeGd3ZnFKa3JiRUliYkd0b08vWjcxaGxPR25nMUJJakNjRHg3SzF6d2p0OTM1R1Exai83NGhiSGhzekV6cGZUQlQyMG9PS1lQL0p4R2FTRFZ6NFBHcmJvYUsvZTRXYXdESXJkZk95YTVwTEdLRGtWSWk4L2g4NXJYNmZUZS9FWHdRM3pIR1N5TStrZ21Lci9KWUNsR0FBVld3Q3lkRlMrTnM3TjY5RDlkRTQ1UzVaeWtIWDFQcG96eUliU3pEQzY5SlJyMTlnV2pDNUE3WS9QRXhNZXYwZzM1ZkV1K3NaeCtQTGtqdlV0SjNlQnJKUGtpUHdKUmUrL3RCVXFqMFJEd01hdVo4dDQzdUxOZWZ3NlUxVStrSGZCVi9kc1NYd201TEtzWm5UYXBqU0MxNEJqdGhyclRnM21QQWJQajFRYjFzUXduLzF5dmFzampUV0w5RzdhTUJ0Nk14WjZaeVhXZldpSzgreGRjeVZWQjIvbEFWb1NPclo4eEp5MlZRcGNEZlBqYUFUZWdtVWtVSDFJQWRzWjc5T0M3aEFhclh3ZnVQMG1GOW5Tdk0yNm4rNHZXT0pja1RwNXMyWm9ic3NGWmVvQ2Q0MTlHQWMvZ3JTSkVva2x1ODRveElNeTQiLCJtYWMiOiJiNjA4MTEyNDIxN2Y5MjNjNDliMTBkZDNhOTVmNzc1YmY2NDc5YjgxNTUyMGViMDhkNjc5Mjg5OWVkZjY0MGU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325846211\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1558442904 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558442904\", {\"maxDepth\":0})</script>\n"}}