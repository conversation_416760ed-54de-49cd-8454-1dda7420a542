{"__meta": {"id": "X2c29fb37662e5c651b24bc6c29d10447", "datetime": "2025-06-08 13:44:38", "utime": **********.848523, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390277.404562, "end": **********.84855, "duration": 1.4439880847930908, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749390277.404562, "relative_start": 0, "end": **********.661692, "relative_end": **********.661692, "duration": 1.2571299076080322, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.66172, "relative_start": 1.2571580410003662, "end": **********.848553, "relative_end": 2.86102294921875e-06, "duration": 0.18683290481567383, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45589608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03218, "accumulated_duration_str": "32.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7472968, "duration": 0.02991, "duration_str": "29.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.946}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.803399, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.946, "width_percent": 3.76}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.820503, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.706, "width_percent": 3.294}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 2\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 20.0\n    \"originalquantity\" => 20\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 40\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1362768735 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362768735\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1684556483 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtDVG03SHV5RFQxTmQ2dGpDUUM2VUE9PSIsInZhbHVlIjoiOGd6TXRzS3pBVnhKVFpvQjZqaEgyQWljOGFIdTRJRTJxMzF6amtHK0o0V2NTMTRZNXB0Z1A0QjAvMzZrZU4zTXZsV2RMd3lqQk5uMzlLRjR3ek10V2toSHlUNi9sMVBMN1p6a2pIdEZTT2FJOEErVnpEcHNIQVd3TXlFN0lJZ0RXTUFJR29mTUViYy9wTWRma29mMGFhTWRnRXZ6WWxKU0RsbGU4a1ZGc25EcGNjQk05V1ZkMVVkK1NNRmJ0eTEzZ1MzeGlCOUw3dTRvNFZNZlFFM3lMNFp0WlZLcERLS2k2Tzk2QzBOTTF0bFo2R3h1VFk0OWdZNVBzdG9yM3Vpc3ZEdDlDRWE0Q0ZJdS9UdFVMM3pjeExuTUFNNjE4aVFFMnVtRnAvTTJQbUpJd0EwWHRtRTVPdTZKUFJNRGRqSHJwMXlRSlh2dzkxNjdkNnZEZGVoTDNGYWZvUFhVS1VWckV0ZlQyNGRZL2RGc0xzNDRWUUh5RkVpeWJMVkFVU1dKWDE2QWlyV0hQOFp0SXViQVB5dkJNaHYzTHdkRk1xdnU5cGNVTVJEOEkrdk4vdnpPSGtrQ3UxdEUwZXFGRklWaC9zOHBiWGZCM011MzU4bE9MN0VRSGhNdFFkVGpzM1ZlMVFoWlBiVWhjZTkybXlVQ1lRVUVkSDZLUWd1VjMyT1MiLCJtYWMiOiIwNjllNTZlYThmODI3YWM1ZTA2ZTk3YTM0OGJiY2ZmMWU4M2ZmNjAzOWU3Y2FkZjM5MjUyMjM2ZGI5YTRjYjA3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpodWVEcTNLSnhOTEIydU42aC9JSUE9PSIsInZhbHVlIjoieG8xVWY0VFBCM29paUxWMithZVZCMU94WUFzTmJVRzhVdi9CY0dJVGtld3FCbTV1VmlQSjFXOU9MWEdjWTBHWjU1dm11S2hOZmxSVjBueXlvTFB3ZDNSd3ZOKy9vSEJieERabXMxajJoSkgybitsUnNjd2tBajhJVm9KYmVIWG9JVFlka0hlLzhXSjFkK3Vlbnc1VGl1VGJ3Y2lHSEo0eG0yb0llMktidWtvcWZKZjJIanhwMDhwbHQ1ZnJIa1Zpai9YMHMzQzB3Y1FQRTlkb3lmbTV5SkxiV1VJWXJyTGN0cUVWekdid1VCOUEyRGkvcWtyZjIvcFV3VzhLNjVnQkhxWW0xVE5zQ0x6TG1JbTNITlFDTzZCRFZYRFdGYU5pTXExaHdsdXZud3c5NDBibHJQd2tBcTBJNVVoWnVXTUhNTEhpK1lGOXJ5WGY0QU1XVWlwY1JGZld0ZGhra003Tmd5Y1R3bEFKR0ovV2M5RGFMTCtQbzdLWHlPcTVDNXJPQzlTZFZ1L0N6MXdiYmxwdC9aWWVkZFgrRE5RTGpsMnNLcWJDNUJNNkNzL0dLWUF1aTNjNzJiVUZ6eGZ4ME9jSFRPSVF1cDBmbm0zaTlWdGV2dURmQVRmTjU1NEdQSitzZEdkU2ptYTRiUEdFKzl5UXJ2dm5EWGRIQ1grcTdsYjUiLCJtYWMiOiJlODQ4YWM5MmUwNmJkNzFiZTJmZjc0N2M3NTRkNDRhNGQxOTkxYTU2YTZkZDg4OGFhN2IwZmM0ODcyZjVlZDNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684556483\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1549364615 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlcrUzBMSkJWelRUdSs0Y2lJaHg3TkE9PSIsInZhbHVlIjoiakdUTTZNZjdwMWVseENXbjhqQklBRC9CcU05Zlkyd1RQbGtXQzlPOG85SC9OYmlOLys3d250allFWCtld0laR09GT1RjSHdTUit4QUVCcXZFY2tKTjRTNkpLdjQ0Nk4xMWp1SWNJbk5UT2Q2TFBKaW1MSnpMbktVTllLMTZocFk0dXdla2VaT1JFTW00dFU5WGdwaGo0R0lpTWFOclYzSVNPZ2E2UjlZRERtU2haM2syUDEvWDUwN3J1dmNYY2h2aldlajVWL1R3a2VqS2M1ajk3ZjBJdk1YYVBQTDU0dGdPTFh3SXBqMEkyNUFnN05lbnQvbUV1K3dWQmo5bm85SjRySzE0dnJHRWpSaXpFeWNTS09jOHlvNlNKbDB3UG1US1dMMlpBWlVqZVBwRjVMaWI0enFyYnJFM0U0NWo2SDNhTVpDSEJIOWcxbmVxUXViU3hzVEtsOEZlUlB5Q3BkeEl3RjhKM0RWSWFhSjFncFNWQzlBbTZnTXJSZiszRXNBOW13L1lsZjhoSzh1VUtpOEwxQmhNV0NsQ3VvaU1DVTA4SlZTK0ZiMExZcW9UdmZaRFV5VlFsTFo4Umd4SHFzRk5xd3cxQkZJenZlOHZGSEl3ZkRUREJZcWluNEc4dlRYRnZkb2djeUhFeUo0QVFTSCtDSFhqdGZERGtKSXR6Wk8iLCJtYWMiOiI0M2QwMmY4YzNlZjQyODNkYzY2NWJkODJiMDgzOTIzNzJkNmE4MmY2NmJmMTAxNzY5MGJlMTg2MWFkZjJlOTFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imprc0hPTnQ5NGNNRmUvR09xZzhNTnc9PSIsInZhbHVlIjoiUEp5b05MUjI2NHQ1R2FlQmZlZlRneGxTcGdjU2VUeGVOS0pkUndaT3FEcWpkWXVLWjdrZjZ6ckNvYzJFUEV5Q0F0WjdYcVk4Q243WmZLbDQ4MVBpY3NsSXQrL3UyRjNCUDdGK21SMTJDbjlLVVFaTjBCMk0wVWVEdUNTa0d5WlpQeWxjY093ME5aTzhsSUpwWVhqWVdaczlPZU5pTlJhbEcyV29GUVpLeHJWVDdVZHBQMzcrMGVkb2daQ2VtMWsrdVJPSm9JM1N4Zy9iTXpIZnp1ZXVFTzFITTdoSWFzeDJDaHJiRWVsWTlUdGludmJsbW9pNWRPMVk2VkUySnhEbGZobU5EMWVFZWt2UXUzb1hXakdaNks3ME1vZktRN2NGR3hIM0JnemlXd1ptYVROZytCNDJpRVRNYXZJVWFsNUNwNGZTM1BhZGlTMzJySnFURDlaRkhKVi9wM1FPMDZEWjhmM0RIMy9BOUdlVG91aDkrd3hCMnU2c1dUT2NiQkNNejZ1aWJPSG8vOWNkTER3dUxmZ1pYYjNOU3d3Mjl6V2hVSkdJU1pHUmhTMSttWWtyNGp0VWpVOXhYQkpsUTFDWEJmYXlrTkRSV1ZETzIvVEE4MVcrNFBoY3BEUXJUa0lBV1BBR0RKUjBzbVM2VVJWU09kVUNocllaVS84dmtubEMiLCJtYWMiOiIyNjQzZmVhNGYwMTllNjJhYTMwNGFmMTIxZWJjNmI1ZmNmMWQyMzc1ODg1ZTk3ZTlhZmJkYTUwMGU0YTNiNjM4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlcrUzBMSkJWelRUdSs0Y2lJaHg3TkE9PSIsInZhbHVlIjoiakdUTTZNZjdwMWVseENXbjhqQklBRC9CcU05Zlkyd1RQbGtXQzlPOG85SC9OYmlOLys3d250allFWCtld0laR09GT1RjSHdTUit4QUVCcXZFY2tKTjRTNkpLdjQ0Nk4xMWp1SWNJbk5UT2Q2TFBKaW1MSnpMbktVTllLMTZocFk0dXdla2VaT1JFTW00dFU5WGdwaGo0R0lpTWFOclYzSVNPZ2E2UjlZRERtU2haM2syUDEvWDUwN3J1dmNYY2h2aldlajVWL1R3a2VqS2M1ajk3ZjBJdk1YYVBQTDU0dGdPTFh3SXBqMEkyNUFnN05lbnQvbUV1K3dWQmo5bm85SjRySzE0dnJHRWpSaXpFeWNTS09jOHlvNlNKbDB3UG1US1dMMlpBWlVqZVBwRjVMaWI0enFyYnJFM0U0NWo2SDNhTVpDSEJIOWcxbmVxUXViU3hzVEtsOEZlUlB5Q3BkeEl3RjhKM0RWSWFhSjFncFNWQzlBbTZnTXJSZiszRXNBOW13L1lsZjhoSzh1VUtpOEwxQmhNV0NsQ3VvaU1DVTA4SlZTK0ZiMExZcW9UdmZaRFV5VlFsTFo4Umd4SHFzRk5xd3cxQkZJenZlOHZGSEl3ZkRUREJZcWluNEc4dlRYRnZkb2djeUhFeUo0QVFTSCtDSFhqdGZERGtKSXR6Wk8iLCJtYWMiOiI0M2QwMmY4YzNlZjQyODNkYzY2NWJkODJiMDgzOTIzNzJkNmE4MmY2NmJmMTAxNzY5MGJlMTg2MWFkZjJlOTFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imprc0hPTnQ5NGNNRmUvR09xZzhNTnc9PSIsInZhbHVlIjoiUEp5b05MUjI2NHQ1R2FlQmZlZlRneGxTcGdjU2VUeGVOS0pkUndaT3FEcWpkWXVLWjdrZjZ6ckNvYzJFUEV5Q0F0WjdYcVk4Q243WmZLbDQ4MVBpY3NsSXQrL3UyRjNCUDdGK21SMTJDbjlLVVFaTjBCMk0wVWVEdUNTa0d5WlpQeWxjY093ME5aTzhsSUpwWVhqWVdaczlPZU5pTlJhbEcyV29GUVpLeHJWVDdVZHBQMzcrMGVkb2daQ2VtMWsrdVJPSm9JM1N4Zy9iTXpIZnp1ZXVFTzFITTdoSWFzeDJDaHJiRWVsWTlUdGludmJsbW9pNWRPMVk2VkUySnhEbGZobU5EMWVFZWt2UXUzb1hXakdaNks3ME1vZktRN2NGR3hIM0JnemlXd1ptYVROZytCNDJpRVRNYXZJVWFsNUNwNGZTM1BhZGlTMzJySnFURDlaRkhKVi9wM1FPMDZEWjhmM0RIMy9BOUdlVG91aDkrd3hCMnU2c1dUT2NiQkNNejZ1aWJPSG8vOWNkTER3dUxmZ1pYYjNOU3d3Mjl6V2hVSkdJU1pHUmhTMSttWWtyNGp0VWpVOXhYQkpsUTFDWEJmYXlrTkRSV1ZETzIvVEE4MVcrNFBoY3BEUXJUa0lBV1BBR0RKUjBzbVM2VVJWU09kVUNocllaVS84dmtubEMiLCJtYWMiOiIyNjQzZmVhNGYwMTllNjJhYTMwNGFmMTIxZWJjNmI1ZmNmMWQyMzc1ODg1ZTk3ZTlhZmJkYTUwMGU0YTNiNjM4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549364615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>20.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>20</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>40</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}