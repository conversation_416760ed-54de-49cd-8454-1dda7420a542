{"__meta": {"id": "X6413993595494d53fbc33c1deb84c5fd", "datetime": "2025-06-08 12:56:06", "utime": **********.084713, "method": "GET", "uri": "/users/17/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387364.512889, "end": **********.084746, "duration": 1.57185697555542, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1749387364.512889, "relative_start": 0, "end": **********.754194, "relative_end": **********.754194, "duration": 1.2413051128387451, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.754237, "relative_start": 1.2413480281829834, "end": **********.08475, "relative_end": 4.0531158447265625e-06, "duration": 0.33051300048828125, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51400672, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x user.edit", "param_count": null, "params": [], "start": **********.053508, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/user/edit.blade.phpuser.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.edit"}, {"name": "3x components.required", "param_count": null, "params": [], "start": **********.067634, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.required"}]}, "route": {"uri": "GET users/{user}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.edit", "controller": "App\\Http\\Controllers\\UserController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=235\" onclick=\"\">app/Http/Controllers/UserController.php:235-250</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.01728, "accumulated_duration_str": "17.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.859955, "duration": 0.00592, "duration_str": "5.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 34.259}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.897269, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 34.259, "width_percent": 6.308}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 238}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.909818, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "UserController.php:238", "source": "app/Http/Controllers/UserController.php:238", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=238", "ajax": false, "filename": "UserController.php", "line": "238"}, "connection": "ty", "start_percent": 40.567, "width_percent": 6.134}, {"sql": "select * from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 239}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.921144, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "UserController.php:239", "source": "app/Http/Controllers/UserController.php:239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=239", "ajax": false, "filename": "UserController.php", "line": "239"}, "connection": "ty", "start_percent": 46.701, "width_percent": 7.465}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9915931, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 54.167, "width_percent": 10.127}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.998993, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 64.294, "width_percent": 6.134}, {"sql": "select * from `users` where `users`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 241}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.011542, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "UserController.php:241", "source": "app/Http/Controllers/UserController.php:241", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=241", "ajax": false, "filename": "UserController.php", "line": "241"}, "connection": "ty", "start_percent": 70.428, "width_percent": 5.903}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'user' and `record_id` = 17", "type": "query", "params": [], "bindings": ["user", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 242}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.01843, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "ty", "start_percent": 76.331, "width_percent": 10.532}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 243}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.027057, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "UserController.php:243", "source": "app/Http/Controllers/UserController.php:243", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=243", "ajax": false, "filename": "UserController.php", "line": "243"}, "connection": "ty", "start_percent": 86.863, "width_percent": 5.903}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 17 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "user.edit", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/user/edit.blade.php", "line": 100}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.073065, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "user.edit:100", "source": "view::user.edit:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=100", "ajax": false, "filename": "edit.blade.php", "line": "100"}, "connection": "ty", "start_percent": 92.766, "width_percent": 7.234}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1933053813 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933053813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010377, "xdebug_link": null}]}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/17/edit", "status_code": "<pre class=sf-dump id=sf-dump-649121906 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-649121906\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-101718264 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-101718264\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1004417084 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1004417084\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1807959012 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387360042%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVzWVhyY2lRbTlqb1FyVHc1MUhNTWc9PSIsInZhbHVlIjoiY3M3aERpTzNaL2MxeFc1TzBVRXpBUW9weE1nQjNSNzA2MiszQndlREk4QTA2TUtvUlBRRTdHb1pDWCt1ZVZ0aFBhOGVtbjRBWWpiSDJBWkNpbE5oOG44ZTAvWGVBR3FEajhMeDlOdi93ZkhLVzlPc2JiK3BMZXhFWFFCekp0c1IvUmNnQmFVbjNDTUFSazBtU1ZPR1ZzWlhhSlhCQmxUcXdPdjJPbjZvOVBsajl2V055eGI1MW5KZDl1VTZ4ZEFpSGtKelhOc2U4d1FwTUxoZkhpYi9FRnZYaXlGQlVjcTd3VDhjUkRTdm1vWXJ6OWU3TFFPcG9id0Q1UjhndTRWNzZBcVJLRm1pSm5ESzd4a21iTXFZSWloSXhCZzlaZ0pBblNtd01Ham5qMEtRczdNNXFZc2w2QnloWXF5bk1sYk1YMi91dlV0QnAxOUl2QUxwS3dId2FMS3FDN2RkU0NycStCNGVqaXdBbXFIN2wxZENMeVo3dUVHUDMyR1BiVTRFRnpKR0FZS2EzTm80U1UrRFl1aStCMzNLWTJ4OHI3aVJGVVdWZzN2enkyMW02aDNvUDk4MnJiK2pCZUJDSnY4Qjlzem5XY2UxQVFJNmg1MEljMzNlK1dtWUcxM2t6Q1UvMWh0QTd6MHFJMk1WK05kdFZpS2IyVXN5bUxlQjFCU2IiLCJtYWMiOiJiNmEzNTUyODBiZTlhNjUxYmZiOWNjNDE0NDBhOTc4ODFjYjcxZjRhNWZkMzZiYjQzNDBkMGRiOTBlMDk2MjQyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFMaVJoS1AwMlVlcXBOaFdERFh4NWc9PSIsInZhbHVlIjoiTktBM2VJZXZVdXQrZzlNdVVBWUFRV1AyYjh6WHM4Y2d3aTBZMWZrUjRBb0xHSElBaWVKZjhDay9EWW5uUGJRT1pUZUJsWEJaU3NVcURyRVhpY1hoWUlTdmZST2h3VkxxZmVBQ0FPRC9TL0FqZXlUUDVEckxhZHJRLzBiREtidVcveHpPdDEyNytuN2hsTmU3OEgvRklBTERwc1FmRWpYMkROWjFYZ1Nkc1Y5MFNEUmo0dWRUeGlNWXUxMUtiTytXaVRWZkk5dDloOGcwL3kvbjdOQmpJRG8xN0gxb0hVVUt4TWZSZFU1eSs0ZjZjMFdGR2k1VWRVZFVpZHlySk5zTC84T2g1TXBrYmlxYTNsa05WaGlFckdoK1EyVFMwcDErMXpzRmlWNk9Dek9mazl2VmYvQmFPU2RadjhLSDZSeHVEK2p5STNGQ0xiTUk4Y2ZyZ3dvajQ2bnppdmpGSU12dFRDTXJsMFdWallWK211WUdmNW8wNHgwYXVQVU5DcEF2RnhkRGJwSkdna0d5ZDBWakJmcitBcmxiUmtyeDhuRmd4Sk5xVGIvQzFhK3RjcUQvMVk1V0J1NVZ3YktlSTJEc3MvVTlVNllvYWx6UVRFUEpBL0c4Qys0bjV6ZkNiUExJajBKSGdpSzdDU0s5TkFxcEEwckVzd2NCd3h2THNwNjciLCJtYWMiOiJlNWM2ZmFkNjQ0Mjc2NjU3OTBkODliMDQwOTdjNTNmNTM5Y2IxOWU1OTJiMzA3NzM2YzMxNDlmNTIzZWYwYjNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807959012\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-544598803 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544598803\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-904077937 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:56:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxTSUpKVTBBei8wenFSN0VtWFhBcnc9PSIsInZhbHVlIjoiQ2UvL3NoWENlY3JBTGZaSUxmc0RjYmhPQU51bnZIb2lXTWYxaWN1c1BYODJaRTIvWFlOWlV0TG85ZG5rdS9KcXVuYmxzd0hpVUIyWHBJMXNJbXVXWTNqOVQwRkV4K1NyQnYweXN3bEZrVTV2b3VhaEtoSHRlQjhiRXJsWnZtdVBDcHIzS1VxdVNnSlZVa25XcHRtRmNyelBuN20xREZmWHpVUmI5U1RzZnFWT2IvaHFYOTYzR24xdVVRTzJXVm1neHJxVDRNRlNsVmtCOGJQZEdjb08vWW5HK3BXZzBOSUtYRmlORGhHQTdyNW1FVWVTMitWaVR4K2dLck15by96UEtEekVGcGJsbHdELy9JRHJrZUJSMW0wNlFVYytKMFFMdFN1d3Y3dVdWckY1cTNBc3V0Yk9hZXNpTUN0bVF5SHJzeGZmY0NqUEdnK0RUS1Z1M1l2N0FYRk9lOVdhSUVoaE9QaXBwbSs4QjMrL1Y2U1JGQVUvbitXdXR0UWV2WnNRRVlkVlY0ajljRzNXNWx2QWxDMzRiVFB1TFF5UG9QQWNBVklvRWNUcUJBeXZCcU9BWWtYNEgveEVmOE1rbC9VOGVxSmhyY3BjSmFLT0dFOCsrYlZTc2xUTUNrQ1VpV2dQcE5jTU9xR29qN3Z6bi9iTnhMTEpvMzAwT1IwTVZjLy8iLCJtYWMiOiJhZWMxMmI2MDFiZWU4YTIyM2MwMDkyNGViODAyZDE0MGJkM2FhYzRiNzRlYzBjODhiYmRhOGJjYjMzMmE1ZjI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxybWJJMG1qbHJ6b2theDltU2NZbXc9PSIsInZhbHVlIjoiM3phdWZHTnBFVi93eDB5YjBLS1pldmNPVUJGQmNxQW5WdzljZCtmTFhqdmdKRFFrUEt0UnZmNldDcDZaR2I2KzQyMXpHZGcxaVluc0VUZWVxQjE0TWpucENlTXd0NmloWFByNnRrTERXUkQ0cXlWVjcyRWE2K2xsTGNNNFVJL09EZGVzOFNWeXRpTlVOeXJydGlxeTZyRmYwMTRvSnJLZ2xxUzR5MEtqQ2pJaTFvSHBNV1YxRWpCLzIzb053UW5BclJ3ckZodzAyc0NpbGF2RE0ydDg5d29Gem55NXJIMEU1Vm1GRjB6TkZpT1RqVzVNSnNGcXJZSytYbmFHM3ZpcXZvbDU2dXVBeG90Q1pYcm1ra3NyaFhhYzlqNEdSc3U2TW56aEZnRlFNUjcxQWExODNma0NZbm1TWW5TSkZkb3JvTG1nWU9kUStsYkZOcXh2OE5Nb2J3VURhcGN0MEVJUE9keWRUL2NYbmU1MjRTLzYzcDh3T3NTTFpXUk5EOVozY3IySEhsZVM2WWV4R3Q2YWR4QzA0RG1sZnM2MXdzblhXdUx3MlZzaW45ZnB4K3NQNnVHb2Z6TnpqSEgxUjd4eUZ6YitYWDBtdklnSXlIYXdRUkN5K2VGQlFVM1l4OEJITmhNelFEVEZiS05TTXo2Q0VBLzBadnUrKzVXanN3SUoiLCJtYWMiOiI1YWVjNmE0MDYyOWVkZmI4YmU0NDJjODkzYzQyMjBkNGYwNDQ4ZDJiYjg0YTYyODg2NzllOTg1MDI0MGQ1ZTA2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxTSUpKVTBBei8wenFSN0VtWFhBcnc9PSIsInZhbHVlIjoiQ2UvL3NoWENlY3JBTGZaSUxmc0RjYmhPQU51bnZIb2lXTWYxaWN1c1BYODJaRTIvWFlOWlV0TG85ZG5rdS9KcXVuYmxzd0hpVUIyWHBJMXNJbXVXWTNqOVQwRkV4K1NyQnYweXN3bEZrVTV2b3VhaEtoSHRlQjhiRXJsWnZtdVBDcHIzS1VxdVNnSlZVa25XcHRtRmNyelBuN20xREZmWHpVUmI5U1RzZnFWT2IvaHFYOTYzR24xdVVRTzJXVm1neHJxVDRNRlNsVmtCOGJQZEdjb08vWW5HK3BXZzBOSUtYRmlORGhHQTdyNW1FVWVTMitWaVR4K2dLck15by96UEtEekVGcGJsbHdELy9JRHJrZUJSMW0wNlFVYytKMFFMdFN1d3Y3dVdWckY1cTNBc3V0Yk9hZXNpTUN0bVF5SHJzeGZmY0NqUEdnK0RUS1Z1M1l2N0FYRk9lOVdhSUVoaE9QaXBwbSs4QjMrL1Y2U1JGQVUvbitXdXR0UWV2WnNRRVlkVlY0ajljRzNXNWx2QWxDMzRiVFB1TFF5UG9QQWNBVklvRWNUcUJBeXZCcU9BWWtYNEgveEVmOE1rbC9VOGVxSmhyY3BjSmFLT0dFOCsrYlZTc2xUTUNrQ1VpV2dQcE5jTU9xR29qN3Z6bi9iTnhMTEpvMzAwT1IwTVZjLy8iLCJtYWMiOiJhZWMxMmI2MDFiZWU4YTIyM2MwMDkyNGViODAyZDE0MGJkM2FhYzRiNzRlYzBjODhiYmRhOGJjYjMzMmE1ZjI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxybWJJMG1qbHJ6b2theDltU2NZbXc9PSIsInZhbHVlIjoiM3phdWZHTnBFVi93eDB5YjBLS1pldmNPVUJGQmNxQW5WdzljZCtmTFhqdmdKRFFrUEt0UnZmNldDcDZaR2I2KzQyMXpHZGcxaVluc0VUZWVxQjE0TWpucENlTXd0NmloWFByNnRrTERXUkQ0cXlWVjcyRWE2K2xsTGNNNFVJL09EZGVzOFNWeXRpTlVOeXJydGlxeTZyRmYwMTRvSnJLZ2xxUzR5MEtqQ2pJaTFvSHBNV1YxRWpCLzIzb053UW5BclJ3ckZodzAyc0NpbGF2RE0ydDg5d29Gem55NXJIMEU1Vm1GRjB6TkZpT1RqVzVNSnNGcXJZSytYbmFHM3ZpcXZvbDU2dXVBeG90Q1pYcm1ra3NyaFhhYzlqNEdSc3U2TW56aEZnRlFNUjcxQWExODNma0NZbm1TWW5TSkZkb3JvTG1nWU9kUStsYkZOcXh2OE5Nb2J3VURhcGN0MEVJUE9keWRUL2NYbmU1MjRTLzYzcDh3T3NTTFpXUk5EOVozY3IySEhsZVM2WWV4R3Q2YWR4QzA0RG1sZnM2MXdzblhXdUx3MlZzaW45ZnB4K3NQNnVHb2Z6TnpqSEgxUjd4eUZ6YitYWDBtdklnSXlIYXdRUkN5K2VGQlFVM1l4OEJITmhNelFEVEZiS05TTXo2Q0VBLzBadnUrKzVXanN3SUoiLCJtYWMiOiI1YWVjNmE0MDYyOWVkZmI4YmU0NDJjODkzYzQyMjBkNGYwNDQ4ZDJiYjg0YTYyODg2NzllOTg1MDI0MGQ1ZTA2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904077937\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1463792863 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463792863\", {\"maxDepth\":0})</script>\n"}}