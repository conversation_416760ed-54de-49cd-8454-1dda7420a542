{"__meta": {"id": "X3f61bcd7f93b7be91e80cbddc58f2c99", "datetime": "2025-06-08 13:35:12", "utime": **********.94276, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389711.561459, "end": **********.942788, "duration": 1.381328821182251, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749389711.561459, "relative_start": 0, "end": **********.759369, "relative_end": **********.759369, "duration": 1.1979098320007324, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.759387, "relative_start": 1.1979279518127441, "end": **********.942792, "relative_end": 4.0531158447265625e-06, "duration": 0.18340492248535156, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.026059999999999996, "accumulated_duration_str": "26.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.859287, "duration": 0.024579999999999998, "duration_str": "24.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.321}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.916821, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.321, "width_percent": 5.679}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1693731810 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1693731810\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-211123277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-211123277\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-38327738 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38327738\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1632414160 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9IVTdzRjFaQXRSRFNVQmFiSnRadVE9PSIsInZhbHVlIjoiSGp4aWdQZnRPbklGRGYwT0d4QWxteDNHM0ZlcU9aUExYTkxneTFSNkphQXp0bkdIdy93aHdWckNWWTBqR01xN0MrSDBZNVNqU2U1Y3VsYzlLbU41UEc5d0luYndWRCtkQ0k2Zk5VaytwSm1PSUJEQ2FScHBIMEdVZ1BCbFJpL2RvdWxIcEl2QndLSlNhWmFNTDlZcDlDY0hYbk5xcjAxVkNsU3hQbXZSLzQ1K2RHS2tVdE9odEFPWjl2T2xZR3VBK2VxdjhMM2k0dGJ0cnRBK2hGenNNOGRrdFVMenZha0l1NG1VVUVOdEJMMHpuQmVualNSdks0L01rWVRuWUlaV2toNjRwRFVVbHpQTzczdktNeVQrcnExUko0d3hoNUlKK2ZySDltenJUemU4d3dMaVFCbEtCWklJUGh4QmhZcW9EYXZwTmozVDJaemd3K1VyeVFnRjZzNHJaVWdSREtTWjRxanIrUDQ3YlNJNkVnWE1XK1JLc3NteWtEUkdJL3Ntc3NrTFRxN0FVN1ZTdHRsei8xMHJ4aUQ5ek5LSmVpQWNMZ1MzZXdOcW5hZW1mM2FLbEMveEd3OUFwdFUzY2RQeTI3cmtnbmoydDd5SlRJNUFuMExmTThLMERYaGJ6M3lPRWV4RCsxenVMRVlNUDlLUk9ueHB0N2RkK0MzVFV0MWEiLCJtYWMiOiI0YzY1MDAyMDIyM2M4YWM3NTlhYjM3MjM2YTkxN2FmODcxYjBlODYxODQ0NjI0NDQ2MTgyMTJhYWNhNTdjOTQwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijl6REVIN3UzOUQyOHdpMW9OSndtNXc9PSIsInZhbHVlIjoiMCswNXZvZlV2L2Q0TE1CQng0aVpnYUpLQjBaZ2pmOWZ3QkNhTGlvaFRKSkpZUXpxM3BSNDJFRk9sY3BhcmEycEkxTDF6L1VpYUFOdjd0Wm9uMUcvdkVCT2NTU25HcGJYRTB5VXphMHNlZW4waEhZV1RPcVpJemlscVJ6WG5KZGNKNGxBZVlTalFKS3c5RUhyQTdPK1VNSnJuVnlyNWN5blN2RTQ2SjZqOEhhWDdON0xEUkR3OTd1ZDFuMC9BZ3R1Y2pnMGp5bWdmcW44K2hMOUxKdThiZVpqZFdXTy9wN3ExRTRtS1ZzUWVLaHZJSFJOVzUyUmVFUHN3clFFd3pPUHhrd3dCWm1GMksvSE9qbXl4U3YrRWh2VTN0N3BYd2xEOGNpUEIvdzRTbEIrSjBpS3hWMWtLRmIvTkM1Z1NTZHU2N21MWmp1ZUs3TVh0WjBjZzQ5WWdSQ0pnM0YrdDl6T2ZZb2l6S0JOSkw3cUhlMDZ1S0c2ZzAvSHdiVDVRbTEya0lGWmpVdmVXSjl4cmx0S0FtdDQyQzEzVWxHUWpIZGFDdXI4QVVsSFo3dUZtd2JyMEZnay9oeTI0S2hqR2hhSjdDQjZBNE5xSUxSMG5wVlZpSHlyVlEvNDZCV3pPeVdmZWl6ZkQ4dHFINkcwY0Uxc2F3N1EvUlhqWHphdFNyc1IiLCJtYWMiOiIzNjllNWQ0Mjg5YzAwNDFmNmI3Yzk3YmVhZjZkNzk4YmYyY2FjMGQ4YWY3OGFjZmUzODhjOTU1Mjc4YjcwNDQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632414160\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-578659957 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578659957\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:35:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhHOCt5aUgwVWQ2Y0xNTnAyUFJKMGc9PSIsInZhbHVlIjoiZTQ4YVM3QjZqNmwvYUUwQmNldUNiaFc4U2hhOHBXcis4N3J1UjdrTVN4c3NwaHdlbEtSNVBaS1VzbGp2RXZYdVdsWXVndHFCdnFiYklJQW5teDZlZFp1L1lqdjMxTVB2bHFDZG0wNEw5VVA0aERjNllCNHY2K1I4V1Q3MGRjbjQ0K2dDSWUvbEtzMHlnNkc4T1k5KzlQU043QitwRTZDdkk2dW1xSWM5cTI5NkxhelJCV3g3Z0xJWm9OeGhDb3pISGlzOWhkcWllOEVpWEhYWTkrb1ZCN3JOZGhYL21vd0dEVXJUS3dGSXhiLzJVTU4vSkhqeHhQVGx6dWYwVGp2MVl0blNqaXhnL2dRTEY1MDQvcHkvTE0wV3RsRkVUdDlnVHJ1V0lRckQxcFZhaTAveXNFQkFyS0lJbmdXYXY5V0VabnQ1ZFd4akJ5WC9EbVg1OWIzNW8yQVJ3b2I0cGxZUXhpeGFyS3ZvcFlSQkExMXFJaUNFbGVrSGFYQ09mM01QZTZ2Q055OXpXK1g2YUpUam0rekFWblA5MFBqYTJBb2ptQ3JYdk8zU0VJajR3VE5qZXZnSFd0OXhRRjBzWHlJUEtoMHhuYUN4b2dHSEJ6SUxIdCs5eWNFUHVhemRVaHJhclFKRDZ6NC9vVWl0OEc2b2d6YXBjY3I3K05CT3JnU2giLCJtYWMiOiI1MDg1Zjk0MmExZmRhNjMxNDVmODI5MzIxMzg0YjYwYjgxNjI0NWU3YTI0YWUxMmQ0OTMxYmY2NjRjNzQ5MmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdISENKNEJPNWJ5Z08wMmV0eTQxMFE9PSIsInZhbHVlIjoieDZ5OVJseFVCWitaMFR5eXE3a0lWZUpNdWJKc2U5UWhTenBHbnd0VWdsSG11RjRzSlloT1pNTFZNK3kwM1E2UGMzODdEZ0R1OExXNno0aGtpVnNJUDNsRzE2MCtCVVlFekNqa2p0R3NQVGo5MVhlU0VQWmNrallnVEYyU0txQ3RyTlEyeXBEMHUzbTdWcnd6bGxqbTZXbkVUb2JoWDFIWXhyUXk4ZUEyN3RGOVEvbWFjZ1BvY0VkVzRUSDRsNFhaanp1SlpJcFlteUNGQlpxVUh6UlRSWDlwUEcraW44eVBobExSRUlDbmlsZkVZUHNTSDZLRks3QktjSjVBNDg1NkJoZ0tJUGxUVmZ1WTVPM0tPeU41UGZnbjJZeWpVVENhS1JpTEd6N2JNcnUzTWUyaHUwUXJJMGY5dk1DWVAwM3N3ZSs4MDV5SzlobzBZVFd1MXlTSld4OFVza1RHQ0c0ZkF6Q1BmdTc1OFg1NXJyUXYrcjVtdlFDZUVJWk9KaWU5RXFjbmRPckVvNWFNeTdRdmY1WGVieTdzRnBUWEZLNDZ3TVN4bS9XTzlYM002cU5XeE8xWE5CeFR2a0tkbjZnWUZsT2s4M3hSd3NEc05oaVVqaGFXTXNDMHorMnhvWCtqMGxVcitGbXNZWWE4VklEVWVyYThXYVlVUTNHWGk1M3oiLCJtYWMiOiJkMTc4MzQ2ZDY0YTM1ZTg4MjA0Y2ZhMWI2ZDNlZjI1YTJkNjZiZjg3MDBmYjlmZGUwNjE0MDk2MjNkMmMyOGFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhHOCt5aUgwVWQ2Y0xNTnAyUFJKMGc9PSIsInZhbHVlIjoiZTQ4YVM3QjZqNmwvYUUwQmNldUNiaFc4U2hhOHBXcis4N3J1UjdrTVN4c3NwaHdlbEtSNVBaS1VzbGp2RXZYdVdsWXVndHFCdnFiYklJQW5teDZlZFp1L1lqdjMxTVB2bHFDZG0wNEw5VVA0aERjNllCNHY2K1I4V1Q3MGRjbjQ0K2dDSWUvbEtzMHlnNkc4T1k5KzlQU043QitwRTZDdkk2dW1xSWM5cTI5NkxhelJCV3g3Z0xJWm9OeGhDb3pISGlzOWhkcWllOEVpWEhYWTkrb1ZCN3JOZGhYL21vd0dEVXJUS3dGSXhiLzJVTU4vSkhqeHhQVGx6dWYwVGp2MVl0blNqaXhnL2dRTEY1MDQvcHkvTE0wV3RsRkVUdDlnVHJ1V0lRckQxcFZhaTAveXNFQkFyS0lJbmdXYXY5V0VabnQ1ZFd4akJ5WC9EbVg1OWIzNW8yQVJ3b2I0cGxZUXhpeGFyS3ZvcFlSQkExMXFJaUNFbGVrSGFYQ09mM01QZTZ2Q055OXpXK1g2YUpUam0rekFWblA5MFBqYTJBb2ptQ3JYdk8zU0VJajR3VE5qZXZnSFd0OXhRRjBzWHlJUEtoMHhuYUN4b2dHSEJ6SUxIdCs5eWNFUHVhemRVaHJhclFKRDZ6NC9vVWl0OEc2b2d6YXBjY3I3K05CT3JnU2giLCJtYWMiOiI1MDg1Zjk0MmExZmRhNjMxNDVmODI5MzIxMzg0YjYwYjgxNjI0NWU3YTI0YWUxMmQ0OTMxYmY2NjRjNzQ5MmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdISENKNEJPNWJ5Z08wMmV0eTQxMFE9PSIsInZhbHVlIjoieDZ5OVJseFVCWitaMFR5eXE3a0lWZUpNdWJKc2U5UWhTenBHbnd0VWdsSG11RjRzSlloT1pNTFZNK3kwM1E2UGMzODdEZ0R1OExXNno0aGtpVnNJUDNsRzE2MCtCVVlFekNqa2p0R3NQVGo5MVhlU0VQWmNrallnVEYyU0txQ3RyTlEyeXBEMHUzbTdWcnd6bGxqbTZXbkVUb2JoWDFIWXhyUXk4ZUEyN3RGOVEvbWFjZ1BvY0VkVzRUSDRsNFhaanp1SlpJcFlteUNGQlpxVUh6UlRSWDlwUEcraW44eVBobExSRUlDbmlsZkVZUHNTSDZLRks3QktjSjVBNDg1NkJoZ0tJUGxUVmZ1WTVPM0tPeU41UGZnbjJZeWpVVENhS1JpTEd6N2JNcnUzTWUyaHUwUXJJMGY5dk1DWVAwM3N3ZSs4MDV5SzlobzBZVFd1MXlTSld4OFVza1RHQ0c0ZkF6Q1BmdTc1OFg1NXJyUXYrcjVtdlFDZUVJWk9KaWU5RXFjbmRPckVvNWFNeTdRdmY1WGVieTdzRnBUWEZLNDZ3TVN4bS9XTzlYM002cU5XeE8xWE5CeFR2a0tkbjZnWUZsT2s4M3hSd3NEc05oaVVqaGFXTXNDMHorMnhvWCtqMGxVcitGbXNZWWE4VklEVWVyYThXYVlVUTNHWGk1M3oiLCJtYWMiOiJkMTc4MzQ2ZDY0YTM1ZTg4MjA0Y2ZhMWI2ZDNlZjI1YTJkNjZiZjg3MDBmYjlmZGUwNjE0MDk2MjNkMmMyOGFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}