{"__meta": {"id": "Xcdb5122f7759a281563b3931defbeeb5", "datetime": "2025-06-08 13:35:55", "utime": **********.462899, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389753.930574, "end": **********.462927, "duration": 1.532353162765503, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1749389753.930574, "relative_start": 0, "end": **********.157479, "relative_end": **********.157479, "duration": 1.226905107498169, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.157502, "relative_start": 1.2269279956817627, "end": **********.462931, "relative_end": 3.814697265625e-06, "duration": 0.30542898178100586, "duration_str": "305ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53630448, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.010889999999999999, "accumulated_duration_str": "10.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.305491, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 49.587}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.340999, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 49.587, "width_percent": 11.387}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.39239, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 60.973, "width_percent": 11.019}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.398572, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 71.993, "width_percent": 9.55}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.411337, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 81.543, "width_percent": 8.173}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.422276, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 89.715, "width_percent": 10.285}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1863143281 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863143281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.40916, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 2\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 20.0\n    \"originalquantity\" => 20\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 40\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-1050905842 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1050905842\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1792997224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1792997224\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1776641473 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1776641473\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1068466431 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImN4TGNEVmhIY3F5aXRYQUpEbnVBVFE9PSIsInZhbHVlIjoiM2grNnFSUHBYSHdQaVpneHZMRVFySkRuMXp6V2VHQlJrb0VOYWpMdjZCdzFwM1l5b2l6bTd1SnhnYUhITU5lZWdJdFUxT2Q4bG1zaGRVYUlLQmU5VXJIR1JvSERUbFdNTWU2WjdFRDdlREVweEVNMzhwem16UDFGdC8zK3N3OGZYNGVhWUFkeU12UEJ1akdDdjBFRUlkdWk3ZU5YdGRCd29lMGdyMjlrZEtrMDlyUFlhaHZwSENaSVBtZlFtbXM3Mm9YN08rRU52NG1SV0xjeit6aWpTNlFON3dXbjN0M3U0MHVQV2J2L3gzRU9aYU5Qc29QUzVIQk05NGc2aXpCbXpxQVp1Q0F3OGF5YlByRS9oMTBoaldKNDhkakJBbXNDNVlMKzZhNHljOENxNnkySWVOTlR5ZXRic1daRGJGS011aktJTFNIc0J2Z3E3bHBmT1ozYWwzb3RPTmFYY3AvY1p4Z3pRWmJPejZhYmltNlNjOXQzOHVoNU5vMVRyQWJxcUZNcHhIUGlCWkU1aDNRY3JEVVdKaTlqM2RaSGI4ZEYwT3I0ZU84ekROaHFNMGxjVTNuQjQ3NkEzdGZ2eWhLVzFHTThrNE56ejRpbXJLTkVKTnhqTkM2ak5iZGtKYjJXc1BOVmdsYkFQNlpJV3VTNHd3RTF0OWFZL3VHL09ibE0iLCJtYWMiOiIyMWMyNTE0NTEwZDcyNmQ1ZWFkY2NjMDViNGRmNjE5MTRiYTRhZTBlMWUxYTAyNWQ4ZDBjOGY3NDU0M2QzMGQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5Xeks1SVpqd2d4MEVndG1xaHlOanc9PSIsInZhbHVlIjoiWVYyczFQVnA2MXJVNUM4ejdFL0srVVd2Tjd3NFdhSXJtK1ViTXRYbTJhVTlYbW5hR0ljSWYrSW1EWHVBUmRmSmo0dGw3VGJtc3RIdEcyK05UVUVBelVXeXpUYklMazlCNDh3aHFHRGEvOWZBZUxMT2hCN3A3NUtMaGNMdjVFUFU1ZDNXVWVZSXlCYUk2OGN1Mlo5OGNhcysxc293ZU44dVZSYlc3UFErQjAxYUttZURHNjFxT1JvTHJVMUVHek5tMzFWS0NRZlpBT0VHU0dmaWJkNFI2YWxGclc1eDdaNDlOZnpVWGNnTmRYRlQ4QmxibFJqSXNZZjF5RjQ1NkZxb3BaR3VVU0RZcDJsek1Pb2p4TW5NR3RmZ1pxNlB1UDhBcEd6bXRud1MvNnU5aUw2eE9obnJZc0krRkduU0NLeVduZ29ZbE9HYmh4R21rRm5mTWJSNnhwMkY5TnpjL2orVnlzUlZIYzZkMDBBLzk0bHRadGhJdEd6VE40cTgzNzlsaXlId2Z2d25GWk9vdnJQZTF5VzErM2hUaXF4VmgvcFdBT3ZmSkpjaUZIUTlSeE5yNEgwM0IrK2RwRjJxRC9QVDI2Yk5MeUN6azdubGU4Tlo3S2luTUVRaFJaaDlTNUVKVHVmZzRaUTlHRllqWVU4SXRodTlPbmErb2RrS0RjTEkiLCJtYWMiOiJjZjA4YmM3NGQxNDUwZmNjMzQ5Nzk1NDUwZGUyNzc1YjE0ZDYyNTVkZGM1NWVjOTJhZjVkYzU3NDAwYWQxYTE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068466431\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-937938713 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937938713\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-942354508 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:35:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh2aFZoNVpwWk51OHNkaDlINjduWXc9PSIsInZhbHVlIjoicVlDZ0hoTjNFT0lUTFJFN01GcExVdU41S3VPcVphUTJqekpwWVh6YjJrTEZXWkFoengvOWhPQi9ncWUycmlvaWR2N1JQVUJIUDJGZXVDSlgvNzJMUmhGRTRrY0ttRHdkeHlDalBjQm54cEJBeXR0c3B3MUltWGlWR2l5UjlsdEdsWjFPb0kyZ21WM0ZzZ0RvdjdhTTJSaGcrNDd0U2RBa0NMWDVsSkZXUUNqSTlmcVRvUWlKZ09Rd25TTlp6MnFxdXRPb0JOQlF0NktORlpJN0c5VXZDb2dCajV0d0lscCsrRElUMmpwR1pJTGVZVTNvSnZFWGdSRTVROG9wMFA2UDQ5QlptclNpbE82aTN2N283dWpiMTAzYzBIdWJIcWdFamFVL2FLUlVLY3JXb3JjRDBuRDVmbXpTZ3dvV0pyUVYwcXNjQVVMUnZkQXoxQldUK2tlaDBUMEdzZFBqc1dlNFBFUEV3ZDFUZTVjTFBRU0d3UXpqeE0yNXJMNWhMOTZYL25CNmFRb2VTQ3lsQnBKcmZCL1JDQ3JOSGNiaC80UEtFd1h0R3JFcGswdE5LS243dGg5Sm5NMWpoMzVPOXkvL29vSWVsVFN2RVlIK2FOaVluMyt6OWtsb0dPNjZEaGFKbHZXQmloaXNmR0xqeUhneERPNXdYZWoraTJQc3hDV0UiLCJtYWMiOiI0NTMzZjc0NjJkZTgwMGZmOWMxODMwNDNhMjkyNzYxMjJjMGY2ODA4ODM1MDZkMjA0ZjY4MzBkNzM1YWI0NzkzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5ZdDJxbFRqc3JPNjM2RmM4c2JDWEE9PSIsInZhbHVlIjoiL2ZTcWtLRVNCaW5sc1ljTFdJT2V5UkFYZXNDcCtXYzBJbERSZjM2RTRjblZ2L1lqTE9VcW5lT3dRVHk5d2ZPaWVFdjBBUFdZaFJkSXMzcnQ1MHpydE8waUNLN04rOEpOQTU5d3lWaVJIdTZEV1BQTmpsNUJDcVl0c1lYTFBOZERMTEozeDhoVW5Sd1htZDU4T0MzNWxpSUJyQ0ZNdlBpek1sK0IvU2xNc0tLc0JBdVZHcHAzdWwxRmtWdEpML2c1T1NoakxxNHlTbXZCd1Jtd0lPdmVlWHp6Nk5xV01CYUJqVHZmdnV1S2ZpS3VYcW5SV1dHVFNZTWY2R09WU1FXOXFyaDFkTkpyWnh5QllONXJqVDRQVlFxZ0dCbWN2RmpZNm5kYm9ZaWhxVjh0dUpPcSt3RHo2cTJwdTJELzZ2STlrSkZZcXBrTEhQQVpIMFdyUzltcG1ORVhnQ2l3ZXhsaDJJZTMzVlJnMFMwUVlBQUtVazB0OVh6YmcvVDVEZzNSZVFDZTUrL2dycXc2d1VnTUlCNUpNYlJhTWw1eS9Eem9BR1luUldOcXZzeXBOSmh4dllWb3RLeExVaHFKLzhYbGdOQnIxbDVyZjFlbnFCQVRaMmhQdldKcWpTcWhkcit5elJWZWJyUnU3VC95ZGh1VUhJN0wybXdsSnk4bWplelUiLCJtYWMiOiI2OTAzZWJhZWVlMzJlZDczNDVkNjZiODFlZjFlZmYwMTM2NTQ3OGE2Y2E1MDY3Mjg0Zjg0OWRiMGM4Y2JmYzk3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:35:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh2aFZoNVpwWk51OHNkaDlINjduWXc9PSIsInZhbHVlIjoicVlDZ0hoTjNFT0lUTFJFN01GcExVdU41S3VPcVphUTJqekpwWVh6YjJrTEZXWkFoengvOWhPQi9ncWUycmlvaWR2N1JQVUJIUDJGZXVDSlgvNzJMUmhGRTRrY0ttRHdkeHlDalBjQm54cEJBeXR0c3B3MUltWGlWR2l5UjlsdEdsWjFPb0kyZ21WM0ZzZ0RvdjdhTTJSaGcrNDd0U2RBa0NMWDVsSkZXUUNqSTlmcVRvUWlKZ09Rd25TTlp6MnFxdXRPb0JOQlF0NktORlpJN0c5VXZDb2dCajV0d0lscCsrRElUMmpwR1pJTGVZVTNvSnZFWGdSRTVROG9wMFA2UDQ5QlptclNpbE82aTN2N283dWpiMTAzYzBIdWJIcWdFamFVL2FLUlVLY3JXb3JjRDBuRDVmbXpTZ3dvV0pyUVYwcXNjQVVMUnZkQXoxQldUK2tlaDBUMEdzZFBqc1dlNFBFUEV3ZDFUZTVjTFBRU0d3UXpqeE0yNXJMNWhMOTZYL25CNmFRb2VTQ3lsQnBKcmZCL1JDQ3JOSGNiaC80UEtFd1h0R3JFcGswdE5LS243dGg5Sm5NMWpoMzVPOXkvL29vSWVsVFN2RVlIK2FOaVluMyt6OWtsb0dPNjZEaGFKbHZXQmloaXNmR0xqeUhneERPNXdYZWoraTJQc3hDV0UiLCJtYWMiOiI0NTMzZjc0NjJkZTgwMGZmOWMxODMwNDNhMjkyNzYxMjJjMGY2ODA4ODM1MDZkMjA0ZjY4MzBkNzM1YWI0NzkzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5ZdDJxbFRqc3JPNjM2RmM4c2JDWEE9PSIsInZhbHVlIjoiL2ZTcWtLRVNCaW5sc1ljTFdJT2V5UkFYZXNDcCtXYzBJbERSZjM2RTRjblZ2L1lqTE9VcW5lT3dRVHk5d2ZPaWVFdjBBUFdZaFJkSXMzcnQ1MHpydE8waUNLN04rOEpOQTU5d3lWaVJIdTZEV1BQTmpsNUJDcVl0c1lYTFBOZERMTEozeDhoVW5Sd1htZDU4T0MzNWxpSUJyQ0ZNdlBpek1sK0IvU2xNc0tLc0JBdVZHcHAzdWwxRmtWdEpML2c1T1NoakxxNHlTbXZCd1Jtd0lPdmVlWHp6Nk5xV01CYUJqVHZmdnV1S2ZpS3VYcW5SV1dHVFNZTWY2R09WU1FXOXFyaDFkTkpyWnh5QllONXJqVDRQVlFxZ0dCbWN2RmpZNm5kYm9ZaWhxVjh0dUpPcSt3RHo2cTJwdTJELzZ2STlrSkZZcXBrTEhQQVpIMFdyUzltcG1ORVhnQ2l3ZXhsaDJJZTMzVlJnMFMwUVlBQUtVazB0OVh6YmcvVDVEZzNSZVFDZTUrL2dycXc2d1VnTUlCNUpNYlJhTWw1eS9Eem9BR1luUldOcXZzeXBOSmh4dllWb3RLeExVaHFKLzhYbGdOQnIxbDVyZjFlbnFCQVRaMmhQdldKcWpTcWhkcit5elJWZWJyUnU3VC95ZGh1VUhJN0wybXdsSnk4bWplelUiLCJtYWMiOiI2OTAzZWJhZWVlMzJlZDczNDVkNjZiODFlZjFlZmYwMTM2NTQ3OGE2Y2E1MDY3Mjg0Zjg0OWRiMGM4Y2JmYzk3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:35:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942354508\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784216512 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>20.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>20</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>40</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784216512\", {\"maxDepth\":0})</script>\n"}}