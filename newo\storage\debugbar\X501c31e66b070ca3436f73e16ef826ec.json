{"__meta": {"id": "X501c31e66b070ca3436f73e16ef826ec", "datetime": "2025-06-08 13:07:02", "utime": **********.369655, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388020.916517, "end": **********.369693, "duration": 1.4531760215759277, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749388020.916517, "relative_start": 0, "end": **********.166987, "relative_end": **********.166987, "duration": 1.2504699230194092, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167013, "relative_start": 1.2504959106445312, "end": **********.369706, "relative_end": 1.2874603271484375e-05, "duration": 0.20269298553466797, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45401608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02359, "accumulated_duration_str": "23.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.284204, "duration": 0.021070000000000002, "duration_str": "21.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.318}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3383648, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.318, "width_percent": 5.511}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.34822, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 94.828, "width_percent": 5.172}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1217606762 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1217606762\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1824932302 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824932302\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1xcmwyNTRoUXdTVkJlV0VtVFBzY0E9PSIsInZhbHVlIjoiTU5SZ21ZTG91RWJ2ckUwZGRKU1BLNlR5bms2RUt6VUY3Y1dvRFZDc1dVR3ZqbXF0aTFkK3RRcUZZZTZsNWU1cjJ3NUVHV0hjVjNFbUFrMnpJTldzQ1ZBd254dWI3L2IwTHJzSWQrZ1hzU0FKdnM4ZzZOWWxwVXNyNE1IZWR2UDBraU1sU2h2QzBxWDhmTjZ6c2VrLzVLWU43eTdNOWxWQ0tUaFVaazRURFlJUnlIR1gvVm1uS1BFdGxkWWxLTVRMQ25Mc2RZMk4zTDdaVGpvVHZVdWpUdmdNMXlKYlNiUGFvTXUzQ0twVUlmL1pqNCtNSzVhSEFtOUZIakpUdlRWZFdGd2dZVTMyRXh5Nzg0bG4yQTlJT0tlNXQ5UDFXSjhrNms5SllRNmJndStiSGkvRFlvYTlhUzJSYTU5WEMrNDJGRnk0cDlsdzVSOVg3KzlGMDBuS0lVSHJLU3dyQ0xwUVhLdWNTaUg1bVd4N3QxL2RhMDduRVdPakxSbGZCQnNreUIyWE0xN0tCcDdEL3FQdmo3MzQ2SmdYZHRWWU9ud1BucUYwbGdBeHM0azhta2FIWXEvZTV0Y0x0aHBCVU9neXNFT1FkN3FjTTlLUGN5Q0J5V0kxU3l4Rk5aUnNjM1hPU254NEN4MUtLcjZxNHVXSExKK2owVjA5L3BBZTQra2EiLCJtYWMiOiI2ZDk1MTViYjYxYWU1ZDIwNGQ2ZDkwNDhiNTc4NTU1YzRlZWMxY2UyZTdkNmI5M2YyNWE2MzUyY2JmYWUxNGE0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdaOG51MlVuWWh0UEJwQ1V3QUFXeUE9PSIsInZhbHVlIjoiREtLVjNaV01lV0g1M2RMQURxd3NyRGJLa1BmRlZOSXFDOU5qSS8rOFlPUUNZUWllM1NyQm5UYklRbEQvZTNDQ2E3cGkwSkNueFdLS243VXMxa2xhNFlTUDBUblNUK3AxOHUzR2hDcXF3Wm5WZ3VhODlzaWEwSURRSVVZSllTc0hENmUrYlFyUExMKzhjNkNEYWc4VUw0RmpJS1pFekdvUEZqTUZwTWJaUjd0MGFYUC9jNzhWazRtY2pMR2ZqSkVJbVF1S292eFB2Z2twbFlQaGdLZ2ZMSlB5cHZhcXBWTk9PcHJiZW5IU2haVkZadGJrMGpxaWgxVzFzNllxV1Q4TnA2Z3FUWTg3RzdsOEtGeUxESnE3YWhMV01YUHFxcW1NOVVocy9NeHBmcms2T2FNY2NydEliVmE4VXpOVGp4eUFaSGphQWlqcGhMazFVbytHclBjY2ZaWTFQL2dFM0F1bzBkajRYTmxCRVQ4YlhaSDR2Tjk2NENLc0tNN1Bnb0U3dU9mS01rMDZaaHhZemgrRkZhL3d2cWVtYmp4S3hERDFhbTNwbHlnVmVVNzkxVEdJcHc2VlowZUN1N1B5a3ZLbHhlOHh3SlJQSGtVcmN4djZKd0JPcmtvVWRtRit6RnUrajVzYXNnaG9IMHVsbURLSDNiRjJsT0ZTZis2U05aWWkiLCJtYWMiOiJmM2FmOTU0N2ZlNDIzZGI2NmZiYmIwYTJjY2U2OTFlY2FhNTUyZGNkM2UyMTI2Nzg4NjlkOGJkOTc1YzM3ZDAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-942968997 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:07:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5ITWhNbHUyejdscVN2dFBCNURDV1E9PSIsInZhbHVlIjoiTi9PRERLa01QSXNRd3E0SnlnTkdYVHVQS1ROYWJDTGVySU9MMGx1RGhTajBjOEEvMHRYMjVwWkZja0xkNWEwS3QzdWM2VHBFYXhLc1RSSUNuVWVDbHpVbUdwRHA3bnBVRHJqa2doR1pjUnM0TXAvT0g2WUJNRmo1cUYzMVFPbHV5WGZyaUdhbHNaTTVKaGVsdnB6bHIrTVZJdTZ0SWIxSDVVMmtOQmZib1BoblZUL0h2UEFvS3RDclZOam5mM2FPcko5QWtxa0tMVXJxWXJLemYrQlJVRTd4RU5nYWRQbWNQR2lzV3J6Wm5ra00xTW5ldG9pMGNpTnpwMmcvVG5GMjNQKytTVThKZ3hPMWtodTByQWx4eXlNbWtmRjZXRnRDNTVRZnNxOTFsQjNLTGJsRlRUZ2ErVVBlWFdWUDFsMEMweGJ0ZHBPMUcrcWVMN2NyRU5VR3JlNVFDR1FkSnBMN29ZbXdsNVZTbDBSeWJjbXhJMkdrMEx0MzRDbXZUcFpoR2d3S2ZRMytBaktldDlTUVJWRlZkRkVkc2RZNHh4Y3FEelVnSXRURkY4VTA5c1RXb3B1SzIyOXo1MFFqQlhva2t1K1VmcWlWVXJPcTVjSkZXMFFmUWNzNGJZbEVZdVQxOXhLbGVucEtyVFBMc09xUzNVNERTelF4clVHZ21vaW0iLCJtYWMiOiIxOGRlOWE0ZTJkOGYzZDQ4NzIzMmUwYTU4ZTBkYWU3NTQ5Y2Q5MjI3Mzk1MjAwNDU2OGZiMjdlNGY4OWRkMDFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpiYk8zQ3dKN1kzbm1Fb3BFRmRZaVE9PSIsInZhbHVlIjoidjBGSjBPWUI3L2JXSi9mejRIb1pSRkMzdUsvT0NYUWpFMFcwRXBSakpFR3g2S25PbElXaFo3R25pbzQyYXRKRGE1UER2Rk9YWDU0elljRStCcEVBM2ZGMC9FcE5yOU9mRVRyRFI5OXVGTmVIZFMzQnlGMjhmOE9FMzM2WTlESFNibCtLdzdUT0RrRm1aSmdIRWNYRThNaEE3ZmJVL0lIem5mSlRIVUZFY0hLajBFZHJUZmVvemxvaGdsUUpXY21YL0l6N2lXSTlwVXJIQXkxUTlJM2lTK0ZYR0JWU2ErckRZLy9BdVJmeHVHOGczNlh0RmZZcElzU0Q3cFhYQlA3emsvbDBvK1BxQ09GQnB1MnpGeEVLbDl0c2UxZFovME9LeWp5QU0xU3JNeGFtVHVTSXZ3SDVjcWRKZnpVUUZ2RWFGcXlTdDAxTHdQblZjNjFrZ2V3TW5zcU1ZbnNXV2w5SkpoRjZZYmU0REhoYzR4QU92VjIzMTJ4WlpTMTNTVXd1WXdiTXYvVTUyWXZQVFVGcFRZcjI5aFg1MVBKd3oxdXRkS1FyOWVTa2E3R0xaSXE1ZURqbUwwN25EYjU4dE53dFNLVEVxZGk1YXVkNWZKNDhsMGU3ZjlEZXNMa2RKbnIzMmY5VkxDNGMrZ1g4VVcwcDdUUkxiSXJ2bUVKemNINHciLCJtYWMiOiJlZGQxODZkYjY2MzFjYzU5MmYzYmUxMTAyZDJjYjI4OWRkNGUwOTZjODI2ZjNjZjg3ZjI0Njk3MmM5NDk1Mzg3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5ITWhNbHUyejdscVN2dFBCNURDV1E9PSIsInZhbHVlIjoiTi9PRERLa01QSXNRd3E0SnlnTkdYVHVQS1ROYWJDTGVySU9MMGx1RGhTajBjOEEvMHRYMjVwWkZja0xkNWEwS3QzdWM2VHBFYXhLc1RSSUNuVWVDbHpVbUdwRHA3bnBVRHJqa2doR1pjUnM0TXAvT0g2WUJNRmo1cUYzMVFPbHV5WGZyaUdhbHNaTTVKaGVsdnB6bHIrTVZJdTZ0SWIxSDVVMmtOQmZib1BoblZUL0h2UEFvS3RDclZOam5mM2FPcko5QWtxa0tMVXJxWXJLemYrQlJVRTd4RU5nYWRQbWNQR2lzV3J6Wm5ra00xTW5ldG9pMGNpTnpwMmcvVG5GMjNQKytTVThKZ3hPMWtodTByQWx4eXlNbWtmRjZXRnRDNTVRZnNxOTFsQjNLTGJsRlRUZ2ErVVBlWFdWUDFsMEMweGJ0ZHBPMUcrcWVMN2NyRU5VR3JlNVFDR1FkSnBMN29ZbXdsNVZTbDBSeWJjbXhJMkdrMEx0MzRDbXZUcFpoR2d3S2ZRMytBaktldDlTUVJWRlZkRkVkc2RZNHh4Y3FEelVnSXRURkY4VTA5c1RXb3B1SzIyOXo1MFFqQlhva2t1K1VmcWlWVXJPcTVjSkZXMFFmUWNzNGJZbEVZdVQxOXhLbGVucEtyVFBMc09xUzNVNERTelF4clVHZ21vaW0iLCJtYWMiOiIxOGRlOWE0ZTJkOGYzZDQ4NzIzMmUwYTU4ZTBkYWU3NTQ5Y2Q5MjI3Mzk1MjAwNDU2OGZiMjdlNGY4OWRkMDFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpiYk8zQ3dKN1kzbm1Fb3BFRmRZaVE9PSIsInZhbHVlIjoidjBGSjBPWUI3L2JXSi9mejRIb1pSRkMzdUsvT0NYUWpFMFcwRXBSakpFR3g2S25PbElXaFo3R25pbzQyYXRKRGE1UER2Rk9YWDU0elljRStCcEVBM2ZGMC9FcE5yOU9mRVRyRFI5OXVGTmVIZFMzQnlGMjhmOE9FMzM2WTlESFNibCtLdzdUT0RrRm1aSmdIRWNYRThNaEE3ZmJVL0lIem5mSlRIVUZFY0hLajBFZHJUZmVvemxvaGdsUUpXY21YL0l6N2lXSTlwVXJIQXkxUTlJM2lTK0ZYR0JWU2ErckRZLy9BdVJmeHVHOGczNlh0RmZZcElzU0Q3cFhYQlA3emsvbDBvK1BxQ09GQnB1MnpGeEVLbDl0c2UxZFovME9LeWp5QU0xU3JNeGFtVHVTSXZ3SDVjcWRKZnpVUUZ2RWFGcXlTdDAxTHdQblZjNjFrZ2V3TW5zcU1ZbnNXV2w5SkpoRjZZYmU0REhoYzR4QU92VjIzMTJ4WlpTMTNTVXd1WXdiTXYvVTUyWXZQVFVGcFRZcjI5aFg1MVBKd3oxdXRkS1FyOWVTa2E3R0xaSXE1ZURqbUwwN25EYjU4dE53dFNLVEVxZGk1YXVkNWZKNDhsMGU3ZjlEZXNMa2RKbnIzMmY5VkxDNGMrZ1g4VVcwcDdUUkxiSXJ2bUVKemNINHciLCJtYWMiOiJlZGQxODZkYjY2MzFjYzU5MmYzYmUxMTAyZDJjYjI4OWRkNGUwOTZjODI2ZjNjZjg3ZjI0Njk3MmM5NDk1Mzg3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942968997\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}