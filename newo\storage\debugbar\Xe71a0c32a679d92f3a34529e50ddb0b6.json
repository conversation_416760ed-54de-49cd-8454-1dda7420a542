{"__meta": {"id": "Xe71a0c32a679d92f3a34529e50ddb0b6", "datetime": "2025-06-08 13:26:53", "utime": **********.920082, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389212.587547, "end": **********.920112, "duration": 1.3325648307800293, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749389212.587547, "relative_start": 0, "end": **********.758296, "relative_end": **********.758296, "duration": 1.1707489490509033, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.75832, "relative_start": 1.1707730293273926, "end": **********.920115, "relative_end": 3.0994415283203125e-06, "duration": 0.16179490089416504, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45184904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02015, "accumulated_duration_str": "20.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8486722, "duration": 0.01802, "duration_str": "18.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.429}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.892247, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.429, "width_percent": 5.31}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.901413, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.739, "width_percent": 5.261}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-752503381 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-752503381\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1338866179 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338866179\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1836148232 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1836148232\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-105834053 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlZISG45VERZbEtQaU9RaTRxbnBiQ3c9PSIsInZhbHVlIjoiRGplMWZ2SmtkUnI1b05QS2RtVm8vblZ4NkRVRVAzUEpTa3JJMXhFYVZzMDg3WmJRVkRCNmlKd2gxY0psUGNTU1NNVThPeGRuRW9zVVdjNXJXUXhXdXA1ZVhLdTNtc2NtN3hDcDZhR3JNTW9kd25JdlV6aVp2ZzVkVUdaeHVCTmh5QVJ0djJvbVovVDJLck04aG4zRVkwQnBSS2RPdEs3Y1R1bmFNWEJodmcrTUQ1dzdkRXNzQThuV2tRUTJsb2YrNlFOUkpWNXpDc1pBbTBSK0lCRk0ydFJDbUl2RDRDd09UbXU2TkVpQ1lxcGpZSFI5TG5iY0hmTGxRemlnN25ULzRDbGczcVVNRzhpSDBhbG1NV0UrUUdGZzNOSmxpMkdsQml0K1lPaTEvTlpScDlPbG04dUQyazZBY20xU0thSFp1VFVQN2g4aUF5a2drN24xSG96TXhiUk5HdktsbG1ja2xha0lKS0NrZVBtb1I1SmJVVm50V0p1K3EyUExtZVgxUDhZLzcrSFAxd2pPb0pXdkQ5Yk9qTkZHMVRtU0VFRmtoLzZLejkxUFQ1NXEzb3pjcnFkVEVjU1J4K3NBMzgvTGFPNGdVK1huTmRFaUtNTDREZE02by84RzNRR0V0czNMdyszVFdub1c5MTkyNjQ1bGF1bVNVVk16ck0veEtvYm8iLCJtYWMiOiIxOGZiZTFiZmRlMzg3M2U4NDYwNjljNTU0NGJlMGZlYzkzOWY4OWI2Y2M4OWUzNGUzNmI5ZGUwNjIzMDlhOTY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllHZ0tLazZmVm1wUTl1aWkzL2JqM1E9PSIsInZhbHVlIjoickloM05hOHlXUFY4YWlRMEUyYkhKQjNJT2dZZ0ExeEJ5dUYzcDdoOTRxRVpiZW1YYXFkWFlRa2F1aVVTN3pWV0JPSUFlRmYzcm9pSkMvT25jUElBWEl5KzVpWjZiellJM2RhTXpGQUcrVUVTUHhackY5WVMvUDBqL01TUHF5Rk1LK3pKeXhIdWc3ZCtHakpiME9CTFdxdUpxMEo1Tk1STndIZXo3ODE1bGg0OWNKcjhyTGVjSC9jVUdnVXZ6THlwcjNQbnFSQi9sT3pwanZ2TEV2NVZpZ3NEQ1FXdDJXbHBwZ1p5ZFFQamJNUGw5bDZiWHFKNVJ4eFgwTnNkY2JMQlNBMFIxZ0FaV2x6Wi85RGNLcEhYZWhkR3FjaTluMElHakFKcTdjUm9wOHBma1d4TW9FQWNWU3o4Q1VtUFZYZ1lXZVcrdmdRNHN0UTduODBTSTNtNVBlZHU5eGlMYldraWlQMm1UZENxekJhajRFSTlGc243S0hrZ1FZZXVLUFJYYTFiRUNQcW5NMjhPcnR6d2VtSkV3cUk2TzBpNy9PMXpJVU4yY0RqbGoyVDUvcWhwZlAzdTk0eUhTVm9yVjZqYmJjUXRhcUxIK1JPTUVnd3VNM2J0azgzSGtEbVh6OFlKVVFiTTZJbXZmOE9MTTZjSnVnT3NUVW1IditVd2trb1YiLCJtYWMiOiIyMTQ5NzdjMTZjZDIyZDZkOWFmOGEwZjFlYzMwNDJjYmU1NjBhMjY5ZGE5NDE1MDMzZWFjN2JiZTM4MDA1MjU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105834053\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-546419302 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546419302\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1565963396 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:26:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9qeW53VTZvUkoyQWxRVXFhNGwvRlE9PSIsInZhbHVlIjoiV00zR2RZUkhaTWtDVnJYUGlCTWVGSHBuZlhSeXNSQVdjakxlc29qVnh1MDRPZTIyTEJ1RUc5VUFPMzVySEQ3RWdkMFgzTFZ1aTNEVVQydk0rMnNhYVUvb09ldDhYSXpMY3ZjV0R4Qms4SU1rMXNkcnpTcEErRng5ZS9mZnBsVUZER1Jxd0V0M3lQVEk5eDJvNGZubGJ2T1dYZDlVeVkrdUxQc1lLd0J1ZmsrOVh4aEhHdFd2b1cySDNBaW13bTE5UFo5YWRVdlFzS3pyeUtiaFROaGlFdVhINVZMcDI5RnNQTUtBTmllRmRLMmxMU1o5MWVxVFNEajJjQWtaQUlxR3J4NGQ5c3M1OWl5Y1lNN0dpTGFtZUd6eVdBU3RLeVVuK1B4VkNSU0pZWXNGWFhtdXloTlRtbDlJNDVIQjZnbmFwaTlTWXZ6MTJ0K3BPSG03bGh5REhPM3ZMWUs5a2U1clpJNGxZK0pHelprSkZJSEZEcWcyNmJ2QlNmcGRWaE1SeWE2NmorSGJuQ1JjV05Kc1NjaTNtRlBpR3JDVHh0eVBVZ3B3OFpLUit4L0Fha3U0V0w2dklncVI1dEwxU0xpNzYvSkdKR1pDdmI4QXh5K0dDRW1kaWFoUGw5TCsyRUp0cVk0T2hudUEySndJcmd1SVZtMGl5UHdkT0pBb0t1U00iLCJtYWMiOiIyZGEyNDc0ZjdhM2MxOGZkZmYzNmQwNWJkOTQyZmJkNzE2ODZjMWVkMjk5Y2U4Yzc5Mzk4NGUzYzM3ZDZjMjVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik8vWWYrWTczSFZtVDRkdWdwZWk2S2c9PSIsInZhbHVlIjoiWXdaRW9FUjY4NWp2cmlzbS9sd3gxa0NIYVVSbjhVc2FDeUlHdUgxYUJaWjVpbk5lTmlDTk96amFKMkhvU3lDdHd2SitQMjlVazArTWFyOVBCZGJaeVVTQ1NSc2VSQjdtVkNQUWppYm5GYWRaQldVWVFJLzZkZXJ1eXRkSUVKZ2JDUFFzVjlvU3NDZzhxZFlBUEM2Y1RQQWx6V2tha2s1MDMvald1TWdNYm5wVnYzSlVGcVhSM3hyRmZBY1pFNEIxV1p5ZUhJZ3VGbTFrNEdTTkpqelYyeEhCZTFtMGtsTGFnZ1RRL2pmQTNRODVLR1JLaXNSSDQzallrRmM0L0lNZWtuQTVENzdxbUx2c3p4bEhoSGtOakFsNmZxNndyTXpZeWRBUXpnV0JGZGRTUGtYME5pYkoweUxMdTJPWlEwenp0VVZDalZCZ2hVNXZmRElheXovWDd1MXRqQWpwR3dXWFptMGZpVUNPMEhhQm53RS9xRjlFT082UFdjYlR5Y0E5RHh1MXNXTmRJS3NtNTN6VWFLdVVPMjVxamJ5SDloZ1h0MmZuVlJSZGMybEhWbzE1VkJlMUIwcS9tNWJwTFNLa2NJazArcVRabnRxSC9zdXZiS1JMSGFTMCtZM2ozTmIzZEhOdXZSaWlGcWhGTjlWMFJBVnRNdHVnVXhSczZNSFIiLCJtYWMiOiJiNTQ5NDI0Yzc3NWE2NmQxNzJlYzg4OTA0MDVlNmI5ODMyZWU2MzE3ZmY4OWZhZjc1MmY4ZjIwMjRkOGEyZmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9qeW53VTZvUkoyQWxRVXFhNGwvRlE9PSIsInZhbHVlIjoiV00zR2RZUkhaTWtDVnJYUGlCTWVGSHBuZlhSeXNSQVdjakxlc29qVnh1MDRPZTIyTEJ1RUc5VUFPMzVySEQ3RWdkMFgzTFZ1aTNEVVQydk0rMnNhYVUvb09ldDhYSXpMY3ZjV0R4Qms4SU1rMXNkcnpTcEErRng5ZS9mZnBsVUZER1Jxd0V0M3lQVEk5eDJvNGZubGJ2T1dYZDlVeVkrdUxQc1lLd0J1ZmsrOVh4aEhHdFd2b1cySDNBaW13bTE5UFo5YWRVdlFzS3pyeUtiaFROaGlFdVhINVZMcDI5RnNQTUtBTmllRmRLMmxMU1o5MWVxVFNEajJjQWtaQUlxR3J4NGQ5c3M1OWl5Y1lNN0dpTGFtZUd6eVdBU3RLeVVuK1B4VkNSU0pZWXNGWFhtdXloTlRtbDlJNDVIQjZnbmFwaTlTWXZ6MTJ0K3BPSG03bGh5REhPM3ZMWUs5a2U1clpJNGxZK0pHelprSkZJSEZEcWcyNmJ2QlNmcGRWaE1SeWE2NmorSGJuQ1JjV05Kc1NjaTNtRlBpR3JDVHh0eVBVZ3B3OFpLUit4L0Fha3U0V0w2dklncVI1dEwxU0xpNzYvSkdKR1pDdmI4QXh5K0dDRW1kaWFoUGw5TCsyRUp0cVk0T2hudUEySndJcmd1SVZtMGl5UHdkT0pBb0t1U00iLCJtYWMiOiIyZGEyNDc0ZjdhM2MxOGZkZmYzNmQwNWJkOTQyZmJkNzE2ODZjMWVkMjk5Y2U4Yzc5Mzk4NGUzYzM3ZDZjMjVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik8vWWYrWTczSFZtVDRkdWdwZWk2S2c9PSIsInZhbHVlIjoiWXdaRW9FUjY4NWp2cmlzbS9sd3gxa0NIYVVSbjhVc2FDeUlHdUgxYUJaWjVpbk5lTmlDTk96amFKMkhvU3lDdHd2SitQMjlVazArTWFyOVBCZGJaeVVTQ1NSc2VSQjdtVkNQUWppYm5GYWRaQldVWVFJLzZkZXJ1eXRkSUVKZ2JDUFFzVjlvU3NDZzhxZFlBUEM2Y1RQQWx6V2tha2s1MDMvald1TWdNYm5wVnYzSlVGcVhSM3hyRmZBY1pFNEIxV1p5ZUhJZ3VGbTFrNEdTTkpqelYyeEhCZTFtMGtsTGFnZ1RRL2pmQTNRODVLR1JLaXNSSDQzallrRmM0L0lNZWtuQTVENzdxbUx2c3p4bEhoSGtOakFsNmZxNndyTXpZeWRBUXpnV0JGZGRTUGtYME5pYkoweUxMdTJPWlEwenp0VVZDalZCZ2hVNXZmRElheXovWDd1MXRqQWpwR3dXWFptMGZpVUNPMEhhQm53RS9xRjlFT082UFdjYlR5Y0E5RHh1MXNXTmRJS3NtNTN6VWFLdVVPMjVxamJ5SDloZ1h0MmZuVlJSZGMybEhWbzE1VkJlMUIwcS9tNWJwTFNLa2NJazArcVRabnRxSC9zdXZiS1JMSGFTMCtZM2ozTmIzZEhOdXZSaWlGcWhGTjlWMFJBVnRNdHVnVXhSczZNSFIiLCJtYWMiOiJiNTQ5NDI0Yzc3NWE2NmQxNzJlYzg4OTA0MDVlNmI5ODMyZWU2MzE3ZmY4OWZhZjc1MmY4ZjIwMjRkOGEyZmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565963396\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1187962533 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187962533\", {\"maxDepth\":0})</script>\n"}}