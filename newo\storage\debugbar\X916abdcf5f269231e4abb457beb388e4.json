{"__meta": {"id": "X916abdcf5f269231e4abb457beb388e4", "datetime": "2025-06-08 15:09:40", "utime": **********.600623, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749395379.808609, "end": **********.600646, "duration": 0.7920370101928711, "duration_str": "792ms", "measures": [{"label": "Booting", "start": 1749395379.808609, "relative_start": 0, "end": **********.509042, "relative_end": **********.509042, "duration": 0.7004330158233643, "duration_str": "700ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.509054, "relative_start": 0.7004449367523193, "end": **********.600649, "relative_end": 3.0994415283203125e-06, "duration": 0.09159517288208008, "duration_str": "91.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43918752, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02631, "accumulated_duration_str": "26.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5582628, "duration": 0.02552, "duration_str": "25.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.997}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.589212, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 96.997, "width_percent": 3.003}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1404459499 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1404459499\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1249998714 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249998714\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1541741364 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1541741364\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1190322125 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxhR0ZvbExQaVRzbU5KbHEvL2FTc1E9PSIsInZhbHVlIjoibXZIaWNOTmlCcWpNOHRPaDhZZVJacmwyTzRhU1NaekRBdHRidVp4SUQ3L0VZNnArSEhkY1lGQkczQ2I3L1BtWm04ZExianEyamtZOEpPUTkrdWJ2ZWg5b3JRTVIrQUdjMlJueGVhVTBRV284emdoYkh3eVNvTDcrSHZRejk2amZxbWNwOTJKQlRoV0hCY2JQMUZkTTF3aWZFTHN4akh1bzNFUENaSjBwMklPdHVCNUEyZklPQXl6bjNXRWJWOUd4ZC9NQ3VUajE2VVVqVXdWU2ZHWHlTWVc4ZVk0NGtyQVF3REt2czBTWFpzUGdPdXJGL20zUDFWeUFFYUd4cEV0Zm81ZVRtUlNIT09pZW9NUjkxMS83NnZmTkRrbDFiMEZlUXNnS3RzTkcwSlRxbk1OMUorQzNZbTNxcWdtZ2xKSFpzMzhlako0YitQNm9mUStscEVGVG4remtRbHV5TDV6U0dCVEkxTFJWWGdIYkozNVhnVm5QTVRSd2ZYanVRZm10N3lSK01raEJlcEF1MWxnc2RzMnREbkxtY2NvVmFJeFFjWEw3WXlBdVVOcUsxdEFtc0NsN24wbE0xOVc0Wks5OE4yZjR1Vkx4WkNadW04eGs2UDBNL0FaZFA4YlhhTDlyWVlQcDBwWDZTMkVBUW5XSXRKbldZWnIvNER4QUhnVmEiLCJtYWMiOiJhMTYxMGU5ODNjYzcyYmJlNWU0ZGExN2Q2ZDhiOWU1N2FkYmE0OWQ0ZTU2NzRiMTZkNzBkN2U0ZjljODM5YTFiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikx0ZFR2N3VMVnJTOEtmZHp2VUVIY3c9PSIsInZhbHVlIjoickhRUXRIMTNTYjh6OWJiMnB0UW1pQU05dlhrNTBqNVFHZGUzSExYYWMwa3R0WXhwRFZpcG9zcWpRbXQvZDluSkEwSUd5UHdJbjBqQ2dHcXVtb0txaHJPcFVvYU51NnBQVUtiN081ZjJTaEUvc2Z0bnhJSXIybmtUMzJvZFFWaWtvbEZyZ1Bad2xMNWZZV29TdDc1VUp0eDRDS3RrRnRTUzRSeGIrTWZnZmVMMXMwL3lHT0F1cFllSFNSR2RxQjYzNVpyNVQrRlpIa3JsSkpwNmp5dklkaG1UVjcxRVhjcUhsazZ5VUg1RGd4ZEtpM0p5U3l3ZTN1YUh0NkY3a0grM3VpcHVCUnpQQlN1cnRORUtqNndJTkZPZ1JhRFl0RmhRTzVzWExQeUx5NGwxb2F5OWIybzNIUHVJc0hyNDJqdyt5VGZFMHdidEdlWFcxWkhQdTF1amVqZkRacUhBMXVHbGkyZjU0Ty92ZmpwSkxJbHpNQjQycnZtVU9jKzUrOGJudkdvZ1l1SVc1UU5OTzFJU0R4c2toM0MrTVc2WE1xSUJEWU9CTW9mZWU1eWtucklBbEVuRmpCVVdab2hTdlRlSkZ5S3JQM3dBUFU4VjJEODg4TDhwV3lKL0I5bW4xb2IvUkw5WHB4emVwOWNyTjBzTUo5cml4cEhJTFJMcmttcVoiLCJtYWMiOiJjM2NiYjM3ZjFhZWVkOGFlNWQ4MTJjZjdmZmEwYzE1YTcwMjk4NTllNWJlOGQ0ZDRiOWJlMzBjZGZlMTk0OGJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190322125\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2138428228 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138428228\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjQxMmRldzhBSjJiWUpHYnhWYzlPbGc9PSIsInZhbHVlIjoiWDRMZjBLejRLNGRQYm0raHRlcS90TXJES09DWDZRMmtERkRQcjZLWWgrcFNsSEtpSlNmaXFTTWcwN1htVUNKblZLTDRjendRRUhXZEp0Mk53ZXNMS3R0TkNueG9vSlVFKy9ndHNKYUZKelRuc25MVVJwY1BrTlBmUkNkUEhpbll0SW5mWE9HTnRJYjcwYkdNc1VNYXY4eisyMGFkY2NkeVpYQ3BzMVdsNE5VbDJQdlE5ZGRQVkRFVEozNE9HLzVYbERjdEdlY01wMG9FakJDaHVFTkVTaHVNRzhLR3FLeU9sS0R6UlVjRGUyOFF3RXYxS0NBbmxQT3FIZFZVNTByUE92YmtEeFFjOUtPSzRqWXZuTWQ5ZTlUdHM0dytLdk5NRmtDLzFIdENDZVF3UkJ2RU1ic1JhdkFKek1xUnl0dnVHSm5iQ2hxb0xnWEh6akVFZVpFUHVJWlFQWWQxSnplMllBWWt1NTJ5OXhPQnRJVVVhWWxUd2FxVXdWU1A0UExadkxnckVXYWxQaHM5MElKRFBRUU9Wb1hyUWdnSnc1aUNCdElWcWtRL3NFaFVkTys2U2JRVytCN0dKSG9uTmJINGd6bk41WndvL0FGekUycW1uZkxONTVQRk4vNVNaQWJ4N3ZJNllVVTEzQUMzV3ZTK3ExSVRCRDMrRWZVbXd6dk0iLCJtYWMiOiIxNTNjN2M0NzFhYmNlMDhhOTUxMjY4NzAxYzkxOTdkYTE5MDc4ZDcxMDI0MjJlMWNhMDNmN2I4ZGVlM2YxOTU2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImdZMEVkektyL0VjdUlROEFGd1ExZmc9PSIsInZhbHVlIjoia2ZrN2NMRFhMS3kzQ0V2d2VrZmRPZXRsKzBRWVVKWXE0VFZZaXlXN2lQV2FSWCtTMHd5MUVKS0owZzNKM09DWW9DWGt1bmZTdG9hZ1Q2dzAzRi9qenhZTUl1ZE9SK0ZSMXpHbjZUQnZ3d2w0eStOQ2czL3JaMk9iNWd4dm9NdzlCZHppTHp3UzN4MkE2WnFQOUhYeXVIQlFrSFdnekQ3V0UyS0VDZDBqMzl4WFlWeVJRazRmbkVpMXlUWWwxL01SNFhua0M4SWFrbXdqVFZQbzZaVTlHOEExbDNDd1lWSkd2RWFBRDVQUTBMTGlDNnhoNkRTN0Q1UjlOUXBOemNIZllkNGxRNnJkL05WdHBGV0hQcG9lcEVCR0I5QkpZaEFBdmNYZ1p4eG1MQjNscGozM1ZTajB2Vi9PWnowdnFESnVVV2tCKzdjdUh6bGtZbHJWc1dTeGg1YW9ZVDZwOTV5M3NNME9OUzVwVXVVbmtWeE8vTjVocFB1SVByai9qeFRiY0dBOVlncWVqOVdnK2hOa2lMRzIwdWRrMmJOdG16QXNCV2J5RVVQVnhseU9teUp3MG0rbVNmYkVWYkR6K1JQM3hBTUpQQS9FdXFPbUJOV1Z2cCtRdEM0SFNyTm12UzV2VzJaS0Joa2V6N08zckFtRWt1ZTJhNU1QbTZMNHd2eEMiLCJtYWMiOiJhMGQxNmU4YWQ1M2JiZmU1ZTcwNTJjNDExODNlMzJiMWIxMWI3MDhmNzczNWYwYTJmYzliNDM1Y2YzZGNkMWJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjQxMmRldzhBSjJiWUpHYnhWYzlPbGc9PSIsInZhbHVlIjoiWDRMZjBLejRLNGRQYm0raHRlcS90TXJES09DWDZRMmtERkRQcjZLWWgrcFNsSEtpSlNmaXFTTWcwN1htVUNKblZLTDRjendRRUhXZEp0Mk53ZXNMS3R0TkNueG9vSlVFKy9ndHNKYUZKelRuc25MVVJwY1BrTlBmUkNkUEhpbll0SW5mWE9HTnRJYjcwYkdNc1VNYXY4eisyMGFkY2NkeVpYQ3BzMVdsNE5VbDJQdlE5ZGRQVkRFVEozNE9HLzVYbERjdEdlY01wMG9FakJDaHVFTkVTaHVNRzhLR3FLeU9sS0R6UlVjRGUyOFF3RXYxS0NBbmxQT3FIZFZVNTByUE92YmtEeFFjOUtPSzRqWXZuTWQ5ZTlUdHM0dytLdk5NRmtDLzFIdENDZVF3UkJ2RU1ic1JhdkFKek1xUnl0dnVHSm5iQ2hxb0xnWEh6akVFZVpFUHVJWlFQWWQxSnplMllBWWt1NTJ5OXhPQnRJVVVhWWxUd2FxVXdWU1A0UExadkxnckVXYWxQaHM5MElKRFBRUU9Wb1hyUWdnSnc1aUNCdElWcWtRL3NFaFVkTys2U2JRVytCN0dKSG9uTmJINGd6bk41WndvL0FGekUycW1uZkxONTVQRk4vNVNaQWJ4N3ZJNllVVTEzQUMzV3ZTK3ExSVRCRDMrRWZVbXd6dk0iLCJtYWMiOiIxNTNjN2M0NzFhYmNlMDhhOTUxMjY4NzAxYzkxOTdkYTE5MDc4ZDcxMDI0MjJlMWNhMDNmN2I4ZGVlM2YxOTU2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImdZMEVkektyL0VjdUlROEFGd1ExZmc9PSIsInZhbHVlIjoia2ZrN2NMRFhMS3kzQ0V2d2VrZmRPZXRsKzBRWVVKWXE0VFZZaXlXN2lQV2FSWCtTMHd5MUVKS0owZzNKM09DWW9DWGt1bmZTdG9hZ1Q2dzAzRi9qenhZTUl1ZE9SK0ZSMXpHbjZUQnZ3d2w0eStOQ2czL3JaMk9iNWd4dm9NdzlCZHppTHp3UzN4MkE2WnFQOUhYeXVIQlFrSFdnekQ3V0UyS0VDZDBqMzl4WFlWeVJRazRmbkVpMXlUWWwxL01SNFhua0M4SWFrbXdqVFZQbzZaVTlHOEExbDNDd1lWSkd2RWFBRDVQUTBMTGlDNnhoNkRTN0Q1UjlOUXBOemNIZllkNGxRNnJkL05WdHBGV0hQcG9lcEVCR0I5QkpZaEFBdmNYZ1p4eG1MQjNscGozM1ZTajB2Vi9PWnowdnFESnVVV2tCKzdjdUh6bGtZbHJWc1dTeGg1YW9ZVDZwOTV5M3NNME9OUzVwVXVVbmtWeE8vTjVocFB1SVByai9qeFRiY0dBOVlncWVqOVdnK2hOa2lMRzIwdWRrMmJOdG16QXNCV2J5RVVQVnhseU9teUp3MG0rbVNmYkVWYkR6K1JQM3hBTUpQQS9FdXFPbUJOV1Z2cCtRdEM0SFNyTm12UzV2VzJaS0Joa2V6N08zckFtRWt1ZTJhNU1QbTZMNHd2eEMiLCJtYWMiOiJhMGQxNmU4YWQ1M2JiZmU1ZTcwNTJjNDExODNlMzJiMWIxMWI3MDhmNzczNWYwYTJmYzliNDM1Y2YzZGNkMWJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-262404749 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262404749\", {\"maxDepth\":0})</script>\n"}}