{"__meta": {"id": "X864a750f78c7dcd85274fe4d937af50a", "datetime": "2025-06-08 12:56:00", "utime": **********.837496, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387359.457816, "end": **********.837539, "duration": 1.379723072052002, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749387359.457816, "relative_start": 0, "end": **********.664657, "relative_end": **********.664657, "duration": 1.206841230392456, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.66468, "relative_start": 1.2068641185760498, "end": **********.837545, "relative_end": 5.9604644775390625e-06, "duration": 0.1728649139404297, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00804, "accumulated_duration_str": "8.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.759924, "duration": 0.00594, "duration_str": "5.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.881}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.794705, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.881, "width_percent": 16.294}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8140159, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.174, "width_percent": 9.826}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-250574251 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-250574251\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1271037609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1271037609\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-59668316 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59668316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387338613%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhJYzBvSDR6NExmekNrSjJ2N1FVTWc9PSIsInZhbHVlIjoibURxbXk5UzBOSmNjQm80VHNBelpDZ1hIMVpXRm15ZFV2M2lqN01QNkdmWVRLa2VRK2I2RWJiM3BPY0Y0dVllMGVVbjZnNWVEUEFJbTcyTHdmR1ozYUxnb2E4ZDRlM0hSR2Rhdm1ySFk3UVdZWmo1NkpSVEo1MWZFV05ESUZMT0tUeGh3K21qVFRuTlFCSXJCcVArSXladlRmOThiZG9QeUp0K1M0OTcrc2dkNFI5eWlIT1pWWk5zK3ZGdFhRN0hQMUZwM3o3YWZVdHBwOHNndGRERi9STUJHcDdrRWRUMTRsTjJjN0JoS2s2YkRFTG1xSWVpcGVOekptN3BwRWVTZ0Nua1VJT2hvZWtPZDAxaWpXeDFUVCt4T0t1dUZhZVJUTnRNb1Rwb1EyTzhsUGQrWS9vY25JWHlYTTkyNlQ0NXI4UjVKMTdiNUw4ZHY0SllpSlFTekRxZjcwTkNGZThYaDZUeXJ3V2hla3RYSnh4b05jTW85UitRbkI2MnFRbXEzalZlN3UyQ1l3bjRMNTFRd0h2QW1OM0MySktzMWZrSmJydjAyOGFlcFdnWjhXSjBZM1NiNjZIR1BycnZRVzcxTGZ3bWtPMll3R1FYKzEwL3BtU2kyeENDQWtndzBTZ25BeVZUYkEyYlMyTDNyV1BXWktVY1h0Ym9rakhRT1phS3IiLCJtYWMiOiI3OWI0OWFkM2ViMzQyMTk4OTAzZGNhZTM5N2JmMjQ5ZWQ4MWQzYjI5MmQwOGYwMzQ0ZGZhN2RjNjE3MmE0NjAwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InphZFBHRjc1NVdPdjBickxXN1ZLbGc9PSIsInZhbHVlIjoiZkYwWE9QMXlnREh2Qi9uYS9aZXBDRk9kKzRYV0ozNG1QNi9aNm9YTW1VaTJPSUFhQlpUVjFwam9leUpQWHFzRkxtdEhKN3NkRWU1UUVLUzQ5V2Vma2NVQ2N4SVdTRDRicXBDN3lnQ3AyUWkycndEU1hpZ0NFeUkzbHlFSWN6UmJqS05TZHZDM2JWdjNObFVpdGtPUlREa2EwcG9uaTl2VmQxdERSLzJTeERraVVoVnllbnI4STk0UzF6bmo3Y3hPY1NMQlluelJBQ0xkUHI1YmNzOFB1T2dNY29iTzZmRnZjbDkvWGNSWFVYSHVLOE5IRjJEVTJCQTk3SG9TdCtFeStDd0JwTHRySjcyQm5COXpYQmE1c3lYQWVodWl2ZEtyamxtRjV4QUhQVnFLWkV3OUlyK0dscGs2bE9kNnFwMUdDcm1nM0tZU1FtZUZlTnJxYjFKS1dranFUSVQvUzIweTB6TklXWlFhR3BWUk1TY21ySXVrby8zYzBzMXU5YUx3ZUdVbDUrM1RYdUYxWFhmeWV5NjI2M1lnL3JSdUNPcFFKcDNLUmdLaGdEbi9mK0VDTllEWXNicVVVRnJZOCttZ0JkN0FMaS9iZ3YydjcrbTlaZEpxcjYvU1dIMmxFcWgrTkdxS1ZSVFpvdnhSbEltM1k2a0NBSFVaVzZVeElUd3giLCJtYWMiOiJjMDA3NzcwYzA1MGUzZjA2M2IzNTIwMWE3YTQ0OTY5MjY3NTY4MTk2ZDAxM2E2YWU0OGMwZTU5OTkyMWJmOWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-184065127 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184065127\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-83561849 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:56:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlBN3ExOU1SN1k4TmgrVEc4bFhpc3c9PSIsInZhbHVlIjoiZUlLS0NqQUQ4UlR1TTBEbGkwUHdmL0w4dSs5Q2M2NmovR0luNnNsMDhkbXZ3ZGlOUlRzNktwWXRYU0VoNkg0RlQyUnhSK1BieHJnVkVnYmZJd3I2T0k2WHBBVy95dFhxQndKbExucTdtaXpzelluaTVjNTVQMVdOR0d1MWVEbjVPWFVPdXc0MFdia0dFUDhla05XbEpvSFNaeWlUV2hYSWRIOFVsSWYyMGlkSStuZWRoRGhxaEcxV0JENTZSbHdYY2xER2tGMzMxR2owbk1qd084UmdDU3Vza2Y4RlFQU1B0bGMwQXd2aHJJb0pDZi95RXloMnJiMjU0VVNXcTEwRTJUcGFmb1hUbzFTdnlrWVViSmppd3h1dU0yU1RnV2syVGpWZnlwTDY1VFNMTGN5M3gwNzVKZUlrY04vQk9hcjBuV0JWb1Bzc1dEQUlWU2FtRVpMdnA2YytCbmZUQWtBM0lNMXJPNmJycGhBekQ2TzVnMXBCM1dTQTVBYytuc0pETU9Zb2xTUEpUYjlsK3U5U0pCVGdIZVF5Smx3ZkwrYUl3d25ucU0zWU9ERThGSFh1V2FXUlVuY29zVm9SYUJzaVpYMXNadXVOUklrYjRQNU44aTBMK3dXZXVaRFd2Nzl4ajNCSjVrN3lPcHZ2M3lPT1poV2VVdmlVekliTFRWL3oiLCJtYWMiOiJkOTI1YTExYTA2ZjVjNGNkODQ5MmQzNmMwNWMzNDUzNDY0M2Q0ODBhMzY3OTA5MzM0YjY5M2UzMjllNjAxNmEzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJZaEJFYXlDb0RqN0VjVzhUR1VvdkE9PSIsInZhbHVlIjoiOThlbUF3MHZ0Rk1zK3JGdlJnTk5YVWdaUzRKTHFrekhTcGREZFRId291eGs0ZW5sMmNpbzgzdjlISmpYWXErbjdnVmF0dTU4K0pVc3hqMzlxelh3c0xZeFVKTWNCRU1qa3plZTV0N1JZOUpDTy9YazRPR1dYNzlmbnNSdGgrTmFiMzlpMWp5czVHM3FWMURqQTRoNnlqQXJEOG5NVTk3YVI1L1VSMEh0cW0wcVVYOU5qbFJmWWwrbnFpUmtDYVlqNWFTRDF5VThMS1dhWmFXU2pzc2FGcnE3bjFNK1RWVkF4Y0NGd1ZvU2lMWGZtUDNub2RBSXpKNXh4b0tLZkpGU3phbVlKUmUrY0h2OXpEcTFDVFphSXd0UGt2V0VlUFR3WktNSlNkTUhYNEFVZFJTSHJFL0JoOTJWU096WTJlOHp3Tm5ob2dLenBXYmMvR1ZTWnJnUVlESldpVFBlczdYK0J2OEZ1MFd2NTYrM3I0KzdoangzTWc3KytrWEg5Z1pkdVpSSGFMTW1ITTdpekFuczZFZm1qbEY1S1h0ZEZjQlR0NndJT3hoYnN1aTdrTE9tMVZtNkpqOTRqZjhkZXd0bGhDQ1l6NGQ0Wm1XRittbG92OUxDV0w0cE5kV1lJZ3BlQ0ljSmg0eG5DZ2tmR0g5QjdrOUo0cGswYis1K2xHb2giLCJtYWMiOiI1ODFhNjMzZmRmZDZkM2UzMjBlMzA2MTQ4OTRmM2NhYjhjYTQwMmRjZWM4YjViMGJmN2E3ZTdmMzU1ZmI5NzVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlBN3ExOU1SN1k4TmgrVEc4bFhpc3c9PSIsInZhbHVlIjoiZUlLS0NqQUQ4UlR1TTBEbGkwUHdmL0w4dSs5Q2M2NmovR0luNnNsMDhkbXZ3ZGlOUlRzNktwWXRYU0VoNkg0RlQyUnhSK1BieHJnVkVnYmZJd3I2T0k2WHBBVy95dFhxQndKbExucTdtaXpzelluaTVjNTVQMVdOR0d1MWVEbjVPWFVPdXc0MFdia0dFUDhla05XbEpvSFNaeWlUV2hYSWRIOFVsSWYyMGlkSStuZWRoRGhxaEcxV0JENTZSbHdYY2xER2tGMzMxR2owbk1qd084UmdDU3Vza2Y4RlFQU1B0bGMwQXd2aHJJb0pDZi95RXloMnJiMjU0VVNXcTEwRTJUcGFmb1hUbzFTdnlrWVViSmppd3h1dU0yU1RnV2syVGpWZnlwTDY1VFNMTGN5M3gwNzVKZUlrY04vQk9hcjBuV0JWb1Bzc1dEQUlWU2FtRVpMdnA2YytCbmZUQWtBM0lNMXJPNmJycGhBekQ2TzVnMXBCM1dTQTVBYytuc0pETU9Zb2xTUEpUYjlsK3U5U0pCVGdIZVF5Smx3ZkwrYUl3d25ucU0zWU9ERThGSFh1V2FXUlVuY29zVm9SYUJzaVpYMXNadXVOUklrYjRQNU44aTBMK3dXZXVaRFd2Nzl4ajNCSjVrN3lPcHZ2M3lPT1poV2VVdmlVekliTFRWL3oiLCJtYWMiOiJkOTI1YTExYTA2ZjVjNGNkODQ5MmQzNmMwNWMzNDUzNDY0M2Q0ODBhMzY3OTA5MzM0YjY5M2UzMjllNjAxNmEzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJZaEJFYXlDb0RqN0VjVzhUR1VvdkE9PSIsInZhbHVlIjoiOThlbUF3MHZ0Rk1zK3JGdlJnTk5YVWdaUzRKTHFrekhTcGREZFRId291eGs0ZW5sMmNpbzgzdjlISmpYWXErbjdnVmF0dTU4K0pVc3hqMzlxelh3c0xZeFVKTWNCRU1qa3plZTV0N1JZOUpDTy9YazRPR1dYNzlmbnNSdGgrTmFiMzlpMWp5czVHM3FWMURqQTRoNnlqQXJEOG5NVTk3YVI1L1VSMEh0cW0wcVVYOU5qbFJmWWwrbnFpUmtDYVlqNWFTRDF5VThMS1dhWmFXU2pzc2FGcnE3bjFNK1RWVkF4Y0NGd1ZvU2lMWGZtUDNub2RBSXpKNXh4b0tLZkpGU3phbVlKUmUrY0h2OXpEcTFDVFphSXd0UGt2V0VlUFR3WktNSlNkTUhYNEFVZFJTSHJFL0JoOTJWU096WTJlOHp3Tm5ob2dLenBXYmMvR1ZTWnJnUVlESldpVFBlczdYK0J2OEZ1MFd2NTYrM3I0KzdoangzTWc3KytrWEg5Z1pkdVpSSGFMTW1ITTdpekFuczZFZm1qbEY1S1h0ZEZjQlR0NndJT3hoYnN1aTdrTE9tMVZtNkpqOTRqZjhkZXd0bGhDQ1l6NGQ0Wm1XRittbG92OUxDV0w0cE5kV1lJZ3BlQ0ljSmg0eG5DZ2tmR0g5QjdrOUo0cGswYis1K2xHb2giLCJtYWMiOiI1ODFhNjMzZmRmZDZkM2UzMjBlMzA2MTQ4OTRmM2NhYjhjYTQwMmRjZWM4YjViMGJmN2E3ZTdmMzU1ZmI5NzVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83561849\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-559149080 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559149080\", {\"maxDepth\":0})</script>\n"}}