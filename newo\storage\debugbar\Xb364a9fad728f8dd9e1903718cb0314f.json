{"__meta": {"id": "Xb364a9fad728f8dd9e1903718cb0314f", "datetime": "2025-06-08 13:34:32", "utime": **********.627954, "method": "POST", "uri": "/receipt-voucher", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389671.262951, "end": **********.627994, "duration": 1.3650431632995605, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749389671.262951, "relative_start": 0, "end": **********.414044, "relative_end": **********.414044, "duration": 1.1510930061340332, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414067, "relative_start": 1.151116132736206, "end": **********.627999, "relative_end": 5.0067901611328125e-06, "duration": 0.21393203735351562, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45789072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST receipt-voucher", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptVoucherController@store", "namespace": null, "prefix": "", "where": [], "as": "receipt.voucher.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=64\" onclick=\"\">app/Http/Controllers/ReceiptVoucherController.php:64-84</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.032690000000000004, "accumulated_duration_str": "32.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.511687, "duration": 0.01452, "duration_str": "14.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 44.417}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.55289, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 44.417, "width_percent": 4.711}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.564809, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:69", "source": "app/Http/Controllers/ReceiptVoucherController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=69", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "69"}, "connection": "ty", "start_percent": 49.128, "width_percent": 4.374}, {"sql": "select * from `warehouses` where `warehouses`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 70}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.576875, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:70", "source": "app/Http/Controllers/ReceiptVoucherController.php:70", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=70", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "70"}, "connection": "ty", "start_percent": 53.503, "width_percent": 4.68}, {"sql": "select count(*) as aggregate from `voucher_receipts`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.584478, "duration": 0.00807, "duration_str": "8.07ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:72", "source": "app/Http/Controllers/ReceiptVoucherController.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=72", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "72"}, "connection": "ty", "start_percent": 58.183, "width_percent": 24.686}, {"sql": "insert into `voucher_receipts` (`date`, `payment_amount`, `receipt_from_user_id`, `purpose`, `payment_method`, `created_by`, `custome_id`, `warehouse_id`, `shift_id`, `updated_at`, `created_at`) values ('2025-06-08', '100', '16', 'اقفال', 'cash', 16, 'S-المستودع الرئيسي-1', 8, 2, '2025-06-08 13:34:32', '2025-06-08 13:34:32')", "type": "query", "params": [], "bindings": ["2025-06-08", "100", "16", "اقفال", "cash", "16", "S-المستودع الرئيسي-1", "8", "2", "2025-06-08 13:34:32", "2025-06-08 13:34:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptVoucherController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptVoucherController.php", "line": 81}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.598636, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "ReceiptVoucherController.php:81", "source": "app/Http/Controllers/ReceiptVoucherController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptVoucherController.php&line=81", "ajax": false, "filename": "ReceiptVoucherController.php", "line": "81"}, "connection": "ty", "start_percent": 82.869, "width_percent": 17.131}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "success": "Receipt Voucher has been Created successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-voucher", "status_code": "<pre class=sf-dump id=sf-dump-1236586056 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1236586056\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1840074701 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1840074701\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2059675714 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-08</span>\"\n  \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n  \"<span class=sf-dump-key>receipt_from_user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n  \"<span class=sf-dump-key>purpose</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1575;&#1602;&#1601;&#1575;&#1604;</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>custome_id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">S-&#1575;&#1604;&#1605;&#1587;&#1578;&#1608;&#1583;&#1593; &#1575;&#1604;&#1585;&#1574;&#1610;&#1587;&#1610;-1</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>shift_id</span>\" => <span class=sf-dump-num>2</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059675714\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1816988112 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">166</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389653152%7C28%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNMVlJCK2FTSHltUnRIU2ViVC9acUE9PSIsInZhbHVlIjoiYUlpRENCYllOajdPK1BMR1dvT1ZCRUFzU2Vrc1JSQzcwN1BLSVRRK1dIalVRSERYUndoc3VrK1FVQ09Ybm5maFp5UytxVEZCRHhpeDVmS3BGNkpXTHNkNDBmaDVwbFZTWlpGa2VYQW03eWRtV2UwcmxweDRUSFYwKzN2UkYxNkE3VHJ3ZVhSU2p2bThqZ2tSekxHR1hWREovMjB1clozWURMZnN3bzRBTWN6ZElVV3R4cDRWU0RqOVBLUE9sK2JiQVc2RGRmTFdQblR3YzVqaU1KS3FjeVZqTlJrYlBsVlRLNnhObXlidFE0bFdKMXROZE5jV29pOHEvdzlLNFJRdUZaQjlHd2ludUdveHo1UnpqWGZWcTlUc1NyQ0hyMTA3U0dwYkd3bjVHbktBU0VqK2lxeUppdzZDWjJoellVQURHUlF5V284SHdEdWIrL0lTWnlSQWUzR0tsMTQ3SjRqTW4rSHN2Z1ljeU14ZTR5eWQwemx3VTRmeitUQTcrWS9ENmtVb01TVVk3aUZTQ083NmJPdnpLY0pnTnEzbXlCb3RLWHhXb0J0NG1EY21uNk4wUXFWL2NKMzVBTjBkdG9UUCtSTTAxZlF2V1cxVitOUTJ0WVIreGEreGpTb2kwSzhBU0tSL2R6UHJQODVSMHZHdDYvMENwcFFmdXNNMHdMdjYiLCJtYWMiOiJmYTc3NzBjYTA1NDEzOTE1Y2U4NDViYmMyODMwYWQ4NmZmYjUzYTVhZDg2ZDE4Y2ZhYTcwMTM1ZmNkOTVhNWZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBrRU05Z1IxcHBqZVc4anVZb0NHQ1E9PSIsInZhbHVlIjoicUJNdTljTjJES2FzQk9sOThrbm5jUVp2a3V2MDNTTGpRSEdGTi9HTjVieDkrRm5MSExFZzNOKzFmWHQwTWFKSVVJUjFIUEVMS1pVQ0RRM2VxamhnYzdZMU5SbWNweFM1VjYvRTd1cWhsY1lCeS9JSUcxaktCOGgrYzJOdjVPbXlreTFuakI3amVEL0VhQ3Rqb1M4clhVM3grRFZOWGRaRUtwWWROVzl4Qlg2TGVPdmg3eDNET0ZBUXdMQXRyTDRLRzkyVU5XNFkxcDRCZXJFNFhqcHd5ckwrM0ltK2RLMEFtWHF5aVNaTmNIWDE2SzdSQU9OUGpmTHZwNTBVVDUvS2lqT212WlhJNFJiKzJGWGJINXhsNzM0aExnMmh5UGIrTTZ2VVQ4NUxoSWhzbTl3WmQvbXRJaDRTcmlLVTljYkxvT3VYeURzUzB2aTNnSUFKZHVDT2JBQlVLcjZ1cUFMSnVWUGZnTWJCb1NEZkI5Z3hjZXNiNTQva3NNR3NRVnQrd1hSdGxONHQvVzBaalE5RTJFY2ZXamppTFBqQVdrZjZUMXdRaEVrbDc4M09xZCtQL3JKdVBhRHFIdllpRFJVUXBIV2dxaVR2dkVYL3Y5U2g5YWt2M09qUXBtdjhpYU9UKzBVam5GeFBXcVpKSkJ1eDQrN0NYVHhGNlF3KzJkdEYiLCJtYWMiOiIzNTU1YzQ2MDlhMGE4MjQxODZhZmMzNGVlNTZlNTA2Y2M1NjU2NDhmNmRkOTk4YTNmNzI2MjFhZDQwM2EzYjhkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816988112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-995016210 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995016210\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-533476837 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJLdC9idkRuc0JyY25sQm5LS1RXalE9PSIsInZhbHVlIjoiNXFqTTRPc2xWczhsVjJaK05XNXVKaEVMWVMzcE5WeXpCRGRMYUtVWHhUeFBuaGRvTDZ0bGNybURSZUp3dDJzeHZ2L0xXQTIzNU1GQzRGVCs1UEtJSVBUOFcyU21BNWg5bnYrWFlIUGFzRC8xbloydHhSNjRIeEE0NzdwZ2dhSkZXcG1DdHNlcU4yUlZwNVVuTGZ0bFFOZnk3cGg1TGpoT0l2ZENYSkxBRlJMY1dGY2lKQVNEamt1akV6SEl5dkFMQjg4ZXA3cWdZVmZWOWdOV0podFhYYkREdmgyVDc1bWV6eE4xVXZMejk5VU9Nd2ZLMEp5ZXhPM254Yy9FMGpseVJxMm5mZWZUa21haVpCU2VlYy9GRlBxMlZqWmZNZ1ZJSFBucVdQSWxZSm1FeG1xckk0em9JbTBpTjkvN2dEMDBhdk5Sc092NVA4NXkzYlZRQ01mUkdCNWFzTEZLYmt1NEFYNnc1NWRBNEtONFlER3BPbjFtV0RKNlpVNERyYUkxTjhtbEozNkdWc29RME1aaEk4dTg4R0RJVUplOW1nd3JSclVKdjZYNGFsZGZmdzR0ZWhQVGFySDJqNFoxNXIxd2FrckdvaGlwalJ0QlBiOVRpQXNzUWs3Wm1qeEhsQ3FidzFLRHdBUlBkTDYvbGlRenJDTWdtMXpISUtoMlE3YnoiLCJtYWMiOiJmZjBiYWFhNmY4ZDQ5OTRiNWQwZjkxNWUwNDhkOTVjNmM2ZWMxZDhiODEyYjMzZDBlNjY0MGMyNTM1M2UxNTY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5LUERHTWJTUElyRFFnU1I0ZTA2dVE9PSIsInZhbHVlIjoiYWtCdmJRMXFRSEVsaHBjcGJrVDNibEpSejNaZnF0WDFrako2TFpYdmRtZVUzM2hKdmQ1RzBtRHhwdnZ5dXc2dFhjZmticVRTQWJkVjJGQzZyZDRwYWJTcjY2REtmVjU4MXZOY3l2UVZmV3pYK1NTRXExM0wzUmJCVERoTUZpVGVZdXBodktGb3B4d3pIL2dyaUtNZzBsWFJzOFRKUDBDQmxoNXJQRE96OG51aGg1TTVONkIzaGFKZnFia0RyZHI4M2FXOHVNWm1tQjJ0M2QvTHViVDRDell4d2VQdS9HYU9rblJhQW9BV3FPNFFwU0JBNW50b3VuUEEvOVpsUzdPK1dyNzllY25TU0lCQUtNZmpxRGx2cTk0emRZWFVqY3N2M2NBU0VFV2RVZ0Z0UEY5dkdsRlZQUm0yUjhzNXFWaTRzU2tJVHB3bU41eEFBNkJlcEdkWU1VZHd2VkFibXMrYXZreUUvWkR0Y0R5THFsRDVZMHJHb3h2TEtNOTlob3hMWEJoU05ZMnpSOVV6dFl3d0d4c3VsWG9XUXp2eVkvNFgrVVNETzRYcENBTXRxQmFaSHNuL2JWN3RNQ1lOTXB3cnVQQzNtUXJFZmtHbktMeG5DRjQ2Skx3YjFtcFBRbkpmYVc4TExzYTZJNXIwbUJUbWdFS2wxY2lXNTM5NCt2VysiLCJtYWMiOiIwOTZlNTQ4NzM2Y2M5Mjg3Mzg2NWNhOGI4NjFhM2NhMTA0MmU3MWNiMTFiNzIxMTk0NTY3ZmM4MjRlYmNlZWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJLdC9idkRuc0JyY25sQm5LS1RXalE9PSIsInZhbHVlIjoiNXFqTTRPc2xWczhsVjJaK05XNXVKaEVMWVMzcE5WeXpCRGRMYUtVWHhUeFBuaGRvTDZ0bGNybURSZUp3dDJzeHZ2L0xXQTIzNU1GQzRGVCs1UEtJSVBUOFcyU21BNWg5bnYrWFlIUGFzRC8xbloydHhSNjRIeEE0NzdwZ2dhSkZXcG1DdHNlcU4yUlZwNVVuTGZ0bFFOZnk3cGg1TGpoT0l2ZENYSkxBRlJMY1dGY2lKQVNEamt1akV6SEl5dkFMQjg4ZXA3cWdZVmZWOWdOV0podFhYYkREdmgyVDc1bWV6eE4xVXZMejk5VU9Nd2ZLMEp5ZXhPM254Yy9FMGpseVJxMm5mZWZUa21haVpCU2VlYy9GRlBxMlZqWmZNZ1ZJSFBucVdQSWxZSm1FeG1xckk0em9JbTBpTjkvN2dEMDBhdk5Sc092NVA4NXkzYlZRQ01mUkdCNWFzTEZLYmt1NEFYNnc1NWRBNEtONFlER3BPbjFtV0RKNlpVNERyYUkxTjhtbEozNkdWc29RME1aaEk4dTg4R0RJVUplOW1nd3JSclVKdjZYNGFsZGZmdzR0ZWhQVGFySDJqNFoxNXIxd2FrckdvaGlwalJ0QlBiOVRpQXNzUWs3Wm1qeEhsQ3FidzFLRHdBUlBkTDYvbGlRenJDTWdtMXpISUtoMlE3YnoiLCJtYWMiOiJmZjBiYWFhNmY4ZDQ5OTRiNWQwZjkxNWUwNDhkOTVjNmM2ZWMxZDhiODEyYjMzZDBlNjY0MGMyNTM1M2UxNTY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5LUERHTWJTUElyRFFnU1I0ZTA2dVE9PSIsInZhbHVlIjoiYWtCdmJRMXFRSEVsaHBjcGJrVDNibEpSejNaZnF0WDFrako2TFpYdmRtZVUzM2hKdmQ1RzBtRHhwdnZ5dXc2dFhjZmticVRTQWJkVjJGQzZyZDRwYWJTcjY2REtmVjU4MXZOY3l2UVZmV3pYK1NTRXExM0wzUmJCVERoTUZpVGVZdXBodktGb3B4d3pIL2dyaUtNZzBsWFJzOFRKUDBDQmxoNXJQRE96OG51aGg1TTVONkIzaGFKZnFia0RyZHI4M2FXOHVNWm1tQjJ0M2QvTHViVDRDell4d2VQdS9HYU9rblJhQW9BV3FPNFFwU0JBNW50b3VuUEEvOVpsUzdPK1dyNzllY25TU0lCQUtNZmpxRGx2cTk0emRZWFVqY3N2M2NBU0VFV2RVZ0Z0UEY5dkdsRlZQUm0yUjhzNXFWaTRzU2tJVHB3bU41eEFBNkJlcEdkWU1VZHd2VkFibXMrYXZreUUvWkR0Y0R5THFsRDVZMHJHb3h2TEtNOTlob3hMWEJoU05ZMnpSOVV6dFl3d0d4c3VsWG9XUXp2eVkvNFgrVVNETzRYcENBTXRxQmFaSHNuL2JWN3RNQ1lOTXB3cnVQQzNtUXJFZmtHbktMeG5DRjQ2Skx3YjFtcFBRbkpmYVc4TExzYTZJNXIwbUJUbWdFS2wxY2lXNTM5NCt2VysiLCJtYWMiOiIwOTZlNTQ4NzM2Y2M5Mjg3Mzg2NWNhOGI4NjFhM2NhMTA0MmU3MWNiMTFiNzIxMTk0NTY3ZmM4MjRlYmNlZWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533476837\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1382262871 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Receipt Voucher has been Created successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382262871\", {\"maxDepth\":0})</script>\n"}}