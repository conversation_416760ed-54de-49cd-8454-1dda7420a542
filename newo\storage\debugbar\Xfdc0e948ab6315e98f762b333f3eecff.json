{"__meta": {"id": "Xfdc0e948ab6315e98f762b333f3eecff", "datetime": "2025-06-08 15:09:16", "utime": **********.597854, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749395355.678875, "end": **********.597882, "duration": 0.9190070629119873, "duration_str": "919ms", "measures": [{"label": "Booting", "start": 1749395355.678875, "relative_start": 0, "end": **********.458594, "relative_end": **********.458594, "duration": 0.7797191143035889, "duration_str": "780ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.458616, "relative_start": 0.7797410488128662, "end": **********.597885, "relative_end": 2.86102294921875e-06, "duration": 0.1392688751220703, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0057599999999999995, "accumulated_duration_str": "5.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.544419, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.201}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.578698, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.201, "width_percent": 15.799}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1937385864 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1937385864\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-92848151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-92848151\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-512900130 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512900130\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2054050948 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394693221%7C71%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFmYVltYnZQL3k0ZFNtSHZoQkg3Znc9PSIsInZhbHVlIjoiVkdPdnhDMTVzZ1VNRFJYZUdORFM0eVFMUnBYUVBmdlBNVDd6YmZadjhldUtzSmg2STlsRXdJVlRVY3U5QjVuckUxQ2dUalc3L1JsMW9FMnBhL004RGgyQWN4ZmVlUG1TZ0h3SktvSytrVnZ6eEpkTDVUcWtyRDFmangreCtRT01mZncxUU1XQUtXUjdqUjJ6M0V3cmh1T1BjZHpsdVFRZHJjV2MyVnFuMTV5Y2pOL1RpbFUxekRZdTJXaDl2S3BNTVErbEZDLzdXbkN5emxPanFyVGFHVmk3aVROTlgzMkF1dHZUaVp6N2R1UEFsV3FIcXJ6Qzl2RnBsVVNZbFJlcjQ4TUlZNElZOUtmMGFOODZMRFpJckF1TnUxZS85UjhnUXk1cjI3N2tSMXh4WWtyMFN6cDhLdjJPemFPTkdodHZKNEJoVkJ0dlBOSWFxSjA1Y0IvQXgydENNcjZyaFZTYitBNFBIYXUzNkpZSEwrZlczc05Gd0owTkZyUVJCMjRLcnBwNzlKTXYvVUQ0WmVvQzFJc2VyUi9QdXE3WE5xbDVpQzEwR2xxR3o5czE2MkFCeC9SbE9HSlVxR0MzWktVbG9GdG1zR1lBS3czbmpTVUNQcU5tT2tEdjY1elR3ZEZhYnMwYmdMQXNLaWtaWUFrekhwTmE0ZGRwdlgrZ2N3bmoiLCJtYWMiOiJiZTBlYzQxNDRmMGI4MzRkMjU3YWFiNmRhY2ZmNTMzNGFhNzk3MzM0NGUyNDVmYmRjNTQyYjAxOTZhZjE5NDA2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFFc0NpK29oUlU2bUxzZWRBY1FkUWc9PSIsInZhbHVlIjoiV0REYjdWWXFKMENrWnVlc3d6WGhnem1OVGlrSTl2d1BNZm9jQ3lrMVQ0eE9JU1RXaVU0OTZtVnVhY0ZWRHZ6YlhHUVpHRmVGVUZyVlRna094NVdSWDZEakZCVjQ4TGtRNStNd3Rtc081K0h4Y0pqUWcrUXZIeCtDaHFTU1BxcjRtVzVJNSsxZnVHZ2pXVWtBdkZTR0NLWE5qYmJFMVc0R3RhWWs2VHo4aEMzTWNKdG9OajFldFVDWVYwVVVjbWFwL280OUtOdUUybHE3OWtKWFkyZHh5TmsvUlZlZW9PV09tK0xPRUR5RzhlNVVWTUhXR2hsTkNqWkxQdkRKTGtWNkxMSkR5YWNkVDdTek9ieHZjUnFEVDhaUkZQaVFpSzRESG0zVFpEb0VKaHAyQzhmbHRIOVBLTHZMRTltb1lWRzB4YlA4SzlqQTRQaWEvQ1E3ZUF4QW1EbkhBZkx5UkM5NVRNamJrZlo2WDBzZjN6R2s5aml5ZStQbFhoWnpldnpZSi9KWEx1UlRVZGRWbTl2QkYzN0oxWHo0L1V4MVl6TEpiZ3krQXRsdEFEYTJNdCtNVjU4THJTYVQ4aUhpWGd2eGk1UmR0ZHdNRzRYZzNsS3RtYVc2QXh5bms5L1dJYTF0ckNJaWFDYWw5bUx1TlFUa3RVMmh0RkN2UXhkaTAwS3AiLCJtYWMiOiIwZGU5ZjFmNTc1ZmI3YzQ4ZTJlZGFmYTE0OWNmMWNmNzQyMzBhOWY0OGM4ZDMyZjZlOGY4ZDJjNGM3OTlmYWQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054050948\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1709127947 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709127947\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-405014048 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:09:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inp5ZkhrTlRTcVRZSFVGZ3RIVytsT1E9PSIsInZhbHVlIjoiYlN4cERkWEYzTlk5eXFVUEhibnA2YWNhNkxhSU1zTlFtTVQxNWxkL1g3ekRnb3MrMmJ6b21HSjFqaWxHdHZNSldodUNEMTliTDJkcUpBNjdXMHdUTzV6SEpKdk1iMW1od3dkUGFZZWdjd3Jua1I2ZVQrbno5MHdIbmNXVjJtYjlyMHJkTVdIa29hUWVub0U1R2dnM2VubExWSWs0Y1lzbmNEWFoybjNsSXBmYVROZGIrRGMzb0hjVUZYQWtKUmp4VTJOeFo1cmJPU3hYZlJxL2tMcGJ3b0tHOHJnaENwODl3aHVxU0w4MVkxODdjY1FkMDdVOTk4dWM1RWV1YkNoYU9nQkwwSFhnWS9YVk1PdlZuS2tTVSt5NmdKRGhpVFNrUW0zZWUvSUtYZFBNVitFUVNyNThndExVWmQyM0Y2YmVVeXgvd2dtQm90enExYmpQcTY3WklqWm1GY3Z2d3dTV1BOQWhzQ1UvUlZKTHhKNm9tS3UvMWRkaU9Za2I5R3dLTzVvTVlvdERZaWNDb2hRZlpSRldKZzVObWtkcU1TTEoyanpTK0lTOE9KbldDQW9NMC9TaVRBN3ZURU9jbk5PVVI3V1VMTFc0V1FnbysxY0VJdldKUW8yRkM3T0tmUWdFWkZMd0FxQWNMZFJJdHRwWER0ZFI1VDZmYTJ2TTFZODkiLCJtYWMiOiIyNzA0ZGFjYmMwYmIzYWExZDE1NWM2MjVhZWYzN2M1MTViOWYxMjdlNWRmYmY0ODg3YmFlNzk0Zjc2MjM5Y2M4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZVK3RzSzhSQUt4ZUZTU0VYcFhlNUE9PSIsInZhbHVlIjoiNUNhdGxFellWY0tVUlo3Z0hRVlZYV0p3eHhxb0xuS0NnR3kvRG8vb3MrdWsxb3lIK054Y1lFMXo3d29Zd1hhbTMrTVhQaHZNV0xMZDJGanpQK1k0ZVZYL012bjVvSW5zMjQxUG9nVHpSY3dhWEVJcG9BdFZrNzQ0L2QyZkVEKzgyM0t0OHJKd2EvTEd2NDdsdEV3MDM2cFJwaVlEQkJGaGZKanNISjVxckZoclo2RVdiR1JwSlNucTliaXk3a2o0b1RiQURGaTI2RlZuMHRvWVEzNmlOMjlGMXlTR3lKRHBTcXE2MEtJK1Vhemdmb0Q2TUlBb0FZaGd3Tys3ck1MTUxMZ0JFeGhxNVpnTnpZb3JRMmdXejZFeVRYYkZLM05aZENxQ2ErWXpTc28vT0xwME9aT2IvTjZweXZnZEl3RlRRMTZJTmxON1BVNTMwY2hqeGF1LzlMNFRqbEFqTnRRbC9CbklNcTF6aStTV1dTNXl2cGtoY1dzYzVHbHlWdjdRclk0eW5WOWxLN1JxcjNYckhVVkxPOGZBd1ZHTGl3UmR0bmVqckwrR3g5SWJ6SzlSZVhMb0p5cWJCNDBITEx2aFlkMmtxNVAveW9RZ2dLT0MxWHIrVXpxN0xCUVhuY0MvUUtDelU3SHc4OFNQRkNKZ29CNVBEMXFrVG1qMUtqUTYiLCJtYWMiOiIxMjUzZjk2YmViZDcwMjQ2MzFmNmMyYjM4Njg4ZTEyZjhiNzFjNzdhOGM5N2ZkZjczMGFhMDJhNmRhMDU4NmMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:09:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inp5ZkhrTlRTcVRZSFVGZ3RIVytsT1E9PSIsInZhbHVlIjoiYlN4cERkWEYzTlk5eXFVUEhibnA2YWNhNkxhSU1zTlFtTVQxNWxkL1g3ekRnb3MrMmJ6b21HSjFqaWxHdHZNSldodUNEMTliTDJkcUpBNjdXMHdUTzV6SEpKdk1iMW1od3dkUGFZZWdjd3Jua1I2ZVQrbno5MHdIbmNXVjJtYjlyMHJkTVdIa29hUWVub0U1R2dnM2VubExWSWs0Y1lzbmNEWFoybjNsSXBmYVROZGIrRGMzb0hjVUZYQWtKUmp4VTJOeFo1cmJPU3hYZlJxL2tMcGJ3b0tHOHJnaENwODl3aHVxU0w4MVkxODdjY1FkMDdVOTk4dWM1RWV1YkNoYU9nQkwwSFhnWS9YVk1PdlZuS2tTVSt5NmdKRGhpVFNrUW0zZWUvSUtYZFBNVitFUVNyNThndExVWmQyM0Y2YmVVeXgvd2dtQm90enExYmpQcTY3WklqWm1GY3Z2d3dTV1BOQWhzQ1UvUlZKTHhKNm9tS3UvMWRkaU9Za2I5R3dLTzVvTVlvdERZaWNDb2hRZlpSRldKZzVObWtkcU1TTEoyanpTK0lTOE9KbldDQW9NMC9TaVRBN3ZURU9jbk5PVVI3V1VMTFc0V1FnbysxY0VJdldKUW8yRkM3T0tmUWdFWkZMd0FxQWNMZFJJdHRwWER0ZFI1VDZmYTJ2TTFZODkiLCJtYWMiOiIyNzA0ZGFjYmMwYmIzYWExZDE1NWM2MjVhZWYzN2M1MTViOWYxMjdlNWRmYmY0ODg3YmFlNzk0Zjc2MjM5Y2M4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZVK3RzSzhSQUt4ZUZTU0VYcFhlNUE9PSIsInZhbHVlIjoiNUNhdGxFellWY0tVUlo3Z0hRVlZYV0p3eHhxb0xuS0NnR3kvRG8vb3MrdWsxb3lIK054Y1lFMXo3d29Zd1hhbTMrTVhQaHZNV0xMZDJGanpQK1k0ZVZYL012bjVvSW5zMjQxUG9nVHpSY3dhWEVJcG9BdFZrNzQ0L2QyZkVEKzgyM0t0OHJKd2EvTEd2NDdsdEV3MDM2cFJwaVlEQkJGaGZKanNISjVxckZoclo2RVdiR1JwSlNucTliaXk3a2o0b1RiQURGaTI2RlZuMHRvWVEzNmlOMjlGMXlTR3lKRHBTcXE2MEtJK1Vhemdmb0Q2TUlBb0FZaGd3Tys3ck1MTUxMZ0JFeGhxNVpnTnpZb3JRMmdXejZFeVRYYkZLM05aZENxQ2ErWXpTc28vT0xwME9aT2IvTjZweXZnZEl3RlRRMTZJTmxON1BVNTMwY2hqeGF1LzlMNFRqbEFqTnRRbC9CbklNcTF6aStTV1dTNXl2cGtoY1dzYzVHbHlWdjdRclk0eW5WOWxLN1JxcjNYckhVVkxPOGZBd1ZHTGl3UmR0bmVqckwrR3g5SWJ6SzlSZVhMb0p5cWJCNDBITEx2aFlkMmtxNVAveW9RZ2dLT0MxWHIrVXpxN0xCUVhuY0MvUUtDelU3SHc4OFNQRkNKZ29CNVBEMXFrVG1qMUtqUTYiLCJtYWMiOiIxMjUzZjk2YmViZDcwMjQ2MzFmNmMyYjM4Njg4ZTEyZjhiNzFjNzdhOGM5N2ZkZjczMGFhMDJhNmRhMDU4NmMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:09:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405014048\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1952807010 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952807010\", {\"maxDepth\":0})</script>\n"}}