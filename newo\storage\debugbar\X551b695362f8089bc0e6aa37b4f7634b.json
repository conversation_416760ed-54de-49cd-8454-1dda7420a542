{"__meta": {"id": "X551b695362f8089bc0e6aa37b4f7634b", "datetime": "2025-06-08 13:32:40", "utime": **********.6652, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389559.351445, "end": **********.665232, "duration": 1.3137869834899902, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749389559.351445, "relative_start": 0, "end": **********.516687, "relative_end": **********.516687, "duration": 1.1652419567108154, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516706, "relative_start": 1.1652610301971436, "end": **********.665235, "relative_end": 3.0994415283203125e-06, "duration": 0.148529052734375, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43935448, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.024629999999999996, "accumulated_duration_str": "24.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.612598, "duration": 0.023489999999999997, "duration_str": "23.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.371}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6460738, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 95.371, "width_percent": 4.629}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2013881622 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013881622\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1427082538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1427082538\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1457345935 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389251885%7C24%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpTNkpnOGQ0NGtpVHhnQk82ZlRIZ0E9PSIsInZhbHVlIjoiMzVRU2x4SXorQmM0R2dUMW8raE5RYmJnVjA1M0NYSjNsQWgzL2JLMytnVzZZdFNhaHJNQlRCS090ajBjdGZscFNSOHpKRkt1VUwzVTVyKzgyUEZRUTdEd0ZrMUdnRmJEcXZiM3ZuQWRMZ3RUNnNrc0xzaTRQV1krWXlMOFRJMUdmaGo2anF6aDluNGVNZFhCbERSblBBNDZKSDRVZ0RXaXBlOUhodGZzdVJNVlNHZkFjTVNWdjVlb1QvNGJJak1WSEVIVE0yOTU1emkwMW5KY3ZRZHpOU3BJZ011Qk9BK0FQV2oxNnZlNTdXTzNsQk1CWkdRRUN6RGZ1S1l0aktSQ0ZqQ3MxbkNXcTlUNFVRWmZ0M2xPaFNkWWIxMG5PdFN1d0lSdUcwZjl6TGp3eHlITlAvSW9wZDdOd1c0TmhaazRHTTI4WlBLR1gvZldaMUFQcXVZR0Vocko0Y2ZaY0xwejlkcXJoZnREdHdDTTFVMmVOZlZIM3F1WjQ1UDZvRURWbUtQazc0N2hhTmRQOVIyaXdPWHVrdDQrSVQvUDZJOER2V3F0L0RNbVltb29LL0hOc0tyd2ZpRlNhSWlQYWF5MGxMaDM1bktwaVVQSkxYS0VYY3liUEk5dW05SHdKcHgrM1VaSHNBTTZUcXBNY1FmQzJNZEQyb1d4cE1LSGxLTEwiLCJtYWMiOiI4ODEzYzFhZDk5ZDcwMWRmYjJmMmQwNzM2ZTMyOGU1MThjZjdlZTZjZTQyNDIwMDkzODU4OGI0NDIzNThjNjk3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iis1b0MzdmE4UlE4MVJHWHpIQkEvQlE9PSIsInZhbHVlIjoiY0xJQVQwYkxIbkM2OGxERXBOcWVDK1FsZHZvZXFqanhvZjBoKy9vUjU0d084M1ZHbnBCYkw4SzJkTnNDV0h3NmZLaGdTTFoyY0RzK2dveG5hcUUrVFprZ1Flcm03cEVPRXdZdVE4UndQSk1CTVBDNWZxUzYwRlorZFhPUTBtbDlXdGhwTU83TVM2anh1VEJsUm13RHBCTUZONkRSbDV0U1lIRDZSVmluUEwxU1E5aWVHaklCdkRkaWR3dmVsdDBqdUl4a0dtNUVRZ0NoaGZSL3VhR1VYT1BaUzZjTGpSc3FwaUROK2RHNzRsZDNiK3VvQ0lDVlRKbERNeWNpcVZuejc5REljaTFhekEzYTRoUVdJR3IrQU1pMjBrRmdYZHljRlVCTnk2TkNJeEpEbC8xSXd6RnJMRlZzdXk5eTZMMU1FL3JJZ3FmZkNNbnZFNE9hSnFYYmEycUJPWGJuZENJemdIMVR4K0NuUkpYVThyNHl2NWRqN0hWNHpRWVlFc1piS3paWG51dE5mcERkOWs0c3FXQnpURnBCR3pwdmFsSzRWT3ViakxvRVN6c2tqOU5wdUlPbGxTRGNmVXhaTmF1bkZxb0hldHhwZzVIRVVjQ2ZRcXpQTlpab3hrclhleHQyQjVwcHZtZE9NU3dHanBsSUdDWmdzejNMdmlDaElQTE4iLCJtYWMiOiI1ZGI4NWFmMTgxZTVjY2I1Y2E2NGFmZjk4Zjk4YWY5ZmU1ZTRiYWVjZDFkMjUxNTVlYzlhNmUzM2Y2ZTBiMjAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457345935\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1932722508 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932722508\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-387508742 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:32:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNMZm9YWGpiRE5TOWpXUEdtMFc4YUE9PSIsInZhbHVlIjoibms5dDNKM04wd3ZzUzBsemVCSmVxVllMeS9qV2RNY05QakJIUm1sVmJVZVA3K2pMVldOUWxXNnBzMTZqb2VSL2psemlFNEJJbllDdU5DSWk2K1gwTGhITlVVVGpXaHF3ZE5hVGdSbS9HZTkzVTVFRWtPeTdFZ0s4Zzd0b0kwNkRHWWd3aHVZSXpTbnBWcUxKaitkQlJUeVlJNDdQdjV2dkUramFMTEdaSzV0MWR6bFZRWm9VNmZMdEJSamtRN2VpNWFkejM2UEp6K3R2M3dPWTBRUElmazRUUXZ2QS85bzJGblhiVHRSQzloMzJkcThBTXJLckM4YW82YXBPQmUrUEVjeEY2TE5SR0c1NGJ1dDNZeWNtL0VmcG5uWHgvSG83eDg3dWRzK3o4WmkzcCttRmJ5Y3dlWG5qNG91b0F5OGlOMC9aa0VwMGFwVUY0aDJwZ3dhcEM3S1U1MDh2YUM1WS81NW9ZRHA2Q2locTlrb1dCZTM5S1ZxbE95bTcwWkRnVzBYY1FxM3YrQnZSQWJYTVdnMm10eTRYcGFKRFlTUkt0UThEOWZpY1dHc3lndzBQSnVSeCtZV0FFMTFpZGR5TUtIMHpFOW1hZ2xkdE9aZlZBKzI0d1k3N0kwSHFodVdvQnlXMHdRdmxnSHR2MkxXQnBuN3JxUW5qVDdrVjlXeDEiLCJtYWMiOiJiYzQwM2VmMmM0YjY0YTE4MWY1ZDY4MzdlOWE2NjA4Nzk1ZWE1M2Y2MGIwYWE2MGFhZTFmODFkNGRhNDhlMTFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjI2WFNyRE0yck5uWTVUcVU2Zlcrd2c9PSIsInZhbHVlIjoiTStQMkc5STVMZkQ5TWJOZWJ3VVRNcTN2QXlTZ0RPUTZCK2xOODB0MzZVOS9TTWpFSEZ4cldwYzRIckQvYkZhT2l4UFVPYnlwcHpUNHdhdHpSOW1BSndMMkU3VkVnd1h3SGlUSVdYMjFxYndFZFRDSHV1YVdYQjNsVWtoU3dLcUxteHJiWkUzWFZac0UwTnNkZFpyNnpZbytTZGYvZFlZK2NiQTN3Qk1zbWlEVW9xOGtEWWxacE16b2c0ZGpwZlJuRnpKTnlwK0Y0MmhuTzBJa1ZtdVhuemIrQlQrRzUrUU4xRUljQWtRTU1ISlJGRlI2RjBRd0d4NEptcDhtenVOVmY5ekcwekcxRUh6eVUyNzFyaXlKcThoOEN3eFJjVTRZd24rdnVqRUdWSHpJVGF2ZEdBUFFRVG5sWGZuRldzVlVzN0JWQUdPeC9CbVRaZDFvNnhSbllzenVGSUhiblZqb2s1STdQNmRPMlpESHZEM1FSNFY0eEVweTJmVHA1VElIUUJaLzI2UTBiMVl1blBscE5SUTB6amZrQXlvc0FpZG9VcXJaM1dJZlB0V3VUNndpdHB6TnAvcFdrdzMrbTdBWCt5Q3haRHpRaWJUQk16STlrcithV1phVjVxSXFDeUVOTmJDL0VndlNYZDFKbXNDTnh1YzRTWkdWNE9HU0hUY2YiLCJtYWMiOiI3MzQzOTBhODNiMmRhZDRjYjhjZTVhYTZmYTI1Nzk2MGVkZjAyYzBlMTU1ODY4NTVlZjU5ZjkwZDNiMTZlMGZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:32:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNMZm9YWGpiRE5TOWpXUEdtMFc4YUE9PSIsInZhbHVlIjoibms5dDNKM04wd3ZzUzBsemVCSmVxVllMeS9qV2RNY05QakJIUm1sVmJVZVA3K2pMVldOUWxXNnBzMTZqb2VSL2psemlFNEJJbllDdU5DSWk2K1gwTGhITlVVVGpXaHF3ZE5hVGdSbS9HZTkzVTVFRWtPeTdFZ0s4Zzd0b0kwNkRHWWd3aHVZSXpTbnBWcUxKaitkQlJUeVlJNDdQdjV2dkUramFMTEdaSzV0MWR6bFZRWm9VNmZMdEJSamtRN2VpNWFkejM2UEp6K3R2M3dPWTBRUElmazRUUXZ2QS85bzJGblhiVHRSQzloMzJkcThBTXJLckM4YW82YXBPQmUrUEVjeEY2TE5SR0c1NGJ1dDNZeWNtL0VmcG5uWHgvSG83eDg3dWRzK3o4WmkzcCttRmJ5Y3dlWG5qNG91b0F5OGlOMC9aa0VwMGFwVUY0aDJwZ3dhcEM3S1U1MDh2YUM1WS81NW9ZRHA2Q2locTlrb1dCZTM5S1ZxbE95bTcwWkRnVzBYY1FxM3YrQnZSQWJYTVdnMm10eTRYcGFKRFlTUkt0UThEOWZpY1dHc3lndzBQSnVSeCtZV0FFMTFpZGR5TUtIMHpFOW1hZ2xkdE9aZlZBKzI0d1k3N0kwSHFodVdvQnlXMHdRdmxnSHR2MkxXQnBuN3JxUW5qVDdrVjlXeDEiLCJtYWMiOiJiYzQwM2VmMmM0YjY0YTE4MWY1ZDY4MzdlOWE2NjA4Nzk1ZWE1M2Y2MGIwYWE2MGFhZTFmODFkNGRhNDhlMTFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjI2WFNyRE0yck5uWTVUcVU2Zlcrd2c9PSIsInZhbHVlIjoiTStQMkc5STVMZkQ5TWJOZWJ3VVRNcTN2QXlTZ0RPUTZCK2xOODB0MzZVOS9TTWpFSEZ4cldwYzRIckQvYkZhT2l4UFVPYnlwcHpUNHdhdHpSOW1BSndMMkU3VkVnd1h3SGlUSVdYMjFxYndFZFRDSHV1YVdYQjNsVWtoU3dLcUxteHJiWkUzWFZac0UwTnNkZFpyNnpZbytTZGYvZFlZK2NiQTN3Qk1zbWlEVW9xOGtEWWxacE16b2c0ZGpwZlJuRnpKTnlwK0Y0MmhuTzBJa1ZtdVhuemIrQlQrRzUrUU4xRUljQWtRTU1ISlJGRlI2RjBRd0d4NEptcDhtenVOVmY5ekcwekcxRUh6eVUyNzFyaXlKcThoOEN3eFJjVTRZd24rdnVqRUdWSHpJVGF2ZEdBUFFRVG5sWGZuRldzVlVzN0JWQUdPeC9CbVRaZDFvNnhSbllzenVGSUhiblZqb2s1STdQNmRPMlpESHZEM1FSNFY0eEVweTJmVHA1VElIUUJaLzI2UTBiMVl1blBscE5SUTB6amZrQXlvc0FpZG9VcXJaM1dJZlB0V3VUNndpdHB6TnAvcFdrdzMrbTdBWCt5Q3haRHpRaWJUQk16STlrcithV1phVjVxSXFDeUVOTmJDL0VndlNYZDFKbXNDTnh1YzRTWkdWNE9HU0hUY2YiLCJtYWMiOiI3MzQzOTBhODNiMmRhZDRjYjhjZTVhYTZmYTI1Nzk2MGVkZjAyYzBlMTU1ODY4NTVlZjU5ZjkwZDNiMTZlMGZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:32:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387508742\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}