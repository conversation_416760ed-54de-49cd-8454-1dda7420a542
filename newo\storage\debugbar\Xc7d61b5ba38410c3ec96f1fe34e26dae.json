{"__meta": {"id": "Xc7d61b5ba38410c3ec96f1fe34e26dae", "datetime": "2025-06-08 12:54:23", "utime": **********.990857, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.508407, "end": **********.990901, "duration": 1.****************, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": **********.508407, "relative_start": 0, "end": **********.767269, "relative_end": **********.767269, "duration": 1.***************, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.767292, "relative_start": 1.****************, "end": **********.990907, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.011630000000000001, "accumulated_duration_str": "11.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8580892, "duration": 0.0066, "duration_str": "6.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 56.75}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.897851, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 56.75, "width_percent": 7.825}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.957865, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 64.574, "width_percent": 35.426}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387248352%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFPcjcveTM3L0ZSSG9qazNGYmQwQ3c9PSIsInZhbHVlIjoibjRsc0JZamxHZUluK3YzdWdzL2UvQWRiRDNucGFOWHR0d0xkVkxQZDc4bFhETGUwQmhXRXJQcmVkbEl6NHcxK3F2U3BGT2E4SE5TVlFQU3p1ZkN4Yi9teURwdFp5Qk9qdGZleUkxdUMxRkdDTE0rbW5IbTNVSHFwZm91dUhIaWU1YmJ3NnMzb3FvWXY0a0IwNnFjalNMbVRaams5Yk1jVjdyN0Zrb3RUUjM3ZUk1NUNneU8ySC9jNzRFMHVxT2MxV3ZXVU8xa3ZZaGR4ZTVhL1ZEdzBvOU1ndFlNM05PenpjcWVkVktJdE1IU3hvT2p5c1pyNnRkc1M1RURmQjJpN1R3MjZKc3RTS2N1VDdiY1orNDdMUkV2elVIeVNqVFBkZHA5NXhPWnBSU0pyd21CRFVwUThkUElhOElXN3RxTTd5MVRCWTRpaC9vYzVPb0puZlY4c0JKeFJmNzJRZlZlT3p3K0hwbWZGVFlSN0lSTXlMQ2M5eTRsU1pqa0dMQXNGWnFkd0Z6ZFlyWHZmM3M4UWxOTVZmQmNOcXlDaDBpTHIrKzJYRUovSExmRkJBbVFFejQ5K2RseG9aYTA3VDl5cHMxQVhXNHZROW1jbXlNUU9BV256SnhjNlFhcE5wUTRsc0Rtc004K3FYeE5sTE1rUngwNWdFeEhZY0V1TkZwN1UiLCJtYWMiOiJhMTg0NjBiNDE2ZDM4YmRkOGQwN2I1ZTU0YmFiMzY5OTQwNGU4NjVkMWFhZDAxODI3M2M5MGQ5N2Y2YTkwODM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlhFeEEzYWJuTjZ5djlleTZiUDNxT2c9PSIsInZhbHVlIjoiNi9rRU9lcjROMWlMS0lleUJPc1lPREFxUmJpa3NESFBSUitvZThqNjNZZE5makwxL2RSVE8rSHc4S2c2RU5uWm4xcXl4TXA1WS9tWmxHbVNweFdaR2V4RThoQ2RkTFh1RjlhcExBbW5iSFB0OU9aRFUxbCtjcFI2UW5SQTQwSytTcFlyai9SMnV1VmlUQ0ZFSTE0UlpiZ2ZyQjY1djBIdXF5M0xpMVpiSkcvcUV6Y0JJMzZma1pGK2llRUdoNThzUVF2ejhjeFczbkxIQmw0VHR5anJtQmxvemlnMm1DZVNjMjgzRC83NnpYVkc0R1YybVcwUGdNdDRyeXRnRXNsOUQ2cHQ5TjhHeVJSdGp4NWNXQVJSRTl1VFJVcWwweGtDVDlyeFc4dFRzdDRyRUp4VnI4NnA1VU93KzlxTGNKVk9zWWU0YlN3ZXlUWlAvam1QL24xSGJDQmZDWXFaWjdmUzNMYmJzN081eVhBZ3c5anliZ2FKMGVzZGcvMkhLUEU3QkVlSEtTUUNzYVNPUXp4eU4vMm1peVZEa3Zhd1hoYjBRUSs0QkhnU1psZEw1eUlFSDJyejlCSXBaSG5rRkRLNDNzTGdPL0pHc3ZvbEYvUU9vUHNVZ2NBd1JZRUEzKzliSlVVeGlKdXJGK3hxSlBSWTdLaFFPQkVnVUFMMmxjSEIiLCJtYWMiOiJmOWI1Y2U4Yzg1MWVkNTYwNThkYjFmYmUxNjE3MmZkYTA1YmE4ODFjYzRhMTQyMzAyNDQwMTA0NThhYjZmY2U1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1346854136 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346854136\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1787966099 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlUa1FiZjh6ZnAvTEUzeXRPNkdzcXc9PSIsInZhbHVlIjoiazZ0Q1A3REpMbVNQSmdmaFhBNENaMktuenVSNEM0VzJRdFpvM0ZiTVBxaTZTaUozcVM1MnNlck4vemhKSzVKRlFZdWhBTmpBMnFjcU5NOVNKTEZ2cDdOaFZ2Y2crY2dnaC9wRng0OHhhUkZodGN5alhzZDh4bFV5NExpekxyNGFvbkpzblU5YmI1MGlUSWRnSmhLUmtVVUJtaU5VK1BJU2NkVXJhNkFBR0tNV0Y1QUw1bEFjNys5M3N3K0xUQmxSdkloc3hNVmVndW5leHg4Wi9FRW5KazJhbnQ0NjgydGZZVXdtWWhVaW5pSU5sdG0wTWJRa2dFN1NOVUQwTDhLRGtZYUU5a3pTU1czV2tmc1hSeUpGQjg4cWtuQnNIckFrZHhpdkcyQ1haYnpkc1JDRU9PUVhwbnhBTVhKbVhHVkxoeXdGdmx5MXdCZWlzK3kydUFhMTNaTUxaTnhYeFdWb2ozdUJxaDNoWVB2aWlFNy9SYzEzMStGVkNmemEzYUZObGJTaG82TW9MKzc3bERPVVpBSVM5MmYxL2JPbFdTZXpuRWFoVXJHOXFoMlVTdi9JQ1l4WTh2Nk9vUEZwWGFaa0tZbW1mMGRiZ3lqaGNHZ041eDJGRzQ1SjZQUzFoOThQU3djdUNwbmV1aCs3UzhBRm1aQm55VEJSWERrY2c5U3IiLCJtYWMiOiJhZmQ1ZDFhOWUxYjY5MzliZWMwMDI5NWNkYjAxYjg5ZjVmMGE2ODExYTQzYjIxZmQ1NjE0NTcyYjYyNTBiOWRmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNjWm95Tm9CV1BJU1BMYmsrMm9iVnc9PSIsInZhbHVlIjoiZmtjeEFoSG9OSDlPWDRxbVlGMUd2c2hGY2I1NlI0WmhxUWFEaXkrNGorWFh3aXNLZHBzbmxLNUJIQnR2eFJSenp2YWNTTzkwL0RHRGZlOVViSGZrWHhRVzh5aGdrWlhtY1ZlbitLdEhEYmdYZUNjUFYrM1VsV2JncllBUllleWJaR3FEUTEzaDhUUWJYcWg5ZmZOZWtYbGR2eGd3dFBjbklHYkVTenJ5a25PbmxVTjRMTlprUjJvOUVUck5lcVhHU2hGMmZNM2hhbVpxM2pRWkFCdDVEQmZ6b1dBU0NUNUY4VHlGUjVNVEREby9RV0F1Z0JSMlQwYnFWL1pndjQ2bjEwWVVHWGkxRWtEZFZneUJKN0labGtyTElNZzlUQlB1K1ZXQmtFTEZkdlQwWXRERVNtQU5nNmJxVWZsS2Q2TVl5VmlHdEtMT1BRUmtzd1RkamppYmd3WURUcEYyZmsrTjY3eWEySVRSdm5SUkg2TjBLTmVUNUsxTDVFeDlPdUJLUUpzamxnaDBFcnRWZHE5cURRaWxsblZSL09Lbmd3TmZpZ0x0OWVuR20wcmhSaHJwNERDRVVIaWNlSnk3R1FLRGxrTjRRY3ZkanFCRkpabWZna1NlMU0yeHpsYlRNSWc5R2cxVUpSVjExbVVUZTR3K1dUbjZrNHFEK1QxMWJsZUQiLCJtYWMiOiIzNTM5MDBiYmY5ODUxNzk4ODhkOTY5NWViZDJjNDMxNDBkYmMwNTIzMGNmODdlYTkxNWEwZTA5MGQ3ODgxMTllIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlUa1FiZjh6ZnAvTEUzeXRPNkdzcXc9PSIsInZhbHVlIjoiazZ0Q1A3REpMbVNQSmdmaFhBNENaMktuenVSNEM0VzJRdFpvM0ZiTVBxaTZTaUozcVM1MnNlck4vemhKSzVKRlFZdWhBTmpBMnFjcU5NOVNKTEZ2cDdOaFZ2Y2crY2dnaC9wRng0OHhhUkZodGN5alhzZDh4bFV5NExpekxyNGFvbkpzblU5YmI1MGlUSWRnSmhLUmtVVUJtaU5VK1BJU2NkVXJhNkFBR0tNV0Y1QUw1bEFjNys5M3N3K0xUQmxSdkloc3hNVmVndW5leHg4Wi9FRW5KazJhbnQ0NjgydGZZVXdtWWhVaW5pSU5sdG0wTWJRa2dFN1NOVUQwTDhLRGtZYUU5a3pTU1czV2tmc1hSeUpGQjg4cWtuQnNIckFrZHhpdkcyQ1haYnpkc1JDRU9PUVhwbnhBTVhKbVhHVkxoeXdGdmx5MXdCZWlzK3kydUFhMTNaTUxaTnhYeFdWb2ozdUJxaDNoWVB2aWlFNy9SYzEzMStGVkNmemEzYUZObGJTaG82TW9MKzc3bERPVVpBSVM5MmYxL2JPbFdTZXpuRWFoVXJHOXFoMlVTdi9JQ1l4WTh2Nk9vUEZwWGFaa0tZbW1mMGRiZ3lqaGNHZ041eDJGRzQ1SjZQUzFoOThQU3djdUNwbmV1aCs3UzhBRm1aQm55VEJSWERrY2c5U3IiLCJtYWMiOiJhZmQ1ZDFhOWUxYjY5MzliZWMwMDI5NWNkYjAxYjg5ZjVmMGE2ODExYTQzYjIxZmQ1NjE0NTcyYjYyNTBiOWRmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNjWm95Tm9CV1BJU1BMYmsrMm9iVnc9PSIsInZhbHVlIjoiZmtjeEFoSG9OSDlPWDRxbVlGMUd2c2hGY2I1NlI0WmhxUWFEaXkrNGorWFh3aXNLZHBzbmxLNUJIQnR2eFJSenp2YWNTTzkwL0RHRGZlOVViSGZrWHhRVzh5aGdrWlhtY1ZlbitLdEhEYmdYZUNjUFYrM1VsV2JncllBUllleWJaR3FEUTEzaDhUUWJYcWg5ZmZOZWtYbGR2eGd3dFBjbklHYkVTenJ5a25PbmxVTjRMTlprUjJvOUVUck5lcVhHU2hGMmZNM2hhbVpxM2pRWkFCdDVEQmZ6b1dBU0NUNUY4VHlGUjVNVEREby9RV0F1Z0JSMlQwYnFWL1pndjQ2bjEwWVVHWGkxRWtEZFZneUJKN0labGtyTElNZzlUQlB1K1ZXQmtFTEZkdlQwWXRERVNtQU5nNmJxVWZsS2Q2TVl5VmlHdEtMT1BRUmtzd1RkamppYmd3WURUcEYyZmsrTjY3eWEySVRSdm5SUkg2TjBLTmVUNUsxTDVFeDlPdUJLUUpzamxnaDBFcnRWZHE5cURRaWxsblZSL09Lbmd3TmZpZ0x0OWVuR20wcmhSaHJwNERDRVVIaWNlSnk3R1FLRGxrTjRRY3ZkanFCRkpabWZna1NlMU0yeHpsYlRNSWc5R2cxVUpSVjExbVVUZTR3K1dUbjZrNHFEK1QxMWJsZUQiLCJtYWMiOiIzNTM5MDBiYmY5ODUxNzk4ODhkOTY5NWViZDJjNDMxNDBkYmMwNTIzMGNmODdlYTkxNWEwZTA5MGQ3ODgxMTllIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787966099\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-12******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12********\", {\"maxDepth\":0})</script>\n"}}