{"__meta": {"id": "Xa40ca1e597d6c2ce087fa0252ece6291", "datetime": "2025-06-08 14:54:55", "utime": **********.389744, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394494.80098, "end": **********.389766, "duration": 0.5887858867645264, "duration_str": "589ms", "measures": [{"label": "Booting", "start": 1749394494.80098, "relative_start": 0, "end": **********.276937, "relative_end": **********.276937, "duration": 0.47595691680908203, "duration_str": "476ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.276949, "relative_start": 0.4759688377380371, "end": **********.389769, "relative_end": 3.0994415283203125e-06, "duration": 0.11282014846801758, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.013059999999999999, "accumulated_duration_str": "13.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.321528, "duration": 0.009689999999999999, "duration_str": "9.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.196}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.342455, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.196, "width_percent": 4.594}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3642092, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 78.79, "width_percent": 4.747}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.36763, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.538, "width_percent": 4.518}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.37431, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 88.055, "width_percent": 7.58}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.379286, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.636, "width_percent": 4.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-351765460 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351765460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.372956, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1202455083 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1202455083\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1165050221 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1165050221\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-429571171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-429571171\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-257284781 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394379618%7C66%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZpdWRVVGhMWEtNZGhYSWp0M1o2aWc9PSIsInZhbHVlIjoiQlQyWDh3M083TGd2cEh3NmQrVGZrVFFiOEtRR0JaUURybkpxd1VqUmI5MWNucEpOeHpwU2dsQTZ0MERVOVlxWVJxZzdEYmxDUko1clF2bTRSL2xWZG5icGUxaTljNDBMQnFIeWZHR1RNeldsRkJDSlFTcGFJVFVMSVJSL1pMMExSdzdhUWMyd2pkWHQ2NlNpZmx6b0NXVlpJcUY2RjdvQStsVGVwNC9aQXZhTC9OSHlwOU16NUNiaU40MWx0WThwekszM3Z5WFdkem9FN0E4akJMMUVvSnhiWkZjQnZvZFdYaWNLdTZIL29vTm1PcWdEU3MybXoyQVhlUGloT1Y4eTBqSDk5a2QvMzZUWWw3NU50Z2ZkRWhweEVVYzc2VGxBWTdNNzN1WkJGNUNEOXJ2RlpZN0ZHRm1tVWcyMlRKaGQvM0w2MUJLUXVhLzUrcHRzQkR4YThjd1ZjUXJlWTFld29xd0lzaHZOZXJLTC9vVXZrcGxPYjFXRFhXVXorK2VTU0l0WGU0NmhsUW4ySmlsUG5YMnIvdGIwS3FHNkVsclk3V1lPSmpPb055Z2JIcjNrUHJqbXo4SkpPcFRHVnpmdENPa2lLaWJRbFVXTzU5R0NvT1NQb2w2UGpoRlEvWWcxMXNkd3lGWWxtS3pDYU5NK2VJOFBMcVZJY1dHekR4MVQiLCJtYWMiOiI2ODFmZjdiZDdiZDA5ZDZmNzhlOTliZjIzZDk0Mjk0NjNjYjQ2N2JjZDIyZjNlMzBkZGRiNjA2OTY1ZWVmZGRhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImUxcVJ2T1o4WFg1dStHeDFVM3A5Q0E9PSIsInZhbHVlIjoiVzRXZkJUa3NwMElmR2dDa2JTUWRJVFVZT0ZTRit6QWhEZFQ3MGZSUGFFbkpNN2RpMkFJdi9kNStNVzg2REh1bVkwcEpNbGpSc01CcVJSNWZFczVTKzJDT1Zwbm5mUDhoend4QTh3NTQzRzRYeFR3WVdCcEIyTkZ3enlVTFQ4MFc5eE1lQzVtc0xva0lJT0Fta1MwbTRKNzZTUVFiZ3VXbUYyM01WQzllUmtXNUZ4ZXpubXpKQ0JJMUlpZGdQeFBrekM5U3F1TXg1UEdvaldvVU5OTnJsK0ZIMDF2cGU3WDRaTEl1U0J0R2JYYnlFZHNTQklwTnlDMWwwSlhQUVpJbnNRQUtIcmN0b0E1ZExmVi9FTHp4STU5NE1DelZmR2lBampKa0JxcC9QTFd4bXp4eXVKSTRFUVhRNXlYSm5KVXNFa1pPQ3ZkMWJBK1UvQVRIbGlPZUxjQVA5VWdJNGVaODlLdFptVVlzbFJQcGlCZ3BMVWlnSTlGWklRQ0VjZEhLTUdveTFzYjFUM0ozNGJpd3d3TXZYei9pOTVBZENHVlFheGJ2cHpyelVCRkd2NFZKWUZLeWF1VThyd05Ga1VpckVzcjllTnZLNG1HdGUzT01GUmZXdVlpQjhDd3A2Qi9HeENkY0cxeUNOWXY0Y3RtT2dQSjFQeGVVNVdValpuUmIiLCJtYWMiOiIyNTI0NGE1ODVjNmVkODllZDFlYmZjNzQ5MzIyYWM5NzNjZTFmMTY1YjI4OTQ4NzFmMzJmYzJlNmI3ZTY0ZDJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257284781\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-211570475 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211570475\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1010846536 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:54:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpDYi9Dc2pETG9nYnk5bjlSdnl2T0E9PSIsInZhbHVlIjoiK3Ztc2lXZUx5cWVFeTJrZUcwRWZGdmpMbkk3RUlZcDFkcFRLRXpoR2hyMFdReUtOSE1mQlZKb0RPaEJpcFF4Qk9sNnNTSS9vL0NwZjVkNmJNK3JpSVc5dk5tTWFPWXFWRC8xaGJFVjE3ajk2dnNlWWFJTWFmekFoU1plWTRUZU1PU1g3aUU0TXBjb3o5TmZMMjlaV3c2K2pjckc3OWdRZHVMTHc3OS8wdUtTbnZTdHcxRmFESWIzZG5wNktOaWR6Y0t2c2hudWNxZEFEeUtCQXFIMWIwUXhsR1p5R2FETExtL0gyM0w2bzh6NmQyUmU1WmIvRGdOTnY1NkJpVlhmQUNlSXJPMkVsMitHdi83RmM1YUNnWTA0WnJSMXVEV3BMYWllY0lXL2RBT1Y0eXdsc29mZVM2VHNFZTluUnkzT1REMVgvcEdYMFpPNS9QOWVMMkh2VCtaRC9uZVNMaitucVdMVnhSR3Q2cXlhNHh5VndiL1o3MFVWaWtsblRIREJUSEFiZFFmOXJRNFRXU280RTVIOWJWKy9BT2o4Tm96Rmp6b3NpdHVjRjNTb21wNjJrVHhNT3JGckxwSVk4NXA5aU1OUTA3YUF4S3NWTnZoL0RSazQ1d3JoRHBzdGM4R0lvRmRRWnJveUxRS2hWR3dxNURjS0RjdFNuZmdudHZ5MUYiLCJtYWMiOiIyYTQ2NmI2NWY3ODNjMzRlYTIwYzA4MjE0YjdjZjk0YjM3MmM3MmIzMjkzNTUwYzRhOWJmMTk0MjZiMzFkNzA5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:54:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjF5WlgxbUd2WFZWU3FncnpHNmYyS1E9PSIsInZhbHVlIjoib2RKejN6c0hBR3JtNy9SYnU4bDB5RkE5MmdVNmNHa1grSjM5MFkyaENwajBRcjU1eG5ja0JmQWxici8xYVVtT2t5TWpQTlZ5bXdHcjUzcnc1aWZNNjB2ZEtEWEUrcC9SR0wxNDlBd2pvS3d2UStaWnpPZWIxZ1F4MTRrZTNjTm1lc1VqRlk2aFlCaUJPNFU4bmk4WmRQRUNhanZmYkNtcVRQNnFiUTBtaTZ0V3UwbjdETE1xMUtOa3Y2N1VTKzRxSG80SFJaWHNGSkh1ZC91bFA0Q3hUcS9iMWFaV01VR3hMOGFQOGp6YzY2by9QZ3Jjd3VvaW5JdkN1bkgycC9hRjJ1STQzYjNsdXpSSjZRdkV4NXQ0Y090YUZGYXYyUWlpV2ZsTGM5N1NFVXZtYWdzbHZpYmxBR21iS0xsblBwV0YxNzhpQ3orMjNFVlVOY3YzMVFJMHZpU3dyM2Ftd21WSVNBY0docE1rditnU1g2c3BWRHR4aERSWnJUbVdJaUYwcVY4WEpCaWRucVFRNVJmdjNtcHdUd3ExdmlYclpLU0htL1k2dVVPQWt1TDlLajNxcVh6T09tWlRNN0N4Z0pnQXl6N2FpODJ0TmwwR1h0Qkgwei80OXhkb0VFVDh6YzBvS2sxS3c3TEN2Ni9CUW1EWVh5OXI5N21KNVZwSUthU2YiLCJtYWMiOiI2ZDgxNGE2YWE4ZGVmN2RlZWJjMTI5NzRlNGE3M2QyZTVmNTcxOGVhMTYwNzRhNDE5NDk2N2M5MjM0ODNkZjA3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:54:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpDYi9Dc2pETG9nYnk5bjlSdnl2T0E9PSIsInZhbHVlIjoiK3Ztc2lXZUx5cWVFeTJrZUcwRWZGdmpMbkk3RUlZcDFkcFRLRXpoR2hyMFdReUtOSE1mQlZKb0RPaEJpcFF4Qk9sNnNTSS9vL0NwZjVkNmJNK3JpSVc5dk5tTWFPWXFWRC8xaGJFVjE3ajk2dnNlWWFJTWFmekFoU1plWTRUZU1PU1g3aUU0TXBjb3o5TmZMMjlaV3c2K2pjckc3OWdRZHVMTHc3OS8wdUtTbnZTdHcxRmFESWIzZG5wNktOaWR6Y0t2c2hudWNxZEFEeUtCQXFIMWIwUXhsR1p5R2FETExtL0gyM0w2bzh6NmQyUmU1WmIvRGdOTnY1NkJpVlhmQUNlSXJPMkVsMitHdi83RmM1YUNnWTA0WnJSMXVEV3BMYWllY0lXL2RBT1Y0eXdsc29mZVM2VHNFZTluUnkzT1REMVgvcEdYMFpPNS9QOWVMMkh2VCtaRC9uZVNMaitucVdMVnhSR3Q2cXlhNHh5VndiL1o3MFVWaWtsblRIREJUSEFiZFFmOXJRNFRXU280RTVIOWJWKy9BT2o4Tm96Rmp6b3NpdHVjRjNTb21wNjJrVHhNT3JGckxwSVk4NXA5aU1OUTA3YUF4S3NWTnZoL0RSazQ1d3JoRHBzdGM4R0lvRmRRWnJveUxRS2hWR3dxNURjS0RjdFNuZmdudHZ5MUYiLCJtYWMiOiIyYTQ2NmI2NWY3ODNjMzRlYTIwYzA4MjE0YjdjZjk0YjM3MmM3MmIzMjkzNTUwYzRhOWJmMTk0MjZiMzFkNzA5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:54:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjF5WlgxbUd2WFZWU3FncnpHNmYyS1E9PSIsInZhbHVlIjoib2RKejN6c0hBR3JtNy9SYnU4bDB5RkE5MmdVNmNHa1grSjM5MFkyaENwajBRcjU1eG5ja0JmQWxici8xYVVtT2t5TWpQTlZ5bXdHcjUzcnc1aWZNNjB2ZEtEWEUrcC9SR0wxNDlBd2pvS3d2UStaWnpPZWIxZ1F4MTRrZTNjTm1lc1VqRlk2aFlCaUJPNFU4bmk4WmRQRUNhanZmYkNtcVRQNnFiUTBtaTZ0V3UwbjdETE1xMUtOa3Y2N1VTKzRxSG80SFJaWHNGSkh1ZC91bFA0Q3hUcS9iMWFaV01VR3hMOGFQOGp6YzY2by9QZ3Jjd3VvaW5JdkN1bkgycC9hRjJ1STQzYjNsdXpSSjZRdkV4NXQ0Y090YUZGYXYyUWlpV2ZsTGM5N1NFVXZtYWdzbHZpYmxBR21iS0xsblBwV0YxNzhpQ3orMjNFVlVOY3YzMVFJMHZpU3dyM2Ftd21WSVNBY0docE1rditnU1g2c3BWRHR4aERSWnJUbVdJaUYwcVY4WEpCaWRucVFRNVJmdjNtcHdUd3ExdmlYclpLU0htL1k2dVVPQWt1TDlLajNxcVh6T09tWlRNN0N4Z0pnQXl6N2FpODJ0TmwwR1h0Qkgwei80OXhkb0VFVDh6YzBvS2sxS3c3TEN2Ni9CUW1EWVh5OXI5N21KNVZwSUthU2YiLCJtYWMiOiI2ZDgxNGE2YWE4ZGVmN2RlZWJjMTI5NzRlNGE3M2QyZTVmNTcxOGVhMTYwNzRhNDE5NDk2N2M5MjM0ODNkZjA3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:54:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010846536\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-20447555 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20447555\", {\"maxDepth\":0})</script>\n"}}