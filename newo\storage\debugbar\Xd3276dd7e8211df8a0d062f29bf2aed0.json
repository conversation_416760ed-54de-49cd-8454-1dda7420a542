{"__meta": {"id": "Xd3276dd7e8211df8a0d062f29bf2aed0", "datetime": "2025-06-08 13:48:42", "utime": **********.714101, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390521.154647, "end": **********.714134, "duration": 1.5594868659973145, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1749390521.154647, "relative_start": 0, "end": **********.533487, "relative_end": **********.533487, "duration": 1.3788399696350098, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.533509, "relative_start": 1.378861904144287, "end": **********.714138, "relative_end": 4.0531158447265625e-06, "duration": 0.18062901496887207, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02222, "accumulated_duration_str": "22.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6377292, "duration": 0.02137, "duration_str": "21.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.175}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.689107, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 96.175, "width_percent": 3.825}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1101234832 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1101234832\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-706924082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-706924082\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1038666268 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038666268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-822499272 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1CcXJVY1RSNjU3eGdNSGVPVmYzbHc9PSIsInZhbHVlIjoielVkeWhnci9xcnJYdTJpWGtaejVLc3E2SHdWZG1CcFI3Qy8vbGxMYlZ3cTBFN1gvdjFHOUwxRHpxTHdhWjNoTDlHZVNLQU0xR3BtSGxkbzQ2YVNkV0poeVY2d3YwblFkVVVHQ1VxdXh3cjNkSTV1cjJUbFV3VGRxNU9XM2ZkWWFGL1ZmMml6R1B0SWoyckIvbk1EcjN0QXpqcnVWK0NhOVpHdUd5Q2F0R0hPWWVGaC92eXFXQ2N2d3VrMzNmTlhaS1cxRVBnallhbDI1KzJYdXMwaERoeXZtQ3JlZ1lWSDNnYkpiZC96aWU5b0d3bWVmL2RJT05wQ3BrQmExa3dQWHFFNlgyZHNuOWVMNndQMzRtck9CR2hFemxWclVkMnlkUFNLeE1kVkUrVVY3ZnFkb09waUhGMDA3aEdBWml5c1d4UEpKaHdjNkMrbXV5SjhVejk2RDVHWmhKTXhINkJ2bXg1TDRHcFNETC9vd0NVUjY3U1A3T0VrQzdvV04rckloUldQekQzSHFuNUZEeWVpZnh5bzZnZjhUMm14eUxWU2o4blFLZGRwT0FBeDMweW5EV2w3RXpLajR3ZE1kVzVqOUdXWkRwY0FBQ1Jkc2ZpTkJrZzgrSUZRUEVlR0dEMUYvb0piVkJaVUxSaTh6ZWkyUDd3cHlpWXBoYWFLbm81OFkiLCJtYWMiOiIwMTlkMmQyYmIzMzU0N2IyYjRjNjIyMzQ0ZDFjZGFhNjVhYTc4Nzg5MDc1ZjYxMTI1N2I2NjZiYjdjMzZhM2YyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVRWk9haWhzMU0zTkQ5cU04QzJ5cHc9PSIsInZhbHVlIjoiL1Y0Q2hncXBrVTF5cXg3WFBQL3dxb0xaa2hBTUhORVh5VjJXZ0czOUhTRnhScVdyU1R5VWtLL0FxMnhFVi9vL0tEem0xWFhHYkF4bnNQVEttTnFjeU5TWHZMQ1phMG93QUxCNWVkaUpqejBPTkkraDdoeE1HNjdtUWVNcTB1SndGdTZYNW9SYzIyak5KdVptSERoVUx5UWZkOWxNOFB5enI2RU1GZkVUem5DQW50dUVQNVVzcWJ6bzNxZmREd0hNMTVrZ3pMSERvU0IvWkZ3azFmWlA4aHVZYTRpYjlVTjlFZ2pzbWM5WUN3dG0wR0YvNnNMUTB0QlpOekhTL1J4N1RPakI2YmNzL3BoVVF0TWpHdG5TYmk0NGxFL3FnbUlWSnFCWmFFeVJqUTlITDVXSWo3UitnU2VEZEdTc3V3Z2RKZzRjcXBrQmo2Qi9ldnJXdmlTS2RUVUFSbHB5VGtqUDRkV01ZcDcxTVZoYWJPR3dJVFlTNVhCVHg5Z1pHTS9oUFIxUWp2RnB4c0pYWHNmUFFRNXR4VEtMSURnWFJHeEdmSmhNNVV2Vzl2WnZidFNJSktWRHo4Yk1sM21iSEV0aVNhQklNQWc1bjJlcmRIREUwamY1THYxa1NEbm42cFk5bFlyUkZFSVUwaS8zZTgwbWRqVVRnZVlvTWFSSjQ1VHEiLCJtYWMiOiIxNjRiMmY1MjgxMTM0NWYwNTI3MDRiZTQ5YjUwZTBmZWUzNzFiOTNiZmFhYjkyOGNlMWU1N2E0MWQ1MDE2ZDUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822499272\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-231295352 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:48:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5CSmNlQ21zN1hvdStxdnpCMFVCZ2c9PSIsInZhbHVlIjoibytqb0t5Q25tcVpkQUZRZ21Jc0FOMC9vdG5mK2Z3ZG8vb1FUMWIzUlF3Q3JsVTZMWjM2a09BYnJiS1lmUm8vZVc2OW8xanI1OFBtS3hBZXc0V2FNUlJ5Ri91MGZHYzgxS3RoTWVSUm81M3JKc3M5Q3FXVmtOZ3k2bmJ6d2dxc29rb29sUTBuSFRSVUZ4MDIzYitFVHErY2h5VjRqdGd2a0lGUHRQY0NPY1lqUm5Rc1g1NFVRYkdGMzVZYWRvdjlJWUxlelBpNzg0VUxRdEtIV1UyOVphSXlFZFJCUnFxSXdsbmwyaDgyUFR6WlAwaTFLQnUvM1FPNmMwZk9ONVFrSHg1RHFCZ0hmSGVaTEJ2dkx6UlNlYm9sK0NRNHA1NzhUMGc2RWhUeDZTMnZFQUJUaGY5S2x4REhiK1RGbTJDK0dkRTc0ZERSUy9WTDB0eVdBdHZZZENqSlo3M1hFZnl1dk1TNFZrRncycmF2UXN1aHRvdE1hUEhIT1ZiamhkYkRqemVEdFZrbkUvYm56WVVoeTdoTExuaURJeUNLaExjcjNBNnlPRTNSYUxCYVZYM3pJN2pVNUtHYkYvck9LUVljZ09tN2dzckY3cU1EaGw4SGh6ckMrL3orZ1cvWENXZVQyVGMzTWMzeFh2WlVDZWEvajd3aE5JV0JKdm15ODFwMXEiLCJtYWMiOiI5Mzc3ODA5Nzc2NjYxNzE1ZWVmNTkyZjVkNjQzOTE2OWJiZjUxOGQyZjE3YTZiYmUzMmU3YTEyNDkyODljMjg3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9EeXpPMURrRDR1aGtuVzBKYVNsRUE9PSIsInZhbHVlIjoiWFR5MHhCRWltQXo2ak9RcmlNYmF1WHlKQWhYT0tMZXg4Z2FZMXJDbS9JVW5XQU50MVNjY3lpK2VoNUREZXNKL1VjSVJVb3EzdkF3WHpWcWo3cCs4U3BzdXV5anNBY0NkK3NQRnlCb3ZZdzVvR1FTNjh4MTYvM09Xc1QreW5UeEpzN1l0YlpNZmJXM29rbjVjbEFCcFl4QzFHTUtHNlo2SGRDRy9zYzVFUjE3cnFNNVc0TldnRHQ1OS9NQXhTd2lIdS9OQ2owVFRYeHNEeE5XYSs2QlNMS1lOKzdyTEtqRHpDdHVzQ1RSMzVsb3VEZVlTa3JPcm9oczk4SG9SVlA0aHNmSnpXdWUwNXliczliSEpXQmdNZjJnR3hraXBOQ1lmdU1NeUtFemF6SmY5TFB1WVZaaUJLNzhOUkZDZ2toZ3JkdHIyVGkrMS9BL3c0RU9lNUNqSFU3bWZ6WmMyck5iTkpqYTVoR0xzVk16WE5uY21IRE82YjU1V0RMckFuNGtJTkhDMHJMRnpEaDFWZlZtMzdNOGZDTGcwWDdOajFPWkc4NWVuZTJkUjN2alY3YTR3S2JoMmZEMkdnbEw2bXppcHl5dWZzYlBzOWorakVLOVVrYlc4S3R3SnUwQmxxdzZPaHJ4SWlVNnI3eTc2VU9pZHpyK0xGVVZQbmpqZU4wUEYiLCJtYWMiOiI4OWY1YjZkYWFmNDNkYTM5ZTEzY2NmYjk4Mjg2NGNlZGI1MTg2NDMwNzI5N2FiOWFiNTljYjY0MTAzNTk5N2IwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:48:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5CSmNlQ21zN1hvdStxdnpCMFVCZ2c9PSIsInZhbHVlIjoibytqb0t5Q25tcVpkQUZRZ21Jc0FOMC9vdG5mK2Z3ZG8vb1FUMWIzUlF3Q3JsVTZMWjM2a09BYnJiS1lmUm8vZVc2OW8xanI1OFBtS3hBZXc0V2FNUlJ5Ri91MGZHYzgxS3RoTWVSUm81M3JKc3M5Q3FXVmtOZ3k2bmJ6d2dxc29rb29sUTBuSFRSVUZ4MDIzYitFVHErY2h5VjRqdGd2a0lGUHRQY0NPY1lqUm5Rc1g1NFVRYkdGMzVZYWRvdjlJWUxlelBpNzg0VUxRdEtIV1UyOVphSXlFZFJCUnFxSXdsbmwyaDgyUFR6WlAwaTFLQnUvM1FPNmMwZk9ONVFrSHg1RHFCZ0hmSGVaTEJ2dkx6UlNlYm9sK0NRNHA1NzhUMGc2RWhUeDZTMnZFQUJUaGY5S2x4REhiK1RGbTJDK0dkRTc0ZERSUy9WTDB0eVdBdHZZZENqSlo3M1hFZnl1dk1TNFZrRncycmF2UXN1aHRvdE1hUEhIT1ZiamhkYkRqemVEdFZrbkUvYm56WVVoeTdoTExuaURJeUNLaExjcjNBNnlPRTNSYUxCYVZYM3pJN2pVNUtHYkYvck9LUVljZ09tN2dzckY3cU1EaGw4SGh6ckMrL3orZ1cvWENXZVQyVGMzTWMzeFh2WlVDZWEvajd3aE5JV0JKdm15ODFwMXEiLCJtYWMiOiI5Mzc3ODA5Nzc2NjYxNzE1ZWVmNTkyZjVkNjQzOTE2OWJiZjUxOGQyZjE3YTZiYmUzMmU3YTEyNDkyODljMjg3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9EeXpPMURrRDR1aGtuVzBKYVNsRUE9PSIsInZhbHVlIjoiWFR5MHhCRWltQXo2ak9RcmlNYmF1WHlKQWhYT0tMZXg4Z2FZMXJDbS9JVW5XQU50MVNjY3lpK2VoNUREZXNKL1VjSVJVb3EzdkF3WHpWcWo3cCs4U3BzdXV5anNBY0NkK3NQRnlCb3ZZdzVvR1FTNjh4MTYvM09Xc1QreW5UeEpzN1l0YlpNZmJXM29rbjVjbEFCcFl4QzFHTUtHNlo2SGRDRy9zYzVFUjE3cnFNNVc0TldnRHQ1OS9NQXhTd2lIdS9OQ2owVFRYeHNEeE5XYSs2QlNMS1lOKzdyTEtqRHpDdHVzQ1RSMzVsb3VEZVlTa3JPcm9oczk4SG9SVlA0aHNmSnpXdWUwNXliczliSEpXQmdNZjJnR3hraXBOQ1lmdU1NeUtFemF6SmY5TFB1WVZaaUJLNzhOUkZDZ2toZ3JkdHIyVGkrMS9BL3c0RU9lNUNqSFU3bWZ6WmMyck5iTkpqYTVoR0xzVk16WE5uY21IRE82YjU1V0RMckFuNGtJTkhDMHJMRnpEaDFWZlZtMzdNOGZDTGcwWDdOajFPWkc4NWVuZTJkUjN2alY3YTR3S2JoMmZEMkdnbEw2bXppcHl5dWZzYlBzOWorakVLOVVrYlc4S3R3SnUwQmxxdzZPaHJ4SWlVNnI3eTc2VU9pZHpyK0xGVVZQbmpqZU4wUEYiLCJtYWMiOiI4OWY1YjZkYWFmNDNkYTM5ZTEzY2NmYjk4Mjg2NGNlZGI1MTg2NDMwNzI5N2FiOWFiNTljYjY0MTAzNTk5N2IwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:48:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231295352\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1933740470 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933740470\", {\"maxDepth\":0})</script>\n"}}