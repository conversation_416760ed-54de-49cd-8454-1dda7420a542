{"__meta": {"id": "Xd2c673aadf0a6e7e996434e40a4a993a", "datetime": "2025-06-08 13:15:30", "utime": **********.97499, "method": "GET", "uri": "/barcode/pos/html?only_available=1&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388529.543818, "end": **********.975035, "duration": 1.4312169551849365, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749388529.543818, "relative_start": 0, "end": **********.686993, "relative_end": **********.686993, "duration": 1.1431748867034912, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.687012, "relative_start": 1.1431939601898193, "end": **********.975039, "relative_end": 4.0531158447265625e-06, "duration": 0.2880270481109619, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52360864, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.barcode_pdf_simple", "param_count": null, "params": [], "start": **********.946027, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/barcode_pdf_simple.blade.phppos.barcode_pdf_simple", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fbarcode_pdf_simple.blade.php&line=1", "ajax": false, "filename": "barcode_pdf_simple.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.barcode_pdf_simple"}]}, "route": {"uri": "GET barcode/pos/html", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@barcodeHtml", "namespace": null, "prefix": "", "where": [], "as": "pos.barcode.html", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=888\" onclick=\"\">app/Http/Controllers/PosController.php:888-1006</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0286, "accumulated_duration_str": "28.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7895498, "duration": 0.02026, "duration_str": "20.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.839}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8355682, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.839, "width_percent": 4.091}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.884561, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.93, "width_percent": 5.629}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.891757, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.559, "width_percent": 5.14}, {"sql": "select `product_services`.*, `warehouse_products`.`quantity` as `warehouse_quantity` from `product_services` inner join `warehouse_products` on `product_services`.`id` = `warehouse_products`.`product_id` where `warehouse_products`.`warehouse_id` = '8' and `product_services`.`created_by` = 15 and `warehouse_products`.`quantity` > 0", "type": "query", "params": [], "bindings": ["8", "15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 916}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.906919, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "PosController.php:916", "source": "app/Http/Controllers/PosController.php:916", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=916", "ajax": false, "filename": "PosController.php", "line": "916"}, "connection": "ty", "start_percent": 85.699, "width_percent": 6.748}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 987}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.914813, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 92.448, "width_percent": 4.231}, {"sql": "select `value`, `name` from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4078}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 988}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.922283, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4078", "source": "app/Models/Utility.php:4078", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4078", "ajax": false, "filename": "Utility.php", "line": "4078"}, "connection": "ty", "start_percent": 96.678, "width_percent": 3.322}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-362419572 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362419572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.903633, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos/html?only_available=1&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/barcode/pos/html", "status_code": "<pre class=sf-dump id=sf-dump-1262824941 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1262824941\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1953067764 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>only_available</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953067764\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-603288578 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">http://localhost/barcode/pos?warehouse_id=8&amp;only_available=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388514803%7C18%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlKUzZyK2pabjEwMGpaTjdna1ZkclE9PSIsInZhbHVlIjoiSDBka3g5dFR4MUhsOGtsN1FPdndZWCswb3BHRTlPbGRPQ0phVjZkSm0xTzJ6NTFaVlM2dXV6U3VvSVhvSEN2RURTdWp4QWFsWm85L0xuY3JEWnIxQk8xNnRIWlQ5QmM4aUhRdHF1MGtiRmNvdU9JMGRBclh3YjFkMG9LL3hwRE52QTEzM0lSaVNocWtLdTU4d2ZlZmN3azh1MkVBLzMyaElzdE55Z2owckd2WWtmbUtDZnQzU2JhN0J3MXVsSStqQ0k4cHBUYXk2azdwdzlaRTVCai83S3pDM004VU5YMmRxaDJPeWJiWUtxZ0ZwNGVFZDlSdTR5enUvZ0RtRjB3VmUwbFdJZUpJNDRuY2J3NkNuWEE1VkE3WG5qNk9rWHVHZmZhUnhCeE8wNTV0dHNpMVdwNDJsbG53M2tVbHpNaFN2YVNDMkFWUkVWRTJ4cnVTcUFMNzJpZWtqa2hUZlJyVWRiR21MZWliWlJJUDNJS3VMVUVOcHY5NnUwYktScWlVOUY3ek9IVmtrQjRyQlk5cVdjMzZMWXpLSU5ocnhvRjNLQUNxT1Zua0x2ZnZGaDNBYlF3SHFYc3J1Ynk3OTYyREVFcXZXaWxsb3ZVdHZFek8xNWp4WVpwTy9kZkJMekZ0Y3Nzdmg0MDlyVFhCYjZpT1NLRkJLRS9Jb25MaE9QMWsiLCJtYWMiOiJkZGZmODFjMjk4ZDJhNmM0N2Q0OTI2MmI4YWEwMmM3OGMxZjA1MGJiZDRiMzAxNDgyNzYzZWI1NGYwOTIxYzJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZIWGYvTTJTTVpjZEFudGcyWWFWcmc9PSIsInZhbHVlIjoiL1J4a1cxMll1SS9Td1M2ejZQWS9QU0FuZTl4Ti9ZaUNyRHZpbGZEN1QvaGVGTTJ0L0g2VGlFWWpKaWd2MVpWS1hzYVpNcjNiOXgxeEVBRWZ5M3piOEl0RTRWRjI3anJNa1MxSzBHTjRKYTRNelozVWJJWWdnWkxQdU5oMXo1R3gwMzN5Wm5DaXd1anEvZFBvNytxS3ZENnlvZlcwQm9xNE1KL01MbDlGWVJVVnJKckZYdW9QRUI4TjFqUmxOSmRqN3NTTy9RTllLZ3FFNjNpYW9qT3RzUXlpNHhVeWxCL1dzb1hFb3pUK04xZ2V3ZDFieGVSbnM0Y0NRbGxscVpwVGRKVUo5UEdBTXU2R3prS2I2WDZDRlFWdVB3T1U1a3BWWlpmWm9aYlM4Ly95Vy8yRnpiZEcva1NMbHVoUCs3cUsyajk5dEE2WDRYWUc2cytOb0hxdzBBdkNUejNaOXZLM2cvd3JsS0dPYTZWTmZXMDJLSXNQSHNyazdGQTdLakdEU0QxbThtdjlSMldzVGllQmFuNlp4RS9mSXZhWXc3Umd2Wmh0TitTb285Znh5enJHNEo2OWo2R0VOUENleUhiVXEyeGVBNGxUcXljMFgwcFFpdVkxR054aGVUVjZYTUQ0Qksxckhhd2pONDVJTTFpTHovczhqUm9LWUhHWXorK1QiLCJtYWMiOiJhMGJkZTJjYzkzYWIyM2ZhOTNmNDE1MTc0Y2JiOTQ2NjFhYjFiYTJhNjViNWQ3OTJjMmY1NDAzY2FmYTA1YjU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603288578\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-505760467 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:15:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktaRHhTajVFdEhDYitRQ2syZjJveXc9PSIsInZhbHVlIjoieVZCdmpSRXZDYUlwbHlrRXBzTGtvYlFIeWVtRlZsa2tqbGJHODFJWThIUDR4RUhHTDVVcnY0b2RDZ21jOStZM2gvOEdjYlZlbXp3QjJORW1XbWE0MDVMR09CTVBYdm12c1BQS29qSlFxN1ZXQ2lwOHRHVFgzUkdkZ0lUSDZScW8wWWtaYkFsNytoU1hUb0pqODJkOFp1VGFsU0p6YkdPU3kwZzVhdTRLYXorT2Uza1YyaHJBQWRIWElqWGlTdmU2VTNsdm9hdE1FYloxNW00UnlqRnZrRVlMMUZhQytXOTloWm1JMWlIMWlXMFBET3pqbnV2L2hsbk1aMjNNMTVRZXNjKzUyS2JYRC9UZ3picnIzOFJ4U2w2bmR6VXZkUGFHZUFxMFovSFB5SHhiWkdtVUdZWHlMa0JqbUhkM1Q1bDFOUkJHZCtuWStsckIrMVlndCtYRUEyNFJ3UXA5K2Y0K01zVzhOSFo0ekEwWlBVbmRmZ0xiZDVPQmlKYTM5Mng5YWdIenNZREZheDFibXlKNFA2UUZyTnlSOUhmOEdlT0F3WEZQb1pVYW9rS3JZbkxua3FodlltS1dZY0Q0TWFsU2YxUzcyZENnOWlZOTV5MmRCekJwbkhnWmx2cEc3Qzd4SjExODlWV3NncExRTzlpVG1ZUzZ5bWJ6L0NNUGRacmUiLCJtYWMiOiJlYTg5MDk5YjBkNzEyYmMwYWQ2OGQ0MjBjYTkwM2E5ZDhmNjM5ZThjMmFiZGMyYWRjOTUzYTRkM2I5YWM1MTFlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndlRkg3bUczMEMxQkJ4THNMTmNXZWc9PSIsInZhbHVlIjoieVR1WDNFNWp3OEVBb0RkRXNnWmxkN0p6MEpSRWQ1NzVaTHBlcHJaRXB4WUhiSTc1c0U0N1FiZU04UjVHaGxESjcwcGRrYWJPUlpiK1A3Q2ZGeFBKNS9EUGxWV0lKSjgzMFdYQWs5QmpNRjQva2FmdXRORWJ2UGhGRDBzRThoYzRWMlAvM2FIVWc1V1ArWmhvZGFWUDAxanZoUDE4Q1l3ZHgwM08xdnF3bG9QUXIvaTBTOWxLMHlYdEdyV3BKTzBwSnhXY1pSVm84MGJ1ZEtEUTc3bVM4a3paU29uelRRTkJGc1oyTXEzS0QyUHp6Z1h3NUxLaVRZSXpyRStTWnhoMEh4UlRFZ0JkdVpMMjRTNXo5UE00U2VUeEZnU09OQmZndG9JdVh2bzBGUXgrZlVBTlo5ZWp2WVN6YlgyYmFRVk41bkFVUGdEdFlQZnlPRUgyWWVFNkFjWnF6dDhCUU9NS1J5RUlSUkdoOGNwV0gyTjJwak0vQmJBUGNUMFRHUHBWWmF1bVRMd2ZxbUFaUlZyV2tTKzJ6S0RGZmpYL1pwWVNtbCtGVDJFYkM4WEgzV2NMQUtuT2ZHVWQ3Vm1adGhvV29GcE9nanNuaUZqN284VFE4YURwNE9Za0NlNHlLOC96bmVLOUtPVDhTVEFRZUljNUd2ckx3dE5kbEJhZGdkTG4iLCJtYWMiOiI0MzA3N2Y2ODY5ZTFhNzBiNWJkODViOGI1Mjg4ZGU5ZDJmYmIwNmQwNDdmYzM0Y2VlNDgyM2YzOGNlOGUxYmY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktaRHhTajVFdEhDYitRQ2syZjJveXc9PSIsInZhbHVlIjoieVZCdmpSRXZDYUlwbHlrRXBzTGtvYlFIeWVtRlZsa2tqbGJHODFJWThIUDR4RUhHTDVVcnY0b2RDZ21jOStZM2gvOEdjYlZlbXp3QjJORW1XbWE0MDVMR09CTVBYdm12c1BQS29qSlFxN1ZXQ2lwOHRHVFgzUkdkZ0lUSDZScW8wWWtaYkFsNytoU1hUb0pqODJkOFp1VGFsU0p6YkdPU3kwZzVhdTRLYXorT2Uza1YyaHJBQWRIWElqWGlTdmU2VTNsdm9hdE1FYloxNW00UnlqRnZrRVlMMUZhQytXOTloWm1JMWlIMWlXMFBET3pqbnV2L2hsbk1aMjNNMTVRZXNjKzUyS2JYRC9UZ3picnIzOFJ4U2w2bmR6VXZkUGFHZUFxMFovSFB5SHhiWkdtVUdZWHlMa0JqbUhkM1Q1bDFOUkJHZCtuWStsckIrMVlndCtYRUEyNFJ3UXA5K2Y0K01zVzhOSFo0ekEwWlBVbmRmZ0xiZDVPQmlKYTM5Mng5YWdIenNZREZheDFibXlKNFA2UUZyTnlSOUhmOEdlT0F3WEZQb1pVYW9rS3JZbkxua3FodlltS1dZY0Q0TWFsU2YxUzcyZENnOWlZOTV5MmRCekJwbkhnWmx2cEc3Qzd4SjExODlWV3NncExRTzlpVG1ZUzZ5bWJ6L0NNUGRacmUiLCJtYWMiOiJlYTg5MDk5YjBkNzEyYmMwYWQ2OGQ0MjBjYTkwM2E5ZDhmNjM5ZThjMmFiZGMyYWRjOTUzYTRkM2I5YWM1MTFlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndlRkg3bUczMEMxQkJ4THNMTmNXZWc9PSIsInZhbHVlIjoieVR1WDNFNWp3OEVBb0RkRXNnWmxkN0p6MEpSRWQ1NzVaTHBlcHJaRXB4WUhiSTc1c0U0N1FiZU04UjVHaGxESjcwcGRrYWJPUlpiK1A3Q2ZGeFBKNS9EUGxWV0lKSjgzMFdYQWs5QmpNRjQva2FmdXRORWJ2UGhGRDBzRThoYzRWMlAvM2FIVWc1V1ArWmhvZGFWUDAxanZoUDE4Q1l3ZHgwM08xdnF3bG9QUXIvaTBTOWxLMHlYdEdyV3BKTzBwSnhXY1pSVm84MGJ1ZEtEUTc3bVM4a3paU29uelRRTkJGc1oyTXEzS0QyUHp6Z1h3NUxLaVRZSXpyRStTWnhoMEh4UlRFZ0JkdVpMMjRTNXo5UE00U2VUeEZnU09OQmZndG9JdVh2bzBGUXgrZlVBTlo5ZWp2WVN6YlgyYmFRVk41bkFVUGdEdFlQZnlPRUgyWWVFNkFjWnF6dDhCUU9NS1J5RUlSUkdoOGNwV0gyTjJwak0vQmJBUGNUMFRHUHBWWmF1bVRMd2ZxbUFaUlZyV2tTKzJ6S0RGZmpYL1pwWVNtbCtGVDJFYkM4WEgzV2NMQUtuT2ZHVWQ3Vm1adGhvV29GcE9nanNuaUZqN284VFE4YURwNE9Za0NlNHlLOC96bmVLOUtPVDhTVEFRZUljNUd2ckx3dE5kbEJhZGdkTG4iLCJtYWMiOiI0MzA3N2Y2ODY5ZTFhNzBiNWJkODViOGI1Mjg4ZGU5ZDJmYmIwNmQwNDdmYzM0Y2VlNDgyM2YzOGNlOGUxYmY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505760467\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">http://localhost/barcode/pos/html?only_available=1&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}