{"__meta": {"id": "X4106e85004018d45bd0fd4010499c71a", "datetime": "2025-06-08 13:07:09", "utime": 1749388029.048462, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388027.738381, "end": 1749388029.048492, "duration": 1.3101110458374023, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749388027.738381, "relative_start": 0, "end": **********.883236, "relative_end": **********.883236, "duration": 1.14485502243042, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.883258, "relative_start": 1.1448771953582764, "end": 1749388029.048495, "relative_end": 3.0994415283203125e-06, "duration": 0.1652369499206543, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45579520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006919999999999999, "accumulated_duration_str": "6.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.974655, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.092}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749388029.0025399, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.092, "width_percent": 17.919}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749388029.0215108, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 76.012, "width_percent": 23.988}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1554461767 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1554461767\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-28079395 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28079395\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1174477424 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388021952%7C13%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5vcm1lWW05OTdrdGZpbXhPM3ZzRVE9PSIsInZhbHVlIjoidk5SZjU4cnFRb2FiRHdXK3B1ejFhWTlQWFdDaEFmUzdycHl1MFdpYXFxN296VlRZMDhBSVJkSnloS0xJNUZRMGh3dkd3eGxrM1Q2NDVMY3VoMk1LeExWbzl5MTRTTkY1dFRmZG9TeDMzTWM0TndTRGtyMlREdDd3RytQbUtFNDlPaWp3TjcvT1JOMVF3L0hOQzM3UWgrUGI0YUZhRFRuNVlKTjRKSG8vRmNtL2lzOE01aGlWRk5maUxZVXMxM2drOUJOcklQWGJ1NkdXdTB3Wlo5Tm9aREZlcEhYaVZmbG9jMFhIZ0ZHV1ZKUnRSdTVsOXdPWjdCTjJ2SmxsWXNGT1ZjVTRPZTEvR2VxZjZaM0dEL2V4SmlBUXpJVGVMZU5XTEdSbFRvOU5TWnZ6VUhBMFVMQUk3eFFQNjFKKzMydmQyYlBWWmgyc0hDMmpFYUVhU3JEYUQydjQ0M0xwaDNEcUg0Zldab2ROeHN3VmxMZTUrUXdzYUtSOCt2S2ZKNTBiclZPakZ4TTI4Z1RlejVkVC8zbFN5d1p3dTVxanA5aGNIeVVpc1pNRXlMeVBvQTlvVlo5OThCcjI1dytzb2pZK1hpL0swcjRpYy9QbVUyZjlqVzZrajA0OUpYT094aG1qakI5aE0yV1BwRkFrZ1lIMWxWUkoxdzJuZUNFR29Fc08iLCJtYWMiOiI0MmM2Yzk1ZTI5ZjdmNjhjMDBlOGZhZTBhM2RiODQwYzdhNmQ5MjIwZWVlMTQ0NmI0ZWYwYzIxZTNlMzBkZmFkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFGSk4yaUZmS0I4NTQwWUNySEZ4Mmc9PSIsInZhbHVlIjoiUTVwaFcvK2tMRzN3Y2NvQmlrWHp1WjlMc1FDcjZaOFlhbjVETkhMb2RPb3BGRDEybGo2VEJ4L3ZqQ0lQcmthMTQrRlhFUXcyNEF4bDV2MDB5bXNLdjM4MEJ2TUdxZkVWblNINFRvOURQM25oRktMcVJXRXdyVzEyZDFaTURvSVhzaVhnYmpObzQ4blJ4NEwxZWtKcFhubnBlZC9Fd29VOUFzTUh2akwwS0Vpa2xuTHFPcytEeXNadnFaS3M3Sndia1hXbzJzS2F6RkdsVVNkMldYTVd6RGpiWnJwR2N4bms1YXBJYzAxTlNDMlk2a1pPY1dTRzRLdzlLdGdYQmVtRndsSUlmNHhSUk15L2NCU3g2ZTJKQ2VKR3lwanVMRWZvTGdPZmVIV3JwU2RYMnJMcCt4Smh2UDgrTXVWQmFZek8ybVd2VWs2RTRObGQ3RUxCS2Zma0dSY3VPY0g2VGpIcTZPTVJRbXN2Q3FQS0NOQ1JpV252WjFuUDQwNVZJN0RwTnB4a09rRENlWThwU0RDUStwK1hDRlVpMFFQcHJ4T0dZeGt2MFBXM2RXR0M0NWdsNUR0cG9HaFhlay9GUnpVWUJzL0lSR1FWbnY5VzhKYTcyWHd2YU5raENvWWpPR0ZiT1JZeWlaN3FBbWpQQkE3YmZpdDVDZmI1NWlQOXhPaUsiLCJtYWMiOiIzYjA2NWIyOTU0OWMyNjFlNTNkMTQ5ZjRiMDE4YzMzNTEzZTM0MDY4ZDQzMzNlYzgxNWMwYTI1NGUzNjg4YjAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174477424\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-55453347 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55453347\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-85579129 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:07:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRNRkhqQmU4RTc3NGRNYm9EUDFJV3c9PSIsInZhbHVlIjoiY1dZa0JKYkUxd0hxN1ErNkNxZmJPVk1IaEdLNllPUTZSVWE2bE9YYUZFOVN2NklXaWhScElIZ095L29rYTBLZytNNXJtV0MxckE2R1AxdFJ5ejlJNUJJeFRZeUZvTFVIQlhKY1A0ejVaSW4xQTU2OExNQVZoYy9NRVAxUFhLSm1iZHBaN2ZqTkdobVJvTUpVNTRRaHFqcHlpYzQrNytRU0xiTnhKVkkzOXVQQlMzakJOMUEzRW9kQlo5cE9Cdm9KdTkxZlBPQ2xJbkh5SVkwUzlGaXJxblUxSDNCcS9WWDJyeU5TRlc3a0w1a0hoNHdnYkxGOTQvSkJSSVFhMXRHN0VWclg5aklpVDdaYmY2b0JKN044QUFEbWx2TkhqYlp3NE90ZCtOelZSdzFMVy9tY2Q1cU12YVB2Ujk3NGNDRUxvN0hHZ0ZPblIzZkZBeWtyWVZzK1Y5NEg0R2FsNlFGeXUrRVhyOG1LeDVJOUFCRVBSWUZPTlBLNGJOUnpFREN1VHo3azZWMUdOKzgrY2duc2NaaVpVUExla2N4OEVON1lIeEV2TDNZa2Y5OWdBYlFwQ3p0UVRZaUlMZVdlaDllK1FmTlVacFArSUlkcTVBOFhORjU5VVhkdy9YSTk1NHlSM3dPQ1hLVUI3YTdFT0JZV3A1c2hKN2oxSUhmMXRWQWoiLCJtYWMiOiIyZWMyOWYxMTViYzRmZGI2NjEwODdjMzlmNWRjOGIzOTJiZmI0OTM4NzcwYjJlYWE5NGYwNDk1YWNjMGEzMjMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImcvbDdHeGJId0pPazFPejBNTE1wcHc9PSIsInZhbHVlIjoibE9Sb2lrdkIwNjcxbzNhOFQyYmg2cnFHZ00yK2NwWXUyL1ZvQ1NoNG8rUUVUblFSdlRTRGpMWGpUMi9Ea2JrdEdRRFJjYUlYLzJHdFdCOVRLYlNBOEpCSk9xREI0SWJXYThiOXpEazJ1SFdsSXQzby9kTTFkNzNRa0NsKzRGWFdhUEEzZ3lYUEd1SzFOVUt1eXd2TnAwUTdwdmlTaFBudWYzallONVAxeEZHV3A3QWp6KytHalNUQU1DMW81U1dGRDlFRHBLa3VCemRNY1BaZFVmd21OdnVGRjFDV2wvUC81QXUyYU1YNVdoMXRzRFlNTHNyT0xhRmtEQnNPQWkzTlZWalpjYUFCNjVyVjVBNlUxSi9NTHNtM2UxRm9GdkgwUWs5Z2xxb0lLc1NRNkFpaDNhdSs2YTZmLzRXWVEvLy9Pa3EycmZGYTBvbDFEeVVNZmZ1Y3hFUDU2N0NBZXp2NTZ0VS9tQ0JFMXNBZ0c5SWRlazkxdjNuMCt4YjBDazhVVlJyT0p5WWdmVjVXUk9yK3Q4NkJrVVMwemF6WGV4c3lEMzZ2dUVJWjlTeGI3bW1FRGV6d3E5Z0lmSmhqQkQxNkNHRkFKMGlNUWVSNkw5MWRrRHJQY3o3NW9SNHNEWWhoRTZNU0dCbk1PamRPN0ltd0xzOWtubjQrTUlkbTlJOHQiLCJtYWMiOiI4MzZjYzc2ZmU0Zjg5ZmU5MGRlYTRjZTg3ZmNiZmI5MGJiZWZkYTAzMmQxNmVkZGI3MzBkY2E1MmViOTMyMzBkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:07:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRNRkhqQmU4RTc3NGRNYm9EUDFJV3c9PSIsInZhbHVlIjoiY1dZa0JKYkUxd0hxN1ErNkNxZmJPVk1IaEdLNllPUTZSVWE2bE9YYUZFOVN2NklXaWhScElIZ095L29rYTBLZytNNXJtV0MxckE2R1AxdFJ5ejlJNUJJeFRZeUZvTFVIQlhKY1A0ejVaSW4xQTU2OExNQVZoYy9NRVAxUFhLSm1iZHBaN2ZqTkdobVJvTUpVNTRRaHFqcHlpYzQrNytRU0xiTnhKVkkzOXVQQlMzakJOMUEzRW9kQlo5cE9Cdm9KdTkxZlBPQ2xJbkh5SVkwUzlGaXJxblUxSDNCcS9WWDJyeU5TRlc3a0w1a0hoNHdnYkxGOTQvSkJSSVFhMXRHN0VWclg5aklpVDdaYmY2b0JKN044QUFEbWx2TkhqYlp3NE90ZCtOelZSdzFMVy9tY2Q1cU12YVB2Ujk3NGNDRUxvN0hHZ0ZPblIzZkZBeWtyWVZzK1Y5NEg0R2FsNlFGeXUrRVhyOG1LeDVJOUFCRVBSWUZPTlBLNGJOUnpFREN1VHo3azZWMUdOKzgrY2duc2NaaVpVUExla2N4OEVON1lIeEV2TDNZa2Y5OWdBYlFwQ3p0UVRZaUlMZVdlaDllK1FmTlVacFArSUlkcTVBOFhORjU5VVhkdy9YSTk1NHlSM3dPQ1hLVUI3YTdFT0JZV3A1c2hKN2oxSUhmMXRWQWoiLCJtYWMiOiIyZWMyOWYxMTViYzRmZGI2NjEwODdjMzlmNWRjOGIzOTJiZmI0OTM4NzcwYjJlYWE5NGYwNDk1YWNjMGEzMjMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImcvbDdHeGJId0pPazFPejBNTE1wcHc9PSIsInZhbHVlIjoibE9Sb2lrdkIwNjcxbzNhOFQyYmg2cnFHZ00yK2NwWXUyL1ZvQ1NoNG8rUUVUblFSdlRTRGpMWGpUMi9Ea2JrdEdRRFJjYUlYLzJHdFdCOVRLYlNBOEpCSk9xREI0SWJXYThiOXpEazJ1SFdsSXQzby9kTTFkNzNRa0NsKzRGWFdhUEEzZ3lYUEd1SzFOVUt1eXd2TnAwUTdwdmlTaFBudWYzallONVAxeEZHV3A3QWp6KytHalNUQU1DMW81U1dGRDlFRHBLa3VCemRNY1BaZFVmd21OdnVGRjFDV2wvUC81QXUyYU1YNVdoMXRzRFlNTHNyT0xhRmtEQnNPQWkzTlZWalpjYUFCNjVyVjVBNlUxSi9NTHNtM2UxRm9GdkgwUWs5Z2xxb0lLc1NRNkFpaDNhdSs2YTZmLzRXWVEvLy9Pa3EycmZGYTBvbDFEeVVNZmZ1Y3hFUDU2N0NBZXp2NTZ0VS9tQ0JFMXNBZ0c5SWRlazkxdjNuMCt4YjBDazhVVlJyT0p5WWdmVjVXUk9yK3Q4NkJrVVMwemF6WGV4c3lEMzZ2dUVJWjlTeGI3bW1FRGV6d3E5Z0lmSmhqQkQxNkNHRkFKMGlNUWVSNkw5MWRrRHJQY3o3NW9SNHNEWWhoRTZNU0dCbk1PamRPN0ltd0xzOWtubjQrTUlkbTlJOHQiLCJtYWMiOiI4MzZjYzc2ZmU0Zjg5ZmU5MGRlYTRjZTg3ZmNiZmI5MGJiZWZkYTAzMmQxNmVkZGI3MzBkY2E1MmViOTMyMzBkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:07:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85579129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-81148980 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81148980\", {\"maxDepth\":0})</script>\n"}}