{"__meta": {"id": "X15dfa6d0e99f94bd8c457e09bc9e91a2", "datetime": "2025-06-08 12:59:47", "utime": 1749387587.020413, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387585.735106, "end": 1749387587.020448, "duration": 1.2853419780731201, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749387585.735106, "relative_start": 0, "end": **********.842815, "relative_end": **********.842815, "duration": 1.1077089309692383, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.842837, "relative_start": 1.1077311038970947, "end": 1749387587.020452, "relative_end": 4.0531158447265625e-06, "duration": 0.17761492729187012, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45595056, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02135, "accumulated_duration_str": "21.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.928066, "duration": 0.01907, "duration_str": "19.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.321}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.97498, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.321, "width_percent": 5.527}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9933999, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.848, "width_percent": 5.152}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IlVsRDVlOW9jdDlzZ2gyZUQ0UWRCeEE9PSIsInZhbHVlIjoiRmJYVUdvNDNPM1lkZ1hUNllSU2wwZz09IiwibWFjIjoiYjhjMmM1MDcwODk4MDFkNmUzOGE4MzljZGU4MDcxOGIyMmVlYzJhNDJmOWYxNzU3YjRmYjlkOGFkMDYzYzBkMyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1271657521 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1271657521\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1105715762 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1105715762\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-317262875 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317262875\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1927440565 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlVsRDVlOW9jdDlzZ2gyZUQ0UWRCeEE9PSIsInZhbHVlIjoiRmJYVUdvNDNPM1lkZ1hUNllSU2wwZz09IiwibWFjIjoiYjhjMmM1MDcwODk4MDFkNmUzOGE4MzljZGU4MDcxOGIyMmVlYzJhNDJmOWYxNzU3YjRmYjlkOGFkMDYzYzBkMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387496852%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVCUmg0QmdZY2FIUmpucElLUXF3dVE9PSIsInZhbHVlIjoiazM1NHVOOGpWbFBkUU1YM1lvTUx6RFZQdjl4RTlzcVBuS0NCT2dyVkhRKzQzTC9qNnNaVzZlZmtodmYzSlNWVGVxN0xCRFV6OWJUSjZyK09PdmVhejNDQlk4Y2k3aWRxTzdoeTR2Um1sdWZxd1I4V1RNV2Raajc2dVV0b3M3aURwRFBvZVNsU3hYcnp0YWtiNlhXU2IwYzFUL0dRc2xqMUtnR3VKU3ZaSW9yYlZpekNwdGFlKzVjQXNvejdPM2x6YkR0cEFueWNGUlJJeUNPMi9OaTdjbUdWeW1BTmttY2lTSTVsMjhmZHp0NTJUcW81ZEJSODB0R1NNcGVhNG0rdm9nMUhrRGR6TDdvZVNFNllBRXhLdzk0VXp3RkZwMWx3dVRkQ0J5d2Z6SzVtTHBrb20ydWhZNHdZS3hTMGYwQy94eDF6K0hjdVdFOTVkZUFWSXUyUWVUZjdIdGszQ2pXdHlrZWlLdE16T3pOWEJaNGZtcTgxSWxBeGxrUzg3TGlpM1ZzTG52ZnY5bG5vSjROd0FQRDQxaEVjQmNtczNrMkdaUXc4MXhCVm1zWGo2TXdqQmxTNlhpcVp4VytLakFRTUZNZU9IanQrOWJrOEk4ejF0M1hmN29JVlpBQUtjc2RGL01WK01ZcEN0amdwMTJJVGxRbnRFVS83OHVZSUlFTWgiLCJtYWMiOiI4MDUzZGFkYmQ1MzExYWM0NmNmMzk0MmQzNDE3Y2Y4Mzk5ZDFlNDkzMTlhYzA5ZjJkYWEwMGFiNjcxODVhNDdjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBlRWxKb01YQ3lISVRlc3FUS0g4OHc9PSIsInZhbHVlIjoiUXo5ZTI0WTZwekNCUDFpQWRDek5iUlU1V0Q3VStZaWFiK0hiZTkrVGtZR1R4WTRGZ0hQNk9qb3NlV21wbjJvQkY2ZzZ2dFVablRmaFlqUHlUZmR4ejM1d05lMXVyQkhJalkxZWxZM2lVTU5VeXM1QmxlaW5MRWlqbzJmK01jUG1NVGI3aE5GWnA4aEs5WVJaUmhDdTVEQ3dFaTQ2U0FROFdWK1lIU1hDOW9Ha1F4N2RqSGNPV1NpWmVCNmkxbEF2Y0JyeE5yaFBHaTkzOG1FcnNuN3BDeFV6WDNTdHE0Mm9DZGQ2eEtESzB1cmFhTGt1Z1d1NDIxcHFmUlpKR3VPQkl2U2VlaURtS0MraFEreTBrYWNvRmI1c1pFS2l4OGV2MHNkalp3THU4ZUhOb1FsU3pLd0RjRGVIMTl2aXZDWThWeTBqWnliTkw5OENucnFMbytIVzFpczVNRXd6TTROZWdWSHdWMXgxa0s1VFdJM0x6NGVEb1VvQS9nNGVKQWlPcGVDVW5LKy9reXNtd0NJd3RTcHdPQko1MUxvdlN1dHR3UFhNbDUySEF5N0xrd1FsYVVXRHAzQ2ZNUkdQcHJpK2R4RXc5WWdCK2lGZHFqUkswZFlqb3Zjb01UeTZWQXlvUDVXcm5QTW5zemQ0ZDFhaG11Sk1OQkprTDhiTGlPZlQiLCJtYWMiOiJmZjNjNmM2ODFlNDU1YmVhY2QzYTU0ODhkYTg2ZjNmMDliMzJhZjNkYTRlMThiODIwYzVkNzgxOWExYjg3N2EyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927440565\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1225486187 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225486187\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-818623914 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:59:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlovNFc1VFI2Q1huVGdyN2NJQUtDTUE9PSIsInZhbHVlIjoiWEV3SUJkci9JbWg3cXNmMStMUm43TnBuU0lSY2Y4Uld6Wjg5NmV0cCtma2l3WEtxTkNwcjNiMkoyVVFpZEN4cmpmaGI1MHpNN2g2WG1MbzIwa2VIVWxTdHFlTG5xQllEYVpFb0R6KzlqZ0VIOFZwWTFFa0M0Tm5VNTNrcGtkTWQwa052bUs4VkNjVEJUVFlsTUdtbDU5bllQSVJ3NHRJWkRxNG55MVpwUnQ1SWlaUWovdFNNSktLQ3lJeVBPd1ZqeWJhL1drUGprK1ZndFRSK1RRelUwaDd5d003VmF0ZzlJc2xjanB5ZzgzT3RQN092UzdhNE1PckNCbml5V21rc2tuSFhldjNVLzliUlZtZjdySTBBV1RhWW11S1NYSjZRVElCSXdZYTRDMGpRTDJxTTZBZTNOcGhXQ3ozQmZOZllmNWs2RXpIL1VTZkhFRnZreDBQQXlneTMrSHVuQnhkQnNBNDF4Tmk3d3VuVXNqUkQyc1ljR1FGQUFtWWZPSTBoMzVpS0RmT1gxUmhMNllvZEw1RW1IYWFCRVBqcUp0OTRKdjVqYkRRYnh5cTk0WllCeEZhQmd5VjdCdWMxVDdSeGpYVFRTeFQyaDlydjlqRXhteDdTWmxqeFBIR0dHbWVMVHFTaS9wVHpwK2MrUXpqOW16aFVGQTdLU01UV29NcHAiLCJtYWMiOiIwMmI0NWJkMjBiNTg4YTRjYzY0MDRkOTg4Yjk4Y2YxNzBlNTczYzI3MDU4NTAyMjFkNWU4MzgwOGU1YmI5ZWUxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:59:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjUxTEo3c25IalVMWkdBWExCQXphWEE9PSIsInZhbHVlIjoiK1htOHp1NGRmN25RZ1FWNjF3dWp5Wm0xd3dtdkpOeXhLcFVkL2toRGc4Vk5jbzI0Y3o4QTJCYTJpS012aldVRGNlcW43VVV5V1N2Slh6OXVnSVEyb0J1R1ZiL2Z3STgzbWZJNm9scWpxYlhQNkR3OHBlMzgyMFlJT0FmM3AvVHo4cDIwcGNaSDVGd0RiOVBtTHF5enZHMHpGNEtKc0NzenV0YVN0S293NzZQdmI3dnRQeFlkMDVVWUdwTGtyWEthSGRyODVhR3N4d09CVFN3amc4eG10WHRFQ1AxdFlZUjVuMkFQTDliUlkvY1pHeWdEV2ttK1liQk13QVhYRyt3UzBzRnU4L2hoMVg5VnVvemJmZGQzcEpZNmxZTzZqNGdTYUFzekc2WWlDcmtIYnRPZ2o2L0NIR0xzRDVNZWs2ZC9QSFBnQ3hITGNpYVFTZk5kZW1lYUtxek50WWJuUWRLTGE3WjJlWmRaVGZsejdKZHZTM2JWK2cyc2Y4VHRxclhaNVVzVjFwaENvU1R5N0orMjQyYTBkSFlTWlY5bVprUDFCSlpJQ2xETVA1Qlc1VjVsRWszalVST2RMb00xQ21IdU5jVDlyQ3h5UU14T3hSdXBjMDhmT1dodjJhZWIvVFRkRVBwZ1dEZzZBa3doVDdUNXpUaWVUaFQ3T1ZTc2xHY1MiLCJtYWMiOiI3NzYwMjVjNjg5ZmI2NmM4MTlmOGU1MDU3YjI5ZjE3MjU1NzAxZWJlOTg2OTM3OTBlZmQxMWUyZTA3YmZkYjRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:59:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlovNFc1VFI2Q1huVGdyN2NJQUtDTUE9PSIsInZhbHVlIjoiWEV3SUJkci9JbWg3cXNmMStMUm43TnBuU0lSY2Y4Uld6Wjg5NmV0cCtma2l3WEtxTkNwcjNiMkoyVVFpZEN4cmpmaGI1MHpNN2g2WG1MbzIwa2VIVWxTdHFlTG5xQllEYVpFb0R6KzlqZ0VIOFZwWTFFa0M0Tm5VNTNrcGtkTWQwa052bUs4VkNjVEJUVFlsTUdtbDU5bllQSVJ3NHRJWkRxNG55MVpwUnQ1SWlaUWovdFNNSktLQ3lJeVBPd1ZqeWJhL1drUGprK1ZndFRSK1RRelUwaDd5d003VmF0ZzlJc2xjanB5ZzgzT3RQN092UzdhNE1PckNCbml5V21rc2tuSFhldjNVLzliUlZtZjdySTBBV1RhWW11S1NYSjZRVElCSXdZYTRDMGpRTDJxTTZBZTNOcGhXQ3ozQmZOZllmNWs2RXpIL1VTZkhFRnZreDBQQXlneTMrSHVuQnhkQnNBNDF4Tmk3d3VuVXNqUkQyc1ljR1FGQUFtWWZPSTBoMzVpS0RmT1gxUmhMNllvZEw1RW1IYWFCRVBqcUp0OTRKdjVqYkRRYnh5cTk0WllCeEZhQmd5VjdCdWMxVDdSeGpYVFRTeFQyaDlydjlqRXhteDdTWmxqeFBIR0dHbWVMVHFTaS9wVHpwK2MrUXpqOW16aFVGQTdLU01UV29NcHAiLCJtYWMiOiIwMmI0NWJkMjBiNTg4YTRjYzY0MDRkOTg4Yjk4Y2YxNzBlNTczYzI3MDU4NTAyMjFkNWU4MzgwOGU1YmI5ZWUxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:59:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjUxTEo3c25IalVMWkdBWExCQXphWEE9PSIsInZhbHVlIjoiK1htOHp1NGRmN25RZ1FWNjF3dWp5Wm0xd3dtdkpOeXhLcFVkL2toRGc4Vk5jbzI0Y3o4QTJCYTJpS012aldVRGNlcW43VVV5V1N2Slh6OXVnSVEyb0J1R1ZiL2Z3STgzbWZJNm9scWpxYlhQNkR3OHBlMzgyMFlJT0FmM3AvVHo4cDIwcGNaSDVGd0RiOVBtTHF5enZHMHpGNEtKc0NzenV0YVN0S293NzZQdmI3dnRQeFlkMDVVWUdwTGtyWEthSGRyODVhR3N4d09CVFN3amc4eG10WHRFQ1AxdFlZUjVuMkFQTDliUlkvY1pHeWdEV2ttK1liQk13QVhYRyt3UzBzRnU4L2hoMVg5VnVvemJmZGQzcEpZNmxZTzZqNGdTYUFzekc2WWlDcmtIYnRPZ2o2L0NIR0xzRDVNZWs2ZC9QSFBnQ3hITGNpYVFTZk5kZW1lYUtxek50WWJuUWRLTGE3WjJlWmRaVGZsejdKZHZTM2JWK2cyc2Y4VHRxclhaNVVzVjFwaENvU1R5N0orMjQyYTBkSFlTWlY5bVprUDFCSlpJQ2xETVA1Qlc1VjVsRWszalVST2RMb00xQ21IdU5jVDlyQ3h5UU14T3hSdXBjMDhmT1dodjJhZWIvVFRkRVBwZ1dEZzZBa3doVDdUNXpUaWVUaFQ3T1ZTc2xHY1MiLCJtYWMiOiI3NzYwMjVjNjg5ZmI2NmM4MTlmOGU1MDU3YjI5ZjE3MjU1NzAxZWJlOTg2OTM3OTBlZmQxMWUyZTA3YmZkYjRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:59:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818623914\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1717993821 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IlVsRDVlOW9jdDlzZ2gyZUQ0UWRCeEE9PSIsInZhbHVlIjoiRmJYVUdvNDNPM1lkZ1hUNllSU2wwZz09IiwibWFjIjoiYjhjMmM1MDcwODk4MDFkNmUzOGE4MzljZGU4MDcxOGIyMmVlYzJhNDJmOWYxNzU3YjRmYjlkOGFkMDYzYzBkMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1717993821\", {\"maxDepth\":0})</script>\n"}}