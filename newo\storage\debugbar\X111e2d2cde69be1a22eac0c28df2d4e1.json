{"__meta": {"id": "X111e2d2cde69be1a22eac0c28df2d4e1", "datetime": "2025-06-08 13:02:35", "utime": **********.487216, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387754.098141, "end": **********.487264, "duration": 1.3891229629516602, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1749387754.098141, "relative_start": 0, "end": **********.296468, "relative_end": **********.296468, "duration": 1.1983270645141602, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.296487, "relative_start": 1.1983461380004883, "end": **********.487269, "relative_end": 5.0067901611328125e-06, "duration": 0.190781831741333, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45577432, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025009999999999998, "accumulated_duration_str": "25.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.374989, "duration": 0.02231, "duration_str": "22.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.204}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.436548, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.204, "width_percent": 4.838}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.460212, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.042, "width_percent": 5.958}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1906669361 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1906669361\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1143998641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1143998641\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1021114825 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021114825\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1112853384 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387735347%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9Xb2RKV2pDN2JKVVNycVlVSVhlQmc9PSIsInZhbHVlIjoiOStXU0JoTno3aXZ1TjNES2RMcDdqRlJCUHVic2V1YlVMY2FmSzVNa0ErK2NFaHFVUHp2dnR1cFROamFTVEQzRVQ2WlRpMHhBcWhaY0JRblVHWldDbTdVNHFmYnhHaVY2MXZORXlxK2xPVXhVUVhuUTJRSUpIMHR1dkFQc3cyOU13cTEwNFlUcEF2YjRoOHBwalVhT1hRVUk1bTl0L1k5cjVBRU1kSXYxQVFzZ0dtZlVXRzNTS2M3V2Z3eWZMM0JZMXhVMUlDdFRSZE5ibkNha3c3emlkSU0ycGptUU8zRFc0dkU3d293S1JWbDNiSkFxOWY2dnB2WjlqT3J4SU1BcEYvdVFuQno3NnRLclgyY2pkSDIxZnNtbnN6S2lDRUVDUE9jQkg2N0tQMDBtZXhETDFRY0FaWDIrNE5RTzBoR0ZFOU1JK2pubGIvYVZEa0hYOEYySUNOTHZ5cWc4UmkzdU5QQnlIVndiRjM3eElNZWNqbHY5dTI3bjdKTDZlS2dyb3c4Q3B3Vytob2RjR3hjNFZNamgxKzEySDkxUmttRlc1eE5JTDROdHMyUU10ZC81MTBjQ3c4bUE2VG01NkZ3Wmp3NXAwY0JSSWljYm1LZnA5TDAyS09HbkRLa0VHNDRyREJSWnU2ZFlPWlBuM2lwbFRacWxBakxkSjdCRDlyMlkiLCJtYWMiOiIwM2YzYzlmODM0YWQwZGNlN2QyNGMwYTZiOTI2ZTNiNjNhOGEwNmEzM2QzNzUyMjRiNTc4ZWUxNjE5ZDNmMjk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkY3S2phUlAwV0ZCNVFVM3JKekFSUVE9PSIsInZhbHVlIjoicE1QZjFSaVJrNTFWZm10Z2xRRCt0WG40N2creHVBcVJiM0V1OWQ5bjdEWnVMMHpmMUlGUWZTZUlEcGZjTnNzeWszVVNiSXhtVXJPeTR1SHlIeDYydGpjVXkzNS9XMkZFaTQ2em9sMjJCWXlxTDVzWUMybDBlR0hlY05HMjR6T3AralNyT2ROU2RTdWRESUtGZUI3eTczTjdpRlJ3VGNNRERJOGVGalgzVFJ1eUNnRnNHMWtHYXJBeGlUZ0V3cnRoYXVWY2k5SnJaM2RGTWl0SzhnWUgzblM2R1lCZjRzQStNbnRPZThaaGZhc2dJcjdFUCtheVB1YnQzZjhKazc3ZTdzaVF4amkxM2V2Y2NIdVppTXB4bXhnSG00MngzcWJWSmw4VFovQ2JpL0dOb3lxZGxIa0hDN25XZFE0SlY3dDcxcjJqZkR5OUNXTThTM2dKUlo5Q1lDY2NMeDJDL0R5Mm5ybHNvU3pwSDdaZ1FOeEd1Ujc1TGdzVk5WdDdJQ2VJT2RVWlRNdDF1OE0yam5tWmFLWGMyWHhwdFBnaC9xU0VvcDBTZ3BrUnJHQTJHTGUyWHhYRmgxaUR0eUFzY1ExMVE4LzA3Z1o1azQ2akRKYWVFM0NzMzlicDNYd1pJUGdOUEEzam5ZVEVEakVkRVJhQTlrSU1zSGZrSlZMVlNyKzkiLCJtYWMiOiIzNTExYWY5MTY1NWQwNGIwMTZkNGJjMDIwMjZlMTI0MDJhZjE1Y2VhODYxYWEwYmYzOTk2NTMyZTZhODI5OTZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1112853384\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-139174853 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139174853\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1431143224 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:02:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldraDJiRmtGb0NORG9ra0VIdWZhSUE9PSIsInZhbHVlIjoiT1UvME1sSVFDWWJvZHZ3aFV2RGdVbmdpbk55cFNkalo3dkcrWHhlV3hJbDRCT2d1ZGdIazlJMVpCNUt3YlFXR1NGNENaNUpiS3E2YmwwSmtCSkxmZHJtSFlsWU00eWdxVEhvbm1hdVZMNHBRYXcvc0lrTjcydzFwQ2J4cFRJSmhNQ1VkOXE5Y2lxcFZEK1BIZEJZNlNFdmlta3g3ZXJ2a1VZM3N2UXFkTEhHaWdIUTNYWG82cUFQalB3OFVySUpHMVNCRThweDNZOU14bml3TTRBQjJTNFNRMGxFNXRIMU92RVg1djlTbE5lNHZlVmJMaUdMbC9ZOSs3VFE0ZksxWitQSjY2OVFtWjI4QkFNRXJodWk3cDVUb1lIRWtMTVhFV29CK3Z3d0dld2w4Z2Zvd1NTTmx2YW1oekZ6cFF3MGhNc3dkVk9VRFJLYUQyK2JXYXJxaFV2eHpxSkRISTRxOFo4ZEVhdWhya2FpN2h1N3hicHU4WDZqaUpwdlZWcXVlK05kYThJSDc1V3NEeGZYdVRSQk9uVjg2Z0Vybm90SllYOEZyTWp5c2RVT29JcHdwaU1QZ1M2LytpQktuRkE5WFhLTG0rUGVOMlk5QjlnRHBBYXZGUm5RZHRzTVB3M09Zajg2VFR2aEQydnhCSG44dnJwUkhPQXpCL3praTBoN1YiLCJtYWMiOiJlYmQxYzZiZDZkNzBiM2FlMTRkNTYwMmVjMzU2MTg2ZTFlNDQ0NTI2ZmI5MTg3M2Q0ZDUwNDhkYmJkN2FmOTlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im83eDlWRG5ySnk5Zi9sOSs4SndvSmc9PSIsInZhbHVlIjoiUVhRME1vRUJmd0NiOE83VWhCc25mV3o3NEJDQ2VXck9iWFN2UHB1R2pLOUd2TW1zZ1lLQ000TEFiK2JsVmhCaHBPMEN1dzh2KzB1MS8wSFNiRE5hd1dIMDZxYy9PZHV1WWtZSDl3dit2dVhhbERhdmxaL0lBOGtsT1M1aFVyM24xQ3pKRE1wV1E1STZJY1JWSHRCaTlOS3hsb0dCZ2tPUHR3VzF6QXVYQXcrOXQ4bnRzRDcyeFcxQVlTOGVHTnhtQ0hPQ3hNZEphMGJta1FIVEgyRVhWTUhEME5RdktLNDFiN1ZNam0xWm9tcGNkZVVmdVc5UmxCTnR3SkRERWQzT3haeVV1cFJ6VXk2V3BCMERVbWU1OFQ2dWF1NlNmWERmaDZ6bVFOYVVsc21GSXBFbXJ2QnUzUkxnSyt6azZzY2g2NFI5MjNSWVpKM083U0MrTzlzYU5Va3Bmand4OG8xc2dJQXhBTEJySm1wMXhkS1I5aDM0dnVsNmdick83SXloV050S3dzMHB5ZnJEZEhwcFU0YjQxaFBmK3BhZ0NrYUtKL2hSYTh2a1BSS3NkSEZTU1AxQVU5S1kyYWdFZ0hzVzlXa1VDV2pGbStWbjlWRVJUb0psdXpjam1UVkVsOXlGSnNOd3Z1OTNxdDZpVThUYy82a0E0V0RDc0tnWm5Tc08iLCJtYWMiOiJlM2FmNWFmZTQ2N2RlNzgwZjZhNmM0MzMzZjcwNWJlNjUwOTYyMWVjNDFiMDFhNjdiM2QyYWJlNWFjN2QwYmYyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldraDJiRmtGb0NORG9ra0VIdWZhSUE9PSIsInZhbHVlIjoiT1UvME1sSVFDWWJvZHZ3aFV2RGdVbmdpbk55cFNkalo3dkcrWHhlV3hJbDRCT2d1ZGdIazlJMVpCNUt3YlFXR1NGNENaNUpiS3E2YmwwSmtCSkxmZHJtSFlsWU00eWdxVEhvbm1hdVZMNHBRYXcvc0lrTjcydzFwQ2J4cFRJSmhNQ1VkOXE5Y2lxcFZEK1BIZEJZNlNFdmlta3g3ZXJ2a1VZM3N2UXFkTEhHaWdIUTNYWG82cUFQalB3OFVySUpHMVNCRThweDNZOU14bml3TTRBQjJTNFNRMGxFNXRIMU92RVg1djlTbE5lNHZlVmJMaUdMbC9ZOSs3VFE0ZksxWitQSjY2OVFtWjI4QkFNRXJodWk3cDVUb1lIRWtMTVhFV29CK3Z3d0dld2w4Z2Zvd1NTTmx2YW1oekZ6cFF3MGhNc3dkVk9VRFJLYUQyK2JXYXJxaFV2eHpxSkRISTRxOFo4ZEVhdWhya2FpN2h1N3hicHU4WDZqaUpwdlZWcXVlK05kYThJSDc1V3NEeGZYdVRSQk9uVjg2Z0Vybm90SllYOEZyTWp5c2RVT29JcHdwaU1QZ1M2LytpQktuRkE5WFhLTG0rUGVOMlk5QjlnRHBBYXZGUm5RZHRzTVB3M09Zajg2VFR2aEQydnhCSG44dnJwUkhPQXpCL3praTBoN1YiLCJtYWMiOiJlYmQxYzZiZDZkNzBiM2FlMTRkNTYwMmVjMzU2MTg2ZTFlNDQ0NTI2ZmI5MTg3M2Q0ZDUwNDhkYmJkN2FmOTlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im83eDlWRG5ySnk5Zi9sOSs4SndvSmc9PSIsInZhbHVlIjoiUVhRME1vRUJmd0NiOE83VWhCc25mV3o3NEJDQ2VXck9iWFN2UHB1R2pLOUd2TW1zZ1lLQ000TEFiK2JsVmhCaHBPMEN1dzh2KzB1MS8wSFNiRE5hd1dIMDZxYy9PZHV1WWtZSDl3dit2dVhhbERhdmxaL0lBOGtsT1M1aFVyM24xQ3pKRE1wV1E1STZJY1JWSHRCaTlOS3hsb0dCZ2tPUHR3VzF6QXVYQXcrOXQ4bnRzRDcyeFcxQVlTOGVHTnhtQ0hPQ3hNZEphMGJta1FIVEgyRVhWTUhEME5RdktLNDFiN1ZNam0xWm9tcGNkZVVmdVc5UmxCTnR3SkRERWQzT3haeVV1cFJ6VXk2V3BCMERVbWU1OFQ2dWF1NlNmWERmaDZ6bVFOYVVsc21GSXBFbXJ2QnUzUkxnSyt6azZzY2g2NFI5MjNSWVpKM083U0MrTzlzYU5Va3Bmand4OG8xc2dJQXhBTEJySm1wMXhkS1I5aDM0dnVsNmdick83SXloV050S3dzMHB5ZnJEZEhwcFU0YjQxaFBmK3BhZ0NrYUtKL2hSYTh2a1BSS3NkSEZTU1AxQVU5S1kyYWdFZ0hzVzlXa1VDV2pGbStWbjlWRVJUb0psdXpjam1UVkVsOXlGSnNOd3Z1OTNxdDZpVThUYy82a0E0V0RDc0tnWm5Tc08iLCJtYWMiOiJlM2FmNWFmZTQ2N2RlNzgwZjZhNmM0MzMzZjcwNWJlNjUwOTYyMWVjNDFiMDFhNjdiM2QyYWJlNWFjN2QwYmYyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431143224\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1224629155 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224629155\", {\"maxDepth\":0})</script>\n"}}