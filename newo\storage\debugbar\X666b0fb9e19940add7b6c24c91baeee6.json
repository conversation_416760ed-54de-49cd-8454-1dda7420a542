{"__meta": {"id": "X666b0fb9e19940add7b6c24c91baeee6", "datetime": "2025-06-08 13:19:55", "utime": **********.142872, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388793.687028, "end": **********.142909, "duration": 1.455881118774414, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749388793.687028, "relative_start": 0, "end": **********.826921, "relative_end": **********.826921, "duration": 1.1398930549621582, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.826939, "relative_start": 1.13991117477417, "end": **********.142913, "relative_end": 4.0531158447265625e-06, "duration": 0.31597399711608887, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53609376, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02666, "accumulated_duration_str": "26.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.927562, "duration": 0.01986, "duration_str": "19.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.494}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.982461, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.494, "width_percent": 5.739}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.056122, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 80.233, "width_percent": 6.002}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0637798, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.234, "width_percent": 4.126}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.079442, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 90.36, "width_percent": 4.726}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.094003, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 95.086, "width_percent": 4.914}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1419753102 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419753102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076876, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-1341338873 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1341338873\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1161767286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1161767286\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1146190820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1146190820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1389013986 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklnZXkvWi8wdm5IYnEvT0ZiazNsVGc9PSIsInZhbHVlIjoiNnVacTQ4elV3WEZqNmg4T2FhN2RBVlZkU25jRll6TnJUWDd6S0xOYXZjbENXTXlGVnJzVVlJaWVvQmlTK29EcTJIVUNNU3ZLWmxjT0FYbC9BYWlmTERJejhPTXhhOVhZMjdIYTlzS1BBcVgzNHlXUWEvODNhNkVCbUo0dk1HeWxqK25sUDlwZFMrbTdnUGF4K2d0VkZ3bXh1ZWYxcm5kK2I0allYWU9abVN6bjBaQ05VaUkxQXhtK3J2TUg2bUhlWXl0WnExa3QvV01kVW13UHd0M2dSZTdMaXdzNHJvTkZDOHdYblNXQVdRQUMza2xGUXZJb3pGbnVTd3JtK3dpT3NYVWkzamNKQkd2SE92MzEyckNqSklDeGFXVkcwdC92Nmp6VGxlMDVSMitQbDNoL2E4MUswOHE0bWVCblduZW0yWGNlMDJHRlFRUXp5akZBVEFYdldVN2FMVGluRk95Zlp5dUFFSG1mWHNkWCtLRE1xbWNyQ2JyangwTXQ4clQ3Z2Ivd3NZT1pISTRjSWtYamhzVllOSWRnMkg5ZVJEYWtOaythZ3ozcU15TlBaZXdZZTJVVEdOeEZycEhUMktXMlJaT3M1akxUdVZpQS9CcFdkNVR6Z1lHR3EvNXJUZHp6YlpndE41ZmhoVDZ2Nys1VlBBTXhJUU1LZkhWK054M0YiLCJtYWMiOiIxM2ZlYjNlMDQ4YWMxNDE5NTFiNGRhNGZhMThmM2I1ODM0NTUxZGMyNDMxNTk5OGIyZGI0M2IyYjYyYjkxYmZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZ2eVZpbjhYVVRaTjI1cTBPbHlXTEE9PSIsInZhbHVlIjoiUFFWdFdzNFlldTBxSEswUDhVYk5mUGNhTHMwY0lJL0FIU3JYMThEWEQ3czJFV1FVRklUL2J6TjJpaHRaL3cxbnZZUjlwNHZPYW1BdEhMY2hIRHRHVWN5TjZCL1pyRU5hVHBVcGV3QUxGRkhKU3VqTEpGT2ZhTFhrVFVOeFFwVGdmMFFRejZYanhuaTJSU3RZU2Zla01XNDJ1SUlHUURUNlFSaDNhMWRsK3JxaEY2VVE3ckxuQUZ6a1NrL3R4dFdNMXNXNTMyUXZPbGlBTUQ2T3BhMVFKcGlBM1I0YkZwQnprYkt3eTJyM2tEbXdSbHlDM0UwY0JkM1EzellXcFUvZ1ZTVVNoSk5TNEVRMXdUSk16QWEvanZuWnVNWDJ4TWl1eDl3cGJpaG9HYkRVd3lndVBuSEFXbWhMNTVXbG9DdlA0NGp3Zml6NklYN09WUDA1MUlxeFBQYWQwT3FscDN6RUVocU41RmNLNG0wcnZYbTVIR2dYTmNXbGZlT0FhYXhxOUVBcXdzcFpvd05TbVJ6Rmpmb3IycWtlNU8yQXIxVitwYVdHc2dndzJQbWNXa0dqYlgrUHFnNEdyRllsR1ZyMWJFN25rMWZjblBsS2VPb0QzL3E5YStOamJQQkkwNEZZMzlIR3g3WXBqM2RZQ1orb0J4dTlIYkdLenRDb1dlKzgiLCJtYWMiOiJkY2I0NjI4ODc3YzU5NTIyNWZmZGU3NDQ1ZjU4N2RjODUwNmM5NjFmY2QyOWJhNjczNmFmYmZmZjMzMzc4Yzk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389013986\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1239467409 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239467409\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-67841423 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:19:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpGaFlnL0pVRzJpcUNORTJmb1JMQ2c9PSIsInZhbHVlIjoidmR6NXJ6aG9hb1hrb2RsWHFZcDNwUXBMSWVTcXFDMm1ibVFTYjBzbEN5dkNtaStKMDkxdVljaG45Vm5yZzJCaS9zWGtRSm5BaXlzWXdUTXJKa3JqRzlocW0ycENINzNFKzFKOFJRZHBXc3hVeCthSHVFMVlINW10M1hBdVlyK1c2STh3UFZ4d3JWVG9VejlackphRHgxNlFva0xKaWhFVFQ4NE1EY3BTSjIvZUFDZ1JoUzREdFZhblBkRkJvR0NSbzFKelhKM2w0amRTUUtoRVloR29mN0JlQnV6b0xPWkFsZHZFTkNkNXdSTlVpR01vcE05SWhyd3ZrenJaaFZsbDNjVWF5V0pMc0FqbUwwKzdBTDFUOEdybUlPNVdkUmVOdXF3Uk5tb1JDcEZJdzdaSEhPNVRKQW0zbnF3cndaZjZJVzVXVnlNcDRwZ010aklaRXJSWnJmNlUrR1hzUDlGdHdMb2RRVXNjQ2dLWU5ZYmJiRk9GN2xZZFB6bUZRTG15amtWYVpnWmFqN0lVWWk4M0VqMkpYR2lFUEpKMzJ6K3FPZllkZ09TYzJyYXh6ZGFmWXg0NEN2ZEhxRytoZC9BS3ViZUFlRExDYjVyRHRIRGVORWhqUG0xSXI5YlowaDI0djlhUjg3QUZ6K2Q2ZGZua2tZZC9jc1JwR3ZYd3VjMEkiLCJtYWMiOiI0MzZjYTk0ZDQ3NGY2NGQ3YTIzMTkxMzZhY2I1YmRjZjJmMmI5NTZhN2Y1YTY2ZGRhYzgyNjY1YzBkZjNhZDY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpaVGl6dGtQRXVvNE1nMGRzVTc2RHc9PSIsInZhbHVlIjoiNm00bFB0QmtibTNDTWhlN0FHUHphdk9ob3A2Mmp4V3R0aUFLcG1lK0ZYbkNOQmd1ZitRNWNXblJ5RU5VMWxHY3hWN0lkYmZsWFdTMnZrL3oxZHVwNDFnbnFSWnFvYm9aUWZBbU0vQmQxRVUvYjVLc1Q2ZUVlMUFmbFdxQXBGQTlGN2lCc0oxZGwzVVNUclhzUUxOSHgvRWhGMmZFVzkwaWMxaHNjdzhRbUpNTDBiU3VGQldiT3d3L3Bpc2RPd2EycTJsNnRjcmlRM3FZTmoyb0xwWk91WDYyUStESE9PbW44djJYbjZVbDJ5QmJGSGtWYUdvRktiQUtOcWVRd3MybEdLUG5NSzdqazBCQjltYkhwL3NMemRYWC9LS3hSYmwrbGRqc2U2dWp0cXpPNDZzV3VzdlNNYUV3WjVjWE9MQ1VpT25aL0VOem5xd0pRTS9QbHJGUDJXTTM1NDZjZWpqbmgzVkxNVk9UbldaQVI5TDFHY08zaHJCNWgxeThVYmpWODFmVFV6L3RZdDVHaXNLUVA1bWhLSGsrUC9yaU1GYWIxZWEwTGNKajEyaXRkUFk1d1hQSFZyM3EwUGEyWVZhY2RiV2F2UEsxbDhtSE1jMUIvZXBNalh0SWhwd01xVHpOZHpYTWNOajlKaDhMNmxSbmJKbnY0ejVTNWVwemY4QjkiLCJtYWMiOiJmYzZhOGQyOWQ4NmQxZjY4Nzk2YWRhZjMwZmM0OTc0N2Q4NTIwNjJiZDM1ZWZlMThkNDkwYjA4NTA0ZDk3MmY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:19:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpGaFlnL0pVRzJpcUNORTJmb1JMQ2c9PSIsInZhbHVlIjoidmR6NXJ6aG9hb1hrb2RsWHFZcDNwUXBMSWVTcXFDMm1ibVFTYjBzbEN5dkNtaStKMDkxdVljaG45Vm5yZzJCaS9zWGtRSm5BaXlzWXdUTXJKa3JqRzlocW0ycENINzNFKzFKOFJRZHBXc3hVeCthSHVFMVlINW10M1hBdVlyK1c2STh3UFZ4d3JWVG9VejlackphRHgxNlFva0xKaWhFVFQ4NE1EY3BTSjIvZUFDZ1JoUzREdFZhblBkRkJvR0NSbzFKelhKM2w0amRTUUtoRVloR29mN0JlQnV6b0xPWkFsZHZFTkNkNXdSTlVpR01vcE05SWhyd3ZrenJaaFZsbDNjVWF5V0pMc0FqbUwwKzdBTDFUOEdybUlPNVdkUmVOdXF3Uk5tb1JDcEZJdzdaSEhPNVRKQW0zbnF3cndaZjZJVzVXVnlNcDRwZ010aklaRXJSWnJmNlUrR1hzUDlGdHdMb2RRVXNjQ2dLWU5ZYmJiRk9GN2xZZFB6bUZRTG15amtWYVpnWmFqN0lVWWk4M0VqMkpYR2lFUEpKMzJ6K3FPZllkZ09TYzJyYXh6ZGFmWXg0NEN2ZEhxRytoZC9BS3ViZUFlRExDYjVyRHRIRGVORWhqUG0xSXI5YlowaDI0djlhUjg3QUZ6K2Q2ZGZua2tZZC9jc1JwR3ZYd3VjMEkiLCJtYWMiOiI0MzZjYTk0ZDQ3NGY2NGQ3YTIzMTkxMzZhY2I1YmRjZjJmMmI5NTZhN2Y1YTY2ZGRhYzgyNjY1YzBkZjNhZDY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpaVGl6dGtQRXVvNE1nMGRzVTc2RHc9PSIsInZhbHVlIjoiNm00bFB0QmtibTNDTWhlN0FHUHphdk9ob3A2Mmp4V3R0aUFLcG1lK0ZYbkNOQmd1ZitRNWNXblJ5RU5VMWxHY3hWN0lkYmZsWFdTMnZrL3oxZHVwNDFnbnFSWnFvYm9aUWZBbU0vQmQxRVUvYjVLc1Q2ZUVlMUFmbFdxQXBGQTlGN2lCc0oxZGwzVVNUclhzUUxOSHgvRWhGMmZFVzkwaWMxaHNjdzhRbUpNTDBiU3VGQldiT3d3L3Bpc2RPd2EycTJsNnRjcmlRM3FZTmoyb0xwWk91WDYyUStESE9PbW44djJYbjZVbDJ5QmJGSGtWYUdvRktiQUtOcWVRd3MybEdLUG5NSzdqazBCQjltYkhwL3NMemRYWC9LS3hSYmwrbGRqc2U2dWp0cXpPNDZzV3VzdlNNYUV3WjVjWE9MQ1VpT25aL0VOem5xd0pRTS9QbHJGUDJXTTM1NDZjZWpqbmgzVkxNVk9UbldaQVI5TDFHY08zaHJCNWgxeThVYmpWODFmVFV6L3RZdDVHaXNLUVA1bWhLSGsrUC9yaU1GYWIxZWEwTGNKajEyaXRkUFk1d1hQSFZyM3EwUGEyWVZhY2RiV2F2UEsxbDhtSE1jMUIvZXBNalh0SWhwd01xVHpOZHpYTWNOajlKaDhMNmxSbmJKbnY0ejVTNWVwemY4QjkiLCJtYWMiOiJmYzZhOGQyOWQ4NmQxZjY4Nzk2YWRhZjMwZmM0OTc0N2Q4NTIwNjJiZDM1ZWZlMThkNDkwYjA4NTA0ZDk3MmY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:19:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67841423\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}