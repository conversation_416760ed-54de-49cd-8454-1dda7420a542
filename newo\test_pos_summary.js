/**
 * اختبار ظهور الفواتير في POS Summary
 * انسخ هذا الكود في Console المتصفح في صفحة POS Summary
 */

console.log('=== اختبار POS Summary ===');

// 1. فحص الفواتير الموجودة في الجدول
function checkInvoicesInTable() {
    console.log('\n📋 فحص الفواتير في الجدول:');
    
    const tableRows = document.querySelectorAll('.datatable tbody tr');
    console.log('عدد الصفوف في الجدول:', tableRows.length);
    
    if (tableRows.length === 0) {
        console.log('❌ لا توجد فواتير في الجدول');
        return false;
    }
    
    let deliveryInvoices = 0;
    let totalInvoices = 0;
    
    tableRows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            totalInvoices++;
            
            // البحث عن badge التوصيل
            const paymentCell = cells[7]; // عمود Payment Method
            if (paymentCell) {
                const deliveryBadge = paymentCell.querySelector('.badge.bg-warning');
                if (deliveryBadge && deliveryBadge.textContent.includes('جاري توصيل الطلب')) {
                    deliveryInvoices++;
                    console.log(`✅ فاتورة توصيل رقم ${index + 1}:`, {
                        posId: cells[0]?.textContent?.trim(),
                        customer: cells[2]?.textContent?.trim(),
                        status: deliveryBadge.textContent.trim()
                    });
                }
            }
        }
    });
    
    console.log(`📊 الإحصائيات:`);
    console.log(`- إجمالي الفواتير: ${totalInvoices}`);
    console.log(`- فواتير التوصيل: ${deliveryInvoices}`);
    
    return deliveryInvoices > 0;
}

// 2. فحص أزرار تحصيل الدفع
function checkPaymentButtons() {
    console.log('\n💰 فحص أزرار تحصيل الدفع:');
    
    const paymentButtons = document.querySelectorAll('.pay-delivery-btn');
    console.log('عدد أزرار تحصيل الدفع:', paymentButtons.length);
    
    if (paymentButtons.length === 0) {
        console.log('❌ لا توجد أزرار تحصيل دفع');
        return false;
    }
    
    paymentButtons.forEach((button, index) => {
        const posId = button.getAttribute('data-id');
        console.log(`✅ زر تحصيل رقم ${index + 1}:`, {
            posId: posId,
            title: button.getAttribute('title'),
            disabled: button.disabled
        });
    });
    
    return true;
}

// 3. فحص فلترة الجدول
function checkTableFiltering() {
    console.log('\n🔍 فحص فلترة الجدول:');
    
    // فحص إذا كان DataTable مفعل
    if (typeof $.fn.DataTable !== 'undefined') {
        const table = $('.datatable').DataTable();
        if (table) {
            console.log('✅ DataTable مفعل');
            console.log('عدد الصفوف المعروضة:', table.rows().count());
            console.log('عدد الصفوف المفلترة:', table.rows({filter: 'applied'}).count());
        }
    } else {
        console.log('❌ DataTable غير مفعل');
    }
}

// 4. محاكاة تحصيل دفع
function simulatePaymentCollection(posId) {
    console.log('\n💳 محاكاة تحصيل دفع:');
    
    if (!posId) {
        const firstButton = document.querySelector('.pay-delivery-btn');
        if (firstButton) {
            posId = firstButton.getAttribute('data-id');
        } else {
            console.log('❌ لا توجد أزرار تحصيل دفع للاختبار');
            return;
        }
    }
    
    console.log('POS ID للاختبار:', posId);
    
    // محاكاة الطلب (بدون إرسال فعلي)
    const requestData = {
        pos_id: posId
    };
    
    console.log('بيانات الطلب:', requestData);
    console.log('URL:', '/pos/process/delivery/payment');
    
    // يمكن إرسال طلب فعلي إذا أردت
    if (confirm('هل تريد إرسال طلب تحصيل فعلي؟')) {
        fetch('/pos/process/delivery/payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('✅ استجابة تحصيل الدفع:', data);
            if (data.success) {
                console.log('🎉 تم تحصيل الدفع بنجاح!');
            } else {
                console.log('❌ فشل في تحصيل الدفع:', data.error);
            }
        })
        .catch(error => {
            console.log('❌ خطأ في الطلب:', error);
        });
    }
}

// 5. فحص حالات الفواتير
function checkInvoiceStatuses() {
    console.log('\n📊 فحص حالات الفواتير:');
    
    const statuses = {
        'جاري توصيل الطلب': 0,
        'تم توصيل الطلب': 0,
        'Cash': 0,
        'Network': 0,
        'Unpaid': 0,
        'أخرى': 0
    };
    
    const paymentCells = document.querySelectorAll('.datatable tbody tr td:nth-child(8)');
    
    paymentCells.forEach(cell => {
        const badge = cell.querySelector('.badge');
        if (badge) {
            const text = badge.textContent.trim();
            if (text.includes('جاري توصيل الطلب')) {
                statuses['جاري توصيل الطلب']++;
            } else if (text.includes('تم توصيل الطلب')) {
                statuses['تم توصيل الطلب']++;
            } else if (text.includes('Cash')) {
                statuses['Cash']++;
            } else if (text.includes('Network')) {
                statuses['Network']++;
            } else if (text.includes('Unpaid')) {
                statuses['Unpaid']++;
            } else {
                statuses['أخرى']++;
            }
        }
    });
    
    console.log('إحصائيات الحالات:');
    Object.keys(statuses).forEach(status => {
        if (statuses[status] > 0) {
            console.log(`- ${status}: ${statuses[status]}`);
        }
    });
    
    return statuses;
}

// 6. فحص التحديث التلقائي
function checkAutoRefresh() {
    console.log('\n🔄 فحص التحديث التلقائي:');
    
    // مراقب تغييرات الجدول
    const table = document.querySelector('.datatable tbody');
    if (table) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    console.log('🔔 تم تحديث الجدول');
                    checkInvoicesInTable();
                }
            });
        });
        
        observer.observe(table, {
            childList: true,
            subtree: true
        });
        
        console.log('✅ تم إعداد مراقب تحديث الجدول');
    }
}

// 7. اختبار شامل
function runFullTest() {
    console.log('\n🧪 اختبار شامل لـ POS Summary:');
    
    const hasInvoices = checkInvoicesInTable();
    const hasPaymentButtons = checkPaymentButtons();
    const statuses = checkInvoiceStatuses();
    
    checkTableFiltering();
    checkAutoRefresh();
    
    console.log('\n📋 نتائج الاختبار:');
    console.log('- وجود فواتير:', hasInvoices ? '✅' : '❌');
    console.log('- أزرار تحصيل الدفع:', hasPaymentButtons ? '✅' : '❌');
    console.log('- فواتير التوصيل:', statuses['جاري توصيل الطلب'] > 0 ? '✅' : '❌');
    
    if (statuses['جاري توصيل الطلب'] === 0) {
        console.log('\n⚠️ لا توجد فواتير توصيل في POS Summary');
        console.log('💡 تأكد من:');
        console.log('1. تم حفظ طلب توصيل من POS ADD');
        console.log('2. المستخدم لديه صلاحيات مناسبة');
        console.log('3. الوردية مفتوحة');
        console.log('4. لا توجد فلترة تخفي الفواتير');
    }
    
    return {
        hasInvoices,
        hasPaymentButtons,
        deliveryInvoices: statuses['جاري توصيل الطلب']
    };
}

// 8. أدوات مساعدة
const helpers = {
    // إعادة تحميل الصفحة
    refresh: function() {
        location.reload();
    },
    
    // فتح POS ADD في نافذة جديدة
    openPosAdd: function() {
        window.open('/pos', '_blank');
    },
    
    // تصدير بيانات الجدول
    exportTableData: function() {
        const data = [];
        const rows = document.querySelectorAll('.datatable tbody tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 1) {
                data.push({
                    posId: cells[0]?.textContent?.trim(),
                    date: cells[1]?.textContent?.trim(),
                    customer: cells[2]?.textContent?.trim(),
                    warehouse: cells[3]?.textContent?.trim(),
                    total: cells[6]?.textContent?.trim(),
                    status: cells[7]?.textContent?.trim()
                });
            }
        });
        
        console.log('بيانات الجدول:', data);
        return data;
    }
};

// تشغيل الاختبار التلقائي
console.log('🚀 بدء اختبار POS Summary...');
const results = runFullTest();

console.log('\n📋 الأوامر المتاحة:');
console.log('runFullTest() - اختبار شامل');
console.log('checkInvoicesInTable() - فحص الفواتير');
console.log('checkPaymentButtons() - فحص أزرار الدفع');
console.log('checkInvoiceStatuses() - فحص حالات الفواتير');
console.log('simulatePaymentCollection(posId) - محاكاة تحصيل دفع');
console.log('helpers.refresh() - إعادة تحميل');
console.log('helpers.openPosAdd() - فتح POS ADD');
console.log('helpers.exportTableData() - تصدير بيانات الجدول');

console.log('\n✅ اختبار POS Summary جاهز!');
