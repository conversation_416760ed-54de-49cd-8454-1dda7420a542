{"__meta": {"id": "Xa5411cef7e3fd008281f164322edc488", "datetime": "2025-06-08 12:54:32", "utime": **********.329096, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.859602, "end": **********.329145, "duration": 1.****************, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": **********.859602, "relative_start": 0, "end": **********.114017, "relative_end": **********.114017, "duration": 1.****************, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.11404, "relative_start": 1.****************, "end": **********.329149, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00994, "accumulated_duration_str": "9.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.225287, "duration": 0.0062699999999999995, "duration_str": "6.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.078}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.262681, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.078, "width_percent": 15.493}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.304358, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 78.571, "width_percent": 21.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387267916%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InptSDVWa0RxU3orQVFBUmtPQ0tGZFE9PSIsInZhbHVlIjoiZWJMUlk4RWdMakppcjQzekZLRDR1UHdkSjgwditwbzhjQXM2Z3Zucy91VXRiN3FDaU9CWVZ2U280TnRmbDU5Y0tqZDlvcnVsYUE5WmRkc2ZpK0E5NUgzMEExUXhxR1lsNjlVR0VSRVlyVHRyUU9IaWtwT0JvSlJLQ2hkN3JKYVA3NTZJeTFReEkyZjBFUU5mZFdGYnE5OU42aUpKdEYxVVNSanRDV3AzK3NkaVkyOCtneWUyaUN6alA3TWtZR0tyVzZtVWlnTXVGcGh4b0hwWk1iSU9qRTVSSnlHZzArNXgyZVFOV1RncG1Id3FldTZ6cllFSEJKRkt1WU5mTUZ4VmlPWEQySncvOVFONlY3WWNZak4xK2l0ZVd5dWFaTXZJc3Q1MDdhZmpwZDE4ZXQ5NlpLdTA1aHJibWhIdXg0WUFhNVFDMy9mcVluZDBvdDd0US8yaVJ2cTZueDhYUkRsSlBPNms2NHB3ZUhhRUZacHVPMllyaitMTTlHSk54OHF4VnpFYUxOSnVOclB0bjBnMlhxYnhoZUlLY2lyZUh3SUU1NDFnZmNHUkFzc3lTMjZqZXgrV3c4UHBqc25wSmxoQzRMQ1QreURTTFY5RVNxV2V4dTQ0S0FDanJhNUxkdVNEd3g5dXV5WTk2L2VmbDU4UTlHQzIvQkswMzlnYXFpZFMiLCJtYWMiOiI2MWU4NzBkNDFmZTQ0ZTAzZDA4YzI0MzJhNjVkODEwYzdiMGYwOTg0ZTNiM2MxN2Q4ZjVhYTIyYjM1NmFlZTU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhrSEM0NTNYMzcvYzNmTm9hcHhWZmc9PSIsInZhbHVlIjoiUFBIUlhYcWdqYTVZUUt4RncvY3RSZ1FYWG8zL3N3RHI2cWdjd04zbVgzTnl3eTlnY2NLajFkMU9wazMxVWNJTVlQRFgzNUNTNkQxQ3lPWlBWZ1BnRWdndmlDZ0JUQ0pQUGx6VkNYR0RueEF5TWI5ZFZKM0lWTlNKOENvMG9Ta1VkY2w1NGhTOEdQVloxVXRhMHhlU3pBbWNrOWFWQTBmbDNaajFKbW4ranFkc3JoczhzSFdjb0YvNEZMN0hHNHRZalhieW1mTW9BU3kwSjNhTFZGY3FzVWVDSVUxbjhvUVlJL1g1d1p4T2ZxUGpySDJWMjg3Y2pGUkNCK0RRakM3RHZpSTRFQmxVTW5kYTd1UUF0cXUxc1UyL2x3SEo4TEZCa2dIc08raVZjSTc4MHlINWh5VEtZRHIxemErbHZZY3M1djFIeDFhUGRHOEt0alhmREQ1eGJNL3FNRTZQMUZ0QXhNWjdwVE5lSG8xWU1QR2MzVXNWWHRJU1I2QTZObzRIZkp4NWtsRkhoR3owaUNlZ0lHOThQeTdoZDRvK0RBOTl6K0ljeVZFNXJiU0ltWEpqUElzOS9zWkF5dU95eWJyeWhEcjZMem9iM3dLMm1uTzA0eGVHTDlEUzFDME1tUHAzQUtmdjhxWTVzcVI4WGpoVjlPWTh5K2FHQ3NIQnhaNlYiLCJtYWMiOiI3YzhmNTBmOTUzNzRiMjU5OWYxOWI3NGIyMTE0Y2I5YTQyOTFmZDUyOTYxNTMwOGI5MTY4NmFkNWE2MzM5ZWIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1546420762 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:54:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9PSVRxVGd4MHBrOU9JNEdUWmhZNkE9PSIsInZhbHVlIjoiYlY0bnBtSDBpdGFuQThoaTk5R2hWL3ZkS0ZvUnlLZU5iNVE1TGR4MVkxbGh1UTZLd2swanRWeG5jaU1uYzNzTVVPaXNpN3RHZFIvdCtxaTlwWkVwampKMkhHN09NcmJxNTN6TzhMUXcvZ1Q5UlFGei9MQ012c2V5WjNlRVRNRno1RmFNMXBJYWZKZUcwcHF4Ri9RNlhMaXZtY216U2VtYzFPazhrQ2hYQkROUGxyMDMyZS9KSThncytxMmw4T25IRjZDVFlOT0czYk42TXRsbC9qcU1BVmVXWjFxNlZvZVJnRzhSMVNVK0tFNnoxbkNHRUQrMGFCNmpONmlrK0J6QXRoaEQrd0g0K0dyR2ZKNG13SkVTZVdLejBJaUQ3bm5IYkEwLzZXVHE1WDEvY210RUIwVmZwdzMzbWJUSkZtMGFNQTlvZjVrNTJBa0lLSnlhRjJhVmczVURqMGIyajlhYkN5RitpWWpkTWhGTGVJdElDZXZEbFZka21JTm12OTlVNzRXcmIrSkptcXBSNHVHNkV4VDZyS2VWVkQ5WmwvaVpoaVpueVdTK0dzNWV2MVhLSGcwT1ZnYUtwcVBMMXNYRHVCWFpMNnpUdjMwTkxLVDBTZTJFZEJnTE1Cd1hUeG1DanRWd1hGSks1WUEweHZhVWFKaUtySTMxQ1lnOE1WR1EiLCJtYWMiOiJmZmMzMjE3NGM2OGM2ZTZiYTg5ZTAwYjhmODE0YjZmZWY4ZDZlNTBjZjM4ZmI0MzJiYTI0YjcxYzk2Y2ZkZGRkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZPWWJndVA0aUZveWNKQlc5aGdFakE9PSIsInZhbHVlIjoic1ZXSTh3b0NjdU1laXFRVHBLbERBajhXUG1kYkc2M1VlT1g5Q2cyUFJZY3U4am9nNjdFUytKd2pURlA5YXo1UVc4VHkyS0p0RDRUd0hRMWxpbjFLYUNPdW80Wm52cWs1UVBzdkNqMm9ZdjBNenZINCszcGYxd3k0YWRiSGhaNFRBaEMxUDNGdkRWcG1rbkJtMSttbkxnMUZrVlNDZUVzVS81Z3Y0TkR4WkZ1SS9id0dCN2VFejQ2My9oREpzc3ZSdHQwZ0F4bGpiclB1SmlFNDZNWWFQTXE5dzlBYkI5d05aT0xIcE5NeGYyK2d1cXhUemZFaGl1R2ZhcWRzc0VjTUlLTUF4L3J2V0w3MXNvUlB0eCtzRmFsbTJ5RTg5VEJNaVdqU3ZoOGFGQ1RVTm9BemNlMzNFTFZvZ1dROGsvWk91ZjE5Mk9IUUhTNUtmVU9jQXJEOStTamdDcDdvRC9VSFJhWjgzOVYrNFZMUkZ1Q0FicG95ZzBTWSsxbGphUmhtMnBjTVRWSUl0K2s2RjFtR2pSQ2JIbHh5TGZDNGhlVllFcldTWldvdU1NT0lwZTg5ZmQwVnlSQktwQ0hYU1JZaHo4OUpxN29PTHkrUkI3SVowSnpjdlR3blVmT0o4emcrY0haV1dVTk8vWlI1bC9YU0ZOZGZYVDUwTUxDaERkdjAiLCJtYWMiOiIyZWVkZTEyMThkOWJmMGU0ZDU3OGY0YTk5Y2U4MzBmZGYyYzQ5NDc5NzRmZjQ3YzEzYTMyZTNhMDRkNzE3OTg5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:54:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9PSVRxVGd4MHBrOU9JNEdUWmhZNkE9PSIsInZhbHVlIjoiYlY0bnBtSDBpdGFuQThoaTk5R2hWL3ZkS0ZvUnlLZU5iNVE1TGR4MVkxbGh1UTZLd2swanRWeG5jaU1uYzNzTVVPaXNpN3RHZFIvdCtxaTlwWkVwampKMkhHN09NcmJxNTN6TzhMUXcvZ1Q5UlFGei9MQ012c2V5WjNlRVRNRno1RmFNMXBJYWZKZUcwcHF4Ri9RNlhMaXZtY216U2VtYzFPazhrQ2hYQkROUGxyMDMyZS9KSThncytxMmw4T25IRjZDVFlOT0czYk42TXRsbC9qcU1BVmVXWjFxNlZvZVJnRzhSMVNVK0tFNnoxbkNHRUQrMGFCNmpONmlrK0J6QXRoaEQrd0g0K0dyR2ZKNG13SkVTZVdLejBJaUQ3bm5IYkEwLzZXVHE1WDEvY210RUIwVmZwdzMzbWJUSkZtMGFNQTlvZjVrNTJBa0lLSnlhRjJhVmczVURqMGIyajlhYkN5RitpWWpkTWhGTGVJdElDZXZEbFZka21JTm12OTlVNzRXcmIrSkptcXBSNHVHNkV4VDZyS2VWVkQ5WmwvaVpoaVpueVdTK0dzNWV2MVhLSGcwT1ZnYUtwcVBMMXNYRHVCWFpMNnpUdjMwTkxLVDBTZTJFZEJnTE1Cd1hUeG1DanRWd1hGSks1WUEweHZhVWFKaUtySTMxQ1lnOE1WR1EiLCJtYWMiOiJmZmMzMjE3NGM2OGM2ZTZiYTg5ZTAwYjhmODE0YjZmZWY4ZDZlNTBjZjM4ZmI0MzJiYTI0YjcxYzk2Y2ZkZGRkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZPWWJndVA0aUZveWNKQlc5aGdFakE9PSIsInZhbHVlIjoic1ZXSTh3b0NjdU1laXFRVHBLbERBajhXUG1kYkc2M1VlT1g5Q2cyUFJZY3U4am9nNjdFUytKd2pURlA5YXo1UVc4VHkyS0p0RDRUd0hRMWxpbjFLYUNPdW80Wm52cWs1UVBzdkNqMm9ZdjBNenZINCszcGYxd3k0YWRiSGhaNFRBaEMxUDNGdkRWcG1rbkJtMSttbkxnMUZrVlNDZUVzVS81Z3Y0TkR4WkZ1SS9id0dCN2VFejQ2My9oREpzc3ZSdHQwZ0F4bGpiclB1SmlFNDZNWWFQTXE5dzlBYkI5d05aT0xIcE5NeGYyK2d1cXhUemZFaGl1R2ZhcWRzc0VjTUlLTUF4L3J2V0w3MXNvUlB0eCtzRmFsbTJ5RTg5VEJNaVdqU3ZoOGFGQ1RVTm9BemNlMzNFTFZvZ1dROGsvWk91ZjE5Mk9IUUhTNUtmVU9jQXJEOStTamdDcDdvRC9VSFJhWjgzOVYrNFZMUkZ1Q0FicG95ZzBTWSsxbGphUmhtMnBjTVRWSUl0K2s2RjFtR2pSQ2JIbHh5TGZDNGhlVllFcldTWldvdU1NT0lwZTg5ZmQwVnlSQktwQ0hYU1JZaHo4OUpxN29PTHkrUkI3SVowSnpjdlR3blVmT0o4emcrY0haV1dVTk8vWlI1bC9YU0ZOZGZYVDUwTUxDaERkdjAiLCJtYWMiOiIyZWVkZTEyMThkOWJmMGU0ZDU3OGY0YTk5Y2U4MzBmZGYyYzQ5NDc5NzRmZjQ3YzEzYTMyZTNhMDRkNzE3OTg5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:54:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546420762\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-8334783 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8334783\", {\"maxDepth\":0})</script>\n"}}