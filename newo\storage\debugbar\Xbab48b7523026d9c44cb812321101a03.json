{"__meta": {"id": "Xbab48b7523026d9c44cb812321101a03", "datetime": "2025-06-08 13:34:52", "utime": **********.556204, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389691.221632, "end": **********.556233, "duration": 1.3346009254455566, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749389691.221632, "relative_start": 0, "end": **********.381492, "relative_end": **********.381492, "duration": 1.1598598957061768, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.381515, "relative_start": 1.1598830223083496, "end": **********.556236, "relative_end": 3.0994415283203125e-06, "duration": 0.17472100257873535, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45587448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01565, "accumulated_duration_str": "15.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.465693, "duration": 0.013630000000000001, "duration_str": "13.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.093}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.510366, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.093, "width_percent": 5.495}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5292861, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.588, "width_percent": 7.412}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-voucher/1\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1586939638 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1586939638\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1239161491 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239161491\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-492039558 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389689334%7C32%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdmcGhRbGVRQXo2M0EwT2FGOXBpYlE9PSIsInZhbHVlIjoiS0dGUVdRekRIVk1pZHhjeHRBcXlqN09RYTA1dVNtUWRtWUQ5a2xQUk5INmNhYVJMbU5NUEQ5UndhRXRRdEs2d1oycjZjN0hTc1luRytzMWZTV1hmcUFHaFFWVEYwSWh1WXFTaFdxVXZmdnhZdmJQc3NMTEJpQURNNmJ6bC9NQW5aTCtFZHVoVXJhZmVQM3Z3aDNJOXNreGgzWmYwVjEwV0RqNDNQOHRLRVl1a1pxZmhrU2didnlYTERZbG0vR292cUliYk5ENmJYY1pCZE1JTVRtQk92M1NRc3hycHFuNzgvTjlIMDJ4aEE0ZXNiOTcwZmR0cnNvUm5LRVJSVmptN2tjUlozS29DRkd3b29RMXNqOGRTbGpGNk5WUThrNytQYUJVNFR4cFlKV3VDMXd6dkhLTW5COGo1OHdiUFVnS0p3aS92REs4ZFg5VTNQekpjQzdoOUt6WnpjaGVjTE02SEd1R1JjTWlKM0N0WU9EY3NoKyt6UDdZNjhsaEJRb0MrbkowUnZSdFAxUXRBVFpRaTEzbEJvbjZYNzRnUG9QWUVSYlRhWURLeCtES0xBUkppZFNJSzdVQkRNZzJ5S3hENS8yclc1SFNWMmdjYTlRaVJNNUxiVmhPdWhaUkQweDFZY2xvOXIyOWFpK1lEZEJEU0pVUmpVck1oSFl5ZnJDeTciLCJtYWMiOiI5ZDYzMjcwOGI4NTUwMDg5MzU4NzIzZmUwNmVkNTc2YjEzNzA1MWYyNjE4ZjBmOWZkYWUyOWRmZTJjNjJiZTdmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImwwS1JwQzVmL3NvNFJlTHhFN0JKclE9PSIsInZhbHVlIjoiOWQ1bVJxNURMeEZTU2RPbGszQzBuaXBzYjJnUzNaUFV1MllpMElHUzIzdiszVkYwWFB2MHRLUllyWGxQK3FLbTVEQjBtL3NSZ1haYTNnS0t0NTlZUXRXQVNaT0VWMThieG5kMk1jMW5ubVR4cG9lYS9NUCs2SlJ5UGp0cjhtaTV1Und4ZlA1UUpWcVhXWWc2RU0zUTFkVWpnT3A4TStnM2hySzVGNksyaWlKRy9ZcWNrYkppVGFWT0l5YVoybjVsSzYzRklDeDd0Q1lCb2FHenNab0c2ekQ3WVN0NUM4TkNIVDlMNnFtL1JNVHJsZTNrRHhmbzdnS1ZOZDhVRHVud2JuclErTFMrY2tDWTZtZXhrMndPZlhGRjZuL0RkVHJzYUt3NkdFVVdSOEJISU5zdjIvUWxtZlU1bFdZWFpoZFZ5R1h6WklWVGNkeTNDVVREdzU3dkFaTkd0bURvRVdWN24zcHl4amRuTCtLT2Q0TWVFVnpKeE5KT1M4WHR0VWtZckg3UnhDRDhGaXNkK2wrakNOK2FYVENmZmdENSthajVoY29sYWZMSGRuNFE5Z01Bd0dBZ2lzZXFsOFZzV3RtczBnNDhSNUpod3RBMGRsZHdwbHMxUTJCSHYyRnMwU3Y0SXdHaHZNY1IvRUo0YnNqTEZ5SGV1TThDOUZOQ1R4amsiLCJtYWMiOiIyYzEyOGE3YTQyYmIxYmQ4M2ZhOTM0YjM2NDI5NDQ0YWYyYWNmNTY4ODFhNmNmZGEyNjYxNTFkNjAxNTBlM2I1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492039558\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1325035443 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325035443\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-130841815 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh6dmUyNjRHdStIWnBSTkg1aGJ6Umc9PSIsInZhbHVlIjoiNk9kYXlVa01ibHZFbzJURDZMelB3T3FPWmlzZk5TUW5rbmdCWHZtRlorNkJYaFF1VkZzM25Dbi9qS1pwbTJzMnBqaTVGZUZFcmdSNE82MlVUTHIyVk9scU1OcXVsSzJvQ2wyNzB1V3ZNRkorUXVocFRacjJValpKNGV4MUR0ZFVEaHZLRXZUa3N3cFRrL1dPTWJHYm1yS1pRTkFQL3lnSVFiNHh0N1B3OUplelNISUVKTUk5NndyRHNUajVHdmpyakpyWmExSzZPaFpSMTNXYXd1YVJFYmNBaStVbmN3UmY0NG83L1ladkdjYTEzdmdmSlN3Y2I5NUVsdWE2TVNnOStpelphVjZDeFVHa0I4bFhLMUZHaWFpbitFRVBpK2wvMkRtRGdoL0RvRDdLcmtFS3Bja21xamJubW1iNkVzV3dLcjdOYmZlTEhKUW9Pa1MyVWNMelM5dmVCbTAzVzMvN3c1eGJ6aDV3bDgvS1lYT0JaTHNsZEczSkRGZ1hhaXUrMVhydjBsQ2lSTFZGZGo4ampOaEJ0K2k1THp6L1VtOWtrK0h5ZlRSMllnSDhMV1FKdjcrelJ3VXVvSlVlUWdDUVJKNHBqL21ZQTVON1NoSm5CSHJQak1NQWtRM0J4dnB4c3duMHFFVll6ci9aa01CVHB5alRkcTlIaXEzYWJDR2siLCJtYWMiOiI2MzUwMTA0ZTM5NmMxZTI4MjU4ZDU3MTgzZDVjMGIyNTI2NTE4ZWQyY2Y1NjRlNzdhOWE5MTM4YzhkMjZiNTA0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZNQTcxczdUWTBTbEZiTlRMaDBMeUE9PSIsInZhbHVlIjoiQm1lUmlOb09pcCtORHZxVGNsblB1V2VrWUVRWWU3cytKdDdObHVlN3V2QVlyUW8zdUZTWHJxVkdjSjJOMDhWNDNEUEUvNGZTaU9HT0JrbkdhWlUwTDNGZzFydVhoemdNRWhORXBQWmZxa2lHVzAxcG81K2FSczN0WWR4aUhjWWI1U0FOejdzVDBFWGFtYXAvWmxhUEx4dkpSV2s2eHNqQjdVZytyRG9UcG9USVRNVXBGUnNNbWE5NDFVMU9lSi8ydmlUWkdZWmcvSVJNeFAyNWNYajRHVkhiYjBPTHpGR2grNW8rUmZMRVA3M2ZucWl1Tnh0TXYxT0ZHM2pKR0s1Y1B4dHhTSWtZYXpnSWlUdWRJNy9zTDdjSGxPTGw4MGRzVnE0bzlSNXZTVVl6R2plVlJYQW94WmN5ZUxFd0VjSmgxeFdmaFMzdlRtVlgwMXkvbU0zOFFYYWd0T3NadmQrNmZZNTQ1TG9yYU51N2oyNllQSjNYVnhUWUNGNVhPbjdheUZxS2R3ejYrTWhuUXFBY1kyYVBXNVJMK0VsUnRwUElZeUxkTkFTL2EvZXNjQ1o1TVMvUU5CTDdyMU04STgzZkZrOW9MazNRNERqRlRwUVVnT1N0TGZoUnFISURPOFhhMEtQTzVodk1Lek44bFExeUdiUS95aDIwbURJSTlhaWwiLCJtYWMiOiIwZGM1Mjc2M2E5ZWRmMGUwOTIyNzI5MjUwY2MwODljMDJkYTQwYWFkNmFjOGM5NTYxZDI1Yzc1M2U1MmQzMzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh6dmUyNjRHdStIWnBSTkg1aGJ6Umc9PSIsInZhbHVlIjoiNk9kYXlVa01ibHZFbzJURDZMelB3T3FPWmlzZk5TUW5rbmdCWHZtRlorNkJYaFF1VkZzM25Dbi9qS1pwbTJzMnBqaTVGZUZFcmdSNE82MlVUTHIyVk9scU1OcXVsSzJvQ2wyNzB1V3ZNRkorUXVocFRacjJValpKNGV4MUR0ZFVEaHZLRXZUa3N3cFRrL1dPTWJHYm1yS1pRTkFQL3lnSVFiNHh0N1B3OUplelNISUVKTUk5NndyRHNUajVHdmpyakpyWmExSzZPaFpSMTNXYXd1YVJFYmNBaStVbmN3UmY0NG83L1ladkdjYTEzdmdmSlN3Y2I5NUVsdWE2TVNnOStpelphVjZDeFVHa0I4bFhLMUZHaWFpbitFRVBpK2wvMkRtRGdoL0RvRDdLcmtFS3Bja21xamJubW1iNkVzV3dLcjdOYmZlTEhKUW9Pa1MyVWNMelM5dmVCbTAzVzMvN3c1eGJ6aDV3bDgvS1lYT0JaTHNsZEczSkRGZ1hhaXUrMVhydjBsQ2lSTFZGZGo4ampOaEJ0K2k1THp6L1VtOWtrK0h5ZlRSMllnSDhMV1FKdjcrelJ3VXVvSlVlUWdDUVJKNHBqL21ZQTVON1NoSm5CSHJQak1NQWtRM0J4dnB4c3duMHFFVll6ci9aa01CVHB5alRkcTlIaXEzYWJDR2siLCJtYWMiOiI2MzUwMTA0ZTM5NmMxZTI4MjU4ZDU3MTgzZDVjMGIyNTI2NTE4ZWQyY2Y1NjRlNzdhOWE5MTM4YzhkMjZiNTA0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZNQTcxczdUWTBTbEZiTlRMaDBMeUE9PSIsInZhbHVlIjoiQm1lUmlOb09pcCtORHZxVGNsblB1V2VrWUVRWWU3cytKdDdObHVlN3V2QVlyUW8zdUZTWHJxVkdjSjJOMDhWNDNEUEUvNGZTaU9HT0JrbkdhWlUwTDNGZzFydVhoemdNRWhORXBQWmZxa2lHVzAxcG81K2FSczN0WWR4aUhjWWI1U0FOejdzVDBFWGFtYXAvWmxhUEx4dkpSV2s2eHNqQjdVZytyRG9UcG9USVRNVXBGUnNNbWE5NDFVMU9lSi8ydmlUWkdZWmcvSVJNeFAyNWNYajRHVkhiYjBPTHpGR2grNW8rUmZMRVA3M2ZucWl1Tnh0TXYxT0ZHM2pKR0s1Y1B4dHhTSWtZYXpnSWlUdWRJNy9zTDdjSGxPTGw4MGRzVnE0bzlSNXZTVVl6R2plVlJYQW94WmN5ZUxFd0VjSmgxeFdmaFMzdlRtVlgwMXkvbU0zOFFYYWd0T3NadmQrNmZZNTQ1TG9yYU51N2oyNllQSjNYVnhUWUNGNVhPbjdheUZxS2R3ejYrTWhuUXFBY1kyYVBXNVJMK0VsUnRwUElZeUxkTkFTL2EvZXNjQ1o1TVMvUU5CTDdyMU04STgzZkZrOW9MazNRNERqRlRwUVVnT1N0TGZoUnFISURPOFhhMEtQTzVodk1Lek44bFExeUdiUS95aDIwbURJSTlhaWwiLCJtYWMiOiIwZGM1Mjc2M2E5ZWRmMGUwOTIyNzI5MjUwY2MwODljMDJkYTQwYWFkNmFjOGM5NTYxZDI1Yzc1M2U1MmQzMzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130841815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1206059961 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206059961\", {\"maxDepth\":0})</script>\n"}}