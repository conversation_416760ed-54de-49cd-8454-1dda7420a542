{"__meta": {"id": "Xdafe14cde04a68c1c77787b087ebc7e5", "datetime": "2025-06-08 12:57:22", "utime": **********.679954, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387441.159856, "end": **********.679983, "duration": 1.5201268196105957, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749387441.159856, "relative_start": 0, "end": **********.41777, "relative_end": **********.41777, "duration": 1.2579138278961182, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.417794, "relative_start": 1.2579379081726074, "end": **********.679986, "relative_end": 3.0994415283203125e-06, "duration": 0.2621920108795166, "duration_str": "262ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45978320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.575627, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.594022, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.653792, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.663162, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03396, "accumulated_duration_str": "33.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5058582, "duration": 0.01487, "duration_str": "14.87ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 43.787}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.529386, "duration": 0.0107, "duration_str": "10.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 43.787, "width_percent": 31.508}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5478358, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 75.294, "width_percent": 2.739}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.577251, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 78.033, "width_percent": 3.504}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.59604, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 81.537, "width_percent": 3.327}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6251469, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 84.865, "width_percent": 4.358}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.634742, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 89.223, "width_percent": 3.298}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.641403, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 92.521, "width_percent": 3.563}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.6568441, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.084, "width_percent": 3.916}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ToM9MzFHjrqchhleuo7Qv68shFdHDviRp2ZwOxFx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2088993856 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2088993856\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1463123345 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1463123345\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-995257943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-995257943\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-226182433 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1873 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfw2%7C0%7C1961; XSRF-TOKEN=eyJpdiI6IlI0V0ZuT3p0SU8xQXFEaHdYbCtnSkE9PSIsInZhbHVlIjoia291Q1F3aG1LYjJpN0hyQVNYSDh4YlZlY003YzBYdVExTnorcEdQNWlQNi9McElQSERscjNzS2lramYvYytNclJCMy9qTy9tSXpEeGRUYXpKTktScGhxVXBELzNyOCs1c2c3dTBrU29jMWpZQjZ2U2F3dGZhQTMvTG5mZGx5NGdESzFtTDZ1c2tZd0hoMXUvcUJZOEdlbENZWFZFU2ZtTjNFbmVHVWxwTUJJMGxqeEZkalRHOWllZ1NyS0o1M0VLZnQxL1VlUjEzTnFpK1NMUzBSY0dqRDBscldtcTZqVi9heE9QZm9qa3VlTGc3WmZHeEhLTzFBMHhnMUJES1d6Z1lvQncwNWxYckJFVUlxbC9wdUdIc2IyYVV1RVltcDBuU3c5RmxwVEcyanJWd2NkMmpLcnVvVHhJU21FMnppY2wyU0wvWVBmY1NiVTFQOC83bU1TazlrbE00bFFsTC94ci80WnA0bmxFbXFQMFdRTTVUZjM1cEVPaTBKQ3ludXZOS1R0NTY3NWQ4TjM0czdVV0U3SzFJTTNWUi9LMTNxR29CYkxLSVdaMVV0clp5SkJNTHBwNm5oZy9MWDQ1cHlWTndrY3JWbnptQlp4YVh0UzVHZXNHWmEvRnJrMFd6QzVYZGZWZmxZclRWUzZxVWJkUGV2OXVFQlJoci9XUzlkRVoiLCJtYWMiOiIxNzJlNDIzNzZhODQ4ZDg0YmFlN2ExMjBmZDgzNzA2Y2E2MjhlMDI1Y2RjNDdkN2UyNDBkNWQ2MTNmYjU0ZTYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitGV0dQYTEra0cvdExtclltWXNJaVE9PSIsInZhbHVlIjoiZWU5aDVLQ2xZdngwVEFtOTJZRlhwdjI0Z1ZhT01wTDB2ajRBZFppMG1kNmxxVjIvZW5jbE5iUVJiSXVEM3lCeE1oaG9hdlNXYmJ0QkMzb3ZUNFc0L2RwYmRaN3NraGNsZ0FUaWNnQjZYYmlHRkNjb2cyT1h1NE50MFhLalZJaHIvVDNYOENxQ1g0SkRvQ3daNDdDTzlLM1JzQUhzNlZhQjQ3a2hZZ1o1KzRWQ0w5RXNYV1lyM3h2cjVJVlI2RytnVUZyRDlzVFYxRlpwTmsxYlM4WnkzNzQrZ095VGZ5bmo0L1Q1UzB1OHJoQzNlRFBQZGRlZlhibVo1Umllb25lWkNvTFc4L2JzZGNEQys4L3l6ZTRSdHMyUGdLd3NWTC9KOWJvcU9GRG0xZ0xXdUVLbktoV0d6RStaMUl1WnpnVTJTY1N2MHFoazJ2ZklSRWVMSFJZb2hmUlRWNXZvUHk3czZRNTB3eW5NNWp0Q1pyVnIyNjRYY2xFT0xvZEQ2Z1lEaldTaldVU0ZXc05iRWhMUkh5dDdDYmNrSC9DWjdJbGVvZmh5R3Uzdlc3Q3VvMU9qSHJpV1RvcUJuRktiaFhaSXhydzMrUWhEb3NoSmZ2RTNGcEZ5djZKZjdFdXVkNHRBcnkwbjBlekFVZUw0eFA0NmpxWld0L25XL0pHWnUzSGkiLCJtYWMiOiJkOTA3ZmNlYmE4YjExNTA5Yjc3NmIyYTg2NWYyMzljYjQ4NDhjNGUyOTY5OWVjZmQ4Njk1NzU1NDljMThlZTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226182433\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1305946471 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ToM9MzFHjrqchhleuo7Qv68shFdHDviRp2ZwOxFx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nrZph8Dla3jIgGwCbHvkhb1P3uakyTyeArEn5gUJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305946471\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1739555293 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:57:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUvd1FNMXFqS05aMGJoczRZNkpNcGc9PSIsInZhbHVlIjoicDErUFpyTy9qN3IvT3B6YVZKRkR5ZVBGQkVqaktuZnJ3L3UxQmZUaGk0RHR4clZIWkZIdGtHeERPT0tHUEJWUFlZMnRBMldQb0hBdUEzejlsbzJVeFh6Ty9pM1p2NmdBS1lOQkhmRFRHY0VhSGtDanNMZE1rV0E3d3FNVWQyQXRycWd0ejYzV0NuQ2RsUWw2TmhRVytUbGRsdklHRU5mMEcxZThYejF2anFXUkZxYzM3a2l5ZmoxM3ZSMUxlN1F6ZFloaVV3VjdDMW00VWU3VmFzblZjd0NCQjh4NEpSWU83ZjRKMktQa1pHaktCVWh4UGFjMUFFem1lbDk5K3ZHZjBjbnpSTmdzTXJaS2t2ZG1xckRxQ01hb0I4WDBRTEZlRzNFcjlUL3hxdmlsMVBjTWxXZjdkQ1ZRWms2K0E3Z3BsNGlvcnAxVnJrRnl0NzBmRGMySmd1VnVkbWJUNHRYYmFyL096R0JOa3NwcUhIdHp6bkl1OGczeEEwWUVEZk1lNGwwSm5CT1hpdGJkaXRGczF2TFM1N1gxeHhGYUNKS0hOVXVSb1dESVNnQ3BheXdJL2RxU29hREtPWFZLcEU4SkZ3QlRpVnVnTXVKY1BKRlhwdE9IS3BKVXlEUFQ1SE5tMmJsV25jREhtUFFUYzhBelpHRXNOM3V3bFkxby8yZ1ciLCJtYWMiOiI0NjNmNjUzZjk2ZTkyOGU3ODVjMTBmZWRhMTdmYmM2NzY5NGUzNzQxMzc1ZTU0OWYxMGRhNWViZGJmMDVjNzcwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:57:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRvNlFIMEhtbFpIeEFOMG5KTHJGSHc9PSIsInZhbHVlIjoiVzBROUUycVFXUys0UU5XaGhabzVHeTgxd3hMbGVYOTRCNzc4UWVsWUcycU80Zm95NnJpamI0M001cGg5RUFEdzV0dUd3K1B2Vit5amlCS2RiRzNhd0FVeGRmc2E2QzFtMno3ckdXZzhnK1U1K3p6MDJoTFhhNDM0Ty96Q0tIV21tSlptbDF0cTlxbVQxQ3lkeEF1UXZvRG50Vm5nbnNUei95TEg3VEVBcEtxeVcwRFM3cVVhemdlSFdwV0dWYWZJcUxYdmZHRVdUOEZXQTFrVklzOHM1UXZSNmh2NUJCNGlHTUNNVWdUSVFYOURTRkhiMlJyWVRzL0lTTGx6R3JtakZsMlBKNHE4dGFQbFA0aGprL3NoODREbE4yMFh2Sm5uL0l6Z0h6QmE0eVEyY2Z5STRKTmRSYU8vNmZzbUZGTXhmdGVUNC80U3h2TTc5UjRtR0RZbDlZb3g5Y3RzZmJqNzJUYUhmT2VkNlIzT1dHRHo0Z0t2T0dUY2wyQ2hRTklXYVFnVnBNdjlaUzdPWk9GQmM0NXNpclI5NHR1akNOeFA5K3dIT2YwdXltdEw1WHAxTUNkaTJ3VW9VcWFtcHVla1A0endHNUFVbXFXNTJOUkJUVFZsZHczbVR5S1J2WW9GQS9nSGJBanByaGR3Z3BsK3hFY3RCTi9qeFM0STJlb0IiLCJtYWMiOiIzMGRjNzcwN2QyNzc2YmRhZjkzYjJiNWNhY2ZiYzMwYjYwYWUzYWI1ODUzYTMyZmJmNTY1OWMyOTY5MzY5ZTExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:57:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUvd1FNMXFqS05aMGJoczRZNkpNcGc9PSIsInZhbHVlIjoicDErUFpyTy9qN3IvT3B6YVZKRkR5ZVBGQkVqaktuZnJ3L3UxQmZUaGk0RHR4clZIWkZIdGtHeERPT0tHUEJWUFlZMnRBMldQb0hBdUEzejlsbzJVeFh6Ty9pM1p2NmdBS1lOQkhmRFRHY0VhSGtDanNMZE1rV0E3d3FNVWQyQXRycWd0ejYzV0NuQ2RsUWw2TmhRVytUbGRsdklHRU5mMEcxZThYejF2anFXUkZxYzM3a2l5ZmoxM3ZSMUxlN1F6ZFloaVV3VjdDMW00VWU3VmFzblZjd0NCQjh4NEpSWU83ZjRKMktQa1pHaktCVWh4UGFjMUFFem1lbDk5K3ZHZjBjbnpSTmdzTXJaS2t2ZG1xckRxQ01hb0I4WDBRTEZlRzNFcjlUL3hxdmlsMVBjTWxXZjdkQ1ZRWms2K0E3Z3BsNGlvcnAxVnJrRnl0NzBmRGMySmd1VnVkbWJUNHRYYmFyL096R0JOa3NwcUhIdHp6bkl1OGczeEEwWUVEZk1lNGwwSm5CT1hpdGJkaXRGczF2TFM1N1gxeHhGYUNKS0hOVXVSb1dESVNnQ3BheXdJL2RxU29hREtPWFZLcEU4SkZ3QlRpVnVnTXVKY1BKRlhwdE9IS3BKVXlEUFQ1SE5tMmJsV25jREhtUFFUYzhBelpHRXNOM3V3bFkxby8yZ1ciLCJtYWMiOiI0NjNmNjUzZjk2ZTkyOGU3ODVjMTBmZWRhMTdmYmM2NzY5NGUzNzQxMzc1ZTU0OWYxMGRhNWViZGJmMDVjNzcwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:57:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRvNlFIMEhtbFpIeEFOMG5KTHJGSHc9PSIsInZhbHVlIjoiVzBROUUycVFXUys0UU5XaGhabzVHeTgxd3hMbGVYOTRCNzc4UWVsWUcycU80Zm95NnJpamI0M001cGg5RUFEdzV0dUd3K1B2Vit5amlCS2RiRzNhd0FVeGRmc2E2QzFtMno3ckdXZzhnK1U1K3p6MDJoTFhhNDM0Ty96Q0tIV21tSlptbDF0cTlxbVQxQ3lkeEF1UXZvRG50Vm5nbnNUei95TEg3VEVBcEtxeVcwRFM3cVVhemdlSFdwV0dWYWZJcUxYdmZHRVdUOEZXQTFrVklzOHM1UXZSNmh2NUJCNGlHTUNNVWdUSVFYOURTRkhiMlJyWVRzL0lTTGx6R3JtakZsMlBKNHE4dGFQbFA0aGprL3NoODREbE4yMFh2Sm5uL0l6Z0h6QmE0eVEyY2Z5STRKTmRSYU8vNmZzbUZGTXhmdGVUNC80U3h2TTc5UjRtR0RZbDlZb3g5Y3RzZmJqNzJUYUhmT2VkNlIzT1dHRHo0Z0t2T0dUY2wyQ2hRTklXYVFnVnBNdjlaUzdPWk9GQmM0NXNpclI5NHR1akNOeFA5K3dIT2YwdXltdEw1WHAxTUNkaTJ3VW9VcWFtcHVla1A0endHNUFVbXFXNTJOUkJUVFZsZHczbVR5S1J2WW9GQS9nSGJBanByaGR3Z3BsK3hFY3RCTi9qeFM0STJlb0IiLCJtYWMiOiIzMGRjNzcwN2QyNzc2YmRhZjkzYjJiNWNhY2ZiYzMwYjYwYWUzYWI1ODUzYTMyZmJmNTY1OWMyOTY5MzY5ZTExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:57:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739555293\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-230947038 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ToM9MzFHjrqchhleuo7Qv68shFdHDviRp2ZwOxFx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230947038\", {\"maxDepth\":0})</script>\n"}}