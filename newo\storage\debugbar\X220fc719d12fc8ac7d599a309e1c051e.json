{"__meta": {"id": "X220fc719d12fc8ac7d599a309e1c051e", "datetime": "2025-06-08 14:46:40", "utime": **********.200909, "method": "GET", "uri": "/pos-payment-type?vc_name=7&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749393999.568617, "end": **********.200933, "duration": 0.6323158740997314, "duration_str": "632ms", "measures": [{"label": "Booting", "start": 1749393999.568617, "relative_start": 0, "end": **********.037946, "relative_end": **********.037946, "duration": 0.4693288803100586, "duration_str": "469ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.037958, "relative_start": 0.46934080123901367, "end": **********.200935, "relative_end": 1.9073486328125e-06, "duration": 0.16297698020935059, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52973168, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.19492, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1472\" onclick=\"\">app/Http/Controllers/PosController.php:1472-1580</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.01755, "accumulated_duration_str": "17.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.086465, "duration": 0.01264, "duration_str": "12.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.023}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1107888, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.023, "width_percent": 3.362}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.134525, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 75.385, "width_percent": 6.097}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.138541, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.481, "width_percent": 3.305}, {"sql": "select * from `customers` where `name` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1483}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.145623, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1483", "source": "app/Http/Controllers/PosController.php:1483", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1483", "ajax": false, "filename": "PosController.php", "line": "1483"}, "connection": "ty", "start_percent": 84.786, "width_percent": 3.419}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1484}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.149777, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1484", "source": "app/Http/Controllers/PosController.php:1484", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1484", "ajax": false, "filename": "PosController.php", "line": "1484"}, "connection": "ty", "start_percent": 88.205, "width_percent": 3.989}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 577}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1488}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.155369, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "PosController.php:577", "source": "app/Http/Controllers/PosController.php:577", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=577", "ajax": false, "filename": "PosController.php", "line": "577"}, "connection": "ty", "start_percent": 92.194, "width_percent": 4.217}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1567}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1787412, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1567", "source": "app/Http/Controllers/PosController.php:1567", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1567", "ajax": false, "filename": "PosController.php", "line": "1567"}, "connection": "ty", "start_percent": 96.41, "width_percent": 3.59}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-538282254 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538282254\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.144192, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1692137449 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692137449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.154381, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 2\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 20.0\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 35\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1384241031 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1384241031\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1362904247 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362904247\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1796618119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1796618119\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1610339122 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749393346722%7C61%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZFQzd5Q2JJajREQjhnaGYwVWVwc0E9PSIsInZhbHVlIjoicFA5K2Q5emhNYnVXR3hweTY1dUdjWVBtQll4SmFtNGpkQ1JjZW9EYWRpZjZiMVpiYzd0VWl3UGJ1RzF6MlRma21LVCtMVGp5ZmpxM3RUV2lNOTNyN1pNNVVaRHZOQ0xtMkRBalNJM1NoZTd5TDJSSjFEQnF2cHR5NGxKS1R4K2dWOWZQbjIvTXVsMmtjUXB0YWUvQlJDcHFNQmx6c0tNenJXR0ZqOXE0R2NBL3I5VmVSZnY0KzZNR0FEdkIvb1ZqMnU2ZmpBdGdUSUR5dnF6K05MTkxWRlVJWE16aS9YRkxKSmg1ZVE1Q1hIOTZ4TklvYTRQMmZ2MW00ZmF5Y2dxZnVwNnF1KzE4SkFrUElNQlhzZ083RzhJRjdRUmVnaUZWUU10Umh4bU13TW9ka3Z2a2pCTkZOa3Z3dnNEaGExN0UyZk1PdC9SaUdqSHpOb1JBUkRaSHR6emg1Z1dhQld2OUtJZ0VzQ2xpVVZtbm4yMU9DL3QvWXVrdWR6eVpwcnJTcndlQTNHOTdRVFdOQ05XcXZId1UxdkRLbHpkc0N1N0Vnd20yMVlRdk94OVF2ZmRtRXQxWW9vRFppK1ZqVy9jdldEcytZZVZJbERGRENNQkhEZUM3YUFkK012MnQxYkN5dytPeERmRUlVbzViWldOUkcrbVcyQjlWdU96REZqYTAiLCJtYWMiOiJhMjk4ZmIxZmVkMmIyZWVlYWRhNDFkOTk5M2NjMzVkYjEyNjc0ZmE4MzQ0YWVlNDkwMGQ4NzBjMDJmYmJjMzBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJweU1NVzlhTkd0bC93SmpQRXVHbWc9PSIsInZhbHVlIjoiSi9Xa3lDNDFhbVdwajVVcm40ZEV6U0lCbHpHK21pVXVHbDZ5NW5vNDhDM1ozaTI4T3E0TkNKaElTSmhQdXU3ek1GdCsxQ1ZCcTl5OHFLRkhNa2xXYTYyYmc1QnRGN25ZTGFpSlJMUyt0eW05TUhkVWd1V205bVBjZ3libHJVNzRMR0RFRURFUVlKVFA0djR3aG05engvcHYwZ05aYnkxR3VIWjFnMlZVbVB5WUYyTE1kVmgzaUd5a3JmMnZBL2JQM2dtVDc0d2hKeHoxdWExbzZQYmhOc0xRbzg0UnJoZkxOWmE3MkljNUdDSHBuREdudlJLUnBnSTVwT25jdmVrdGN5WnR2ajNycFhpT01DeVdYQ29mUmxobG0zWXhoMHFJaVdXN05FTFFZYUFrZjlSbkl0RXkvS1Y4ZVRDZlBoeTlNZ3JkUTlvUzZDL3VhWUJyTUlhOFRUZkhrdndFdjZUM3hOc2puRkFTa0Qrdjg3eEVvT3RweEJuYXRuV2JPNUt4b0FqMG1WenlvNHkyajVkbWtnYi9va2Z2R2V6Um44Q2VGRmRLMERzLzlxWnZRaEM2RUY2UFgxMFBRdHZETG90bE9JVWhSUUgyc3NycTZrV2pUOUcxWlplTjRkR2RFMElrY2d4eENMMU1Gc1pIM1B2RFF4Sm12SG5nNDdyQnM5RWQiLCJtYWMiOiIwMTdlZDNmMDliODdkZjRjYTE0ODcwMDJhNTc4YzI4ZTc1NzkwNWMyZTJmZWZkNzMyYWRkOGI3YjUxODcwNGI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1610339122\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1085866816 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085866816\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1729892047 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:46:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNOK0FSd3laVTVlbEt4cWtMdGEvRmc9PSIsInZhbHVlIjoiSDhVZUhyUUFtZHVDWWhSa2cwRlRsMllHVytSWWtRSXNXMnFXZXIxQVRyK2srN1BsUTdGTDBIZHBwTnZ4ZU9Db2lOaTBDMEdXRXdEQWFSZHpqWGhBYml3bVVncEw4b3RZeUMxY3NKdlhRL3JvSkx6bXVEckxNQTRTZ3g4dS9qSm9zKzczT0RrQ3FIa2RidC9aQ3owZUE0VXZpaUsrNkVYQytjR3JiZUowRzRhaGZLZ3diYjVobDV3Uk9qNXZRbkhpWjFpOEQrTWpHdHVjUVpkSjQ3VThram5DZk5MdWxxYUNSZU15bnRsN3lEeENoWFU3NXErVEx0WHBES0NZSDIwWWVBNWFmZjBjRFJRYnFVWStMazVvOU5oUW84anRqdnFySnpRWFJLcFo2UzVSTDUwTytOV044bnZ2b1BIeUhkU2FOTzVrVTVJNkpRSFlYYVE5WXdVRndwWFVXWnJYc1duUjRrOGE5SHZaWGRUUlBjMFIycm5JVVQybm1LbzB0cG5wZ2NQb0hxVGxscXhqc3liaFpyVVVYWTJPSEdwM2dndXIwOE0zNHNkaTBJeFVma093czBDSktyUHg0OFVpeUZIMHlJUmx5UmhvbXhMQ1FkSlkxU0IrYXFsdXlZMjNuajFJSlJiWEZVU3Q2T2g0cGl3TC9rQkQ2RVE1TnhYL2FUU0wiLCJtYWMiOiJiMTZmNTBiMzZjNjA0MTQ1OWE1YWJkZWRlZTE1ZjA0ZjMxOGEyNGM4ZjAxMDRjNTMyYjI3NTBjYjVmMzA5N2UyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ino5M0lRSm0xWjIwdDh2ZENlUzJHclE9PSIsInZhbHVlIjoiVTRHSHU3Y1c4R1ZZRXdIMjVjR080WXhYbmtZZklRU2tvNFJEWFp0M2gyVFg4MjUwNjZpbTlLbGw2MUdVR3VHbGZERVFvZ0s4QlVSc1h1bjYxZWk3dlpMUVozalkyUHdlQ2tPUzBCbDBPMytubGdydGJDZy9TSE81OHVUajJEVXJhR3BCd2JnaGI3MWpEMHF5YUlLNzNIdXUyNzZyeVhEMGJGL0cvQnZJUG04OGl4SGpnR2UyRmRHK2xxeDZyNmZlK1dBMG5YWG9uaHV3N0tUcGN5MTRUQ2dZdWJOU1FZVUJHV3RpYlhpK1hpeGNjMTVNdGVUMStuTWlWSDV6OVpLbm05c1JNTWw5b1QzMlNPWkI3NTUxRWFpVXorb2tkUGQ1YXZwOWI0d0lZVjNqeXh4Q1BpWU9iOG1WTlQ0ZS9jYkN5cTR2OEhYNmtGbC8yQ09vSzA0TlU4ZiszKzBtR3J0VXBwWnBVRm8zMXA2N3lPcXpJb05RYXFhdFFKckZ5RE9tM3VsSlVJWncrZ1FYaVA1K0N3all0YU5WSmhBZERyb0FVcGRZcjgyQmhHOENlazg4dmlUbFpRcTE0MFhuSlZnQWw4NytpbXlBRzdPbWZ0UzNBM2ZoYjFML3VPdXRTVDE3cmx2c2pRK0x2dHR2RzFRcHNHQ3AvbllpWEtKS2VlMk4iLCJtYWMiOiJjMmU0N2Q3OGI2MjA2MjdiNjUzNzZmZjQ5OTM4NmFjOGFkYmE1MzVmODFlZGEzMTU5M2ZjOWIxY2IzMTFiMzMxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNOK0FSd3laVTVlbEt4cWtMdGEvRmc9PSIsInZhbHVlIjoiSDhVZUhyUUFtZHVDWWhSa2cwRlRsMllHVytSWWtRSXNXMnFXZXIxQVRyK2srN1BsUTdGTDBIZHBwTnZ4ZU9Db2lOaTBDMEdXRXdEQWFSZHpqWGhBYml3bVVncEw4b3RZeUMxY3NKdlhRL3JvSkx6bXVEckxNQTRTZ3g4dS9qSm9zKzczT0RrQ3FIa2RidC9aQ3owZUE0VXZpaUsrNkVYQytjR3JiZUowRzRhaGZLZ3diYjVobDV3Uk9qNXZRbkhpWjFpOEQrTWpHdHVjUVpkSjQ3VThram5DZk5MdWxxYUNSZU15bnRsN3lEeENoWFU3NXErVEx0WHBES0NZSDIwWWVBNWFmZjBjRFJRYnFVWStMazVvOU5oUW84anRqdnFySnpRWFJLcFo2UzVSTDUwTytOV044bnZ2b1BIeUhkU2FOTzVrVTVJNkpRSFlYYVE5WXdVRndwWFVXWnJYc1duUjRrOGE5SHZaWGRUUlBjMFIycm5JVVQybm1LbzB0cG5wZ2NQb0hxVGxscXhqc3liaFpyVVVYWTJPSEdwM2dndXIwOE0zNHNkaTBJeFVma093czBDSktyUHg0OFVpeUZIMHlJUmx5UmhvbXhMQ1FkSlkxU0IrYXFsdXlZMjNuajFJSlJiWEZVU3Q2T2g0cGl3TC9rQkQ2RVE1TnhYL2FUU0wiLCJtYWMiOiJiMTZmNTBiMzZjNjA0MTQ1OWE1YWJkZWRlZTE1ZjA0ZjMxOGEyNGM4ZjAxMDRjNTMyYjI3NTBjYjVmMzA5N2UyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ino5M0lRSm0xWjIwdDh2ZENlUzJHclE9PSIsInZhbHVlIjoiVTRHSHU3Y1c4R1ZZRXdIMjVjR080WXhYbmtZZklRU2tvNFJEWFp0M2gyVFg4MjUwNjZpbTlLbGw2MUdVR3VHbGZERVFvZ0s4QlVSc1h1bjYxZWk3dlpMUVozalkyUHdlQ2tPUzBCbDBPMytubGdydGJDZy9TSE81OHVUajJEVXJhR3BCd2JnaGI3MWpEMHF5YUlLNzNIdXUyNzZyeVhEMGJGL0cvQnZJUG04OGl4SGpnR2UyRmRHK2xxeDZyNmZlK1dBMG5YWG9uaHV3N0tUcGN5MTRUQ2dZdWJOU1FZVUJHV3RpYlhpK1hpeGNjMTVNdGVUMStuTWlWSDV6OVpLbm05c1JNTWw5b1QzMlNPWkI3NTUxRWFpVXorb2tkUGQ1YXZwOWI0d0lZVjNqeXh4Q1BpWU9iOG1WTlQ0ZS9jYkN5cTR2OEhYNmtGbC8yQ09vSzA0TlU4ZiszKzBtR3J0VXBwWnBVRm8zMXA2N3lPcXpJb05RYXFhdFFKckZ5RE9tM3VsSlVJWncrZ1FYaVA1K0N3all0YU5WSmhBZERyb0FVcGRZcjgyQmhHOENlazg4dmlUbFpRcTE0MFhuSlZnQWw4NytpbXlBRzdPbWZ0UzNBM2ZoYjFML3VPdXRTVDE3cmx2c2pRK0x2dHR2RzFRcHNHQ3AvbllpWEtKS2VlMk4iLCJtYWMiOiJjMmU0N2Q3OGI2MjA2MjdiNjUzNzZmZjQ5OTM4NmFjOGFkYmE1MzVmODFlZGEzMTU5M2ZjOWIxY2IzMTFiMzMxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729892047\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1021806596 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>20.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>35</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021806596\", {\"maxDepth\":0})</script>\n"}}