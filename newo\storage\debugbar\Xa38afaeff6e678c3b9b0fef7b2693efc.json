{"__meta": {"id": "Xa38afaeff6e678c3b9b0fef7b2693efc", "datetime": "2025-06-08 12:58:08", "utime": **********.894405, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387487.491432, "end": **********.894439, "duration": 1.4030070304870605, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749387487.491432, "relative_start": 0, "end": **********.717644, "relative_end": **********.717644, "duration": 1.2262120246887207, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.717664, "relative_start": 1.2262320518493652, "end": **********.894443, "relative_end": 4.0531158447265625e-06, "duration": 0.17677903175354004, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45577688, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019569999999999997, "accumulated_duration_str": "19.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.804497, "duration": 0.01722, "duration_str": "17.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.992}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8487282, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.992, "width_percent": 5.876}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.868392, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.868, "width_percent": 6.132}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-579065632 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-579065632\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2101118132 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2101118132\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-305146482 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305146482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-876305911 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387444987%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRkNVFJZkZEYWEyd3dNd3JoN0cyMUE9PSIsInZhbHVlIjoidzBVRkNkYU5BNjJzNnd4SlZhVXMxcnZmT0dGcHc3dU4xZ296SDdXTWlBaVRrdHExTlRHVSt4dmdySEdDYlVqL2lRTDZzdzNVQzQ3ZElyY1J1SmJqR0lraFJKYnRMZWZiaDBiUTFmQmhVSFYrZEdPUVNRQTlsWlBKTXdSOFVqcGtJc1duekY3dGpCMXc5UlF2MUhQaE1RMWl3bGhqYWFMcHVSUUxqVWJBZXBjQXY4QzRrZ0hCc2g5U2pyR3BjY21Fa0hvNllwcUozNmYyUFZLMTQ2M0lmMUcwRTBGaytheDc4Q0pnenM5eFlhWVFjbWtIMGMwdHNHd1BhOXVtNTNCMXUrbnNqaHMxWmkvNjlzWWZGQXJoMWhBQklpamlHOE9zU0tQeTlBTzJMSE11a1g3ZEZ5SWk3WjBlZ05WRjZVdStBVUVRM3JvUGVWLzUyNXMxRmxRUGhYTWNOcnp0dk1kblZVdWp5Vy84b3NkeVJsZUlLcExoNk45aFFBVDNpTFFvMGF6UW5xZFRNaWNTcHZTeDlab1R0MDE4cVBQeWlEWGJJNnVxKzFhSGI5MElVZXJHUEQvQ1RHRUVKcHhLcExzY0Z3RVVVT1BGa3hqWXpDS2Y3bDZ3UjNwZmZBNGYrNjlVREZLQmlFMldqNmYzcWZXY2ZxTVpmYnJRSHRaM1dKT1giLCJtYWMiOiJmZGI4YzFjNWE3ODZkNWRhMzc2ODBiZmE0ZWEyNGQwNGJlYzU4ZThhZjU2MjJmNGQzMDFiODg5YmUyNjRlOWJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im50cHBSRzhuWUZTeGQ0MzFCazRwUWc9PSIsInZhbHVlIjoiNGFvemxWcEVrajF6eGtKSUUvcCtpTExjQkdDb213UEhWN045WWxUY2Z0Y2grUkJuUDFCMDBFdFA5NWw4WjJrZ3A3RmpvakQrMWErTlJtZnNqTGdqM3VBSW43Nzc2U3dqSjcxRmJQcnYxL29HNGRRclpUMTByS2UxZlpZblZITDM0VDhkdERjbitBUjNRUlNxaGJkeGpqUHQ0V3IxODluN0hwcFkyQlFHOU1GMFB6Q3UrSEs0aUlRN0lkcWI1Qkh2ZXRwbUlRbFBQQUxWTTRqNDFxZXdmenExQitpeWs4dUtENzV1Q1gxeVYwdnd0bzlqSXRDUFBsaFo4RGR1SDYzTysyYW9ubTZiQ0pBSVl2YldzbmNDdnduUkVQalV3OUpRSG5jRFZPWGZiOXJmdkRwMUpPUE5OMHlvaHZRMzBqMC81NHQ3UXhHQXZHZDJWMUVpbHhmTW9lZlp2NHdtamZoQWtuR001b1BCaWtOTllNNVNCdFJGSGpqcExWb0U0WnhXT0VGZWVScmo2QmszdGRRaVNtd21sK29LeFEvV3VzWmUzdzhiSUg0QTdYZ3FHdGhNczhTSzVpUEpKRGNhVjlwbkY5OXVZZEUwaGtScngzWm42cWE3NVhVam8vMGcyUE83VVQ5YUVHcjNrbzc5UmhCRkRqZW1yem95UUQ1L3BUcU0iLCJtYWMiOiJhOGEyN2E1YTVmNDUzMWEyODkzODcwM2JlODk2NWNkNjdjMDNkOWMzMTU4ZDYyNDA3M2Y0NTgzYzg0NWZjOTUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876305911\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-950291948 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950291948\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1057134462 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:58:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZudGhWS0MwZDJXQmlLa20wZURJUmc9PSIsInZhbHVlIjoiSnMrbHZ4MnU0RUFTRzdVNzFLOEtFUnJ2VmZ6SHRPUWJUeW5PNksxbWlGTWpCQTRQV3dFOWNYMVZiSFNIRlBTSzhWWldXTzlHY3BQcUZUZmtKaTk4RTBiejN4RHpKTmp1VmoyMGdtaVF3U0RwN1ExTDBaNktpRStYZXB1YUNjVU0zWUU4SlFVZldUODM3KzE1ZFRURWhzbWE4eXVETmQ2VUd4MElOTTRBZkdYM1hObnZySk9TN1BjaCtyaTN3anZMeXNJMUova2ZhUlBUWnVDbTZFejZkQkxzUDlyRTFEOTRhQy9PazM0NEdjR0xIOFQxNFhJVzFkc2NVTkdwdDNCcDFQYldISk1KMm12WWlFeVBKdkN0TDJkZk1rU1JnU3Q0K29kdFdpazg1NzJ4WlZsQlNkdHk2NFUza2J3OVNTVjdlY29GYXdyeHlZaEpZNDd3ZUJnc0xzOGFjbEVNbE5hOUdDdmE4czU4TUxnZFZsUzBUQkJpVi9vNnR1OTdKM2t6Mzdma1ZCWDloQTk2Ry9BZk5xeERWYVJNd0ppdVJ5Sjh5NExDalc0SjBWaVprcVBySzZWZEZFcmxqWVp6c3VkQnhiYUU5bjdtZ09nNGlianVRZmUzdjdCUkpuS3NxeS9xMTI1eFlvdVlLM0JtRlpnQXRGa05RMVhTZ0s5ZzBsQ2YiLCJtYWMiOiIzNTA5ODhkODJlOWNhMDc3OWVmOTBhZDllYWM5Y2UzOWI2NzI2MjFmODYzZTExYzE0Yjk0YWQ2MWMyNzU1ZDkwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRDUUpBMWJTQmp1Z0xrcXMxY3NEZmc9PSIsInZhbHVlIjoieW9XaUR5Tm9RN3hodCtnN0lnT0plZjBpY3lNRXgwZmY1MlBaNms5ZDFJZWNkNi9uUzJoR2tNeW9mcTVYenpRa1k5M1RuOGNSTUFJQVZXcHNZK21aWWIrb25Sais5TjRZdjhJa0ZoZTNseGk3cFQ4a3pnZ1o4NDRBMFZMM3lCSEI0Ym5HK09iSmdPKzhReWJ5aFlZV0tHL2ZJSlE2VlRWemxzanZ5Sm1Gd0svT0gzSk1jVTkwaXNWOGhPSVJXQ3dsdTV3Zzg0SDYycjM3QllxanJsc0hNNVJQbUtkWkdpTWY2TGx4bkkvSTMvcjY3NldMVm9UK1RYKzExUG5iSGEyTlJ3RHJnTGRZZHZBdU51QytMOUY5bFBXRjNaV0s1UUJ6ckZrMDkvdjlGMkRtckxBV0oxSm92MG1SUjl0dG9HV1grdHVzSVpEejIxYzM0anNkU2FrcC83OU5OUzRKVUhCTVhha2l1NWJ2MWtoOXZ3Q3NlVkJjVTZVQzdXM1BtWXBkT1dUWForbjI3NzNsaUg0RU9HdS85NktXa0dkd1dIb2tKTXRSZ2d6T1MyTGxLdy9pUDM3Vm5mMHB3czlPcDlCc3JDUVlPbzdMNlZCQmtmUFRKOFVxdmpicmZjdGkwU0pXNEEvZXBTdjQ1b1VoYnZibmU4c2pobFVRckUzYlVMYXAiLCJtYWMiOiJkNTNiMzk3ODgzYWY1ODdkYjRjZTdkNWI0NjhhMmU4NWRmYWRiZjFmMTBhMzBjZjdlM2QxZDg2MDE4ODM3MzRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZudGhWS0MwZDJXQmlLa20wZURJUmc9PSIsInZhbHVlIjoiSnMrbHZ4MnU0RUFTRzdVNzFLOEtFUnJ2VmZ6SHRPUWJUeW5PNksxbWlGTWpCQTRQV3dFOWNYMVZiSFNIRlBTSzhWWldXTzlHY3BQcUZUZmtKaTk4RTBiejN4RHpKTmp1VmoyMGdtaVF3U0RwN1ExTDBaNktpRStYZXB1YUNjVU0zWUU4SlFVZldUODM3KzE1ZFRURWhzbWE4eXVETmQ2VUd4MElOTTRBZkdYM1hObnZySk9TN1BjaCtyaTN3anZMeXNJMUova2ZhUlBUWnVDbTZFejZkQkxzUDlyRTFEOTRhQy9PazM0NEdjR0xIOFQxNFhJVzFkc2NVTkdwdDNCcDFQYldISk1KMm12WWlFeVBKdkN0TDJkZk1rU1JnU3Q0K29kdFdpazg1NzJ4WlZsQlNkdHk2NFUza2J3OVNTVjdlY29GYXdyeHlZaEpZNDd3ZUJnc0xzOGFjbEVNbE5hOUdDdmE4czU4TUxnZFZsUzBUQkJpVi9vNnR1OTdKM2t6Mzdma1ZCWDloQTk2Ry9BZk5xeERWYVJNd0ppdVJ5Sjh5NExDalc0SjBWaVprcVBySzZWZEZFcmxqWVp6c3VkQnhiYUU5bjdtZ09nNGlianVRZmUzdjdCUkpuS3NxeS9xMTI1eFlvdVlLM0JtRlpnQXRGa05RMVhTZ0s5ZzBsQ2YiLCJtYWMiOiIzNTA5ODhkODJlOWNhMDc3OWVmOTBhZDllYWM5Y2UzOWI2NzI2MjFmODYzZTExYzE0Yjk0YWQ2MWMyNzU1ZDkwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRDUUpBMWJTQmp1Z0xrcXMxY3NEZmc9PSIsInZhbHVlIjoieW9XaUR5Tm9RN3hodCtnN0lnT0plZjBpY3lNRXgwZmY1MlBaNms5ZDFJZWNkNi9uUzJoR2tNeW9mcTVYenpRa1k5M1RuOGNSTUFJQVZXcHNZK21aWWIrb25Sais5TjRZdjhJa0ZoZTNseGk3cFQ4a3pnZ1o4NDRBMFZMM3lCSEI0Ym5HK09iSmdPKzhReWJ5aFlZV0tHL2ZJSlE2VlRWemxzanZ5Sm1Gd0svT0gzSk1jVTkwaXNWOGhPSVJXQ3dsdTV3Zzg0SDYycjM3QllxanJsc0hNNVJQbUtkWkdpTWY2TGx4bkkvSTMvcjY3NldMVm9UK1RYKzExUG5iSGEyTlJ3RHJnTGRZZHZBdU51QytMOUY5bFBXRjNaV0s1UUJ6ckZrMDkvdjlGMkRtckxBV0oxSm92MG1SUjl0dG9HV1grdHVzSVpEejIxYzM0anNkU2FrcC83OU5OUzRKVUhCTVhha2l1NWJ2MWtoOXZ3Q3NlVkJjVTZVQzdXM1BtWXBkT1dUWForbjI3NzNsaUg0RU9HdS85NktXa0dkd1dIb2tKTXRSZ2d6T1MyTGxLdy9pUDM3Vm5mMHB3czlPcDlCc3JDUVlPbzdMNlZCQmtmUFRKOFVxdmpicmZjdGkwU0pXNEEvZXBTdjQ1b1VoYnZibmU4c2pobFVRckUzYlVMYXAiLCJtYWMiOiJkNTNiMzk3ODgzYWY1ODdkYjRjZTdkNWI0NjhhMmU4NWRmYWRiZjFmMTBhMzBjZjdlM2QxZDg2MDE4ODM3MzRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057134462\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1108987693 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108987693\", {\"maxDepth\":0})</script>\n"}}