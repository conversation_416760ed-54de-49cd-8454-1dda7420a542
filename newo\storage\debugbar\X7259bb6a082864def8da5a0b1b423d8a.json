{"__meta": {"id": "X7259bb6a082864def8da5a0b1b423d8a", "datetime": "2025-06-08 14:52:54", "utime": **********.325848, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394373.666717, "end": **********.325874, "duration": 0.6591570377349854, "duration_str": "659ms", "measures": [{"label": "Booting", "start": 1749394373.666717, "relative_start": 0, "end": **********.228726, "relative_end": **********.228726, "duration": 0.5620088577270508, "duration_str": "562ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.228738, "relative_start": 0.562021017074585, "end": **********.325878, "relative_end": 3.814697265625e-06, "duration": 0.09713983535766602, "duration_str": "97.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45588280, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01936, "accumulated_duration_str": "19.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2666519, "duration": 0.0181, "duration_str": "18.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.492}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.300223, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.492, "width_percent": 3.254}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.31062, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.746, "width_percent": 3.254}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 13\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 34\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1096159747 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1096159747\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-85322095 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394371272%7C64%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjEyV1hOdGYyekFJZm8zTmJuUEo0bkE9PSIsInZhbHVlIjoiemQ0WEJsQUYrVUZQTVI2bjVrS2haT2tnYTZuYzM5QUVpUmZLcHdsZk0zMzl5cXZXYWpFMldQcitPR2hMdHBYcHQzMTFGbkJidHZVTFlva3hHTU5Db1JrRUdndXBIVlo5aWtkeVVVdUFpS2FPY1Y5NDF1T21XZ0hwOVJyYlNpZzFDRU1rRHFzR1ZUNTNuNkpOUEJSaEJvVnhVTk5WcFJ6bnRtdFVTSW0xWVJZdXZqVVNCbm1PQ0FVTUJDZWtjVGEzeG4zRjlBd2xNSEMxTUtnMnN4dmk3OCt3bHZ3SU9OU2FUN214TXpTN2EyR1BoTVU3blpETUh6OFJ1ZFdmQ1V2SHVJd24wcEp4UGJKME51YlRkK1pmM3daalFJbk0zOUlNLzJZQTFvcG5PcUpvNHlGcXd5Y2tWUHgrRkdYQ3dVbTRkdHpUUFc3NHJYQ3ZIaUdDZVRWNjQ3ajFTN3RPS1M5QmxuRXc0ci92alVGblpMcHBmOGMySkpFWUFsNjdDWXgwbHdJaFhTN09qcjVOenhaL3YzTENFdGhScUcxR0NSaVdITDBCVzRXeVU3VS92dm5vQzByd25qUWxoakIySFpCWkU1dE9aVkdQS21DSDNDWW9nUzlSbm96c2tINnpJWklDQ1htVUEzRGxLSmt0RmRxZk80dkYwZldvZ1BwTy9mdloiLCJtYWMiOiJjZTdmZTI4MjFkNDhkY2Q2NWU4NjdjOWI0OWVlN2Q2ZWNhY2JiNDNhMDFlZmFkZTBiZjBmNjE0YTEzODdlNDY1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjcxelJMTU1Xa1JjNmFPNWlzcmdlRHc9PSIsInZhbHVlIjoiM1hzMXE1VmFvWGtyK1k4K1diRGg3TmdYcmg3cFpKL3AyTDQ3OHNNSmxjRWVRWVNSbXJLLzhXekRkOU1uRWRBYytjelZCeUtoSjcyc0N4TElRZHpUcFNpUWhiNHRXR3FSQ3czUEEwMjl6Ky9sNXplTHphT2hQN0d0ZlNmQnBjVGV2Q0dDNnBuOVp0RVN0NWduQWNSbGtHWUlLYzY0QXVCcGgwMXJjMGpDY2VXUkp6TXgwUUhJVmwxQkpSNW5QOUVVNlFFNm1BbHVkL1R5dkZVZm8zaDlhMGhtTWczNFlGaHQvNUtLcVlod2ZZR29PcGVuR2g2UStLb0dFK2UrU3lQajJZY0NGRjIrSkRSOU9nNStSZ2FOMlYzTDRmc3hXK0Q3OW5obHlnVDdIQWJOTWZFdjNBQjM4RGJWSU9ERFM1ZGxoek9XRE9uT2FPZytJUGl4QXJydWtRUnk5dXRjTUdjMHFxUnhLdkJPM3pDL2hqZXkxTWI1Tk10ZlhQZGpmWDlIdGdRYmFtaVNFNmg0amFXb1VQL3FBdFB0am1uWDJsNEpRWWZOOWkzRFRaL2FNaWtWSys0b1UzN2RFYml2YkprdVZTR25GT0ZxVi9IV2lLaHVqSDZ4a0JBbDg3eVhCQ3Q1SFc5TklLSm4waVg4cTBNL3ZZVk5PdldnUGQrcVBkSC8iLCJtYWMiOiI2ZTk3ODhhOTZhNWUxMDc5YTcyMDgwYzg1MTQzYWNjYzJmMjQwNjRjZDA5MmM5ZGExN2U4MDExZDUwYzU3YjEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85322095\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-204789285 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-204789285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-476638579 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii90ZUs2U0xpa2V3Wk5CQXdmOE5kY1E9PSIsInZhbHVlIjoiUnV3Vk5rOVNJSnZvZXpVSUxmWkNUNndKV25WeEJWU3F3OTVFZ0p3eDBoZ1Q0ckkrRmxoY3FDUDFLVE1vY2twbmtzY3lmallQeGZqRytSY0ZlczExUWpzQVlrd3dVZnBMY1FQcnJIQm92WGVCRkN4UkRzVWY4cUFTRUlvRHVodG5qZFV1M1JNNTczWUlvYmdMVVN6UGRkQzZlZnpNMEYvR3JGOTR2ZHJnZ1VYTk1lVGxEK01RTjMwMWozQjY1VlRBS2RSb3pjdzhUQml4TzRQQW9ZT3Zua0lRNUxqVFhTc2pqNXFvS1llVFVDTlNISVlkcSt2Uzd5TWJXaEhxZVVsUHFRSkFMZVlBY1JqL2ZuZ2Vaclg3eHd4Q0k2ZldrWFhBR0o3QU05QWU4MVR1V3QwZCs3TTFGSW15aG4rYlBkTEs2U1ljYzF6TzQ1V3FUQ29DSkJlYi9sQmN0bWR6UlFYbGViMW5vS0JkQmdaYzN1eHRqaXhqaW9IQ2JKNEthanNVMkpEY0F3WlhtS3lRaTJnd3FZYndVRW9FK3J3TzdIVVZUOVg1M3hyL3Z0ZUtKRGh3dlVWWnIrcjI5Uk1LenlFNGhCU2FQOE5GdWprTU5hVWRuSzhwNTJLR0tISXlKL3J6VVVNYkVSZSsxTUpseXUrTUIvRG9Md2h3bVF6eEkyR0ciLCJtYWMiOiI2ZDZiZmFiMTY1MjRkMjMwYjhiYThlMjUxZDkxNGVkNzFjZjA2ZTQ1YzJiMTg3Njc2ZTg3ZDE4ZThmZmRhNWJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkV5NzE1QjdNM2tzL2FuWmFsVEMzQkE9PSIsInZhbHVlIjoiVkpLYis2b1JmRzhTd1hFU29iUlYvS2xSQ1BuNDllcXZtZVFMUDc4MEk4OXl0ZjJUMHN5M2lucXdOb3Z3aFZPRmNEOGZJa0I0TjdoN2ZXbFVJWXJGMkJ2VXp2OFNEWUlZV0lFdTVFRFAxOGZWRzU1NmdxdytDdmlRcUZXekdzRkpHTkpqOVowY3ZFRTgrenNISjBSQUkycHEvZW1aTVgyS2xsbnhUUmRJbHFVdWdnVWZoR1h2ejVwY09ZeitUSlhodzBtaFR5NnBGOFA5aTRwblZkUllXdDRmS2NUS3IrY3BzNzFhQmRaUlppSnhSTGlYZDNQVlVGeGRCM2cvR2ZjWU92Y3NFaWNDWnVXOTcyVTJUM1ExSys4U3V0SCtSS0g2NncxKzgyOSsvUEZFL1ZQaXJPVGdic0J3MXYrdmJ6RlpUamNvYTJxeldSOG51NVE4NStsck5yZlkrZkUwTURjaXY1UlJhZTJNMDNmQVBHOHN6Um1idnpUQ2pYR01zK243cjNxNyszM1NxYXNTOEdOelJZbjZOd2JYOVJWREdvYlFoOUh4cUtGWHpXSHRmL0hIOXhnOVYxQytnb002TnNkamRLV1RDNXU3dVJrb0t0NVRVU1ZBU2RqbUdFVVFmMWdaZ1ZLMGh1ZUVEbVUrTnVtKzAyckdZNWUzTHVaQlYzcngiLCJtYWMiOiI1ZjI5YWNlOGYyN2ZlOWRhYTkxNzBmNzQzZmYzMWQ2NzE0YmEyM2Q0Y2QyNTEzZGMzYjQxMDczNzBiYzMyYjgxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii90ZUs2U0xpa2V3Wk5CQXdmOE5kY1E9PSIsInZhbHVlIjoiUnV3Vk5rOVNJSnZvZXpVSUxmWkNUNndKV25WeEJWU3F3OTVFZ0p3eDBoZ1Q0ckkrRmxoY3FDUDFLVE1vY2twbmtzY3lmallQeGZqRytSY0ZlczExUWpzQVlrd3dVZnBMY1FQcnJIQm92WGVCRkN4UkRzVWY4cUFTRUlvRHVodG5qZFV1M1JNNTczWUlvYmdMVVN6UGRkQzZlZnpNMEYvR3JGOTR2ZHJnZ1VYTk1lVGxEK01RTjMwMWozQjY1VlRBS2RSb3pjdzhUQml4TzRQQW9ZT3Zua0lRNUxqVFhTc2pqNXFvS1llVFVDTlNISVlkcSt2Uzd5TWJXaEhxZVVsUHFRSkFMZVlBY1JqL2ZuZ2Vaclg3eHd4Q0k2ZldrWFhBR0o3QU05QWU4MVR1V3QwZCs3TTFGSW15aG4rYlBkTEs2U1ljYzF6TzQ1V3FUQ29DSkJlYi9sQmN0bWR6UlFYbGViMW5vS0JkQmdaYzN1eHRqaXhqaW9IQ2JKNEthanNVMkpEY0F3WlhtS3lRaTJnd3FZYndVRW9FK3J3TzdIVVZUOVg1M3hyL3Z0ZUtKRGh3dlVWWnIrcjI5Uk1LenlFNGhCU2FQOE5GdWprTU5hVWRuSzhwNTJLR0tISXlKL3J6VVVNYkVSZSsxTUpseXUrTUIvRG9Md2h3bVF6eEkyR0ciLCJtYWMiOiI2ZDZiZmFiMTY1MjRkMjMwYjhiYThlMjUxZDkxNGVkNzFjZjA2ZTQ1YzJiMTg3Njc2ZTg3ZDE4ZThmZmRhNWJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkV5NzE1QjdNM2tzL2FuWmFsVEMzQkE9PSIsInZhbHVlIjoiVkpLYis2b1JmRzhTd1hFU29iUlYvS2xSQ1BuNDllcXZtZVFMUDc4MEk4OXl0ZjJUMHN5M2lucXdOb3Z3aFZPRmNEOGZJa0I0TjdoN2ZXbFVJWXJGMkJ2VXp2OFNEWUlZV0lFdTVFRFAxOGZWRzU1NmdxdytDdmlRcUZXekdzRkpHTkpqOVowY3ZFRTgrenNISjBSQUkycHEvZW1aTVgyS2xsbnhUUmRJbHFVdWdnVWZoR1h2ejVwY09ZeitUSlhodzBtaFR5NnBGOFA5aTRwblZkUllXdDRmS2NUS3IrY3BzNzFhQmRaUlppSnhSTGlYZDNQVlVGeGRCM2cvR2ZjWU92Y3NFaWNDWnVXOTcyVTJUM1ExSys4U3V0SCtSS0g2NncxKzgyOSsvUEZFL1ZQaXJPVGdic0J3MXYrdmJ6RlpUamNvYTJxeldSOG51NVE4NStsck5yZlkrZkUwTURjaXY1UlJhZTJNMDNmQVBHOHN6Um1idnpUQ2pYR01zK243cjNxNyszM1NxYXNTOEdOelJZbjZOd2JYOVJWREdvYlFoOUh4cUtGWHpXSHRmL0hIOXhnOVYxQytnb002TnNkamRLV1RDNXU3dVJrb0t0NVRVU1ZBU2RqbUdFVVFmMWdaZ1ZLMGh1ZUVEbVUrTnVtKzAyckdZNWUzTHVaQlYzcngiLCJtYWMiOiI1ZjI5YWNlOGYyN2ZlOWRhYTkxNzBmNzQzZmYzMWQ2NzE0YmEyM2Q0Y2QyNTEzZGMzYjQxMDczNzBiYzMyYjgxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476638579\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImgwbmthSW9ya0Q2UGlZdU42L25PdVE9PSIsInZhbHVlIjoiNnFLOVhVQlQ2bTFobVFZWlpuNU0yQT09IiwibWFjIjoiYmU5NDc3ZDQ4ZDc1NDIwZmE4NmRjNDU5YTQ5NjVmZGYyZDVmYjYyZWMyODQ2Zjg4YzMyZDVhM2ZmYmQ4NjE4ZCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>34</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}