{"__meta": {"id": "X97295623ec485ef34e9f83a9fc4d49d4", "datetime": "2025-06-08 12:55:34", "utime": **********.041104, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387332.80537, "end": **********.041139, "duration": 1.2357687950134277, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1749387332.80537, "relative_start": 0, "end": **********.901977, "relative_end": **********.901977, "duration": 1.096606969833374, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.902002, "relative_start": 1.0966320037841797, "end": **********.041143, "relative_end": 4.0531158447265625e-06, "duration": 0.13914084434509277, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43801624, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00651, "accumulated_duration_str": "6.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.991247, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.639}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.008883, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 84.639, "width_percent": 15.361}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1703699495 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387279672%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImEwek5PaDFBS09mV1lPUmRHTDY2TXc9PSIsInZhbHVlIjoiQlo5V1VucnhUc0NtVWlBZnNEZFZlR09tUUhVVUttWCtXZkx1TWRlOCtUMi9Oa3hiaEZqcUpxOFhCU3ViVUdTVHV6ak5lN0J5T2ZEN0Z3MktDcnNLZHQrV2ZLa2JHWW5tNnREOUw4YTZ6djVLTmJheEdiVExOODRJWWI1b2pieWR1ZFl4aTZ2THBzTm43S0xiMHZwU0dsSDU2V3Z0L2FXU2FhcDRjWmc2aXhOdWR0UWNwSHlkbU1lMGkxMG94dmlLWmpoYVhKQlByVENab3VPWE5RalVPRWx1SVgxNnNaVjgrVWtCSmQ5MTZqTGhLZWVhUVZpdGR3OFp5KzFDTTlZNTNvUnozZFBmZTdzdVRFRWl2TFEyMHBCTTB0bU8yWElTa01uN1J0NWpFYkdDNFRqYTlTSWlEc2tNN1RNWVVWM1FoUTZxUks4Z2JsTGVQNlNRTW04VnFocGF1NlJYaTBoMjU5cDJwYk56QkJUUXhlZE5FZVFjbjgzSFgvbTU5WjNZOEQ4eGRHL1Vwak5ZcVV5Rk5UWldBMWtwTlVYK3llVFd1ZUt1blZvRitUQWphRnVSZk5Id3R6R0d1RjY4T2E0S0lwVzF4dExzQzg3Y3RLMHJEeTFWYjZJeFFVMXFKazB4ZWFjY2dydjZvY2tvbGpqU1crcnBEd3lWbkdnR242QVciLCJtYWMiOiJhNGFmM2U0ZTBkYTkwZGRlM2UwODA5ZWQ3ZmI1ZTg2Yjk4OGJiNDI4ZjQ4YmY4OGQyN2M2NjA5NTYyMjhiNTNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBFTzFNazN3VXdjMHFhNGwxdVpmTGc9PSIsInZhbHVlIjoibE4wNllMem9NSUx3S0UxRER6RVBPbzNydTQ0cmVVL253bFlpRmQ0ZDh3aTZCeWVYQU1uTlRIYVJjK1RaNTQvQy9RQUpoQ2I5aG1RZzBxVTZYdm5DTGI3Q0lZT09mUWRZVU10RU5tb3UwSkNvSHJxcy9hOHJsRk9VdGRpU3VNeWVsZGVmWnJMQ2ZWVTNGZlN1L0V2ZTNPN0hLVURLNFpWSE9pbjFGb25wTDRLRGVnS1pyS0ZZR0lYK1pobElUemk4SlI4a3dKQjVVQW5KRGl1a0owaXVxbzcrUjJkNWZUcjNrNGlLcXF6YkNCUWVtVVQ3M0FVQmdmcnNZb1J2dEEzdlE5d0p2TW9sakF1WldFWVZTazFhTnpyOG1BMjVqSDFXL2txLzZCNURyWEdCWlJ0L2trR1hRc2tPRHhqWGJpZFJQRmVCTmllMGJKaWpZVjZSMUFZbkNyT0J1U3hlL1VoWXBCOGUzdCtobzdraVJTNmtva2Vxa3lmckJzZFIvZStkM0VBVitEdlVFd1ZVRnVDbHp6c1lmUUp3OC9yYTFqaEtGbTZid1hLMnR5SE4yQ1lybUxINCsxMHl5V2M1VFMzNTB0QmlxbTNoSktnYk1rYUg2ZW82cGxTUDdlS3BqRS9PYkFrM0hWQ09EWE1GK3gvTWxNQzdXN0FGOTZ4Mmx5R3EiLCJtYWMiOiI3YWQxNmUwNDBkNTM3ZTFhOTRkM2ZmNmEzNTJiZmU3ODgxYjcwMjBiZDdjYzhjNzg3YWQ2MzgyZTA3NmY0NjA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703699495\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-579098258 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wrC9Uz7KM9WLVzRuZzvV0HYHpXkBofTlHlKWDUIP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579098258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-643677642 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:55:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNqQjViSDZTSHB1M0x4OTFWWHp6UVE9PSIsInZhbHVlIjoiVXFPNkhEdmRjV0RiUjlxdDNUamZoZE4wcklFaktJbTBBR0Y4ZTZHaWZHSXFDMDhIWlc2WFRqSjNyTzlKVi96SWMxZHA4bXFvQ0ZDekN4MnRPWWFBTEFCNUNxbkR1UitHVlJCTWN1WS9EVjcyTzY2cW96YisrVVFlOStndzlTS1c5WTFoR0kzWXREV1VWTzAzcU0wWjFBa1NQaGFxQkUwNnFHL1ZQZjQ1U2MyMlVRdVAxb3pXSkE4cDJOaVhQeE92S04zYlVxNnNGa2xQVVVLZTR0THZTb2FON0FNSGhncHA4aE8wTWJYUy82bWx1V00vNlpOMSsvMzBicUNuOGF0TjFDUG52T25XSG13QThiS2VUbi84dG9mRjdSTlJCMXI2cmRVb1ZKdmRIcFNEUm45MFhHNXcwUXd3Zk05Qmhvb0JGLzA3NTA3SHl5SmxGczlTUUhrdE4wSUY4ejdzZWtUaENWWWdwYzNia3NCZkZGd29CMldpOENZRHZpY3VGVTJqWXQzUjRyMGI4V3Noc3ZTMXRTNXp6MFVsZjg5UTNaT2pOL3lpU2xjeUdxR1hQMGhablU4UEtlZW5qbmovTW4xOW9KclkzUUI1STM0aHEzTDA4VWZOMnAzcHFQK0F4Z1ozMEY1SjFoY0dGcUQwcUt5Q3NMVUFzWG5uNkplMmx3SVgiLCJtYWMiOiIxMzVmNTQ4NmE1YmZmMGYyMDEzZDY4MDRmYmJlNDVhZTZjZjIwNGNmYWYwMDdjZjNmNjcxNThmZWRkYzc4MDMyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:55:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilo0amY0TVhMc3NoYVpiZFFxQVg5Mnc9PSIsInZhbHVlIjoicU1wN2c3UjRORmFCZ3hsbHVxL2c4dUdSdFdhWFNRRTZDVFhYOGowN00vWURxbEJSSVBBZ2MvT01WTk5ySE9rL3hVUnJtYkwyM1JlYm0xUC9idVdRd2ttZ205YWg1ekpYTlk2VGdDbVozNXZuRmYyblZibXFwbDZPcmVwV25jTHdCUUNIRG1NMHplbjUySEduL0h4YXdjVkZhMDR4cHloUTJadGE2SjRTdzRoQ2hDRkJCdUV3WUMycGM1RHZMaklPcUMwcFNEZVh4WU9zQmt3NjN0SXFmaElzSFB5Sm1kaEp4elc1RlhMR29JRjBscTRHZjdpUExYVmFNNWxGSUlRdTg5SmVrcjJOZjRXdG83Vkw4b3o2M3FiWU1zZ0VLWGI2Zy9JZExJWkNUL2ZrNlhjZDBpU3V5dCsrK01QcFFGYmFSbWNFU0RPeklKeWtmUjMwcWtaNW52UG5MWFdadFA4UHAyNFJmaityakNseFV6cEJDUlhSK214QkJCZUtyTXZHWFhmcWRFYWI4V3pMT3FzQ25mUEdFTUdXWmxXeUNEdjFoZDYyeFpJQ05nTkttWmZaSE42djN0TGFwd3RpeUt3RUg0K2NDMWlvUlkrNnlsTEJiS2lSTlJrT0loUGFvazNwZmJ3bE5uamZHbEF4S25RYjdQVi90bTJnQ2FDNk1QbWwiLCJtYWMiOiI0ODVlMzVlOTUwY2UyNDgzYmNlMjVkNWMwMGI0ODRjOGIxODhjM2U1ZjY2MGQxOWQ3OWIyZTlhZWEzMTBmZWU2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:55:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNqQjViSDZTSHB1M0x4OTFWWHp6UVE9PSIsInZhbHVlIjoiVXFPNkhEdmRjV0RiUjlxdDNUamZoZE4wcklFaktJbTBBR0Y4ZTZHaWZHSXFDMDhIWlc2WFRqSjNyTzlKVi96SWMxZHA4bXFvQ0ZDekN4MnRPWWFBTEFCNUNxbkR1UitHVlJCTWN1WS9EVjcyTzY2cW96YisrVVFlOStndzlTS1c5WTFoR0kzWXREV1VWTzAzcU0wWjFBa1NQaGFxQkUwNnFHL1ZQZjQ1U2MyMlVRdVAxb3pXSkE4cDJOaVhQeE92S04zYlVxNnNGa2xQVVVLZTR0THZTb2FON0FNSGhncHA4aE8wTWJYUy82bWx1V00vNlpOMSsvMzBicUNuOGF0TjFDUG52T25XSG13QThiS2VUbi84dG9mRjdSTlJCMXI2cmRVb1ZKdmRIcFNEUm45MFhHNXcwUXd3Zk05Qmhvb0JGLzA3NTA3SHl5SmxGczlTUUhrdE4wSUY4ejdzZWtUaENWWWdwYzNia3NCZkZGd29CMldpOENZRHZpY3VGVTJqWXQzUjRyMGI4V3Noc3ZTMXRTNXp6MFVsZjg5UTNaT2pOL3lpU2xjeUdxR1hQMGhablU4UEtlZW5qbmovTW4xOW9KclkzUUI1STM0aHEzTDA4VWZOMnAzcHFQK0F4Z1ozMEY1SjFoY0dGcUQwcUt5Q3NMVUFzWG5uNkplMmx3SVgiLCJtYWMiOiIxMzVmNTQ4NmE1YmZmMGYyMDEzZDY4MDRmYmJlNDVhZTZjZjIwNGNmYWYwMDdjZjNmNjcxNThmZWRkYzc4MDMyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:55:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilo0amY0TVhMc3NoYVpiZFFxQVg5Mnc9PSIsInZhbHVlIjoicU1wN2c3UjRORmFCZ3hsbHVxL2c4dUdSdFdhWFNRRTZDVFhYOGowN00vWURxbEJSSVBBZ2MvT01WTk5ySE9rL3hVUnJtYkwyM1JlYm0xUC9idVdRd2ttZ205YWg1ekpYTlk2VGdDbVozNXZuRmYyblZibXFwbDZPcmVwV25jTHdCUUNIRG1NMHplbjUySEduL0h4YXdjVkZhMDR4cHloUTJadGE2SjRTdzRoQ2hDRkJCdUV3WUMycGM1RHZMaklPcUMwcFNEZVh4WU9zQmt3NjN0SXFmaElzSFB5Sm1kaEp4elc1RlhMR29JRjBscTRHZjdpUExYVmFNNWxGSUlRdTg5SmVrcjJOZjRXdG83Vkw4b3o2M3FiWU1zZ0VLWGI2Zy9JZExJWkNUL2ZrNlhjZDBpU3V5dCsrK01QcFFGYmFSbWNFU0RPeklKeWtmUjMwcWtaNW52UG5MWFdadFA4UHAyNFJmaityakNseFV6cEJDUlhSK214QkJCZUtyTXZHWFhmcWRFYWI4V3pMT3FzQ25mUEdFTUdXWmxXeUNEdjFoZDYyeFpJQ05nTkttWmZaSE42djN0TGFwd3RpeUt3RUg0K2NDMWlvUlkrNnlsTEJiS2lSTlJrT0loUGFvazNwZmJ3bE5uamZHbEF4S25RYjdQVi90bTJnQ2FDNk1QbWwiLCJtYWMiOiI0ODVlMzVlOTUwY2UyNDgzYmNlMjVkNWMwMGI0ODRjOGIxODhjM2U1ZjY2MGQxOWQ3OWIyZTlhZWEzMTBmZWU2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:55:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643677642\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1650498744 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650498744\", {\"maxDepth\":0})</script>\n"}}