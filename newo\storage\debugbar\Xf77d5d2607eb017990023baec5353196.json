{"__meta": {"id": "Xf77d5d2607eb017990023baec5353196", "datetime": "2025-06-08 13:16:02", "utime": **********.468141, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388561.081768, "end": **********.468182, "duration": 1.3864140510559082, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1749388561.081768, "relative_start": 0, "end": **********.2976, "relative_end": **********.2976, "duration": 1.215831995010376, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.297621, "relative_start": 1.215852975845337, "end": **********.468187, "relative_end": 5.0067901611328125e-06, "duration": 0.17056608200073242, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45581800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00791, "accumulated_duration_str": "7.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3845868, "duration": 0.00535, "duration_str": "5.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.636}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.416613, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.636, "width_percent": 15.297}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.435195, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.933, "width_percent": 17.067}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos?only_available=1&warehouse_id=8\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1950136110 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1950136110\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1221341042 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221341042\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-362354937 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">http://localhost/barcode/pos?warehouse_id=8&amp;only_available=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388514803%7C18%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlROZ1djUjI1Qmw4NUJTeUdrN1hEOXc9PSIsInZhbHVlIjoiWU1udktJVmNCRGRuQXZEZndWMjJidjVOelEySWpIZHcrcVVsT29lUjh4Wkp5N2VCTWRFQjVjbWthZXdNVC90ZzF3MzQwUmJiZDVaVkRqZDVvandnNkNHTWdwS0QyaFEwMzVLRVBoYXd1TmdWaWpaME9IUU1TWjNuWGNjZ2RWTElaRnJ6SDdIQVpaaDBVcWhZckhNUEVpVkczNTNrK2VtSXFPbzN5RmY5akwyS0JnMWwwZ1MvTnQzaktBNGtObzRlYmtySWZxVjg5NVhyNklDUnpvOWFJRTIzNDNEd2xZVEN4ck44Ui9QcEdDTU4zR1JmdHJicXZLTjVqY0s4QmtoSmJpY3M5NjA1WFhLOEhGa1ErSDhKZ3NRalFIb3BYdjQrSWhIaUtGYnI2Zi9ZMXhsTDVibmQvd3d3YXVET1hMck9iMHRyeDdsTHRGSS9hbWZ2WlFDazZxY0FScFp1Z2hMQ0wzcEhBNGVrNEhHUzVWU0kxOHY3R0VmWEI2blJjQXRYcjZsVHJBeWRvMlU0K2dXMG84V3Qyei9KcVh1SGJ3MUNUVlh4UXllREVYOHlqOFBqenFIR1hiMTlVbjVKNDNya1NGS0Y0eVF0YmxpbFhUYXowcVc3RWNKNFp3RDkzK0xURFF1Y25qVHpCOGR3alIyTHVxM1VxTjU2dWNQbG9QNTgiLCJtYWMiOiJkZTI0MmE0MDBmNDAwMjI3OTcxNGVlMThlMGM0YThiODIxNzAzOTg2MzdiYTk3YzJhYThhOTBkNTkyMjg4ODBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlzU1EyYVdFRUR6c053TGRsRDNJOVE9PSIsInZhbHVlIjoiMzBWa3dYNXVoYmtLaUdVWGNPZUlta0NheWpDYlFudWJvcVZtM3E2ZDlld0M4UTN4cmg4WVBEc0Z6cGlOc2g0b2NtYzNpZ0s4cFlNYi9jWUlVVkpNQ0NoYjdSeDRPMzI3RWFzQVVabWp6SmF3QWFBMUdCVURCMVJBQjFqUW1aaGdtRDBzTVJ1dWhhVTNubE9RWmUwdU91dkYzTkhIc3hob2h6bzNnMzJTTWpYQXROR3ZLVDYwTUFKWEo5bVUvd1JveTNocCsrM3hqdWx0T1NJUHhTc2NzS2RIRWxlQjBENkVGeWkrTDRESnF4TVdtOTR3RlJMOExDdjB6aEFVMTN1NUJ5YUVwa1ROZkFSNlhYOC9JRUhibklhNVdwTDNmL2ljaTY3SDU3d0g5a2RaRHBibGl0MitpQXord2JFcHNqbkFLb3pHNWl3ZnVMc2FBdkk4cGtwY0Z1Q0RvaE1kSDl1bTNKdG11R0xIMko1NG9xN1o2c3VVMlJmekVjVVRHWXhyWExveEZNOWpLYi9pSUZrRnk0ZWFweU1nVWh4R1V0RmZBd3hTUEgrNm1YeTROcFo1R3hTNklMaHdWNkkzdGNRMUZlVUJvUVZ1MWNDTXh0NERBVEZCMXVkckNFbm9tbXlmRXBoSGFyWlFnejEyQm0rTWovS2NhWG5DcDE4OG81QXoiLCJtYWMiOiI3NmNmYTVhZDgyMWRkNTEyMDNmMjYyMjllMmUyNWZhYTFlODlhMDRjZmRjNmRiNjFlMjE4NDA3MWM5NGRjMjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362354937\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-610089922 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610089922\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-585761580 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:16:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9oZ0tiaFBxUzRMbi9yWWNwc0RDTXc9PSIsInZhbHVlIjoib1h4MXRXTHhDL0dLZ1BKak1OSUNHOFRTTXZONXAzSlE2TDRRMVNuRjFuQUFPV2JETWp0Ym5vWHJ4Q0FwRldPUFVCNnZ6Ti9kRmVYelpZdktlWFlUZWhyVGc3YjQrbnZIUUQ3c2NVV2lYQ2owNi9mNisyRDhFaTIwaDBXemhmNmdjN0tuaXFnT0VMZzNRSkZaYUhKTVRXU3NzVWhqRVlFTENsSTgxMXhyZmMxcHFTUUZidTIwbXN4MG1VVVM3RCsxVUVSMnFEY3hMZ0taeUg1eUtJS0xEZ01seEdJWXBnaC95eTVLYVdQTG8vUExvV3BLSXlzOTNHaWZlT0xzcGJKTjlJajhEdEtlMkFITDZmbmxWZGVLVHJwekcwNjVJUitoMFA3M25PR1BNRTZxeEZhOUl6VU9IWVhRODRkV1RNRExXQTkvWk4rVHR3dGphQjZDUElzU2QzUkEvRUhvMnVFTE5HL0ZtS1ppeE5ic0RZOC8yM3N0enA3MXVFemRIWkJrbVFiWm1TZDVHZVYrTDZJQ1IzTjBLUVhiMkE3ajUwWDIzU1lsMWNLQTZBeStMQ0ttaDZyRXRud2RDZFl1MmRPUXZKZzFTVnFoUHJWVlpkbzFUQ0Zwc1J6MU5vS053L3VZdklYTm5MS2Y0RWhXbUJSQWt6U1dNTzZ2S2hoVE9OMWciLCJtYWMiOiIzZGQ4NTljY2I2OWI2NTk4ZTg3OWQyMDk3N2U4ODMxYjJhZmY2Mjg1MWRmNGFmODk0N2U2MWEzNjExYmQ4ZDU5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:16:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRzUVY2ai81bk5ZVHB6eW1NYjgzR2c9PSIsInZhbHVlIjoiaTlualU2alBGUHpWZWUyM0lCUFdrd0s3Qlh2dGlWbmk0SzA4UldPMVV2YTFqbExPVGhPMHNQT0NSc1ArTUpzVUZWWFcxYXAxNUNaVHJKeDFScmJFSnRnNWdNTC9LbzZJaExoYVBKTVVDL3A3ZU90NHFKK1JpV1VGZjJQYkZVRWlOY25qWk1rN1QyUnJGaWh6STFVaDV1VlllUmJZMThzRlJRVjdyT29XUTA2QUlsTG8zQ1NJeVVIMUE3b2pqMTJjRnVEK3RqTzFQR2c3Z1FtZnowbEV2S005Sjl2STNpak91cmdzRG53WGRoc1c2N1d3NndRR3JJci9oNExrUTJkWDNBbkpKUklZNFZqeUFtTE9IR29sUlhaRi9jREJaT2dFQU1XKzhPVjJqT0ROQ3Z3UzV2cHRhQ2k4M0Q4UWNUYm1UMUppLy9wajJOZklOY2ozdkpLQWptU0U1bzU1U0lnS0t1WmVWc1dXRGdUeU8wazBydkFqbE12bTErZE1zNG01bHA2NDNpbGNTcUE0c1BNN2h0bFNodnhrU2VWZzd2RzhhS0dzc1JCMHh1RjhTNU1uWk1yTW5xL2xLMEk1Zm1TVUs3SkR4N0xrNjhiOXR2UDRRZER4YnozcWlZMjYzMFRCbys0cTlWL2YzQS96b21icVNWNTQvUEVoMmdFY2ZnakYiLCJtYWMiOiI2NDgzNTM0Mjg0MDg0OWUwY2RkNmM5OWI0ZmY1MTUzZmZjYzgzYzIyYTA0YTdhM2FmZWM3ODJiOGNkOTlmYjRhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:16:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9oZ0tiaFBxUzRMbi9yWWNwc0RDTXc9PSIsInZhbHVlIjoib1h4MXRXTHhDL0dLZ1BKak1OSUNHOFRTTXZONXAzSlE2TDRRMVNuRjFuQUFPV2JETWp0Ym5vWHJ4Q0FwRldPUFVCNnZ6Ti9kRmVYelpZdktlWFlUZWhyVGc3YjQrbnZIUUQ3c2NVV2lYQ2owNi9mNisyRDhFaTIwaDBXemhmNmdjN0tuaXFnT0VMZzNRSkZaYUhKTVRXU3NzVWhqRVlFTENsSTgxMXhyZmMxcHFTUUZidTIwbXN4MG1VVVM3RCsxVUVSMnFEY3hMZ0taeUg1eUtJS0xEZ01seEdJWXBnaC95eTVLYVdQTG8vUExvV3BLSXlzOTNHaWZlT0xzcGJKTjlJajhEdEtlMkFITDZmbmxWZGVLVHJwekcwNjVJUitoMFA3M25PR1BNRTZxeEZhOUl6VU9IWVhRODRkV1RNRExXQTkvWk4rVHR3dGphQjZDUElzU2QzUkEvRUhvMnVFTE5HL0ZtS1ppeE5ic0RZOC8yM3N0enA3MXVFemRIWkJrbVFiWm1TZDVHZVYrTDZJQ1IzTjBLUVhiMkE3ajUwWDIzU1lsMWNLQTZBeStMQ0ttaDZyRXRud2RDZFl1MmRPUXZKZzFTVnFoUHJWVlpkbzFUQ0Zwc1J6MU5vS053L3VZdklYTm5MS2Y0RWhXbUJSQWt6U1dNTzZ2S2hoVE9OMWciLCJtYWMiOiIzZGQ4NTljY2I2OWI2NTk4ZTg3OWQyMDk3N2U4ODMxYjJhZmY2Mjg1MWRmNGFmODk0N2U2MWEzNjExYmQ4ZDU5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:16:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRzUVY2ai81bk5ZVHB6eW1NYjgzR2c9PSIsInZhbHVlIjoiaTlualU2alBGUHpWZWUyM0lCUFdrd0s3Qlh2dGlWbmk0SzA4UldPMVV2YTFqbExPVGhPMHNQT0NSc1ArTUpzVUZWWFcxYXAxNUNaVHJKeDFScmJFSnRnNWdNTC9LbzZJaExoYVBKTVVDL3A3ZU90NHFKK1JpV1VGZjJQYkZVRWlOY25qWk1rN1QyUnJGaWh6STFVaDV1VlllUmJZMThzRlJRVjdyT29XUTA2QUlsTG8zQ1NJeVVIMUE3b2pqMTJjRnVEK3RqTzFQR2c3Z1FtZnowbEV2S005Sjl2STNpak91cmdzRG53WGRoc1c2N1d3NndRR3JJci9oNExrUTJkWDNBbkpKUklZNFZqeUFtTE9IR29sUlhaRi9jREJaT2dFQU1XKzhPVjJqT0ROQ3Z3UzV2cHRhQ2k4M0Q4UWNUYm1UMUppLy9wajJOZklOY2ozdkpLQWptU0U1bzU1U0lnS0t1WmVWc1dXRGdUeU8wazBydkFqbE12bTErZE1zNG01bHA2NDNpbGNTcUE0c1BNN2h0bFNodnhrU2VWZzd2RzhhS0dzc1JCMHh1RjhTNU1uWk1yTW5xL2xLMEk1Zm1TVUs3SkR4N0xrNjhiOXR2UDRRZER4YnozcWlZMjYzMFRCbys0cTlWL2YzQS96b21icVNWNTQvUEVoMmdFY2ZnakYiLCJtYWMiOiI2NDgzNTM0Mjg0MDg0OWUwY2RkNmM5OWI0ZmY1MTUzZmZjYzgzYzIyYTA0YTdhM2FmZWM3ODJiOGNkOTlmYjRhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:16:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585761580\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1938476140 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">http://localhost/barcode/pos?only_available=1&amp;warehouse_id=8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938476140\", {\"maxDepth\":0})</script>\n"}}