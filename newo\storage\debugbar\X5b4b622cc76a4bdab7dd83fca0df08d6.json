{"__meta": {"id": "X5b4b622cc76a4bdab7dd83fca0df08d6", "datetime": "2025-06-08 14:57:03", "utime": **********.832464, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749394622.990644, "end": **********.832499, "duration": 0.8418550491333008, "duration_str": "842ms", "measures": [{"label": "Booting", "start": 1749394622.990644, "relative_start": 0, "end": **********.63696, "relative_end": **********.63696, "duration": 0.6463160514831543, "duration_str": "646ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.636973, "relative_start": 0.6463289260864258, "end": **********.832502, "relative_end": 2.86102294921875e-06, "duration": 0.19552898406982422, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53144512, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1320</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.02013, "accumulated_duration_str": "20.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.704928, "duration": 0.0127, "duration_str": "12.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.09}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.737056, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.09, "width_percent": 3.974}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.768575, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 67.064, "width_percent": 6.259}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7739, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 73.323, "width_percent": 4.918}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7839398, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1236", "source": "app/Http/Controllers/ProductServiceController.php:1236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236", "ajax": false, "filename": "ProductServiceController.php", "line": "1236"}, "connection": "ty", "start_percent": 78.241, "width_percent": 3.924}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3, 5) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.789119, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 82.166, "width_percent": 5.117}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.797591, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 87.283, "width_percent": 6.657}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.802315, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 93.939, "width_percent": 2.981}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8183792, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 96.92, "width_percent": 3.08}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-212125338 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212125338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.782786, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1440755212 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1440755212\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1297335604 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297335604\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1109645347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1109645347\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1592420915 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394379618%7C66%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpDYi9Dc2pETG9nYnk5bjlSdnl2T0E9PSIsInZhbHVlIjoiK3Ztc2lXZUx5cWVFeTJrZUcwRWZGdmpMbkk3RUlZcDFkcFRLRXpoR2hyMFdReUtOSE1mQlZKb0RPaEJpcFF4Qk9sNnNTSS9vL0NwZjVkNmJNK3JpSVc5dk5tTWFPWXFWRC8xaGJFVjE3ajk2dnNlWWFJTWFmekFoU1plWTRUZU1PU1g3aUU0TXBjb3o5TmZMMjlaV3c2K2pjckc3OWdRZHVMTHc3OS8wdUtTbnZTdHcxRmFESWIzZG5wNktOaWR6Y0t2c2hudWNxZEFEeUtCQXFIMWIwUXhsR1p5R2FETExtL0gyM0w2bzh6NmQyUmU1WmIvRGdOTnY1NkJpVlhmQUNlSXJPMkVsMitHdi83RmM1YUNnWTA0WnJSMXVEV3BMYWllY0lXL2RBT1Y0eXdsc29mZVM2VHNFZTluUnkzT1REMVgvcEdYMFpPNS9QOWVMMkh2VCtaRC9uZVNMaitucVdMVnhSR3Q2cXlhNHh5VndiL1o3MFVWaWtsblRIREJUSEFiZFFmOXJRNFRXU280RTVIOWJWKy9BT2o4Tm96Rmp6b3NpdHVjRjNTb21wNjJrVHhNT3JGckxwSVk4NXA5aU1OUTA3YUF4S3NWTnZoL0RSazQ1d3JoRHBzdGM4R0lvRmRRWnJveUxRS2hWR3dxNURjS0RjdFNuZmdudHZ5MUYiLCJtYWMiOiIyYTQ2NmI2NWY3ODNjMzRlYTIwYzA4MjE0YjdjZjk0YjM3MmM3MmIzMjkzNTUwYzRhOWJmMTk0MjZiMzFkNzA5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjF5WlgxbUd2WFZWU3FncnpHNmYyS1E9PSIsInZhbHVlIjoib2RKejN6c0hBR3JtNy9SYnU4bDB5RkE5MmdVNmNHa1grSjM5MFkyaENwajBRcjU1eG5ja0JmQWxici8xYVVtT2t5TWpQTlZ5bXdHcjUzcnc1aWZNNjB2ZEtEWEUrcC9SR0wxNDlBd2pvS3d2UStaWnpPZWIxZ1F4MTRrZTNjTm1lc1VqRlk2aFlCaUJPNFU4bmk4WmRQRUNhanZmYkNtcVRQNnFiUTBtaTZ0V3UwbjdETE1xMUtOa3Y2N1VTKzRxSG80SFJaWHNGSkh1ZC91bFA0Q3hUcS9iMWFaV01VR3hMOGFQOGp6YzY2by9QZ3Jjd3VvaW5JdkN1bkgycC9hRjJ1STQzYjNsdXpSSjZRdkV4NXQ0Y090YUZGYXYyUWlpV2ZsTGM5N1NFVXZtYWdzbHZpYmxBR21iS0xsblBwV0YxNzhpQ3orMjNFVlVOY3YzMVFJMHZpU3dyM2Ftd21WSVNBY0docE1rditnU1g2c3BWRHR4aERSWnJUbVdJaUYwcVY4WEpCaWRucVFRNVJmdjNtcHdUd3ExdmlYclpLU0htL1k2dVVPQWt1TDlLajNxcVh6T09tWlRNN0N4Z0pnQXl6N2FpODJ0TmwwR1h0Qkgwei80OXhkb0VFVDh6YzBvS2sxS3c3TEN2Ni9CUW1EWVh5OXI5N21KNVZwSUthU2YiLCJtYWMiOiI2ZDgxNGE2YWE4ZGVmN2RlZWJjMTI5NzRlNGE3M2QyZTVmNTcxOGVhMTYwNzRhNDE5NDk2N2M5MjM0ODNkZjA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592420915\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1473477125 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473477125\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1464569701 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:57:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldJdzlzdEtVdUxlKzlNMFBMYkY5cUE9PSIsInZhbHVlIjoiamdUWVpxenRkQlJPc1YxOVJad2lpUm5mM2YwbzIyUVZzWC95VWk0dUdsQk5PRlVvd0gxMjFxWlFSSm5HcDhQOEVWdjR5UExhNCtoOW8zZ1RiNVc5QmZ0endFZVNobWxIQ1R5UGszVHhjMG9QUVRsKzR0N0kyVk9HUG45cHBJOUZ5ZjZyamNhSWszY09aNHNUK0ZrOTRZeGVWSTVZeWwveXdlTTZzUFZhNFhFd3Y4NDVLSHV3cDQ1UHVpeFRoZHJDSnlML2Fna2lPY2lmYUhLUlk3TU9mQkpybTkvZ0MrMUFSUmZKTTR3TEk2ZzFMdkVubFh1TmpPWGNtbUdvMlZuMHQwdDFYbFp5dTQ5UGx2cU5tK2tyVGZDOENEcWVKODJreVZJU1IvZDU5b3FBejAyZ2piWmhTR01IWXpjV3cxcUZUNnZSOFNEbXRNRExwS3dHM2U0amxWeXVtODVwcE1oQ3BmOCt4UkxOa1EwWTlMazdNWjVDTjFMVDFCQThFdVFxaTRFQkdRU0NhUGFQZ3lQVk56dVVoZ2ZxWkczeC9GTG9ySG1OM2FFM1FxNVcrV0lVS2pNVjN5KzNsZGdSUkxPc0NCRGYwbWlIaUdUTGFhMkhCMTlnbWpRRE9ucDNvekYwMkdpSTZ6bkVoczltNTFrbExYSDJNZ1kyMVU5MndmUXEiLCJtYWMiOiJkYzc5YTUzMTIxYjkxNWM0MWM3MzExYTUwMWVmOGIzNzY2YzQzOWY2YmExZGY5Y2JkOWYyMDg1OTEzYjg3MDJlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldpK3FqdWVGZStmS1UxYkNrbFphMkE9PSIsInZhbHVlIjoiMXFaTUpod1FMYUF0bjlTb2VLR01kVGlVMERVR3p6NjhFVzUyalNjWVRKalI3Nitad29rU1poV0kzM2tleldkODQvTXhzZmFUZmtnVWkwYWZiWW42TEFpb01WeWgrd0dYcUU1OFFaa0ppcUJkODF3YW0yalE1SW5MMzVXcGc2dk1DUnlURjVRdjVrc09oRG83TEU0TFVkcEF2b3ZNNDhxSisyeE1LT04xMUZjODhFWEtIZSs4L1JIeUg3Unl2UFZVc0g4R0JqVUUvOGRzcFdxM2tZdzJVSTFMNWlXNUFJVk9vOHBhdHBXc01uNkdUcW5tYmoxUnNReVNPN1JxSGZxZERRczliUTRUUGdVS1hOWlpTd1VkdmNSY1BlTnhhVU1pWWh6SzJKS1JQMXI3eHQ4MDFXbU9tTmdvQnFkQUxleVBaS3BKdkVlVzhyeGV1TzhlUmRBdi90aW5FQkhVTGxhMERwTm9FMkYva1gxT2FQdVhKMWpaWUpwVWZXUkVmSnNrSENFKzdJeFhETEQ3VGpxWENzUHhjeVBRRG1xaGppQXlRcElkUWk4Z1ZCS1lPVWRLV01xTHU2TXF0YnNSUmZwbGVMcUdYcWoyUnF1TzgwbzRlUkRPRXF2Wm95YlNHUDhRVjNNdzlWWU1iS3paSnF6amlsWTlmc244aGVQVEcrT1YiLCJtYWMiOiI0NGM0YWI3Y2QxMWE0MTgzNzQ2YmQ0MDg0YmFiMTRlMmNmMDc1ZmIzMDY3ODk5OTFlZmZiNGY3N2MxMzc3OTY1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:57:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldJdzlzdEtVdUxlKzlNMFBMYkY5cUE9PSIsInZhbHVlIjoiamdUWVpxenRkQlJPc1YxOVJad2lpUm5mM2YwbzIyUVZzWC95VWk0dUdsQk5PRlVvd0gxMjFxWlFSSm5HcDhQOEVWdjR5UExhNCtoOW8zZ1RiNVc5QmZ0endFZVNobWxIQ1R5UGszVHhjMG9QUVRsKzR0N0kyVk9HUG45cHBJOUZ5ZjZyamNhSWszY09aNHNUK0ZrOTRZeGVWSTVZeWwveXdlTTZzUFZhNFhFd3Y4NDVLSHV3cDQ1UHVpeFRoZHJDSnlML2Fna2lPY2lmYUhLUlk3TU9mQkpybTkvZ0MrMUFSUmZKTTR3TEk2ZzFMdkVubFh1TmpPWGNtbUdvMlZuMHQwdDFYbFp5dTQ5UGx2cU5tK2tyVGZDOENEcWVKODJreVZJU1IvZDU5b3FBejAyZ2piWmhTR01IWXpjV3cxcUZUNnZSOFNEbXRNRExwS3dHM2U0amxWeXVtODVwcE1oQ3BmOCt4UkxOa1EwWTlMazdNWjVDTjFMVDFCQThFdVFxaTRFQkdRU0NhUGFQZ3lQVk56dVVoZ2ZxWkczeC9GTG9ySG1OM2FFM1FxNVcrV0lVS2pNVjN5KzNsZGdSUkxPc0NCRGYwbWlIaUdUTGFhMkhCMTlnbWpRRE9ucDNvekYwMkdpSTZ6bkVoczltNTFrbExYSDJNZ1kyMVU5MndmUXEiLCJtYWMiOiJkYzc5YTUzMTIxYjkxNWM0MWM3MzExYTUwMWVmOGIzNzY2YzQzOWY2YmExZGY5Y2JkOWYyMDg1OTEzYjg3MDJlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldpK3FqdWVGZStmS1UxYkNrbFphMkE9PSIsInZhbHVlIjoiMXFaTUpod1FMYUF0bjlTb2VLR01kVGlVMERVR3p6NjhFVzUyalNjWVRKalI3Nitad29rU1poV0kzM2tleldkODQvTXhzZmFUZmtnVWkwYWZiWW42TEFpb01WeWgrd0dYcUU1OFFaa0ppcUJkODF3YW0yalE1SW5MMzVXcGc2dk1DUnlURjVRdjVrc09oRG83TEU0TFVkcEF2b3ZNNDhxSisyeE1LT04xMUZjODhFWEtIZSs4L1JIeUg3Unl2UFZVc0g4R0JqVUUvOGRzcFdxM2tZdzJVSTFMNWlXNUFJVk9vOHBhdHBXc01uNkdUcW5tYmoxUnNReVNPN1JxSGZxZERRczliUTRUUGdVS1hOWlpTd1VkdmNSY1BlTnhhVU1pWWh6SzJKS1JQMXI3eHQ4MDFXbU9tTmdvQnFkQUxleVBaS3BKdkVlVzhyeGV1TzhlUmRBdi90aW5FQkhVTGxhMERwTm9FMkYva1gxT2FQdVhKMWpaWUpwVWZXUkVmSnNrSENFKzdJeFhETEQ3VGpxWENzUHhjeVBRRG1xaGppQXlRcElkUWk4Z1ZCS1lPVWRLV01xTHU2TXF0YnNSUmZwbGVMcUdYcWoyUnF1TzgwbzRlUkRPRXF2Wm95YlNHUDhRVjNNdzlWWU1iS3paSnF6amlsWTlmc244aGVQVEcrT1YiLCJtYWMiOiI0NGM0YWI3Y2QxMWE0MTgzNzQ2YmQ0MDg0YmFiMTRlMmNmMDc1ZmIzMDY3ODk5OTFlZmZiNGY3N2MxMzc3OTY1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:57:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464569701\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1154319295 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154319295\", {\"maxDepth\":0})</script>\n"}}