{"__meta": {"id": "X1a18521bd68d2818be98927082434199", "datetime": "2025-06-08 13:15:08", "utime": **********.206673, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749388506.952108, "end": **********.206707, "duration": 1.2545990943908691, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1749388506.952108, "relative_start": 0, "end": **********.057101, "relative_end": **********.057101, "duration": 1.1049931049346924, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.05712, "relative_start": 1.1050121784210205, "end": **********.20671, "relative_end": 3.0994415283203125e-06, "duration": 0.14959001541137695, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45596544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007199999999999999, "accumulated_duration_str": "7.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.131129, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.917}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.16222, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.917, "width_percent": 15.833}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.179038, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.75, "width_percent": 16.25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/barcode/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 2\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 24.0\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  3 => array:8 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"3\"\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1323276475 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1323276475\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1291695778 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291695778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1248929783 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388152047%7C15%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1SbTZXOXRlVEYyZ0VhekZXa3RNSGc9PSIsInZhbHVlIjoiZnJwUmdqSHNCYUdhSHJkTm9DM0tZTUJ2RkJmbm1hK2h5YmQ1enBqMkdGeXdqUVVQOXdKMEp3UTVzampVanNMakVQV3RnczZ3MXJqbHBnUzZMWFhmZGxlOFI4aXpmNE9YeHY0UFE4cUVWSWRrS2VrSDIrYjFTSDcwVy84YVRTYUNNemtyUkg5dzFDMGx4anVGcUp3MFJXNHBZRVFwV0FYc3RCMUhHVHBHZWhmQ3RYcmUwTmZpakREaFRwV2UrV1FMUUVGZ0ZyaFlDb1dNaks5eGs1SUdYcDBLblFXOW5zY1RacHBrVURqMDRKeXl1VndxNWo4RDhaY2ZTNHF5R2MwVElKcVJjU0lZQ3hrSDRmcDltOUg1bTN5ZTcwUTI2dThnZVpZN2tmcmNPUmJVMjhQcXdtdFFaRkNodTJYd052a1g0eHJFdXdtMkxaeWd6ZFB0NW1qUTBmTWxiRURUTkQ4eW5vaVBuSVJWZWZKNXhkVVR4SHo4TmRIcjdKQ0thczh1SnI5dForWTNYNEdabWlqQ2FFYkp1dncyTTVybXE0djRPRDRSMTNrMFJhdFdLOGpmWEo5T3NzMmxxdE5xeFRLbFFob0RyS3ZxWURzRnNUekVrWnQwQVFOMld6bEo4WjZpa0tmbWJBakNyRVpLdE5XdjVHVkVBNDJmQzVucTlQeTAiLCJtYWMiOiIxODAzZWE0MWZmMGRmYTY2OGZjMjJlMWZjMzQyZGRkN2JmNGNmZjc4ZmY0NTliMTliODlmY2Y0MWY4ZmNhNGZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im9LdzZBUVQvTXFDRFlRRFo4bkhIV0E9PSIsInZhbHVlIjoieHpYRmM3bG1tdFRkbmNCdmxCd0g4REx3dzdFYnkzdmJhczBsZC9rTEZKdEdyMVBiME1QUkVkLzlFYmx1aGo2TWZ4cFNuMC9GbkpxUHBnQy9EN3g2cUJOdE84b1YzREF0NFA4ZjRrQUxyZC9vanEvWGpMNCtIc0FhR3ZpVHRwZDgzbVhqMEZ1aGN4RG5OVllMS0k5YU5GRGtSakpYQTBBSWpjZndVQkxmS3FrdUtxaFdjWTFwUHN6d0xWS2h1cW0wVktscC9zbXp4L0VtcUxmcDR2bmY2YkVFa3ppN1J0NEcyTHVMZEJTNXIxY0FIS2t2cGZ1a2dIRGhjWERYekJWdEVlMzFDK1B0RkhveUNFQjVJN2R3NktnNTRRUmF4MEMvelZFNXhZZWJqNVo3S0ZCK0RNNU84VlVHUjB6Z0lJbEx0Q3F5cUkyNmNYVGxBVmtVaXdVd3UzMWh6bEpSS09kMmpaaGhpZFJUNTdiSkVxemplWkhEKzUvOFpZemlZQktWUEUvT2VUL3FBL1NhNG5EQkhNRFYyVVptRlZvbjlCbUQwSG5WMFJWSERQWWQwdzQ1QXF5Q05KQmZzR0U2QkFDbTFjWkI4bTV0WXd5bTlWcnpQUXZEUnVDLzJvUTdKUTJvS20xNm1wMzk2SElvbmRDeHhhWFJWQWRpbkIvbFhiQlciLCJtYWMiOiIyM2VmNTkwMDg4YjRkNDAxZjhhNDM4YjdlOWI2YmM0MWNiNGVjZTlhZTk1NThmYTViZGZhMTQ0NGUwNDg3ZDlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248929783\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1803585409 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803585409\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-878657205 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:15:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUxdkoxeDFEODJUM2cvS3l5WkkzM0E9PSIsInZhbHVlIjoiWXBtM0g2eit2Y1lSdHdudkdRSjMyR2gwandCRUd2Rzd2ZDBLakRvS25rcUZJTkFtS1JHNlJ6b2lxN3ZaREtVaDU2eW04TkkydFRscTZMQlBnSFQyV2lmNDJaMTBZRWhQUHhndWFvR2hzNXA1SEtUcGlOcS90SUxhaGlhU3BiaHRZbFk0Y3l5Q21vbmxkeUM3aU9wTjlzU3pVdk5GOWxHd25HZ3RVR0NGUTdVbTlsb3BxSTBoN3JxcHFSVU1ETFE1NW9MZTFYQy95Y0p5Y3h3bU9ORnRyaVIvZE44VSs2bFdtQmJHS2l6cWhuOFVTM3JIbkpjUlBpSTM0bitRakR0cjdEbXhob2RSZ3ZXeXlzenRyNkFQckU3WjE2SU1rdVJZL1hDMkU0cVMzRHlyNEJ5UzhSZ2ZheS9DeFdQVTBjOXFnM0NhS3BQdS9pWkVUS2k4ZU1FZGtLMUFaaVZFNXdwZkdyT3loSDM4TWJ1V3M0SHg0dXdVMFdpbGJTNFZjaFZwSWt0T1VZcGpuUnh5eW5sR29KUDdmTFNvRUNYZmRLZXQ5MGFwaGtMQkl4YlBCWllIOEtMNzZtQWR3dWI0OFlqSE1pLzlQS0dOS3hhWHdUZ0F6UEJkOHJJRzhaeFlhL0pzL05ZdE1FbDFTUXkxREt3L3JYd0dFQnEwTGhiVmhmdVkiLCJtYWMiOiJlMjM0ZDljMzFmNDM0MzBhNjM4YzU1Yzc3ZjE3YTcyM2VmZjgyYTEwMzdhZmJkOTBmYWRmOGE2NDI0ODc1ZWMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InYwcW9pcnNmTWhEZ3daVGR3RjZ6VWc9PSIsInZhbHVlIjoiWWRLN3hDVU1RWXhyQVJqV3REeGJIUWJacFJqN0NRMXhVQVdBa1EvbC9mL21BbDhZZWV1cjE5aEJwTjdVeHRvN3hLbnlUR0RhU3UyR0Uwb1B6bUwzV0hMclgrcFNSNlRwMzFvb1pCNUFnUk5JdDU4cnpZOHZkbmNkck9Qc3dYcTkrTk1PeExvWEZLTU9tQmlVeThrcEFRMWJ2c0M1WnVRMG1nZDZ1NEY2YVVVYkRoTS80TExMb1VpcE5vL1FXMi8xNFh3bUN2Wk1HbWtMaHZWSzBDRGRlNmhvY296SGlWaWhaVVlvU1YzYkVVNXlsMGZCWWtVSVVWL1ZnVVMra3lscG5UVEhlWG0xNVBLWjFxb2paUFlSMnF4WEFhME5LNnVBYTg2Q1FDb2F0WUhORlphdFdITlRFT0lvTTgrMUUvelVjejZOSXNzci9rMno4cmh1RVNiNzkvRlFNTHZMbTlaakxmMlF0OXVLZUx4MFY4Tk1HNEU1eHVZejAvaVV3UFF0eDMwN1g5d2d6Si93QTNGTWNJTy9UUUpRMW5HSlVRSWdCc01WT0hSbmRPKzBiM3V2S2lpd08wTGNkSEVrbElndVhFZTJ4WmVlbXJXRnliR1RaNzhtbFd5d3hxa1hUMjRtM3hKQ0lUWnAzVHcwb0J3M1IvSmpkTGs1Q3ViWFZJWHMiLCJtYWMiOiI2Yjc4YzNlNzJmY2NhYzMzZDM4MDgyZjQ0MWY5M2RmOGFhMWE4ZDkzODFlM2VkZTdlMTgyNDAwMWZhMTg0YWE2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:15:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUxdkoxeDFEODJUM2cvS3l5WkkzM0E9PSIsInZhbHVlIjoiWXBtM0g2eit2Y1lSdHdudkdRSjMyR2gwandCRUd2Rzd2ZDBLakRvS25rcUZJTkFtS1JHNlJ6b2lxN3ZaREtVaDU2eW04TkkydFRscTZMQlBnSFQyV2lmNDJaMTBZRWhQUHhndWFvR2hzNXA1SEtUcGlOcS90SUxhaGlhU3BiaHRZbFk0Y3l5Q21vbmxkeUM3aU9wTjlzU3pVdk5GOWxHd25HZ3RVR0NGUTdVbTlsb3BxSTBoN3JxcHFSVU1ETFE1NW9MZTFYQy95Y0p5Y3h3bU9ORnRyaVIvZE44VSs2bFdtQmJHS2l6cWhuOFVTM3JIbkpjUlBpSTM0bitRakR0cjdEbXhob2RSZ3ZXeXlzenRyNkFQckU3WjE2SU1rdVJZL1hDMkU0cVMzRHlyNEJ5UzhSZ2ZheS9DeFdQVTBjOXFnM0NhS3BQdS9pWkVUS2k4ZU1FZGtLMUFaaVZFNXdwZkdyT3loSDM4TWJ1V3M0SHg0dXdVMFdpbGJTNFZjaFZwSWt0T1VZcGpuUnh5eW5sR29KUDdmTFNvRUNYZmRLZXQ5MGFwaGtMQkl4YlBCWllIOEtMNzZtQWR3dWI0OFlqSE1pLzlQS0dOS3hhWHdUZ0F6UEJkOHJJRzhaeFlhL0pzL05ZdE1FbDFTUXkxREt3L3JYd0dFQnEwTGhiVmhmdVkiLCJtYWMiOiJlMjM0ZDljMzFmNDM0MzBhNjM4YzU1Yzc3ZjE3YTcyM2VmZjgyYTEwMzdhZmJkOTBmYWRmOGE2NDI0ODc1ZWMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InYwcW9pcnNmTWhEZ3daVGR3RjZ6VWc9PSIsInZhbHVlIjoiWWRLN3hDVU1RWXhyQVJqV3REeGJIUWJacFJqN0NRMXhVQVdBa1EvbC9mL21BbDhZZWV1cjE5aEJwTjdVeHRvN3hLbnlUR0RhU3UyR0Uwb1B6bUwzV0hMclgrcFNSNlRwMzFvb1pCNUFnUk5JdDU4cnpZOHZkbmNkck9Qc3dYcTkrTk1PeExvWEZLTU9tQmlVeThrcEFRMWJ2c0M1WnVRMG1nZDZ1NEY2YVVVYkRoTS80TExMb1VpcE5vL1FXMi8xNFh3bUN2Wk1HbWtMaHZWSzBDRGRlNmhvY296SGlWaWhaVVlvU1YzYkVVNXlsMGZCWWtVSVVWL1ZnVVMra3lscG5UVEhlWG0xNVBLWjFxb2paUFlSMnF4WEFhME5LNnVBYTg2Q1FDb2F0WUhORlphdFdITlRFT0lvTTgrMUUvelVjejZOSXNzci9rMno4cmh1RVNiNzkvRlFNTHZMbTlaakxmMlF0OXVLZUx4MFY4Tk1HNEU1eHVZejAvaVV3UFF0eDMwN1g5d2d6Si93QTNGTWNJTy9UUUpRMW5HSlVRSWdCc01WT0hSbmRPKzBiM3V2S2lpd08wTGNkSEVrbElndVhFZTJ4WmVlbXJXRnliR1RaNzhtbFd5d3hxa1hUMjRtM3hKQ0lUWnAzVHcwb0J3M1IvSmpkTGs1Q3ViWFZJWHMiLCJtYWMiOiI2Yjc4YzNlNzJmY2NhYzMzZDM4MDgyZjQ0MWY5M2RmOGFhMWE4ZDkzODFlM2VkZTdlMTgyNDAwMWZhMTg0YWE2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:15:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878657205\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1966172133 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/barcode/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>24.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966172133\", {\"maxDepth\":0})</script>\n"}}