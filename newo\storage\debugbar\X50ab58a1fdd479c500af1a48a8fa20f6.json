{"__meta": {"id": "X50ab58a1fdd479c500af1a48a8fa20f6", "datetime": "2025-06-08 13:12:02", "utime": **********.264453, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.555059, "end": **********.264481, "duration": 2.****************, "duration_str": "2.71s", "measures": [{"label": "Booting", "start": **********.555059, "relative_start": 0, "end": **********.758864, "relative_end": **********.758864, "duration": 1.****************, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.758885, "relative_start": 1.****************, "end": **********.264483, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x dashboard.account-dashboard", "param_count": null, "params": [], "start": **********.968988, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.phpdashboard.account-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fdashboard%2Faccount-dashboard.blade.php&line=1", "ajax": false, "filename": "account-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "dashboard.account-dashboard"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.073012, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.087924, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.212729, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.250114, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.258058, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.259319, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 159, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "210ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8442402, "duration": 0.00554, "duration_str": "5.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 2.634}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.877389, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 2.634, "width_percent": 0.537}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.923964, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 3.171, "width_percent": 0.447}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.930126, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 3.618, "width_percent": 0.338}, {"sql": "select * from `revenues` where `created_by` = 15 order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 92}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.942879, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:92", "source": "app/Http/Controllers/DashboardController.php:92", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=92", "ajax": false, "filename": "DashboardController.php", "line": "92"}, "connection": "ty", "start_percent": 3.956, "width_percent": 0.685}, {"sql": "select * from `payments` where `created_by` = 15 order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 93}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9510372, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:93", "source": "app/Http/Controllers/DashboardController.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=93", "ajax": false, "filename": "DashboardController.php", "line": "93"}, "connection": "ty", "start_percent": 4.641, "width_percent": 0.732}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'income'", "type": "query", "params": [], "bindings": ["15", "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.960476, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:97", "source": "app/Http/Controllers/DashboardController.php:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=97", "ajax": false, "filename": "DashboardController.php", "line": "97"}, "connection": "ty", "start_percent": 5.373, "width_percent": 0.666}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'expense'", "type": "query", "params": [], "bindings": ["15", "expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 113}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.969002, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:113", "source": "app/Http/Controllers/DashboardController.php:113", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=113", "ajax": false, "filename": "DashboardController.php", "line": "113"}, "connection": "ty", "start_percent": 6.038, "width_percent": 0.499}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 1 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.981782, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 6.538, "width_percent": 0.428}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 1 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "1", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.990171, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 6.966, "width_percent": 1.331}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 1 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.001264, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 8.297, "width_percent": 0.404}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 1 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "1", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.008684, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 8.701, "width_percent": 1.246}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 2 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.017775, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 9.947, "width_percent": 0.466}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 2 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "2", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.024333, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 10.413, "width_percent": 0.556}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 2 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0313058, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 10.969, "width_percent": 0.452}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 2 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "2", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.037959, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 11.421, "width_percent": 0.628}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 3 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.045188, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 12.048, "width_percent": 0.319}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 3 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.051362, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 12.367, "width_percent": 0.609}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 3 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0584512, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 12.975, "width_percent": 0.314}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 3 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0648859, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 13.289, "width_percent": 0.528}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 4 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.07177, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 13.817, "width_percent": 0.437}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 4 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "4", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.078254, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 14.254, "width_percent": 0.485}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 4 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.085171, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 14.739, "width_percent": 0.376}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 4 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "4", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.09159, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 15.115, "width_percent": 0.599}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 5 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0986638, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 15.714, "width_percent": 0.452}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 5 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.104842, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 16.166, "width_percent": 0.694}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 5 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.111584, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 16.86, "width_percent": 0.49}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 5 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.117581, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 17.35, "width_percent": 0.613}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 6 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.124209, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 17.963, "width_percent": 0.371}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 6 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1299288, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 18.334, "width_percent": 0.533}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 6 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.136402, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 18.866, "width_percent": 0.352}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 6 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.142277, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 19.218, "width_percent": 0.547}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 7 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.148494, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 19.765, "width_percent": 0.371}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 7 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.154357, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 20.136, "width_percent": 0.58}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 7 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1607928, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 20.716, "width_percent": 0.437}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 7 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1667562, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 21.153, "width_percent": 0.713}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 8 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.173864, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 21.867, "width_percent": 0.437}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 8 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1800601, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 22.304, "width_percent": 0.461}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 8 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.186362, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 22.765, "width_percent": 0.38}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 8 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.192255, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 23.146, "width_percent": 0.58}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 9 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1986852, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 23.726, "width_percent": 0.309}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 9 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "9", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.204545, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 24.035, "width_percent": 0.685}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 9 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.211843, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 24.719, "width_percent": 0.499}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 9 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "9", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2183888, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 25.219, "width_percent": 0.704}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 10 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.225228, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 25.922, "width_percent": 0.499}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 10 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.231748, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 26.422, "width_percent": 0.666}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 10 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.239396, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 27.087, "width_percent": 0.471}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 10 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "10", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.246028, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 27.558, "width_percent": 0.694}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 11 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2535, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 28.252, "width_percent": 0.485}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 11 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "11", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.259883, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 28.737, "width_percent": 0.694}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 11 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.266957, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 29.431, "width_percent": 0.523}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 11 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "11", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.273654, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 29.954, "width_percent": 0.68}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 12 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 704}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.281348, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:704", "source": "app/Models/User.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=704", "ajax": false, "filename": "User.php", "line": "704"}, "connection": "ty", "start_percent": 30.634, "width_percent": 0.485}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 12 and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "12", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 572}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 705}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2899878, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "User.php:572", "source": "app/Models/User.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=572", "ajax": false, "filename": "User.php", "line": "572"}, "connection": "ty", "start_percent": 31.119, "width_percent": 0.618}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and year(`date`) = '2025' and month(`date`) = 12 limit 1", "type": "query", "params": [], "bindings": ["15", "2025", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 711}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2976189, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:711", "source": "app/Models/User.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=711", "ajax": false, "filename": "User.php", "line": "711"}, "connection": "ty", "start_percent": 31.737, "width_percent": 0.452}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 12 and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "12", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 637}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 712}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 127}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.305541, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "User.php:637", "source": "app/Models/User.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=637", "ajax": false, "filename": "User.php", "line": "637"}, "connection": "ty", "start_percent": 32.189, "width_percent": 0.656}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-08' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-08"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.313561, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 32.845, "width_percent": 0.456}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-08' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.321161, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 33.302, "width_percent": 0.59}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-08' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-08"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.327967, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 33.891, "width_percent": 0.456}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-08' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.335593, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 34.348, "width_percent": 0.604}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-07' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-07"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3424768, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 34.952, "width_percent": 0.442}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-07' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.34878, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 35.394, "width_percent": 0.846}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-07' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-07"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.356659, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 36.24, "width_percent": 0.414}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-07' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-07", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.362993, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 36.654, "width_percent": 0.661}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-06' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.370631, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 37.315, "width_percent": 0.433}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-06' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3770301, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 37.747, "width_percent": 0.628}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-06' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.384732, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 38.375, "width_percent": 0.433}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-06' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3916628, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 38.808, "width_percent": 0.685}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-05' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3988898, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 39.492, "width_percent": 0.456}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-05' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-05", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4057488, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 39.949, "width_percent": 0.542}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-05' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.412514, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 40.491, "width_percent": 0.442}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-05' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-05", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.419214, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 40.933, "width_percent": 0.575}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-04' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-04"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.426157, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 41.508, "width_percent": 0.461}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-04' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-04", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4325001, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 41.969, "width_percent": 0.637}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-04' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-04"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.439898, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 42.607, "width_percent": 0.428}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-04' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-04", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4463582, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 43.034, "width_percent": 0.637}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-03' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-03"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.453969, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 43.672, "width_percent": 0.428}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-03' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-03", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4604368, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 44.099, "width_percent": 0.533}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-03' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-03"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.467535, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 44.632, "width_percent": 0.556}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-03' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-03", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.474637, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 45.188, "width_percent": 0.647}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-02' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-02"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.481693, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 45.835, "width_percent": 0.428}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-02' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-02", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.488597, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 46.263, "width_percent": 0.59}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-02' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-02"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.495626, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 46.852, "width_percent": 0.423}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-02' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-02", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.502439, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 47.276, "width_percent": 0.661}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-06-01' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.509562, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 47.936, "width_percent": 0.423}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-06-01' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.516037, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 48.36, "width_percent": 0.656}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-06-01' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5232148, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 49.016, "width_percent": 0.456}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-06-01' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-06-01", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.529717, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 49.472, "width_percent": 0.642}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-31' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-31"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.537691, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 50.114, "width_percent": 0.48}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-31' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-31", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.544692, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 50.594, "width_percent": 0.609}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-31' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-31"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5519502, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 51.203, "width_percent": 0.461}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-31' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-31", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5584838, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 51.664, "width_percent": 0.77}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-30' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.565841, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 52.434, "width_percent": 0.571}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-30' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-30", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.572702, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 53.005, "width_percent": 0.704}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-30' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5804439, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 53.709, "width_percent": 0.442}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-30' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-30", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.587388, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 54.151, "width_percent": 0.746}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-29' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.594853, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 54.897, "width_percent": 0.423}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-29' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-29", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.601203, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 55.32, "width_percent": 0.647}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-29' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.608048, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 55.967, "width_percent": 0.428}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-29' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-29", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.614371, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 56.395, "width_percent": 0.727}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-28' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.623094, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 57.122, "width_percent": 0.485}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-28' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-28", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.632523, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 57.607, "width_percent": 0.718}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-28' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6401289, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 58.325, "width_percent": 0.371}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-28' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-28", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.646605, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 58.696, "width_percent": 0.547}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-27' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.654598, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 59.243, "width_percent": 0.404}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-27' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-27", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.663824, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 59.647, "width_percent": 0.647}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-27' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6710079, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 60.294, "width_percent": 0.452}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-27' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-27", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6780221, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 60.746, "width_percent": 0.642}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-26' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6861842, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 61.387, "width_percent": 0.461}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-26' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-26", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6927998, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 61.849, "width_percent": 0.613}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-26' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.70045, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 62.462, "width_percent": 0.437}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-26' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.706824, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 62.899, "width_percent": 0.632}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 15 and date = '2025-05-25' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-25"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 796}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7148879, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:796", "source": "app/Models/User.php:796", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=796", "ajax": false, "filename": "User.php", "line": "796"}, "connection": "ty", "start_percent": 63.532, "width_percent": 0.452}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-05-25' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-25", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 798}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.726999, "duration": 0.00519, "duration_str": "5.19ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 63.983, "width_percent": 2.468}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 15 and date = '2025-05-25' limit 1", "type": "query", "params": [], "bindings": ["15", "2025-05-25"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 803}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.742, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "User.php:803", "source": "app/Models/User.php:803", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=803", "ajax": false, "filename": "User.php", "line": "803"}, "connection": "ty", "start_percent": 66.451, "width_percent": 0.49}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-05-25' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-25", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 805}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7494512, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 66.941, "width_percent": 1.469}, {"sql": "select count(*) as aggregate from `taxes` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 134}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.762688, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:134", "source": "app/Http/Controllers/DashboardController.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=134", "ajax": false, "filename": "DashboardController.php", "line": "134"}, "connection": "ty", "start_percent": 68.41, "width_percent": 0.447}, {"sql": "select count(*) as aggregate from `product_service_categories` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 135}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.771152, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:135", "source": "app/Http/Controllers/DashboardController.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=135", "ajax": false, "filename": "DashboardController.php", "line": "135"}, "connection": "ty", "start_percent": 68.857, "width_percent": 0.433}, {"sql": "select count(*) as aggregate from `product_service_units` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 136}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.77884, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:136", "source": "app/Http/Controllers/DashboardController.php:136", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=136", "ajax": false, "filename": "DashboardController.php", "line": "136"}, "connection": "ty", "start_percent": 69.29, "width_percent": 0.718}, {"sql": "select count(*) as aggregate from `bank_accounts` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 137}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.788826, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:137", "source": "app/Http/Controllers/DashboardController.php:137", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=137", "ajax": false, "filename": "DashboardController.php", "line": "137"}, "connection": "ty", "start_percent": 70.008, "width_percent": 0.656}, {"sql": "select * from `bank_accounts` where `created_by` = 15 limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7957208, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:139", "source": "app/Http/Controllers/DashboardController.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=139", "ajax": false, "filename": "DashboardController.php", "line": "139"}, "connection": "ty", "start_percent": 70.664, "width_percent": 0.366}, {"sql": "select `invoices`.*, `customers`.`name` as `customer_name` from `invoices` inner join `customers` on `invoices`.`customer_id` = `customers`.`id` where `invoices`.`created_by` = 15 order by `invoices`.`id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 145}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.803387, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:145", "source": "app/Http/Controllers/DashboardController.php:145", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=145", "ajax": false, "filename": "DashboardController.php", "line": "145"}, "connection": "ty", "start_percent": 71.03, "width_percent": 0.542}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_products.price * invoice_products.quantity) - invoice_products.discount) as price, (SELECT SUM(credit_notes.amount) FROM credit_notes WHERE credit_notes.invoice = invoices.id) as credit_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as total_tax from `invoices` left join `invoice_products` on `invoice_products`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-26' and `issue_date` <= '2025-06-08' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 861}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1031}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 147}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.810026, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "User.php:861", "source": "app/Models/User.php:861", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=861", "ajax": false, "filename": "User.php", "line": "861"}, "connection": "ty", "start_percent": 71.572, "width_percent": 0.946}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_payments.amount)) as pay_price from `invoices` left join `invoice_payments` on `invoice_payments`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-26' and `issue_date` <= '2025-06-08' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 872}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1031}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 147}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.817548, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:872", "source": "app/Models/User.php:872", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=872", "ajax": false, "filename": "User.php", "line": "872"}, "connection": "ty", "start_percent": 72.518, "width_percent": 0.689}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_products.price * invoice_products.quantity) - invoice_products.discount) as price, (SELECT SUM(credit_notes.amount) FROM credit_notes WHERE credit_notes.invoice = invoices.id) as credit_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as total_tax from `invoices` left join `invoice_products` on `invoice_products`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-08' and `issue_date` <= '2025-06-08' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-08", "2025-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 861}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1044}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.829815, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "User.php:861", "source": "app/Models/User.php:861", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=861", "ajax": false, "filename": "User.php", "line": "861"}, "connection": "ty", "start_percent": 73.207, "width_percent": 0.956}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_payments.amount)) as pay_price from `invoices` left join `invoice_payments` on `invoice_payments`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-05-08' and `issue_date` <= '2025-06-08' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-05-08", "2025-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 872}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1044}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8403041, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "User.php:872", "source": "app/Models/User.php:872", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=872", "ajax": false, "filename": "User.php", "line": "872"}, "connection": "ty", "start_percent": 74.163, "width_percent": 0.718}, {"sql": "select `bills`.*, `venders`.`name` as `vender_name` from `bills` inner join `venders` on `bills`.`vender_id` = `venders`.`id` where `bills`.`created_by` = 15 order by `bills`.`id` desc limit 5", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 154}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.850034, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:154", "source": "app/Http/Controllers/DashboardController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=154", "ajax": false, "filename": "DashboardController.php", "line": "154"}, "connection": "ty", "start_percent": 74.881, "width_percent": 0.875}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-08", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 920}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1078}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.860004, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "User.php:920", "source": "app/Models/User.php:920", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=920", "ajax": false, "filename": "User.php", "line": "920"}, "connection": "ty", "start_percent": 75.756, "width_percent": 0.856}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-08", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 932}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1078}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.868844, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "User.php:932", "source": "app/Models/User.php:932", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=932", "ajax": false, "filename": "User.php", "line": "932"}, "connection": "ty", "start_percent": 76.612, "width_percent": 0.965}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-08", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 982}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1079}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8762472, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "User.php:982", "source": "app/Models/User.php:982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=982", "ajax": false, "filename": "User.php", "line": "982"}, "connection": "ty", "start_percent": 77.577, "width_percent": 0.889}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-26' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-26", "2025-06-08", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 994}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1079}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.884623, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:994", "source": "app/Models/User.php:994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=994", "ajax": false, "filename": "User.php", "line": "994"}, "connection": "ty", "start_percent": 78.466, "width_percent": 0.689}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-08' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-08", "2025-06-08", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 920}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1091}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.891515, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "User.php:920", "source": "app/Models/User.php:920", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=920", "ajax": false, "filename": "User.php", "line": "920"}, "connection": "ty", "start_percent": 79.156, "width_percent": 0.723}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-08' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-08", "2025-06-08", "15", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 932}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1091}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.898851, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "User.php:932", "source": "app/Models/User.php:932", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=932", "ajax": false, "filename": "User.php", "line": "932"}, "connection": "ty", "start_percent": 79.878, "width_percent": 0.746}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-08' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-08", "2025-06-08", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 982}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1092}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.905875, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:982", "source": "app/Models/User.php:982", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=982", "ajax": false, "filename": "User.php", "line": "982"}, "connection": "ty", "start_percent": 80.625, "width_percent": 0.689}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-05-08' and `bill_date` <= '2025-06-08' and `bills`.`created_by` = 15 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-05-08", "2025-06-08", "15", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 994}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 1092}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.913, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:994", "source": "app/Models/User.php:994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=994", "ajax": false, "filename": "User.php", "line": "994"}, "connection": "ty", "start_percent": 81.314, "width_percent": 0.547}, {"sql": "select * from `goals` where `created_by` = 15 and `is_display` = 1", "type": "query", "params": [], "bindings": ["15", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 158}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.921509, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:158", "source": "app/Http/Controllers/DashboardController.php:158", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=158", "ajax": false, "filename": "DashboardController.php", "line": "158"}, "connection": "ty", "start_percent": 81.861, "width_percent": 0.642}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.92815, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:161", "source": "app/Http/Controllers/DashboardController.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=161", "ajax": false, "filename": "DashboardController.php", "line": "161"}, "connection": "ty", "start_percent": 82.503, "width_percent": 0.737}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 162}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.935719, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 83.24, "width_percent": 0.594}, {"sql": "select count(*) as aggregate from `customers` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 394}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.979987, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "User.php:394", "source": "app/Models/User.php:394", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=394", "ajax": false, "filename": "User.php", "line": "394"}, "connection": "ty", "start_percent": 83.834, "width_percent": 0.594}, {"sql": "select count(*) as aggregate from `venders` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 399}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 274}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.991967, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "User.php:399", "source": "app/Models/User.php:399", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=399", "ajax": false, "filename": "User.php", "line": "399"}, "connection": "ty", "start_percent": 84.428, "width_percent": 0.542}, {"sql": "select count(*) as aggregate from `invoices` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 404}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 288}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.998459, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "User.php:404", "source": "app/Models/User.php:404", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=404", "ajax": false, "filename": "User.php", "line": "404"}, "connection": "ty", "start_percent": 84.971, "width_percent": 0.409}, {"sql": "select count(*) as aggregate from `bills` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 409}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 300}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0051432, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:409", "source": "app/Models/User.php:409", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=409", "ajax": false, "filename": "User.php", "line": "409"}, "connection": "ty", "start_percent": 85.379, "width_percent": 0.475}, {"sql": "select sum(`amount`) as aggregate from `revenues` where `created_by` = 15 and Date(date) = CURDATE() and `created_by` = 15", "type": "query", "params": [], "bindings": ["15", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 414}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 355}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.012029, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:414", "source": "app/Models/User.php:414", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=414", "ajax": false, "filename": "User.php", "line": "414"}, "connection": "ty", "start_percent": 85.855, "width_percent": 0.661}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '25-06-08' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["25-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 587}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 415}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 355}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.019571, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "User.php:587", "source": "app/Models/User.php:587", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=587", "ajax": false, "filename": "User.php", "line": "587"}, "connection": "ty", "start_percent": 86.516, "width_percent": 0.984}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `created_by` = 15 and Date(date) = CURDATE()", "type": "query", "params": [], "bindings": ["15", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 430}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 366}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0281441, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "User.php:430", "source": "app/Models/User.php:430", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=430", "ajax": false, "filename": "User.php", "line": "430"}, "connection": "ty", "start_percent": 87.5, "width_percent": 0.632}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '25-06-08' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["25-06-08", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 654}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 432}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 366}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0350442, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "ty", "start_percent": 88.132, "width_percent": 0.713}, {"sql": "select sum(`amount`) as aggregate from `revenues` where `created_by` = 15 and MONTH(date) = '06'", "type": "query", "params": [], "bindings": ["15", "06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 450}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 377}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0422351, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "User.php:450", "source": "app/Models/User.php:450", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=450", "ajax": false, "filename": "User.php", "line": "450"}, "connection": "ty", "start_percent": 88.846, "width_percent": 0.404}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where MONTH(invoices.send_date) = '06' and `invoices`.`created_by` = 15 group by `invoice`", "type": "query", "params": [], "bindings": ["06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 602}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 451}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 377}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0476902, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:602", "source": "app/Models/User.php:602", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=602", "ajax": false, "filename": "User.php", "line": "602"}, "connection": "ty", "start_percent": 89.25, "width_percent": 0.485}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and MONTH(date) = '06'", "type": "query", "params": [], "bindings": ["15", "06"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 488}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 388}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.05429, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:488", "source": "app/Models/User.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=488", "ajax": false, "filename": "User.php", "line": "488"}, "connection": "ty", "start_percent": 89.735, "width_percent": 0.428}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where MONTH(bills.send_date) = '06' and `bills`.`created_by` = 15 group by `bill`", "type": "query", "params": [], "bindings": ["06", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 671}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\User.php", "line": 489}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/dashboard/account-dashboard.blade.php", "line": 388}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.059778, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "User.php:671", "source": "app/Models/User.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=671", "ajax": false, "filename": "User.php", "line": "671"}, "connection": "ty", "start_percent": 90.163, "width_percent": 0.537}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0745502, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 90.7, "width_percent": 0.737}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0811071, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 91.437, "width_percent": 0.475}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.094881, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 91.912, "width_percent": 0.661}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.1022599, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "ty", "start_percent": 92.573, "width_percent": 0.694}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.213932, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 93.267, "width_percent": 0.494}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.219854, "duration": 0.010320000000000001, "duration_str": "10.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 93.762, "width_percent": 4.907}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.236133, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 98.669, "width_percent": 0.395}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.2428381, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "ty", "start_percent": 99.063, "width_percent": 0.49}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 6212}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.251276, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 99.553, "width_percent": 0.447}]}, "models": {"data": {"App\\Models\\Revenue": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FRevenue.php&line=1", "ajax": false, "filename": "Revenue.php", "line": "?"}}, "App\\Models\\Payment": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\BankAccount": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FBankAccount.php&line=1", "ajax": false, "filename": "BankAccount.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 60, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 72, "messages": [{"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.941019, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.974642, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110656, "xdebug_link": null}, {"message": "[ability => manage delevery, result => null, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1864545155 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864545155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.113414, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.11477, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115335, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115825, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116749, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1450859137 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450859137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117761, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-353323531 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353323531\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.118636, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1516800713 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516800713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119622, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1931366367 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931366367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.120483, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-926567745 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926567745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.121369, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1656483484 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656483484\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122174, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1043404734 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043404734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123026, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2106553553 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106553553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123894, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1887579194 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887579194\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.124757, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125636, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12623, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1852055682 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852055682\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.128685, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-566389512 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566389512\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.129424, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-655677273 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655677273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.131966, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1926896046 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926896046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13378, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1087977429 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087977429\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135302, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-655090601 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655090601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.136881, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-545992931 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545992931\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.139005, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-689855047 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689855047\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.141062, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-144001049 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144001049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.143174, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-14802007 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14802007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.14527, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-62504141 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62504141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.146985, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1388388782 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388388782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.148687, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-27018761 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27018761\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.150794, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.152554, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1836739104 data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836739104\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.154294, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1072281435 data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1072281435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.15606, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1978925867 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978925867\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.157846, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-204582685 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-204582685\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.159763, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2046991959 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046991959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161604, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1973678818 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973678818\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.163689, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1831010174 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831010174\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.165704, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2062765154 data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062765154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.16716, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1430738344 data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430738344\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.168746, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.169617, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.170414, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.171181, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-762433609 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762433609\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.171882, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1823309020 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1823309020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172835, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1375923690 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375923690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.173681, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174379, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.175651, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.17699, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1475454584 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475454584\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177644, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1437385983 data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437385983\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.178218, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2000954116 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000954116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.17874, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2126889487 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126889487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.179219, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-989718960 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989718960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.179743, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1450436525 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450436525\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.180815, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1883298910 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883298910\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181568, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-403292836 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403292836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.182267, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-834344505 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834344505\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183008, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2073009534 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073009534\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.185545, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-516517052 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516517052\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.188087, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-59777450 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59777450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190741, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1974772652 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974772652\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.194016, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1161421568 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161421568\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.196777, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1891558160 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891558160\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.199305, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2043597497 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043597497\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.203064, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.205767, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1704853491 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704853491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208355, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208878, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.211207, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.212091, "xdebug_link": null}]}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749388318651%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik56Sy8zV0RyZGQ4SklOSFo5WHdEOFE9PSIsInZhbHVlIjoiUDYwM3hka3RoN21iTG90SGhhT3hvZ2xUV09zdktjSm1QM3ErZFE5TGRLNHF3bG9ZWHVFL0ZvM2tuSGZ4dUQ2bkZVYU41U09OSml0R2IyMmN5elZnalNkOWp3eDFRUzZJSVFwbC9nZlZ0eDdBdCtnR0haVzhOOW5uNlJMY2RZUXJQelBBMm1wdG41bGdYbVNnWW11RUFJOWVESitxa1ZiVzZhTkZYQThHQnE1UyszY3hob2RQbG9YZGNpRVpnZm13dWdLOGFzSDIxM3hxRjc0a05HTjJLcUtuQjBpbmE2dlJZVTEzVDVTczdVQWp1bVVZeHA1aSszSTBjeXMxYXMzZ0N3cXBkYmd6NDJ5TXVaYm1OT3ZTc2gvLzJWSzF5QUJIVDhDWmo5OTYvTXc0NE5vSzZKOTlQanM4WXl1Q1lBa0JLMjNnZXlBS0c3UU15b3BCTUNwQXdDSERmcjBvNlhGcEpNMEQvQlpHeSszWm9qK01lYUxaUWJBdVB1SDlKbHZRa1FtY0ttZXp6SHNXVnBKU0FJQ0d6Nk0zSmdsZ0NNbjRXK0tHQUZHQTNVQi95QnYyelJJOWwvNFJia1hBeE9aa2FyU3NyZFloRkVHdFJHeFZQNWZLSXZ3bnBsZ1hFbVF3V2xTbTNQSXErK05IQUJRMjZlTVlXczFSRE5yaktoelkiLCJtYWMiOiIwN2I2ODA4MWMwNTM4MjhjNTU4OGQzNDlhMGY4MGRhODEzNDg4NGE0NDI4NTgxNzg2ZTFlMTBjNTk2MGRmNGU0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZDQVlWcXd6L1F2TG0vRG5OSkhXWkE9PSIsInZhbHVlIjoiT0NQWUc0ZVdMaER6N2w0MEI4OUVpeWJlV1VvU29QV042NXdwMGlyR3pqTDloNWQrdzRhdGdlNyszekxOalBwWmhtY2svTHA2WHkzcDJWZFF4T2R0SkhlTjJwNzVMZlNhNXVGWGZ4TjFlUktXcS9KWTZpb2RCdE9Cd2k2ZUhvWHdQZnNYWWE3WXBqd2RocHpVQ1lsR1NVUGtHZ0hsMXVyVXg3N1ZBeEdmclFrSXZiVWZ3NTlka3JGQzlYR0pseDlkZTZ4WkxWaXJucERVckJ0Q2N1TXlEcEVON2dKS1pleWJYZkRXVnVhc25Qc2JyRlNuWG03c3l4RG9rWVhzMUJDTWpYTEJwZW15aDVjS21OdTcrMEVNQWpWOU1TSk96dFIyY3BLVWtsTFFyQmNqZEt4aHRJYnNZZVFWOUVUTllYaHduTWtrdnBkTEppUkJSalIza1Fsa3BtVDd2OER4Q25OTzcwNUhLbS9kOFg2OWpab1dCVVJ1K0lPQ3VLUGxXMitPNVdpMmtkMDdDRXQzK3BFcEowZ0ZyaUNrRTc5RVBjOVN4a0NDcTdkRkNKSG5GUjIyeXJleENPajdIOTlMNDNvSElTMHdReEFHZUFaZWlSckVVYU1tNGd1SGlXbDRvZzlsZ3ExSFllaTNiMkZmWXcxWEFwcFdKUGpJaDB3RzhFZm0iLCJtYWMiOiJmZmM3ZDIwYTU2NTdlOWRhMmJmZGNiZjcyOTkyOWM3ODMyZTBhYzRiOTBlNGQ5MjQxOTYyNzllZGM4YTNmZmYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-917751179 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917751179\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:12:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhETGtJTFRoaElmNUJyTDJobDBuaGc9PSIsInZhbHVlIjoiT2czTGFOdlNLbTNtLzFBaEk0eTN0aEh4OUx2STlXdmkrZjQrYnpuN0Zhb0lsb29CVDZxaEpmRzVrb1ZucUFVUk9uL2pJczdEdzlZdG4yYzYwQXFCYlBRdFJlMTlYcWF3Sjdha1Vwb0NuWUZEanVwbmh3dVJTTU5pZEVzZ2dYTkRhc2xVL0QycllRMnNUdmROUXNmbUFmQ3AxTVJKRmRhb0NSYlh3ZnVKaFFOUHlXeTVlcm9US1RVcnlCdzZkblFXemliMURIeXFzVlQ2eUZzTmNWUm14dnlNOGszZGpRUVNTT1pUdXQ2ekhjVWdqbDM0YkNUVllxS1BuSHZJcW5ub3hocDJyQ2UrTncraWdkZXVydnpiZXY1Q3c5ZCtFUEFKT3VHT2lXbE40MFd1aHN1cVNVRGtheFhoVlZOWXlET1p6bytJbWdSUENObDI0enBEclRpZVNuY3d2QUJZelg0OG5FVjN1VlBiakI0Mjk1WC9QUG5lOEN2bVJoM0RlMVFzVHAydXpuampUUjhQdklrOVBHWm40dXZVZDNaRVBkR1ZaQzd2UWhVL3grcHdManhTR3R6VEhVaTh1ZUk3dkQreG1JamlSalI3RWdsKzNtQWVZL3k1WlZCWjdtSlNmWnVuRlp2RUV2TUR6TTZDbkJoa1ZFMW9odVFBNTVYUzlKTGIiLCJtYWMiOiIxZmQwNTUxZDEzZGM1NTAxNmFiNGEyZThiYmZkMDg3YjBiN2QwYjE4OTJmZjgwMTJjMGVkNDZiODlkNjdiYzk3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:12:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdMZm1MVXRsVWZXUmdhTXhCcStJTHc9PSIsInZhbHVlIjoiOG5uMTgvcHFwcmlxN2c3MjdYenF5NlVMRTRsRXJ3dEZNbjIvRUdNTEVTdzBUUUVWZ2RXTG4zNEZQd3k3YU5XRlJ1ZDNuTU52a08xMjNJZ21aTDQvVUJtQ2JiUkFKLzdaT2gxeURpNWVOYXRpZTdzSnBDSFoySmpqa1RIRHpqV09MdGd3OTVFWFN2U0xRYXlKMnlrVGV5Z2RLOVJOK1FoNGx6QXd4eUhkd2VLTzkvT1RDbk9ybXUvcmkraHFUNHdyREIzNm1VYnpvcXZGU0xQS2pjZGU2Nkdja1BGRDFmK3ZLSnpuNllKZjZma3F1RE0wNGt0dTRIbHpmQmUyWlVlRm9Yb0JJNkQ4a25FNCtUMjFMQTlVN1dGSVV2eFF2ZEZZTXk2eUZrRnZuclBNcVpvQnVrM1FhUjBjV2hIbCtnNlhkR2l5VkVLV0ZhQUVXQngyRHRmVVllVlpZTC9xZTE2RjhWMmtsdk9xVzJPdjNhay9QVWg2alBFSFVSUSs5bVhPcy9iT0svS0JOSW1BZnNsYWFZTFNUWDc1RHdrR0UxTnI3RW5XQkFueXJ1VGkzem9QMlM5UlpBM3gvR2NrR3FaVXdNTThjY1MyVW4wTlFXc25SZHAyRGo2eVBZOUp6UnppNytCdHBaOWhxcnhsQ0Jqbks1MEpYYzNHSmsvYjQwRkgiLCJtYWMiOiI2OTZjMmY5OTlhYWFlYjFjMjUzMjI5MDUxMGMwYWVjODIyYzk3NzZlMmQ4OGFhZDA4NjVhNWJjNjIzODYzODljIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:12:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhETGtJTFRoaElmNUJyTDJobDBuaGc9PSIsInZhbHVlIjoiT2czTGFOdlNLbTNtLzFBaEk0eTN0aEh4OUx2STlXdmkrZjQrYnpuN0Zhb0lsb29CVDZxaEpmRzVrb1ZucUFVUk9uL2pJczdEdzlZdG4yYzYwQXFCYlBRdFJlMTlYcWF3Sjdha1Vwb0NuWUZEanVwbmh3dVJTTU5pZEVzZ2dYTkRhc2xVL0QycllRMnNUdmROUXNmbUFmQ3AxTVJKRmRhb0NSYlh3ZnVKaFFOUHlXeTVlcm9US1RVcnlCdzZkblFXemliMURIeXFzVlQ2eUZzTmNWUm14dnlNOGszZGpRUVNTT1pUdXQ2ekhjVWdqbDM0YkNUVllxS1BuSHZJcW5ub3hocDJyQ2UrTncraWdkZXVydnpiZXY1Q3c5ZCtFUEFKT3VHT2lXbE40MFd1aHN1cVNVRGtheFhoVlZOWXlET1p6bytJbWdSUENObDI0enBEclRpZVNuY3d2QUJZelg0OG5FVjN1VlBiakI0Mjk1WC9QUG5lOEN2bVJoM0RlMVFzVHAydXpuampUUjhQdklrOVBHWm40dXZVZDNaRVBkR1ZaQzd2UWhVL3grcHdManhTR3R6VEhVaTh1ZUk3dkQreG1JamlSalI3RWdsKzNtQWVZL3k1WlZCWjdtSlNmWnVuRlp2RUV2TUR6TTZDbkJoa1ZFMW9odVFBNTVYUzlKTGIiLCJtYWMiOiIxZmQwNTUxZDEzZGM1NTAxNmFiNGEyZThiYmZkMDg3YjBiN2QwYjE4OTJmZjgwMTJjMGVkNDZiODlkNjdiYzk3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:12:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdMZm1MVXRsVWZXUmdhTXhCcStJTHc9PSIsInZhbHVlIjoiOG5uMTgvcHFwcmlxN2c3MjdYenF5NlVMRTRsRXJ3dEZNbjIvRUdNTEVTdzBUUUVWZ2RXTG4zNEZQd3k3YU5XRlJ1ZDNuTU52a08xMjNJZ21aTDQvVUJtQ2JiUkFKLzdaT2gxeURpNWVOYXRpZTdzSnBDSFoySmpqa1RIRHpqV09MdGd3OTVFWFN2U0xRYXlKMnlrVGV5Z2RLOVJOK1FoNGx6QXd4eUhkd2VLTzkvT1RDbk9ybXUvcmkraHFUNHdyREIzNm1VYnpvcXZGU0xQS2pjZGU2Nkdja1BGRDFmK3ZLSnpuNllKZjZma3F1RE0wNGt0dTRIbHpmQmUyWlVlRm9Yb0JJNkQ4a25FNCtUMjFMQTlVN1dGSVV2eFF2ZEZZTXk2eUZrRnZuclBNcVpvQnVrM1FhUjBjV2hIbCtnNlhkR2l5VkVLV0ZhQUVXQngyRHRmVVllVlpZTC9xZTE2RjhWMmtsdk9xVzJPdjNhay9QVWg2alBFSFVSUSs5bVhPcy9iT0svS0JOSW1BZnNsYWFZTFNUWDc1RHdrR0UxTnI3RW5XQkFueXJ1VGkzem9QMlM5UlpBM3gvR2NrR3FaVXdNTThjY1MyVW4wTlFXc25SZHAyRGo2eVBZOUp6UnppNytCdHBaOWhxcnhsQ0Jqbks1MEpYYzNHSmsvYjQwRkgiLCJtYWMiOiI2OTZjMmY5OTlhYWFlYjFjMjUzMjI5MDUxMGMwYWVjODIyYzk3NzZlMmQ4OGFhZDA4NjVhNWJjNjIzODYzODljIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:12:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}