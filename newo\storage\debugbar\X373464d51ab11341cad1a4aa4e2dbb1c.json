{"__meta": {"id": "X373464d51ab11341cad1a4aa4e2dbb1c", "datetime": "2025-06-08 13:02:01", "utime": **********.440014, "method": "GET", "uri": "/receipt-order-warehouse-products?warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387720.09012, "end": **********.440057, "duration": 1.3499369621276855, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1749387720.09012, "relative_start": 0, "end": **********.216185, "relative_end": **********.216185, "duration": 1.1260650157928467, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.216219, "relative_start": 1.126098871231079, "end": **********.440063, "relative_end": 5.9604644775390625e-06, "duration": 0.22384405136108398, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46009712, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET receipt-order-warehouse-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ReceiptOrderController@getWarehouseProducts", "namespace": null, "prefix": "", "where": [], "as": "receipt.order.warehouse.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=106\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:106-144</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.013659999999999999, "accumulated_duration_str": "13.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.338674, "duration": 0.00701, "duration_str": "7.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 51.318}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3762138, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 51.318, "width_percent": 9.59}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 121}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.387824, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:121", "source": "app/Http/Controllers/ReceiptOrderController.php:121", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=121", "ajax": false, "filename": "ReceiptOrderController.php", "line": "121"}, "connection": "ty", "start_percent": 60.908, "width_percent": 10.102}, {"sql": "select * from `product_services` where `product_services`.`id` in (3, 5)", "type": "query", "params": [], "bindings": ["3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4047022, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:121", "source": "app/Http/Controllers/ReceiptOrderController.php:121", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=121", "ajax": false, "filename": "ReceiptOrderController.php", "line": "121"}, "connection": "ty", "start_percent": 71.01, "width_percent": 16.252}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.412528, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:125", "source": "app/Http/Controllers/ReceiptOrderController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=125", "ajax": false, "filename": "ReceiptOrderController.php", "line": "125"}, "connection": "ty", "start_percent": 87.262, "width_percent": 12.738}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/receipt-order-warehouse-products", "status_code": "<pre class=sf-dump id=sf-dump-165068696 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-165068696\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1645093964 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645093964\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1311355241 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1311355241\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1636329303 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387646954%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBoNTlxM2ZrRVkrMEFZU0hJRCs3VUE9PSIsInZhbHVlIjoiTmJjTGhZcHNoS3M1eG9rWjIwTktncS95SkI5S1RVTUFyRFFPaGtDVGpiUGNaSVB1VTZyc1FXQkkwWkU2Qll1bDJVS29ZN3l6VzZXSGdlcTZkTDV4SXI3SVZyN1B0ak9ZaE5seVZOdjRTOFFybUpTUE4wODd2Q1NyS1ZNekQwdXVJbUpBTTEvQ0ZBOVFlWUhWYTJ3dElrSVpob3VsRHpFdkhMUFRrU1dpTGxHRWZJSFE5dTVDMmRWYVNmNHhDRzBPdS94V0s1aUo1Mit6aWRscTgzT2FYckIrNGRYNXJCVldZMXJvdExTVStLalhtWjl2b0JDSjI1L1VYa1NNWkt4ZzhLMk0vYWM4YkZaY05RVVgwb0NvMmwxS0FZVVlqRnVMbFhtR1k5VkNlQmZLamtsN3dMdERJVC9GZUNFKytkT3g5NnhCek54UEMzblZhTldnTEpkeEV6emR3aXZQLzl2NFZjcmw1d2w5aUtHdFd4QjJMQVB5dzBSR24wdnJ0WmdUTUdlaGxQVFovUFIyRXZxWEJnTlljRGhwOFFEWkk5SC9nQ3hsTEE5VzBDYituL3hjZ1hBaHRnSG5oSld1SEpNeUFGMDRWS2hLaDFENXlDMCtISUhrQ2RjZGhNUWtRRnRjdmJOQnFTUmt5cFk0enM2MWEreDJOdTNERmxtckN6anAiLCJtYWMiOiI0MTU3MDMzYzlkZmUzMmY3Njk1YjVlM2NmYWIyYjg3ZDlmMjBjMjQ5ZGM1OWY5YTYwOGY0NTFjOGM3YzFiYzU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRCQkx6bzlBdVVBWmw0MS9JSGtBdlE9PSIsInZhbHVlIjoiL01RMG9MdW4vaXFBdW1ZTHpNVW4zK1kxV0ZFTHZ4Z2FNejk3am04Zm9sMW5HcEJENmphZmd6Qm4wNmFIUTYzb3ZiMHRFS0pOTnpoK203QlhYK1JZWVZnKy9FekJzUG5nckZzTFlZRzlqV2hKZXp4QVRnTlh3cUFJelRRamlmdWdYU3pRRldMQzNaWEVYTWIwTTJFaGU5UVJZdkxFS3V3cGZyMWhkT3c3eWU1L1dpTDU1STRjWGMzbHAwQUkzMmtXeTdDd2hzQWxaSTRlWEo2OTlUaUxUTFhuWFJjV0g1TzU0Y2NKNGhQWDhzSEVQNnR5Tk1STTZ1Qk56Si8xN2NvMW1TS1owT0FzWXpYelhNV3dyNE9yc1JvRDFWY0ZIdWVlcDJhdW84Ull5VDgwd1F1NWtPckl2NHZTTHZOVmRneW5VZ3FnSE1mTTlaMDZNdUlHVExNUHV0MU13TThxNDE2a3poY2V6VkpubWlXLzVTYnVSNTIwQ3U5dWhOdDlTZXlxUVVUOWd4em56UmhyUkt0ZkF5VjB0S3Q2QmMxdlhBSWlNbnlURHQyQ2JwdVVydzM0Y3didUpBVllaUkh5ZGRqSEZOUlZ3OStUVXNlb2RycUpwaFk4NHVzb3d3SVVSbkdZdDl1akJzTEVZcWhEbzVHZjg0bW1icEJSZHp6YkVKUG0iLCJtYWMiOiJmZjMxNmZiZWI1MDFiNTcxODQ4ZWM4YTE5OGU2MGQyMDc4MWU1OGY0MjhiYjBhMjgyNGIyYzgwZDE0M2I4NGQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636329303\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-127309543 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127309543\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:02:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkloZHRDbE5WM2h4S0hpWCt5WW54Rnc9PSIsInZhbHVlIjoiWG5jdnM3NEpsOG9CYXhwRUhPTTV5MGtZeWEwRHpEaGpvTmZjYUJidFlRM2REaVRlTFltY1VMcVZ0b21zTnBHQ2o2d2RCNUN1ODdncitQSkN5NTJhQ3MrcnpIQ3JqRnYyODZPU0NwUlBvcWxnTmg1eHBabVVmcDhLWWkvZGJicURtdThOUzVwdTQvSTU5Um5rWmxxVzhBRG1aVk5MNUQ5UkpTQzJyOFp0M0dCRHNjUm5veURFOVhjalZsUEFMamtwalRGQzhRR05iVkExSTU5VjA5bzlFbC8rNThXUkRiV0hvZk9BYkE4VmdHdzM0WW0yZGpKWmVMbHF4M3RPWjJ1Y0Q4K0UzNFp1R3Z5T3IyVkU0UlBHVUI5ZVM3QjNzRUtTbnFZNlJCcWlkdGZQcjNKZDdpZWRJZk9sRitISldsd1BkNmxPU2JSazNWdkxDTGVXRzJMOXo1Unk3Q0paaG9Ud0gzWTJnRFhZeS9heW1TVnF5OE9paTlkUlZVaTBrNEJUUDU5MG5NNzg3OTl0QXZRVHdSbFVoVHBXNDR3ampJbnFFaHFsbnFCSzJRSmlObUdzaXdrbnVkYXhpV2pmZkpLWEZ5NzRuUmZLVzc1U0l4KzhCRkRXUWpscTVpNEttMk5kV05WSVhkR3V6NG11ZHhhWkZ5ZVYzRUpwc2ZLaHpCSDYiLCJtYWMiOiJiMWRhMzQzMmI4NzZlNTU2MjMwNzA0OWE4YjI2NDVlZmE4N2ZhMTI2NDFmN2I3ZmFlZWRjYWI1NzcxMjI0ZWJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImhjY3JkMW5QYVlBY05TeHZqQlBVTkE9PSIsInZhbHVlIjoib1MzR25zOUljMDBnOHBZbG1YWVNucTBoMG1IM0hVdmI0UE1EN09hNFJxUkQrVnppMmFFOXpRSmlzREpDaVYxUlBBbThkRkpoZjJHRytsU1BPTDA2bk9wa1UyKzVBRSsyVjhWYXBjbElvM3Zlc1pUSEVVTEJUcWNqSzFMWlpVTGp6Z0JURndEMWdjSmJ6aCtqdWE4SFEya0ZYNlZZdVFRNzZxZldrRGtybVlGM2RrZVQvRmkvQmpUMVUxNDRIMXBkTjJ4VmxXb0hFeGhNS3JCRldNTmJ2bWxqTGNLcjZFU3RmejJCVHFmT21ydXNBcTJJOENaZCtUK1d3cGRKeFphU085R1RUcytzUlcwSStTTEtUSmkwZUNaWXBDT2xvR01MbUpVQUpWY0VxOEU4NHgxMVpPY3JNUXF4SUdXWVJFZTNxWGM5ZGs1NFZQcEVPcFJ2ejN2NDJwaER2azhCMnlnRkhoK2VnSHJockhyN2hlRkU5TUZFc1VtamJyZmdwSGI1R09icFhKUWlFYnV2cE5yZFMrUFNIckhLN1ZiOHhpV0FKaVVGSUk4RTY4MjBRRmxpNXBMbnJKa3gzeVdpQmlSRXIvRFptUmtndDAzSWJhK0VTNE5CRkk2Y3l4aU05MkdpRUdjdHlpRlZXM3dwU2RpSzJNb2w4VCtOYk9OTGkxNisiLCJtYWMiOiIzNjU2OTQyOWRkM2YzOWY0MDliZWY1ZjU4OTliNDVkNDc4NjBmZjYzZDZkOWJkNmM1OGE2NzM2Nzc2MjVjYTkwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkloZHRDbE5WM2h4S0hpWCt5WW54Rnc9PSIsInZhbHVlIjoiWG5jdnM3NEpsOG9CYXhwRUhPTTV5MGtZeWEwRHpEaGpvTmZjYUJidFlRM2REaVRlTFltY1VMcVZ0b21zTnBHQ2o2d2RCNUN1ODdncitQSkN5NTJhQ3MrcnpIQ3JqRnYyODZPU0NwUlBvcWxnTmg1eHBabVVmcDhLWWkvZGJicURtdThOUzVwdTQvSTU5Um5rWmxxVzhBRG1aVk5MNUQ5UkpTQzJyOFp0M0dCRHNjUm5veURFOVhjalZsUEFMamtwalRGQzhRR05iVkExSTU5VjA5bzlFbC8rNThXUkRiV0hvZk9BYkE4VmdHdzM0WW0yZGpKWmVMbHF4M3RPWjJ1Y0Q4K0UzNFp1R3Z5T3IyVkU0UlBHVUI5ZVM3QjNzRUtTbnFZNlJCcWlkdGZQcjNKZDdpZWRJZk9sRitISldsd1BkNmxPU2JSazNWdkxDTGVXRzJMOXo1Unk3Q0paaG9Ud0gzWTJnRFhZeS9heW1TVnF5OE9paTlkUlZVaTBrNEJUUDU5MG5NNzg3OTl0QXZRVHdSbFVoVHBXNDR3ampJbnFFaHFsbnFCSzJRSmlObUdzaXdrbnVkYXhpV2pmZkpLWEZ5NzRuUmZLVzc1U0l4KzhCRkRXUWpscTVpNEttMk5kV05WSVhkR3V6NG11ZHhhWkZ5ZVYzRUpwc2ZLaHpCSDYiLCJtYWMiOiJiMWRhMzQzMmI4NzZlNTU2MjMwNzA0OWE4YjI2NDVlZmE4N2ZhMTI2NDFmN2I3ZmFlZWRjYWI1NzcxMjI0ZWJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImhjY3JkMW5QYVlBY05TeHZqQlBVTkE9PSIsInZhbHVlIjoib1MzR25zOUljMDBnOHBZbG1YWVNucTBoMG1IM0hVdmI0UE1EN09hNFJxUkQrVnppMmFFOXpRSmlzREpDaVYxUlBBbThkRkpoZjJHRytsU1BPTDA2bk9wa1UyKzVBRSsyVjhWYXBjbElvM3Zlc1pUSEVVTEJUcWNqSzFMWlpVTGp6Z0JURndEMWdjSmJ6aCtqdWE4SFEya0ZYNlZZdVFRNzZxZldrRGtybVlGM2RrZVQvRmkvQmpUMVUxNDRIMXBkTjJ4VmxXb0hFeGhNS3JCRldNTmJ2bWxqTGNLcjZFU3RmejJCVHFmT21ydXNBcTJJOENaZCtUK1d3cGRKeFphU085R1RUcytzUlcwSStTTEtUSmkwZUNaWXBDT2xvR01MbUpVQUpWY0VxOEU4NHgxMVpPY3JNUXF4SUdXWVJFZTNxWGM5ZGs1NFZQcEVPcFJ2ejN2NDJwaER2azhCMnlnRkhoK2VnSHJockhyN2hlRkU5TUZFc1VtamJyZmdwSGI1R09icFhKUWlFYnV2cE5yZFMrUFNIckhLN1ZiOHhpV0FKaVVGSUk4RTY4MjBRRmxpNXBMbnJKa3gzeVdpQmlSRXIvRFptUmtndDAzSWJhK0VTNE5CRkk2Y3l4aU05MkdpRUdjdHlpRlZXM3dwU2RpSzJNb2w4VCtOYk9OTGkxNisiLCJtYWMiOiIzNjU2OTQyOWRkM2YzOWY0MDliZWY1ZjU4OTliNDVkNDc4NjBmZjYzZDZkOWJkNmM1OGE2NzM2Nzc2MjVjYTkwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1352090006 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352090006\", {\"maxDepth\":0})</script>\n"}}