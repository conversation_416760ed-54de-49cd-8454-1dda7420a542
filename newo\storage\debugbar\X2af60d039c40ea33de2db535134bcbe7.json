{"__meta": {"id": "X2af60d039c40ea33de2db535134bcbe7", "datetime": "2025-06-08 13:05:35", "utime": **********.464548, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387934.155001, "end": **********.464581, "duration": 1.3095800876617432, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749387934.155001, "relative_start": 0, "end": **********.206241, "relative_end": **********.206241, "duration": 1.0512399673461914, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.20626, "relative_start": 1.0512590408325195, "end": **********.464584, "relative_end": 3.0994415283203125e-06, "duration": 0.25832414627075195, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48123192, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03339, "accumulated_duration_str": "33.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3105822, "duration": 0.02662, "duration_str": "26.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.724}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3620489, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.724, "width_percent": 3.774}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4129899, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 83.498, "width_percent": 3.235}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.419873, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.733, "width_percent": 3.085}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4335349, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 89.817, "width_percent": 7.427}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4433548, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.245, "width_percent": 2.755}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2033665739 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033665739\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.430835, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-229129593 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-229129593\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1881758932 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1881758932\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1293758754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1293758754\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1679328010 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387916971%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijd4YkVqL2tRZW9TdUIwVWVjcitQN2c9PSIsInZhbHVlIjoidjFPM0hSa1NVakR4M2NtYUJQa2FPU2Q5NjlFeE54OVgzNnlCcWk4eGh4MTF6NkZCMTRDRVE0VXNVKzR3THVhMUlXMUh1QjVsN1R5WGpkMmZmYlV3Z2hzanpJeGFLNEtnbE1sdGN0YVh5bjQxSXFkTXBOS3o2SEh1L0hPL2JRYkxPUjQ3eFJ1cXJlNU1rTFQ0RnA4RVpXNTl6YVRVQUo2ZGoyay9GRzNBWUkxSVVqUy90dXp6R3Qxb05MRXdCT2U2RjNxVG0ySXJBMkxKOFo2S25MUjhBQjVXN1NFY2V3Sjc4b0x0czk5S2N5NkRiWHl2eU1IdEZOZEVaUktZSmoyNWVQNlJJQ3l0dnhvTzhTR1dmYmkxZDZTVmJsWjVaRVgrdFhUdGt0Tks0OHlVRUpvaU9lc3NuUE5MR1RKUzlnRFNCRGRxS0VDNENQZXczVDVSQVVPZFV3aXlMMUF0VnpjUWhZajRGOW9Fd0NlRjZpOWEzc2VUQVVNemFtdE14VzNubDJudVNwbFFvOE10VG82Rzl4ZUxkSlhQMTRHM1JDTHNnY3NZdWVHM0ovMDdIbFBuSG04ZHplTEJvL2RTZFh4dmpRUUNZcHpva2duMkJFa1JwaytSV1E1V3dha0xMLzZtU1g4Y09mNE5aU2kySW9mMkVsRFRlVkRpaVBST01JdW4iLCJtYWMiOiJlMjA4ZWRmZTJkMTMxNDZlMzQ4ZWNhMmMxYWVjYmY0YTZhZDg5ZGNkN2E4NWM1N2I5OTJmYTkwOGJmNDdhMTZkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVWVVVDL0JuMjd1VmpCT3ZpbXp1REE9PSIsInZhbHVlIjoiMDB1SG91c1JIUDdyVW5YZW92bWQ1RVRqVFpEaWtvQWRmL2NFVG5wVmZQSjdMUzNIVXFIazBTdXdubU9MSlo5Nm1pVVpLdTg0R2dnR3ptUnpsbzFaNlI3RHZ1aTlYM0pIS1ZzRlFHVHV6djlOalZwNXoxU29ISnJjTUJSWnFWamNrUnFnbFlUcGxteW5qRk5oTWNaRmt2T2dyS3dxMFhJeE94bFlRQjFobFdjcWJySGlZcSsxckZQTUZGdE9EamNzKzd1aktvNTNwbDV6NGlFTlhvMHBoSFZLNVJwWlpDL0QrNDV6N2dPOTZJSkw4dE9QMmZCUy83Vk1JaDJ6c2FCOFRjWDBzOTBRd1RSdEVTVGo0MHJOR3B5Q3lQUjJtM2RrejhVZ1BIZjA3aHBLTHdnTVZZQmV3RzVaNm5BcnN2T0RjN1NGcTZZTnp3Vm94MmIzTStiK1lGZ21WQXFpaXRLNHNTNDNrdlR4eE5BVXgrVlVPNmJqUFRCNGlxOEpnbm0wOE9yczVPVFRMSE80NC92NXRoUnB0SVBwSVJlL081YzhFeWZKTlpDNzZYR0FuaUlscUJ6MEVQUFBYRzlyaUFJa1p0WFNGRUgrNVVmUDdQcDJLYzBqQjFBWGZPdU1rSlFScHlmdFVtYjJJcG9oVXRiMEljR3IxU1RJZ3U0TWxWUHYiLCJtYWMiOiJhZjA2ZmI4NDQ2ODdjYmU2NjA4MjhlMGFlOWJhYmQyM2Q3ODNkODMyNzQzMDU3ZWJlYzU0OTMzNjY5ZDVhZjc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679328010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1523744308 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523744308\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1733981552 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpmaDRBcTRJUXNEeVg4N1dQNFJiRmc9PSIsInZhbHVlIjoiYW1RNmJ1NFZ6cUhnelhHRFQyK3FKOFRXb3EvV2QyeVMxV3JJSTNRZHBmYStyOFZZbDIzZ3BTazB4VitPbitOYWZIQUhtTzlWZ254TVJDY1JKMjFMQis3UUJVZTZ0L3lrUkJUM2lCcWg1TUp0bFpUOHlnZmFNRWw0dUtCV2NVZkQ1Q1NBTnhQV1poRnFIWVVtbFBBLytpcFJiVG5sa2hrNlZpbW9VNmE1Q2sxVXpPL2czRWlRdGtJU2NMQlduUmpKOHdPMW9wNVpOVjVlc2M4WnNvTGpZTk5OZC9tWktOWnJ1TS81aXVWZU1obmVVc3lxd3pYWG5EMS9PQlFuallpRzlPaUh5TUtDVFRTeFAxMVQyVVBZaGZtVzdiei96ZnRzWmovSHZIY1ZXdVdoZm5wVDYxekQvQlFuNHZmQ1o0MkZTL1hybXNIdmNzU2J4Zk1oL0tMUk1DOHAzTlF4dzJiWHo0N2RkaXBOSDIvN3B6d2dUYmVtU0x6dkE1UHd5MG9ob2lzQXF2K3pza2hSUjZVRURCTy9GWnZUblFVbld2aG9DeDg5NjdVanBPbWE3dlJlQ3VpNUlmNXVrc1ptenAwK0RMV0RmVHo2cGloUXRGUE9sdHFQT1BpaDJmR25SczBMb1RLc2l6TlNRZnNpbGdVRGlWUm5ndmVKSHp1eFFyY0QiLCJtYWMiOiI5NTQzNmM2YTI0MzI1YTRjZDcwNmNhODBiMGY3YzNjYzgzOWQxMzhhN2M4ODNmMGZiMDZiYmViMTUwNDI2ZjgxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iko4NnlRelJFeTVTU1ZZRTJvRzZDYkE9PSIsInZhbHVlIjoicGUrMGZETlRMYy81M0FKdkIvbE5reEdpTytjRlVvbFJmVlZ5M2FJa1ExeXB0WWhjZEhMQVpFZmt5b0xIQWRvVFBzZTJFYmNVNHp4OTR6Mk5VNHFML3hSRjhJWFhxQThjMzBzTFdnT3N1djZwNzdNVkJwL2xnM0t3em9ESDdWQ2JLSlRzNWQwNG9DQXliNFVKa1YzK0w2MmNKWWgzTFJxZVFKTmtSUTI4enJ1UXFzanNjTnlnQzE2WTdvb21ZQTZuTUdwU2swOHYvOWdicHFNS2VUeTZubVJSR0tXYmFGaHUzNzhucXhIbFgzcE42OWpqTGY2MWNDTjZGSXBjeEdzSlZQdFhMMWUySEExK1dVTFNMaFJ3bkFIN3JxYUJvbEJsN0pBZSt6TVBtRklwcHdiNzR1RlpNZGFzZnpHMjE2UnJvanpGcjIxd1doYU54OVkzUkNZQlVZblM2MEVERVVUa0I3UDAzazRKdEk2VkY5Z3ZZc3d3cjFiTnVTUEdydHhtTFRTL3NCUzBXcDhicGZOcS9BaWttM20zdXFvSXZjTTljdDJ3dDZGS2grclVMRyt2ejVLTHgzYTA0bjZZRUowazdWbE5wZGxpd25KSlVFNkRtaVI1WkgwRHdOZ0xjbEtYZ3lGK3NVWC9yTm5iRndtczIwUUYzNDhscFRMbHQxNmwiLCJtYWMiOiJhYjc0NjExMzgzZWYwMTE4MWIxMGEwOWRiOWJjZjcyMTE3ZDY4MGEzNTcxMzE5ZTE4NGMwZWE5ZmI5ZDk1ZmY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpmaDRBcTRJUXNEeVg4N1dQNFJiRmc9PSIsInZhbHVlIjoiYW1RNmJ1NFZ6cUhnelhHRFQyK3FKOFRXb3EvV2QyeVMxV3JJSTNRZHBmYStyOFZZbDIzZ3BTazB4VitPbitOYWZIQUhtTzlWZ254TVJDY1JKMjFMQis3UUJVZTZ0L3lrUkJUM2lCcWg1TUp0bFpUOHlnZmFNRWw0dUtCV2NVZkQ1Q1NBTnhQV1poRnFIWVVtbFBBLytpcFJiVG5sa2hrNlZpbW9VNmE1Q2sxVXpPL2czRWlRdGtJU2NMQlduUmpKOHdPMW9wNVpOVjVlc2M4WnNvTGpZTk5OZC9tWktOWnJ1TS81aXVWZU1obmVVc3lxd3pYWG5EMS9PQlFuallpRzlPaUh5TUtDVFRTeFAxMVQyVVBZaGZtVzdiei96ZnRzWmovSHZIY1ZXdVdoZm5wVDYxekQvQlFuNHZmQ1o0MkZTL1hybXNIdmNzU2J4Zk1oL0tMUk1DOHAzTlF4dzJiWHo0N2RkaXBOSDIvN3B6d2dUYmVtU0x6dkE1UHd5MG9ob2lzQXF2K3pza2hSUjZVRURCTy9GWnZUblFVbld2aG9DeDg5NjdVanBPbWE3dlJlQ3VpNUlmNXVrc1ptenAwK0RMV0RmVHo2cGloUXRGUE9sdHFQT1BpaDJmR25SczBMb1RLc2l6TlNRZnNpbGdVRGlWUm5ndmVKSHp1eFFyY0QiLCJtYWMiOiI5NTQzNmM2YTI0MzI1YTRjZDcwNmNhODBiMGY3YzNjYzgzOWQxMzhhN2M4ODNmMGZiMDZiYmViMTUwNDI2ZjgxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iko4NnlRelJFeTVTU1ZZRTJvRzZDYkE9PSIsInZhbHVlIjoicGUrMGZETlRMYy81M0FKdkIvbE5reEdpTytjRlVvbFJmVlZ5M2FJa1ExeXB0WWhjZEhMQVpFZmt5b0xIQWRvVFBzZTJFYmNVNHp4OTR6Mk5VNHFML3hSRjhJWFhxQThjMzBzTFdnT3N1djZwNzdNVkJwL2xnM0t3em9ESDdWQ2JLSlRzNWQwNG9DQXliNFVKa1YzK0w2MmNKWWgzTFJxZVFKTmtSUTI4enJ1UXFzanNjTnlnQzE2WTdvb21ZQTZuTUdwU2swOHYvOWdicHFNS2VUeTZubVJSR0tXYmFGaHUzNzhucXhIbFgzcE42OWpqTGY2MWNDTjZGSXBjeEdzSlZQdFhMMWUySEExK1dVTFNMaFJ3bkFIN3JxYUJvbEJsN0pBZSt6TVBtRklwcHdiNzR1RlpNZGFzZnpHMjE2UnJvanpGcjIxd1doYU54OVkzUkNZQlVZblM2MEVERVVUa0I3UDAzazRKdEk2VkY5Z3ZZc3d3cjFiTnVTUEdydHhtTFRTL3NCUzBXcDhicGZOcS9BaWttM20zdXFvSXZjTTljdDJ3dDZGS2grclVMRyt2ejVLTHgzYTA0bjZZRUowazdWbE5wZGxpd25KSlVFNkRtaVI1WkgwRHdOZ0xjbEtYZ3lGK3NVWC9yTm5iRndtczIwUUYzNDhscFRMbHQxNmwiLCJtYWMiOiJhYjc0NjExMzgzZWYwMTE4MWIxMGEwOWRiOWJjZjcyMTE3ZDY4MGEzNTcxMzE5ZTE4NGMwZWE5ZmI5ZDk1ZmY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733981552\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1346964603 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346964603\", {\"maxDepth\":0})</script>\n"}}