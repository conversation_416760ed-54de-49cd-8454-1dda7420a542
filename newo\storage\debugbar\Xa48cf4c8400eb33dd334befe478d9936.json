{"__meta": {"id": "Xa48cf4c8400eb33dd334befe478d9936", "datetime": "2025-06-08 13:49:14", "utime": **********.557516, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390553.131574, "end": **********.557553, "duration": 1.4259791374206543, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749390553.131574, "relative_start": 0, "end": **********.403207, "relative_end": **********.403207, "duration": 1.2716331481933594, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.40323, "relative_start": 1.2716560363769531, "end": **********.557558, "relative_end": 5.0067901611328125e-06, "duration": 0.1543281078338623, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43918496, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00605, "accumulated_duration_str": "6.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.514496, "duration": 0.00511, "duration_str": "5.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.463}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.533769, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 84.463, "width_percent": 15.537}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-938715562 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-938715562\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-777927149 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777927149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2128974460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2128974460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-381331537 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390543443%7C38%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlAvMTM5SElEenVLVXRJZTc4WFZJUnc9PSIsInZhbHVlIjoiTldyNml3STd3b1NES0J4TXFCV3l2MC9SUkJXVTZDNWdzNEJneFFVN3JjZ096YXV3NDI4Z29YMGQvVlpGdm5DbVpQRjNPNjNzTVdqWE1lVElhb0NEZ2JGQkZUSWxHcm1GaUxHZjVPNFZmQ0hGRkduZ2ErM24zcUY4bzhLNFA5RmlqZVhEc2NmdHZGODYyUXN4OGkvRmdKcC9qc05OSHZkSWRIcFVwV2QwL2tBOGxGVHAxaU53L1VWVjhYWWxiSDBRMkVJckwxMzZ4MllwVndFamFKNDlRUngrMlVqNW95djFjSDYyVzBPM1ZXWE1KSjJ4NTIvZ1duWG1PbjAzSExTS200Z0s4cmVpSXYya056aXkzUjJSUnpQaHBIYXI5UkJQTE5tM3ROUGZVckxvTllFc3hWVUwva1I4OHl0S0hSMGQxN0RkMGx3MzJ6c1pCdGErendDUi95b25vUmlYbkdiUm95SUMzNHd2TnJhUGF6Nm1JQVF6d1I3K0JvZ3ptcTdJdFlaZDlvRExTQUltdkFhV2tvSXhCSlljV2ZWS1ZRMGw5QytzdU1NT0MvQ3hOS1UxRTQyZy9Wa0hJT25nckh3SHpWdjJ3bUJnbXlralpJcjVoeWFYeHgxN0RZTXdlMFcxTHVIZXI4Nkd3Q2FqbDJaREVNVUhVb1F5T2Y1NHN5bUoiLCJtYWMiOiIwMTQ5NTllNTY0NDgyNWE1ZjlmZTU4MzdmNmZiNDQ4MzVkNTVmNGEzNWE4MDEzOWM5ZjE3MzA4NTJiYzUwOWYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpoUjUyekw0WGlTMnZYSGFJSDNQa1E9PSIsInZhbHVlIjoiQnNVMlV1bFViZmNLNG9FQmZwcEdPWjBLbm5Na1R4ekF3VnppNmFyOENCdlV3QnB3TG5udnh1dktqRnZTZ2M1THZYYTgrNWNHa3I1ZFh6MDBYRFRhcE9MTGdkWTAyMlBua0xSdm8ydU5FaWFNbk04Yko0cnZPL29ia2FYL3RzTnI4NlJNcG9SZVJIRkFiMklzU1dabk9mbWlNSTByTElLaWtEbDc1cjJGV2NOT3hha3RYQVA1Y1lPd0UxeWxEYzVBZWc2SEcwazJGYmg1UGVFMTByaklpK29vRWVQWnJMQWFuZG9WMHdBWHIyYWtTVVp3RXVuaUJiUGpXWUMrTFVKZW9oVEQ5SitEQTd2VnllUU12cG8xRVV2Zys0RmwzcHNkcVdwN2hEY3g4ZHV4blpKUmRiSUhPYnYwVWlQKzNYSmNEaFpkd3U1ejN3R2FuU2pXUmlhWUJBSjlYelhkTlBOcTE2RmJQVXJRMmcrNVNpT0pVaHM2eitESTNwQ1B5eTM2bHRxRHBNU1VJK3BEakFlZWlYK0JVWlczT3AxR1NOV2pxV2FwYUV6V0VUMmpZWVJuUWZxN0daWUlKQnpuUmNVZ2tTL0hGWGxSTlFiMHVNQ1UrTmpiZk9INkE0NG5LWWVsSDNzU0lJWGQvdVpEUE9tSHpaODlZVlNraVZSZ3VlTFkiLCJtYWMiOiJiMTE5NGU5Y2YwYTc0NDRjMDBlMjkxMDI3ZDYwYjdiYTY0YmFjNjhiMmVkMzM3ZTA4YmMwYmNmNGZkZTQ2MGQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381331537\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-519230782 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-519230782\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1325746001 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:49:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBHQ1dUN25sMUQ0T2xicG0zaWR5VlE9PSIsInZhbHVlIjoiSGVrWVJqN3Y4OHp1NFhRNHZ0bFQvTWE5M2RjZDdjRlpnTWFLTVNORkZCUys0N3MvWHlFR0tuWElzZ3Zha3kvVXdkZE85Yzk2d0d2bWswZlJJYUZFLzdBenNhV1Z6TlcwaHpQUDdsTUZSSktOUUNEUTZmUFo2RHpOaFNxTnFTVG9veTVXb2QvNVdxakNPNWxPd0VuaXY1OHc0RDBtRkpMRnQzWmJ3QVlnT0xPQS9HOXA5cXd5M1NLbU1kb1RwZG05TmxYL1RKUFpmQjA3WndiYU5BeXJQTS9uUG5oUkVqSTVZb3RyeG90UHViVDJabFl3amV3dnFZNTdYU0FLdEh5SnloZnQwbXlDRWllYUl1enhhUWFjUGFVMU1mTDF4djd5WmNSUnZxbVZHVFRqTVRZYllqdnpQbkJZalM2eDMyaytiTVlGeERCalAwVG1QbHVuUGNyeEdNbmRHaHFFSVBYZ0VScVBMbWFFS2tkUHFwNS9TZkpBdlZmKzJYUUdZUlRiSzhlWmxRTkFwK0RGRWlMWEtqQ3p2dnc2aUpDTG13MHdiRlFWblJKNU9BZ1hiZEU0RVYyeUhTNUhvRkhLK2ZBdEJiL0ZKRXB2Y0ZGZjR6MUxPeHMyaEczRG1oVCtGV1NOM05GYm1LelpmWFZPY0tpQURyQTdGZDRMLzc4MWFkVTIiLCJtYWMiOiJiODc3YTQ2YTQ4MTRhNmJlMjQyNjFhNTRjY2YzODUzYmZjOTI2MTE3NmU5ZjQ5ODlmODdmOWQzZmI5YWExMjAzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:49:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpnSmFBU1ZpaFFHUWlGdk1ldHlZdHc9PSIsInZhbHVlIjoiQWk2UWZuTElpWUEvaE1nZVVSTG9OQUpMdWl6bkQvbm1GV040RXRsNTdiL1AzWTdVZWp0cXFtZmJ1M2V5eVVVVjFIaXJOQ0tyV0xXK0Eza0lOTDd2dmtzL1E3dEdOV1ZPcnRhMklNaWRTTTJLMkJUVjlUbXIyL1hZSDdyM3MzMTdjdUlOa3R6bU90aXI2YzNxZzBYWS82YWNNQ0lYZEQ3Z1VGUEdjM0wyek5QRmlsUkdoYzhJM0ZSWFZIc0tLRTd5c1RaMnpKNnZKYy9ZdkxjZ2I0U2w4cXpUZXViYXZVQmJVMWN4Yk8vQmtjT3lOVE13bXMzcTJ0a3I2NzFkM3AvYXJwVzJVT0tEaVBGbXM5dzBmKzJreWFIMS9jQ3N5b0RwelJyVEoraElwOThPaHJUOGZrKy9UWFZMbm1zWjc5UWY2MEh5dnZjZmVweFYxdko2UElBb0FpNW05OThDMXVqaXpXbmUwcTJ4SDk3NUM4eXF3QmQzN2VjblBIaTlOOTZseDRVbXRPTEdnWHhhQnVzeEtDWWpwVklONWZDem5EM1V3MWQzbmdrYzBoMXM4WTB1RmZIRzgwM0xVRGRGR1U0TEYwWlhvR2ExUXZ5YnBaS2w0dkd5bENHcjZNNjVJdEJQNmpCWjhPTTV1YXN5MWpDRGtqYlJtdUh4UXBUTzh6NFQiLCJtYWMiOiIyNjBjYmUwMzg1MDMxYWQyOTkwYWU5Mzk4ZDU0MWEzZTkzZjllNmVhYTZhMzUzNjVmNTAzZTE1ZTEzMTVkNjEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:49:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBHQ1dUN25sMUQ0T2xicG0zaWR5VlE9PSIsInZhbHVlIjoiSGVrWVJqN3Y4OHp1NFhRNHZ0bFQvTWE5M2RjZDdjRlpnTWFLTVNORkZCUys0N3MvWHlFR0tuWElzZ3Zha3kvVXdkZE85Yzk2d0d2bWswZlJJYUZFLzdBenNhV1Z6TlcwaHpQUDdsTUZSSktOUUNEUTZmUFo2RHpOaFNxTnFTVG9veTVXb2QvNVdxakNPNWxPd0VuaXY1OHc0RDBtRkpMRnQzWmJ3QVlnT0xPQS9HOXA5cXd5M1NLbU1kb1RwZG05TmxYL1RKUFpmQjA3WndiYU5BeXJQTS9uUG5oUkVqSTVZb3RyeG90UHViVDJabFl3amV3dnFZNTdYU0FLdEh5SnloZnQwbXlDRWllYUl1enhhUWFjUGFVMU1mTDF4djd5WmNSUnZxbVZHVFRqTVRZYllqdnpQbkJZalM2eDMyaytiTVlGeERCalAwVG1QbHVuUGNyeEdNbmRHaHFFSVBYZ0VScVBMbWFFS2tkUHFwNS9TZkpBdlZmKzJYUUdZUlRiSzhlWmxRTkFwK0RGRWlMWEtqQ3p2dnc2aUpDTG13MHdiRlFWblJKNU9BZ1hiZEU0RVYyeUhTNUhvRkhLK2ZBdEJiL0ZKRXB2Y0ZGZjR6MUxPeHMyaEczRG1oVCtGV1NOM05GYm1LelpmWFZPY0tpQURyQTdGZDRMLzc4MWFkVTIiLCJtYWMiOiJiODc3YTQ2YTQ4MTRhNmJlMjQyNjFhNTRjY2YzODUzYmZjOTI2MTE3NmU5ZjQ5ODlmODdmOWQzZmI5YWExMjAzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:49:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpnSmFBU1ZpaFFHUWlGdk1ldHlZdHc9PSIsInZhbHVlIjoiQWk2UWZuTElpWUEvaE1nZVVSTG9OQUpMdWl6bkQvbm1GV040RXRsNTdiL1AzWTdVZWp0cXFtZmJ1M2V5eVVVVjFIaXJOQ0tyV0xXK0Eza0lOTDd2dmtzL1E3dEdOV1ZPcnRhMklNaWRTTTJLMkJUVjlUbXIyL1hZSDdyM3MzMTdjdUlOa3R6bU90aXI2YzNxZzBYWS82YWNNQ0lYZEQ3Z1VGUEdjM0wyek5QRmlsUkdoYzhJM0ZSWFZIc0tLRTd5c1RaMnpKNnZKYy9ZdkxjZ2I0U2w4cXpUZXViYXZVQmJVMWN4Yk8vQmtjT3lOVE13bXMzcTJ0a3I2NzFkM3AvYXJwVzJVT0tEaVBGbXM5dzBmKzJreWFIMS9jQ3N5b0RwelJyVEoraElwOThPaHJUOGZrKy9UWFZMbm1zWjc5UWY2MEh5dnZjZmVweFYxdko2UElBb0FpNW05OThDMXVqaXpXbmUwcTJ4SDk3NUM4eXF3QmQzN2VjblBIaTlOOTZseDRVbXRPTEdnWHhhQnVzeEtDWWpwVklONWZDem5EM1V3MWQzbmdrYzBoMXM4WTB1RmZIRzgwM0xVRGRGR1U0TEYwWlhvR2ExUXZ5YnBaS2w0dkd5bENHcjZNNjVJdEJQNmpCWjhPTTV1YXN5MWpDRGtqYlJtdUh4UXBUTzh6NFQiLCJtYWMiOiIyNjBjYmUwMzg1MDMxYWQyOTkwYWU5Mzk4ZDU0MWEzZTkzZjllNmVhYTZhMzUzNjVmNTAzZTE1ZTEzMTVkNjEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:49:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325746001\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1936089434 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936089434\", {\"maxDepth\":0})</script>\n"}}