{"__meta": {"id": "X0e908c4dbd49c9c7b481d5ef93d718ec", "datetime": "2025-06-08 13:04:33", "utime": **********.060309, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387871.664341, "end": **********.060338, "duration": 1.3959970474243164, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749387871.664341, "relative_start": 0, "end": **********.755578, "relative_end": **********.755578, "duration": 1.0912370681762695, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.755599, "relative_start": 1.0912580490112305, "end": **********.060342, "relative_end": 4.0531158447265625e-06, "duration": 0.30474305152893066, "duration_str": "305ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53622584, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02289, "accumulated_duration_str": "22.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8807828, "duration": 0.01586, "duration_str": "15.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.288}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.926619, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.288, "width_percent": 4.718}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.981315, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.006, "width_percent": 6.946}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.987997, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.952, "width_percent": 6.029}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.003591, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 86.981, "width_percent": 5.81}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.017289, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 92.792, "width_percent": 7.208}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-635688825 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635688825\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.000779, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 24\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-2030593767 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2030593767\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1326673092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1326673092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1528993297 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVISFBqYkV3dzZUSU5QUll3MlVkcHc9PSIsInZhbHVlIjoiUDVBdXFvTExJblZLS2lzMGM2QU9rRnVRS1dCVnhtTmZIVTBGVjRhaVI2M3A4S1dka3FBbEYrMWlDQStnNExCODd0VEgwS0pjbmh6akYraCt5WkJzZlM5ZmtKV0syamRoeVhaV2ozQ00vSEN0TGl2bzQ4WWhHVUNWcG9lcXJjcW5VUXpyWUVEWlQxaWd5Ylc0dGZoWGk4UFBFQUJFZ1EvbVFlV3BQT2c3UTJiakxtVThlcFpkQzg0dkpmWGtaNnNzUlhDdEZ0cEpDY2hCd3VpR01uek5rV1c4Z0t5QXd6Si9TTm94WU1xVUtzNk9LcWtSa3RPMkowT2tiRjdmS3FXYVhOeGsvSUNERXFQaXRseVNoeTEwRXI0QkpCSG5CekJBc2xhZDhHRkJuSHVjM0VibWpIVUdjWE5rbVJNOGp4eEdnTkNZQVdvWTNtQlNKdGR3NWt3TFVadXRDQW5CTzVRK05LV29DMFFyU010eVVWUmZaK1dLc0RIVFBpWk9yblIrMmt5YVpPdzF1NWs4em1TKzBqQ0tBTEFBKzBrM2RtbDVTcmFHZkRmNlROcllpeGVTcGU5WERMenh1cmJpaC9pODdFM0RjK3p0Sjk3ajIrc2oxUjhnVk5vUzV5NmN4NFhhUlZ0THlDZlBwYnByaFlmR2JRL1BJRmJVTHIwMGNpTmkiLCJtYWMiOiI4MmExYzA1MDYwMDE5MGJiZGQ4Yzc5NmY4MDI5Y2YwNGZkZDZhNjUyMGUwODQyYWRhYmVjZmJlZGIyMzAzMzk3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImF3LzJMWWxmWXBDRldOdlBSWExtUGc9PSIsInZhbHVlIjoidEh6anZCZVpzRFNHTTNzYW1TYmxKQVNnc3JnenljZXAzSDlIYWRSRDFVME45QU0zTGpFTllrWlh0V2tWaTQ4ejh4YTRFN29PT2s5VDN3WDV3YjIwakpqVXJSV2pRRXRicS91dmJITnBmNitDVnJ3OWhNcnMwdkxCMlhUK2tIbWc0ZUxQL1Z1cTIzUURnUkdYVG5hbmNxREYybm14ZUx0dkxoZ2lHemhOTUNSVW5UQU9PWE4rNDNsWFlvRTZRNlJqVWRPdmw2OGNZbTNXNCtRZ0RuWDR5T3MzMTdOUVRJVHdQWEw2b1RIczdGckI1dGM0bDdIdWc3aFh5ZHU2bXBvZk1ZcnBhSEhRQVdxTDhxSFd5MDZRZktLOHE4Skplc2xsYVd6NHhQTVk2OHVYa2xMWS9KTy9KQ2hpVHRoVE5vMm9wY1UzZGhPSXFsQ0JMV2VYWjcrZ1hHVGdUZGU3bGg2cEdIYW5FSkhRc0NUZU9WdUZRRGRoNVpmYlRNUysrRVE3YjdYS2FTc25JUlR3c3YwYmlUY1dWdkZZaFllTHBEcUFBeVFhYWVwa3JxT1VBZ2g4MDZEaG90U1Z3NnNkYVdMTXp2dkw3aXJjTDZCaElKZGtjeDlPRkRuSmVNcmNqcFJHbFp2TVB5NTZwbWpocFdCeEFBNUZidzFxa29HRytHdXUiLCJtYWMiOiIwOGVmOTQ0MWY3NjFhNTYxMTQwZTc1YWQzZWU5MDhkZTY3MjBmMjViYTdjNDkwYzY1MDY4ODg5MWQ1YTAwMWY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528993297\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1014791418 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014791418\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1282499078 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:04:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkE5MkdINklTUVNDZmlXVXpXRkJURFE9PSIsInZhbHVlIjoiR040ajlxWC9PRzlvSlBBeTNjbytvUE10ZVNmSVdRTm9OTk11MU90YS9HeE52cmIrY0dpQ0twZThxcFF4YW1OMDVyNjFHU0pUeXhDZkI2Z2FUMWdLVGU3WFFzam1LckhENVZWYytPeDFFTVdxTVR6bXVXK3MvaTU0MjNDT2RwUHJhbGNhZTRKRXdtTXdsOTlMeFNTRDIrem9vVndnNlEzNHRLRXkzRXJyZk4xQkprK0VYeFFkMXNYOWdWMTNrUWZvVmpGOWFjblUwNEhXc3plRzg5c2FCRVR0alVzNDdlbjAzaWt3cTN1em1UNmRlYlVYL2tCNGRDZmthOE8wY2dURlljMk5qdzNYMU1xam1hOXorYUYwN21ZVGFxc3JyOWY0Y050WDRYbUhkWFcxYW5OKy9ONFQrb2hYUGVOaTNEajhDaWlpWGZsNUh4NFhUTEhZb1Q5VHFiNlptZVlJTUQ5VFlzZzRKZ0Q2bHhya213VDFVd0MyWVpXaktOV1AvYTVobGJKOTVhUEtrbWtlZkdGaVpreGNkdmMveEFveDlqS0xETHlHK2V5amNqZE40UmxJaGF0cGR3dHZMclorZ20xK0hCeUtQQWpUZHFLcE1UdUNKL04yYTdlNWxRRXVRYXduT1p5K2pRSS9iWVQ3eWIrL2Z0YnNXTExNSEl6NFNPUjEiLCJtYWMiOiI4ODAzNTYxYjFlZGExYmVjY2IzMjNjYWIwNzFmMmFhYWQ3MTIwODU1ZDBiMTI0YjM0ZmU5YmFhYjQ4NmE3NjNjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlkvWHpYcTl0ZHh4VUhzdTBXV1lIVmc9PSIsInZhbHVlIjoiU0tTczBYcUhvcFd6dlZoL0I5TENLTTA1NlZlNVJOV0M2bnE4ZGo0VEdvNXdlNG83VEZqTStvSTRiSjFRTFg1dWZDdllOckppbXFhb2kxR0FxZGZXQ1NiWFVDTmtoZzNGaG9mVUFBbEtzQjVtbHRCSnkxTU1JVWw2dThRcnhjTGYrZi9zTXlwcUNtaWEvMkNFR0k3cGg5Njd6NEl0RHdvWi9FU3FQUDZ1M29BdXNWYkdxZ1o1eFozRTQ1amNpWVFaL0MyWlBYVTJNeXNIT0ZNYVppV0pIeHZndVVGSGE0UWl5M2FZTEFoS2lqOG9iUmx1NUt1NzFBMEM2NGlhaWZuTUdRbngwb2ZRVXFXaDFienFHWkc3SmliWnFWeUZtbVhnZFNIUmFtVWJVZE80RFdpcWhIbHRycmRJRWxHQ1F3Z09lcEI3VUFPSXZwcytkUDJSQjZMcEwxMklsWWI0UVkrL3hsWWI0TE9LaEQzYzNpRDdSWEFscUJ0bUNsYXhBYnJXTnB5ek1jaERXNGxCWnYzcGZhb3JpR3RFUm9lMkNpcjFoR1N3ak02aGIyb2RQMnFBMGtzZ015RExxY09TUTFudWxpa0dObm16ai9Rb242TnAxSVZ3MFJjVm8wMUJEaFUwNXNuR1dSVndaV0QvR3NXV0hqVjRZekppWnpwc1RHMnAiLCJtYWMiOiJmMDZmMGE3NGQ3MGNiMzRjM2E3ODEzZjUxY2I3NGVmZTZjYTcwNTcwNDliOWM2NTMyYmI1YzU4YjZlOTQyYmNhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkE5MkdINklTUVNDZmlXVXpXRkJURFE9PSIsInZhbHVlIjoiR040ajlxWC9PRzlvSlBBeTNjbytvUE10ZVNmSVdRTm9OTk11MU90YS9HeE52cmIrY0dpQ0twZThxcFF4YW1OMDVyNjFHU0pUeXhDZkI2Z2FUMWdLVGU3WFFzam1LckhENVZWYytPeDFFTVdxTVR6bXVXK3MvaTU0MjNDT2RwUHJhbGNhZTRKRXdtTXdsOTlMeFNTRDIrem9vVndnNlEzNHRLRXkzRXJyZk4xQkprK0VYeFFkMXNYOWdWMTNrUWZvVmpGOWFjblUwNEhXc3plRzg5c2FCRVR0alVzNDdlbjAzaWt3cTN1em1UNmRlYlVYL2tCNGRDZmthOE8wY2dURlljMk5qdzNYMU1xam1hOXorYUYwN21ZVGFxc3JyOWY0Y050WDRYbUhkWFcxYW5OKy9ONFQrb2hYUGVOaTNEajhDaWlpWGZsNUh4NFhUTEhZb1Q5VHFiNlptZVlJTUQ5VFlzZzRKZ0Q2bHhya213VDFVd0MyWVpXaktOV1AvYTVobGJKOTVhUEtrbWtlZkdGaVpreGNkdmMveEFveDlqS0xETHlHK2V5amNqZE40UmxJaGF0cGR3dHZMclorZ20xK0hCeUtQQWpUZHFLcE1UdUNKL04yYTdlNWxRRXVRYXduT1p5K2pRSS9iWVQ3eWIrL2Z0YnNXTExNSEl6NFNPUjEiLCJtYWMiOiI4ODAzNTYxYjFlZGExYmVjY2IzMjNjYWIwNzFmMmFhYWQ3MTIwODU1ZDBiMTI0YjM0ZmU5YmFhYjQ4NmE3NjNjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlkvWHpYcTl0ZHh4VUhzdTBXV1lIVmc9PSIsInZhbHVlIjoiU0tTczBYcUhvcFd6dlZoL0I5TENLTTA1NlZlNVJOV0M2bnE4ZGo0VEdvNXdlNG83VEZqTStvSTRiSjFRTFg1dWZDdllOckppbXFhb2kxR0FxZGZXQ1NiWFVDTmtoZzNGaG9mVUFBbEtzQjVtbHRCSnkxTU1JVWw2dThRcnhjTGYrZi9zTXlwcUNtaWEvMkNFR0k3cGg5Njd6NEl0RHdvWi9FU3FQUDZ1M29BdXNWYkdxZ1o1eFozRTQ1amNpWVFaL0MyWlBYVTJNeXNIT0ZNYVppV0pIeHZndVVGSGE0UWl5M2FZTEFoS2lqOG9iUmx1NUt1NzFBMEM2NGlhaWZuTUdRbngwb2ZRVXFXaDFienFHWkc3SmliWnFWeUZtbVhnZFNIUmFtVWJVZE80RFdpcWhIbHRycmRJRWxHQ1F3Z09lcEI3VUFPSXZwcytkUDJSQjZMcEwxMklsWWI0UVkrL3hsWWI0TE9LaEQzYzNpRDdSWEFscUJ0bUNsYXhBYnJXTnB5ek1jaERXNGxCWnYzcGZhb3JpR3RFUm9lMkNpcjFoR1N3ak02aGIyb2RQMnFBMGtzZ015RExxY09TUTFudWxpa0dObm16ai9Rb242TnAxSVZ3MFJjVm8wMUJEaFUwNXNuR1dSVndaV0QvR3NXV0hqVjRZekppWnpwc1RHMnAiLCJtYWMiOiJmMDZmMGE3NGQ3MGNiMzRjM2E3ODEzZjUxY2I3NGVmZTZjYTcwNTcwNDliOWM2NTMyYmI1YzU4YjZlOTQyYmNhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282499078\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1871492593 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>24</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871492593\", {\"maxDepth\":0})</script>\n"}}