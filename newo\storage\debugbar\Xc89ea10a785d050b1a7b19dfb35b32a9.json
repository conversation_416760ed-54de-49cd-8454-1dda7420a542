{"__meta": {"id": "Xc89ea10a785d050b1a7b19dfb35b32a9", "datetime": "2025-06-08 14:46:32", "utime": **********.396379, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749393991.839139, "end": **********.396398, "duration": 0.5572590827941895, "duration_str": "557ms", "measures": [{"label": "Booting", "start": 1749393991.839139, "relative_start": 0, "end": **********.306035, "relative_end": **********.306035, "duration": 0.46689605712890625, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.306046, "relative_start": 0.4669070243835449, "end": **********.3964, "relative_end": 1.9073486328125e-06, "duration": 0.09035396575927734, "duration_str": "90.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45184904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027529999999999995, "accumulated_duration_str": "27.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.343498, "duration": 0.026199999999999998, "duration_str": "26.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.169}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.382601, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.169, "width_percent": 2.252}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.387344, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 97.421, "width_percent": 2.579}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1785014694 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1785014694\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-343736305 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343736305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-942390753 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-942390753\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1027522765 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749393346722%7C61%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkF0MW5SdXlsZnp0aTBuUEc0L21sS0E9PSIsInZhbHVlIjoiVXBBd3l0aUVEcGlTOTVIaXA1TXFES2dNbGU4dmxSSFUzczFNenVxYTlyMmpMNk5sZXBNU2FNRlp0YzNnZTlIQjdyUXE3UWt3dTlvdUpSeXU4WG1YTEFPNkhHRUxvYkUycG5VQVNGU0lNYWRZejFnaERLUnY5M1ZHZEhRZVFXRmFldy8xa2RpMmhxcTVTNnd5cVFHYzJqeTFNelQrUTJXNFA3NGFPTk1zVlU3MTk3SVBicFJjbElqalFBTElvd3ljQWppcURIakUvV0UzdjZSVFFkdWR6OHJoUllDSkh6K3AzRTZwNVBqeHJ6bHBzOTl3aWhYcWNMRGRqUkxWRHB1NFRWNGJhMVFkcitVcWVOVmxTelhmZGZabTF2ejVGQnpjUUdkYURKQlY2MWhFeVBKTzgvR3lFdmVHL1FPU2JkczJrUWNreTA3bkQrMW5lOFVQN0h2MGVQQ0lTN0l1aGhKelE5S0VlMW5lbWZhVkk3UUhFbW9Bc1Jrb2JQVXpaYzRYYjdEdjEyRDM3MmNKRjM0ZTg0a2J3V1QzNkhwSk1ocmsybmEzTFlCNFNLa241ZkNWVDE0Y1RIb1l4K3JnODVnekZncGdwOFhuREJ1TlljZ2RObERIL1J3VjBKTnIyblhiVk9vRjZReFk4U2dNSzdVUGsrcVRtMk1KRFNncVI5RGsiLCJtYWMiOiIyYzA3NGE4MWJmMTFmNGUxYzBmNWZlMDQ4ZTBlODNkMGY4ZWQ3YTA5OWI0MmI2NWM4MWQyMjY0NTQyYjEwMGUzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxjTmphUStKaTAwdER2Ni9BTjBDTVE9PSIsInZhbHVlIjoiZU0yWXBZYW92QzhxSGtiRjZURDBrUlBseHJRU3pqZ0IyV1FRZUFxbnBKQUV4Y0gxWTlDOXFUYXpma1lBZEU2Q1NPdjlpQk8razMwSUhpUlR6eHpTaXVlZzd2WDhIYXRCTnpwMEtuZFBvQ1pVVDRTb2Jvalc1SVdEUHNpY3dsZWFBSjBaUGpzY0ljdTFJajFSQ0RDRXEyT0hmU1h5VElWZmNnS1B4NEd0ZWhiLytidTROQ280dHh6VHN3aVlORC9tck95V2NtSnNIb3RZbDFKWnpJOHdDTHBnamtGb0h4M3pTMUovR3p6S2lxSXV0Q05VVnV3MlA0Y2NpM3JkRlFpUHM5aWVLK29LZGZZR3JhNmtjQnNKWUlNNDIrUFdmUUF4djJ3UlZvQmlFV2FzUW1ud2twYVBpdElKQW5YRmR6S2MxVzBJZXdKdlpQSmxHdzRPNUo1TE1HRDJNbHNpSGNCTzY5SlFRMy9mb2JZdjlqd3lVS1dZeUwwU2hvTU9aTFQ0cFQ0aGhFNXJueC80SVZPaGc4S0ZyVnUzdFNZV3ZYUXdUdGM0bGpZdDNBQnE5Rm5mQ3J1a2JqeWRqL3ZPeTNCeFE2WWZucUNtRzZVWEdFV3BTVjdoNTdRNnpKYzZXcFBTVWhSVWN2N1FNR1FteWMxZ0hXVTdmYzZ3T2RxTkVSYU0iLCJtYWMiOiJlZTAyODRmOTVlNGQ5NzZjYjM3ZmJkZGViMmI2NWI4MDIzZWFlNDk3NWNjOWRiOTQ3MzRmY2Q0NjNkOWYyOWUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027522765\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1419658341 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419658341\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1698795494 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:46:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRYemhLQlk4UFdrNm51VnlLSGZRaVE9PSIsInZhbHVlIjoiMHY1MGd3cVlMT09yRnlqaXNHNDJvU2xYT0NvLzdJeEZYdi9hVnRQSVNPVWNQMUdrSDNCYUdvdVFiN2dqY3FVSnBaYjlTUzI4Z2NtblVsWTRVUnpHLzJuN1lMVlU5Qnk2MWpiSFBIcjZiazYva2FUcXFXOEhySkdyRU5salM5QXpZMHVkZ1k0b0NoK1E3NEN3K2VaeXlYdmYvbENla29DeWRqbWFXS1BEODJzaUpnZm9zbS8wL3RTY0VlWlJJV3VrRHBzakFQY2YzeWFRcGlsczZzaXI3Tkd1eHREWG1vZFdycVoxbTZuSHV5dWptWHRmQnhPS2xNRjczSXVkakwxd2s1OVptT3FqWU1VL3EzeDI3SEJqbEhkcFA0UTJKUDF4VGplRjZhVk5NaTU2dm1xSlZkdkN1SjdzUGpjUldYbVFrTW9TNis5VVRQbWR3QWMwb2RtVEFjeWVCUVZ5Tmcxa1hLb1l3RDlvWEdKaUJmY0RVN3NpUjIrd25mRlFBSE1TK3U2K011REVQSitGS2NlcmY3VnI5RjM4aGhlZjY2SHZCdk5xYi8rKzY2cUlTVE02MUU0eEZYOVVSVzA4dERaUTV5bmhpU0M0RTFSUjg2UncwS1F5QnVpdThXQkNjV0Q2NjB0b0hMVlJXZFlPaElOamhhT1Z0NkN3Wmx3eU1CeGQiLCJtYWMiOiJkNTZkZDJjMDY0ZmQ5ZTA2MTZmNTM3Njc4MGNkMWNmZmYyYTQyYWMxYzQyYTI2MjYzOTQ2ZGVlNDAyMzIyZDdhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjltQy9NREtCU0ora29OZURVVi8vN0E9PSIsInZhbHVlIjoieGNVOUdZaHc0b3g1YVRvZ0F5YVpGOUVBTEtHQ2NRTFdkU3RaRnppZW1HbGtFU2V3RU1pK1E5WXlINlZESitBQ0JoNENPWW9pK1FJUzZROHQrNEEwbVM2QXljTjVoSkcwRnBRMXRMb0Z5enkwYjg1ZC92QXVVMHgrczBFNFBuS1dXV1dHS0NhbnFiMUxxaUJMakt2MGNUdTFza1E2b2EvMEpaYjkxc2RrWjZDemNGZ0JpamtrRWg0MWJST2IySUh5N2hLdXNPemtaWENMUWRpTTVaU3d5S1VqMGVQc3YzNnB5MTBpSXpWdWduZ0FvWHhLWGtwNElRSFdzamIzcTl6a1BadlA1L2tCZ0dkSXJTSWc2ZUw5N0dIa09Gdi8yRWdZaVRHZitwbjhyckh0OFpJb2xuYk1xZjRObmpvK1c0bXRlR3k0UWxmdkp3UVE2TlFFN0ZOQ0pTcVBLaVJqUnZPd2NMcnNWZHlmNWowYnE1L2tIUGpRQlE3aWhCY3l0UW5TdGh0RHFGTm5ZNnpkekV1RXNlWDZZaFB5SVVsWXdMOURYWkUySk5sTXpNV2xQZDZ1L1dYQlNMNTlteHB1bHRZcHJZMS80ZmlvV3NGT2M1cUVvWHFFQi9DNlBrLzRUYk43VHp0MXY5SW04RUszWkM1OXFueEVsZ0FKcXRhMUZqeHciLCJtYWMiOiI5ZTU3ZjA0M2JkNGI5MjE3MWRlMzMwMDFlN2IzYzJhYmUxZDY2NGJmMDk5NTE3ZGZiYzQ4MDliZThmZTY2ZDBkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:46:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRYemhLQlk4UFdrNm51VnlLSGZRaVE9PSIsInZhbHVlIjoiMHY1MGd3cVlMT09yRnlqaXNHNDJvU2xYT0NvLzdJeEZYdi9hVnRQSVNPVWNQMUdrSDNCYUdvdVFiN2dqY3FVSnBaYjlTUzI4Z2NtblVsWTRVUnpHLzJuN1lMVlU5Qnk2MWpiSFBIcjZiazYva2FUcXFXOEhySkdyRU5salM5QXpZMHVkZ1k0b0NoK1E3NEN3K2VaeXlYdmYvbENla29DeWRqbWFXS1BEODJzaUpnZm9zbS8wL3RTY0VlWlJJV3VrRHBzakFQY2YzeWFRcGlsczZzaXI3Tkd1eHREWG1vZFdycVoxbTZuSHV5dWptWHRmQnhPS2xNRjczSXVkakwxd2s1OVptT3FqWU1VL3EzeDI3SEJqbEhkcFA0UTJKUDF4VGplRjZhVk5NaTU2dm1xSlZkdkN1SjdzUGpjUldYbVFrTW9TNis5VVRQbWR3QWMwb2RtVEFjeWVCUVZ5Tmcxa1hLb1l3RDlvWEdKaUJmY0RVN3NpUjIrd25mRlFBSE1TK3U2K011REVQSitGS2NlcmY3VnI5RjM4aGhlZjY2SHZCdk5xYi8rKzY2cUlTVE02MUU0eEZYOVVSVzA4dERaUTV5bmhpU0M0RTFSUjg2UncwS1F5QnVpdThXQkNjV0Q2NjB0b0hMVlJXZFlPaElOamhhT1Z0NkN3Wmx3eU1CeGQiLCJtYWMiOiJkNTZkZDJjMDY0ZmQ5ZTA2MTZmNTM3Njc4MGNkMWNmZmYyYTQyYWMxYzQyYTI2MjYzOTQ2ZGVlNDAyMzIyZDdhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjltQy9NREtCU0ora29OZURVVi8vN0E9PSIsInZhbHVlIjoieGNVOUdZaHc0b3g1YVRvZ0F5YVpGOUVBTEtHQ2NRTFdkU3RaRnppZW1HbGtFU2V3RU1pK1E5WXlINlZESitBQ0JoNENPWW9pK1FJUzZROHQrNEEwbVM2QXljTjVoSkcwRnBRMXRMb0Z5enkwYjg1ZC92QXVVMHgrczBFNFBuS1dXV1dHS0NhbnFiMUxxaUJMakt2MGNUdTFza1E2b2EvMEpaYjkxc2RrWjZDemNGZ0JpamtrRWg0MWJST2IySUh5N2hLdXNPemtaWENMUWRpTTVaU3d5S1VqMGVQc3YzNnB5MTBpSXpWdWduZ0FvWHhLWGtwNElRSFdzamIzcTl6a1BadlA1L2tCZ0dkSXJTSWc2ZUw5N0dIa09Gdi8yRWdZaVRHZitwbjhyckh0OFpJb2xuYk1xZjRObmpvK1c0bXRlR3k0UWxmdkp3UVE2TlFFN0ZOQ0pTcVBLaVJqUnZPd2NMcnNWZHlmNWowYnE1L2tIUGpRQlE3aWhCY3l0UW5TdGh0RHFGTm5ZNnpkekV1RXNlWDZZaFB5SVVsWXdMOURYWkUySk5sTXpNV2xQZDZ1L1dYQlNMNTlteHB1bHRZcHJZMS80ZmlvV3NGT2M1cUVvWHFFQi9DNlBrLzRUYk43VHp0MXY5SW04RUszWkM1OXFueEVsZ0FKcXRhMUZqeHciLCJtYWMiOiI5ZTU3ZjA0M2JkNGI5MjE3MWRlMzMwMDFlN2IzYzJhYmUxZDY2NGJmMDk5NTE3ZGZiYzQ4MDliZThmZTY2ZDBkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:46:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1698795494\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-16315996 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16315996\", {\"maxDepth\":0})</script>\n"}}