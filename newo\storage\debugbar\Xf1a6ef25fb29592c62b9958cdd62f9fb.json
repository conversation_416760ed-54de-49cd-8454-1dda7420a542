{"__meta": {"id": "Xf1a6ef25fb29592c62b9958cdd62f9fb", "datetime": "2025-06-08 13:04:28", "utime": **********.804254, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387867.546511, "end": **********.80429, "duration": 1.2577791213989258, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1749387867.546511, "relative_start": 0, "end": **********.666253, "relative_end": **********.666253, "duration": 1.1197421550750732, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.666276, "relative_start": 1.119765043258667, "end": **********.804295, "relative_end": 5.0067901611328125e-06, "duration": 0.13801908493041992, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45161832, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00675, "accumulated_duration_str": "6.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.747207, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.704}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.776094, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.704, "width_percent": 15.852}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.785017, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 79.556, "width_percent": 20.444}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-202988016 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-202988016\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-430970956 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430970956\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2057233878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2057233878\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1178874680 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387754593%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBseUZuNStsZXM3VjRQTVRFSFMxUWc9PSIsInZhbHVlIjoiUFh3cVFObUgyNGIyWUlrR0FJNnBGdjN1ejR1TDRsR2xWQUtJdkIwMlUvZDlyTkwreEdFWHR5blRqUGRsQ0U3ZFhzWHdRNEgwOUU2QjE4cXNZVjg4Uk1oS1dKK1Y0TXdJS1R5d2RoaFlOR3N0a09UbHE2UjJ5VmRKTldCUmN5NUF5aDd6NGNwY2c4UGt4cVdVaXdLNlBDa0NreGV1blR5MnFKeGQra1dkd25LY213Sjl0NVRwaWFJa1VUTXVTK1AxdXNNNVNKSDRMMFV6UG9HNXJRN0VpS3djQ1JyblNHLzR4UlZObklVL0taRVcxYzJ2QTFvc0xUYmY4TjB5VFRsYndkUFUzUlo2Um12bzRTOUw5R1dxYm9RMUVNNC95R3FDLzBYVFh0Qks5QzNEWXZmQXJrUWVCK1poK2w0U3NlWlFIUTBhd0VuZHRoL082b3JLY212K3NJUVViOUZpdzQzcCthUFQxYWFtbmdZbWtSYzFmT1gzYi9KK0c5OXRaMEsrN0JoTUU1NytQUlJCTjNxVWw2UHdIQk5EdVZ3amtSc1dpeUlRa0t3dk9iSnFLSXZRSFEranpiTjhtMHJZQVFGZ0ZuL0xMdk9OQWNCNDBodGwyaWNrUnRnaDNBZmd4Y1hTRzdPbGJHWmxmVkM1K0I1RVl4RUQwVzhGcWY2TFJ6Rk4iLCJtYWMiOiI4MjRhZmM2NDQxMWZjYzgyYWE0ZWYyMTI3ODU2ZmFjMTczNTM1MzM5ZDkxM2VmODk5Zjc1YzYyMjRhOThlNzdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InZ4a1J0Q2dyR0h2QWpGTTkzNUhTd2c9PSIsInZhbHVlIjoiS0Z4Z2JVb2FvTjgvTENlaFZ2MVZjdVMyMlJrRlBkc0ZZazhNalI2VXlNWGIvTFA1aGt1VUd1UFFuNFNmK2VHSyszcjJZMWROWDltUG42WTBmVEtmUm5kWTVrOFJSSW1wUXBsQlpTdWNXa200WVVDMVg4UVFuWHNOczJwem1ORlVPTk1pSlI1eHlGWFZUZnNhamJCaE5idE9YQVkwMVBLY2FYVmxjTFI4WmFDdnBWMmpCbGsvZDkzbzBoaHdNSE9ORU1tUTBlWGtCUThyZGlXR1hzdXhiTEp5anRCZnBlOWxSYWVBbGRKOUxJWnMrVjZLcjIxWGJVUEFXZFczdENxQkhuZllXek8rUCs1ZzFZcHQvTjVDNWxFTUJSbkJBTVF3ejU4Y2tRcHB1T01wcVBSN2NnZ3dFZGtvN0I2eXVjYUFOcC80bFkzKzRlOG5DQmE3NU02L2I5WHd2WU1FYnJxcmJ0N0x3YzhjQ0tLWUlISnRYSU9weGhwK1ZDYkh2VDRoNyttRXBMcU0rNHNlaHF3c2RIbkNxQ0I2bkx0RGdGMDNiNWZhTnVDM2xnMmpqRTFBNXExRnJPN1BxL2t5Mld1TWgyMVpYdkt3eC9VU00vTjdvTWVqamxuSWNBWFE2RW1HNUR1RkQ5bEVUV2JKcFNVb2J2R0k2aXkwY2FaMFprSVciLCJtYWMiOiJjNjEwZjVhYTk1ZDgxMzQ1OTJkMzdiMTg3Y2RiMDk3N2M3MmI5YWJmODdiNzJjNTQ2NGIyNDVkMjdjNjhhYmVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178874680\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1974236732 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974236732\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2071838104 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:04:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhKM2RCWHMxUzdJQ3lFZkF4S2ViSFE9PSIsInZhbHVlIjoidXpTSmd3NGtRZmo3ck9BTUtCSUllYlZ3TDRxRm05NVE0aXlCTUZUWUhhSUMrUy91RndyS29UYXlySjVxcVJSY0NpRVJZVDJhLzVwN3YyMEppb2NsTlk3b1ltQ3N5aldUNFpxRmlWL3pZa1V1eHAvYkRFcGNHeWNQUmhDNCtudUx6Vjl5QThEOUNoK00ySjloUXlObzNKZi9oWDFqZWpIeVQ4dE9CR1cveW9jcUtKWE42T0NhQXUyVmhaR1RCRTZtRmd4WVNwL1hVc3BGRDF1NjZ1dGZQQ1V0YjZjN0hGdXloV3ZlNXJCZ3Z6UVpMY1BFbWxOZFJuakUrbmZKZ21aZ094cVAwZVFqU05GR28wRkZzN1VPV0VhNU1ZeTVmVk9SZmxmWm5WWFVRdUVqU1QrK0ZMQ0cvVjUyZ3oyM3lSaXBwUUdHZmlBK2xvUzVrckRQZExYVThQaTdiejh2RVBadGVIS0JwTktCaUVSaGNneEFUT1RnMWRCWno3cG8zaEp4Sm9RSTlzOUNiSUlXMDV6YU9lZlVwSFZiZENodDdWVmJKNDRDQ2l1Vm1XRXh3aFFYbExTK01DdTNvVzcxcGxzTG9DKy9VbWpXUUtkVTJLeGhzVzEyVHZUS2NQT09jajFQYWkwaHdMZkxteThnOUxqNjU4d0NZdy9XSWRMK21IYTQiLCJtYWMiOiJlZDZiOWExZjAyZDZjZTNlZmRmNWE2MDFlZGM3ZjU5YmFhZGM4YmQzZDM0NWY1YjE2ZTU2ODE0MjRiODljNmY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJDQmlrWVVnRHpJcmpvZjVYbjZ2T3c9PSIsInZhbHVlIjoiTER0V3o4MTNvajh1NWVUSmMzRzFtRld0MFBlK0ZTdkFhanlwL1VGTkVicW1jSy9OU2Vpc3JVQzJicGhjY0x3UEFVMmNMSVpxNEE5MkUrQUtvL3hlZUhtRzZtaG5IVEZCaDZSRFJiTGYyZWp0T1BKQldvSHdRdU1yUXFNN0xqazUySnZMZ08xQ1RvbEVsekNveDBYOWlVYi84Z21BMGZlSW1ubXcrbEd4L1pUZCs4TGpsLzRGSXRxemhKSUM0am5pNU5SOU80S0VOZjZBNWFFUDY5eEhIaHVtRmtQL2NpTkV3VGdwdzJmNUJhUk5Qa1VueHdSRytWUUx5OW5PWUQxcCtSUTF2OTBiYTlIOWFGVmJBU0RQQ2VHU3ZVM0hwZHlqWHNUZVZ5NUdpVlBKSHdyMG53b2E2NDd3U1RzbEsxcXdhNysyUWszb1EreUEwd0lLZG5TQ2NqeDY1djVWaGhucWppWWRRUm5oSTIxWndJZlpDb3lVL1JzVGxESXo1OVZEb1Q3eS9yU1IwUy9IT3hTTGdMRlRBcnJXV0RkRG5XU1BXdkRVQ2g3TkNFbzVINlZDU2pONmIvdHlmSE1WOE0rYUtFenJlSGhtYnBGQlRycXNWQ3ZFSGJiU1dKcFY4eDFhR2lGTklnSm1lL21LZGM0bEJpV3NPQ0ZtTUtBdFlRWFoiLCJtYWMiOiI4YTE0ZWJlOGIyODU5OWVlZmMzODY5MWM2OTVjM2ZmOWRjZDgwZTY3ZjM0NjA0MzhiMjZhNTcxYTEyYjg3M2YxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:04:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhKM2RCWHMxUzdJQ3lFZkF4S2ViSFE9PSIsInZhbHVlIjoidXpTSmd3NGtRZmo3ck9BTUtCSUllYlZ3TDRxRm05NVE0aXlCTUZUWUhhSUMrUy91RndyS29UYXlySjVxcVJSY0NpRVJZVDJhLzVwN3YyMEppb2NsTlk3b1ltQ3N5aldUNFpxRmlWL3pZa1V1eHAvYkRFcGNHeWNQUmhDNCtudUx6Vjl5QThEOUNoK00ySjloUXlObzNKZi9oWDFqZWpIeVQ4dE9CR1cveW9jcUtKWE42T0NhQXUyVmhaR1RCRTZtRmd4WVNwL1hVc3BGRDF1NjZ1dGZQQ1V0YjZjN0hGdXloV3ZlNXJCZ3Z6UVpMY1BFbWxOZFJuakUrbmZKZ21aZ094cVAwZVFqU05GR28wRkZzN1VPV0VhNU1ZeTVmVk9SZmxmWm5WWFVRdUVqU1QrK0ZMQ0cvVjUyZ3oyM3lSaXBwUUdHZmlBK2xvUzVrckRQZExYVThQaTdiejh2RVBadGVIS0JwTktCaUVSaGNneEFUT1RnMWRCWno3cG8zaEp4Sm9RSTlzOUNiSUlXMDV6YU9lZlVwSFZiZENodDdWVmJKNDRDQ2l1Vm1XRXh3aFFYbExTK01DdTNvVzcxcGxzTG9DKy9VbWpXUUtkVTJLeGhzVzEyVHZUS2NQT09jajFQYWkwaHdMZkxteThnOUxqNjU4d0NZdy9XSWRMK21IYTQiLCJtYWMiOiJlZDZiOWExZjAyZDZjZTNlZmRmNWE2MDFlZGM3ZjU5YmFhZGM4YmQzZDM0NWY1YjE2ZTU2ODE0MjRiODljNmY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJDQmlrWVVnRHpJcmpvZjVYbjZ2T3c9PSIsInZhbHVlIjoiTER0V3o4MTNvajh1NWVUSmMzRzFtRld0MFBlK0ZTdkFhanlwL1VGTkVicW1jSy9OU2Vpc3JVQzJicGhjY0x3UEFVMmNMSVpxNEE5MkUrQUtvL3hlZUhtRzZtaG5IVEZCaDZSRFJiTGYyZWp0T1BKQldvSHdRdU1yUXFNN0xqazUySnZMZ08xQ1RvbEVsekNveDBYOWlVYi84Z21BMGZlSW1ubXcrbEd4L1pUZCs4TGpsLzRGSXRxemhKSUM0am5pNU5SOU80S0VOZjZBNWFFUDY5eEhIaHVtRmtQL2NpTkV3VGdwdzJmNUJhUk5Qa1VueHdSRytWUUx5OW5PWUQxcCtSUTF2OTBiYTlIOWFGVmJBU0RQQ2VHU3ZVM0hwZHlqWHNUZVZ5NUdpVlBKSHdyMG53b2E2NDd3U1RzbEsxcXdhNysyUWszb1EreUEwd0lLZG5TQ2NqeDY1djVWaGhucWppWWRRUm5oSTIxWndJZlpDb3lVL1JzVGxESXo1OVZEb1Q3eS9yU1IwUy9IT3hTTGdMRlRBcnJXV0RkRG5XU1BXdkRVQ2g3TkNFbzVINlZDU2pONmIvdHlmSE1WOE0rYUtFenJlSGhtYnBGQlRycXNWQ3ZFSGJiU1dKcFY4eDFhR2lGTklnSm1lL21LZGM0bEJpV3NPQ0ZtTUtBdFlRWFoiLCJtYWMiOiI4YTE0ZWJlOGIyODU5OWVlZmMzODY5MWM2OTVjM2ZmOWRjZDgwZTY3ZjM0NjA0MzhiMjZhNTcxYTEyYjg3M2YxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:04:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071838104\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-202409900 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202409900\", {\"maxDepth\":0})</script>\n"}}