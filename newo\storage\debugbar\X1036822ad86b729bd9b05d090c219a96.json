{"__meta": {"id": "X1036822ad86b729bd9b05d090c219a96", "datetime": "2025-06-08 13:26:40", "utime": **********.602434, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389199.198692, "end": **********.602465, "duration": 1.4037728309631348, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749389199.198692, "relative_start": 0, "end": **********.340038, "relative_end": **********.340038, "duration": 1.1413459777832031, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.34006, "relative_start": 1.1413679122924805, "end": **********.602469, "relative_end": 4.0531158447265625e-06, "duration": 0.262408971786499, "duration_str": "262ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48127136, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.030770000000000002, "accumulated_duration_str": "30.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.444644, "duration": 0.023850000000000003, "duration_str": "23.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.511}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.493673, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.511, "width_percent": 4.452}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.548317, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 81.963, "width_percent": 4.257}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.555121, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.22, "width_percent": 3.932}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.569528, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 90.153, "width_percent": 6.305}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.579165, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.458, "width_percent": 3.542}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-356277057 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356277057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.566074, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1767938405 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1767938405\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1163053993 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1163053993\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-710088868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-710088868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749388563683%7C19%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZEL0V5VTVUbG81SWxMZWorblNXK2c9PSIsInZhbHVlIjoiOEk0RkYwZnZIV3VKeUtxc0FSSFNKY1VoVWpxRmZ0WnlGMCtLRktTWmFXVXlKWm9qTE9qa0hkVVNXUWN1MW9RR2Z6b0loWkRuS3VuVVVHNyszVFErZ2ppYTd5TUdiOGpVa0hsUGFaWEFUVkZiYlVyUlpaNEkrTUFaVlJJUXZYSVMyc0FWUzdGSFBPeUNGWnpOUSs5NFEweXU2Q3hzRmZHUUw3MEV0MEVRY1RyMjNXQlZpOVBDY2hGdmdaRlIxaW1wYU1hVnNxRUdldTVMTTRlSGdNMmpLZnJ1TFdqc1hjYlNTM0dRbW5mZS9IZ1FLb3VPTG4vdG5kZGV6V1JCRlhDK0lIbE5aTVQxcWdKQjlRSDVoN1pOQ3BxMHlvWGQxYjUrUzZiQWJhK3BZZFJyN001Mk85WkNLdjF5TTV2UlJ3YSt2NFdTbU1HK3VkUC9sRFRLU1dsQlozUFBjTnBldTRCZVhvbUYyMFV3aS9Pa0UrZUVjZUxKYTd1UERLK2ZBbk96d3RVbm1CNWpnWDI0dTdkT3FMVTk5OUU0MVg5a2lMam1WVWJjQXZBTWZYRnAzUlAzbTVlTFVMMTNoNkk0Y0RmdC9sMzl6QXlXR2RrZ3puRUxUZ0g2SEd4ZFc3QkcydHRBVjBRSUQyQlV1L0wyZTBHY2VLckJ3Tm1BeUF0eHV4R3QiLCJtYWMiOiJjMTg3ZGUzMjQ5ZGE1MmI1MmI5ZjBjODQ3NDI2Mjk4YTgwYWFlNTk3YmRhYjQ4MjVlMWI3NzZlZTEyYThiYjZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Inl6Ty9oZmphd3Bha0xXT0JLQXRhSHc9PSIsInZhbHVlIjoiNENkajEvaHZZcFZpMnJ1Nkt4K1RTaXM4bUI2bE9UUkpONUN5WjN1K1RZS2cvRFJZN2UvTUtwWHdjTW5PcXVKYkFFR2pjREF6cXpsOFhoaEZzY1V6d05xUXRzS1ptbG4vRmczMXRHVWxWSmJGaWN1YXdJZVFkS2w1WWxJRm9paThRanQ2dVBCcDJsbCtWSVkyWEhQTGdKeXNNSUdOSXd4MENCQ1BWemhSV0syOE5Jb3l1d3d6V2c3L2h0eUE4TGEwZzlwaVdPMC9sM3g0WTY5bVp5ZEFTMzNtUForREw3b3QxeWxuUHgwTEpCK05MemMxOWVaeGhlcmM1MmdDZHRNQVJRTmR0SjVOUk5WdUo3SFBOYWM0ZUlXSldzS1c3VkozdzkzNC9YbHlTVk1RM0t0UXl6YnNaTWU1eFAwRUFYN0xyMEszTFZiOVVaeXNGVGpCS0FsdTFBVWV4Q1hDeklLNXVtZVp0bURJYWd3YzJKN1psNG5oM2NZbUJmTEYvMkZmRDBEYTVWMGUvVjJ1SEhSRmUrM2xMNmhTR2NYdGNnaXFrZzFUajlUSVlzUXFKekdlUmkvMVpocjdzcWE1dktmSGhvZ0Qrd2JkdXlVN1NLY3IxakJqMGlWMldGMWx4djFBQm9LU2R6RTY5cGc5K1h2MjAxQXFEWTVWV3g0ZEgrTVYiLCJtYWMiOiIzODU1MDY4ZmFkYzkzNDFkYzRmM2NiZWU5M2YyNmQ1MjFhOGI4ZmE5ODY5M2VmYTA1OTFmMzIzNGQ0ZWI3MjQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1754492641 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:26:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjgwekpvQXpjWndoRFg3RnU1RHowZ1E9PSIsInZhbHVlIjoicjRBd21NTFBrTDRqK2VxVzJXazB4LysyQmNTdnZxRDg5RW5IUnpRbEhUVTRJZkhad3VYQmN3QS9jWitKSmFTZE5OUnIyVFEyNDI3WGRHc09xSGFnc0VNYXZNTmMyUVdjdzcvOHNrYmN4RTllVHFmcTB1N29wYWJVSWxGYTZnTmx4NTcydXVPTHZTWXVjSjFvbHVIUG1UaXdPOTFtRkdyMDkwaVZMS3B4UHQzeS9zYlVJNXNkU2Mzd3FFZ2RqdS96d2h4YmkrbktGTWJKV1lzQ2Y2V3J0VGoyK2NVSGZFM04yUUFYcFYwYmRZaklCTnlLYms3YlNMM2ZieklFUDVJUm9UMUJqTnpDQUtUdHJYZE1VelBDZ0hPb0pkNDVzd3NSMk8vQ1JJVGNZcGx3cWlTQ2FRVDgvMHIrR1hUdTBPbWtNMVQ5U0pSc2VOU3AyTGNsOGlVV245bDBWOWVId2FYcTFqeHNXYmRaVjRIQ2Q5WWNGU0prTVUzeWtUNnRsallRZ2hTc2hmSFhiMjVmenlPbVBmc2RKYlcraitmbm1sazBrWSthWW9yVEVxOXhybmp1ZmtpcGs2M0d2WDNXK2tER1Z3aHVQbmdVZjhpaTB1K1dLalZiMm52aUR0NWtHRTNqUW13L3hCeXNRNDNnY3ZRallCVGE2RW5oczd5ZGx6WWoiLCJtYWMiOiIzNWQ2Y2RkZDNmZWI3ZmZiOWJjMzdkZjkxM2IwYWNiM2I1MzMxZjFkNzU4MjBhZmI0NWJkODAwNDdjZmUyZmZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNqb3BZNVhoMGVJS203cndaVzBMMWc9PSIsInZhbHVlIjoiblZ2MFZJMjEvWEpWOFJmc0pudXhTWjRrQUhuTXNMdkw4bjJ0djd4c09KSDdSZC9pVnJ2dW5ZV1pFbDc3blpIeTFXTTZad3dTbXhlUDBFRTFuMllXWDdYMnc2aE81ZWQzalBQdDdaWm5sbEljMDZGQThxSU5FeTlhOUs2RFZUYXo0cjhFUG5GT1c2NXZBV0FUQ2o0c0QrT0ZGWW1ZNFlqUUlwcnFqN2NlSkZ3M2RZNG15VlFmZnM2bDYyblNFSlRXOXZzSjdlOFd5cDV4UmF3clowc0JoaXI1NkQ2eFZrcTZTeDY3TlFaS3F6N1dyL0ZzRVJHMG0vaDMxbG5VV2lKZzBQcnBTUDUxVnpjVUFXaEo5ZEpqdDRybVNyWlJocllvRFVlelpZYUJSdGxRR05XL1RzKzJTS095Q2NDaGJ4TVlVQmZJWFViZjAyOVFsYmpWNkJ4UnEzR2dnMzh5dEVPWkdVYzA5a2g3UjJKQjR0YnIxQzNhek5GVWJIN0NORmQzaDZpa2xLMGJvaVYvcU1BUFdGV2ZoWTkxVFNGR2czQ2gxb2VnNW1BVENPaWJMc1ZCdzBQbGJTOEdUb3luQUpCTEV1QUpMREhWNnRQMmJOWFRFby8xcG9EWDJ4ZzlkMFZsWjJUM0NUcG5JQ2t5RTVpdFNFa1BuejAyUTRLYTJ6NSsiLCJtYWMiOiIyMzQwODI0OTY0NGZjM2Y5OGQ2ZGVjZmNlNzkwOWZhNDBjODJkYzk5ZmM3OWFjNmExZDkxMjE4ODNkZTFjZTEzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:26:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjgwekpvQXpjWndoRFg3RnU1RHowZ1E9PSIsInZhbHVlIjoicjRBd21NTFBrTDRqK2VxVzJXazB4LysyQmNTdnZxRDg5RW5IUnpRbEhUVTRJZkhad3VYQmN3QS9jWitKSmFTZE5OUnIyVFEyNDI3WGRHc09xSGFnc0VNYXZNTmMyUVdjdzcvOHNrYmN4RTllVHFmcTB1N29wYWJVSWxGYTZnTmx4NTcydXVPTHZTWXVjSjFvbHVIUG1UaXdPOTFtRkdyMDkwaVZMS3B4UHQzeS9zYlVJNXNkU2Mzd3FFZ2RqdS96d2h4YmkrbktGTWJKV1lzQ2Y2V3J0VGoyK2NVSGZFM04yUUFYcFYwYmRZaklCTnlLYms3YlNMM2ZieklFUDVJUm9UMUJqTnpDQUtUdHJYZE1VelBDZ0hPb0pkNDVzd3NSMk8vQ1JJVGNZcGx3cWlTQ2FRVDgvMHIrR1hUdTBPbWtNMVQ5U0pSc2VOU3AyTGNsOGlVV245bDBWOWVId2FYcTFqeHNXYmRaVjRIQ2Q5WWNGU0prTVUzeWtUNnRsallRZ2hTc2hmSFhiMjVmenlPbVBmc2RKYlcraitmbm1sazBrWSthWW9yVEVxOXhybmp1ZmtpcGs2M0d2WDNXK2tER1Z3aHVQbmdVZjhpaTB1K1dLalZiMm52aUR0NWtHRTNqUW13L3hCeXNRNDNnY3ZRallCVGE2RW5oczd5ZGx6WWoiLCJtYWMiOiIzNWQ2Y2RkZDNmZWI3ZmZiOWJjMzdkZjkxM2IwYWNiM2I1MzMxZjFkNzU4MjBhZmI0NWJkODAwNDdjZmUyZmZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNqb3BZNVhoMGVJS203cndaVzBMMWc9PSIsInZhbHVlIjoiblZ2MFZJMjEvWEpWOFJmc0pudXhTWjRrQUhuTXNMdkw4bjJ0djd4c09KSDdSZC9pVnJ2dW5ZV1pFbDc3blpIeTFXTTZad3dTbXhlUDBFRTFuMllXWDdYMnc2aE81ZWQzalBQdDdaWm5sbEljMDZGQThxSU5FeTlhOUs2RFZUYXo0cjhFUG5GT1c2NXZBV0FUQ2o0c0QrT0ZGWW1ZNFlqUUlwcnFqN2NlSkZ3M2RZNG15VlFmZnM2bDYyblNFSlRXOXZzSjdlOFd5cDV4UmF3clowc0JoaXI1NkQ2eFZrcTZTeDY3TlFaS3F6N1dyL0ZzRVJHMG0vaDMxbG5VV2lKZzBQcnBTUDUxVnpjVUFXaEo5ZEpqdDRybVNyWlJocllvRFVlelpZYUJSdGxRR05XL1RzKzJTS095Q2NDaGJ4TVlVQmZJWFViZjAyOVFsYmpWNkJ4UnEzR2dnMzh5dEVPWkdVYzA5a2g3UjJKQjR0YnIxQzNhek5GVWJIN0NORmQzaDZpa2xLMGJvaVYvcU1BUFdGV2ZoWTkxVFNGR2czQ2gxb2VnNW1BVENPaWJMc1ZCdzBQbGJTOEdUb3luQUpCTEV1QUpMREhWNnRQMmJOWFRFby8xcG9EWDJ4ZzlkMFZsWjJUM0NUcG5JQ2t5RTVpdFNFa1BuejAyUTRLYTJ6NSsiLCJtYWMiOiIyMzQwODI0OTY0NGZjM2Y5OGQ2ZGVjZmNlNzkwOWZhNDBjODJkYzk5ZmM3OWFjNmExZDkxMjE4ODNkZTFjZTEzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:26:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754492641\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-183428400 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183428400\", {\"maxDepth\":0})</script>\n"}}