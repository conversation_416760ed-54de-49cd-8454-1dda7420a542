{"__meta": {"id": "X4ba62ed5c3f04a41b739ec88b5ae8ca8", "datetime": "2025-06-08 14:14:53", "utime": 1749392093.013845, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749392091.515922, "end": 1749392093.013874, "duration": 1.4979519844055176, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1749392091.515922, "relative_start": 0, "end": **********.845224, "relative_end": **********.845224, "duration": 1.3293018341064453, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.845244, "relative_start": 1.3293218612670898, "end": 1749392093.013878, "relative_end": 4.0531158447265625e-06, "duration": 0.16863417625427246, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45278136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.007200000000000001, "accumulated_duration_str": "7.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9535449, "duration": 0.006030000000000001, "duration_str": "6.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.75}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.989396, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.75, "width_percent": 16.25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-908609349 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-908609349\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1772723212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1772723212\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-129307610 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129307610\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1787275197 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749391833855%7C52%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAwVVBVbmIwN2dzT1BjQ0JRTEtkL2c9PSIsInZhbHVlIjoiczFwc0lyc09Sb3lTNWVEUlh4TlcwNy8zMlN1alV0ZVhSR0JoMFN3MHlWbGVtMTk3bElCV3BOaUIxeTVIaWxqVTU4c3ZqbTBid2YzdDlmdGtNUnBROS9pbDJrUEVmeHNVSGlMbm5hWmJFUi82TENCWHEvdzNRa0cxa0xLUGkyOG8vd0dZU2l4Snd1enp0MWtSUlpyQmFnY1YvWHVENTVpTjBLQkxQWk9GRWlJZWRkc0lFL1orZmNzb2VPRUFDOWRaY0pJcG1qKzFOeTllY3I5eFp0aHJSckpXaTRPb2pCRXgzVkErTGN6Y1BPaCswOWpYZm1VOW51M3ZGZ2VYNitLU2tNa29nRFlnK2JCRFRzUTMvaVpsWHphTXBlSjFzTXQzNWFnREZBMFJiUWlWbGFQR3VZeW5RejR6emRXNDh0aDhKNEhqV29KYW5lbVdkV2VuK29iY01zTSsrZDhDVzhWRzlGWG9rUmhpN25VeS80UHpRSStMbHdJUWNFTzJKYVh3N3MwL0c4OGttTFluOXBVUno3b2F3QzZwTmtTdWxpb3cweE9paEZQV2RHN21qemtOYVVuUkRtQXNBR2h5QUNzWHFnWi9OdVB2bSswSGpnK0NtM1I2c2JhWUxiR2FSa0RpTjRQcURtaVJIdkg2OHlzS2YvYnVHTEFzWUpqMTdiKzIiLCJtYWMiOiJmMTU1OTAyMWM4N2IyOTFlZWI0ZjJjYjc0ODhkZDUwMDgwNmNmOGZmNGIxOTE3OWQ1YjI1NjE1NDYxMWE5OTc3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRaWVdLRXhRd3JVc3RBNm9kM2VXQXc9PSIsInZhbHVlIjoiMDc0OTZpbFhsRmduZms5LzI1ZXlCWTdPbGVTb2QrSWdKUmowM2E0eUs1MWcxOEdtK1ZyVmV2Q3VNcUkyc052NnFjNnlxSUROZEVtQzdRTGJLaEFRWkFpREdyM3prNDRpT1RGZmh1a2hSbXZ5aUlDSjZXQmVyMlQyY21sL0kxblpRSVZ6YklGemhpTGVad2FCWXk0OEE1citidTVLcXFuQXpnMmgvVHM1T2dHZXBOazI1bU1NQ1ZJNmV5d2kyRk1xZ0dSNGdmdjdZaXIzUmlTc0VuZkFscFN6YkNSbEFOZ3lBeGl5MlErTW9iRnQ2UHJncTFvbmUwWGJxb3dNNzBJckNUZmtoakJMVGp4dEZCbDVvMXFKVHp6Nm9pK2hVL0lTODE0MWJuQWV2SkdhNHJXYjl1bnd1QjU1K0h1VTVnRkNuTCswNVFXYlRNOVZxZ0NMTk5zSXNpMlBsVWdCVXhuYklGZ2srTVkwWENRczNBRG1sQ1lVbnRWNSsvdHh2eTFiZjgvamJ6aWpGUnFQTjc3M2E3UzZSMkNzL1ZXclZWcXdNVHNMcmFLVHJPYkYzOHFreDJqN2sydDUyZS9GRW5ZQkx0M1NKZmVzZWpLUk9VenRGSGFNOWFBa0NlVDVhVDI1UG9HMjNBSlBoRjBjQjlqZHNjaHZjajVkaCtsOW5FQ0wiLCJtYWMiOiJmZjRlZjdlYTkwMDgwYjFkZGY2MWRmMzZhODIxZjk2MWY0ZGJiOTRlYWIzOTBiZjBmMTNiZGY4MTBmOWJlZjhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787275197\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1587314456 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587314456\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1268503175 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:14:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFjNUs2STRyQkt1ekhtd1lBcVRHN3c9PSIsInZhbHVlIjoiM3JXNjg3RGdCcjJ3blBXZUNqUTZqNVhwN3IxbUpvUmtHR1g4UlN0SW5aV0xVd05lRGY3bkhtVWRNN1RpbmlmaWRuRkNTclFsUlJaQ3BuWlhsOVE5TVI3aEVQOTh0d3M0b3FYR1d1cnFqWUVvRmtsbkcwNUV5ZWxYMzN0cHNJKzA4QzVDVjJuTW9iNlNKdjIwYlZNYzlrdGNINmhwUlpPY1NhMlBGWmpUbm9kSjV3b05GK28xSlBhNktHYmszaXgycGJSaDZrcGxMdVoxTmpNeHh6SUwwWWFKNGUvd1k5QjMzUzA5QldJd3dLQzYvVlN4OENuQTFOUFFXaTlvRmRkeVdPTnRPdFZLMHVCL3RuM2RxYlZ3MXNLNXVPVms3U20zQWIrREFRdFZGSGdGY3NiWk1kUE5DanFUbytMTDZ5WlBKMkxpem9ZN204YzNHQktieHNzU2FQK2ZkcFlrOWFEZVBWZnVuS2F1U0ZRQlgrNHpiRlYxaUNqcFpseWdwYmFXb3RlY29KSWRWZ1FyQmU2ZU5Jd21SYkVIRC82Vk41ZGxoNWV0UDhOK000VUVHWW1BWUpITEluY0RYVDZJbFpJWWkwc2xLTkJpUldMa1NNdTFsdkR6WVBaMk5tY1krT3d3SERJNGIxcTI4TDA4UkUrN0VPL1BrZ0JsVGh6MjlkV3QiLCJtYWMiOiI1YjMyMWYwZTI5NGQ5Y2QyMzU3ZjVmMWNmZDFhOTcxM2JhZjE1YTRmYzI2MDlhNjhkZTNjNjlhMDI4NGY0ZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNYWTluWUthSE1BaHpmQ3ZnbjhGWEE9PSIsInZhbHVlIjoiVGdVMnBLZFowWXBkRU9pdWg4TmR5MldoMXlIZjlPV2U1Z2JtTlM3NUpqaHhLcHRpdEh1ZTNTbDZyaG1yams5SDcweCs2NUducGFDa2kxU3BVd0VFYlRua29kVDJmUzlSTDFqdUtkZEYwbDFNb0REMFJGR1RpemRMenBDRXBtdjFuL0VtY0M4T0R3U1crRXN3dnNNOWVxS2Y4VHdqYWs4amN1a3lMMkFwYzRGczRNR3UxSlpVdjJmK3lWSVdjd2JwV0xCbWdGUnl3Ymx0OEdCdTcrOVhURFdxVXhkbzFFWFVCVnFWcDZLR1lwMEh0Y1Z0NUFYMGJqNHpDRkxQay9aY3BXQkdEdXgrT0sxL0w5aXZlY3lvU2N1aEJXRkgxTEQyK3pIMUd1ekprUGlXZkNLejE4MFRubUxwaFlNbzNBeE9VUW03VVhUL0ZBaUlMOFJFdEFMNnBoMVFubXdFaUsxajVvNEp4bmwwbVpnc01xVEMyTVR0SC9IaUhBYjZwdUxZV25qUDJycXJYMDhWSXZkWURuTit5WGs1a3d3Y0x5b1UwNHA1MjRTVGR0ZzgyblVJenErcVU0ay9UWXR2elI2Y0N6VGRwSUNZVGlHZHgvMnFNYWdQSFByWFJuQWZzYmVoR1BKQlVZUEFuNGR0NnIzTUF6TTdDR3R4bmVnWDBKUFMiLCJtYWMiOiJkYzI3ZTdkNDQxYTQyYWQ5ZTViYzA1NDAwM2Y1ZDVlNDI4NGM3NTMyYjcwZWY1YTEyNjVmYzJkZWVkNzQ4MjRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:14:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFjNUs2STRyQkt1ekhtd1lBcVRHN3c9PSIsInZhbHVlIjoiM3JXNjg3RGdCcjJ3blBXZUNqUTZqNVhwN3IxbUpvUmtHR1g4UlN0SW5aV0xVd05lRGY3bkhtVWRNN1RpbmlmaWRuRkNTclFsUlJaQ3BuWlhsOVE5TVI3aEVQOTh0d3M0b3FYR1d1cnFqWUVvRmtsbkcwNUV5ZWxYMzN0cHNJKzA4QzVDVjJuTW9iNlNKdjIwYlZNYzlrdGNINmhwUlpPY1NhMlBGWmpUbm9kSjV3b05GK28xSlBhNktHYmszaXgycGJSaDZrcGxMdVoxTmpNeHh6SUwwWWFKNGUvd1k5QjMzUzA5QldJd3dLQzYvVlN4OENuQTFOUFFXaTlvRmRkeVdPTnRPdFZLMHVCL3RuM2RxYlZ3MXNLNXVPVms3U20zQWIrREFRdFZGSGdGY3NiWk1kUE5DanFUbytMTDZ5WlBKMkxpem9ZN204YzNHQktieHNzU2FQK2ZkcFlrOWFEZVBWZnVuS2F1U0ZRQlgrNHpiRlYxaUNqcFpseWdwYmFXb3RlY29KSWRWZ1FyQmU2ZU5Jd21SYkVIRC82Vk41ZGxoNWV0UDhOK000VUVHWW1BWUpITEluY0RYVDZJbFpJWWkwc2xLTkJpUldMa1NNdTFsdkR6WVBaMk5tY1krT3d3SERJNGIxcTI4TDA4UkUrN0VPL1BrZ0JsVGh6MjlkV3QiLCJtYWMiOiI1YjMyMWYwZTI5NGQ5Y2QyMzU3ZjVmMWNmZDFhOTcxM2JhZjE1YTRmYzI2MDlhNjhkZTNjNjlhMDI4NGY0ZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNYWTluWUthSE1BaHpmQ3ZnbjhGWEE9PSIsInZhbHVlIjoiVGdVMnBLZFowWXBkRU9pdWg4TmR5MldoMXlIZjlPV2U1Z2JtTlM3NUpqaHhLcHRpdEh1ZTNTbDZyaG1yams5SDcweCs2NUducGFDa2kxU3BVd0VFYlRua29kVDJmUzlSTDFqdUtkZEYwbDFNb0REMFJGR1RpemRMenBDRXBtdjFuL0VtY0M4T0R3U1crRXN3dnNNOWVxS2Y4VHdqYWs4amN1a3lMMkFwYzRGczRNR3UxSlpVdjJmK3lWSVdjd2JwV0xCbWdGUnl3Ymx0OEdCdTcrOVhURFdxVXhkbzFFWFVCVnFWcDZLR1lwMEh0Y1Z0NUFYMGJqNHpDRkxQay9aY3BXQkdEdXgrT0sxL0w5aXZlY3lvU2N1aEJXRkgxTEQyK3pIMUd1ekprUGlXZkNLejE4MFRubUxwaFlNbzNBeE9VUW03VVhUL0ZBaUlMOFJFdEFMNnBoMVFubXdFaUsxajVvNEp4bmwwbVpnc01xVEMyTVR0SC9IaUhBYjZwdUxZV25qUDJycXJYMDhWSXZkWURuTit5WGs1a3d3Y0x5b1UwNHA1MjRTVGR0ZzgyblVJenErcVU0ay9UWXR2elI2Y0N6VGRwSUNZVGlHZHgvMnFNYWdQSFByWFJuQWZzYmVoR1BKQlVZUEFuNGR0NnIzTUF6TTdDR3R4bmVnWDBKUFMiLCJtYWMiOiJkYzI3ZTdkNDQxYTQyYWQ5ZTViYzA1NDAwM2Y1ZDVlNDI4NGM3NTMyYjcwZWY1YTEyNjVmYzJkZWVkNzQ4MjRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:14:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268503175\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2086090630 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086090630\", {\"maxDepth\":0})</script>\n"}}