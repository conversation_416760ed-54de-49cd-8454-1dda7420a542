{"__meta": {"id": "X5108168b129da1675a3d8fda87387cd1", "datetime": "2025-06-08 13:02:12", "utime": **********.724907, "method": "POST", "uri": "/receipt-order", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387730.953186, "end": **********.724948, "duration": 1.7717618942260742, "duration_str": "1.77s", "measures": [{"label": "Booting", "start": 1749387730.953186, "relative_start": 0, "end": **********.202219, "relative_end": **********.202219, "duration": 1.249032974243164, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.202242, "relative_start": 1.2490558624267578, "end": **********.724952, "relative_end": 4.0531158447265625e-06, "duration": 0.5227100849151611, "duration_str": "523ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52042336, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST receipt-order", "middleware": "web, verified, auth, XSS, revalidate", "as": "receipt-order.store", "controller": "App\\Http\\Controllers\\ReceiptOrderController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=257\" onclick=\"\">app/Http/Controllers/ReceiptOrderController.php:257-315</a>"}, "queries": {"nb_statements": 20, "nb_failed_statements": 0, "accumulated_duration": 0.06499000000000002, "accumulated_duration_str": "64.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.322026, "duration": 0.019170000000000003, "duration_str": "19.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 29.497}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.373138, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 29.497, "width_percent": 2.17}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.429615, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 31.666, "width_percent": 2.354}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.436973, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 34.021, "width_percent": 2.093}, {"sql": "select count(*) as aggregate from `warehouses` where `id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.481107, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 36.113, "width_percent": 2.908}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '3'", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.498595, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 39.021, "width_percent": 2.262}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.50613, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 41.283, "width_percent": 1.677}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 292}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.530571, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:292", "source": "app/Http/Controllers/ReceiptOrderController.php:292", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=292", "ajax": false, "filename": "ReceiptOrderController.php", "line": "292"}, "connection": "ty", "start_percent": 42.96, "width_percent": 0}, {"sql": "select * from `receipt_orders` order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ReceiptOrder.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ReceiptOrder.php", "line": 77}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 324}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.532524, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrder.php:77", "source": "app/Models/ReceiptOrder.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FReceiptOrder.php&line=77", "ajax": false, "filename": "ReceiptOrder.php", "line": "77"}, "connection": "ty", "start_percent": 42.96, "width_percent": 2.877}, {"sql": "insert into `receipt_orders` (`order_number`, `order_type`, `vendor_id`, `warehouse_id`, `invoice_number`, `invoice_total`, `invoice_date`, `has_return`, `created_by`, `updated_at`, `created_at`) values ('RO-2025-000001', 'استلام بضاعة', '4', '8', '8475', '900', '2025-06-08 00:00:00', '1', 16, '2025-06-08 13:02:12', '2025-06-08 13:02:12')", "type": "query", "params": [], "bindings": ["RO-2025-000001", "استلام بضاعة", "4", "8", "8475", "900", "2025-06-08 00:00:00", "1", "16", "2025-06-08 13:02:12", "2025-06-08 13:02:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 333}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.541401, "duration": 0.00656, "duration_str": "6.56ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:333", "source": "app/Http/Controllers/ReceiptOrderController.php:333", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=333", "ajax": false, "filename": "ReceiptOrderController.php", "line": "333"}, "connection": "ty", "start_percent": 45.838, "width_percent": 10.094}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `total_cost`, `updated_at`, `created_at`) values (1, '3', '10', '1', '2025-06-18 00:00:00', 0, 10, '2025-06-08 13:02:12', '2025-06-08 13:02:12')", "type": "query", "params": [], "bindings": ["1", "3", "10", "1", "2025-06-18 00:00:00", "0", "10", "2025-06-08 13:02:12", "2025-06-08 13:02:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 347}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5590122, "duration": 0.00644, "duration_str": "6.44ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:347", "source": "app/Http/Controllers/ReceiptOrderController.php:347", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=347", "ajax": false, "filename": "ReceiptOrderController.php", "line": "347"}, "connection": "ty", "start_percent": 55.932, "width_percent": 9.909}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '3' limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 354}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.572496, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 65.841, "width_percent": 2.277}, {"sql": "update `warehouse_products` set `quantity` = 14, `warehouse_products`.`updated_at` = '2025-06-08 13:02:12' where `id` = 2", "type": "query", "params": [], "bindings": ["14", "2025-06-08 13:02:12", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 354}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.581473, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 68.118, "width_percent": 2.046}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '3' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["3", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.591706, "duration": 0.00922, "duration_str": "9.22ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 70.165, "width_percent": 14.187}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.614177, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 84.351, "width_percent": 0}, {"sql": "insert into `product_expiry_dates` (`product_id`, `warehouse_id`, `expiry_date`, `created_by`, `updated_at`, `created_at`) values ('3', '8', '2025-06-18', 16, '2025-06-08 13:02:12', '2025-06-08 13:02:12')", "type": "query", "params": [], "bindings": ["3", "8", "2025-06-18", "16", "2025-06-08 13:02:12", "2025-06-08 13:02:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.614922, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 84.351, "width_percent": 2.862}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.626742, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 87.213, "width_percent": 0}, {"sql": "insert into `receipt_order_products` (`receipt_order_id`, `product_id`, `quantity`, `unit_cost`, `expiry_date`, `is_return`, `total_cost`, `updated_at`, `created_at`) values (1, '5', '20', '8', '2025-06-24 00:00:00', 0, 160, '2025-06-08 13:02:12', '2025-06-08 13:02:12')", "type": "query", "params": [], "bindings": ["1", "5", "20", "8", "2025-06-24 00:00:00", "0", "160", "2025-06-08 13:02:12", "2025-06-08 13:02:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 347}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.62843, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:347", "source": "app/Http/Controllers/ReceiptOrderController.php:347", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=347", "ajax": false, "filename": "ReceiptOrderController.php", "line": "347"}, "connection": "ty", "start_percent": 87.213, "width_percent": 2.97}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '5' limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 478}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 354}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.637773, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:478", "source": "app/Http/Controllers/ReceiptOrderController.php:478", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=478", "ajax": false, "filename": "ReceiptOrderController.php", "line": "478"}, "connection": "ty", "start_percent": 90.183, "width_percent": 1.539}, {"sql": "update `warehouse_products` set `quantity` = 27, `warehouse_products`.`updated_at` = '2025-06-08 13:02:12' where `id` = 4", "type": "query", "params": [], "bindings": ["27", "2025-06-08 13:02:12", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 494}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 354}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.645405, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:494", "source": "app/Http/Controllers/ReceiptOrderController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=494", "ajax": false, "filename": "ReceiptOrderController.php", "line": "494"}, "connection": "ty", "start_percent": 91.722, "width_percent": 2.385}, {"sql": "select * from `product_expiry_dates` where (`product_id` = '5' and `warehouse_id` = '8') limit 1", "type": "query", "params": [], "bindings": ["5", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.654598, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 94.107, "width_percent": 1.631}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.667415, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 95.738, "width_percent": 0}, {"sql": "insert into `product_expiry_dates` (`product_id`, `warehouse_id`, `expiry_date`, `created_by`, `updated_at`, `created_at`) values ('5', '8', '2025-06-24', 16, '2025-06-08 13:02:12', '2025-06-08 13:02:12')", "type": "query", "params": [], "bindings": ["5", "8", "2025-06-24", "16", "2025-06-08 13:02:12", "2025-06-08 13:02:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6680899, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 95.738, "width_percent": 1.908}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 372}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.68113, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:372", "source": "app/Http/Controllers/ReceiptOrderController.php:372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=372", "ajax": false, "filename": "ReceiptOrderController.php", "line": "372"}, "connection": "ty", "start_percent": 97.646, "width_percent": 0}, {"sql": "update `receipt_orders` set `total_products` = 2, `total_amount` = 170, `receipt_orders`.`updated_at` = '2025-06-08 13:02:12' where `id` = 1", "type": "query", "params": [], "bindings": ["2", "170", "2025-06-08 13:02:12", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 388}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 295}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6822321, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:388", "source": "app/Http/Controllers/ReceiptOrderController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=388", "ajax": false, "filename": "ReceiptOrderController.php", "line": "388"}, "connection": "ty", "start_percent": 97.646, "width_percent": 2.354}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/ReceiptOrderController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ReceiptOrderController.php", "line": 302}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.703385, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ReceiptOrderController.php:302", "source": "app/Http/Controllers/ReceiptOrderController.php:302", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FReceiptOrderController.php&line=302", "ajax": false, "filename": "ReceiptOrderController.php", "line": "302"}, "connection": "ty", "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage warehouse, result => null, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-845102399 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845102399\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.453986, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-order/create\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "success": "تم إنشاء الأمر بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/receipt-order", "status_code": "<pre class=sf-dump id=sf-dump-44801248 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-44801248\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1401271136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1401271136\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1773784481 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>order_type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1575;&#1587;&#1578;&#1604;&#1575;&#1605; &#1576;&#1590;&#1575;&#1593;&#1577;</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>vendor_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>invoice_number</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8475</span>\"\n  \"<span class=sf-dump-key>invoice_total</span>\" => \"<span class=sf-dump-str title=\"3 characters\">900</span>\"\n  \"<span class=sf-dump-key>invoice_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-08</span>\"\n  \"<span class=sf-dump-key>has_return</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>from_warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>exit_reason</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>exit_notes</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>exit_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-08</span>\"\n  \"<span class=sf-dump-key>responsible_person</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-18</span>\"\n    </samp>]\n    <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n      \"<span class=sf-dump-key>unit_cost</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>expiry_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-24</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773784481\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-91621509 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">601</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387646954%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkloZHRDbE5WM2h4S0hpWCt5WW54Rnc9PSIsInZhbHVlIjoiWG5jdnM3NEpsOG9CYXhwRUhPTTV5MGtZeWEwRHpEaGpvTmZjYUJidFlRM2REaVRlTFltY1VMcVZ0b21zTnBHQ2o2d2RCNUN1ODdncitQSkN5NTJhQ3MrcnpIQ3JqRnYyODZPU0NwUlBvcWxnTmg1eHBabVVmcDhLWWkvZGJicURtdThOUzVwdTQvSTU5Um5rWmxxVzhBRG1aVk5MNUQ5UkpTQzJyOFp0M0dCRHNjUm5veURFOVhjalZsUEFMamtwalRGQzhRR05iVkExSTU5VjA5bzlFbC8rNThXUkRiV0hvZk9BYkE4VmdHdzM0WW0yZGpKWmVMbHF4M3RPWjJ1Y0Q4K0UzNFp1R3Z5T3IyVkU0UlBHVUI5ZVM3QjNzRUtTbnFZNlJCcWlkdGZQcjNKZDdpZWRJZk9sRitISldsd1BkNmxPU2JSazNWdkxDTGVXRzJMOXo1Unk3Q0paaG9Ud0gzWTJnRFhZeS9heW1TVnF5OE9paTlkUlZVaTBrNEJUUDU5MG5NNzg3OTl0QXZRVHdSbFVoVHBXNDR3ampJbnFFaHFsbnFCSzJRSmlObUdzaXdrbnVkYXhpV2pmZkpLWEZ5NzRuUmZLVzc1U0l4KzhCRkRXUWpscTVpNEttMk5kV05WSVhkR3V6NG11ZHhhWkZ5ZVYzRUpwc2ZLaHpCSDYiLCJtYWMiOiJiMWRhMzQzMmI4NzZlNTU2MjMwNzA0OWE4YjI2NDVlZmE4N2ZhMTI2NDFmN2I3ZmFlZWRjYWI1NzcxMjI0ZWJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhjY3JkMW5QYVlBY05TeHZqQlBVTkE9PSIsInZhbHVlIjoib1MzR25zOUljMDBnOHBZbG1YWVNucTBoMG1IM0hVdmI0UE1EN09hNFJxUkQrVnppMmFFOXpRSmlzREpDaVYxUlBBbThkRkpoZjJHRytsU1BPTDA2bk9wa1UyKzVBRSsyVjhWYXBjbElvM3Zlc1pUSEVVTEJUcWNqSzFMWlpVTGp6Z0JURndEMWdjSmJ6aCtqdWE4SFEya0ZYNlZZdVFRNzZxZldrRGtybVlGM2RrZVQvRmkvQmpUMVUxNDRIMXBkTjJ4VmxXb0hFeGhNS3JCRldNTmJ2bWxqTGNLcjZFU3RmejJCVHFmT21ydXNBcTJJOENaZCtUK1d3cGRKeFphU085R1RUcytzUlcwSStTTEtUSmkwZUNaWXBDT2xvR01MbUpVQUpWY0VxOEU4NHgxMVpPY3JNUXF4SUdXWVJFZTNxWGM5ZGs1NFZQcEVPcFJ2ejN2NDJwaER2azhCMnlnRkhoK2VnSHJockhyN2hlRkU5TUZFc1VtamJyZmdwSGI1R09icFhKUWlFYnV2cE5yZFMrUFNIckhLN1ZiOHhpV0FKaVVGSUk4RTY4MjBRRmxpNXBMbnJKa3gzeVdpQmlSRXIvRFptUmtndDAzSWJhK0VTNE5CRkk2Y3l4aU05MkdpRUdjdHlpRlZXM3dwU2RpSzJNb2w4VCtOYk9OTGkxNisiLCJtYWMiOiIzNjU2OTQyOWRkM2YzOWY0MDliZWY1ZjU4OTliNDVkNDc4NjBmZjYzZDZkOWJkNmM1OGE2NzM2Nzc2MjVjYTkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-91621509\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1731212611 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731212611\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-684778318 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:02:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/receipt-order</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVsUkkwSVVsdlE0ODZCUGRqTlhmWHc9PSIsInZhbHVlIjoiQjVSUmFLRnpEc1JaTC82RjJZNXFWWWc0aXZGMVQ5WWZydGU4UXN6dnIxT1NsZzFJa0ZNZE9xZk9OYll5RGd5dGRpb1EzWURyZHJKTXlDTGEwcDNsdVRwc0Z2SlFMRlZFM3JSaG5hRHFscmU0VmtSQVdjNFMvMnFLSW5uclFVbEJKMjU5Q1B0WjlvTE9XSTVYM0lPWlk4K3QvOVpkUkVFWEVkcG13bFFySTBMN3hMYjBWZnJFWWd5ZlBmZGFOamFyN2Z5NVpEeHhqQ3FEMkl4UVp5Z2N2RlVJWWJTMnJKbkM5TEdGZzVPTHlQaE5UNjJUUkJFU09Vd1ErbWkyNUhQOXFYWmJPaDNFWHB6ZVJVQXB4NFZla3dObktQVUNNeXdsdUJmVUFkOUwzZGVHQVFIZ2Y3N3ZueHhDNm5IUWlubDRlZ083QzNQenI0cmF0THQrTTc2RWxNcksrZGF0RXlNOTBEVWRFS09ib0MwN1pMVEdFUGtmSVl5ZCtFZDdCd3lkaTZjakovZUdQTW5ST2xCUXFtNzhQSFVtSWEwWG1USmFMaXFXQ1N3dWxBMmVGWHMyOHlNU3c5ZXJPRk1kZWlVY3MzVGZuWkRhcGI3bCtaRS9jbGczQjFndkpCeXdRNkJnaTdLemluRXl6eFNnKzJzMERSd2t5RlhRNDRGTThDMFYiLCJtYWMiOiI3Y2I5M2NiNjQyODNlZWVhMDQ2ZDhmNWE0NTA2MDJkMDZmMmY5NTIyOTQ2YTY0OTRkY2M0ZTIzZGNjZGViMDFjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFDd050Y3NLeUJERUg5NlEwU0tMcEE9PSIsInZhbHVlIjoidlp6QXk5SmhBY09xc1FnVmZBLzA5eVFyTFZYNGNGQnJLSnZzMXVPRVF6QjlqMTZhcnF3OFc2K3cxbGc2Q1B3aUxVRU01MEg2dC8xbHFrTlJuZDB3NGJBMzhVY1g0M3BoVk5TZVhiOUFCNUxTVUJXT3lwSTJ0akVlVitJcno2QzEyd0VGMHZvaEV1UWdyOEVKWkluT2oyZWJaN3V6ZDdrMkpBaTBLUXpXcFR5ZHMwbCtBNkd5WldhcVRad041eDNGK1JCMXg4OEdSU2FpaVZ0UGgvRktHdG52R09LWjdndzY3MFE3cUo4ajUraWlDM1ZHYXRUSmtwWXZGNXJQa1pwZCsrazlFQ0cyWUYwMUpqekRneWlUaUxNTTIycHE1OGhpY0hGZjlSY2hYQjZiQWdCZDNFQURCZWNZZGtFeVFOY2orMnJ3cUptMWJmOWZ5aDY2S0xoY08zOVJkSXkraWZpTFU0cmFUYm9zU1Z3eGwwTlo1djVDQUVaOVczNGdWQ040eXdjK0dEaDlYVVQxQ2pNTWxWOU9TbURlemNyMlRuWnZiMm4yRFN5WXZBWHQxYTg2ZjR4ZmVrRmVDRVo5TmEzZlg2ODVoZFZscG9NSmUrTlNBZEllRS9GUSt4NmRFVi9wLzdzRUs0Z0o4T1cxSjdqdnQ3Uk9aKzErb3dNRjNta3ciLCJtYWMiOiIyODUyMTNiNDYwNjM2N2M1MTEyYzNmYmExZDhjMmRkMDM1NzMxMzI1NjIzOTM1NTUzMTMzNGQ2ZDZkY2I2YzJmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:02:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVsUkkwSVVsdlE0ODZCUGRqTlhmWHc9PSIsInZhbHVlIjoiQjVSUmFLRnpEc1JaTC82RjJZNXFWWWc0aXZGMVQ5WWZydGU4UXN6dnIxT1NsZzFJa0ZNZE9xZk9OYll5RGd5dGRpb1EzWURyZHJKTXlDTGEwcDNsdVRwc0Z2SlFMRlZFM3JSaG5hRHFscmU0VmtSQVdjNFMvMnFLSW5uclFVbEJKMjU5Q1B0WjlvTE9XSTVYM0lPWlk4K3QvOVpkUkVFWEVkcG13bFFySTBMN3hMYjBWZnJFWWd5ZlBmZGFOamFyN2Z5NVpEeHhqQ3FEMkl4UVp5Z2N2RlVJWWJTMnJKbkM5TEdGZzVPTHlQaE5UNjJUUkJFU09Vd1ErbWkyNUhQOXFYWmJPaDNFWHB6ZVJVQXB4NFZla3dObktQVUNNeXdsdUJmVUFkOUwzZGVHQVFIZ2Y3N3ZueHhDNm5IUWlubDRlZ083QzNQenI0cmF0THQrTTc2RWxNcksrZGF0RXlNOTBEVWRFS09ib0MwN1pMVEdFUGtmSVl5ZCtFZDdCd3lkaTZjakovZUdQTW5ST2xCUXFtNzhQSFVtSWEwWG1USmFMaXFXQ1N3dWxBMmVGWHMyOHlNU3c5ZXJPRk1kZWlVY3MzVGZuWkRhcGI3bCtaRS9jbGczQjFndkpCeXdRNkJnaTdLemluRXl6eFNnKzJzMERSd2t5RlhRNDRGTThDMFYiLCJtYWMiOiI3Y2I5M2NiNjQyODNlZWVhMDQ2ZDhmNWE0NTA2MDJkMDZmMmY5NTIyOTQ2YTY0OTRkY2M0ZTIzZGNjZGViMDFjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFDd050Y3NLeUJERUg5NlEwU0tMcEE9PSIsInZhbHVlIjoidlp6QXk5SmhBY09xc1FnVmZBLzA5eVFyTFZYNGNGQnJLSnZzMXVPRVF6QjlqMTZhcnF3OFc2K3cxbGc2Q1B3aUxVRU01MEg2dC8xbHFrTlJuZDB3NGJBMzhVY1g0M3BoVk5TZVhiOUFCNUxTVUJXT3lwSTJ0akVlVitJcno2QzEyd0VGMHZvaEV1UWdyOEVKWkluT2oyZWJaN3V6ZDdrMkpBaTBLUXpXcFR5ZHMwbCtBNkd5WldhcVRad041eDNGK1JCMXg4OEdSU2FpaVZ0UGgvRktHdG52R09LWjdndzY3MFE3cUo4ajUraWlDM1ZHYXRUSmtwWXZGNXJQa1pwZCsrazlFQ0cyWUYwMUpqekRneWlUaUxNTTIycHE1OGhpY0hGZjlSY2hYQjZiQWdCZDNFQURCZWNZZGtFeVFOY2orMnJ3cUptMWJmOWZ5aDY2S0xoY08zOVJkSXkraWZpTFU0cmFUYm9zU1Z3eGwwTlo1djVDQUVaOVczNGdWQ040eXdjK0dEaDlYVVQxQ2pNTWxWOU9TbURlemNyMlRuWnZiMm4yRFN5WXZBWHQxYTg2ZjR4ZmVrRmVDRVo5TmEzZlg2ODVoZFZscG9NSmUrTlNBZEllRS9GUSt4NmRFVi9wLzdzRUs0Z0o4T1cxSjdqdnQ3Uk9aKzErb3dNRjNta3ciLCJtYWMiOiIyODUyMTNiNDYwNjM2N2M1MTEyYzNmYmExZDhjMmRkMDM1NzMxMzI1NjIzOTM1NTUzMTMzNGQ2ZDZkY2I2YzJmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:02:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684778318\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-868015287 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/receipt-order/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1578;&#1605; &#1573;&#1606;&#1588;&#1575;&#1569; &#1575;&#1604;&#1571;&#1605;&#1585; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868015287\", {\"maxDepth\":0})</script>\n"}}