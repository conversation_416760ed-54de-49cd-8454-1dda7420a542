{"__meta": {"id": "Xa441a0ef38f35bf1cc6c31668c454e29", "datetime": "2025-06-08 14:52:19", "utime": **********.937178, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.396752, "end": **********.937199, "duration": 0.5404469966888428, "duration_str": "540ms", "measures": [{"label": "Booting", "start": **********.396752, "relative_start": 0, "end": **********.86371, "relative_end": **********.86371, "duration": 0.46695780754089355, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.863722, "relative_start": 0.46696996688842773, "end": **********.937202, "relative_end": 2.86102294921875e-06, "duration": 0.07347989082336426, "duration_str": "73.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45185160, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.011340000000000001, "accumulated_duration_str": "11.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9011722, "duration": 0.010150000000000001, "duration_str": "10.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.506}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9231231, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.506, "width_percent": 5.644}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.927723, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.15, "width_percent": 4.85}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-810285405 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-810285405\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1386535117 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386535117\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-762072549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-762072549\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1585878684 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749394122823%7C63%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhycThWcG0zVDREWElWblFxK1gyUlE9PSIsInZhbHVlIjoiZ2xZZEMzVVRzTHY3Zm1tVFpWZ2k3QktuSUVQeDJhOWJIUWFHQ0hsUGxBVU9IcExQMi82MmNwcGlQQ1dDVHhrbkJicTN5L1ZvSkRYdzRrbXFZMkt4VFJVTTRDbnVIRGRsUStIcW5ZMmZvQ1dRT2Vlazhvckt3VzNHU1MxdCtTRW50VkN6TFBmWG01WU1ualFseklmcDN3Z3RNQlAvVG1IckMxSkprWW44dENSR05qaDVMTjQrNXNwekxHeVZuSDdUYTZ1NnU2NlV0aDlHaXU0UVlPMUJEZ1RJeWtOdjB0eUs5YTBudStjTlNuVWZHanQ5YlRyanVUd00xQWo4L29Md01hdGorMUlSUTN2bjNnd1ppdXY4VXFDbE5uWjgyWmRmd0hPQlRZbWRpUWxIZlRJemU2L2hvTkNEazlmNGExeGdnZHdHZ3NhMDZ1Lyt4OGkwMXR2WXFxNnZxWWd5ZStWK3EyQ0RKUjkzRHkvcjJsS2Q4YlFtQ0RwRDVHOHNST2ZnYWIvRmRwUkh6eUl1c0tkRWZRRVVFM2NLN3hiazV6ZHJMWlZZa2JnSTVUeHVmMTVlOU52TzVyUDhHMVA1RmQwRStpai9aaGQ2S2E1Z1JwWDZ5UmQ4S3BST1Rxc0N3UXg4WFpDTUcxUmNVY1duaVNWekhvdytHeHFLbVRwOG42bi8iLCJtYWMiOiJlNDY2MDg2ZmI5OGJlMWI5ODJmYjAwMTQzOGZhMzZiZWYyMzNjZWZhYWZlZjFhYTlmYWY1YWRhNmZkMDdmODBmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imd3ekdubytBYUZ0Y0oycVhEUWlzUFE9PSIsInZhbHVlIjoiWCsyZUd2ZE5sdzV2MGd4SjJRdjlSU1FrUDJzM1M4WlBxRlJKSDJHcFpNN1B2THVrN0NYcjBxVXpBNFh5cXlKY1JjbXY1SEQxTzVMN1hrbno5QVY1eDNrb0ZIUTZYbldjdEVvd0tPV3dsS0laY3EzRVVEL29kTlJHa2tSRW1yOFVHU1AxSEs0MGpTU044QVpQNUZTaHorL2F3WmFhWndmMDR0eEg1dnN3a082U2ZpeUp3TGVZbDJhUGd2THhnRk9IdjBoVEYxKy81RlFWK2JKOFFqekgzclp6TzE2ZzM3QkZDL01QdWduTFZGNk14K3NDMHNOVnp1a2VxV3luMWs4NE02N0ZPZDA4MFN4THdrandmZ3dySkdHS254Sm9BSGVCdmg4YkdaUHRGK0NmU08yRUtwb09jdHhIaFdTMEZmQ2I2K2VadDZnWDNXT0E5bm1ydUlFbG1obGVPcXVNeVlFbWJBQWFKWFk0QTlVdGN5ZGd2cHBzc21FbGJXYTNiaW1DUW1QUHF4UFNQUWdRdmc2NGtEM3lkUXZhZWRTTEtiN3ZlYThpYk9EaFpQSjBldGc5cXB1UDBzRUtkZnpCSTJHdG5tMmowdTd6TSt0NXp4c0t2eDVpakkzTmc1L2RibGJ3RkpxZHpVNVhhWWhPMWJkazQwR3RNNXMzMFQ5QnJsUjkiLCJtYWMiOiIwYmYyZTYzYWE0ZGZkNzRkZDVlNWQ0ZDkwNmViYjYzOGExZTZkOTlkMTFiNjk4YjgxMWIxYTZhYzViZDFhYzU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585878684\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-767530922 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767530922\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-484842002 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 14:52:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdVRDhhekZqMG5ZZEI2elhxT0xsSnc9PSIsInZhbHVlIjoiL3QrZzBLaVozL2xlM0thVWFGbUt2bUVZYVY0aCtmVElFOThyeGk0RnEwYmFuK3dhbWtOeUNRZno1dXhiTWJXRnY3Tzg3NEhrekswMWlua0Y2c3JiSVVLSGZ0NndJVHE3ZENObXB4aWg2QUFzMGh3bnYzZU9acXhKa2NJZHZ4OGp1clhzdUM3c2t3OUZyRDdpMXRjUFRmTEFUTTdHSmx3czlPQmt0M0dOSzFESGNLT1V3dUcwQVRUb3NveExpdUNqaVlVbDg5Zy95ZlR5aVl1NmlTYng3U1M3UmJkQmtUYTBsOFZiTnFSOFZGbEZkekhSMTVvSjBRY3M3MGFkSElwQkVvWkd0amhrV2Z3Vy9WalBLbnBYUHVZc043aEszVjdlMjJCTXZJdGZDdzNZVlpuTndQSUovWmdGOXY0OVMxbVhqSEFtNnUrbkJ5N0xxKzJFODNtR0VvVDVPaGF2dE9DRkovWXJPakJJMFllWGJINFl4VmQraXMrb2d0ejMySlVYL0EzNEVDVytIbjJZR3p5bk95YXh0YXZkNDA2T28yclV5LzZVUFVpc2V4cmp6L0grWnRDWTJmNmFsT3dnYkNva2dkV2NRZ2p1cnBJVWRRc3NyQmZTWm96dHlvbDdXd1NSUXkraElpUmdFL1Fua1ZyK3FldUViS1VPVzM3UmlPRlgiLCJtYWMiOiJmMjY2YjVmODhjNzBmN2YxY2VlZWIyOTcyNWQxMjk5M2I2NWU2NGRiMDVmOTUxZDI0MDAzZjhjNGY2MTUzYzZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imtla2hnYXZraVloU0Jkc2VPS0tONlE9PSIsInZhbHVlIjoiYWlEWEJxSWVOaTVyK2lwcEJJZmRUMHNWb0FHeUlIY2MwNmQ0eGNGODVGalR4eWh3bWVqU0o5OC9pbS96cnVrKzdqenQ0MmRZMDZHNWx0SzNKSmJwUmE2M0dOQlBnT3ZKcXV3UWVHRzd3THZzaDVGSXRHUWJ1Q0M0ZnVMRmRDVDViMXdpeGRlcEYyc1EwU1FhaDdnWUMrdGt0THlZT0ZCejQxcklkZDVrL3pHTndyUWpXWTVicnpZM2ZnRzlWSE0xTlkwSzJTUkwvc2dJaXFzVkVaTmg3WGY1dE5Ma0phNWVtSEdSR2dLMUVhUVJDQkQyVVRRaFl3d3J1M2pDWGZtOHo0cys5UkIyVlNTZlhRcEFtTE1YRk9MNXRqY0F1YTJYSmlSVnZaaWlGYkZXNnhXRFkwWjQrTml0MTlLRVFlaDFOdi9nR0R5V2VQQVd5TkQzL0piMFlqTUZxaHF4VzJpUGgzbU9ndnJNMFcrbHRNQ0lnQ1VQOWxOVjRPU2FabXJFOFkyR2ZqYXB5QkVVaElIT0hTSGZnM2FLdTZ6cHZLc0d3eklKNXNLalBFMm96VklLMHBua2pITUl6REo4L2d5K2p2R2lyOGVhc3NIeTErMkpKdEFLclFpcHlqemJTZlRvYmJ6T05MR3pBaE0zMmxYbWJjMkNUN2NoVWpheG9OUUIiLCJtYWMiOiI2MjljYWM2ZTY5Y2QzOWZiNTQzODBjZjJiYTkyYzgyMDU5N2EwMzRhMzRhZjRkZTE1N2RmZjJiNDA4OGFjMjBkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 16:52:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdVRDhhekZqMG5ZZEI2elhxT0xsSnc9PSIsInZhbHVlIjoiL3QrZzBLaVozL2xlM0thVWFGbUt2bUVZYVY0aCtmVElFOThyeGk0RnEwYmFuK3dhbWtOeUNRZno1dXhiTWJXRnY3Tzg3NEhrekswMWlua0Y2c3JiSVVLSGZ0NndJVHE3ZENObXB4aWg2QUFzMGh3bnYzZU9acXhKa2NJZHZ4OGp1clhzdUM3c2t3OUZyRDdpMXRjUFRmTEFUTTdHSmx3czlPQmt0M0dOSzFESGNLT1V3dUcwQVRUb3NveExpdUNqaVlVbDg5Zy95ZlR5aVl1NmlTYng3U1M3UmJkQmtUYTBsOFZiTnFSOFZGbEZkekhSMTVvSjBRY3M3MGFkSElwQkVvWkd0amhrV2Z3Vy9WalBLbnBYUHVZc043aEszVjdlMjJCTXZJdGZDdzNZVlpuTndQSUovWmdGOXY0OVMxbVhqSEFtNnUrbkJ5N0xxKzJFODNtR0VvVDVPaGF2dE9DRkovWXJPakJJMFllWGJINFl4VmQraXMrb2d0ejMySlVYL0EzNEVDVytIbjJZR3p5bk95YXh0YXZkNDA2T28yclV5LzZVUFVpc2V4cmp6L0grWnRDWTJmNmFsT3dnYkNva2dkV2NRZ2p1cnBJVWRRc3NyQmZTWm96dHlvbDdXd1NSUXkraElpUmdFL1Fua1ZyK3FldUViS1VPVzM3UmlPRlgiLCJtYWMiOiJmMjY2YjVmODhjNzBmN2YxY2VlZWIyOTcyNWQxMjk5M2I2NWU2NGRiMDVmOTUxZDI0MDAzZjhjNGY2MTUzYzZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imtla2hnYXZraVloU0Jkc2VPS0tONlE9PSIsInZhbHVlIjoiYWlEWEJxSWVOaTVyK2lwcEJJZmRUMHNWb0FHeUlIY2MwNmQ0eGNGODVGalR4eWh3bWVqU0o5OC9pbS96cnVrKzdqenQ0MmRZMDZHNWx0SzNKSmJwUmE2M0dOQlBnT3ZKcXV3UWVHRzd3THZzaDVGSXRHUWJ1Q0M0ZnVMRmRDVDViMXdpeGRlcEYyc1EwU1FhaDdnWUMrdGt0THlZT0ZCejQxcklkZDVrL3pHTndyUWpXWTVicnpZM2ZnRzlWSE0xTlkwSzJTUkwvc2dJaXFzVkVaTmg3WGY1dE5Ma0phNWVtSEdSR2dLMUVhUVJDQkQyVVRRaFl3d3J1M2pDWGZtOHo0cys5UkIyVlNTZlhRcEFtTE1YRk9MNXRqY0F1YTJYSmlSVnZaaWlGYkZXNnhXRFkwWjQrTml0MTlLRVFlaDFOdi9nR0R5V2VQQVd5TkQzL0piMFlqTUZxaHF4VzJpUGgzbU9ndnJNMFcrbHRNQ0lnQ1VQOWxOVjRPU2FabXJFOFkyR2ZqYXB5QkVVaElIT0hTSGZnM2FLdTZ6cHZLc0d3eklKNXNLalBFMm96VklLMHBua2pITUl6REo4L2d5K2p2R2lyOGVhc3NIeTErMkpKdEFLclFpcHlqemJTZlRvYmJ6T05MR3pBaE0zMmxYbWJjMkNUN2NoVWpheG9OUUIiLCJtYWMiOiI2MjljYWM2ZTY5Y2QzOWZiNTQzODBjZjJiYTkyYzgyMDU5N2EwMzRhMzRhZjRkZTE1N2RmZjJiNDA4OGFjMjBkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 16:52:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484842002\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1924672736 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1924672736\", {\"maxDepth\":0})</script>\n"}}