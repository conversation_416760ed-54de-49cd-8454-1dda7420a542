{"__meta": {"id": "Xba791c23c26df0815e5569d1132aedd8", "datetime": "2025-06-08 13:05:17", "utime": **********.647543, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387916.376474, "end": **********.647572, "duration": 1.2710981369018555, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749387916.376474, "relative_start": 0, "end": **********.495657, "relative_end": **********.495657, "duration": 1.11918306350708, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.495677, "relative_start": 1.1192030906677246, "end": **********.647576, "relative_end": 4.0531158447265625e-06, "duration": 0.15189909934997559, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45577432, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00634, "accumulated_duration_str": "6.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5725548, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.984}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.604584, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.984, "width_percent": 17.508}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6205359, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.492, "width_percent": 17.508}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-78822399 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-78822399\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-888550797 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-888550797\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-299072407 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299072407\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387909649%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpmeWoxNjRNbkRDUmY3U2E5a1dBWUE9PSIsInZhbHVlIjoicWwrb0FiUDdaRVU0V2FBVkpDcXJ5YUc3RXpCNGU0VmE4L3RKQUh6RTlGR2N6eERwRTFmYlhFZGVMaExBTDQ0Mml5UCtYVW1TWGhlbGluYUZFY0Mwb09DYjBzbFB5ODJJTVp4WExpb3g1MGg1RGpDQlFnK1RtMUkwUEpzU0RvU0ovSWptUTJpbXhKRndjbVNTM3RUbFBQdUxJajBzamZ3c29vTGpVUTdsR0o3ZDdJRjV6UlRTeUZvY3MrWTlWdStDN09lTmVZNHFrNjgyMTBXS0RkNkN4UUx3QzNZMDBta2FVRnNZNVQ3R0V4OHQyM2dOZzhqbW1EeTJxak1LVmIvSWZiRFhZaWZlUnZUcWk0ZnUyQ2RJOFIwaUZ0czMrT3YxZDdwdWEyRzljYTZMQWlPb3NSeWRCRis4UTlES1VNYzQ0WFFiWDZtN0lsRzIyZEhtVDN3cis1alhQZUdCVml4WXZ3MCtiRVQray9YQzZ3V3V2eHdXdFd2QWpBOHZIeVlldEZ3UDhJVnJGR1hBc214Z3Y0YktPUG8waEJERXloaUxlMlhSTjhtRmx0Zk1JZDVkdjJpUFZjVnlBRXE3dG9kN1ljMi9TZGZYTTlXdHFsOHZqc05vZHlkYmNjNTRpSHdTNDh3aDdxRlR4N05jTWQvNlEzNXVZMzBCTkxQc255d3YiLCJtYWMiOiI3NTY5Y2RiODI3YjViNjcxM2I0ZTFhMTJiNWI2YzFlZTc1ZTRkMGExYTY0NGYxZGM5YmFjZDMxNWNiMjkxN2NlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBEUzFuLzNtUjhIVWhyZFpsRWRDdXc9PSIsInZhbHVlIjoiYnVFbUZVb0JBbi9kU2FWbm9ZcjZHeXVyWmhPRUJDY0oyQThLRVFQdE5FS0ROK1FvWmhUNFlxZ1Bjd2Y4cDlqSmZadTNRQ0owU3hGaE1Wc3JjSUhGd29ENWxQQWJHdlB4WHlQVzBSUDhkaXpybDVmREZORlE5THVmWThzbG8xcytjUytuR3FtYk5PQTVnVVVJcjlhbHZiWEVac3JPTlBPVFZxbzJWNnZOUmtBZkUzdVVRWmhCcnpINUxFMmZ0a0pqbU5veGtyL3Q2ZFlRVnhxQTdxOUorU3ZxWjlnOFJIN2hDak9GN0tYb0N0ZUFFeHRua1ViR203KzNZUDlnYWJNMEFxM3NNRXl1a3prK1ljd1pqajRsUE0yRnNkZzV6TjFGdWhXYllTNTBncytpZlVaSUs5ME5WRFhUeTg0MGtkYWxObGlkNyt2N2p1QzRGT3hSQWd0dVNBdlNLbkNCUmpKN0dyV2k4LzQyM1NkUFMxVzVmTzVVbytUSTdVRll2d204dUdYNWxXbXJUY2tuZ2owRVNSUVpUOWF6WG9QQXVqSHM5QzlpME95cDJBaTJqS1dtd2xwdlcxblFhTUd6UU94ckxCbDMwNjhuaWVRUVJ2SXliblRsd2JJdFBVUWNxWkJOcCszUVRub2I1M1FkSHJaeTF5eGVLNFNvekhpZ2hmUzgiLCJtYWMiOiJiOWQ0ODk2N2EwZjI2YmE4ODcyNjNjZjAwMWY0MjFlMDBkMjM3YWU0MjkzZTIyMjI1ZjE0MDY1YmY0YjBjYTIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:05:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImkxYWQwdlh2SzY4dGNOeEcxVE9XUlE9PSIsInZhbHVlIjoiQi94Sk5ZakNVNHpmM3UwNXlHVUplTjFRbk5rKzdSbG4rQk42cXplWC9Pa2VneXlpRXZXdWZ4WTVyekU5N1NLa1Qwd2ZVbVlEVHZkdkJ2dGpMR0w2VGJWMjk0ZnNhZm5UM1c5aVdVRlo1Ri9ka3B6R0R5RW9qbVlmRVEvMHl5Y0xFc05QMlZtYVJYay8zbmUwSGFDY3BVZDNmMUgvSlB3SWFRVHdjYlEwWTlnWVVkSUxZV2ZWcGxqam0wWlR6a0tVWWcrQ09XL2J1SVBJTGJ6TlFRMVVJc3lPWm14NkVIcUs0cVVjRkVhNjR5UVdJSEZqbFMvTDErNDZ3K2JqYklTVFIzTzc5UUVYTGZJeDZzb215L3RSaXdzNGdOa1hTVElpZkV1eHJ5Q3JzaUhuSncwbld4NXJOYzQyUWhJd09UKzk4TitnamVqMjlkazloU0hVOUxFZmRCNEphR29WWXpLSGEvcXhjSm1yVTNjYy9TdEtBMHZTaGErb0FiamNSZHFUZkJ4ZTRtM2R1bkdvNDBJeDUzK2tMcm5YaXlycmdpYnd2VlZ4Yi9uTmhhNEl0SjhzSWZiRjNKUlRFUXdVSUZBNVBBL2pZLzROUUlzQlJWSThhVy9ibXo0TFgyMG9rd2xKSmJNWjBSUzJzT0QvREJNMmRyR0MzRnhEU25OVWZkaFUiLCJtYWMiOiJiYWNmMTJmMTdkZjJhZmNjODAyY2QzMjE3YjA0MGUyOTEwOGY5NTEyYWM4NzQzYTNlNDk5NWYxZGE2MzA3NjE4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkU2U3ZNYk9EUW9GdDlzRGtBcXU3dlE9PSIsInZhbHVlIjoiOXEyenpTY3kxd2pGMDBRMGphMG9ac1BQZFN1QWxMUjREdDVaNkdXWmJVbFFZRzB0aFlIY3BucWo5MFN3c2RSN1gxTitFUW4xYjN0OE1qenIyY1BWbysrVWx2UlRjVVNsOWNlb2ZVMStRNFBBZ0gzeGdsZ2ZxTkUyb0lYYWw1eFI1V0d6VU5hNzR5dnlMdVUzclB2b25zTWo1c3lxVEw4VGRXUFlFVkF2TGxRMXFDOVZxa3l1TzJjdGl5UDRoWUQ3ZVdUa1JzUGlGOVlUclI1SHpUU3JxSXBUcW1oMVBKMWU3Q3AxYzAyOWM1V2hSOWNiZDNRS3hXRTBZVGNsRnJsSGJDY2xUT2UwcStlSG12NlZJRDZmek84OG9uRWlRaVZtaXRXNlVQWFUrWXNnMWgyem1hK2gxWmNBZFF2SlVhai9ON2g1bXRtWlZtUk1lQ2YzYlpFTVVCWU9YTVpaa0ExUndMRkhNc2graFlXK3FISVNOR3o5OUR5S2dwQUw0Z09VSC9vVkE3SDJHRTduY1ZDQTRxL05nK3ZrZExQdzhYbEhXZnR2NTB2K2VDQ0I5bURqbWNSWUFtU0Rwb0hmbEJvY1plemprQ1hkVmNuQ2ZWYkYwSzVCTVJrM3RzREE3Njd6Uk9waWRDNFd5SDRPMWREcURobU5BZVFSZXZVOWFCTk8iLCJtYWMiOiJhY2ZmZjZjNjQ2NjNmZTk5MDg5ZTE4ZTlkYjE4ZjkzMzg5OGEyZTgwYmNmMDQwYmVhMzI2MDQyNzMyY2FlYTYyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:05:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImkxYWQwdlh2SzY4dGNOeEcxVE9XUlE9PSIsInZhbHVlIjoiQi94Sk5ZakNVNHpmM3UwNXlHVUplTjFRbk5rKzdSbG4rQk42cXplWC9Pa2VneXlpRXZXdWZ4WTVyekU5N1NLa1Qwd2ZVbVlEVHZkdkJ2dGpMR0w2VGJWMjk0ZnNhZm5UM1c5aVdVRlo1Ri9ka3B6R0R5RW9qbVlmRVEvMHl5Y0xFc05QMlZtYVJYay8zbmUwSGFDY3BVZDNmMUgvSlB3SWFRVHdjYlEwWTlnWVVkSUxZV2ZWcGxqam0wWlR6a0tVWWcrQ09XL2J1SVBJTGJ6TlFRMVVJc3lPWm14NkVIcUs0cVVjRkVhNjR5UVdJSEZqbFMvTDErNDZ3K2JqYklTVFIzTzc5UUVYTGZJeDZzb215L3RSaXdzNGdOa1hTVElpZkV1eHJ5Q3JzaUhuSncwbld4NXJOYzQyUWhJd09UKzk4TitnamVqMjlkazloU0hVOUxFZmRCNEphR29WWXpLSGEvcXhjSm1yVTNjYy9TdEtBMHZTaGErb0FiamNSZHFUZkJ4ZTRtM2R1bkdvNDBJeDUzK2tMcm5YaXlycmdpYnd2VlZ4Yi9uTmhhNEl0SjhzSWZiRjNKUlRFUXdVSUZBNVBBL2pZLzROUUlzQlJWSThhVy9ibXo0TFgyMG9rd2xKSmJNWjBSUzJzT0QvREJNMmRyR0MzRnhEU25OVWZkaFUiLCJtYWMiOiJiYWNmMTJmMTdkZjJhZmNjODAyY2QzMjE3YjA0MGUyOTEwOGY5NTEyYWM4NzQzYTNlNDk5NWYxZGE2MzA3NjE4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkU2U3ZNYk9EUW9GdDlzRGtBcXU3dlE9PSIsInZhbHVlIjoiOXEyenpTY3kxd2pGMDBRMGphMG9ac1BQZFN1QWxMUjREdDVaNkdXWmJVbFFZRzB0aFlIY3BucWo5MFN3c2RSN1gxTitFUW4xYjN0OE1qenIyY1BWbysrVWx2UlRjVVNsOWNlb2ZVMStRNFBBZ0gzeGdsZ2ZxTkUyb0lYYWw1eFI1V0d6VU5hNzR5dnlMdVUzclB2b25zTWo1c3lxVEw4VGRXUFlFVkF2TGxRMXFDOVZxa3l1TzJjdGl5UDRoWUQ3ZVdUa1JzUGlGOVlUclI1SHpUU3JxSXBUcW1oMVBKMWU3Q3AxYzAyOWM1V2hSOWNiZDNRS3hXRTBZVGNsRnJsSGJDY2xUT2UwcStlSG12NlZJRDZmek84OG9uRWlRaVZtaXRXNlVQWFUrWXNnMWgyem1hK2gxWmNBZFF2SlVhai9ON2g1bXRtWlZtUk1lQ2YzYlpFTVVCWU9YTVpaa0ExUndMRkhNc2graFlXK3FISVNOR3o5OUR5S2dwQUw0Z09VSC9vVkE3SDJHRTduY1ZDQTRxL05nK3ZrZExQdzhYbEhXZnR2NTB2K2VDQ0I5bURqbWNSWUFtU0Rwb0hmbEJvY1plemprQ1hkVmNuQ2ZWYkYwSzVCTVJrM3RzREE3Njd6Uk9waWRDNFd5SDRPMWREcURobU5BZVFSZXZVOWFCTk8iLCJtYWMiOiJhY2ZmZjZjNjQ2NjNmZTk5MDg5ZTE4ZTlkYjE4ZjkzMzg5OGEyZTgwYmNmMDQwYmVhMzI2MDQyNzMyY2FlYTYyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:05:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1124160367 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124160367\", {\"maxDepth\":0})</script>\n"}}