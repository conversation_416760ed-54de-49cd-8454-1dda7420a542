{"__meta": {"id": "Xa2288063cfbc18e6da4e31037af87b92", "datetime": "2025-06-08 13:44:06", "utime": **********.503332, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390245.142411, "end": **********.503362, "duration": 1.3609509468078613, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749390245.142411, "relative_start": 0, "end": **********.238182, "relative_end": **********.238182, "duration": 1.095771074295044, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238206, "relative_start": 1.095794916152954, "end": **********.503366, "relative_end": 4.0531158447265625e-06, "duration": 0.26516008377075195, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131096, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01863, "accumulated_duration_str": "18.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.338795, "duration": 0.01174, "duration_str": "11.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.017}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3742769, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.017, "width_percent": 7.837}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.426176, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.853, "width_percent": 8.266}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4338672, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.12, "width_percent": 6.173}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.468733, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 85.293, "width_percent": 9.018}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.48017, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 94.31, "width_percent": 5.69}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-184543837 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184543837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.446083, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-930683567 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-930683567\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-930020633 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-930020633\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-832891899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-832891899\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpDSkpCZnY2cUE4aEV1VGphc20wN2c9PSIsInZhbHVlIjoiUUpqck9hZmhxOFBjeHprVVJ5czg0N3RnZ2VOVGNWeHlXMU53NzR6RjNoQlNoTmtDQm93dE9lUXpLQnB0MGVlZklyRlNtcmM3azdpcnJSMnNDQ3A0bWNSblA1NS84Q0orS2xiQmJDWTQ0ZFRQeW42UEVFR012OEtKTUtUVXhLU0pnVWFwZzRmdkhJcGJwL0ZxR2tPUTRtTm94ck5WTi92SGpOc00xa0hxRE1FK2lDemZSL0NkM0xEQ0N1em1qQ2RUTXBXKzIyKyttZWFVblBXQzdseDRXRnk0Y2RTaFVncUlLTUJaSjFrZFlEb2RoY0lmOUkxN1BQaXU1RjZMN2Y0V2NrclZTdXh2M1JmRU1aWW5BZFo2cElVZDJ2bUZxQi9DblhtMnlPdTk2bThUc25DYjhEczN2YnVXZWRCNmxmRll5K1VEaGhUZHd5ZXdUNzNFdkV4anl0VS9FNXFSSTN3c25MYUlZcnhPbWZJbW0wWHZNaUdTaUFoZ2hDaTBObXpKQi9FL242UHpHRzhBcCtVYy9FcC9WeUZHMlU1RStKWElnYjMrb3VmczRmdDFZd0l1d2lleU1Cby9RbFNEaHc5WllPV2Z6T3lBYUs5b095WmxhWlgyQ3E4Q0tWUjJjODVhMzdnYW5TRk1tMXlTUTJ2aGg1WmJqeGRNZ3gxRE1JeUwiLCJtYWMiOiJmN2ViNjk1ZjA3ODc1NjUwMWE3MzIzOGI0ZWMzYjQyODc4MmIwMjczMWNiNWQ5ZjE1ZDFiM2YzMDNkZGMzOWRmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5wMTd0S1dzMnljZmFjbjVqeUI4L2c9PSIsInZhbHVlIjoiQjdNOE9QcngwbE5DMmlTd3hUOFdtT0gxL1hLZmlWekdtcU9UVzFUL1A4ZU1zdEtDMjlQUmtOMytDclk1NUZDcGFCWUlVb3lTMmRnMytPeFRhMTl2aDIyYk54VVZDbVhPQjFJVGpaeEdKNm4zTUUybk5scHJFN0x0bTI0dlk0OGhNQ3lRanNHSkQ2UTEyUWR3bzYrSXNvclVhTURFRFQ4UFdaNmtRMXUwQ2lwblhsOWR3bDc0MG5zMkJZQnNIN0NsOVNvekF5bk5US0pKYnc2NEQ1VFpMcDRGMlUwdEdJcC9FWFIzYnJ3bjNFQ3ZvMURsM1luS1hUeWdTZXRtcXQxamhiTlpGZnZBVGRnejRXLzRiSHFxL28yS2tkUUpEMmxEQXgvK29ndUhCVk9tSjhHTWw3UGZibFhPWmk3Umw0MzZPcXZzd012bk16MTBFSFlQR2lYU0JGS2FsaXNSVmJUYndScFV2bzAwejE0b2s2VWUwZXhGNzFSRkhJYTMrLzFrYkhKYWU1ZlNxanhzV2R3azhWeStpZFVJWlJTNzV2UGxyT2htak5JQzQ2Tm1wTE8zTlB6SnBGZ3dPSWhacko1UDkvUzladEZSUVhwQVBGbWhsQkdUKzRmSHBEcnBkYWVrQWp1a0hURzNvdHhrYzlLUTdocjZMR3N4eE1ZOXlDMjkiLCJtYWMiOiI4NTQ0YTU3NjkyODBkYTc2YmNhNmVlZjQ1NmIwZWM0OGVlN2JjMDEwOGMzYjZjYzYyYWRlNzY0NDlhN2UwZmQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1421352995 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdBcHQ0VUdzbEFPaDFUV3V1OTFva0E9PSIsInZhbHVlIjoiY1RtZnNSaFV5dTFUdGlwdVdyZWZ1cnpKbkVDWFRSUVcvRzNKNy9aUnRhUndGZ3l4Y3Y1ZC8wQlBDWXdPZ1RtRDI3ZjV0V0Rrak93N0htZHlrUCtzaHd6cVFhdjA5U25YUlZRSDNVVnI0cWdTN3JJb1FlUXUrc2cwZmw5NUw0MG90cndwenNLRU1rcDlZLzl4S0c2aDVwUm5jUkplbk5UQlUrSjJia1FjN3J4OGt6bTFYbDdzbkwxMkdUQ0xjRm42TWlGTEg1ajJsM2l1clY5TE40aU50bFB6SXdNTFFZYmxjMG4vT0QxVFp6L0pQaEFOTzh3L1JENTRsbFdSZDhYS3EzR0ZINW1ROVdRYmcvOXlTUEVRV2EzYUxrelAyMTRydVFRZzFmclljWWp3K0pub3FmUkZoTUxBYThNaThIOXBtNVMrYzRsSnZRTXpnM2NyYmRqS3kra2RHKzJtUHkwR0ZXd1F3NzFTVHZ2OHBVU24rRytFUEJoaEttMHlGYy9hazNESmpFZy9QUC9rL1E2U2JFNkRkODNhM1J2dURNMXNUUC9IS3Z1b3J3Z05NMUZ5VVlROE90WFRBTmpwTnZYUFpzQ0hSemMvTkltV3hzVmM2UTBkV0MwT3FtM3JDVXpKN2VkT1JCeW5jTE9DWS9xbDZIRnpwTDdpd2IxVzhaVFAiLCJtYWMiOiJhMzNjYjg0M2M0MDk1OGFlNTc3NWQxMTdiZGYzN2UxMjE1YjQ3NzY2YTJkMzY3MGM0ZDk4NjU2YWNjMGJmOTA1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlY1NkdHV0NhVlh0RUVmS3BmMHdjWEE9PSIsInZhbHVlIjoicUVSMlc5bmh4SVFreXIrS3BCOWdRRU1wSFE5UDFrbDc5NVVkdFJ1TTBBaEszYjE5Nk1leVcvakhQKzZ6N1dtV1JVb3hqUXRZMGdJWGtMd0ltcXRORFJiZVBaTVd1WjFlN0xoMmpXTWs1WC9iZjdwY2ZhS3R5a1JwUkc4ZDhYRGpUZmhYNFJNNDlzWHNqVmh4VmpNb2ZoRnM1YkRlbDFIL05sQmdhRkxBVzNOaVgwaHVGdlJsYzlmRW1MbktueDV3WTZObWxlWmRRYmRCWFE1em5xNTJ5cUFwRkYzNFdESmxYUXEyTVZMeDd3bnlIUVhlZ2prT09NY3djdDY2ZkZFL2owcU1udGNQV2NxclZYMEk3UEcyaDJYRFlHUnYrYUlIUkFpcGJSTjFvMGo0NS9HU3pLN2Ntallvais1Y0I4WmZFS1liemluajE3dW1jZEcwSHBtUjd2Z3Y0NGxxNU1rZkhzaTAwUEtBWEdPMk5aVHRmQy9tQll5VVphUjlMN1FkRmF0Y0k0aS9Hd3cwbjhQTzZJcEwyWEphdG9FbHdUa25Yc0x6VHMzNGJYc2pTK1dYTzY1WHB4Ny9hQlVxM3NxckZEdXJtTFlhMEtHZ1dyMHpuUnFJSjk3ZXNtSjd4ZVMxd3p0SVQ5TXdCTDI5Z28vT3RXK0N6Rll0TU5PY0ViL08iLCJtYWMiOiJkNmUwNzk4Nzc0M2YxN2I0MjYwZGU5M2RmNmVjMjZiYmE3NjYxN2M5MjA0ZTNlODkwZTUyMjYyYzgzYWY5M2IxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdBcHQ0VUdzbEFPaDFUV3V1OTFva0E9PSIsInZhbHVlIjoiY1RtZnNSaFV5dTFUdGlwdVdyZWZ1cnpKbkVDWFRSUVcvRzNKNy9aUnRhUndGZ3l4Y3Y1ZC8wQlBDWXdPZ1RtRDI3ZjV0V0Rrak93N0htZHlrUCtzaHd6cVFhdjA5U25YUlZRSDNVVnI0cWdTN3JJb1FlUXUrc2cwZmw5NUw0MG90cndwenNLRU1rcDlZLzl4S0c2aDVwUm5jUkplbk5UQlUrSjJia1FjN3J4OGt6bTFYbDdzbkwxMkdUQ0xjRm42TWlGTEg1ajJsM2l1clY5TE40aU50bFB6SXdNTFFZYmxjMG4vT0QxVFp6L0pQaEFOTzh3L1JENTRsbFdSZDhYS3EzR0ZINW1ROVdRYmcvOXlTUEVRV2EzYUxrelAyMTRydVFRZzFmclljWWp3K0pub3FmUkZoTUxBYThNaThIOXBtNVMrYzRsSnZRTXpnM2NyYmRqS3kra2RHKzJtUHkwR0ZXd1F3NzFTVHZ2OHBVU24rRytFUEJoaEttMHlGYy9hazNESmpFZy9QUC9rL1E2U2JFNkRkODNhM1J2dURNMXNUUC9IS3Z1b3J3Z05NMUZ5VVlROE90WFRBTmpwTnZYUFpzQ0hSemMvTkltV3hzVmM2UTBkV0MwT3FtM3JDVXpKN2VkT1JCeW5jTE9DWS9xbDZIRnpwTDdpd2IxVzhaVFAiLCJtYWMiOiJhMzNjYjg0M2M0MDk1OGFlNTc3NWQxMTdiZGYzN2UxMjE1YjQ3NzY2YTJkMzY3MGM0ZDk4NjU2YWNjMGJmOTA1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlY1NkdHV0NhVlh0RUVmS3BmMHdjWEE9PSIsInZhbHVlIjoicUVSMlc5bmh4SVFreXIrS3BCOWdRRU1wSFE5UDFrbDc5NVVkdFJ1TTBBaEszYjE5Nk1leVcvakhQKzZ6N1dtV1JVb3hqUXRZMGdJWGtMd0ltcXRORFJiZVBaTVd1WjFlN0xoMmpXTWs1WC9iZjdwY2ZhS3R5a1JwUkc4ZDhYRGpUZmhYNFJNNDlzWHNqVmh4VmpNb2ZoRnM1YkRlbDFIL05sQmdhRkxBVzNOaVgwaHVGdlJsYzlmRW1MbktueDV3WTZObWxlWmRRYmRCWFE1em5xNTJ5cUFwRkYzNFdESmxYUXEyTVZMeDd3bnlIUVhlZ2prT09NY3djdDY2ZkZFL2owcU1udGNQV2NxclZYMEk3UEcyaDJYRFlHUnYrYUlIUkFpcGJSTjFvMGo0NS9HU3pLN2Ntallvais1Y0I4WmZFS1liemluajE3dW1jZEcwSHBtUjd2Z3Y0NGxxNU1rZkhzaTAwUEtBWEdPMk5aVHRmQy9tQll5VVphUjlMN1FkRmF0Y0k0aS9Hd3cwbjhQTzZJcEwyWEphdG9FbHdUa25Yc0x6VHMzNGJYc2pTK1dYTzY1WHB4Ny9hQlVxM3NxckZEdXJtTFlhMEtHZ1dyMHpuUnFJSjk3ZXNtSjd4ZVMxd3p0SVQ5TXdCTDI5Z28vT3RXK0N6Rll0TU5PY0ViL08iLCJtYWMiOiJkNmUwNzk4Nzc0M2YxN2I0MjYwZGU5M2RmNmVjMjZiYmE3NjYxN2M5MjA0ZTNlODkwZTUyMjYyYzgzYWY5M2IxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421352995\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1982625027 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982625027\", {\"maxDepth\":0})</script>\n"}}