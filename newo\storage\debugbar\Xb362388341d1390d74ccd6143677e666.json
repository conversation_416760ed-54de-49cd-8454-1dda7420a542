{"__meta": {"id": "Xb362388341d1390d74ccd6143677e666", "datetime": "2025-06-08 13:34:50", "utime": **********.261871, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749389688.985047, "end": **********.261901, "duration": 1.2768537998199463, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1749389688.985047, "relative_start": 0, "end": **********.097732, "relative_end": **********.097732, "duration": 1.112684965133667, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.097753, "relative_start": 1.112705945968628, "end": **********.261904, "relative_end": 3.0994415283203125e-06, "duration": 0.16415095329284668, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45602312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00694, "accumulated_duration_str": "6.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.188128, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.96}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.21868, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.96, "width_percent": 16.138}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.234766, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.098, "width_percent": 21.902}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/receipt-voucher/1\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:2 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 3\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 30.0\n    \"originalquantity\" => 21\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 41\n    \"product_tax\" => \"-\"\n  ]\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1240162495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1240162495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-643340117 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389683194%7C31%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFRb2xpUmtLTFY0Z3U3SnlmVjhXbGc9PSIsInZhbHVlIjoibXV0eVM2UVV2ZUtGWXJNOUZCemRsZXp1SFc4WmNiZ0FQdDZ1Zkc2U1hHcHFJd0hOalUrS1o4bktDOUk4TDZ4K2RJSy81V1B2ZzdPK3ZzUjFTb1p6N0JBRUVubmpLdHRUMVNuTytSdktOU0xUYmlWczg4L0ZGY2NJZEdlaEE3UEYwckcyOHdGR1c3Qk9kWVROSldZU0ZQVFNxUzlvSUtBeFZiZ3JzTXorQVBsTTNScnhqcFhpRXlPbkFKNW9aU09KeFV1Q0tNNnhKbnhZeGdPNXZ6Q2tWWnhWdy9jOU9DdTF2dSsrUUpZamJFSythcXRDTGUySlVzN0FEc0hJUUZHSEZyUTZkZ1BoWnZYeTd4cGZpMHVrUk9jcGwwVm0rUDNaby9pSkdNZ3RlRzBxbTZGeTdocUlxQkt0M2Y3OS9oNzFPS2ozd1k4NmQ1UlVVaWc3dFBoakVtTllQT3oyZmJqNW53MzdaY2FTOGxjUlFkWVdhUWt1R1NnTHZIVUdOSHNwQVFiZXB0SFErcml0aFg2TUJXUkhmZWhzSEhIVWdVWlNnZVJKVDEvSUZVd0RlY3d1TnNLWkM5REpaRGowb3ZzU1BIcGNuNmJoQ3QyMjlOMXRSN2Ftd1FBZjhVU1RTTmUrZEV1M3ZNeTZ1d2VIZmx1QzNJUEw0ZDBNbFhKb0U0ZE8iLCJtYWMiOiI2OWU5ZWFiZjE2NDZhMjk1NTMzYTQxYTAyZGVjYzA1OWFlZGQ0YzI4NDZkNzA1OWM5MTFlZDJlODRmZmE4Mzg3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZEVGd2Wm1qdWU1QUFhNDdlcW5TMVE9PSIsInZhbHVlIjoiVDhncHVHT2NSb0xTRjczQ1NYcUlpTzliQmpyOGRVbHRBRGpHbmpsOTNWUXppblMzL3UvNTg5Mm8yOFhRUCtya0xrRUx0TDFENEVrTFIvV1kydVk4cnVscDBtZGJSZjV3YklENkF2eGM3WGNqN3QxMWdveWpTdzRTNnphMURtVk9IV0NERU8yWnFkMTgwUzl6bC90VVJ0c0Zwd1JtOFBRQjRsY2pLaXpwd3hkdDRobGlvVjdld3BCdkFxU0xibXJ1NGZZWHVycGJjRTdLekRNRERIa09CU3FDbGRaS0VjTTVvNzVPL3RIeE1BQnh4RlBhSmVJVGdLRWlwZDZlZmtlZ0E4ZTk5bkxiTGxUOGExTFFRQWxlL1FDOUdBeXpXdkI2OGhyaVdMcVNZVU9ZZ2xiQU94clFidTFoSlhCUGJZNFNpTmI1dzhzd3ZZTkd2RG83RUtJMVM0cGJPRTVjUjlka20zRXd5S0xhdEljYWJSeU1qZkhBUCtpUHA5Z1g3T1YyY0sxUU9uZDk1TlQ4cjIycUhhT0pjYWhGd2RpcHM5QlNTQXFqRC8vb2NsTTJpcXdJREtIRktDcW82YmQzUElhbnc5djNucmcrZFBqa2o4Q21KYVJETXhwdG9nNm5EMmF4SGlscTl5emI4VlFtZlMzTFh3NEZzQ1pwTGROdDdEb2MiLCJtYWMiOiJlODQ1NzM0NTkyMGIzYTA1ODVhZTkwYjMxNmVhZjIyNDc1Y2YwZWY2MmI5ZTk2M2MxMWExYWI0YjJhYWUxOTEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643340117\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1016032240 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016032240\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-82328426 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:34:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZVS01kNnBDVXhZNTBrSmt1QzlRMEE9PSIsInZhbHVlIjoic0djeXI3aGRRTG1tNnEwaklEVE9qUDBKQ3BodU9sZnQzTmhFSC9xSXpQc2ZWQjY5TW40RllnYWtnU21OSHJ4V0ozS241UEd6UzMrQTZNekdCZ1Nvb2Q1V1lsZWJwdGFBVzQwcDVaUVkxUmdkVjlpQVY4djBTQTNiakJXTnU1Sk1OSjdIMnlTNjFrdXg1VTNRbi9GOHNGQ3RaY1JOMEVHaUZLdDJsNmFPMllKNUR2Y3pxM09kN0UwWlZsZ3FGb3NJU0dxYXJoWGhJWWViOUh6Tk0xcDVyQm16UW5lN2ppamptM2RJTWFCdHFLU2dHN0N1bmNGZVdrckx4OUdieDRWZDhOUElFY2hMTmNPbFBYdU43RDQzR08xKzRFaUlUWTZvQXBvVUc2ZTZrV1E1Z3N6OGZ5U0pkZTVMOERmS21LYWo5aUx5SXpzaHVaSXZVb0kzYWhtazRBOUhVVkhXRVdtY3lIL3lNRWRXWVZmQ3Q5S05SaEtyakxvTytaaWpVb1ZjU01KSnJNNkYxZkl0Zi9tUmowYW9QTm5IV2UrMGVRMFNYdi9PTE1yb2gvSmNhcHJJNERBcGVNTmd2MnBBTG93anBFVitvRXdnMnZ3UUVKN2FUNTQwTWRpWGVacEV6ekxQVkMwbDJFR0NwUDB1VG0xYmR2UUwxeXZpRTNtVE12cnkiLCJtYWMiOiJmOTI1ZGU3NDJhN2Q4NmY4NmM1MzdhYTgxZmNjZmE1ODJhNTI0MmYzNmU1ZmU1MjIyNzY2YWQ4NGFlNjBiNmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZzV3VZdGp4VGk3bHYzRnhUa2ZrcUE9PSIsInZhbHVlIjoiSklITUVPajkxY2FobEJxcy85Z3Z4NmI3V3JwQ2V6UEoyMEQ4YUx5emhseldSRTd6RkkrMFJkZlpFbHk5M1VUMVc1c0tLaWdmYTVGa3hld0dmcE5sb3U1LzY4UnRMTEFGYS9nbmFUOFhXYVUzcmVmcFhOb2tkSi9PbTdScFIvZlhxdGQ1UHNKSkM4UmtWbTZHN1A0cDN1RnpHVkE3cmd5enRlRFhhTjNhL0NrUEtTL1gyU01NclNGR0plUTdQQ1loY0lqNkoyNW1mQlFNd3Q1akI1VVNIWlFSVlRxQUVoQ3BDS3NUeERkbUptOFU3WVI3M1hwaGVkc3RCelAzM0RsS1U2b2RRUTV5VENFUEloUkpqZE1TUHc4UmRNTHZVSVcrbFkwSkJCeFo2bFFEOGpoTmlLbU01U2pZd0pLVEJMQU0rbjIwVXR4bExqb1lGWjRCWmJxNmpXTFowYnpkcmpVMnkyNS83MHYxdW5PUkwwTGdKQWU5V2ZOeVNyZFRMcTRpR3ZZYU9laS9oSnVnSjFvaE1YTXZGUzNzdFV5OHVtWWc1dDJ0ZW9xbUR6eGJqRWxyUzNKZ1JRdzRMN1ViTlFXSTBCZUlxMXA0bERtMGxmU1B6aUdEdGNBbHJxWitUUXNOSXZkOVY2WmtEcnhXcUpZVE1qb2p2aS9JNzQ4MkZuS3AiLCJtYWMiOiJlNjQ4MmFhOWFlYTVkMGFjYzI2MTRhYWI1YzI3YWQwMmJiMzlkNmQzY2QxODA3YTQ2NjU4ZjNiZTkzOTFkNDZmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:34:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZVS01kNnBDVXhZNTBrSmt1QzlRMEE9PSIsInZhbHVlIjoic0djeXI3aGRRTG1tNnEwaklEVE9qUDBKQ3BodU9sZnQzTmhFSC9xSXpQc2ZWQjY5TW40RllnYWtnU21OSHJ4V0ozS241UEd6UzMrQTZNekdCZ1Nvb2Q1V1lsZWJwdGFBVzQwcDVaUVkxUmdkVjlpQVY4djBTQTNiakJXTnU1Sk1OSjdIMnlTNjFrdXg1VTNRbi9GOHNGQ3RaY1JOMEVHaUZLdDJsNmFPMllKNUR2Y3pxM09kN0UwWlZsZ3FGb3NJU0dxYXJoWGhJWWViOUh6Tk0xcDVyQm16UW5lN2ppamptM2RJTWFCdHFLU2dHN0N1bmNGZVdrckx4OUdieDRWZDhOUElFY2hMTmNPbFBYdU43RDQzR08xKzRFaUlUWTZvQXBvVUc2ZTZrV1E1Z3N6OGZ5U0pkZTVMOERmS21LYWo5aUx5SXpzaHVaSXZVb0kzYWhtazRBOUhVVkhXRVdtY3lIL3lNRWRXWVZmQ3Q5S05SaEtyakxvTytaaWpVb1ZjU01KSnJNNkYxZkl0Zi9tUmowYW9QTm5IV2UrMGVRMFNYdi9PTE1yb2gvSmNhcHJJNERBcGVNTmd2MnBBTG93anBFVitvRXdnMnZ3UUVKN2FUNTQwTWRpWGVacEV6ekxQVkMwbDJFR0NwUDB1VG0xYmR2UUwxeXZpRTNtVE12cnkiLCJtYWMiOiJmOTI1ZGU3NDJhN2Q4NmY4NmM1MzdhYTgxZmNjZmE1ODJhNTI0MmYzNmU1ZmU1MjIyNzY2YWQ4NGFlNjBiNmYxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZzV3VZdGp4VGk3bHYzRnhUa2ZrcUE9PSIsInZhbHVlIjoiSklITUVPajkxY2FobEJxcy85Z3Z4NmI3V3JwQ2V6UEoyMEQ4YUx5emhseldSRTd6RkkrMFJkZlpFbHk5M1VUMVc1c0tLaWdmYTVGa3hld0dmcE5sb3U1LzY4UnRMTEFGYS9nbmFUOFhXYVUzcmVmcFhOb2tkSi9PbTdScFIvZlhxdGQ1UHNKSkM4UmtWbTZHN1A0cDN1RnpHVkE3cmd5enRlRFhhTjNhL0NrUEtTL1gyU01NclNGR0plUTdQQ1loY0lqNkoyNW1mQlFNd3Q1akI1VVNIWlFSVlRxQUVoQ3BDS3NUeERkbUptOFU3WVI3M1hwaGVkc3RCelAzM0RsS1U2b2RRUTV5VENFUEloUkpqZE1TUHc4UmRNTHZVSVcrbFkwSkJCeFo2bFFEOGpoTmlLbU01U2pZd0pLVEJMQU0rbjIwVXR4bExqb1lGWjRCWmJxNmpXTFowYnpkcmpVMnkyNS83MHYxdW5PUkwwTGdKQWU5V2ZOeVNyZFRMcTRpR3ZZYU9laS9oSnVnSjFvaE1YTXZGUzNzdFV5OHVtWWc1dDJ0ZW9xbUR6eGJqRWxyUzNKZ1JRdzRMN1ViTlFXSTBCZUlxMXA0bERtMGxmU1B6aUdEdGNBbHJxWitUUXNOSXZkOVY2WmtEcnhXcUpZVE1qb2p2aS9JNzQ4MkZuS3AiLCJtYWMiOiJlNjQ4MmFhOWFlYTVkMGFjYzI2MTRhYWI1YzI3YWQwMmJiMzlkNmQzY2QxODA3YTQ2NjU4ZjNiZTkzOTFkNDZmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:34:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82328426\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1383247885 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/receipt-voucher/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>30.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>21</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>41</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383247885\", {\"maxDepth\":0})</script>\n"}}