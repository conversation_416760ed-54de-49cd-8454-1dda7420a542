{"__meta": {"id": "Xb09016e0613368d852a2eab2ab45a88d", "datetime": "2025-06-08 12:58:08", "utime": **********.880009, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387487.489259, "end": **********.880045, "duration": 1.3907859325408936, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1749387487.489259, "relative_start": 0, "end": **********.713766, "relative_end": **********.713766, "duration": 1.2245070934295654, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.713788, "relative_start": 1.2245290279388428, "end": **********.880049, "relative_end": 4.0531158447265625e-06, "duration": 0.1662609577178955, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45399496, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00826, "accumulated_duration_str": "8.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8148792, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.433}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8495, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.433, "width_percent": 13.317}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8596659, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 80.751, "width_percent": 19.249}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1815800551 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1815800551\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1285182642 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1285182642\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1869032591 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869032591\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1998239643 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=1adle91%7C1749387444987%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRkNVFJZkZEYWEyd3dNd3JoN0cyMUE9PSIsInZhbHVlIjoidzBVRkNkYU5BNjJzNnd4SlZhVXMxcnZmT0dGcHc3dU4xZ296SDdXTWlBaVRrdHExTlRHVSt4dmdySEdDYlVqL2lRTDZzdzNVQzQ3ZElyY1J1SmJqR0lraFJKYnRMZWZiaDBiUTFmQmhVSFYrZEdPUVNRQTlsWlBKTXdSOFVqcGtJc1duekY3dGpCMXc5UlF2MUhQaE1RMWl3bGhqYWFMcHVSUUxqVWJBZXBjQXY4QzRrZ0hCc2g5U2pyR3BjY21Fa0hvNllwcUozNmYyUFZLMTQ2M0lmMUcwRTBGaytheDc4Q0pnenM5eFlhWVFjbWtIMGMwdHNHd1BhOXVtNTNCMXUrbnNqaHMxWmkvNjlzWWZGQXJoMWhBQklpamlHOE9zU0tQeTlBTzJMSE11a1g3ZEZ5SWk3WjBlZ05WRjZVdStBVUVRM3JvUGVWLzUyNXMxRmxRUGhYTWNOcnp0dk1kblZVdWp5Vy84b3NkeVJsZUlLcExoNk45aFFBVDNpTFFvMGF6UW5xZFRNaWNTcHZTeDlab1R0MDE4cVBQeWlEWGJJNnVxKzFhSGI5MElVZXJHUEQvQ1RHRUVKcHhLcExzY0Z3RVVVT1BGa3hqWXpDS2Y3bDZ3UjNwZmZBNGYrNjlVREZLQmlFMldqNmYzcWZXY2ZxTVpmYnJRSHRaM1dKT1giLCJtYWMiOiJmZGI4YzFjNWE3ODZkNWRhMzc2ODBiZmE0ZWEyNGQwNGJlYzU4ZThhZjU2MjJmNGQzMDFiODg5YmUyNjRlOWJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im50cHBSRzhuWUZTeGQ0MzFCazRwUWc9PSIsInZhbHVlIjoiNGFvemxWcEVrajF6eGtKSUUvcCtpTExjQkdDb213UEhWN045WWxUY2Z0Y2grUkJuUDFCMDBFdFA5NWw4WjJrZ3A3RmpvakQrMWErTlJtZnNqTGdqM3VBSW43Nzc2U3dqSjcxRmJQcnYxL29HNGRRclpUMTByS2UxZlpZblZITDM0VDhkdERjbitBUjNRUlNxaGJkeGpqUHQ0V3IxODluN0hwcFkyQlFHOU1GMFB6Q3UrSEs0aUlRN0lkcWI1Qkh2ZXRwbUlRbFBQQUxWTTRqNDFxZXdmenExQitpeWs4dUtENzV1Q1gxeVYwdnd0bzlqSXRDUFBsaFo4RGR1SDYzTysyYW9ubTZiQ0pBSVl2YldzbmNDdnduUkVQalV3OUpRSG5jRFZPWGZiOXJmdkRwMUpPUE5OMHlvaHZRMzBqMC81NHQ3UXhHQXZHZDJWMUVpbHhmTW9lZlp2NHdtamZoQWtuR001b1BCaWtOTllNNVNCdFJGSGpqcExWb0U0WnhXT0VGZWVScmo2QmszdGRRaVNtd21sK29LeFEvV3VzWmUzdzhiSUg0QTdYZ3FHdGhNczhTSzVpUEpKRGNhVjlwbkY5OXVZZEUwaGtScngzWm42cWE3NVhVam8vMGcyUE83VVQ5YUVHcjNrbzc5UmhCRkRqZW1yem95UUQ1L3BUcU0iLCJtYWMiOiJhOGEyN2E1YTVmNDUzMWEyODkzODcwM2JlODk2NWNkNjdjMDNkOWMzMTU4ZDYyNDA3M2Y0NTgzYzg0NWZjOTUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998239643\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-185662416 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185662416\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-551326747 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:58:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImF1bENMSmJhTyt6bjd1N1N2S1J5Znc9PSIsInZhbHVlIjoic0phQVhHc3NPYzJwaFJJbXIvRGxXc3gvb05JZUI3MS9HRlhoVkhDeFhES0hEeUVKbDBzWjZ2eVBRNjhzb0RjbmRaRmhITG1oZmZjdnRRNHFBVmx0MUQzRWJEazN0dWdrajlGeXRrZG1OK3BxTHdSbEhydHo3WEVseTk5VkdjTjlWTTRtNFdYVHczU1I0N0RlZE1leWJWR1ZiWG5wSHF2L1VOQm05SnNGUm9kNFduelJvYTVwclN2ODZQYW8zeGtzNnJvbUh6QURwbmdaVEttZ1JiNEIzQWVGbmFiUklkZWlpVTkxM0NwQVNhN0p4anhGRnJqb0xDMnRud3NTcWpWRDlzYTR4bHQzbDRtZ2VNb2pTSVFWTXVCRmZEaW9GSDNFTnVsKzZ2d2Q0czErSGdCWHRmam5vdmxQYmkwT0JjZktSTFBxeE1rS2V2MDBHbmFLSFA5SW9aZUd2THMyOXpzYkEvK01TR24yMHhPd2VIRGlFd3I5KzloSWRwNW1TSmVNcUlxQjl2bWM1eGE5b254MmxUVEF3NnZHalFtT0pxQlkrWlo4UFJkQm4wUmFsaElaVytUcWVwY0UzQlNnOU1VdGQ4MExBYzZOcUtmaDFQR3FmMWhLZ0VFOSsyWXBNNlNicERwNFhqZHl1OGtxMVZZTjZHNmUxdmVvVGlVMmpoTVIiLCJtYWMiOiI4MDJjYWFmY2JiZWM5OWI0M2Q3NzM2Njk2MmUzZTgxZmVhMTYyYWFjZjg0MDUwNmNhZTFjMDY1MDJhM2NjZmRiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikh2eTFZQXByeDVmcjJjVVBKWDZ4aEE9PSIsInZhbHVlIjoid1FrL1JhcVIyN1EwSkdXMFlZU3FWNHRFdFFEYzVKdmlycVJxWGtGekFBclEzcjBJSTZPOGpNTHQyODZ3QUVEbW9XSXBWSjYzdUpiNlBsdHNScmJWc1VlYW91QlN2K1AzSUNqcXlVamtIQ0t4UzNkb3RvS0tYZS9VVTFYODdMdlE4UE1FZEU3RmRHYXBXMTRzT0pWMmVBT3RFakRraTFYRGkrajhBcXZEZ0ZqWEJXbGlSbmQ3eEFWN2FXWkZGMXN1cCs1MDhnTVBqeHVHOVZZUG5wWnFkTDU5SC9kUjZ1M0IwQ05ocUYrREFsVTdac0tXeUw1QUx2OXFPODhOZHVzOXNtaGQxL05CMlBsLzFXaGJqVlVXSWtqT0x4NGpMdXFnMWZETnVxWU5wdHNFN29YZGlJS3cyU2FiaVlkRDRQdjdYMmRwNEFaZThsbjcwS0cyK3B1QkVzZmhoUGFFNUdIdW1FQnZNcWR4dXV3UDA4VVAzU1JySkJUVVk5UjN1WWxwdU42aFErdmFRY3RkcVZqRHpLR3kzM1RVUUtzaW1QNkNYbnBRZ2EwZXkzZ1o4Wm5CcWFjckRqdVAveGxnUGRUMGlvZERXS2NMbWpFRHZPMW1pYTZrMXJQMTM0Vmh1K2tPMUh0L054Wlgra0J0OGx4K0pnRDB5REplbmlBd3ppemwiLCJtYWMiOiI4MjhkY2M1ZmRlZTVkMTUxZjYyNjgwY2Q2NmFkMjg3OWY0ODc1NmVlODk1NzFmMDk4MzkxZmI3MTZiNTM1NzJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:58:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImF1bENMSmJhTyt6bjd1N1N2S1J5Znc9PSIsInZhbHVlIjoic0phQVhHc3NPYzJwaFJJbXIvRGxXc3gvb05JZUI3MS9HRlhoVkhDeFhES0hEeUVKbDBzWjZ2eVBRNjhzb0RjbmRaRmhITG1oZmZjdnRRNHFBVmx0MUQzRWJEazN0dWdrajlGeXRrZG1OK3BxTHdSbEhydHo3WEVseTk5VkdjTjlWTTRtNFdYVHczU1I0N0RlZE1leWJWR1ZiWG5wSHF2L1VOQm05SnNGUm9kNFduelJvYTVwclN2ODZQYW8zeGtzNnJvbUh6QURwbmdaVEttZ1JiNEIzQWVGbmFiUklkZWlpVTkxM0NwQVNhN0p4anhGRnJqb0xDMnRud3NTcWpWRDlzYTR4bHQzbDRtZ2VNb2pTSVFWTXVCRmZEaW9GSDNFTnVsKzZ2d2Q0czErSGdCWHRmam5vdmxQYmkwT0JjZktSTFBxeE1rS2V2MDBHbmFLSFA5SW9aZUd2THMyOXpzYkEvK01TR24yMHhPd2VIRGlFd3I5KzloSWRwNW1TSmVNcUlxQjl2bWM1eGE5b254MmxUVEF3NnZHalFtT0pxQlkrWlo4UFJkQm4wUmFsaElaVytUcWVwY0UzQlNnOU1VdGQ4MExBYzZOcUtmaDFQR3FmMWhLZ0VFOSsyWXBNNlNicERwNFhqZHl1OGtxMVZZTjZHNmUxdmVvVGlVMmpoTVIiLCJtYWMiOiI4MDJjYWFmY2JiZWM5OWI0M2Q3NzM2Njk2MmUzZTgxZmVhMTYyYWFjZjg0MDUwNmNhZTFjMDY1MDJhM2NjZmRiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikh2eTFZQXByeDVmcjJjVVBKWDZ4aEE9PSIsInZhbHVlIjoid1FrL1JhcVIyN1EwSkdXMFlZU3FWNHRFdFFEYzVKdmlycVJxWGtGekFBclEzcjBJSTZPOGpNTHQyODZ3QUVEbW9XSXBWSjYzdUpiNlBsdHNScmJWc1VlYW91QlN2K1AzSUNqcXlVamtIQ0t4UzNkb3RvS0tYZS9VVTFYODdMdlE4UE1FZEU3RmRHYXBXMTRzT0pWMmVBT3RFakRraTFYRGkrajhBcXZEZ0ZqWEJXbGlSbmQ3eEFWN2FXWkZGMXN1cCs1MDhnTVBqeHVHOVZZUG5wWnFkTDU5SC9kUjZ1M0IwQ05ocUYrREFsVTdac0tXeUw1QUx2OXFPODhOZHVzOXNtaGQxL05CMlBsLzFXaGJqVlVXSWtqT0x4NGpMdXFnMWZETnVxWU5wdHNFN29YZGlJS3cyU2FiaVlkRDRQdjdYMmRwNEFaZThsbjcwS0cyK3B1QkVzZmhoUGFFNUdIdW1FQnZNcWR4dXV3UDA4VVAzU1JySkJUVVk5UjN1WWxwdU42aFErdmFRY3RkcVZqRHpLR3kzM1RVUUtzaW1QNkNYbnBRZ2EwZXkzZ1o4Wm5CcWFjckRqdVAveGxnUGRUMGlvZERXS2NMbWpFRHZPMW1pYTZrMXJQMTM0Vmh1K2tPMUh0L054Wlgra0J0OGx4K0pnRDB5REplbmlBd3ppemwiLCJtYWMiOiI4MjhkY2M1ZmRlZTVkMTUxZjYyNjgwY2Q2NmFkMjg3OWY0ODc1NmVlODk1NzFmMDk4MzkxZmI3MTZiNTM1NzJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:58:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551326747\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1272268991 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272268991\", {\"maxDepth\":0})</script>\n"}}