{"__meta": {"id": "X412d700217cd0e9d610d40ec40589358", "datetime": "2025-06-08 13:44:12", "utime": **********.370555, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390251.105888, "end": **********.370585, "duration": 1.2646970748901367, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1749390251.105888, "relative_start": 0, "end": **********.247562, "relative_end": **********.247562, "duration": 1.1416740417480469, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.247583, "relative_start": 1.1416950225830078, "end": **********.370588, "relative_end": 3.0994415283203125e-06, "duration": 0.12300515174865723, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43918496, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01693, "accumulated_duration_str": "16.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.325595, "duration": 0.01548, "duration_str": "15.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.435}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.352123, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 91.435, "width_percent": 8.565}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1107675255 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1107675255\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-803219515 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803219515\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1331666573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1331666573\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1165005281 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749389696578%7C34%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhCSWQ4amJ3eWprZ05QSWtQTWVjMWc9PSIsInZhbHVlIjoiWDhKaXd5QmdxaXpqVEFCTUdFWjFXb0dGWHRNUmRPbnZkbW9oZ3VVdEhBRmo1TUZDcnhxcFBsUGR3SXZ2RUJOam1wbVhlcUtnRGZLeTA5NEVuZFlmU0hQUm16bzd2M1VLVE5XU1FERUJnVG4wV1B6YkI2cURCQWJzdjY1T1pybElMWVlpcDR4UW5xZmM5TjhzamMvbFd4MUxVd0FHZWNJOGh4RWQ5REVkVUNWUWtVL3dQczdETi9vZ0k4RnRza1Aydlh0WlVFblg3WjBicjZqbnpGWU9vY2ZyeVVObVcxamZObE1hNmNBLzVDL2JnVWxaV29mQnVvSnNsTEhsRHlzK1BkZ3J1K3lHRWN2ZEhoc3ZDZkQ0c0traTNSYXNuTzJSNW5WYk9jV2FYcEF5N2RHQm0wb0R1SXQvWmdvOHltWkNWeloyR25YeldwTjhNMVd4eG1LSkRoanVnV3N6SUlWYjc1Tko5TFpoKzk0a3Rkb0JPWitUY0toalArT09JUCtUWGhiUjI1WTRnSUd3aWJjUGRJaU1HVzhNQklZa2hScEFmcUswYWE2Y29zcW44OWpSRDUycmI4VFFxSG5LYUJUdTBqMUxYQVFLVGlJSmRxM0N2WmlEUXBIRS9QQjE1aEIwM1BDdnVCZGFJc3FxZlQvYnl2UkNkaFU0T0NjUzVtV3kiLCJtYWMiOiIyZGY1YmY1ZWUyOTgxOWJhZmE0ZDdiMGNhYmU2MzM5ODQyN2UwNDRkNmVmZTQzOWZhNzAwM2U2MTEzY2RkYzgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlNWTZ3QjNKd0pFdWFLRWs0VUxYU2c9PSIsInZhbHVlIjoiSXgxMGdQdjd6L2Z1aXQraFdsOHVJbEVzMko5L3dzSnNUeVpkQUVwZm9vQVVVMTgvSUs2VlpiVnlxdENSSjIyOVVoR1hHYUdCcWdCQUhRMmZFRUxFQWw0d3A0dllHS0QxczdUTm5JdWZVL0tUdU5nUG9JaGduUDRnRktZUWQrYTlLRUVzcEdnWE1pZWRaSVlWeGxoL1hMM3FRYVVFQ1ZIL0FyMFAzbm1LdGJmL01sZkc3YmJ2cjhlVDJWcUR6Smg5Q2xnYzlPMVVjbk5yV0MyZkE1OEVRLzh3YVVOek5KbU4xZXA2S3NDY0gwZVowWFRCd3BXUmdlZ2kxaWE2eGo3RXk2QjVITkloQ2N1bmRCNlQvVm5STWt4MXV1WG12Tm42VHV4VGJsU000OTJMTkpoQU9CT1pyWE1ncjRxSVI4azNtcldaNUplemNqVDFLL1d3NFZvNkZxQnFGR01yWWF5eVFsMUFMRU9jTzNPVnJlKy9zQWI3azFDRVRNOGxEdEo1QlJ5UXJWSnNXTnFsYUluUzZzNUp5L3VUaVdiUmZYK1duRlhMa0YyVEEzWSt4ak1mOGZUdmg2OXU3QXRhY0RXSlVGUFlnMzY5eHA3elk3NjBKM1EwL3BSdVY1bmI0aFIxcUt2QTFlRWZXUXllSWgyWllOMWNxT0pNUHhGQzQ3ckUiLCJtYWMiOiIyMjNmMzcyNTM1MWRlNWI5MTVmNDRjYzAzNDI0Mzc3YzU3ZjhhNTM2NDZiMmUzOGJlZGFiMGFkMjliMjA5YWU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165005281\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1216821017 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216821017\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-758143455 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikp4cTVZN0MzVHVaWHlDNzNkSkRCRmc9PSIsInZhbHVlIjoiT3pES1kwa3h4bnJlTWZLdEE3Z3plMEtNL2xYLy9EVUQ5MzZ3RTAzbjRsOHNlTm1oNmdnRlV0UlVjRzI2MHdKT3oxQjV6UFU0cDBvTDdYcjdlSmV6R3cvY2NmcnFiWkRIcEVTWThENnVEM3c4cmZJcmowR1JzZ1pLYWp4WjZQRkh0UUFucUJwRWxDQXJhWDZHMGIyWXdjMVZaRDYvSDF5OHZ1T1orKzFtWmJmaG1heHJmeE5DbFE1SmluNUp5bTRmSWRQa0VKRTBEVGtIbGxmMzNkdXNJcUNWOGIxS3ZuTnAzenJTTU14cHc4SGpQMDhUbkx6ZHBaL1N5dzlmbDlvdktpV2FkUHg4cXNaZmpIWjVSSmthbDJkTjZTR25EWnV0VWxrR2IrVUpacHRxTm9DNW9Fb2pPcGtZaEtZTnVXUm5CZzFSZEszS0JjS3ZqNkhSQnBoR09Vall3dWtSc2E4MmJlWnRMZjFMVzk5SkpZNkt5TDd1QkRzdXhpVWZEbEVGU29oNlBMSitMbHBVK1JRMVpuQytiQnFGdHd4NFFIajN5SGM2UWY4a3BIL21sWGFWU1dVc3Jqbmd4V3ZjLzVCTTIzcDNaN1NOSjBhRENvTVE1T2hWYi9mcmx2WTVkcUFNSXRuc2hTT3E4OHNxWFFnWTNkK0V4eEtKWDVDRlkyMDEiLCJtYWMiOiIzODA5YTRkNDVlOTcxY2U4MDlkYTQ3Zjk0Mzk3YmY1NjFiYTI5ZGNlMjhmZTExYmI0MTNiYzQ4ZDUyYTYwOTA5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImI5dU5QZ3p6L1VuVk5wRVNDTERWRHc9PSIsInZhbHVlIjoid3p5ckxzbi9Gd2tsT1gwNzBEb0NCWi9yQi94YktLVW5XTXV1UHY1ZVJrL25NTE40czdFNFRWSVF3bUlJNGJYeEUva3RxTFE4ck8rOXY3TTV1OXF3V2djL1FyVldpNGwydHk1ZlR5YlRibVlmL0Q0b0t3RktXLzVwcTR0UUVUUlNkdGpoL3g3aXJBQWpIU3VObzVzM2J0SXJoaEc1YlBUbnhEWmdNb201UGJMWTJKUG84Zms5TFRaN0w0a1VYZUpkdXQxaWJFZXZEZUNuRGZJR0Nla2Z4Smw2cEFGdUtvMkM1WmJEQlliYTRpTks1NE5FMHFjVUFUcWcycWlOUm1rVHZvOUMwclk4NFJyVVBKcDFoNGpkV1BlUmhZV0ZQbkZhR0lBZVR0OWEvVCt2bkpBcXVJa05kUlR6MnFNTHRKTUxjemhtWGM4a3o1NmxCNnNibyswM3FaQzlXYVlTVjJnWmRzclRvMzhJZ0hsWkZRV0xjeHRZY1RLMVpSekw0VzZ4bFl3YmJUT21LS0xOejZ3NWsrRjA4WkF5b2RvQWc5ZVQwSDBYTWk3UVBUam55RloyVFVMUGF3NW5RYmpnNFRrVnhEME5zb3g3K25lWEJmSzQ5eURwcVJmRks1akkyd1ZyQjZ4amNSSGsyMlBSUXRFT3RTTkdSQ244Q2lIc3c4UjMiLCJtYWMiOiJjNDYxYzJhYzc5NTMzNTA2M2YzOTgzZTA4NGQ5MjE5NjllY2M3ZmIzNmQyNzgyMDQxMDQwNGJhNzVjZjIxMDEzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikp4cTVZN0MzVHVaWHlDNzNkSkRCRmc9PSIsInZhbHVlIjoiT3pES1kwa3h4bnJlTWZLdEE3Z3plMEtNL2xYLy9EVUQ5MzZ3RTAzbjRsOHNlTm1oNmdnRlV0UlVjRzI2MHdKT3oxQjV6UFU0cDBvTDdYcjdlSmV6R3cvY2NmcnFiWkRIcEVTWThENnVEM3c4cmZJcmowR1JzZ1pLYWp4WjZQRkh0UUFucUJwRWxDQXJhWDZHMGIyWXdjMVZaRDYvSDF5OHZ1T1orKzFtWmJmaG1heHJmeE5DbFE1SmluNUp5bTRmSWRQa0VKRTBEVGtIbGxmMzNkdXNJcUNWOGIxS3ZuTnAzenJTTU14cHc4SGpQMDhUbkx6ZHBaL1N5dzlmbDlvdktpV2FkUHg4cXNaZmpIWjVSSmthbDJkTjZTR25EWnV0VWxrR2IrVUpacHRxTm9DNW9Fb2pPcGtZaEtZTnVXUm5CZzFSZEszS0JjS3ZqNkhSQnBoR09Vall3dWtSc2E4MmJlWnRMZjFMVzk5SkpZNkt5TDd1QkRzdXhpVWZEbEVGU29oNlBMSitMbHBVK1JRMVpuQytiQnFGdHd4NFFIajN5SGM2UWY4a3BIL21sWGFWU1dVc3Jqbmd4V3ZjLzVCTTIzcDNaN1NOSjBhRENvTVE1T2hWYi9mcmx2WTVkcUFNSXRuc2hTT3E4OHNxWFFnWTNkK0V4eEtKWDVDRlkyMDEiLCJtYWMiOiIzODA5YTRkNDVlOTcxY2U4MDlkYTQ3Zjk0Mzk3YmY1NjFiYTI5ZGNlMjhmZTExYmI0MTNiYzQ4ZDUyYTYwOTA5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImI5dU5QZ3p6L1VuVk5wRVNDTERWRHc9PSIsInZhbHVlIjoid3p5ckxzbi9Gd2tsT1gwNzBEb0NCWi9yQi94YktLVW5XTXV1UHY1ZVJrL25NTE40czdFNFRWSVF3bUlJNGJYeEUva3RxTFE4ck8rOXY3TTV1OXF3V2djL1FyVldpNGwydHk1ZlR5YlRibVlmL0Q0b0t3RktXLzVwcTR0UUVUUlNkdGpoL3g3aXJBQWpIU3VObzVzM2J0SXJoaEc1YlBUbnhEWmdNb201UGJMWTJKUG84Zms5TFRaN0w0a1VYZUpkdXQxaWJFZXZEZUNuRGZJR0Nla2Z4Smw2cEFGdUtvMkM1WmJEQlliYTRpTks1NE5FMHFjVUFUcWcycWlOUm1rVHZvOUMwclk4NFJyVVBKcDFoNGpkV1BlUmhZV0ZQbkZhR0lBZVR0OWEvVCt2bkpBcXVJa05kUlR6MnFNTHRKTUxjemhtWGM4a3o1NmxCNnNibyswM3FaQzlXYVlTVjJnWmRzclRvMzhJZ0hsWkZRV0xjeHRZY1RLMVpSekw0VzZ4bFl3YmJUT21LS0xOejZ3NWsrRjA4WkF5b2RvQWc5ZVQwSDBYTWk3UVBUam55RloyVFVMUGF3NW5RYmpnNFRrVnhEME5zb3g3K25lWEJmSzQ5eURwcVJmRks1akkyd1ZyQjZ4amNSSGsyMlBSUXRFT3RTTkdSQ244Q2lIc3c4UjMiLCJtYWMiOiJjNDYxYzJhYzc5NTMzNTA2M2YzOTgzZTA4NGQ5MjE5NjllY2M3ZmIzNmQyNzgyMDQxMDQwNGJhNzVjZjIxMDEzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-758143455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-495040521 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495040521\", {\"maxDepth\":0})</script>\n"}}