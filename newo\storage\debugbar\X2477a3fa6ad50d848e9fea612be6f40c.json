{"__meta": {"id": "X2477a3fa6ad50d848e9fea612be6f40c", "datetime": "2025-06-08 12:56:23", "utime": **********.556859, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749387382.109114, "end": **********.556902, "duration": 1.4477880001068115, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749387382.109114, "relative_start": 0, "end": **********.40882, "relative_end": **********.40882, "duration": 1.299705982208252, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.408844, "relative_start": 1.2997300624847412, "end": **********.556908, "relative_end": 5.9604644775390625e-06, "duration": 0.14806389808654785, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45053640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00748, "accumulated_duration_str": "7.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.487384, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.588}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.516603, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.588, "width_percent": 13.369}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.533268, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.957, "width_percent": 16.043}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2143414105 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2143414105\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-307990593 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-307990593\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1281277247 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281277247\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=rahd9m%7C1749387360042%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkI5UDN1N2dvM1oveklqVUZNdXFKOHc9PSIsInZhbHVlIjoiRVc2WHl0Mms4bkVnZXlLQWd5TVYyOTcyZXVIcitHYklBRWJjLy9SSzFhcTl1czZwcDNVRE9heU5PU0tGcnEzaUEyaGZYT1VHczhOTHp0QmkwcW5URnY5ZFdJelN4Ujc2Vk9OVkdkNE96eUthNTBTenoxaUtQR3AwOWM2dlVpWHZjRmk1UEJsZnVQaXlSSTB1T3krQ0VoT09MSmdzSWFXYmVrRkFpVzE4WS9yaDBMcG03WCtmaHAyWXVUMVR1SnM1aWpZMVVPeEpsTU5LZ0k4K3J4QjdZKzF3aG5XR2owWjN6SHNBbGp0SFdRT0NmVSttbGNNR0o3WWhMeTFHMjRSUjhkeGpEREFWYkpSNmRDOW9hR1hOWnN6a3hNM09WOTV0d3Q5NzRXOVZ2Ykk1RERWMnVBVVJzUmRRamZDVENtclNDenhiUVcwdFFSWHYyOUlaenMwdXhBWGlYYjlVMGxIem5xODJ6VGVXL1Z0MkZCaWZLSC9DUFdOMW9WR1VvU2JreS9rT0VsNEFDUytCUWxzMGtJbkN5cFNnWGFPUHFDcTVrNFVHb0VCN3huK1FLbVNLQXllTDMzQldEdjM0bjkyeWJYYUdabitZTzJYTXpqV1EvWW1HbXY5bmlHY2JWMXJEK2ZHTUVhRm1rajBlaWNvb3FEOFcyQThPcnAxQk14anAiLCJtYWMiOiIwZjZmNDA0OTJkYjg4ZmFjNGIwMmVlMGY1NjM2Njg0MGQ4ZTY4ZDMzZDQ1YzZhYWQxM2ZiMDc1ZmY4MmRkYzEzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFUcFBkeWxxKzJhckluNmUzZzJ1UkE9PSIsInZhbHVlIjoiZUVJWDdoVDZqazR0KzM0cURTYndvTEdXQ2kwVlVLRDFEQW93ZTNCVEV5MXVBa3NmcmNpYW9OVnA1cGNGRCt5SUlVZ3RYT3JXNkVpeEpJc1BkclJXOWZRdkVEY0R5c2QxalZkVWhrWndiV0I5NURmVVI2Q2FOMGw2TGtIT2ZCck1jRC9naDR3VEY3SUVucG1UbnpueDZmaUlRUlQyNklqbndSOEIrM3R1TXNBWGJDWXB6a1JnVWYxNUNScjFtZ3I0ZjVreUhqWlJDcFZjUTl5cVRWQ1BQMDlkZFZaZ1V5Y3dKUHBWZUdRLzFMaG1GS0g4ZDNNa1M0RjZuRnU4ZllUYkR1RE9wUXcxbGwyaGFIRTMvY2R5VVprVjR4WVJyY29EakVmS3EvRVE2amkzaDFYNGVRTGtuSkVISlNSZzRhQzNDSUExRlNrRGlvcnNUMXQ2UGpNUis1RXRmTkxwRVlQMXhTTjVadVZ3ZVdaTGdZNlp5VVdQa1BsRzNqQlBjdllWM0VDeElqNUh6TnVFeTNWbXRxdWtMTWl3QjdzVVRJY0RPNjc5cmVOUUQ0QUFjK0thd3paYlE3K2dyQ1NSMHBJU01wUmFkaFVFekdsckFESmtpLzNjdTZLc0YvSTU1cTZLMERUSm1RM0c5RkJEUnUyY2UrVHJtUXBGVnc2NnNRdzMiLCJtYWMiOiJlZTIxMGQxMzVjZWIxN2Y0ZGVkMjMzY2E3NTIzNTliNDI4MTYxNWMwN2JiYmVlMjljYTQyMDBmYzM4MTMwNGYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1650546631 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 12:56:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikh6S1B3eW52ZEN6RThDZXFkMExnK3c9PSIsInZhbHVlIjoibG1nd1Y5UTk1Y0JmK2YxOFptdFpwRXJTQzV1Rk1SWS9ZM3BYNStqL0Q0K0lvOHcyRkF1K28yQU9Lb2hpVWJNNFQ4bTlpUTczVHJNcjBOdDR2dVJ4dWJEdnRRdHBIcU1VVDVCcEkrRlhjWUd4cEphUmMxUDZzendhUWJMZVRHTSt3R3FpR1Jxd0FlUFVmT2ppMmRMVEVtR3puTjBBSWNvQ0REV2R2KzZiUHhuNEpvMktSMUtjSUxYcHNucUZ3OG1EM1Z0Wm8wUHFQVXFtQXZQa0NPT2JUc2k1SVhSNW40bFJUNWJob1pjeFVGdThndlVxaW5zZW9neGZKcTY1SW5ObS82aW1RMjczYjJPdnUydStqdlJGVlBHYkNYOFZjc05PMkFpWFNVUHVwQ0t5S1FnN0U0M3hhMXhvOFRzbEpBT0c1Tm1XL1pSd2sxVDJXWVdkNHNVU0VvWGIxcVA0a1hzVlRrc1lQMkVCMjRQT0hOZ2JpSXFkZ2gydFg5RlFoajNMRTgwNElBNjBaeHRobGEvTm5ucmZTMGNlSERSeDRhWXh5c2RjdUVTcmxSY3dodG95bXZqSGV3RU1rR0UxUjcwNG1RazQzTlNBaWdCOE9haGRsVmxEeDRxMG9pczVtb0pkK0hMR0x6M0YwZFVzTHEvaDlaclZQVjhjNmdPeDhWNUsiLCJtYWMiOiIxOTk4Nzk0OTIyNWZlYjkzOWI2NTI1ZWRmMzdhYzkxNjcwYmI5NjYzMWE3NmIzODQyNjU0ZDI2MzJkNDU1ZWE3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5NQXljRlZjeGJraDVQRlFSaTVFT1E9PSIsInZhbHVlIjoiVTdvU3NpUWtYQWF1cWx0cFNUYzRVVExvTzJqQ0RHb3ZUUTJJaitjcFV5emdVRG9QdVFwaDFURjNrR2hnTnZrNlg4TjVWazZPMXdvRktiSmVjbGMxT3hPcTAvUVp2aGFHc0xFVk1pRWRrblZzc1R6NlFCS3YvdUZDcXNINFlnSXExa0E3ZXBmcFU2ZzRkRm1NZ3NBQ2FFaXlBRVk1aE9pbUovSlFZdkNtY3pCMG12SlZpUDFmWk5WL3gxcEFjZzNOMXVzQlRVZkVWbnRsWWlJWlpsVG5uckFkU2ZKdXM4czd2bDJJUFRkeUZHSStpZlM5VnlrdGNjcnp1ejdrbTJXeVFOZFBFUmkzSzFnZmVhM1hrcE1HcHV0S05LRkJHWS8zZmdSWGZxcGhIWFZ2ekZCWU5HUUlneWk3c0swSmpTcURWS2ZnYy9GSEU0VVNzeEtpK3djR3lKM05kc3RGcGw2YzN1NW1rRERldG83UTFVZ2lvekJMV0xFeE1GTXErdWJka2dpUDh0THd5L2MrRFZsMnduMVFFNFNTTks5Uy9tb2RqSWNjTUhFUTlENHZiOVM2eVVxL1BXcmVFMVQwV2V5a1JMTXpmVTVnKzA5cDJDVmM3TWJLL1gwQS9SRGZKZGUwU1NOcU82R0drK0NYdG5lREtrOWZtbXFOb250UnlXbHkiLCJtYWMiOiJlNjAxMjBkNzA3MDM1NWYwYmI0ZjdkMmNiZGUzZGY5YzdiZDFlZjZlMGFlZWYzNDdiMmI5ZjU4YjlmMWE1NDBkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 14:56:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikh6S1B3eW52ZEN6RThDZXFkMExnK3c9PSIsInZhbHVlIjoibG1nd1Y5UTk1Y0JmK2YxOFptdFpwRXJTQzV1Rk1SWS9ZM3BYNStqL0Q0K0lvOHcyRkF1K28yQU9Lb2hpVWJNNFQ4bTlpUTczVHJNcjBOdDR2dVJ4dWJEdnRRdHBIcU1VVDVCcEkrRlhjWUd4cEphUmMxUDZzendhUWJMZVRHTSt3R3FpR1Jxd0FlUFVmT2ppMmRMVEVtR3puTjBBSWNvQ0REV2R2KzZiUHhuNEpvMktSMUtjSUxYcHNucUZ3OG1EM1Z0Wm8wUHFQVXFtQXZQa0NPT2JUc2k1SVhSNW40bFJUNWJob1pjeFVGdThndlVxaW5zZW9neGZKcTY1SW5ObS82aW1RMjczYjJPdnUydStqdlJGVlBHYkNYOFZjc05PMkFpWFNVUHVwQ0t5S1FnN0U0M3hhMXhvOFRzbEpBT0c1Tm1XL1pSd2sxVDJXWVdkNHNVU0VvWGIxcVA0a1hzVlRrc1lQMkVCMjRQT0hOZ2JpSXFkZ2gydFg5RlFoajNMRTgwNElBNjBaeHRobGEvTm5ucmZTMGNlSERSeDRhWXh5c2RjdUVTcmxSY3dodG95bXZqSGV3RU1rR0UxUjcwNG1RazQzTlNBaWdCOE9haGRsVmxEeDRxMG9pczVtb0pkK0hMR0x6M0YwZFVzTHEvaDlaclZQVjhjNmdPeDhWNUsiLCJtYWMiOiIxOTk4Nzk0OTIyNWZlYjkzOWI2NTI1ZWRmMzdhYzkxNjcwYmI5NjYzMWE3NmIzODQyNjU0ZDI2MzJkNDU1ZWE3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5NQXljRlZjeGJraDVQRlFSaTVFT1E9PSIsInZhbHVlIjoiVTdvU3NpUWtYQWF1cWx0cFNUYzRVVExvTzJqQ0RHb3ZUUTJJaitjcFV5emdVRG9QdVFwaDFURjNrR2hnTnZrNlg4TjVWazZPMXdvRktiSmVjbGMxT3hPcTAvUVp2aGFHc0xFVk1pRWRrblZzc1R6NlFCS3YvdUZDcXNINFlnSXExa0E3ZXBmcFU2ZzRkRm1NZ3NBQ2FFaXlBRVk1aE9pbUovSlFZdkNtY3pCMG12SlZpUDFmWk5WL3gxcEFjZzNOMXVzQlRVZkVWbnRsWWlJWlpsVG5uckFkU2ZKdXM4czd2bDJJUFRkeUZHSStpZlM5VnlrdGNjcnp1ejdrbTJXeVFOZFBFUmkzSzFnZmVhM1hrcE1HcHV0S05LRkJHWS8zZmdSWGZxcGhIWFZ2ekZCWU5HUUlneWk3c0swSmpTcURWS2ZnYy9GSEU0VVNzeEtpK3djR3lKM05kc3RGcGw2YzN1NW1rRERldG83UTFVZ2lvekJMV0xFeE1GTXErdWJka2dpUDh0THd5L2MrRFZsMnduMVFFNFNTTks5Uy9tb2RqSWNjTUhFUTlENHZiOVM2eVVxL1BXcmVFMVQwV2V5a1JMTXpmVTVnKzA5cDJDVmM3TWJLL1gwQS9SRGZKZGUwU1NOcU82R0drK0NYdG5lREtrOWZtbXFOb250UnlXbHkiLCJtYWMiOiJlNjAxMjBkNzA3MDM1NWYwYmI0ZjdkMmNiZGUzZGY5YzdiZDFlZjZlMGFlZWYzNDdiMmI5ZjU4YjlmMWE1NDBkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 14:56:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650546631\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-293965676 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293965676\", {\"maxDepth\":0})</script>\n"}}