{"__meta": {"id": "Xc977d8fc434aa0fe892674f8384f9bb9", "datetime": "2025-06-08 13:44:51", "utime": **********.376539, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749390290.045466, "end": **********.37657, "duration": 1.331104040145874, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749390290.045466, "relative_start": 0, "end": **********.102328, "relative_end": **********.102328, "duration": 1.0568621158599854, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.102357, "relative_start": 1.0568909645080566, "end": **********.376574, "relative_end": 4.0531158447265625e-06, "duration": 0.2742171287536621, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48131096, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02979, "accumulated_duration_str": "29.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.228973, "duration": 0.0237, "duration_str": "23.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.557}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.276531, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.557, "width_percent": 4.196}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.325379, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 83.753, "width_percent": 3.793}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.331598, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.546, "width_percent": 3.323}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3460498, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 90.869, "width_percent": 4.397}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.354923, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.267, "width_percent": 4.733}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-995684737 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995684737\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.343104, "xdebug_link": null}]}, "session": {"_token": "xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-769447523 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-769447523\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1954804118 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1954804118\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-252217608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-252217608\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1954518021 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6InFudFZyVTdrcWVleTdONGVZcUVPYmc9PSIsInZhbHVlIjoiNmxmdTFKQVZaaDFCREp1eTAxaDdqNVMzTkJHdWNuNEtDcGlyd1BCUXB0RVYzaWdoc0xzTFVlSE5aWWFYYmVsTVB0VUhzZlN0M1MwR2xhZVd4dUI3ZTVEaG1xWVVjLzVTSzA4SUVYZHFTUEZxUnBLK0pKY2NCdlllbVhZbHFyYUtZNFJOd1phcmt1Mzg0RWw0c29XMnBZNk82VGlEaWNaN3JvN0RReEJDZDZHcFJhTEZlK1VLWlRpL3hvRFFGMU1Ld3lkOXFueDRhdHphVjF6ZTNSNWlRQkNYNGlPaiszM1lGeXhZNlZaYXg1emhPNnppSHRZWjhXMnhOc0lWY21jenp4cTRhM251bXJnL2RMNXJ5Yjd5c2M4bUt2OXBtQ20zSWZnNG14RE9uM3hMSTNOdE04cWdpL0NBM0Z1eDVqa3I3U2l5N3IzV2pvWnI4YjZ6VENYTmVlallLTEhzVS9wdkhoektaTmowZGFJUzVUaVkxTmEvbVVtVlFsQWJuZGVQTW9RMEtVODgyL2tTQjNxZmEzZmJNOFlwRHJzakJqZHFKY0VMQVN1OWtWMm9jNmVyNU40OGs5TmlCcWhVSWE2TGZNR0UyVE1rT1l6cDdzcUhXT0hhTmR4dEIrTzg0MWhtOHFSeTFFVzE4QW1ScUFMNUF5Rm9XZFBNQXpWaEdpRjAiLCJtYWMiOiJkMGMyNWY0MWZkM2Y0YjNhNGUzNzhkN2VhMjYzYzcyMTMwODVmZTgwZDZmMWIxN2U0OTU2ZTk5NTBlYzAyNGEwIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749390282451%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRTckV3WXUyV3VsY3pSVWQ1TDZlV0E9PSIsInZhbHVlIjoiK01sR2FFQWJ4ck1xd1pvNHpqNWFjYTUvQW9EbE9Zb05YODhQYStCcm5CM1FRRCtON0VhZnJ0bDlxZ0hkTFpuS29oejhINmU5OHF4Zm03a1krMU14ZXdmOXcrdVduaUdKWmZFdE5XME5YNFVmcFFsNG9hN0dlbzdiREdxVGpvcVRjb1FWU0VYZVcrSVgxcGhWK2w3RmhIRnFLcXd3MWozWGU2a2NpZjFFUnkwYUF4N1V3QjVsRTNBeGIxRVBQRFFVbDZ1eEdaSzNJcEM5M1hLWlNMdG0vbmVxZW02YS8zMjIxNll5ZUhESWF2aGlzZUtsY0R0Z1BjMlJaQlhQbjhqTmhzc2ZnZVBzRXhuQXpaMGlsaHlZUENIQXZzT05yanlZd2FxaUFwMENObXdtQzdUMkRWRm9GeU5mNlQ3K0RMV3FBc2FvWmxXRE9CTDAyZzA3TGZsMk5zUUFsT3F3YWpmUTROcGtYT2pOY3FXQUF4TTNJWjF1VU1idzBxK01xYjlrbDVXWFVrUDZ2VnIzYnZhVTJUSXRCOGJaUXR0ak9oYmN3emExZHkxRTJiL2N3TnoyczZ4NllGUUtsajFWaEt2K3FDTHI0S1lxQTJORU5WcDAzdkRSeWdqZ1Z4VXFuMTI0ZU0waHh1KzhqZlYwNHBnYW5wUzNpMXhNbVFEZHBIUEsiLCJtYWMiOiJkYzdjODgyNWRjODQ0MzZlNGQ2OThjOGM2ZGM1OTNiNTI4MTgwZTczYjg1MzEyZDc2YzljMTdjMjFjMjFkMTZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlRpdmdFN1dua1lvL3c4YXU0LzN1R0E9PSIsInZhbHVlIjoicngvbVR4NU5pWUZ3NlNyVzd6aUtNN0h2cm8xTStRUTRweUdoRWptZzF4M05seXBpWUhxeE9KSldnREFFMUlGdVBKRjdKeUtwSjdXS2VtQzYwTk5TVWxkdlZycUdGVWN3ZVROOE4rTENDSHpoamVtTDQ0anRsSklRWXRVZXUyOVhJUHo2WnNjakF3OFF0OU5XblUrdHJ0OHFKTkxuSlhyRzhOSTVZNmd2dWdlZG9JdDhJUGl0eFN6Y09XU2VDWU1oYmVQMzg3YTJPZlpNSnNhK1lkOXk4dU44WGdieUZHdlVYNlZJTUtxVE12ZUJpSTNMQ3g1amZZUkswai9RVC8yL1BVSE55eXhidkdUSit2WGI1WUI4N3dkTWYvWERob2JUTG52dXpNZkI1VXE1VXBoOWVzVzdqQnc1c05VaUg5c1lnVU0wc1pvc2poaFVPN1gyWE9FZjF4RjVlU1cvUWJ4WFZGWTBqOGZFei95T0kzYW1FM1FuQXh0aSs2Mk1oM241dkpIMExEYmt5aHBsN1NIT0dxZXh2WHZzeE9SNmh1azNyOWduRUpKOXl4OFhZMkxsVDlrU0plYm4vZktQQzhLalQ0UEFzY2s5eVBsV25UMjMvVlV6Y3RIcXJISnhDd29uSTZ0cS9HV3NXREV2RmErUVdxM0ZPZzNWOC9jVEZIUUsiLCJtYWMiOiIwYTA0MmYxOWYzYWZhMjA1OTdmN2U0YmI4Yjk5ZDE3MGJlMmM5ZjcwMjVhNzkyNzJkZDEyYTY5YzQwYzk1ZThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954518021\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1550380802 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550380802\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1804199216 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 13:44:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFic2dNaGwzWDNHVnNSc21vOTJiMlE9PSIsInZhbHVlIjoicjVTWG1FMEpnUzVObCtLQlkxYldvT1Zod3ppckhJYkp6ajFZYWJNYU5YRFlMWllYTWl3T2dSeldmVjkzUlVBUEJUbmNUTVV6L1BxekxCWnEzK1J5Z3NaWmdjWkpRMFpLSGJEMjVDbkdQOHUyVW80YmVNejk0c1N0NTExSmJ1MSswVjNWTEdLdVUvQVVMYkY5TWg5R2NYSkJ1TGVmakZiQkJETGpmNWdLRlN3WEljdk5MQ0lHckNlNVlVWkYwNmwwU2tTYmdkNzZMcS9TU2h0Z3lhbFhDSmx5SlNwOG1UeldVUHRtbTFxMllxNjF6Y2tjS28rQ01FTmtuMXVxVXc3SXZCdHdQUnJDSVVxaTNNMEEyWkZ5WTN4Q0dnayt0bTJWdXVUR29uUE5kWkZZL3dLQmF0WE1lNHFoVmJwYUZQZjNRRWN6WkpXUllHRktkcklTN2szREdhWHM3anRqVzhiVkRpQ3VrYlp0UlQrZVBRRGhmSEw0NnBsTG1zNlVZZFFXSmk1MjZIL29GejlTa1Z1RmxwYkdkZkYwSnFFb1Z6d3BwSzJPSHZFSTVjakFjU1ZpejlrTGxIa1B2aGtUb3J5aDJ5U2ZsQlRCekpCYVMzdnlJblB1bGN5Zk5abTZCSjVzeGZoVUl6N29Mcndxd0VLZHEyQmU0ZzhnMmtYQ3BNTmIiLCJtYWMiOiJhODI0MTZhNWY0YTRkYzdlMjQ5ZTQ4NTFmNzgwYzBlN2M1OTE0OGY1Y2I1NjQ5ZmQ4ZDZmZjNhYmU1MzFiNTU3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkttcG1CUndiMDNZWnl6dGtQR0VtSXc9PSIsInZhbHVlIjoiU0lwOVVqQXNLVlJ5bW5BMUp6bnQ5eXhRQSs4N0g2YUFIb1ozSDFPekt3SFkwV3FPNG43QXRJM2xaQVR1cUNibWFQcTVVU1AvYkEyWmkrOTAwR3JtKzRmd21aMkViRXIwOENDYXp2RUtKeldSdFVUcnZZMkRHY1l0MEdoMlpSUFZ4aityUU1ZTkNkc2liZjd4NHJhWDFmQU9VemRWZysreVgvVmxzQ3VmaDF2R0NrQmNnd0hjdkRDeXJ1UEpPL2ZZYVE1OE55N0RqMVdENWN1d0ZVdEgzNUY4UlJNMUJ5eTVHY0hpRmZ4RHNZejlyV0J5TEs5RFloUTFzVkZxdWJGNzVWVmFmTXNJNm5uekRDZUpGU0J0MUk2T1VFRHlTcHVNaTE0QUN0N2tOaHJUbDk1WkRYWVdlNElmRm9zeUttOVhUQXFvZXREZXpKS3FBUGY4VU1mMWJ5T1dxY3pSUnIrcXZ6dnVZcFFpbFc1Wlhydkl5OGRYV2UwVXV3aENBU2trTHlNc2l0WE5qSmE4SDdGblJnVlIyYjhyMVpEUnpzeVplZTJtMmJ3eWlqM3RVWHdLOHowQjhFM2hZNnIwSnJRUjA5c1F6MDNjekhMVEY3MVF0c01zU1BDcUtaNHhCRWorcEVhekwzeEJVR2I2VlBwT29jUnJ2RzU2dklab0MwQjkiLCJtYWMiOiJjMjJkMzc3MTcyY2NiY2Y4MmI5ZDkwNjc0NGEwNzY3ZWFhZWI4NjI3ZTEwN2RjMDYyNzdhZWNlYzQ3MGU4OTNiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 15:44:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFic2dNaGwzWDNHVnNSc21vOTJiMlE9PSIsInZhbHVlIjoicjVTWG1FMEpnUzVObCtLQlkxYldvT1Zod3ppckhJYkp6ajFZYWJNYU5YRFlMWllYTWl3T2dSeldmVjkzUlVBUEJUbmNUTVV6L1BxekxCWnEzK1J5Z3NaWmdjWkpRMFpLSGJEMjVDbkdQOHUyVW80YmVNejk0c1N0NTExSmJ1MSswVjNWTEdLdVUvQVVMYkY5TWg5R2NYSkJ1TGVmakZiQkJETGpmNWdLRlN3WEljdk5MQ0lHckNlNVlVWkYwNmwwU2tTYmdkNzZMcS9TU2h0Z3lhbFhDSmx5SlNwOG1UeldVUHRtbTFxMllxNjF6Y2tjS28rQ01FTmtuMXVxVXc3SXZCdHdQUnJDSVVxaTNNMEEyWkZ5WTN4Q0dnayt0bTJWdXVUR29uUE5kWkZZL3dLQmF0WE1lNHFoVmJwYUZQZjNRRWN6WkpXUllHRktkcklTN2szREdhWHM3anRqVzhiVkRpQ3VrYlp0UlQrZVBRRGhmSEw0NnBsTG1zNlVZZFFXSmk1MjZIL29GejlTa1Z1RmxwYkdkZkYwSnFFb1Z6d3BwSzJPSHZFSTVjakFjU1ZpejlrTGxIa1B2aGtUb3J5aDJ5U2ZsQlRCekpCYVMzdnlJblB1bGN5Zk5abTZCSjVzeGZoVUl6N29Mcndxd0VLZHEyQmU0ZzhnMmtYQ3BNTmIiLCJtYWMiOiJhODI0MTZhNWY0YTRkYzdlMjQ5ZTQ4NTFmNzgwYzBlN2M1OTE0OGY1Y2I1NjQ5ZmQ4ZDZmZjNhYmU1MzFiNTU3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkttcG1CUndiMDNZWnl6dGtQR0VtSXc9PSIsInZhbHVlIjoiU0lwOVVqQXNLVlJ5bW5BMUp6bnQ5eXhRQSs4N0g2YUFIb1ozSDFPekt3SFkwV3FPNG43QXRJM2xaQVR1cUNibWFQcTVVU1AvYkEyWmkrOTAwR3JtKzRmd21aMkViRXIwOENDYXp2RUtKeldSdFVUcnZZMkRHY1l0MEdoMlpSUFZ4aityUU1ZTkNkc2liZjd4NHJhWDFmQU9VemRWZysreVgvVmxzQ3VmaDF2R0NrQmNnd0hjdkRDeXJ1UEpPL2ZZYVE1OE55N0RqMVdENWN1d0ZVdEgzNUY4UlJNMUJ5eTVHY0hpRmZ4RHNZejlyV0J5TEs5RFloUTFzVkZxdWJGNzVWVmFmTXNJNm5uekRDZUpGU0J0MUk2T1VFRHlTcHVNaTE0QUN0N2tOaHJUbDk1WkRYWVdlNElmRm9zeUttOVhUQXFvZXREZXpKS3FBUGY4VU1mMWJ5T1dxY3pSUnIrcXZ6dnVZcFFpbFc1Wlhydkl5OGRYV2UwVXV3aENBU2trTHlNc2l0WE5qSmE4SDdGblJnVlIyYjhyMVpEUnpzeVplZTJtMmJ3eWlqM3RVWHdLOHowQjhFM2hZNnIwSnJRUjA5c1F6MDNjekhMVEY3MVF0c01zU1BDcUtaNHhCRWorcEVhekwzeEJVR2I2VlBwT29jUnJ2RzU2dklab0MwQjkiLCJtYWMiOiJjMjJkMzc3MTcyY2NiY2Y4MmI5ZDkwNjc0NGEwNzY3ZWFhZWI4NjI3ZTEwN2RjMDYyNzdhZWNlYzQ3MGU4OTNiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 15:44:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804199216\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2124679972 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xU3zm2wBVLOUJpqkqZSFD4D2hHkz83IYYC0mymBW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124679972\", {\"maxDepth\":0})</script>\n"}}